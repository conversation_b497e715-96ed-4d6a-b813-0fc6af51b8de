<mxfile host="app.diagrams.net" modified="2024-07-27T10:05:00.000Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" etag="yyyyy" version="21.6.8" type="device">
    <diagram name="项目基础架构-调整版" id="diagram-2">
        <mxGraphModel dx="1800" dy="1100" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0" />
                <mxCell id="1" parent="0" />
                <mxCell id="user-request" value="&lt;b&gt;HTTP Request&lt;/b&gt;" style="shape=cloud;whiteSpace=wrap;html=1;fontStyle=1;fontSize=12;strokeWidth=3;rounded=1;" vertex="1" parent="1">
                    <mxGeometry x="50" y="500" width="120" height="80" as="geometry" />
                </mxCell>
                <mxCell id="base-filter" value="&lt;b&gt;Base Filter&lt;br&gt;(Servlet Filter)&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;fontSize=12;strokeWidth=3;" vertex="1" parent="1">
                    <mxGeometry x="270" y="500" width="140" height="80" as="geometry" />
                </mxCell>
                <mxCell id="edge-req-base" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="user-request" target="base-filter">
                    <mxGeometry relative="1" as="geometry" />
                </mxCell>
                <mxCell id="parser" value="&lt;b&gt;Parser&lt;br&gt;(XWL 解析执行)&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontStyle=1;fontSize=12;strokeWidth=3;" vertex="1" parent="1">
                    <mxGeometry x="510" y="250" width="140" height="80" as="geometry" />
                </mxCell>
                <mxCell id="edge-base-parser" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="base-filter" target="parser">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="340" y="450" />
                            <mxPoint x="460" y="450" />
                            <mxPoint x="460" y="290" />
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="file-buffer" value="&lt;b&gt;FileBuffer&lt;br&gt;(静态资源缓存)&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontStyle=1;fontSize=12;strokeWidth=3;" vertex="1" parent="1">
                    <mxGeometry x="510" y="500" width="140" height="80" as="geometry" />
                </mxCell>
                <mxCell id="edge-base-filebuffer" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="base-filter" target="file-buffer">
                    <mxGeometry relative="1" as="geometry" />
                </mxCell>
                <mxCell id="other-filters" value="&lt;b&gt;Other Filters /&lt;br&gt;Servlet Container&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontStyle=1;fontSize=12;strokeWidth=3;" vertex="1" parent="1">
                    <mxGeometry x="510" y="750" width="140" height="80" as="geometry" />
                </mxCell>
                <mxCell id="edge-base-other" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="base-filter" target="other-filters">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="340" y="700" />
                            <mxPoint x="460" y="700" />
                            <mxPoint x="460" y="790" />
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="xwl-buffer" value="&lt;b&gt;XwlBuffer&lt;br&gt;(模块缓存)&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontStyle=1;fontSize=12;strokeWidth=3;" vertex="1" parent="1">
                    <mxGeometry x="750" y="150" width="140" height="80" as="geometry" />
                </mxCell>
                <mxCell id="edge-parser-xwlbuffer" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="parser" target="xwl-buffer">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="580" y="220" />
                            <mxPoint x="700" y="220" />
                            <mxPoint x="700" y="190" />
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="script-buffer" value="&lt;b&gt;ScriptBuffer&lt;br&gt;(服务器脚本执行)&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontStyle=1;fontSize=12;strokeWidth=3;" vertex="1" parent="1">
                    <mxGeometry x="750" y="250" width="140" height="80" as="geometry" />
                </mxCell>
                <mxCell id="edge-parser-scriptbuffer" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="parser" target="script-buffer">
                    <mxGeometry relative="1" as="geometry" />
                </mxCell>
                <mxCell id="controls" value="&lt;b&gt;Controls&lt;br&gt;(UI/逻辑控件)&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontStyle=1;fontSize=12;strokeWidth=3;" vertex="1" parent="1">
                    <mxGeometry x="750" y="350" width="140" height="80" as="geometry" />
                </mxCell>
                <mxCell id="edge-parser-controls" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="parser" target="controls">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="580" y="360" />
                            <mxPoint x="700" y="360" />
                            <mxPoint x="700" y="390" />
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="redis-cache" value="&lt;b&gt;RedisCache&lt;br&gt;(Redis 访问层)&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontStyle=1;fontSize=12;strokeWidth=3;" vertex="1" parent="1">
                    <mxGeometry x="990" y="250" width="140" height="80" as="geometry" />
                </mxCell>
                <mxCell id="edge-base-redis" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="base-filter" target="redis-cache">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="410" y="520" as="sourcePoint"/>
                        <Array as="points">
                            <mxPoint x="440" y="520"/>
                            <mxPoint x="440" y="230"/>
                            <mxPoint x="940" y="230"/>
                            <mxPoint x="940" y="270"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-xwlbuffer-redis" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="xwl-buffer" target="redis-cache">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="940" y="190"/>
                            <mxPoint x="940" y="270"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-controls-redis" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.75;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="controls" target="redis-cache">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="940" y="390"/>
                            <mxPoint x="940" y="310"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="db-util" value="&lt;b&gt;DbUtil&lt;br&gt;(数据库访问)&lt;/b&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#bac8d3;strokeColor=#23445d;fontStyle=1;fontSize=12;strokeWidth=3;rounded=1;" vertex="1" parent="1">
                    <mxGeometry x="990" y="600" width="140" height="80" as="geometry" />
                </mxCell>
                <mxCell id="edge-parser-db" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="parser" target="db-util">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="680" y="360"/>
                            <mxPoint x="680" y="580"/>
                            <mxPoint x="1025" y="580"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-xwlbuffer-db" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="xwl-buffer" target="db-util">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="820" y="500"/>
                            <mxPoint x="1060" y="500"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-controls-db" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.75;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="controls" target="db-util">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="820" y="580"/>
                            <mxPoint x="1095" y="580"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="redis-server" value="&lt;b&gt;Redis Server&lt;/b&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#ffe6cc;strokeColor=#d79b00;fontStyle=1;fontSize=12;strokeWidth=3;rounded=1;" vertex="1" parent="1">
                    <mxGeometry x="1230" y="250" width="140" height="80" as="geometry" />
                </mxCell>
                <mxCell id="edge-rediscache-redisserver" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="redis-cache" target="redis-server">
                    <mxGeometry relative="1" as="geometry" />
                </mxCell>
                <mxCell id="db-server" value="&lt;b&gt;Database Server&lt;/b&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#bac8d3;strokeColor=#23445d;fontStyle=1;fontSize=12;strokeWidth=3;rounded=1;" vertex="1" parent="1">
                    <mxGeometry x="1230" y="600" width="140" height="80" as="geometry" />
                </mxCell>
                <mxCell id="edge-dbutil-dbserver" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="db-util" target="db-server">
                    <mxGeometry relative="1" as="geometry" />
                </mxCell>
                <mxCell id="log-util" value="&lt;b&gt;LogUtil / SLSLogUtil&lt;/b&gt;" style="shape=note;size=15;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fontStyle=1;fontSize=12;strokeWidth=2;rounded=1;fillColor=#fff;" vertex="1" parent="1">
                    <mxGeometry x="270" y="850" width="140" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="task-manager" value="&lt;b&gt;TaskManager&lt;/b&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;fontStyle=1;fontSize=12;strokeWidth=2;rounded=1;fillColor=#fff;" vertex="1" parent="1">
                    <mxGeometry x="510" y="850" width="140" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="online-user-manager" value="&lt;b&gt;OnlineUserManager&lt;/b&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;fontStyle=1;fontSize=12;strokeWidth=2;rounded=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
                    <mxGeometry x="750" y="850" width="140" height="80" as="geometry" />
                </mxCell>
                <mxCell id="edge-base-log" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="base-filter" target="log-util">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="340" y="800"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-base-task" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="base-filter" target="task-manager">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="375" y="800"/>
                            <mxPoint x="580" y="800"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-base-online" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="base-filter" target="online-user-manager">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="410" y="800"/>
                            <mxPoint x="820" y="800"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-parser-log" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="parser" target="log-util">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="480" y="400"/>
                            <mxPoint x="480" y="890"/>
                            <mxPoint x="270" y="890"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-xwl-log" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0;exitDx=0;exitDy=0;entryX=0.25;entryY=1;entryDx=0;entryDy=0;strokeWidth=2;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="xwl-buffer" target="log-util">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="720" y="120"/>
                            <mxPoint x="720" y="910"/>
                            <mxPoint x="305" y="910"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge-redis-log" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=1;entryDx=0;entryDy=0;strokeWidth=2;endArrow=classic;endFill=1;jumpStyle=arc;jumpSize=6;fontStyle=1" edge="1" parent="1" source="redis-cache" target="log-util">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="960" y="400"/>
                            <mxPoint x="960" y="910"/>
                            <mxPoint x="410" y="910"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>