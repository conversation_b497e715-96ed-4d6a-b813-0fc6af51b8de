# Redis主机地 址
redis.address=127.0.0.1
# Redis主机端口
redis.port=6379
# Redis验证密码
redis.password=
# 设置数据库索引，如果=2，以使用db2
redis.database=0
# 最大连接数 - 增加连接数适应集群环境
jredis.maxTotal=500
# 最大空闲连接数 - 保持更多空闲连接
jredis.maxIdle=50
# 每次释放连接的最大数目
jredis.numTestsPerEvictionRun=1024
# 释放连接的扫描间隔（毫秒）- 降低扫描频率减轻负担
jredis.timeBetweenEvictionRunsMillis=30000
# 连接最小空闲时间
jredis.minEvictableIdleTimeMillis=1800000
# 连接空闲多久后释放, 当空闲时间>该值 且 空闲连接>最大空闲连接数 时直接释放
jredis.softMinEvictableIdleTimeMillis=60000
# 最大等待毫秒数 - 集群环境增加等待时间
jredis.maxWaitMillis=15000
# 在获取连接的时候检查有效性
jredis.testOnBorrow=true
# 在空闲时检查有效性
jredis.testWhileIdle=true
# 在归还连接时检查有效性
jredis.testOnReturn=true
# 连接耗尽时是否阻塞
jredis.blockWhenExhausted=true
# 连接最小闲置时间
jredis.minEvictableIdleTimeMillis=300000

# Redisson线程池和连接池配置
# 业务线程数
redis.threads=8
# Netty线程数
redis.nettyThreads=8
# 连接池大小
redis.connectionPoolSize=32
# 空闲连接超时（毫秒）
redis.idleConnectionTimeout=30000