package aliyun.mqtt.test;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.mqtt.server.ServerConsumer;
import com.alibaba.mqtt.server.callback.MessageListener;
import com.alibaba.mqtt.server.config.ChannelConfig;
import com.alibaba.mqtt.server.config.ConsumerConfig;
import com.alibaba.mqtt.server.model.MessageProperties;

import java.io.IOException;
import java.util.concurrent.TimeoutException;

/**
 * 消息消费
 *
 * <AUTHOR>
 * @date 2023/12/15-12:29
 */

public class MessageConsumer {
    String domain;
    //使用的协议和端口必须匹配，该参数值固定为5672。
    int port;//= 5672;
    String instanceId;
    String accessKey;
    String secretKey;
    ServerConsumer serverConsumer;

    /**
     * @throws TimeoutException
     * @throws IOException
     */
    public MessageConsumer(String domain, int port, String instanceId, String accessKey, String secretKey) throws TimeoutException, IOException {
        this.domain = domain;
        this.port = port;
        this.instanceId = instanceId;
        this.accessKey = accessKey;
        this.secretKey = secretKey;
        initConsumer();
    }

    /**
     * 初始化消费者
     *
     * @throws TimeoutException
     * @throws IOException
     */
    public void initConsumer() throws TimeoutException, IOException {
        ChannelConfig channelConfig = new ChannelConfig();
        channelConfig.setDomain(domain);
        channelConfig.setPort(port);
        channelConfig.setInstanceId(instanceId);
        channelConfig.setAccessKey(accessKey);
        channelConfig.setSecretKey(secretKey);

        serverConsumer = new ServerConsumer(channelConfig, new ConsumerConfig());
        serverConsumer.start();
    }

    /**
     * 断开链接
     *
     * @throws TimeoutException
     * @throws IOException
     */
    public void closeConsumer() throws TimeoutException, IOException {
        serverConsumer.stop();
    }

    /**
     * 订阅主题
     *
     * @param firstTopic
     * @throws IOException
     */
    public void subscribeTopic(String firstTopic) throws IOException {
        serverConsumer.subscribeTopic(firstTopic, new MessageListener() {
            @Override
            public void process(String msgId, MessageProperties messageProperties, byte[] payload) {
                System.out.println("收到消息 :" + msgId + "," + JSONObject.toJSONString(messageProperties) + "," + new String(payload));
            }
        });
    }
}
