---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: *.xwl
alwaysApply: false
---
---
description: 
globs: *.xwl,*.js,*.java
alwaysApply: false
---
---
description: 国际化规则
globs: *.xwl,*.js,*.java
alwaysApply: false
---
1. 标签命名规则：
    - 按照"模块缩写_功能描述"格式命名，如：msg_delete、login_username
    - 保持命名简洁明了，便于维护和查找
    - 命名之前一定要注意查看语言包中是否已经存在类似的多语言标签，如果有则直接复用
    
2. 前端配置国际化：
    - 组件属性中的静态文本使用"@Str.标签名"格式，如："text": "@Str.msg_delete"
    - 系统会自动从对应语言文件中查找替换文本
    - XWL根节点的“title”不用加上@，直接使用“Str.标签名”
    - 字符串中的中文，要把"Str.标签名"作为一个对象来拼接
    
3. 前端JavaScript代码国际化：
    - 使用Wb.format(Str.标签名, 参数1, 参数2...)方法
    - 示例：Wb.warn(Str.msg_selectData) 弹出警告信息;
    - 带参数示例：Wb.confirm(Wb.format(Str.msg_confirmDelete, '参数1','参数2'));
    
4. 后端ServerScript国际化：
    - 使用Str.format(request, "标签名", 参数1, 参数2...)方法
    - 注意：后端不使用@前缀，直接使用标签名
    - 示例：var errorMsg = Str.format(request, "msg_queryFailed");
    
5. 全局多语言文件管理：
    - 英文语言包：meland-web\src\main\webapp\wb\script\locale\wb-lang-en-debug.js
    - 中文语言包：meland-web\src\main\webapp\wb\script\locale\wb-lang-zh-debug.js
    - 泰文语言包：meland-web\src\main\webapp\wb\script\locale\wb-lang-th-debug.js
    - 多语言包中不能包含空行和注释行（如：//注释内容）
    - 要保证不能有重复的KEY
    
6. 注意事项：
    - 英文、泰文翻译尽量与中文长度保持一致
    - 如翻译内容过长，在保证含义不变的情况下适当缩写