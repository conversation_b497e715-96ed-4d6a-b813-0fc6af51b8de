package com.wb.fit;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpSession;
import javax.servlet.http.HttpSessionContext;

/**
 * 自定义会话实现类，用于管理和维护会话状态和属性。
 * 实现了 {@link HttpSession} 接口，提供会话管理的基本功能。
 * 通过该类，用户可以存储、获取、删除会话属性，并管理会话的生命周期。
 *
 * 主要功能：
 * - 为每个会话生成唯一的 ID。
 * - 设置和获取会话属性。
 * - 支持会话超时功能，设置最大不活动时间间隔。
 * - 管理会话的状态，如新建状态和有效性。
 * - 提供获取会话创建时间和最后访问时间的功能。
 *
 * 注意：
 * - 此类中的某些功能为简单实现，例如未完全实现的 {@link ServletContext} 和 {@link HttpSessionContext} 返回值为 null。
 * - 使用者应确保使用前验证会话是否有效，检查 {@link #isValid()} 返回值。
 */
@SuppressWarnings("deprecation")
public class CustomSession implements HttpSession {
    private final Map<String, Object> attributes;
    private String id;
    private long creationTime;
    private long lastAccessedTime;
    private int maxInactiveInterval;
    private boolean isNew;
    private boolean isValid;

    public CustomSession() {
        this.attributes = new HashMap<>();
        this.id = "session-" + System.currentTimeMillis();
        this.creationTime = System.currentTimeMillis();
        this.lastAccessedTime = this.creationTime;
        this.maxInactiveInterval = 1800; // 30分钟
        this.isNew = true;
        this.isValid = true;
    }

    @Override
    public long getCreationTime() {
        return creationTime;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public long getLastAccessedTime() {
        return lastAccessedTime;
    }

    @Override
    public ServletContext getServletContext() {
        return null;
    }

    @Override
    public void setMaxInactiveInterval(int interval) {
        this.maxInactiveInterval = interval;
    }

    @Override
    public int getMaxInactiveInterval() {
        return maxInactiveInterval;
    }

    @Override
    public HttpSessionContext getSessionContext() {
        return null;
    }

    @Override
    public Object getAttribute(String name) {
        return attributes.get(name);
    }

    @Override
    public Object getValue(String name) {
        return getAttribute(name);
    }

    @Override
    public Enumeration<String> getAttributeNames() {
        return new java.util.Vector<>(attributes.keySet()).elements();
    }

    @Override
    public String[] getValueNames() {
        return attributes.keySet().toArray(new String[0]);
    }

    @Override
    public void setAttribute(String name, Object value) {
        attributes.put(name, value);
    }

    @Override
    public void putValue(String name, Object value) {
        setAttribute(name, value);
    }

    @Override
    public void removeAttribute(String name) {
        attributes.remove(name);
    }

    @Override
    public void removeValue(String name) {
        removeAttribute(name);
    }

    @Override
    public void invalidate() {
        attributes.clear();
        isValid = false;
    }

    @Override
    public boolean isNew() {
        return isNew;
    }

    public void setNew(boolean isNew) {
        this.isNew = isNew;
    }

    public boolean isValid() {
        return isValid;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setCreationTime(long creationTime) {
        this.creationTime = creationTime;
    }

    public void setLastAccessedTime(long lastAccessedTime) {
        this.lastAccessedTime = lastAccessedTime;
    }

    public void setValid(boolean valid) {
        this.isValid = valid;
    }
} 