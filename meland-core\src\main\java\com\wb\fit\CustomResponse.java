package com.wb.fit;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.util.Collection;
import java.util.HashSet;
import java.util.Locale;
import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;

/**
 * CustomResponse 是一个实现 HttpServletResponse 接口的自定义包装类，用于提供对原生响应对象的扩展功能。
 * 该类通过包装 HttpServletResponse 的方法，自定义处理响应的行为，同时支持对响应输出的捕获和修改。
 *
 * 特性和功能：
 * - 支持捕获响应数据为字节数组。
 * - 自定义管理响应头信息。
 * - 实现对 Writer 和 OutputStream 的互斥调用机制。
 * - 包装部分标准的 HttpServletResponse 方法，例如设置状态码、编码、内容类型等。
 *
 * 注意事项：
 * 1. `getWriter()` 和 `getOutputStream()` 方法不可同时使用，否则会抛出 IllegalStateException。
 * 2. 一旦提交响应，任何进一步的修改都将无效。
 * 3. 该类通过内部 ByteArrayOutputStream 实现将响应写入字节数组，可通过 `getBytes()` 方法获取数据。
 *
 * 方法概述：
 * - `getBytes`: 返回捕获的响应内容的字节数组。
 * - `getStatusCode`: 获取当前设置的 HTTP 状态码。
 * - `addHeader`, `setHeader`, `containsHeader`: 管理响应头信息。
 * - `sendError`, `sendRedirect`: 覆盖并自定义原生的 HTTP 错误处理或重定向方法。
 * - `getOutputStream`, `getWriter`: 提供对响应输出流或输出编写器的访问，同时确保流的互斥性。
 * - `reset`, `resetBuffer`: 允许重置响应或仅清除缓冲区内容。
 * - `setCharacterEncoding`, `setContentType`: 设置响应的字符编码和内容类型。
 * - `isCommitted`: 判断响应是否已经提交。
 *
 * 使用场景：
 * 适用于需要拦截、修改或记录 HTTP 响应的场景，例如日志记录、调试工具或自定义响应数据的封装。
 */
public class CustomResponse implements HttpServletResponse {
	private final HashSet<String> headerSet;
	private final HttpServletResponse response;
	private PrintWriter writer;
	private ServletOutputStream sos;
	private final ByteArrayOutputStream bos = new ByteArrayOutputStream(8192);

	private boolean submited = false;
	private Locale locale;
	private String contentType;
	private String charEncoding;
	private int statusCode = -1;
	private boolean usingWriter;
	private boolean usingOutputStream;

	public CustomResponse(HttpServletResponse response) {
		this.response = response;
		this.headerSet = new HashSet<String>();
	}

	public byte[] getBytes() {
		if (this.usingWriter)
			this.writer.flush();
		return this.bos.toByteArray();
	}

	public int getStatusCode() {
		return this.statusCode;
	}

	@Override
	public void addCookie(Cookie cookie) {
	}

	@Override
	public void addDateHeader(String name, long date) {
		this.headerSet.add(name);
	}

	@Override
	public void addHeader(String name, String value) {
		this.headerSet.add(name);
	}

	@Override
	public void addIntHeader(String name, int value) {
		this.headerSet.add(name);
	}

	@Override
	public boolean containsHeader(String name) {
		return this.headerSet.contains(name);
	}

	@Override
	public String encodeRedirectURL(String url) {
		if (this.response == null)
			return url;
		return this.response.encodeRedirectURL(url);
	}

	@Override
	public String encodeRedirectUrl(String url) {
		if (this.response == null)
			return url;
		return this.response.encodeRedirectURL(url);
	}

	@Override
	public String encodeURL(String url) {
		if (this.response == null)
			return url;
		return this.response.encodeURL(url);
	}

	@Override
	public String encodeUrl(String url) {
		if (this.response == null)
			return url;
		return this.response.encodeURL(url);
	}

	@Override
	public void sendError(int sc) throws IOException {
		this.statusCode = sc;
		if (this.response != null)
			this.response.sendError(sc);
	}

	@Override
	public void sendError(int sc, String msg) throws IOException {
		this.statusCode = sc;
		if (this.response != null)
			this.response.sendError(sc, msg);
	}

	@Override
	public void sendRedirect(String location) throws IOException {
	}

	@Override
	public void setDateHeader(String name, long date) {
		this.headerSet.add(name);
	}

	public void setHeader(String name, String value) {
		this.headerSet.add(name);
	}

	@Override
	public void setIntHeader(String name, int value) {
		this.headerSet.add(name);
	}

	@Override
	public void setStatus(int sc) {
		this.statusCode = sc;
		if (this.response != null)
			this.response.setStatus(sc);
	}

	@Override
	public void setStatus(int sc, String sm) {
		this.statusCode = sc;
	}

	@Override
	public void flushBuffer() throws IOException {
		this.submited = true;
	}

	@Override
	public int getBufferSize() {
		return 8192;
	}

	@Override
	public String getCharacterEncoding() {
		if ((this.charEncoding == null) && (this.response != null)) {
			return this.response.getCharacterEncoding();
		}
		return this.charEncoding;
	}

	@Override
	public String getContentType() {
		return this.contentType;
	}

	@Override
	public Locale getLocale() {
		return this.locale;
	}

	@Override
	public ServletOutputStream getOutputStream() throws IOException {
		if (this.usingWriter)
			throw new IllegalStateException("getWriter() has already been called for this response");
		if (this.sos != null)
			return this.sos;
		this.sos = new ServletOutputStream() {
			@Override
			public void write(byte[] data, int offset, int length) {
				if (!CustomResponse.this.submited)
					CustomResponse.this.bos.write(data, offset, length);
			}

			@Override
			public void write(int b) throws IOException {
				if (!CustomResponse.this.submited)
					CustomResponse.this.bos.write(b);
			}

			@Override
			public boolean isReady() {
				// TODO Auto-generated method stub
				return false;
			}

			@Override
			public void setWriteListener(WriteListener arg0) {
				// TODO Auto-generated method stub

			}
		};
		this.usingOutputStream = true;
		return this.sos;
	}

	@Override
	public PrintWriter getWriter() throws IOException {
		if (this.usingOutputStream)
			throw new IllegalStateException("getOutputStream() has already been called for this response");
		if (this.writer != null)
			return this.writer;
		this.writer = new PrintWriter(new OutputStreamWriter(getOutputStream(), "utf-8"));
		this.usingWriter = true;
		return this.writer;
	}

	@Override
	public boolean isCommitted() {
		return this.submited;
	}

	@Override
	public void reset() {
		resetBuffer();
	}

	@Override
	public void resetBuffer() {
		if (!this.submited)
			this.bos.reset();
	}

	@Override
	public void setBufferSize(int size) {
	}

	@Override
	public void setCharacterEncoding(String charset) {
		this.charEncoding = charset;
	}

	@Override
	public void setContentLength(int len) {
	}

	@Override
	public void setContentType(String type) {
		this.contentType = type;
	}

	@Override
	public void setLocale(Locale loc) {
		this.locale = loc;
	}

	@Override
	public void setContentLengthLong(long len) {
	}

	@Override
	public Collection<String> getHeaders(String name) {
		return new HashSet<String>();
	}

	@Override
	public Collection<String> getHeaderNames() {
		return this.headerSet;
	}

	@Override
	public String getHeader(String name) {
		return null;
	}

	@Override
	public int getStatus() {
		return 0;
	}
}