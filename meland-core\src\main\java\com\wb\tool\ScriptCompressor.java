package com.wb.tool;

import com.google.javascript.jscomp.CompilationLevel;
import com.google.javascript.jscomp.Compiler;
import com.google.javascript.jscomp.CompilerOptions;
import com.google.javascript.jscomp.SourceFile;
import com.googlecode.htmlcompressor.compressor.HtmlCompressor;
import com.wb.common.Var;
import com.yahoo.platform.yui.compressor.CssCompressor;
import com.yahoo.platform.yui.compressor.JavaScriptCompressor;
import org.apache.commons.io.IOUtils;
import org.mozilla.javascript.ErrorReporter;
import org.mozilla.javascript.EvaluatorException;

import java.io.*;
import java.util.Collections;

/**
 * JS和CSS脚本压缩器。
 */
public class ScriptCompressor {


    /**
     * 压缩HTML代码
     *
     * @param html html代码
     * @return 压缩后的html代码
     */
    public static String compressHtml(String html) {
        HtmlCompressor compressor = new HtmlCompressor();
        compressor.setRemoveComments(true);
        compressor.setRemoveIntertagSpaces(true);
        compressor.setCompressJavaScript(true);
        compressor.setCompressCss(true);
        compressor.setJavaScriptCompressor(ScriptCompressor::compressJavaScript);
        compressor.setCssCompressor(ScriptCompressor::compressCss);
        return compressor.compress(html);
    }

    /**
     * 压缩JS代码
     *
     * @param jsCode js代码
     * @return 压缩后的js代码
     */
    public static String compressJavaScript(String jsCode) {
        Compiler compiler = new Compiler();
        CompilerOptions options = new CompilerOptions();
        CompilationLevel.WHITESPACE_ONLY.setOptionsForCompilationLevel(options);
        SourceFile input = SourceFile.fromCode("input.js", jsCode);
        SourceFile extern = SourceFile.fromCode("externs.js", "");
        compiler.compile(Collections.singletonList(extern), Collections.singletonList(input), options);
        return compiler.toSource();
    }

    /**
     * 压缩CSS代码
     *
     * @param css css代码
     * @return 压缩后的css代码
     */
    public static String compressCss(String css) {
        StringReader reader = new StringReader(css);
        StringWriter writer = new StringWriter();
        try {
            CssCompressor compressor = new CssCompressor(reader);
            compressor.compress(writer, -1);
        } catch (Exception e) {
            return css;
        }

        return writer.toString();
    }

    /**
     * 压缩JS文件。
     *
     * @param oldFile 压缩之前的文件。
     * @param newFile 压缩之后的新文件。
     * @throws IOException 压缩过程发生异常。
     */
    @SuppressWarnings("deprecation")
    public static synchronized void compressJs(File oldFile, File newFile) throws IOException {
        Reader in = new BufferedReader(new InputStreamReader(new FileInputStream(oldFile), "utf-8"));
        Writer out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(newFile), "utf-8"));
        String filename = oldFile.getName();
        try {
            JavaScriptCompressor compressor = new JavaScriptCompressor(in, new ErrorReporter() {
                public void warning(String message, String sourceName, int line, String lineSource, int lineOffset) {
                    if (Var.printError) {
                        System.err.println("WARNING in compress " + filename);
                        if (line < 0)
                            System.err.println("  " + message);
                        else
                            System.err.println("  " + line + ':' + lineOffset + ':' + message);
                    }
                }

                public void error(String message, String sourceName, int line, String lineSource, int lineOffset) {
                    if (Var.printError) {
                        System.err.println("ERROR in compress " + filename);
                        if (line < 0)
                            System.err.println("  " + message);
                        else
                            System.err.println("  " + line + ':' + lineOffset + ':' + message);
                    }
                }

                public EvaluatorException runtimeError(String message, String sourceName, int line, String lineSource,
                                                       int lineOffset) {
                    error(message, sourceName, line, lineSource, lineOffset);
                    return new EvaluatorException(message);
                }
            });
            compressor.compress(out, -1, true, false, false, false);
        } finally {
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(out);
        }
    }

    /**
     * 压缩CSS文件。
     *
     * @param oldFile 压缩之前的文件。
     * @param newFile 压缩之后的新文件。
     * @throws IOException 压缩过程发生异常。
     */
    @SuppressWarnings("deprecation")
    public static synchronized void compressCss(File oldFile, File newFile) throws IOException {
        Reader in = new BufferedReader(new InputStreamReader(new FileInputStream(oldFile), "utf-8"));
        Writer out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(newFile), "utf-8"));
        try {
            CssCompressor compressor = new CssCompressor(in);
            compressor.compress(out, -1);
        } finally {
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(out);
        }
    }
}