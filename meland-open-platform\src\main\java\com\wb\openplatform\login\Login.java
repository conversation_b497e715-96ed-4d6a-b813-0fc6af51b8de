package com.wb.openplatform.login;

import java.sql.ResultSet;
import java.util.ArrayList;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.json.JSONArray;
import org.json.JSONObject;

import com.wb.common.UserList;
import com.wb.common.Var;
import com.wb.openplatform.accounts.util.AccountParamesUtil;
import com.wb.openplatform.accounts.util.AccountUtil;
import com.wb.openplatform.dingtalk.util.DingTalkUtil;
import com.wb.openplatform.dingtalk.util.DingtalkParamesUtil;
import com.wb.openplatform.enterprise.util.WeiXinParamesUtil;
import com.wb.openplatform.enterprise.util.WeiXinUtil;
import com.wb.util.DbUtil;
import com.wb.util.StringUtil;
import com.wb.util.WebUtil;

public class Login {
	// 企业微信获取 用户userID
	private static final String qyUserInfo = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=ACCESS_TOKEN&code=CODE";
	// 企业微信 获取用户信息
	@SuppressWarnings("unused")
	private static final String qyUser = "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=ACCESS_TOKEN&userid=USERID";
	// 钉钉 获取用户授权的持久授权码
	private static final String ddCode = "https://oapi.dingtalk.com/sns/get_persistent_code?access_token=ACCESS_TOKEN";
	// 钉钉 根据unionid获取成员的userid
	private static final String ddUserInfo = "https://oapi.dingtalk.com/user/getUseridByUnionid?access_token=ACCESS_TOKEN&unionid=UNIONID";
	// 微信 获取用户
	private static final String wxUserInfo = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=APPID&secret=SECRET&code=CODE&grant_type=authorization_code";

	public static JSONObject loginVerify(HttpServletRequest request, HttpServletResponse response) {
		JSONObject js = null;
		try {
			String referer = StringUtil.opt(request.getHeader("Referer"));
			String redirect = request.getParameter("state");
			HttpSession session = request.getSession(false);
			// 获取code
			String code = request.getParameter("code");
			// 获取验证方式
			String SendType = request.getParameter("type");
			// user_id
			String user_id = "";
			// 判断code是否为空
			if (code.isEmpty()) {
				js = new JSONObject();
				js.put("errcode", 500);
				js.put("errmsg", "账户未绑定移动扫码");
				return js;
			}

			// 微信
			if (SendType.equals("wx")) {
				AccountParamesUtil.init();
				// 获取accessToken
				// String access_token =
				// AccountUtil.getAccessToken(AccountParamesUtil.grant_Type,AccountParamesUtil.appId,AccountParamesUtil.appSecret,"MD").getToken();
				// 获取 openid
				String url = wxUserInfo.replace("APPID", AccountParamesUtil.appId)
						.replace("SECRET", AccountParamesUtil.appSecret).replace("CODE", code);
				// 请求
				JSONObject jsonobject = AccountUtil.httpRequest(url, "GET", null);
				if (jsonobject.has("access_token")) {
					if (jsonobject.getInt("expires_in") == 7200) {
						JSONArray qyarray = DbUtil.query("SELECT wx_id,user_id FROM wb_user_weixin WHERE wx_id='"
								+ jsonobject.getString("openid") + "' and is_type = 'login' ");
						if (qyarray.length() > 0 && !qyarray.getString(0).equals("undefined")) {
							// 存储request
							request.setAttribute("user_id", qyarray.getString(1));
							user_id = qyarray.getString(1);
						}
					}
				}
			}
			// 钉钉
			else if (SendType.equals("dd")) {
				DingtalkParamesUtil.init();
				// 获取accessToken
				String access_token = DingTalkUtil.getAccessToken(DingtalkParamesUtil.smAppId,
						DingtalkParamesUtil.smAppSecret, DingtalkParamesUtil.tokenSM).getToken();
				String DT = DingTalkUtil.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret,
						DingtalkParamesUtil.tokenDD).getToken();
				// 获取用户授权的持久授权码
				JSONObject obj = new JSONObject();
				obj.put("tmp_auth_code", code);
				// 地址
				String url = ddCode.replace("ACCESS_TOKEN", access_token);
				JSONObject jsonobject = DingTalkUtil.httpRequest(url, "POST", obj.toString());
				if (jsonobject.has("errcode")) {
					if (jsonobject.getInt("errcode") == 0) {
						// 地址
						String url1 = ddUserInfo.replace("ACCESS_TOKEN", DT).replace("UNIONID",
								jsonobject.getString("unionid"));
						JSONObject userObj = DingTalkUtil.httpRequest(url1, "GET", null);
						if (jsonobject.has("errcode")) {
							if (jsonobject.getInt("errcode") == 0) {
								JSONArray qyarray = DbUtil
										.query("SELECT dd_id,user_id FROM wb_user_weixin WHERE dd_id='"
												+ userObj.getString("userid") + "' and is_type = 'login' ");
								if (qyarray.length() > 0 && !qyarray.getString(0).equals("undefined")) {
									// 存储request
									request.setAttribute("user_id", qyarray.getString(1));
									user_id = qyarray.getString(1);
								}
							}
						}
					}
				}
			}
			// 企业微信
			else if (SendType.equals("qy")) {
				WeiXinParamesUtil.init();
				// 获取accessToken
				String accessToken = WeiXinUtil
						.getAccessToken(WeiXinParamesUtil.corpId, WeiXinParamesUtil.agentSecret, "SQ").getToken();
				// 拼接地址
				String url = qyUserInfo.replace("ACCESS_TOKEN", accessToken).replace("CODE", code);
				// 获取userinfo
				JSONObject jsonobject = WeiXinUtil.httpRequest(url, "GET", null);
				if (jsonobject.has("errcode")) {
					if (jsonobject.getInt("errcode") == 0) {
						JSONArray qyarray = DbUtil.query("SELECT qy_id,user_id FROM wb_user_weixin WHERE qy_id='"
								+ jsonobject.getString("UserId") + "' and is_type = 'login' ");
						if (qyarray.length() > 0 && !qyarray.getString(0).equals("undefined")) {
							// 存储request
							request.setAttribute("user_id", qyarray.getString(1));
							user_id = qyarray.getString(1);
						}
					}
				}
			}

			if (session != null)
				session.invalidate();
			if (user_id != "") {
				createSession(request, user_id);
			} else {
				// throw new Exception(Str.format(request, "账户未绑定"));
				// response.sendRedirect(redirect);
				js = new JSONObject();
				js.put("errcode", 500);
				js.put("errmsg", "账户未绑定移动扫码");
				return js;
			}
			if (referer.endsWith("/login") || referer.endsWith("m?xwl=sys/session/login"))
				referer = Var.getString("sys.home");
			if (referer.endsWith("/tlogin") || referer.endsWith("m?xwl=sys/session/tlogin"))
				referer = Var.getString("sys.homeMobile");
			if (StringUtil.isEmpty(redirect))
				WebUtil.send(response, referer);
			else
				response.sendRedirect(redirect);
		} catch (Exception e) {
			// TODO: handle exception
		}
		return js;
	}

	public static void SmloginVerify(HttpServletRequest request, HttpServletResponse response) {
		try {
			String referer = StringUtil.opt(request.getHeader("Referer"));
			String redirect = request.getParameter("state");
			HttpSession session = request.getSession(false);
			// 获取code
			String user_id = request.getParameter("uid");

			if (session != null)
				session.invalidate();
			if (user_id != "" && user_id != null) {
				createSession(request, user_id);
			}
			if (referer.endsWith("/login") || referer.endsWith("m?xwl=sys/session/login"))
				referer = Var.getString("sys.home");
			if (referer.endsWith("/tlogin") || referer.endsWith("m?xwl=sys/session/tlogin"))
				referer = Var.getString("sys.homeMobile");
			if (StringUtil.isEmpty(redirect))
				WebUtil.send(response, referer);
			else
				response.sendRedirect(redirect);
		} catch (Exception e) {
			System.err.println(e.getStackTrace());
		}
	}

	/**
	 * 创建会话。首先验证用户名称和密码是否合法，如果非法抛出异常。如果合法，创建HTTP会话， 并存储当前用户数据至会话Attribute。
	 * 
	 * @param request        请求对象
	 * @param username
	 * @param password
	 * @param verifyPassword
	 * @throws Exception 创建会话失败
	 */
	public static void createSession(HttpServletRequest request, String user_id) throws Exception {
		int timeout = Var.sessionTimeout;
		request.setAttribute("user_id", user_id);
		ResultSet rs = (ResultSet) DbUtil.run(request,
				"select USER_ID,USER_NAME,DISPLAY_NAME,PASSWORD,USE_LANG from WB_USER where USER_ID={?user_id?} and STATUS=1");
		rs.next();
		String userId = rs.getString("USER_ID");
		String username = rs.getString("USER_NAME");
		String dispname = rs.getString("DISPLAY_NAME");
		String useLang = rs.getString("USE_LANG");

		HttpSession session = request.getSession(true);
		// 登录的标志为存在会话且属性sys.logined为非null
		session.setAttribute("sys.logined", true);
		if (timeout == -2)
			timeout = Integer.MAX_VALUE;
		if (timeout > -2)
			session.setMaxInactiveInterval(timeout);
		session.setAttribute("sys.user", userId);
		session.setAttribute("sys.username", username);
		session.setAttribute("sys.dispname", dispname);
		// userAgent最大长度限制为500个字符，防止被注入大字符串
		session.setAttribute("sysx.userAgent", StringUtil.substring(request.getHeader("user-agent"), 0, 500));
		session.setAttribute("sys.ip", WebUtil.getRemoteAddr(request));
		session.setAttribute("sys.lang", useLang);
		DbUtil.run(request,
				"update WB_USER set LOGIN_TIMES=LOGIN_TIMES+1,LAST_LOGIN={?sys.date?} where USER_ID={?sys.user?}");
		DbUtil.run(request,
				"insert into WB_LOG(LOG_DATE, USER_NAME, IP, LOG_TYPE, MSG) values({?sys.date?},{?sys.username?},{?sys.ip?},1,'app_login')");
		storeUserValues(request, session, userId);
		if (Var.uniqueLogin) {
			String[] userIds = { userId };
			UserList.invalidate(userIds);
		}
	}

	/**
	 * 存储用户数据至Session的Attribute.
	 * 
	 * @param session 会话对象。
	 * @throws Exception 存储过程发生异常。
	 */
	public static void storeUserValues(HttpServletRequest request, HttpSession session, String userId)
			throws Exception {
		// 定义需要存储的名称列表，可以根据业务需要扩充列表
		String names[] = Var.sessionVars.split(",");
		String valueIds[] = new String[names.length];
		String fieldName, fieldValue, roleArray[];
		ArrayList<String> roles = new ArrayList<String>();
		ResultSet rs;
		int i = 0;

		// 角色
		rs = (ResultSet) DbUtil.run(request,
				"select ROLE_ID from WB_USER_ROLE where USER_ID={?sys.user?} and ROLE_ID<>'default'");
		roles.add("default");// 默认每个用户都具有的角色
		while (rs.next()) {
			roles.add(rs.getString(1));
		}
		roleArray = roles.toArray(new String[roles.size()]);
		session.setAttribute("sys.roles", roleArray);
		session.setAttribute("sys.roleList", StringUtil.join(roleArray, ","));
		rs = (ResultSet) DbUtil.run(request, "select DEPT_ID from WB_PERSON where USER_ID={?sys.user?}");
		session.setAttribute("sys.dept", rs.next() ? rs.getString(1) : "");
		// 其他数据
		for (String name : names) {
			valueIds[i++] = StringUtil.concat("'", userId, "@", name, "'");
		}
		rs = (ResultSet) DbUtil.run(request,
				"select VAL_ID,VAL_CONTENT from WB_VALUE where VAL_ID in (" + StringUtil.join(valueIds, ",") + ")");
		while (rs.next()) {
			fieldName = rs.getString("VAL_ID");
			fieldName = fieldName.substring(fieldName.indexOf('@') + 1);
			fieldValue = rs.getString("VAL_CONTENT");
			session.setAttribute("sys." + fieldName, fieldValue);
		}
	}
}
