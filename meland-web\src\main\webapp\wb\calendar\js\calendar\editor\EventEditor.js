Ext.ns("Ext.ux.calendar.editor");
Ext.ux.calendar.editor.EventEditor = function (config) {
    Ext.apply(this, config);
    this.ehandler.applyCalendarSetting(this);
    var lan = Ext.ux.calendar.Language.Editor;
    this.subjectField = this.subjectField || new Ext.form.TextField({
        fieldLabel: lan['subjectField.label'],
        labelAlign: 'right',
        labelWidth: 60,
        allowBlank: false,
        anchor: "100%",
        regex: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
        regexText: '请输入中文、英文或数字，不能包含特殊字符'
    });
    this.ctpl = this.ctpl || "<tpl for=\".\">" + "<div class=\"x-boundlist-item x-calendar-list-item\">" + this.ehandler.cTplStr + "</div>" + "</tpl>";
    this.calendarField = this.calendarField || new Ext.form.ComboBox({
        fieldLabel: lan['calendarField.label'],
        labelAlign: 'right',
        labelWidth: 60,
        store: Ext.ux.calendar.Mask.getCalendarStore(),
        displayField: "title",
        valueField: "id",
        queryMode: "local",
        triggerAction: "all",
        allowBlank: false,
        anchor: "100%",
        editable: false,
        tpl: this.ctpl
    });
    this.deleteBtn = this.deleteBtn || new Ext.Button({
        buttonStyle: 'danger',
        scale: 'medium',
        glyph: 0xf1f8,
        text: lan['deleteBtn.text'],
        handler: this.onRemoveFn,
        scope: this
    });
    this.saveBtn = this.saveBtn || new Ext.Button({
        buttonStyle: 'primary',
        scale: 'medium',
        glyph: 0xf0c7,
        text: lan['saveBtn.text'],
        handler: this.onSaveFn,
        scope: this
    });
    this.detailBtn = this.detailBtn || new Ext.Button({
        buttonStyle: 'default',
        scale: 'medium',
        text: lan.detailSetting,
        handler: this.onDetailFn,
        scope: this
    });
    this.formPanel = Ext.create("Ext.form.Panel", {
        layout: "anchor",
        border: false,
        anchor: "100%",
        padding: "20 30 10 30",
        defaults: {
            labelWidth: 70
        },
        items: [this.subjectField, this.calendarField]
    });
    this.timeBox = Ext.create("Ext.Component", {
        flex: 1,
        cls: "x-event-title-box"
    });
    this.headCt = Ext.create("Ext.container.Container", {
        anchor: "100%",
        layout: {
            type: "hbox",
            align: "middle",
            pack: "end"
        },
        height: 50,
        padding: "0 20",
        items: [this.timeBox, {
            xtype: "component",
            cls: "x-event-editor-close",
            html: "<span class=\"wb_glyph\">&#xf00d</span>",
            listeners: {
                afterrender: {
                    scope: this,
                    fn: function (box) {
                        box.getEl().on("click", function (e) {
                            this.onCancelFn();
                        }, this);
                    }
                }
            }
        }]
    });
    Ext.ux.calendar.editor.EventEditor.superclass.constructor.call(this, {
        layout: "anchor",
        items: [this.headCt, this.formPanel],
        bbar: [this.deleteBtn, "->", this.detailBtn, this.saveBtn]
    });
    this.on("show", this.onShowFn, this);
    this.on("hide", this.onHideFn, this);
    this.calendarField.on("select", this.onCalendarSelectFn, this);
    Ext.getBody().on("mousedown", function (e) {
        if (this.isVisible() && !this.holdClickMaskFlag) {
            var target = Ext.get(e.getTarget());
            if (target.hasCls("x-mask")) {
                this.onCancelFn();
            }
        }
    }, this);
};
Ext.extend(Ext.ux.calendar.editor.EventEditor, Ext.window.Window, {
    cls: "x-event-editor",
    header: false,
    border: false,
    width: 400,
    closeAction: "onCancelFn",
    resizable: false,
    modal: true,
    onDetailFn: function () {
        this.hideEditor();
        this.fireEvent("showdetailsetting", this.obj);
    },
    onCalendarSelectFn: function (combo, rd, index) {
        var coverEl = this.bindEl;
        var color = rd[0].data.color;
        var _color = "#" + Ext.ux.calendar.Mask.getColorByIndex(color);
        this.headCt.getEl().setStyle("background-color", _color);
        if (coverEl && !coverEl.hold) {
            var event = coverEl.bindEvent;
            var cview = coverEl.cview;
            var eh = cview.ehandler;
            if (this.cloneEl) {
                Ext.get(this.cloneEl).setStyle("background-color", _color);
            }
            var arr = Ext.DomQuery.select("div[name=x-event-" + event.day + "-" + event.eday + "-" + event.eventId + "]", cview.getEl().dom);
            for (var i = 0, len = arr.length; i < len; i++) {
                coverEl = Ext.get(arr[i]);
                if (0 == event.startRow && this.rowCount == event.endRow) {
                    if (this.oldColor != color) {
                        eh.changeWholeColor(coverEl, this.oldColor, color);
                    }
                } else {
                    if (this.oldColor != color) {
                        if (cview instanceof Ext.ux.calendar.view.DayView) {
                            eh.changeEventColor(coverEl, this.oldColor, color);
                        } else {
                            eh.changeLegendColor(coverEl, this.oldColor, color);
                        }
                    }
                }
            }
        }
        this.oldColor = color;
    },
    onRemoveFn: function () {
        var lan = Ext.ux.calendar.Language.EventHandler;
        var coverEl = this.bindEl;
        var be = coverEl.bindEvent;
        var cview = coverEl.cview;
        var eh = cview.ehandler;
        var col = coverEl.col;
        var me = this;
        if (coverEl) {
            if ("string" == Ext.ux.calendar.Mask.typeOf(be.repeatType)) {
                eh.freeEventEl(coverEl);
                eh.deleteEvent(be, cview, col);
                cview.fireEvent("canceldetail");
                this.hideEditor();
            } else {
                me.holdClickMaskFlag = true;
                Ext.get(me.cloneEl).hide();
                Ext.Msg.show({
                    title: lan['deleteRepeatPopup.title'],
                    msg: lan['deleteRepeatPopup.msg'],
                    buttons: Ext.Msg.YESNOCANCEL,
                    fn: function (bid, text) {
                        delete me.holdClickMaskFlag;
                        Ext.get(me.cloneEl).show();
                        if ("yes" == bid) {
                            eh.freeEventEl(coverEl);
                            eh.deleteRepeatEvent(be, cview);
                        } else if ("no" == bid) {
                            eh.freeEventEl(coverEl);
                            eh.deleteRepeatEvent(be, cview, true);
                        }
                        if ("cancel" !== bid) {
                            cview.fireEvent("canceldetail");
                            me.hideEditor();
                        }
                    },
                    icon: Ext.MessageBox.QUESTION
                });
            }
        }
    },
    onSaveFn: function () {
        var me = this;
        if (this.formPanel.getForm().isValid()) {
            var eh = this.ehandler;
            var cview = this.cview;
            if (this.bindEl) {
                var coverEl = this.bindEl;
                var event = coverEl.bindEvent;
                var oevent = Ext.apply({}, event);
                if ("add" == this.action && !coverEl.hold) {
                    coverEl.remove();
                }
                event.repeatType = event.repeatType || "no";
                event.allDay = false;
                event.locked = event.locked || false;
                event.subject = this.subjectField.getValue();
                event.calendarId = this.calendarField.getValue();
                event.color = eh.calendarSet[event.calendarId].color;
                if ("add" == this.action) {
                    if ("string" == Ext.ux.calendar.Mask.typeOf(event.repeatType)) {
                        eh.createEvent(event, cview);
                    } else {
                        eh.createRepeatEvent(event, cview);
                    }
                } else if ("update" == this.action) {
                    if (!Ext.ux.calendar.Mask.isEqualObj(oevent, event)) {
                        if ("string" == Ext.ux.calendar.Mask.typeOf(oevent.repeatType) && "string" == Ext.ux.calendar.Mask.typeOf(event.repeatType)) {
                            eh.updateEvent(event, cview, null, oevent, this.noLayout);
                        } else {
                            if ("string" != Ext.ux.calendar.Mask.typeOf(oevent.repeatType)) {
                                var lan = Ext.ux.calendar.Language.EventHandler;
                                me.holdClickMaskFlag = true;
                                Ext.get(me.cloneEl).hide();
                                Ext.Msg.show({
                                    title: lan['updateRepeatPopup.title'],
                                    msg: lan['updateRepeatPopup.msg'],
                                    buttons: Ext.Msg.YESNOCANCEL,
                                    fn: function (bid, text) {
                                        delete me.holdClickMaskFlag;
                                        Ext.get(me.cloneEl).show();
                                        if ("yes" == bid) {
                                            eh.updateRepeatEvent(event, cview, oevent);
                                        } else if ("no" == bid) {
                                            event.repeatType = "exception";
                                            eh.updateRepeatEvent(event, cview, oevent);
                                        }
                                    },
                                    icon: Ext.MessageBox.QUESTION
                                });
                            } else {
                                eh.updateRepeatEvent(event, cview, oevent);
                            }
                        }
                    }
                }
            }
            cview.fireEvent("canceldetail");
            this.hideEditor();
        }
    },
    onCancelFn: function () {
        var coverEl = this.bindEl;
        if (coverEl) {
            var cview = this.cview;
            var event = coverEl.bindEvent;
            var eh = this.ehandler;
            if (!coverEl.hold) {
                if ("add" == this.action) {
                    coverEl.remove();
                } else {
                    var color = eh.calendarSet[event.calendarId].color;
                    if (0 == event.startRow && this.rowCount == event.endRow) {
                        if (this.oldColor != color) {
                            eh.changeWholeColor(coverEl, this.oldColor, color);
                        }
                    } else {
                        if (this.oldColor != color) {
                            if (cview instanceof Ext.ux.calendar.view.DayView) {
                                eh.changeEventColor(coverEl, this.oldColor, color);
                            } else {
                                eh.changeLegendColor(coverEl, this.oldColor, color);
                            }
                        }
                    }
                }
            }
            this.hideEditor();
        }
    },
    popup: function (obj) {
        var eh = this.ehandler;
        this.obj = obj;
        this.noLayout = obj.noLayout;
        this.bindEl = obj.bindEl;
        this.cview = obj.cview;
        this.action = obj.action;
        var lan = Ext.ux.calendar.Language.Editor;
        if ("add" == this.action) {
            this.deleteBtn.hide();
        } else {
            this.deleteBtn.show();
        }
        this.showBy(this.bindEl, "tr-tl?");
    },
    reloadCalendar: function (eh) {
        var store = this.calendarField.store;
        store.removeAll();
        for (var p in eh.calendarSet) {
            var calendar = eh.calendarSet[p];
            if (true !== calendar.hide && !eh.isReadOnlyCalendar(calendar)) {
                var rd = {
                    id: calendar.id,
                    title: calendar.name,
                    description: calendar.description,
                    color: calendar.color,
                    _color: '#' + Ext.ux.calendar.Mask.getColorByIndex(calendar.color)
                };
                store.add(rd);
            }
        }
    },
    onShowFn: function () {
        var eh = this.ehandler;
        if (this.bindEl) {
            var coverEl = this.bindEl;
            var cloneEl = coverEl.dom.cloneNode(true);
            cloneEl.setAttribute("id", "");
            var founds = Ext.DomQuery.jsSelect("[id]", cloneEl);
            for (var i = 0, len = founds.length; i < len; i++) {
                var it = founds[i];
                it.setAttribute("id", "");
            }
            Ext.getBody().appendChild(cloneEl);
            this.cloneEl = cloneEl;
            cloneEl = Ext.get(cloneEl);
            cloneEl.setStyle("z-index", 20000);
            cloneEl.setLeft(coverEl.getLeft());
            cloneEl.setTop(coverEl.getTop());
            if (!coverEl.hold) {
                eh.setEditingStatus(coverEl, true);
            }
            var bindEvent = coverEl.bindEvent;
            var time = eh.generateInfo(bindEvent);
            this.timeBox.update("<span class=\"wb_glyph\">&#xf017</span>&nbsp;&nbsp;" + time);
            this.subjectField.setValue(bindEvent.subject);
            this.reloadCalendar(eh);
            this.calendarField.setValue(bindEvent.calendarId);
            var rec = this.calendarField.findRecordByValue(bindEvent.calendarId);
            var color = "#668CD9";
            if (rec) {
                color = rec.data.color;
                color = "#" + Ext.ux.calendar.Mask.getColorByIndex(color);
            } else {
                this.calendarField.setValue("");
                Wb.toast("请确认<b>至少启用一种</b>日历类型！");
            }
            this.headCt.getEl().setStyle("background-color", color);
            cloneEl.setStyle("background-color", color);
        }
    },
    onHideFn: function () {
        var eh = this.ehandler;
        eh.floating = false;
        var cview = this.cview;
        if (this.bindEl) {
            cview.resetSCover();
        }
        delete this.bindEl;
        delete this.cview;
        delete this.noLayout;
        delete this.action;
        if (this.cloneEl) {
            Ext.removeNode(this.cloneEl);
        }
    },
    hideEditor: function () {
        this.hide();
    }
});