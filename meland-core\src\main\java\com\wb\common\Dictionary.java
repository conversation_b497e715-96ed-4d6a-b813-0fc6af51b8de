package com.wb.common;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import com.wb.tool.DictRecord;
import com.wb.util.DbUtil;
import com.wb.util.StringUtil;

/**
 * 数据库数据字典。
 */
public class Dictionary {
	/** 字典数据缓存。 */
	public static ConcurrentHashMap<String, ConcurrentHashMap<String, DictRecord>> buffer;

	/**
	 * 在指定表名的字典信息中查找指定字段名称的字典定义记录。
	 * @param tableNames 表名列表。
	 * @param fieldName 字段名称。
	 * @return 字典定义记录。
	 */
	public static DictRecord find(String[] tableNames, String fieldName) {
		String upperFieldName = fieldName.toUpperCase();
		for (int i = 0; i < tableNames.length; i++) {
			String tableName = tableNames[i];
			ConcurrentHashMap<String, DictRecord> fieldMap = buffer.get(tableName.toUpperCase());
			if (fieldMap != null) {
				DictRecord dictRecord = (DictRecord) fieldMap.get(upperFieldName);
				if (dictRecord != null) {
					if (dictRecord.linkTo == null) {
						return dictRecord;
					}
					String[] tableField = StringUtil.split(dictRecord.linkTo.toUpperCase(), '.');
					fieldMap = buffer.get(tableField[0]);
					if (fieldMap == null) {
						return null;
					}
					return fieldMap.get(tableField[1]);
				}
			}
		}

		return null;
	}

	/**
	 * 获取指定表名的字典信息中所有配置有键值关系的字段组成的Map。如果未找到键值关系返回空的Map。
	 * @param tableNames 表名列表。
	 * @return 键值关系Map。
	 */
	public static Map<String, String> getKeyFields(String[] tableNames) {
		Map<String, String> keyMap = new HashMap<String, String>();
		for (int i = 0; i < tableNames.length; i++) {
			String tableName = tableNames[i];
			ConcurrentHashMap<String, DictRecord> fieldMap = buffer.get(tableName.toUpperCase());
			if (fieldMap != null) {
				Set<Entry<String, DictRecord>> es = fieldMap.entrySet();
				for (Entry<String, DictRecord> e : es) {
					DictRecord dictRecord = e.getValue();
					if (dictRecord.keyName != null)
						keyMap.put(e.getKey(), dictRecord.keyName);
				}
			}
		}
		return keyMap;
	}

	/**
	 * 获得结果集当前记录的字典定义对象。
	 * @param rs 结果集。
	 * @return 字典记录定义对象。
	 * @throws Exception 读取过程发生异常。
	 */
	public static DictRecord getDictRecord(ResultSet rs) throws Exception {
		DictRecord dictRecord = new DictRecord();
		dictRecord.linkTo = StringUtil.force(rs.getString("LINK_TO"));
		dictRecord.dispText = StringUtil.force(rs.getString("DISP_TEXT"));
		dictRecord.dispWidth = rs.getInt("DISP_WIDTH");
		if (rs.wasNull())
			dictRecord.dispWidth = -1;
		dictRecord.dispFormat = StringUtil.force(rs.getString("DISP_FORMAT"));
		dictRecord.noList = StringUtil.getBool(rs.getString("NO_LIST"));
		dictRecord.noEdit = StringUtil.getBool(rs.getString("NO_EDIT"));
		dictRecord.autoWrap = StringUtil.getBool(rs.getString("AUTO_WRAP"));
		dictRecord.noBlank = StringUtil.getBool(rs.getString("NO_BLANK"));
		dictRecord.readOnly = StringUtil.getBool(rs.getString("READ_ONLY"));
		dictRecord.keyName = StringUtil.force(rs.getString("KEY_NAME"));
		dictRecord.fieldSize = rs.getInt("FIELD_SIZE");
		if (rs.wasNull())
			dictRecord.fieldSize = -1;
		dictRecord.decimalPrecision = rs.getInt("DECIMAL_PRECISION");
		if (rs.wasNull())
			dictRecord.decimalPrecision = -1;
		dictRecord.validator = StringUtil.force(rs.getString("VALIDATOR"));
		dictRecord.renderer = StringUtil.force(rs.getString("RENDERER"));
		dictRecord.parentId = StringUtil.force(rs.getString("parent_id"));
		dictRecord.dataType = StringUtil.force(rs.getString("data_type"));
		dictRecord.orderIndex = rs.getInt("order_index");
		dictRecord.defaultValue = StringUtil.force(rs.getString("default_value"));
		dictRecord.serverScript = StringUtil.force(rs.getString("server_script"));
		dictRecord.calFormula = StringUtil.force(rs.getString("cal_formula"));
		dictRecord.isKey = StringUtil.getBool(rs.getString("is_key"));
		dictRecord.isLocked = StringUtil.getBool(rs.getString("is_locked"));
		return dictRecord;
	}

	/**
	 * 加载和初始化。
	 */
	public static synchronized void load() {
		try {
			buffer = new ConcurrentHashMap<String, ConcurrentHashMap<String, DictRecord>>();
			Connection conn = null;
			Statement st = null;
			ResultSet rs = null;
			String tableName = null;
			String preTableName = null;
			ConcurrentHashMap<String, DictRecord> map = new ConcurrentHashMap<String, DictRecord>();
			try {
				conn = DbUtil.getConnection();
				st = conn.createStatement();
				//关联数据结构管理，同时缓存关键数据
				rs = st.executeQuery(
						"select a.*,c.parent_id,c.data_type,c.order_index,c.default_value,c.is_key,c.is_locked,c.server_script,c.cal_formula from WB_DICT a LEFT JOIN base_data_table b on(a.TABLE_NAME=UPPER(CONCAT(b.dt_type,'_',b.table_name))) LEFT JOIN base_data_struc c ON(b.dt_id=c.dt_id AND a.FIELD_NAME=UPPER(c.field_name)) order by a.TABLE_NAME,c.order_index");
				while (rs.next()) {
					tableName = rs.getString("TABLE_NAME").toUpperCase();
					if ((preTableName != null) && (!preTableName.equals(tableName))) {
						buffer.put(preTableName, map);
						map = new ConcurrentHashMap<String, DictRecord>();
					}
					map.put(rs.getString("FIELD_NAME").toUpperCase(), getDictRecord(rs));
					preTableName = tableName;
				}
				if (preTableName != null)
					buffer.put(preTableName, map);
			} finally {
				DbUtil.close(rs);
				DbUtil.close(st);
				DbUtil.close(conn);
			}
		} catch (Throwable e) {
			throw new RuntimeException(e);
		}
	}
}