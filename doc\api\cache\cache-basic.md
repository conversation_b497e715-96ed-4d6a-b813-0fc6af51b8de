# 缓存基础操作使用文档

本文档介绍Redis缓存系统的基础操作功能，包括添加、获取、删除缓存等基本功能。

## 功能概览

Redis缓存系统提供以下基础功能：

- 添加/更新缓存
- 获取缓存
- 删除缓存
- 设置缓存过期时间
- 检查键是否存在
- 获取匹配模式的键

## Java 用法

在Java代码中，可以直接使用`Base.map`访问缓存功能：

```java
// 设置缓存（默认1分钟过期）
Base.map.setValue("user:1001", userObject);

// 设置缓存（默认24小时过期）
Base.map.put("user:1001", userObject);


// 设置带过期时间的缓存
Base.map.setValue("user:1001", userObject, 30, TimeUnit.MINUTES);

// 获取缓存
User user = (User) Base.map.getValue("user:1001");

// 删除缓存
Base.map.delKey("user:1001");

// 检查键是否存在
boolean exists = Base.map.hasKey("user:1001");

// 设置过期时间
Base.map.expire("user:1001", 30, TimeUnit.MINUTES);

// 获取匹配模式的键（会阻塞，禁用）
Set<String> keys = Base.map.getKeys("user:*");
//  获取匹配模式的键（不阻塞，推荐）
List<String> keys = Base.map.scan("user:*");
```

## XWL 脚本用法

在XWL脚本中，直接使用`Base.map`访问RedisCache功能：

```javascript
// 设置缓存（默认1分钟过期）
Base.map.setValue("user:1001", userObject);

// 设置缓存（默认24小时过期）
Base.map.put("user:1001", userObject);


// 设置带过期时间的缓存（30分钟）
Base.map.setValue("user:1001", userObject, 30, java.util.concurrent.TimeUnit.MINUTES);

// 获取缓存
var user = Base.map.getValue("user:1001");

// 删除缓存
Base.map.delKey("user:1001");

// 检查键是否存在
var exists = Base.map.hasKey("user:1001");

// 设置过期时间
Base.map.expire("user:1001", 30, java.util.concurrent.TimeUnit.MINUTES);

// 获取匹配模式的键（会阻塞，禁用）
var keys = Base.map.getKeys("user:*");
//  获取匹配模式的键（不阻塞，推荐）
var keys = Base.map.scan("user:*");
```

## 国际化处理

按照系统国际化规则，错误信息和提示可以使用：

```javascript
// 在XWL脚本中
var errorMsg = Str.format(request, "cache_operationFailed");
Wb.warn(Wb.format("Str.cache_keyNotFound"));

// 带参数的消息
var msg = Str.format(request, "cache_setSuccess", key);
Wb.info(Wb.format("Str.cache_expireTime", key, time));
```

## 示例：用户数据缓存

### Java 示例

```java
// 缓存用户信息
public User getUserWithCache(String userId) {
    String cacheKey = "user:" + userId;
    
    // 尝试从缓存获取
    Object cached = Base.map.getValue(cacheKey);
    if (cached != null) {
        return (User) cached;
    }
    
    // 缓存未命中，从数据库加载
    User user = userRepository.findById(userId);
    if (user != null) {
        // 存入缓存，设置30分钟过期
        Base.map.setValue(cacheKey, user, 30, TimeUnit.MINUTES);
    }
    
    return user;
}
```

### XWL 脚本示例

```javascript
// 缓存用户信息
function getUserWithCache(userId) {
    var cacheKey = "user:" + userId;
    
    // 尝试从缓存获取
    var cached = Base.map.getValue(cacheKey);
    if (cached != null) {
        return cached;
    }
    
    // 缓存未命中，从数据库加载
    var user = userService.findById(userId);
    if (user != null) {
        // 存入缓存，设置30分钟过期
        Base.map.setValue(cacheKey, user, 30, java.util.concurrent.TimeUnit.MINUTES);
    }
    
    return user;
}
```

## 注意事项

1. 键名命名建议使用冒号分隔的方式，如`模块:类型:ID`，例如`user:profile:1001`
2. 对于大对象，避免频繁缓存更新，消耗网络带宽
3. 缓存的对象需要是可序列化的 (实现Serializable接口)
4. 合理设置过期时间，避免缓存过期造成缓存雪崩
5. 在业务复杂的场景中，考虑使用更高级的缓存策略，如布隆过滤器、本地缓存等 