# Redis缓存系统使用文档

本文档提供Redis缓存系统的完整使用说明，涵盖基础操作到高级功能的所有内容。

## 文档目录

1. **[缓存基础操作](cache-basic.md)**
   - 添加/获取/删除缓存
   - 设置过期时间
   - 键存在检查
   - 键模式匹配

2. **[本地缓存与缓存防护](cache-local.md)**
   - 本地缓存功能
   - 缓存穿透防护
   - 缓存击穿防护（互斥锁）
   - 缓存雪崩防护
   - 缓存统计功能

3. **[分布式锁](cache-lock.md)**
   - 获取/释放分布式锁
   - 等待时间与锁超时
   - Redisson客户端高级功能

4. **[Hash操作](cache-hash.md)**
   - Hash字段操作
   - 批量Hash字段操作
   - List序列化到Hash

5. **[集合操作](cache-collections.md)**
   - Set集合操作
   - ZSet有序集合操作
   - List列表操作

6. **[布隆过滤器](cache-bloomfilter.md)**
   - 标准布隆过滤器
   - 计数布隆过滤器（支持删除）
   - 与缓存结合使用

7. **[高级操作](cache-advanced.md)**
   - Pipeline管道操作
   - 批量删除/设置
   - 序列号生成器

8. **[发布/订阅功能](cache-pubsub.md)**
   - 消息发布
   - 消息订阅
   - 实时通知
   - 分布式事件处理

9. **[缓存统计功能](cache-stats.md)**
   - 缓存命中率统计
   - 本地缓存监控
   - 性能监控
   - 缓存问题诊断

## 在Java代码中使用

在Java代码中，可以直接使用`Base.map`访问缓存功能：

```java
// 使用缓存
Base.map.setValue("key", value);
Object cachedValue = Base.map.getValue("key");
```

## 在XWL脚本中使用

在XWL脚本中，直接使用`Base.map`访问缓存功能：

```javascript
// 直接使用Base.map
Base.map.setValue("key", value);
var cachedValue = Base.map.getValue("key");
```

## 国际化支持

系统支持国际化错误消息和提示，可按照以下方式使用：

```javascript
// 在XWL脚本中
var errorMsg = Str.format(request, "cache_operationFailed");
Wb.warn(Wb.format("Str.cache_keyNotFound"));
```

## 最佳实践

1. **键命名规范**
   - 使用冒号分隔的层次结构，如`business:entity:id`
   - 例如：`user:profile:1001`、`product:inventory:5001`

2. **缓存策略**
   - 对频繁访问但不常变化的数据使用较长的过期时间
   - 对热点数据考虑使用本地缓存
   - 使用布隆过滤器防止缓存穿透
   - 使用随机过期时间防止缓存雪崩

3. **性能优化**
   - 对批量操作使用Pipeline或批量方法
   - 合理设置过期时间，避免频繁缓存更新
   - 监控缓存命中率和操作延迟

## 版本兼容性

当前文档适用于Redis缓存系统v2.0.0及以上版本。 