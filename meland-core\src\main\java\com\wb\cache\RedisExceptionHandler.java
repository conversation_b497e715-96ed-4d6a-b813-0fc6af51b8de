package com.wb.cache;

import com.wb.util.LogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.ErrorHandler;
import com.wb.common.Base;

import javax.annotation.PostConstruct;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Redis异常处理器及缓存同步处理
 * 用于处理Redis连接异常和缓存失效同步
 */
@Component
public class RedisExceptionHandler implements ErrorHandler, InitializingBean {

    private static final Logger logger = LoggerFactory.getLogger(RedisExceptionHandler.class);

    /**
     * 错误计数器
     */
    private final AtomicInteger errorCounter = new AtomicInteger(0);
    
    /**
     * 最后一次重置时间
     */
    private long lastResetTime = 0;
    
    /**
     * 最后一次日志记录时间
     */
    private long lastLogTime = 0;
    
    /**
     * 日志记录间隔（毫秒）
     */
    private static final long LOG_INTERVAL = 10000; // 10秒
    
    /**
     * 错误阈值，超过此值记录ERROR级别日志
     */
    private static final int ERROR_THRESHOLD = 5;
    
    /**
     * 重置阈值，连续错误超过此值将尝试重置连接
     */
    private static final int RESET_THRESHOLD = 3;
    
    /**
     * 最小重置间隔（毫秒）
     */
    private static final long MIN_RESET_INTERVAL = 30000; // 30秒
    
    /**
     * Redis连接工厂
     */
    @Autowired
    private RedisConnectionFactory connectionFactory;
    
    /**
     * 连接重试次数
     */
    private int connectionRetryCount = 0;
    
    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 3;
    
    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        logger.info("Redis异常处理器初始化");
    }
    
    /**
     * Bean属性设置完成后执行
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        logger.info("Redis异常处理器已准备就绪");
    }
    
    /**
     * 处理异常
     * @param t 异常对象
     */
    @Override
    public void handleError(Throwable t) {
        if (t instanceof RedisConnectionFailureException) {
            // 增加错误计数
            int count = errorCounter.incrementAndGet();
            long now = System.currentTimeMillis();
            boolean shouldLog = (now - lastLogTime) > LOG_INTERVAL;
            
            // 特殊处理"Unexpected end of stream"错误
            boolean isEndOfStream = t.getMessage() != null && 
                    t.getMessage().contains("Unexpected end of stream");
            
            if (isEndOfStream) {
                if (shouldLog) {
                    logger.warn("Redis连接意外中断: {}", t.getMessage());
                    lastLogTime = now;
                }
                tryResetConnection();
                return;
            }
            
            // 处理其他Redis连接异常
            if (count >= RESET_THRESHOLD) {
                // 连续错误超过阈值，尝试重置连接
                if (count > ERROR_THRESHOLD && shouldLog) {
                    logger.error("Redis连接持续失败{}次: {}", count, t.getMessage());
                    lastLogTime = now;
                } else if (shouldLog) {
                    logger.warn("Redis连接失败({}): {}", count, t.getMessage());
                    lastLogTime = now;
                }
                tryResetConnection();
            } else if (shouldLog) {
                // 错误次数在阈值内，仅记录警告
                logger.warn("Redis连接异常({}/{}): {}", count, RESET_THRESHOLD, t.getMessage());
                lastLogTime = now;
            }
        } else {
            // 非连接异常，始终记录
            logger.error("Redis操作异常: {}", t.getMessage(), t);
        }
    }
    
    /**
     * 尝试重置连接
     * 包含时间间隔控制，避免频繁重置
     */
    public void tryResetConnection() {
        long now = System.currentTimeMillis();
        
        // 检查是否满足最小重置间隔
        if (now - lastResetTime < MIN_RESET_INTERVAL) {
            logger.info("上次重置时间间隔过短，跳过本次重置");
            return;
        }
        
        try {
            logger.info("正在重置Redis连接...");
            
            if (connectionFactory instanceof org.springframework.data.redis.connection.jedis.JedisConnectionFactory) {
                org.springframework.data.redis.connection.jedis.JedisConnectionFactory jedisFactory = 
                    (org.springframework.data.redis.connection.jedis.JedisConnectionFactory) connectionFactory;
                
                // 销毁并重新初始化
                jedisFactory.destroy();
                jedisFactory.afterPropertiesSet();
                
                // 更新状态
                errorCounter.set(0);
                lastResetTime = now;
                
                logger.info("Redis连接重置完成");
            } else {
                logger.warn("不支持的Redis连接工厂类型: {}", connectionFactory.getClass().getName());
            }
        } catch (Exception e) {
            logger.error("重置Redis连接失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 手动重置Redis连接
     * 可以在需要时通过其他组件调用
     */
    public void resetConnection() {
        errorCounter.set(RESET_THRESHOLD);
        tryResetConnection();
    }
    
    /**
     * 重置连接重试计数
     */
    public void resetRetryCount() {
        connectionRetryCount = 0;
    }
    
    /**
     * 处理缓存失效消息
     * 
     * @param key 要失效的缓存键
     */
    public void handleCacheInvalidation(String key) {
        if (Base.map != null) {
            // 从本地缓存删除
            Base.map.clearLocalCache();
            LogUtil.info("从本地缓存中删除键: " + key);
        }
    }
    
    /**
     * 处理清除所有缓存的消息
     */
    public void handleClearAllCache() {
        if (Base.map != null) {
            // 清空本地缓存
            Base.map.clearLocalCache();
            LogUtil.info("清空本地缓存");
        }
    }
    
    /**
     * 处理缓存更新消息
     * 
     * @param key 缓存键
     * @param valueJson 值的JSON字符串
     */
    public void handleCacheUpdate(String key, String valueJson) {
        // 此方法可以根据需要更新本地缓存
        // 由于本地缓存通常使用过期时间，通常只需要清除对应的键即可
        handleCacheInvalidation(key);
    }
} 