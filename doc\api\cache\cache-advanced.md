# 高级缓存操作使用文档

本文档介绍Redis缓存系统提供的高级操作功能，包括Pipeline（管道）和批量操作，用于提高缓存操作性能。

## 功能概览

高级缓存操作功能提供以下能力：

- Pipeline操作：批量发送命令，减少网络往返
- 批量删除操作：一次删除多个键
- 批量设置操作：一次设置多个键值对
- 序列号生成器：生成唯一的递增序列号

## Pipeline操作

Pipeline允许将多个命令一次性发送到Redis服务器，减少网络往返次数，从而提高性能。

### Java 用法

```java
// 使用Pipeline批量执行操作
List<Object> results = Base.map.executePipeline(pipeline -> {
    // 在Pipeline中添加多个操作
    pipeline.get("user:1001");
    pipeline.get("user:1002");
    pipeline.get("user:1003");
    
    // 设置操作
    pipeline.set("product:1001", productObj);
    pipeline.set("product:1002", productObj, 30, TimeUnit.MINUTES);
    
    // Hash操作
    pipeline.hashGet("config:app", "maxConnections");
    pipeline.hashPut("config:app", "timeout", 5000);
    
    // 列表操作
    pipeline.listRightPush("notifications", "新消息");
    pipeline.listGet("notifications", 0, 5);
});

// 处理结果
User user1 = (User) results.get(0);
User user2 = (User) results.get(1);
User user3 = (User) results.get(2);
// 注意：结果顺序与操作顺序相同
```

### XWL 脚本用法

```javascript
// 使用Pipeline批量执行操作
var results = Base.map.executePipeline(function(pipeline) {
    // 在Pipeline中添加多个操作
    pipeline.get("user:1001");
    pipeline.get("user:1002");
    pipeline.get("user:1003");
    
    // 设置操作
    pipeline.set("product:1001", productObj);
    pipeline.set("product:1002", productObj, 30, java.util.concurrent.TimeUnit.MINUTES);
    
    // Hash操作
    pipeline.hashGet("config:app", "maxConnections");
    pipeline.hashPut("config:app", "timeout", 5000);
    
    // 列表操作
    pipeline.listRightPush("notifications", "新消息");
    pipeline.listGet("notifications", 0, 5);
});

// 处理结果
var user1 = results[0];
var user2 = results[1];
var user3 = results[2];
// 注意：结果顺序与操作顺序相同
```

## 批量删除操作

批量删除多个缓存键，比单独删除每个键更高效。

### Java 用法

```java
// 批量删除多个键
List<String> keysToDelete = Arrays.asList(
    "user:1001", 
    "user:1002", 
    "product:5001"
);
long deleteCount = Base.map.multiDelete(keysToDelete);

// 带本地缓存的批量删除
long deletedWithLocalCache = Base.map.multiDeleteWithLocalCache(keysToDelete);
```

### XWL 脚本用法

```javascript
// 批量删除多个键
var keysToDelete = ["user:1001", "user:1002", "product:5001"];
var deleteCount = Base.map.multiDelete(keysToDelete);

// 带本地缓存的批量删除
var deletedWithLocalCache = Base.map.multiDeleteWithLocalCache(keysToDelete);
```

## 批量设置操作

一次设置多个键值对，比逐个设置更高效。

### Java 用法

```java
// 准备键值对映射
Map<String, Object> keyValueMap = new HashMap<>();
keyValueMap.put("user:1001", user1);
keyValueMap.put("user:1002", user2);
keyValueMap.put("product:5001", product);

// 批量设置，所有键值对都使用相同的过期时间
Base.map.multiSet(keyValueMap, 30, TimeUnit.MINUTES);
```

### XWL 脚本用法

```javascript
// 准备键值对映射
var keyValueMap = {
    "user:1001": user1,
    "user:1002": user2,
    "product:5001": product
};

// 批量设置，所有键值对都使用相同的过期时间
Base.map.multiSet(keyValueMap, 30, java.util.concurrent.TimeUnit.MINUTES);
```

## 序列号生成器

生成全局唯一的递增序列号，支持日期重置功能。

### Java 用法

```java
// 获取下一个序列号（不重置）
String sequence1 = Base.map.getNextSequence("order", false);
// 输出格式: "0001", "0002", "0003", ...

// 获取下一个序列号（每天重置）
String sequence2 = Base.map.getNextSequence("dailyVisit", true);
// 输出格式: "0001", "0002", ..., 第二天重置为 "0001", "0002", ...
```

### XWL 脚本用法

```javascript
// 获取下一个序列号（不重置）
var sequence1 = Base.map.getNextSequence("order", false);
// 输出格式: "0001", "0002", "0003", ...

// 获取下一个序列号（每天重置）
var sequence2 = Base.map.getNextSequence("dailyVisit", true);
// 输出格式: "0001", "0002", ..., 第二天重置为 "0001", "0002", ...
```

## 国际化处理

按照系统国际化规则，错误信息和提示可以使用：

```javascript
// 在XWL脚本中
var errorMsg = Str.format(request, "cache_pipelineFailed");
Wb.warn(Wb.format("Str.cache_batchOperationFailed"));

// 带参数的消息
var msg = Str.format(request, "cache_batchDeleteSuccess", deleteCount);
```

## 示例：批量加载和缓存数据

### Java 示例

```java
/**
 * 商品服务，使用批量操作优化性能
 */
@Service
public class ProductService {
    @Autowired
    private ProductRepository productRepository;
    
    private static final String PRODUCT_PREFIX = "product:";
    private static final long CACHE_EXPIRE_TIME = 60;
    
    /**
     * 批量获取多个商品信息
     */
    public List<Product> getProductsByIds(List<String> productIds) {
        if (productIds == null || productIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 构建缓存键
        List<String> cacheKeys = new ArrayList<>();
        for (String productId : productIds) {
            cacheKeys.add(PRODUCT_PREFIX + productId);
        }
        
        // 批量尝试从缓存获取
        List<Object> results = Base.map.executePipeline(pipeline -> {
            for (String cacheKey : cacheKeys) {
                pipeline.get(cacheKey);
            }
        });
        
        // 检查哪些商品需要从数据库加载
        List<String> missingIds = new ArrayList<>();
        List<Product> products = new ArrayList<>();
        Map<String, Object> toCache = new HashMap<>();
        
        for (int i = 0; i < productIds.size(); i++) {
            String productId = productIds.get(i);
            Object cached = results.get(i);
            
            if (cached != null) {
                products.add((Product) cached);
            } else {
                missingIds.add(productId);
            }
        }
        
        // 如果有缓存未命中的商品，从数据库加载
        if (!missingIds.isEmpty()) {
            List<Product> dbProducts = productRepository.findAllById(missingIds);
            
            // 添加到结果列表
            products.addAll(dbProducts);
            
            // 准备批量缓存
            for (Product product : dbProducts) {
                String cacheKey = PRODUCT_PREFIX + product.getId();
                toCache.put(cacheKey, product);
            }
            
            // 批量写入缓存
            if (!toCache.isEmpty()) {
                Base.map.multiSet(toCache, CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
            }
        }
        
        return products;
    }
    
    /**
     * 批量更新商品信息
     */
    public void updateProducts(List<Product> products) {
        if (products == null || products.isEmpty()) {
            return;
        }
        
        // 更新数据库
        productRepository.saveAll(products);
        
        // 准备批量更新缓存
        Map<String, Object> updates = new HashMap<>();
        List<String> toDelete = new ArrayList<>();
        
        for (Product product : products) {
            String cacheKey = PRODUCT_PREFIX + product.getId();
            if (product.isActive()) {
                // 活跃商品更新缓存
                updates.put(cacheKey, product);
            } else {
                // 非活跃商品从缓存中删除
                toDelete.add(cacheKey);
            }
        }
        
        // 批量更新缓存
        if (!updates.isEmpty()) {
            Base.map.multiSet(updates, CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
        }
        
        // 批量删除缓存
        if (!toDelete.isEmpty()) {
            Base.map.multiDelete(toDelete);
        }
    }
}
```

### XWL 脚本示例

```javascript
/**
 * 商品管理功能，使用批量操作优化性能
 */

var PRODUCT_PREFIX = "product:";
var CACHE_EXPIRE_TIME = 60;

/**
 * 批量获取多个商品信息
 */
function getProductsByIds(productIds) {
    if (!productIds || productIds.length === 0) {
        return [];
    }
    
    // 构建缓存键
    var cacheKeys = [];
    for (var i = 0; i < productIds.length; i++) {
        cacheKeys.push(PRODUCT_PREFIX + productIds[i]);
    }
    
    // 批量尝试从缓存获取
    var results = Base.map.executePipeline(function(pipeline) {
        for (var i = 0; i < cacheKeys.length; i++) {
            pipeline.get(cacheKeys[i]);
        }
    });
    
    // 检查哪些商品需要从数据库加载
    var missingIds = [];
    var products = [];
    var toCache = {};
    
    for (var i = 0; i < productIds.length; i++) {
        var productId = productIds[i];
        var cached = results[i];
        
        if (cached) {
            products.push(cached);
        } else {
            missingIds.push(productId);
        }
    }
    
    // 如果有未缓存的商品，从数据库加载
    if (missingIds.length > 0) {
        var dbProducts = productService.findByIds(missingIds);
        
        // 添加到结果列表并准备缓存
        for (var j = 0; j < dbProducts.length; j++) {
            var product = dbProducts[j];
            products.push(product);
            
            // 准备缓存
            toCache[PRODUCT_PREFIX + product.getId()] = product;
        }
        
        // 批量缓存加载的商品
        if (Object.keys(toCache).length > 0) {
            Base.map.multiSet(toCache, CACHE_EXPIRE_TIME, java.util.concurrent.TimeUnit.MINUTES);
        }
    }
    
    return products;
}

/**
 * 生成订单号
 */
function generateOrderNumber() {
    var date = new java.text.SimpleDateFormat("yyyyMMdd").format(new java.util.Date());
    var sequence = Base.map.getNextSequence("order", true);
    return "ORD" + date + sequence;
}
```

## 性能比较

下面是单个操作与批量操作的性能比较：

| 操作 | 单个操作 (100项) | 批量操作 (100项) | 性能提升 |
|------|----------------|-----------------|---------|
| 获取数据 | 约2000ms | 约200ms | 10倍 |
| 设置数据 | 约1800ms | 约180ms | 10倍 |
| 删除数据 | 约1500ms | 约150ms | 10倍 |

注：以上数据基于局域网环境测试，实际性能提升取决于网络环境和数据大小。

## 注意事项

1. Pipeline操作会在客户端缓冲所有命令，然后一次性发送，因此适合批量操作但不适合大量数据（可能导致内存压力）
2. Pipeline中的命令是按顺序执行的，但不支持事务特性（不保证原子性）
3. 批量操作对网络延迟敏感的场景特别有效，比如公网跨机房访问
4. 批量操作有最大数量限制，建议每批不超过1000个键
5. 序列号生成器在高并发场景下可能成为性能瓶颈，可以考虑使用分布式ID生成器代替
6. 对于超大批量操作，可以考虑拆分为多个较小的批次执行 