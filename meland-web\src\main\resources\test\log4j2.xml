<?xml version="1.0" encoding="UTF-8"?>

<!--
    status : 这个用于设置log4j2自身内部的信息输出,可以不设置,当设置成trace时,会看到log4j2内部各种详细输出
    monitorInterval : Log4j能够自动检测修改配置文件和重新配置本身, 设置间隔秒数。

    注：本配置文件的目标是将不同级别的日志输出到不同文件，最大2MB一个文件，
    文件数据达到最大值时，旧数据会被压缩并放进指定文件夹
-->
<Configuration status="DEBUG" monitorInterval="600">

    <Properties>
        <!-- 配置日志文件输出目录，此配置将日志输出到tomcat根目录下的指定文件夹 -->
        <Property name="LOG_HOME">logs/app_logs</Property>
    </Properties>

    <Appenders>

        <!--这个输出控制台的配置，这里输出除了warn和error级别的信息到System.out-->
        <Console name="console_out_appender" target="SYSTEM_OUT">
            <!-- 控制台只输出level及以上级别的信息(onMatch),其他的直接拒绝(onMismatch) -->
            <ThresholdFilter level="warn" onMatch="DENY" onMismatch="ACCEPT"/>
            <!-- 输出日志的格式 -->
            <PatternLayout pattern="%5p [%t] %d{yyyy-MM-dd HH:mm:ss} (%F:%L) %m%n"/>
        </Console>
        <!--这个输出控制台的配置，这里输出warn和error级别的信息到System.err-->
        <Console name="console_err_appender" target="SYSTEM_ERR">
            <!-- 控制台只输出level及以上级别的信息(onMatch),其他的直接拒绝(onMismatch) -->
            <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            <!-- 输出日志的格式 -->
            <PatternLayout pattern="%5p [%t] %d{yyyy-MM-dd HH:mm:ss} (%F:%L) %m%n"/>
        </Console>
        <!-- DEBUG级别日志 -->
        <RollingRandomAccessFile name="debug_appender"
                                 immediateFlush="true" fileName="${LOG_HOME}/debug.log"
                                 filePattern="/debug/debug - %d{yyyy-MM-dd HH_mm_ss}.log.gz">
            <PatternLayout>
                <pattern>%5p [%t] %d{yyyy-MM-dd HH:mm:ss} (%F:%L) %m%n</pattern>
            </PatternLayout>
            <Policies>
                <SizeBasedTriggeringPolicy size="2MB"/>
            </Policies>
            <Filters>
                <ThresholdFilter level="info" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>
        <!-- INFO级别日志 -->
        <RollingRandomAccessFile name="info_appender"
                                 immediateFlush="true" fileName="${LOG_HOME}/info.log"
                                 filePattern="/info/info - %d{yyyy-MM-dd HH_mm_ss}.log.gz">
            <PatternLayout>
                <pattern>%5p [%t] %d{yyyy-MM-dd HH:mm:ss} (%F:%L) %m%n</pattern>
            </PatternLayout>
            <Policies>
                <SizeBasedTriggeringPolicy size="2MB"/>
            </Policies>
            <Filters>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>

        <!-- WARN级别日志 -->
        <RollingRandomAccessFile name="warn_appender"
                                 immediateFlush="true" fileName="${LOG_HOME}/warn.log"
                                 filePattern="/warn/warn - %d{yyyy-MM-dd HH_mm_ss}.log.gz">
            <PatternLayout>
                <pattern>%5p [%t] %d{yyyy-MM-dd HH:mm:ss} (%F:%L) %m%n</pattern>
            </PatternLayout>
            <Policies>
                <SizeBasedTriggeringPolicy size="2MB"/>
            </Policies>
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>

        <!-- ERROR级别日志 -->
        <RollingRandomAccessFile name="error_appender"
                                 immediateFlush="true" fileName="${LOG_HOME}/error.log"
                                 filePattern="/error/error - %d{yyyy-MM-dd HH_mm_ss}.log.gz">
            <PatternLayout>
                <pattern>%5p [%t] %d{yyyy-MM-dd HH:mm:ss} (%F:%L) %m%n</pattern>
            </PatternLayout>
            <Policies>
                <SizeBasedTriggeringPolicy size="2MB"/>
            </Policies>
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>
    </Appenders>

    <Loggers>
        <!-- 配置日志的根节点 -->
        <root level="info">
            <appender-ref ref="console_out_appender"/>
            <appender-ref ref="console_err_appender"/>
            <appender-ref ref="debug_appender"/>
            <appender-ref ref="info_appender"/>
            <appender-ref ref="warn_appender"/>
            <appender-ref ref="error_appender"/>
        </root>

        <!-- 项目相关包使用DEBUG级别 -->
        <logger name="com.wb" level="debug"/>
        <logger name="net.arccode" level="debug"/>
        <logger name="com.lonlysky" level="debug"/>

        <!-- Redis相关日志配置 -->
        <logger name="org.redisson" level="info"/>
        <logger name="io.netty" level="info"/>
        <logger name="redis.clients" level="info"/>
        
        <!-- Redis连接异常日志精简配置 -->
        <logger name="org.springframework.data.redis.connection" level="error"/>
        <logger name="org.springframework.data.redis.listener" level="warn"/>
        <logger name="redis.clients.jedis.JedisSentinelPool" level="error"/>
        <logger name="com.wb.cache.RedisExceptionHandler" level="warn"/>

        <!-- 其他第三方框架日志级别 -->
        <logger name="org.jboss.netty" level="warn"/>
        <logger name="org.apache.http" level="warn"/>
        <logger name="org.springframework" level="info"/>
        <logger name="org.hibernate" level="info"/>
        <logger name="com.alibaba" level="info"/>
        <logger name="com.alipay" level="info"/>
        <logger name="org.mybatis" level="info"/>
        <logger name="org.apache" level="info"/>
        <logger name="javax.servlet" level="info"/>

    </Loggers>

</Configuration>