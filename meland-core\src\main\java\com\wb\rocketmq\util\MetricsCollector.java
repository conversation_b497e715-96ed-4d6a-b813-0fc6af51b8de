package com.wb.rocketmq.util;

import com.wb.common.Var;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * RocketMQ指标收集器
 * 用于收集RocketMQ的发送和处理性能数据
 */
public class MetricsCollector {
    // 消息发送计数器 (topic+tag -> count)
    private final ConcurrentHashMap<String, AtomicLong> messagesSent = new ConcurrentHashMap<>();
    
    // 消息发送失败计数器 (topic+tag -> count)
    private final ConcurrentHashMap<String, AtomicLong> messagesFailed = new ConcurrentHashMap<>();
    
    // 处理时间计数器 (topic+tag -> totalTime)
    private final ConcurrentHashMap<String, AtomicLong> processingTime = new ConcurrentHashMap<>();
    
    // 消息处理次数计数器 (topic+tag -> count)，用于计算平均处理时间
    private final ConcurrentHashMap<String, AtomicLong> processingCount = new ConcurrentHashMap<>();
    
    /**
     * 记录消息发送
     * @param topic 主题
     * @param tag 标签
     */
    public void recordMessageSent(String topic, String tag) {
        if (topic == null) topic = "";
        if (tag == null) tag = "";
        
        String key = topic + ":" + tag;
        messagesSent.computeIfAbsent(key, k -> new AtomicLong(0)).incrementAndGet();
        
        if (Var.getBool("sys.config.aliyun.logDebug")) {
            LogUtil.info(StringUtil.format("RocketMQ指标: 发送消息 topic={0}, tag={1}", topic, tag));
        }
    }
    
    /**
     * 记录消息发送失败
     * @param topic 主题
     * @param tag 标签
     */
    public void recordSendFailed(String topic, String tag) {
        if (topic == null) topic = "";
        if (tag == null) tag = "";
        
        String key = topic + ":" + tag;
        messagesFailed.computeIfAbsent(key, k -> new AtomicLong(0)).incrementAndGet();
        
        if (Var.getBool("sys.config.aliyun.logDebug")) {
            LogUtil.info(StringUtil.format("RocketMQ指标: 发送失败 topic={0}, tag={1}", topic, tag));
        }
    }
    
    /**
     * 记录处理时间
     * @param topic 主题
     * @param tag 标签
     * @param timeMillis 处理时间(毫秒)
     */
    public void recordProcessingTime(String topic, String tag, long timeMillis) {
        if (topic == null) topic = "";
        if (tag == null) tag = "";
        
        String key = topic + ":" + tag;
        processingTime.computeIfAbsent(key, k -> new AtomicLong(0)).addAndGet(timeMillis);
        processingCount.computeIfAbsent(key, k -> new AtomicLong(0)).incrementAndGet();
        
        if (Var.getBool("sys.config.aliyun.logDebug")) {
            LogUtil.info(StringUtil.format("RocketMQ指标: 处理时间 topic={0}, tag={1}, time={2}ms", 
                topic, tag, timeMillis));
        }
    }
    
    /**
     * 获取指定主题和标签的发送计数
     * @param topic 主题
     * @param tag 标签
     * @return 发送计数
     */
    public long getSentCount(String topic, String tag) {
        if (topic == null) topic = "";
        if (tag == null) tag = "";
        
        String key = topic + ":" + tag;
        AtomicLong count = messagesSent.get(key);
        return count != null ? count.get() : 0;
    }
    
    /**
     * 获取指定主题和标签的平均处理时间
     * @param topic 主题
     * @param tag 标签
     * @return 平均处理时间(毫秒)
     */
    public double getAverageProcessingTime(String topic, String tag) {
        if (topic == null) topic = "";
        if (tag == null) tag = "";
        
        String key = topic + ":" + tag;
        AtomicLong time = processingTime.get(key);
        AtomicLong count = processingCount.get(key);
        
        if (time == null || count == null || count.get() == 0) {
            return 0;
        }
        
        return (double) time.get() / count.get();
    }
    
    /**
     * 获取指定主题和标签的失败率
     * @param topic 主题
     * @param tag 标签
     * @return 失败率(0-1之间的小数)
     */
    public double getFailureRate(String topic, String tag) {
        if (topic == null) topic = "";
        if (tag == null) tag = "";
        
        String key = topic + ":" + tag;
        AtomicLong sent = messagesSent.get(key);
        AtomicLong failed = messagesFailed.get(key);
        
        if (sent == null || failed == null || sent.get() == 0) {
            return 0;
        }
        
        return (double) failed.get() / sent.get();
    }
} 