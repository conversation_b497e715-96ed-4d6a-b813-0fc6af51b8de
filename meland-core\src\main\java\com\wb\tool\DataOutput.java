package com.wb.tool;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.StringWriter;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.io.IOUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.json.JSONArray;
import org.json.JSONObject;

import com.wb.common.Base;
import com.wb.common.KVBuffer;
import com.wb.common.Var;
import com.wb.util.DateUtil;
import com.wb.util.JsonUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;

public class DataOutput {

	/**
	 * 向指定输出流中输出Excel xls/xlsx格式的文件数据。是否使用xls/xlsx格式由变量
	 * sys.service.excel.xlsx设置。
	 * @param outputStream 输出流。输出数据后该流不关闭，如果有必要需要手动关闭。
	 * @param headers 标题列。标题列允许嵌套，并根据设置的嵌套自动合并单元格。
	 * 标题列所有单元格都为字符串格式。
	 * @param records 输出的记录数据。
	 * @param title 标题。标题将显示在首行并合并所在行所有单元格且居中显示。如果为null，
	 * 将不生成标题。
	 * @param dateFormat 当未指定格式时使用的默认日期格式。
	 * @param timeFormat 当未指定格式时使用的默认时间格式。
	 * @param neptune 客户端是否为海王星主题，海王星主题列较宽映射到表格时需要按比缩小。
	 */
	public static void outputExcel(OutputStream outputStream, JSONArray headers, JSONArray records, String title,
								   JSONObject reportInfo, String dateFormat, String timeFormat, boolean neptune) throws Exception {
		int startRow = 0;

		JSONArray topHtml = reportInfo.optJSONArray("topHtml");
		JSONArray bottomHtml = reportInfo.optJSONArray("bottomHtml");

		// POI在导出XML格式数据且程序重新加载时提示create a memory leak，实际无影响
		// POI3.5之后版本在某些应用服务器上运行一直有该信息提示，测试无内存泄漏
		Workbook book = ExcelObject.getBook();
		JSONArray fields;
		int headerRows;
		int headerCols;
		Object[] values;
		Sheet sheet;
		try {
			sheet = book.createSheet();
			if (topHtml != null) {
				createHtml(sheet, topHtml);
				startRow = topHtml.length();
				title = null;
			}
			if (title != null) {
				startRow = 1;
			}
			values = createHeaders(sheet, headers, startRow, neptune);
			headerCols = (Integer) values[0];
			headerRows = (Integer) values[1];
			fields = (JSONArray) values[2];
			if (title != null)
				createTitle(sheet, title, headerCols);
			startRow += headerRows;
			if (Var.getBool("sys.service.excel.freezePane"))
				sheet.createFreezePane(0, startRow);
			createRecords(sheet, records, fields, startRow, dateFormat, timeFormat);
			ExcelObject.mergeCells(sheet, reportInfo, startRow, Integer.MAX_VALUE);
			if (bottomHtml != null)
				createHtml(sheet, bottomHtml);
			book.write(outputStream);
		} finally {
			book.close();
		}
	}

	/**
	 * 设置Sheet中的标题及其样式。
	 * @param sheet sheet对象。
	 * @param title 标题文本。
	 * @param headerCols 标题占的列数量。
	 */
	protected static void createTitle(Sheet sheet, String title, int headerCols) {
		Cell cell;
		Row row = sheet.createRow(0);
		Object styles[] = createCellStyle(sheet.getWorkbook(), "title");

		row.setHeight((Short) styles[1]);
		if (headerCols > 1) {
			sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headerCols - 1));
		}
		cell = row.createCell(0);
		cell.setCellStyle((CellStyle) styles[0]);
		cell.setCellValue(title);
	}

	/**
	 * 根据表格HTML数据创建对应的Excel数据。
	 * @param sheet sheet对象。
	 * @param data HTML数据。
	 */
	protected static void createHtml(Sheet sheet, JSONArray data) {
		Workbook book = sheet.getWorkbook();

		int y = data.length();
		int startIndex = sheet.getLastRowNum();

		if (startIndex > 0)
			startIndex++;
		for (int x = 0; x < y; x++) {
			JSONObject dataRow = data.optJSONObject(x);
			JSONArray dataCells = dataRow.optJSONArray("items");
			Row row = sheet.createRow(startIndex + x);
			row.setHeight((short) (dataRow.optInt("height") * 20));
			int j = dataCells.length();
			int cellIndex = 0;
			for (int i = 0; i < j; i++) {
				JSONObject dataCell = dataCells.optJSONObject(i);
				int colSpan = dataCell.optInt("colSpan");
				Cell cell = row.createCell(cellIndex);
				cell.setCellValue(dataCell.optString("value"));
				CellStyle style = book.createCellStyle();
				style.setAlignment(ExcelObject.getAlignment(dataCell.optString("align"), HorizontalAlignment.LEFT));
				style.setVerticalAlignment(VerticalAlignment.CENTER);
				Font font = book.createFont();
				if ("bold".equals(dataCell.optString("weight")))
					font.setBold(true); //font.setBoldweight((short) 700);(已过时)
				int fontSize = dataCell.optInt("size");
				if (fontSize > 0)
					font.setFontHeightInPoints((short) fontSize);
				style.setFont(font);
				cell.setCellStyle(style);
				for (int k = 1; k < colSpan; k++) {
					row.createCell(cellIndex + k);
				}
				if (colSpan > 1) {
					sheet.addMergedRegion(
							new CellRangeAddress(startIndex + x, startIndex + x, cellIndex, cellIndex + colSpan - 1));
				}
				cellIndex += colSpan;
			}
		}
	}

	/**
	 * 创建列标题。
	 * @param sheet Sheet对象。
	 * @param headers 原始列标题列表。
	 * @param startRow 开始行索引号。
	 * @param neptune 客户端是否为海王星主题。
	 * @return 对象数组，0项列宽度，1项列高度，2项字段元数据。
	 */
	protected static Object[] createHeaders(Sheet sheet, JSONArray headers, int startRow, boolean neptune) {
		Workbook book = sheet.getWorkbook();

		JSONArray processedHeaders = new JSONArray();
		Object[] values = prepareHeaders(sheet, headers, processedHeaders, startRow, neptune);
		Cell[][] cells = (Cell[][]) values[0];
		Object[] styles = createCellStyle(book, "header");
		CellStyle baseStyle = (CellStyle) styles[0];

		int j = processedHeaders.length();
		for (int i = 0; i < j; i++) {
			JSONObject header = processedHeaders.getJSONObject(i);
			int x = header.getInt("x");
			int y = header.getInt("y");
			int colspan = Math.max(header.getInt("colspan"), 0);
			int rowspan = Math.max(header.getInt("rowspan"), 0);
			if ((colspan > 0) || (rowspan > 0)) {
				sheet.addMergedRegion(new CellRangeAddress(y + startRow, y + startRow + rowspan, x, x + colspan));
			}
			Cell cell = cells[x][y];
			CellStyle style = book.createCellStyle();
			style.cloneStyleFrom(baseStyle);
			style.setAlignment(ExcelObject.getAlignment(header.optString("titleAlign"),
					header.has("child") ? HorizontalAlignment.CENTER : HorizontalAlignment.LEFT));
			cell.setCellStyle(style);
			cell.setCellValue(header.optString("text"));
		}
		Object[] result = new Object[3];
		result[0] = cells.length;
		result[1] = cells[0].length;
		result[2] = values[1];
		return result;
	}

	/**
	 * 预处理列标题，创建单元格方阵，并标记每个header的x,y,colspan,rowspan属性。
	 * @param sheet Excel的Sheet对象。
	 * @param rawHeaders 原始列标题列表。
	 * @param processedHeaders 处理后的列标题列表。
	 * @param startRow 开始行索引号。
	 * @param neptune 客户端是否为海王星主题。
	 * @return 对象数组，0项单元格方阵，1项字段元数据。
	 */
	protected static Object[] prepareHeaders(Sheet sheet, JSONArray rawHeaders, JSONArray processedHeaders, int startRow,
										   boolean neptune) {
		JSONArray leafs = new JSONArray();

		Object[] result = new Object[2];

		int flexWidth = Var.getInt("sys.service.excel.flexColumnMaxWidth");

		Object[] styles = createCellStyle(sheet.getWorkbook(), "header");
		CellStyle style = (CellStyle) styles[0];

		short rowHeight = ((Short) styles[1]).shortValue();
		double rate;

		if (neptune)
			rate = 32.06;
		else
			rate = 36.55;
		leafs.put(0);// 0项为headers最大深度
		markParents(leafs, rawHeaders, null, 0);
		int maxDepth = leafs.getInt(0);
		leafs.remove(0);// 移除深度值
		int j = leafs.length();
		for (int i = 0; i < j; i++) {
			JSONObject node = leafs.getJSONObject(i);
			int width;
			if (node.has("width")) {
				width = node.getInt("width");
			} else {
				if (node.has("flex"))
					width = flexWidth;
				else
					width = 100;
			}
			sheet.setColumnWidth(i, Math.min(65280, (int) (width * rate)));
			node.put("rowspan", maxDepth - node.getInt("y"));
			do {
				node.put("colspan", node.getInt("colspan") + 1);
				if (!node.has("x")) {
					node.put("x", i);
					processedHeaders.put(node);
				}
			} while ((node = (JSONObject) node.opt("parent")) != null);
		}
		maxDepth++;// maxDepth设置为以1为开始索引
		Cell[][] cells = new Cell[j][maxDepth];
		for (int k = 0; k < maxDepth; k++) {
			Row row = sheet.createRow(k + startRow);
			row.setHeight(rowHeight);
			for (int l = 0; l < j; l++) {
				Cell cell = row.createCell(l);
				cell.setCellStyle(style);
				cells[l][k] = cell;
			}
		}
		result[0] = cells;
		result[1] = leafs;
		return result;
	}

	/**
	 * 标识header所有节点的父节点及其节点深度。
	 * @param leafs 子节点列表，系统遍历节点后把所有子节点添加到该列表中。
	 * @param headers 列标头列表。
	 * @param parent 父节点对象。
	 * @param depth 节点深度。
	 */
	protected static void markParents(JSONArray leafs, JSONArray headers, JSONObject parent, int depth) {
		int j = headers.length();

		leafs.put(0, Math.max(leafs.getInt(0), depth));
		for (int i = 0; i < j; i++) {
			JSONObject header = headers.getJSONObject(i);
			header.put("y", depth);
			header.put("colspan", -1);
			header.put("rowspan", -1);
			if (parent != null) {
				header.put("parent", parent);
				parent.put("child", header);
			}
			JSONArray items = (JSONArray) header.opt("items");
			if (items != null)
				markParents(leafs, items, header, depth + 1);
			else
				leafs.put(header);
		}
	}

	/**
	 * 在Excel Sheet中添加正文记录内容。
	 * @param sheet Excel的Sheet对象。
	 * @param records 记录集数据。
	 * @param fields 字段元数据列表。
	 * @param startRow 开始行索引号。
	 */
	protected static void createRecords(Sheet sheet, JSONArray records, JSONArray fields, int startRow,
									  String defaultDateFormat, String defaultTimeFormat) {
		int j = records.length();
		int l = fields.length();

		String[] fieldNames = new String[l];
		Workbook book = sheet.getWorkbook();
		Object[] cellStyles = createCellStyle(book, "text");

		CellStyle baseStyle = (CellStyle) cellStyles[0];
		CellStyle[] colStyles = new CellStyle[l];
		CellStyle[][] dateTimeStyles = new CellStyle[l][2];
		short rowHeight = ((Short) cellStyles[1]).shortValue();
		String boolString = Var.getString("sys.service.excel.boolText");
		String trueText = null;
		String falseText = null;

		int[] dataTypes = new int[l];

		Object[] keyMaps = new Object[l];

		boolean useBoolString = !boolString.isEmpty();
		if (useBoolString) {
			String[] boolStrings = boolString.split(",");
			trueText = boolStrings[0];
			falseText = boolStrings[1];
		}
		for (int k = 0; k < l; k++) {
			JSONObject field = fields.getJSONObject(k);
			fieldNames[k] = field.optString("field");
			CellStyle style = book.createCellStyle();
			style.cloneStyleFrom(baseStyle);
			style.setAlignment(ExcelObject.getAlignment(field.optString("align"), HorizontalAlignment.LEFT));
			if (Boolean.TRUE.equals(field.opt("autoWrap")))
				style.setWrapText(true);
			String keyName = field.optString("keyName");
			String dataTypeStr;
			if (keyName.isEmpty()) {
				keyMaps[k] = null;
				dataTypeStr = field.optString("type").toLowerCase();
			} else {
				keyMaps[k] = KVBuffer.buffer.get(keyName);
				dataTypeStr = "string";
			}
			String format = field.optString("format");
			int dataType;
			if (dataTypeStr.equals("string")) {
				dataType = 1;
			} else if ((dataTypeStr.startsWith("int")) || (dataTypeStr.equals("float"))
					|| (dataTypeStr.equals("number"))) {
				dataType = 2;
				if (!StringUtil.isEmpty(format))
					style.setDataFormat(book.createDataFormat().getFormat(format));
			} else if (dataTypeStr.equals("date")) {
				dataType = 3;
				if (StringUtil.isEmpty(format)) {
					// 未指定格式创建默认的日期，时间和日期时间格式
					CellStyle dateStyle = book.createCellStyle();
					dateStyle.cloneStyleFrom(style);
					CellStyle dateTimeStyle = book.createCellStyle();
					dateTimeStyle.cloneStyleFrom(style);
					format = ExcelObject.toExcelDateFormat(defaultDateFormat, true);
					dateStyle.setDataFormat(book.createDataFormat().getFormat(format));
					dateTimeStyles[k][0] = dateStyle;
					format = ExcelObject.toExcelDateFormat(defaultDateFormat + " " + defaultTimeFormat, true);
					dateTimeStyle.setDataFormat(book.createDataFormat().getFormat(format));
					dateTimeStyles[k][1] = dateTimeStyle;
					style = dateStyle;// 使空单无格默认为日期格式
				} else {
					dateTimeStyles[0][0] = null;
					format = ExcelObject.toExcelDateFormat(format, false);
					if (format == null)
						format = ExcelObject.toExcelDateFormat(defaultDateFormat, true);
					style.setDataFormat(book.createDataFormat().getFormat(format));
				}
			} else {
				if (dataTypeStr.startsWith("bool"))
					dataType = 4;
				else
					dataType = 5;
			}
			dataTypes[k] = dataType;
			colStyles[k] = style;
		}
		for (int i = 0; i < j; i++) {
			Row row = sheet.createRow(startRow + i);
			row.setHeight(rowHeight);
			JSONObject record = (JSONObject) records.opt(i);
			for (int k = 0; k < l; k++) {
				Cell cell = row.createCell(k);
				cell.setCellStyle(colStyles[k]);
				Object value = JsonUtil.opt(record, fieldNames[k]);
				if (value != null) {
					if (keyMaps[k] != null) {
						// 键值转换
						value = KVBuffer.getValue((ConcurrentHashMap<?, ?>) keyMaps[k], value);
					}
					if (dataTypes[k] == 5) {
						// 对自动类型进行归类
						if ((value instanceof Number))
							dataTypes[k] = 2;
						else if ((value instanceof Date))
							dataTypes[k] = 3;
						else if ((value instanceof Boolean))
							dataTypes[k] = 4;
						else
							dataTypes[k] = 1;
					}
					switch (dataTypes[k]) {
						case 2:
							double number;
							if ((value instanceof Number))
								number = ((Number) value).doubleValue();
							else
								number = Double.parseDouble(value.toString());
							cell.setCellValue(number);
							
							// 确保使用数字格式，防止被Excel自动转换为日期
							String stringValue = value.toString();
							if (stringValue.contains("-") || stringValue.matches("\\d{1,2}\\.\\d{1,2}")) {
								// 确保使用明确的数字格式，防止Excel将其解释为日期
								CellStyle numberStyle = book.createCellStyle();
								numberStyle.cloneStyleFrom(cell.getCellStyle());
								DataFormat dataFormat = book.createDataFormat();
								numberStyle.setDataFormat(dataFormat.getFormat("0.00"));
								cell.setCellStyle(numberStyle);
							}
							break;
						case 3:
							Date date;
							if (dateTimeStyles[k][0] == null) {
								// 使用指定的格式
								if ((value instanceof Date))
									date = (Date) value;
								else
									date = Timestamp.valueOf(value.toString());
							} else {
								// 根据值判断使用的默认日期时间格式，并覆盖样式
								boolean hasTime;
								if ((value instanceof Date)) {
									date = (Date) value;
									hasTime = !DateUtil.dateToStr(date).endsWith("00:00:00.0");
								} else {
									String dateTimeStr = value.toString();
									date = Timestamp.valueOf(DateUtil.fixTimestamp(dateTimeStr, false));
									// 字符串需要判断以下3种情景：
									// 无小数，Java以".0"结尾，JS以".000"结尾
									hasTime = (!dateTimeStr.endsWith("00:00:00.0"))
											&& (!(dateTimeStr.endsWith("00:00:00") | dateTimeStr.endsWith("00:00:00.000")));
								}
								if (hasTime)
									cell.setCellStyle(dateTimeStyles[k][1]);
								else
									cell.setCellStyle(dateTimeStyles[k][0]);
							}
							cell.setCellValue(date);
							break;
						case 4:
							if (useBoolString)
								cell.setCellValue(StringUtil.getBool(value.toString()) ? trueText : falseText);
							else
								cell.setCellValue(StringUtil.getBool(value.toString()));
							break;
						default:
							cell.setCellValue(value.toString());
					}
				}
			}
		}
	}

	/**
	 * 根据变量的设置获取单元格样式对象。
	 * @param book 工作簿。
	 * @param type 类型。titile标题，header列标题，text正文。
	 * @return 样式对象数组：0项样式，1项行高度。
	 */
	protected static Object[] createCellStyle(Workbook book, String type) {
		CellStyle style = book.createCellStyle();
		Font font = book.createFont();
		String fontName = Var.getString("sys.service.excel." + type + ".fontName");
		int fontHeight = Var.getInt("sys.service.excel." + type + ".fontHeight");
		double rowHeight = Var.getDouble("sys.service.excel." + type + ".rowHeight");
		Object[] result = new Object[2];

		if (!fontName.isEmpty())
			font.setFontName(fontName);
		if (Var.getInt("sys.service.excel." + type + ".fontWeight") > 600) {
			font.setBold(true);
		} else {
			font.setBold(false);
		}
		font.setFontHeight((short) fontHeight);
		if (rowHeight < 10.0D)
			rowHeight *= fontHeight;// 小于10定义为倍数
		if ((!"text".equals(type)) && (Var.getBool("sys.service.excel." + type + ".wrapText"))) {
			style.setWrapText(true);
		}
		if ("title".equals(type)) {
			String align = Var.getString("sys.service.excel." + type + ".align");
			if (!align.isEmpty()) {
				Object[][] alignments = { { "居中", HorizontalAlignment.CENTER }, { "左", HorizontalAlignment.LEFT },
						{ "右", HorizontalAlignment.RIGHT }, { "居中选择", HorizontalAlignment.CENTER_SELECTION },
						{ "填充", HorizontalAlignment.FILL }, { "常规", HorizontalAlignment.GENERAL },
						{ "两端对齐", HorizontalAlignment.JUSTIFY } };
				if ("居中".equals(align)) {
					style.setAlignment(HorizontalAlignment.CENTER);
				} else if ("左".equals(align)) {
					style.setAlignment(HorizontalAlignment.LEFT);
				} else if ("右".equals(align)) {
					style.setAlignment(HorizontalAlignment.RIGHT);
				} else if ("居中选择".equals(align)) {
					style.setAlignment(HorizontalAlignment.CENTER_SELECTION);
				} else if ("填充".equals(align)) {
					style.setAlignment(HorizontalAlignment.FILL);
				} else if ("常规".equals(align)) {
					style.setAlignment(HorizontalAlignment.GENERAL);
				} else if ("两端对齐".equals(align)) {
					style.setAlignment(HorizontalAlignment.JUSTIFY);
				}
			}
		} else if (Var.getBool("sys.service.excel.border")) {
			style.setBorderTop(BorderStyle.THIN);
			style.setBorderBottom(BorderStyle.THIN);
			style.setBorderLeft(BorderStyle.THIN);
			style.setBorderRight(BorderStyle.THIN);
		}
		if ("header".equals(type)) {
			String backColor = Var.getString("sys.service.excel.header.backColor");
			if (!"默认".equals(backColor)) {
				// HSSFColor兼容XSSF
				if ("金色".equals(backColor)) {
					style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.GOLD.getIndex());
				} else if ("灰色".equals(backColor)) {
					style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.GREY_25_PERCENT.getIndex());
				} else if ("浅黄".equals(backColor)) {
					style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.LIGHT_YELLOW.getIndex());
				}
				style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			}
		}

		style.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);// HSSFCellStyle.VERTICAL_CENTER
		style.setFont(font);
		result[0] = style;
		result[1] = Short.valueOf(Double.valueOf(rowHeight).shortValue());
		return result;
	}

	/**
	 * 向指定输出流中输出HTML格式的数据。
	 * @param outputStream 输出流。输出数据后该流不关闭，如果有必要需要手动关闭。
	 * @param headers 标题列。标题列允许嵌套，并根据设置的嵌套自动合并单元格。
	 * 标题列所有单元格都为字符串格式。
	 * @param records 输出的记录数据。
	 * @param title 标题。标题将显示在首行并合并所在行所有单元格且居中显示。如果为null，
	 * 将不生成标题。
	 * @param dateFormat 当未指定格式时使用的默认日期格式。
	 * @param timeFormat 当未指定格式时使用的默认时间格式。
	 * @param neptune 客户端是否为海王星主题，海王星主题列较宽映射到表格时需要按比缩小。
	 * @param rowNumberWidth 行号列宽度，-1表示无行号列。
	 * @param rowNumberTitle 行号列标题。
	 * @param decimalSeparator 小数点符号。
	 * @param thousandSeparator 千分位符号。
	 */
	public static void outputHtml(OutputStream outputStream, JSONArray headers, JSONArray records, String title,
								  String dateFormat, String timeFormat, boolean neptune, int rowNumberWidth, String rowNumberTitle,
								  String decimalSeparator, String thousandSeparator) throws Exception {
		StringBuilder html = new StringBuilder();

		html.append(
				"<!DOCTYPE html><html><head><meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">");
		if (!StringUtil.isEmpty(title)) {
			html.append("<title>");
			html.append(title);
			html.append("</title>");
		}
		html.append("<style type=\"text/css\">table{table-layout:fixed;border-collapse:collapse;word-wrap:break-word;");
		String value = Var.getString("sys.service.preview.textFont");
		if (!value.isEmpty()) {
			html.append("font-family:");
			html.append(value);
			html.append(';');
		}
		html.append("line-height:");
		html.append(Var.getString("sys.service.preview.textLineHeight"));
		html.append(";font-size:");
		html.append(Var.getString("sys.service.preview.textFontSize"));
		html.append(";}.header{");
		value = Var.getString("sys.service.preview.headerBackColor");
		if (!value.isEmpty()) {
			html.append("background-color:");
			html.append(value);
			html.append(';');
		}
		html.append("font-weight:");
		html.append(Var.getString("sys.service.preview.headerFontWeight"));
		html.append(
				";}td{border:1px solid #000000;padding:0 2px 0 2px;}th{border:0;}.wrap{word-wrap:break-word;}</style></head><body>");
		// <table>在createHtmlHeaders方法内定义因为需要指定宽度
		JSONArray fields = createHtmlHeaders(html, headers, title, neptune, rowNumberWidth, rowNumberTitle);
		getTextContent(html, records, fields, dateFormat, timeFormat, rowNumberWidth > -1, true, decimalSeparator,
				thousandSeparator, null);
		html.append("</table></body></html>");
		outputStream.write(html.toString().getBytes("utf-8"));
	}

	/**
	 * 向指定输出流中输出文本格式的数据。
	 * @param outputStream 输出流。输出数据后该流不关闭，如果有必要需要手动关闭。
	 * @param headers 标题列。标题列允许嵌套，并根据设置的嵌套自动合并单元格。
	 * 标题列所有单元格都为字符串格式。
	 * @param records 输出的记录数据。
	 * @param defaultDateFormat 当未指定格式时使用的默认日期格式。
	 * @param defaultTimeFormat 当未指定格式时使用的默认时间格式。
	 * @param decimalSeparator 小数点符号。
	 * @param thousandSeparator 千分位符号。
	 */
	public static void outputText(OutputStream outputStream, JSONArray headers, JSONArray records,
								  String defaultDateFormat, String defaultTimeFormat, String decimalSeparator, String thousandSeparator)
			throws Exception {
		StringBuilder text = new StringBuilder();
		JSONArray leafs = new JSONArray();
		String lineSeparator = SysUtil.getLineSeparator();

		leafs.put(0);// 0项为headers最大深度
		markParents(leafs, headers, null, 0);
		// 0项为深度去除
		leafs.remove(0);
		int j = leafs.length();
		for (int i = 0; i < j; i++) {
			if (i > 0)
				text.append('\t');
			text.append(leafs.getJSONObject(i).optString("text"));
		}
		text.append(lineSeparator);
		getTextContent(text, records, leafs, defaultDateFormat, defaultTimeFormat, false, false, decimalSeparator,
				thousandSeparator, lineSeparator);
		outputStream.write(text.toString().getBytes("utf-8"));
	}

	/**
	 * 创建HTML表格列标头。
	 * @param html HTML脚本。创建的列标头脚本将输出至该对象。
	 * @param rawHeaders 原始列标题列表。
	 * @param title 标题。
	 * @param neptune 客户端是否为海王星主题。
	 * @param rowNumberWidth 行号列宽度。
	 * @param rowNumberTitle 行号列标题。
	 * @return 自段定义列表。
	 */
	private static JSONArray createHtmlHeaders(StringBuilder html, JSONArray rawHeaders, String title, boolean neptune,
											   int rowNumberWidth, String rowNumberTitle) {
		JSONArray leafs = new JSONArray();
		JSONArray grid = new JSONArray();

		int flexWidth = Var.getInt("sys.service.excel.flexColumnMaxWidth");// 共享变量
		double rate;
		if (neptune)
			rate = 0.87719298;
		else
			rate = 1;
		int tableWidth;
		if (rowNumberWidth > -1) {
			// 存在行号列
			rowNumberWidth = (int) Math.round(rowNumberWidth * rate);
			tableWidth = rowNumberWidth;
		} else {
			tableWidth = 0;
		}
		leafs.put(0);// 0项为headers最大深度
		markParents(leafs, rawHeaders, null, 0);
		int maxDepth = leafs.getInt(0);
		leafs.remove(0);// 移除深度值
		int j = leafs.length();
		for (int i = 0; i < j; i++) {
			JSONObject node = leafs.getJSONObject(i);
			tableWidth += getHtmlCellWidth(node, flexWidth, rate);
			int y = node.getInt("y");
			node.put("rowspan", maxDepth - y);
			do {
				node.put("colspan", node.getInt("colspan") + 1);
				if (!node.has("x")) {
					node.put("x", i);
					y = node.getInt("y");
					JSONArray row = grid.optJSONArray(y);
					if (row == null) {
						row = new JSONArray();
						grid.put(y, row);
					}
					row.put(i, node);
				}
			} while ((node = (JSONObject) node.opt("parent")) != null);
		}
		if (title != null) {
			html.append("<p style=\"text-align:center;width:");
			html.append(tableWidth);
			html.append("px;");
			String value = Var.getString("sys.service.preview.titleFont");
			if (!value.isEmpty()) {
				html.append("font-family:");
				html.append(value);
				html.append(';');
			}
			html.append("font-weight:");
			html.append(Var.getString("sys.service.preview.titleFontWeight"));
			html.append(";line-height:");
			html.append(Var.getString("sys.service.preview.titleLineHeight"));
			html.append(";font-size:");
			html.append(Var.getString("sys.service.preview.titleFontSize"));
			html.append(";\">");
			html.append(StringUtil.toHTML(title, true, true));
			html.append("</p>");
		}
		html.append("<table style=\"width:");
		html.append(tableWidth);
		html.append("px;\">");
		// 添加首行高度为0的行用以指定各列的宽度
		html.append("<tr style=\"height:0\">");
		if (rowNumberWidth > -1) {
			// 行号列
			html.append("<th width=\"");
			html.append(rowNumberWidth);
			html.append("px\"></th>");
		}
		j = leafs.length();
		int i;
		for (i = 0; i < j; i++) {
			JSONObject node = leafs.getJSONObject(i);
			html.append("<th width=\"");
			html.append(getHtmlCellWidth(node, flexWidth, rate));
			html.append("px\"></th>");
		}
		html.append("</tr>");
		j = grid.length();
		for (i = 0; i < j; i++) {
			html.append("<tr class=\"header\">");
			if ((rowNumberWidth > -1) && (i == 0)) {
				// 行号列
				html.append("<td rowspan=\"");
				html.append(maxDepth + 1);
				html.append("\">");
				html.append(rowNumberTitle);// 使用html格式
				html.append("</td>");
			}
			JSONArray row = grid.getJSONArray(i);
			int l = row.length();
			for (int k = 0; k < l; k++) {
				JSONObject node = row.optJSONObject(k);
				if (node != null) {
					html.append("<td align=\"");
					int colspan = node.getInt("colspan");
					String align = node.optString("titleAlign");
					if ((StringUtil.isEmpty(align)) && (colspan > 0))
						align = "center";
					html.append(align);
					html.append('"');
					if (colspan > 0) {
						html.append(" colspan=\"");
						html.append(colspan + 1);
						html.append("\"");
					}
					int rowspan = node.getInt("rowspan");
					if (rowspan > 0) {
						html.append(" rowspan=\"");
						html.append(rowspan + 1);
						html.append("\"");
					}
					html.append('>');
					html.append(node.optString("text"));// 使用html格式
					html.append("</td>");
				}
			}
			html.append("</tr>");
		}
		return leafs;
	}

	/**
	 * 根据指定参数获取HTML单元格宽度。
	 * @param node 单元格JSONObject对象。
	 * @param flexWidth 列指定为flex时的宽度。
	 * @param rate neptune主题下的宽度比例。
	 * @return 计算得到的宽度。
	 */
	private static int getHtmlCellWidth(JSONObject node, int flexWidth, double rate) {
		int width;
		if (node.has("width")) {
			width = node.getInt("width");
		} else {
			if (node.has("flex"))
				width = flexWidth;
			else
				width = 100;
		}
		return (int) Math.round(width * rate);
	}

	/**
	 * 获取正文内容至指定格式。
	 * @param buf 输出缓冲区。
	 * @param records 记录列表。
	 * @param fields 字段列表。
	 * @param defaultDateFormat 默认日期格式。
	 * @param defaultTimeFormat 默认时间格式。
	 * @param hasRowNumber 是否存在行号列。
	 * @param isHtml 输出是否为html格式，true html， false文本。
	 * @param decimalSeparator 小数点符号。
	 * @param thousandSeparator 千分位符号。
	 */
	private static void getTextContent(StringBuilder buf, JSONArray records, JSONArray fields, String defaultDateFormat,
									   String defaultTimeFormat, boolean hasRowNumber, boolean isHtml, String decimalSeparator,
									   String thousandSeparator, String lineSeparator) {
		int j = records.length();
		int l = fields.length();

		String[] fieldNames = new String[l];
		String boolString = Var.getString("sys.service.excel.boolText");
		String trueText = null;
		String falseText = null;
		String[] aligns = new String[l];

		boolean[] wraps = new boolean[l];
		int[] dataTypes = new int[l];

		Format[] formats = new Format[l];
		Format dateFormat = new SimpleDateFormat(ExcelObject.toJavaDateFormat(defaultDateFormat, true));
		Format dateTimeFormat = new SimpleDateFormat(
				ExcelObject.toJavaDateFormat(defaultDateFormat + " " + defaultTimeFormat, true));

		Object[] keyMaps = new Object[l];

		boolean useBoolString = !boolString.isEmpty();
		if (useBoolString) {
			String[] boolStrings = boolString.split(",");
			trueText = boolStrings[0];
			falseText = boolStrings[1];
		}
		for (int k = 0; k < l; k++) {
			JSONObject field = fields.getJSONObject(k);
			String keyName = field.optString("keyName");
			String dataTypeStr;
			if (keyName.isEmpty()) {
				keyMaps[k] = null;
				dataTypeStr = field.optString("type").toLowerCase();
			} else {
				keyMaps[k] = KVBuffer.buffer.get(keyName);
				dataTypeStr = "string";
			}
			String format = field.optString("format");
			formats[k] = null;
			int dataType;
			if (dataTypeStr.equals("string")) {
				dataType = 1;
			} else if ((dataTypeStr.startsWith("int")) || (dataTypeStr.equals("float"))
					|| (dataTypeStr.equals("number"))) {
				dataType = 2;
				if (!format.isEmpty()) {
					DecimalFormat decimalFormat = new DecimalFormat(format);
					decimalFormat.setRoundingMode(RoundingMode.HALF_UP);
					DecimalFormatSymbols dfs = new DecimalFormatSymbols();
					dfs.setDecimalSeparator(decimalSeparator.charAt(0));
					dfs.setGroupingSeparator(thousandSeparator.charAt(0));
					decimalFormat.setDecimalFormatSymbols(dfs);
					formats[k] = decimalFormat;
				}
			} else if (dataTypeStr.equals("date")) {
				dataType = 3;
				if (!StringUtil.isEmpty(format)) {
					format = ExcelObject.toJavaDateFormat(format, false);
					if (format == null)
						formats[k] = dateFormat;
					else
						formats[k] = new SimpleDateFormat(format);
				}
			} else {
				if (dataTypeStr.startsWith("bool"))
					dataType = 4;
				else
					dataType = 5;
			}
			dataTypes[k] = dataType;
			fieldNames[k] = field.optString("field");
			aligns[k] = field.optString("align");
			wraps[k] = Boolean.TRUE.equals(field.opt("autoWrap"));
		}
		for (int i = 0; i < j; i++) {
			JSONObject record = (JSONObject) records.opt(i);
			if (isHtml) {
				buf.append("<tr>");
				if (hasRowNumber) {
					buf.append("<td align=\"right\">");
					buf.append(i + 1);
					buf.append("</td>");
				}
			} else if (i > 0) {
				buf.append(lineSeparator);
			}
			for (int k = 0; k < l; k++) {
				Object value = JsonUtil.opt(record, fieldNames[k]);
				if (value == null) {
					if (isHtml)
						buf.append("<td></td>");
					else
						buf.append("\t");
				} else {
					if (isHtml) {
						buf.append("<td");
						if (!aligns[k].isEmpty()) {
							buf.append(" align=\"");
							buf.append(aligns[k]);
							buf.append("\"");
						}
						if (wraps[k])
							buf.append(" class=\"wrap\"");
						buf.append('>');
					} else if (k > 0) {
						buf.append("\t");
					}
					if (keyMaps[k] != null) {
						// 键值转换
						value = KVBuffer.getValue((ConcurrentHashMap<?, ?>) keyMaps[k], value);
					}
					if (dataTypes[k] == 5) {
						// 对自动类型进行归类
						if ((value instanceof Number))
							dataTypes[k] = 2;
						else if ((value instanceof Date))
							dataTypes[k] = 3;
						else if ((value instanceof Boolean))
							dataTypes[k] = 4;
						else
							dataTypes[k] = 1;
					}
					String valueText;
					switch (dataTypes[k]) {
						case 2:
							if ((value instanceof Number)) {
								if (formats[k] == null) {
									valueText = StringUtil.replaceFirst(value.toString(), ".", decimalSeparator);
								} else {
									double number = ((Number) value).doubleValue();
									valueText = formats[k].format(Double.valueOf(number));
								}
							} else {
								if (formats[k] == null) {
									valueText = StringUtil.replaceFirst(value.toString(), ".", decimalSeparator);
								} else {
									double number = Double.parseDouble(value.toString());
									valueText = formats[k].format(Double.valueOf(number));
								}
							}
							break;
						case 3:
							if (formats[k] == null) {
								// 根据值判断使用的默认日期时间格式，并覆盖样式
								boolean hasTime;
								Date date;
								if ((value instanceof Date)) {
									date = (Date) value;
									hasTime = !DateUtil.dateToStr(date).endsWith("00:00:00.0");
								} else {
									String dateTimeStr = value.toString();
									date = Timestamp.valueOf(dateTimeStr);
									// 字符串需要判断以下3种情景：
									// 无小数，Java以".0"结尾，JS以".000"结尾
									hasTime = (!dateTimeStr.endsWith("00:00:00.0"))
											&& (!(dateTimeStr.endsWith("00:00:00") | dateTimeStr.endsWith("00:00:00.000")));
								}
								if (hasTime)
									valueText = dateTimeFormat.format(date);
								else
									valueText = dateFormat.format(date);
							} else {
								// 使用指定的格式
								Date date;
								if ((value instanceof Date))
									date = (Date) value;
								else
									date = Timestamp.valueOf(value.toString());
								valueText = formats[k].format(date);
							}
							break;
						case 4:
							if (useBoolString)
								valueText = StringUtil.getBool(value.toString()) ? trueText : falseText;
							else
								valueText = value.toString();
							break;
						default:
							valueText = value.toString();
					}

					if (isHtml) {
						buf.append(StringUtil.toHTML(valueText, true, true));
						buf.append("</td>");
					} else {
						buf.append(valueText);
					}
				}
			}
			if (isHtml)
				buf.append("</tr>");
		}
	}

	/**
	 * 根据Excel模板填充数据生成指定的Excel文件，并输出至指定的输出流。
	 * @param params 数据来源的参数对象。
	 * @param excelFile Excel 2007+模板文件。
	 * @param outputStream 用于导出的输出流对象。
	 * @param sheetIndex Sheet页索引号。如果索引号为-1，表示使用所有Sheet。
	 */
	public static void importToExcel(JSONObject params, File excelFile, OutputStream outputStream, int sheetIndex)
			throws Exception {
		if (excelFile.getName().toLowerCase().endsWith(".xls"))
			throw new IllegalArgumentException("Excel file version requires 2007+");
		FileInputStream is = new FileInputStream(excelFile);
		try {
			importToExcel(params, is, outputStream, sheetIndex);
		} finally {
			is.close();
		}
	}

	/**
	 * 根据Excel模板填充数据生成指定的Excel文件，并输出至指定的输出流。
	 * @param params 数据来源的参数对象。
	 * @param inputStream Excel 2007+模板文件输入流。
	 * @param outputStream 用于导出的输出流对象。
	 * @param sheetIndex Sheet页索引号。如果索引号为-1，表示使用所有Sheet。
	 */
	@SuppressWarnings("deprecation")
	public static void importToExcel(JSONObject params, InputStream inputStream, OutputStream outputStream,
									 int sheetIndex) throws Exception {
		Workbook book = null;
		int j, i;
		try {
			book = new XSSFWorkbook(inputStream);
			//如果指定某个Sheet，删除其他所有的Sheet
			j = book.getNumberOfSheets();
			for (i = j - 1; i >= 0; i--) {
				if ((sheetIndex != -1) && (i != sheetIndex)) {
					book.removeSheetAt(i);
				} else {
					Sheet sheet = book.getSheetAt(i);
					importToSheet(sheet, params);
					ExcelObject.executeInstruction(sheet, params);
				}
			}
			book.write(outputStream);
		} finally {
			IOUtils.closeQuietly(book);
		}
	}

	/**
	 * 替换Sheet中使用控件表达式或{#name#}引用的值。值取自JSONObject对象，name为子项名称。
	 * @param sheet 需要导入的Sheet对象。
	 * @param data 引用的数据对象。
	 */
	public static void importToSheet(Sheet sheet, JSONObject data) {
		Iterator<Row> rows;
		Iterator<Cell> cells;
		Row row;
		Cell cell;
		Object value;
		String name, showName, express[];
		char firstChar;
		boolean replaced;

		rows = sheet.rowIterator();
		while (rows.hasNext()) {
			row = rows.next();
			cells = row.cellIterator();
			while (cells.hasNext()) {
				cell = cells.next();
				value = ExcelObject.getCellValue(cell);
				if (value == null)
					continue;
				name = value.toString();
				if (name.indexOf("{#") != -1) {
					replaced = true;
					name = StringUtil.replaceParams(data, name);
					value = name;
				} else
					replaced = false;
				if (name.startsWith("{") && name.endsWith("}")) {
					if (name.indexOf(':') == -1) {
						express = StringUtil.split(name.substring(1, name.length() - 1), ' ');
						name = express[0];
						if (name.length() > 0) {
							firstChar = name.charAt(0);
							if (firstChar == '!' || firstChar == '%' || firstChar == '*')
								name = name.substring(1);
						} else
							name = null;
					} else {
						try {
							name = new JSONObject(name).optString("itemId");
						} catch (Throwable e) {
							name = null;
						}
					}
					if (name == null)
						value = null;
					else {
						//非实例对象引用使用"_"开头
						if (name.startsWith("_") && !data.has(name))
							name = name.substring(1);
						//%开头的name为用于显示的文本
						showName = "%" + name;
						if (data.has(showName))
							value = JsonUtil.opt(data, showName);
						else
							value = JsonUtil.opt(data, name);
					}
				} else if (!replaced)
					continue;
				ExcelObject.setCellValue(cell, value);
			}
		}
	}

	/**
	 * 流式导出Excel数据，适用于大数据量场景，避免内存溢出。
	 * 使用SXSSF API实现，只在内存中保持有限的行数，其余写入临时文件。
	 * 
	 * @param outputStream 输出流。输出数据后该流不关闭，如果有必要需要手动关闭。
	 * @param headers 标题列。标题列允许嵌套，并根据设置的嵌套自动合并单元格。
	 * @param title 标题。标题将显示在首行并合并所在行所有单元格且居中显示。如果为null，将不生成标题。
	 * @param reportInfo 报表信息，包括合并单元格等配置。
	 * @param dateFormat 当未指定格式时使用的默认日期格式。
	 * @param timeFormat 当未指定格式时使用的默认时间格式。
	 * @param neptune 客户端是否为海王星主题，海王星主题列较宽映射到表格时需要按比缩小。
	 * @param rowAccessWindowSize 内存中保留的行数，推荐值100-500，根据每行数据量大小调整。
	 * @throws Exception 当导出过程中发生错误时抛出。
	 */
	public static void outputExcelStreaming(OutputStream outputStream, JSONArray headers, String title,
											JSONObject reportInfo, String dateFormat, String timeFormat, 
											boolean neptune, int rowAccessWindowSize) throws Exception {
		if (rowAccessWindowSize <= 0) {
			rowAccessWindowSize = 100; // 默认值
		}
		
		int startRow = 0;
		JSONArray topHtml = reportInfo.optJSONArray("topHtml");
		JSONArray bottomHtml = reportInfo.optJSONArray("bottomHtml");
		
		// 创建SXSSF工作簿，设置内存中保留的行数
		SXSSFWorkbook workbook = new SXSSFWorkbook(rowAccessWindowSize);
		// 启用自动刷新写入磁盘的临时文件
		workbook.setCompressTempFiles(true);
		
		JSONArray fields;
		int headerRows;
		int headerCols;
		Object[] values;
		SXSSFSheet sheet;
		
		try {
			sheet = workbook.createSheet("数据导出") instanceof SXSSFSheet ? 
					(SXSSFSheet) workbook.createSheet("数据导出") : null;
					
			if (sheet == null) {
				throw new IllegalStateException("无法创建SXSSF Sheet");
			}
			
			if (topHtml != null) {
				createHtml(sheet, topHtml);
				startRow = topHtml.length();
				title = null;
			}
			
			if (title != null) {
				startRow = 1;
			}
			
			values = createHeaders(sheet, headers, startRow, neptune);
			headerCols = (Integer) values[0];
			headerRows = (Integer) values[1];
			fields = (JSONArray) values[2];
			
			if (title != null) {
				createTitle(sheet, title, headerCols);
			}
			
			startRow += headerRows;
			
			if (Var.getBool("sys.service.excel.freezePane")) {
				sheet.createFreezePane(0, startRow);
			}
			
			// 注意：不立即写入记录，而是返回了字段信息和起始行
			// 记录将由调用者通过appendExcelRecords方法分批添加
			
			// 可以合并单元格，这通常不会占用太多内存
			ExcelObject.mergeCells(sheet, reportInfo, startRow, Integer.MAX_VALUE);
			
			if (bottomHtml != null) {
				createHtml(sheet, bottomHtml);
			}
			
			// 不立即写出，等待记录添加完成后再写出
			// workbook.write(outputStream);
		} catch (Exception e) {
			// 出现异常时，确保释放临时文件
			if (workbook != null) {
				try {
					workbook.dispose();
				} catch (Exception ex) {
					// 忽略关闭时的异常
				}
				try {
					workbook.close();
				} catch (Exception ex) {
					// 忽略关闭时的异常
				}
			}
			throw e;
		}
		
		// 不关闭工作簿，由调用者在完成数据添加后关闭
	}
	
	/**
	 * 向流式Excel中追加记录数据。
	 * 
	 * @param workbook 工作簿对象，必须是SXSSFWorkbook类型。
	 * @param records 当前批次的记录数据。
	 * @param fields 字段元数据列表。
	 * @param startRow 开始行索引号。
	 * @param currentRowNum 当前行号，会在方法内更新。
	 * @param dateFormat 当未指定格式时使用的默认日期格式。
	 * @param timeFormat 当未指定格式时使用的默认时间格式。
	 * @return 更新后的当前行号。
	 * @throws Exception 当追加过程中发生错误时抛出。
	 */
	public static int appendExcelRecords(Workbook workbook, JSONArray records, JSONArray fields, 
										int startRow, int currentRowNum, 
										String dateFormat, String timeFormat) throws Exception {
		if (!(workbook instanceof SXSSFWorkbook)) {
			throw new IllegalArgumentException("工作簿必须是SXSSFWorkbook类型");
		}
		
		SXSSFSheet sheet = (SXSSFSheet) workbook.getSheetAt(0);
		int j = records.length();
		int l = fields.length();
		
		String[] fieldNames = new String[l];
		Object[] cellStyles = createCellStyle(workbook, "text");
		
		CellStyle baseStyle = (CellStyle) cellStyles[0];
		CellStyle[] colStyles = new CellStyle[l];
		CellStyle[][] dateTimeStyles = new CellStyle[l][2];
		short rowHeight = ((Short) cellStyles[1]).shortValue();
		String boolString = Var.getString("sys.service.excel.boolText");
		String trueText = null;
		String falseText = null;
		
		int[] dataTypes = new int[l];
		
		Object[] keyMaps = new Object[l];
		
		boolean useBoolString = !boolString.isEmpty();
		if (useBoolString) {
			String[] boolStrings = boolString.split(",");
			trueText = boolStrings[0];
			falseText = boolStrings[1];
		}
		
		for (int k = 0; k < l; k++) {
			JSONObject field = fields.getJSONObject(k);
			fieldNames[k] = field.optString("field");
			CellStyle style = workbook.createCellStyle();
			style.cloneStyleFrom(baseStyle);
			style.setAlignment(ExcelObject.getAlignment(field.optString("align"), HorizontalAlignment.LEFT));
			if (Boolean.TRUE.equals(field.opt("autoWrap")))
				style.setWrapText(true);
			String keyName = field.optString("keyName");
			String dataTypeStr;
			if (keyName.isEmpty()) {
				keyMaps[k] = null;
				dataTypeStr = field.optString("type").toLowerCase();
			} else {
				keyMaps[k] = KVBuffer.buffer.get(keyName);
				dataTypeStr = "string";
			}
			String format = field.optString("format");
			int dataType;
			if (dataTypeStr.equals("string")) {
				dataType = 1;
			} else if ((dataTypeStr.startsWith("int")) || (dataTypeStr.equals("float"))
					|| (dataTypeStr.equals("number"))) {
				dataType = 2;
				if (!StringUtil.isEmpty(format))
					style.setDataFormat(workbook.createDataFormat().getFormat(format));
			} else if (dataTypeStr.equals("date")) {
				dataType = 3;
				if (StringUtil.isEmpty(format)) {
					// 未指定格式创建默认的日期，时间和日期时间格式
					CellStyle dateStyle = workbook.createCellStyle();
					dateStyle.cloneStyleFrom(style);
					CellStyle dateTimeStyle = workbook.createCellStyle();
					dateTimeStyle.cloneStyleFrom(style);
					format = ExcelObject.toExcelDateFormat(dateFormat, true);
					dateStyle.setDataFormat(workbook.createDataFormat().getFormat(format));
					dateTimeStyles[k][0] = dateStyle;
					format = ExcelObject.toExcelDateFormat(dateFormat + " " + timeFormat, true);
					dateTimeStyle.setDataFormat(workbook.createDataFormat().getFormat(format));
					dateTimeStyles[k][1] = dateTimeStyle;
					style = dateStyle;// 使空单无格默认为日期格式
				} else {
					dateTimeStyles[0][0] = null;
					format = ExcelObject.toExcelDateFormat(format, false);
					if (format == null)
						format = ExcelObject.toExcelDateFormat(dateFormat, true);
					style.setDataFormat(workbook.createDataFormat().getFormat(format));
				}
			} else {
				if (dataTypeStr.startsWith("bool"))
					dataType = 4;
				else
					dataType = 5;
			}
			dataTypes[k] = dataType;
			colStyles[k] = style;
		}
		
		for (int i = 0; i < j; i++) {
			Row row = sheet.createRow(startRow + currentRowNum);
			row.setHeight(rowHeight);
			JSONObject record = (JSONObject) records.opt(i);
			for (int k = 0; k < l; k++) {
				Cell cell = row.createCell(k);
				cell.setCellStyle(colStyles[k]);
				Object value = JsonUtil.opt(record, fieldNames[k]);
				if (value != null) {
					if (keyMaps[k] != null) {
						// 键值转换
						value = KVBuffer.getValue((ConcurrentHashMap<?, ?>) keyMaps[k], value);
					}
					if (dataTypes[k] == 5) {
						// 对自动类型进行归类
						if ((value instanceof Number))
							dataTypes[k] = 2;
						else if ((value instanceof Date))
							dataTypes[k] = 3;
						else if ((value instanceof Boolean))
							dataTypes[k] = 4;
						else
							dataTypes[k] = 1;
					}
					switch (dataTypes[k]) {
						case 2:
							double number;
							if ((value instanceof Number))
								number = ((Number) value).doubleValue();
							else
								number = Double.parseDouble(value.toString());
							cell.setCellValue(number);
							
							// 确保使用数字格式，防止被Excel自动转换为日期
							String stringValue = value.toString();
							if (stringValue.contains("-") || stringValue.matches("\\d{1,2}\\.\\d{1,2}")) {
								// 确保使用明确的数字格式，防止Excel将其解释为日期
								CellStyle numberStyle = workbook.createCellStyle();
								numberStyle.cloneStyleFrom(cell.getCellStyle());
								DataFormat dataFormat = workbook.createDataFormat();
								numberStyle.setDataFormat(dataFormat.getFormat("0.00"));
								cell.setCellStyle(numberStyle);
							}
							break;
						case 3:
							Date date;
							if (dateTimeStyles[k][0] == null) {
								// 使用指定的格式
								if ((value instanceof Date))
									date = (Date) value;
								else
									date = Timestamp.valueOf(value.toString());
							} else {
								// 根据值判断使用的默认日期时间格式，并覆盖样式
								boolean hasTime;
								if ((value instanceof Date)) {
									date = (Date) value;
									hasTime = !DateUtil.dateToStr(date).endsWith("00:00:00.0");
								} else {
									String dateTimeStr = value.toString();
									date = Timestamp.valueOf(DateUtil.fixTimestamp(dateTimeStr, false));
									// 字符串需要判断以下3种情景：
									// 无小数，Java以".0"结尾，JS以".000"结尾
									hasTime = (!dateTimeStr.endsWith("00:00:00.0"))
											&& (!(dateTimeStr.endsWith("00:00:00") | dateTimeStr.endsWith("00:00:00.000")));
								}
								if (hasTime)
									cell.setCellStyle(dateTimeStyles[k][1]);
								else
									cell.setCellStyle(dateTimeStyles[k][0]);
							}
							cell.setCellValue(date);
							break;
						case 4:
							if (useBoolString)
								cell.setCellValue(StringUtil.getBool(value.toString()) ? trueText : falseText);
							else
								cell.setCellValue(StringUtil.getBool(value.toString()));
							break;
						default:
							cell.setCellValue(value.toString());
					}
				}
			}
			currentRowNum++;
		}
		
		return currentRowNum;
	}
	
	/**
	 * 完成流式Excel导出，将数据写入输出流并释放资源。
	 * 
	 * @param workbook 工作簿对象，必须是SXSSFWorkbook类型。
	 * @param outputStream 输出流。
	 * @throws Exception 当写入过程中发生错误时抛出。
	 */
	public static void finishExcelStreaming(Workbook workbook, OutputStream outputStream) throws Exception {
		if (!(workbook instanceof SXSSFWorkbook)) {
			throw new IllegalArgumentException("工作簿必须是SXSSFWorkbook类型");
		}
		
		SXSSFWorkbook sxssfWorkbook = (SXSSFWorkbook) workbook;
		try {
			workbook.write(outputStream);
		} finally {
			// 释放临时文件
			sxssfWorkbook.dispose();
			// 关闭工作簿
			workbook.close();
		}
	}
}