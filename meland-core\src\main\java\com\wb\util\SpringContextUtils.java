package com.wb.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 
 * SpringContextUtils是一个工具类，用于获取Spring应用程序上下文的实例以及在应用程序中获取Spring bean的实例
 */
@Component
public class SpringContextUtils implements ApplicationContextAware {

	private static ApplicationContext applicationContext;

	/**
	 * 
	 * 实现ApplicationContextAware接口的方法，将ApplicationContext注入到该类中
	 * 
	 * @param applicationContext Spring应用程序上下文的实例
	 * @throws BeansException 如果注入ApplicationContext失败，则会抛出BeansException异常
	 */
	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		SpringContextUtils.applicationContext = applicationContext;
	}

	/**
	 * 
	 * 获取Spring应用程序上下文的实例
	 * 
	 * @return Spring应用程序上下文的实例
	 */
	public static ApplicationContext getApplicationContext() {
		return applicationContext;
	}

	/**
	 * 
	 * 根据bean的类型从Spring应用程序上下文中获取bean的实例
	 * 
	 * @param clazz bean的类型
	 * @return 返回与指定类型匹配的bean的实例
	 */
	public static <T> T getBean(Class<T> clazz) {
		return applicationContext.getBean(clazz);
	}

	/**
	 * 
	 * 根据bean的名称从Spring应用程序上下文中获取bean的实例
	 * 
	 * @param name bean的名称
	 * @return 返回与指定名称匹配的bean的实例
	 */
	public static Object getBean(String name) {
		return applicationContext.getBean(name);
	}
}
