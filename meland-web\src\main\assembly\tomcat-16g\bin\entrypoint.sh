#!/bin/sh

# ==================== 系统级调优 ====================
# 注意：在容器环境中，只执行可能生效的调优
# 检测是否在容器中运行
if [ -f /.dockerenv ] || grep -q 'docker' /proc/1/cgroup; then
    echo "容器环境优化模式启动..."
    
    # 设置进程限制 (ulimit) - 大多数容器允许
    ulimit -n 65535 2>/dev/null || echo "跳过文件描述符限制设置"
    
    # 获取当前文件描述符限制并输出
    CUR_FD_LIMIT=$(ulimit -n 2>/dev/null || echo "unknown")
    echo "当前文件描述符限制: $CUR_FD_LIMIT (如需更高限制，请通过容器参数设置)"
    
    # 输出当前TCP配置状态（只读取，不修改）
    if [ -r /proc/sys/net/ipv4/tcp_fin_timeout ]; then
        echo "当前TCP连接超时设置: $(cat /proc/sys/net/ipv4/tcp_fin_timeout)秒"
    fi
    if [ -r /proc/sys/net/core/somaxconn ]; then
        echo "当前连接队列长度: $(cat /proc/sys/net/core/somaxconn)"
    fi
    
    echo "SAE环境: 系统层TCP参数需使用SLB和容器配置调优"
fi

echo "===== 跳过系统级调优 (标准容器环境) ====="
# ==================== 系统调优结束 ====================

# 兼容cgroups v1和v2进行容器内存检测
if [ -f /sys/fs/cgroup/memory/memory.limit_in_bytes ]; then
    # cgroups v1 检测逻辑
    CONTAINER_MEM_LIMIT=$(cat /sys/fs/cgroup/memory/memory.limit_in_bytes)
    if [ "${CONTAINER_MEM_LIMIT}" = "9223372036854771712" ] || [ ${CONTAINER_MEM_LIMIT} -gt 1152921504606846976 ]; then
        # 处理无限制或超大值情况（1152921504606846976 = 2^60）
        AVAILABLE_MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
        AVAILABLE_MEMORY_MB=$(( AVAILABLE_MEMORY_KB / 1024 ))
        echo "检测到cgroups v1环境，容器无内存限制，使用系统内存: ${AVAILABLE_MEMORY_MB}MB"
    else
        # 正常容器限制情况
        AVAILABLE_MEMORY_MB=$(( CONTAINER_MEM_LIMIT / 1024 / 1024 ))
        echo "检测到cgroups v1环境，容器内存限制: ${AVAILABLE_MEMORY_MB}MB"
    fi
elif [ -f /sys/fs/cgroup/memory.max ]; then
    # cgroups v2 检测逻辑
    CONTAINER_MEM_LIMIT=$(cat /sys/fs/cgroup/memory.max)
    if [ "${CONTAINER_MEM_LIMIT}" = "max" ] || [ "${CONTAINER_MEM_LIMIT}" = "9223372036854771712" ]; then
        # 处理无限制情况
        AVAILABLE_MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
        AVAILABLE_MEMORY_MB=$(( AVAILABLE_MEMORY_KB / 1024 ))
        echo "检测到cgroups v2环境，容器无内存限制，使用系统内存: ${AVAILABLE_MEMORY_MB}MB"
    else
        # 正常容器限制情况
        AVAILABLE_MEMORY_MB=$(( CONTAINER_MEM_LIMIT / 1024 / 1024 ))
        echo "检测到cgroups v2环境，容器内存限制: ${AVAILABLE_MEMORY_MB}MB"
    fi
else
    # 回退到/proc/meminfo
    AVAILABLE_MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    AVAILABLE_MEMORY_MB=$(( AVAILABLE_MEMORY_KB / 1024 ))
    echo "未检测到容器环境，使用系统内存: ${AVAILABLE_MEMORY_MB}MB"
fi

# 容器CPU配额检测 - 确保在容器环境中获取正确的CPU限制
if [ -f /sys/fs/cgroup/cpu/cpu.shares ] || [ -f /sys/fs/cgroup/cpu.max ]; then
    # 尝试获取CPU配额限制
    if [ -f /sys/fs/cgroup/cpu/cpu.cfs_quota_us ] && [ -f /sys/fs/cgroup/cpu/cpu.cfs_period_us ]; then
        # cgroups v1
        CPU_QUOTA=$(cat /sys/fs/cgroup/cpu/cpu.cfs_quota_us)
        CPU_PERIOD=$(cat /sys/fs/cgroup/cpu/cpu.cfs_period_us)
        if [ "${CPU_QUOTA}" != "-1" ]; then
            CONTAINER_CPU_COUNT=$(( (CPU_QUOTA + CPU_PERIOD - 1) / CPU_PERIOD ))
            echo "检测到cgroups v1 CPU限制: ${CONTAINER_CPU_COUNT}个核心"
            PROCESSOR_COUNT=${CONTAINER_CPU_COUNT}
        fi
    elif [ -f /sys/fs/cgroup/cpu.max ]; then
        # cgroups v2
        CPU_MAX=$(cat /sys/fs/cgroup/cpu.max)
        CPU_QUOTA=$(echo "${CPU_MAX}" | cut -d' ' -f1)
        CPU_PERIOD=$(echo "${CPU_MAX}" | cut -d' ' -f2)
        if [ "${CPU_QUOTA}" != "max" ]; then
            CONTAINER_CPU_COUNT=$(( (CPU_QUOTA + CPU_PERIOD - 1) / CPU_PERIOD ))
            echo "检测到cgroups v2 CPU限制: ${CONTAINER_CPU_COUNT}个核心"
            PROCESSOR_COUNT=${CONTAINER_CPU_COUNT}
        fi
    fi
fi

# 仍需检测实际处理器核心数，作为后备方案
if [ -z "${PROCESSOR_COUNT}" ]; then
    PROCESSOR_COUNT=$(grep -c ^processor /proc/cpuinfo)
    echo "未检测到CPU限制，使用实际处理器核心数: ${PROCESSOR_COUNT}"
fi

# 系统资源自动感知（适配SAE弹性资源）
# 16GB专用环境堆内存优化：从65%提高到72%，提供约11.5GB堆内存
# 对于Nashorn脚本引擎和大型Web应用提供更充足的堆空间，同时保留足够系统和非堆内存
DEFAULT_HEAP_SIZE=$((AVAILABLE_MEMORY_MB * 72 / 100))
echo "设置默认堆内存大小: ${DEFAULT_HEAP_SIZE}MB，处理器核心数: ${PROCESSOR_COUNT}"

# JVM 堆大小设置
export CATALINA_OPTS="$CATALINA_OPTS -Xms${JVM_XMS:-${DEFAULT_HEAP_SIZE}m}"
export CATALINA_OPTS="$CATALINA_OPTS -Xmx${JVM_XMX:-${DEFAULT_HEAP_SIZE}m}"

# 线程栈大小调整 - 增大以解决栈溢出
export CATALINA_OPTS="$CATALINA_OPTS -Xss1m"

# 元空间优化
export CATALINA_OPTS="$CATALINA_OPTS -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=768m"
export CATALINA_OPTS="$CATALINA_OPTS -XX:CompressedClassSpaceSize=256m"

# JIT优化
export CATALINA_OPTS="$CATALINA_OPTS -XX:InitialCodeCacheSize=256m -XX:ReservedCodeCacheSize=512m"
export CATALINA_OPTS="$CATALINA_OPTS -XX:CompileThreshold=5000 -XX:+TieredCompilation"

# Nashorn专用优化
export CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.compiler.deoptimizeThreshold=-1"
export CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.codegen.maxmethod.size=65535"
export CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.compiler.recursion.limit=1000"
export CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.classloader.loadersource=parent"
export CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.args.prepend=--persistent-code-cache"
export CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.typeInfo.cacheSize=1024"

# 关闭偏向锁：减少多线程环境下的锁撤销开销
export CATALINA_OPTS="$CATALINA_OPTS -XX:-UseBiasedLocking"

# JVM 垃圾回收器设置 - 使用G1
if [ -z "$JVM_DISABLE_G1GC" ]; then
    # 首先解锁实验性参数（必须在使用G1MixedGCLiveThresholdPercent之前）
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+UnlockExperimentalVMOptions"
    
    # 基础配置
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseG1GC"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+ClassUnloadingWithConcurrentMark"
    
    # 停顿时间目标 - 默认200ms，可通过环境变量配置
    export CATALINA_OPTS="$CATALINA_OPTS -XX:MaxGCPauseMillis=${JVM_MAX_GC_PAUSE_MS:-200}"
    
    # 设置区域大小 - 显式设置为4MB以避免超大对象分配失败
    export CATALINA_OPTS="$CATALINA_OPTS -XX:G1HeapRegionSize=${JVM_G1_REGION_SIZE:-4}m"
    
    # 并行工作线程数设置 - 根据CPU核心调整
    export CATALINA_OPTS="$CATALINA_OPTS -XX:ParallelGCThreads=${JVM_PARALLEL_GC_THREADS:-$((PROCESSOR_COUNT / 2 > 1 ? PROCESSOR_COUNT / 2 : 1))}"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:ConcGCThreads=${JVM_CONC_GC_THREADS:-$((PROCESSOR_COUNT / 3 > 1 ? PROCESSOR_COUNT / 3 : 1))}"
    
    # 启动堆占用率触发并发GC周期
    export CATALINA_OPTS="$CATALINA_OPTS -XX:InitiatingHeapOccupancyPercent=${JVM_G1_INIT_OCCUPANCY:-45}"
    
    # 混合垃圾收集设置
    export CATALINA_OPTS="$CATALINA_OPTS -XX:G1MixedGCLiveThresholdPercent=${JVM_G1_MIXED_GC_LIVE_THRESHOLD:-75}"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:G1MixedGCCountTarget=${JVM_G1_MIXED_GC_COUNT:-8}"
    
    # 避免单个垃圾收集器的过度收集 - 提高至15%加速老年代回收
    export CATALINA_OPTS="$CATALINA_OPTS -XX:G1OldCSetRegionThresholdPercent=${JVM_G1_OLD_CSET_THRESHOLD:-15}"
    
    # G1 RSet优化 - 16G堆内存推荐配置
    export CATALINA_OPTS="$CATALINA_OPTS -XX:G1RSetUpdatingPauseTimePercent=${JVM_G1_RSET_UPDATING_PAUSE_TIME:-10}"
    
    # 大对象阈值设置 - 默认区域大小的50%，防止大对象触发Full GC
    HALF_REGION_SIZE=$(( ${JVM_G1_REGION_SIZE:-4} / 2 ))
    export CATALINA_OPTS="$CATALINA_OPTS -XX:G1HeapWastePercent=${JVM_G1_HEAP_WASTE_PERCENT:-5}"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:G1ReservePercent=${JVM_G1_RESERVE_PERCENT:-10}"
    
    # 参考线程设置
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+ParallelRefProcEnabled"
fi

# 启用字符串去重 - 对脚本生成的重复字符串有显著帮助
if [ -z "$JVM_DISABLE_STRING_DEDUP" ]; then
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseStringDeduplication"
fi

# 禁止显式GC - 默认启用
if [ -z "$JVM_ENABLE_EXPLICIT_GC" ]; then
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+DisableExplicitGC"
fi

# 内存预分配 - 默认启用
if [ -z "$JVM_DISABLE_PRETOUCH" ]; then
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+AlwaysPreTouch"
fi

# 堆转储设置 - 默认启用
if [ -z "$JVM_DISABLE_HEAP_DUMP" ]; then
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:HeapDumpPath=${JVM_HEAP_DUMP_PATH:-/usr/local/tomcat/logs/heapdump.hprof}"
fi

# GC日志配置 - 默认启用基本记录，详细日志需额外开启
export CATALINA_OPTS="$CATALINA_OPTS -XX:+PrintGCDetails"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+PrintGCDateStamps"
export CATALINA_OPTS="$CATALINA_OPTS -Xloggc:${GC_LOG_PATH:-/usr/local/tomcat/logs/gc.log}"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseGCLogFileRotation"
export CATALINA_OPTS="$CATALINA_OPTS -XX:NumberOfGCLogFiles=${JVM_GC_LOG_FILES:-10}"
export CATALINA_OPTS="$CATALINA_OPTS -XX:GCLogFileSize=${JVM_GC_LOG_SIZE:-100M}"

# 更多诊断详情（可选）
if [ ! -z "$JVM_GC_DETAILS" ]; then
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+PrintGCApplicationStoppedTime"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+PrintHeapAtGC"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+PrintTenuringDistribution"
fi

# JVM 系统属性
export CATALINA_OPTS="$CATALINA_OPTS -Dfile.encoding=${FILE_ENCODING:-UTF-8}"
export CATALINA_OPTS="$CATALINA_OPTS -Duser.timezone=${USER_TIMEZONE:-Asia/Shanghai}"

# 线程控制参数 - 防止线程爆炸
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UnlockDiagnosticVMOptions"
export CATALINA_OPTS="$CATALINA_OPTS -XX:GuaranteedSafepointInterval=60000"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseCountedLoopSafepoints"

# 保留JVM安全参数
export CATALINA_OPTS="$CATALINA_OPTS -Djdk.tls.ephemeralDHKeySize=2048"
export CATALINA_OPTS="$CATALINA_OPTS -Djava.security.egd=file:/dev/./urandom"

# 添加ARMS监控兼容性优化
export CATALINA_OPTS="$CATALINA_OPTS -Darms.logger.level=INFO"

# 启动Tomcat
exec catalina.sh run