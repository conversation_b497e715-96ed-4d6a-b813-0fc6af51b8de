package com.wb.common;

import java.sql.ResultSet;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.wb.util.DbUtil;
import com.wb.util.LogUtil;
import com.wb.util.WebUtil;


/**
 * 创建主键
 *
 */
public class KeyCreator {

	/**
	 * 创建新的树型记录ID
	 */
	public static synchronized void createTreeKey(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		String _parentID = WebUtil.fetch(request, "parent_id"); //上级编号
		String _tableName = WebUtil.fetch(request, "table_name"); //表名
		String _pkField = WebUtil.fetch(request, "pk_field"); //主键
		String _levelCol = WebUtil.fetch(request, "level_col"); //级别字段
		_parentID = _parentID.equals("-1") ? "" : _parentID;
		String _treeID = "1001";
		int _level = _parentID.length() / 4 + 1;

		StringBuilder _selsql = new StringBuilder();
		StringBuilder _wheresql = new StringBuilder();
		_selsql.append(" select max(" + _pkField + ")");
		_wheresql.append(" from " + _tableName);
		if (_parentID.length() == 0) {
			_wheresql.append(" where " + _pkField + " like '%' ");
			_wheresql.append(" and " + _levelCol + " = " + _level);
		} else {
			_wheresql.append(" where " + _pkField + " like '" + _parentID + "%' ");
			_wheresql.append(" and " + _levelCol + " = " + _level);
		}
		_selsql.append(_wheresql);
		ResultSet _rs = (ResultSet) DbUtil.run(request, _selsql.toString());
		if (_rs.next()) {
			String _maxVal = _rs.getString(1) == null ? "" : _rs.getString(1);
			if (_maxVal.length() == 0) {
				if (_parentID.length() != 0) {
					_treeID = _parentID + "0001";
				}
			} else {
				if (_maxVal.equals("9999")) {
					LogUtil.warn(request, "树形ID超出9999范围：" + _tableName);
					_treeID = "9999";
				} else {
					if (_maxVal.length() > 4) {
						_maxVal = _maxVal.substring(_maxVal.length() - 4, _maxVal.length());
					}
					_maxVal = "0000" + (Long.parseLong(_maxVal) + 1);
					_treeID = _parentID + _maxVal.substring(_maxVal.length() - 4, _maxVal.length());
				}
			}
		}
		request.setAttribute("tree.id", _treeID);
		request.setAttribute("tree.level", _level);
	}
}
