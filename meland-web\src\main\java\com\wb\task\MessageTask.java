package com.wb.task;

import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.wb.common.Base;
import com.wb.common.Var;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;

/**
 * 消息处理任务
 * <AUTHOR>
 *
 */
@DisallowConcurrentExecution
public class MessageTask implements Job {

	public static final String KEY = "task.message.running";

	public void execute(JobExecutionContext context) throws JobExecutionException {
		if (StringUtil.getBool(StringUtil.toString(Base.map.get(KEY)))) //如果上次任务仍未完成，忽略本次任务。
			return;
		LogUtil.info("开启消息处理任务。");
		Base.map.put(KEY, true);
		try {
			while (true) {
				if (!StringUtil.getBool(StringUtil.toString(Base.map.get(KEY))))
					break;
				com.wb.message.MessageConsume.consume();
			}
		} catch (Throwable e) {
			LogUtil.error(StringUtil.concat("执行消息处理异常：：", SysUtil.getRootError(e)));
			if (Var.printError)
				throw new JobExecutionException(e);
		} finally {
			Base.map.put(KEY, false);
		}
	}

}
