package com.wb.common;

import com.wb.controls.Control;
import com.wb.controls.ExtControl;
import com.wb.controls.ScriptControl;
import com.wb.controls.ServerScript;
import com.wb.exception.AccessDeniedException;
import com.wb.interact.Controls;
import com.wb.util.*;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.MessageDigest;
import java.sql.Blob;
import java.sql.CallableStatement;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Deque;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 模块解析器，解析并执行模块文件。解析完成后，系统把生成的客户端脚本发送到客户端。
 * 
 * 注意：此类不是线程安全的，每个请求应创建新的Parser实例，不应在多线程间共享同一实例。
 */
public class Parser {
    /**
     * 最大递归深度限制，防止栈溢出
     */
    private static final int MAX_RECURSION_DEPTH = 30;

    /**
     * 用于并发任务的线程池，根据CPU核心数确定线程数 队列容量控制在较小范围内，防止任务堆积导致OOM
     */
    private static final ExecutorService EXECUTOR = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors() * 2, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100), // 设置较小的队列容量，防止任务堆积导致OOM
            new ThreadFactory() {
                private final AtomicInteger counter = new AtomicInteger(1);

                @Override
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r, "ParserPool-" + counter.getAndIncrement());
                    t.setDaemon(true);
                    return t;
                }
            }, new ThreadPoolExecutor.CallerRunsPolicy() // 队列满时，由调用线程执行任务，起到反压作用
    );

    /**
     * 控件类缓存，避免重复反射查找导致的性能开销
     */
    private static final ConcurrentHashMap<String, Class<?>> CONTROL_CLASS_CACHE = new ConcurrentHashMap<>();

    /**
     * 模块运行模式枚举
     */
    public enum RunMode {
        /** 普通运行模式，完成全部流程，如果有前端脚本不返回入口 */
        NORMAL,
        /** 模块引用运行模式，不验证权限不关闭资源，如果有前端脚本不返回入口 */
        MODULE,
        /** 控件引用运行模式，不验证权限不关闭资源，如果有前端脚本返回app主入口 */
        CONTROL,
        /** 外部调用模式，完成全部流程，如果有前端脚本返回整个app对象 */
        INVOKE,
        /** 内部调用模式 */
        INNER,
        /** 内部调用模式 */
        INNER_INVOKE
    }

    /**
     * 用于控制线程池优雅关闭的钩子
     */
    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            EXECUTOR.shutdown();
            try {
                if (!EXECUTOR.awaitTermination(10, TimeUnit.SECONDS)) {
                    EXECUTOR.shutdownNow();
                }
            } catch (InterruptedException e) {
                EXECUTOR.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }));
    }

    /**
     * html头文本，预分配合理容量减少扩容操作
     */
    private final StringBuilder headerHtml = new StringBuilder(16384);
    /**
     * html脚文本
     */
    private final List<String> footerHtml = new ArrayList<String>(20);
    /**
     * html脚文本指针
     */
    private int htmlPointer;
    /**
     * js头文本，预分配合理容量减少扩容操作
     */
    private final StringBuilder headerScript = new StringBuilder(32768);
    /**
     * js脚文本
     */
    private final List<String> footerScript = new ArrayList<String>(20);
    /**
     * js脚文本指针
     */
    private int scriptPointer;
    /**
     * 是否是普通运行模式
     */
    private final HttpServletRequest request;
    /**
     * HttpServletResponse响应对象
     */
    private final HttpServletResponse response;
    /**
     * 不加载任何东西
     */
    private boolean notLoadNone;
    /**
     * 是否压缩脚本
     */
    private boolean isMinify = false;

    // 将旧常量转为对应枚举值的引用
    public static final int RUN_NORMAL = RunMode.NORMAL.ordinal();
    public static final int RUN_MODULE = RunMode.MODULE.ordinal();
    public static final int RUN_CONTROL = RunMode.CONTROL.ordinal();
    public static final int RUN_INVOKE = RunMode.INVOKE.ordinal();
    public static final int RUN_INNER = RunMode.INNER.ordinal();
    public static final int RUN_INNER_INVOKE = RunMode.INNER_INVOKE.ordinal();

    /**
     * 存储smart
     */
    private HttpSession sessionSmart;

    /**
     * XWL文件解析器构造函数。
     *
     * @param request  HttpServletRequest 请求对象。
     * @param response HttpServletRequest 响应对象。
     */
    public Parser(HttpServletRequest request, HttpServletResponse response) {
        this.request = request;
        this.response = response;
    }

    /**
     * 解析xwl模块。依次遍历模块内所有节点，执行控件的create方法。 执行完成后系统自动关闭和释放资源。
     *
     * @param moduleFile xwl文件相对路径。
     */
    public void parse(String moduleFile) throws ServletException, IOException {
        parse(moduleFile, false, this.request.getParameter("xwlt") != null);
    }

    /**
     * 异步解析xwl模块。适用于可以异步执行的场景，不阻塞当前线程。 注意：本方法会创建新的Parser实例处理异步任务，确保线程安全。
     *
     * @param moduleFile xwl文件相对路径。
     * @return 表示异步操作的CompletableFuture
     */
    public CompletableFuture<Void> parseAsync(String moduleFile) {
        // 捕获当前请求和响应对象，传给新的Parser实例
        final HttpServletRequest requestCopy = this.request;
        final HttpServletResponse responseCopy = this.response;

        // 提前获取所需的请求参数，避免在异步任务中直接使用请求对象
        final String xwltParam = requestCopy.getParameter("xwlt");
        final String requestURI = requestCopy.getRequestURI();
        final String clientIP = requestCopy.getRemoteAddr();

        return CompletableFuture.runAsync(() -> {
            try {
                // 创建包含必要上下文信息的请求包装器
                HttpServletRequest safeRequest = new RequestWrapper(requestCopy);
                HttpServletResponse safeResponse = new ResponseWrapper(responseCopy);

                // 创建新的Parser实例处理异步任务，避免共享实例变量
                Parser parser = new Parser(safeRequest, safeResponse);

                // 使用安全的请求参数值，而不是直接访问原请求对象
                parser.parse(moduleFile, false, xwltParam != null);
            } catch (Exception e) {
                // 清晰记录异常并保留原始堆栈
                LogUtil.error(null, "异步解析模块失败: " + moduleFile + ", URI: " + requestURI + ", IP: " + clientIP, e);
                if (e instanceof RuntimeException) {
                    throw (RuntimeException) e;
                } else {
                    throw new CompletionException("解析模块失败: " + moduleFile, e);
                }
            }
        }, EXECUTOR);
    }

    /**
     * 解析xwl模块。依次遍历模块内所有节点，执行控件的create方法。
     *
     * @param moduleFile xwl文件相对路径。
     * @param innerMode  是否内部调用模式
     * @param isInvoke   是否执行
     */
    public void parse(String moduleFile, boolean innerMode, boolean isInvoke) throws ServletException, IOException {
        boolean hasExcept = false;
        List<FileItem> fileItemList = null;
        ConcurrentHashMap<String, Object> varMap = null;

        // 校验模块文件路径
        if (moduleFile == null || moduleFile.isEmpty()) {
            throw new IllegalArgumentException("模块文件路径不能为空");
        }

        // 防止路径遍历攻击，限制路径格式并禁止目录遍历
        if (moduleFile.contains("..") || moduleFile.contains("\\") || !moduleFile.endsWith(".xwl")) {
            throw new IllegalArgumentException("无效的模块文件路径: " + moduleFile);
        }

        Object object = this.request.getAttribute("sysx.varMap");
        if (object == null) {
            varMap = new ConcurrentHashMap<String, Object>();
            this.request.setAttribute("sysx.varMap", varMap);
        } else {
            varMap = JSONObject.toConHashMap(object);
        }
        try {
            if (SysUtil.isNotCustomRequest(request) && ServletFileUpload.isMultipartContent(request))
                fileItemList = WebUtil.setUploadFile(request);

            // 转换为枚举值
            RunMode runMode;
            if (innerMode)
                runMode = isInvoke ? RunMode.INNER_INVOKE : RunMode.INNER;
            else
                runMode = isInvoke ? RunMode.INVOKE : RunMode.NORMAL;

            execute(moduleFile, runMode, null, null);
        } catch (Throwable e) {
            hasExcept = true;
            if (innerMode) {
                throw new ServletException(e);
            }
            WebUtil.showException(e, request, response);
        } finally {
            closeObjects(varMap, hasExcept);
            if (fileItemList != null)
                WebUtil.clearUploadFile(request, fileItemList);
        }
    }

    /**
     * 解析和执行xwl文件。依次遍历模块内所有节点，执行对应控件的create方法。
     *
     * @param moduleFile xwl文件相对路径。
     * @param runMode    运行模式。 RUN_NORMAL：普通运行模式，完成全部流程，如果有前端脚本不返回入口。
     *                   RUN_MODULE：不验证权限不关闭资源，如果有前端脚本不返回入口。
     *                   RUN_CONTROL：不验证权限不关闭资源，如果有前端脚本返回app主入口。
     *                   RUN_INVOKE：完成全部流程，如果有前端脚本返回整个app对象。
     * @param xwlId      模块itemId名称，用于在命名空间中子空间名称指定。
     * @param params     传入的参数。
     * @throws Exception 如果解析过程中发生异常将抛出。
     */
    public void execute(String moduleFile, RunMode runMode, String xwlId, String params) throws Exception {
        JSONObject fullModule = XwlBuffer.get(moduleFile);
        JSONArray children = (JSONArray) fullModule.opt("children");

        if (children == null || children.length() == 0) {
            throw new IllegalStateException("无效的模块结构，模块不包含子元素: " + moduleFile);
        }

        JSONObject module = (JSONObject) children.opt(0);
        JSONObject configs = (JSONObject) module.opt("configs");
        this.isMinify = getBool(configs, "minifyOutput", false);
        // 对referer进行检测
        if (getBool(configs, "checkReferer", false)) {
            String referer = request.getHeader("Referer");
            if (referer == null || !Base.allowedDomainPattern.matcher(referer).matches()) {
                throw new AccessDeniedException("无效访问，请提供访问凭证。");
            }
        }

        // 使用RunMode枚举进行判断，提高代码可读性
        RunMode mode = runMode;
        boolean runNormal = mode == RunMode.NORMAL;
        boolean runInvoke = mode == RunMode.INVOKE;

        if (runNormal || runInvoke) {
            String tokens = getString(configs, "tokens");
            if ((tokens.isEmpty()) || (!checkToken(tokens))) {
                String method = getString(configs, "method");

                if ((!method.isEmpty()) && (!method.equalsIgnoreCase(this.request.getMethod()))) {
                    throw new IllegalArgumentException("Method not allowed");
                }

                if (Boolean.TRUE.equals(fullModule.opt("internalCall"))) {
                    throw new IllegalArgumentException(Str.format(this.request, "internalCall",
                            StringUtil.select(Str.getText(this.request, fullModule.optString("title")),
                                    FileUtil.getFilename(moduleFile)),
                            moduleFile));
                }

                if (Boolean.TRUE.equals(fullModule.opt("loginRequired"))) {
                    if (!WebUtil.checkLogin(this.request, this.response)) {
                        return;
                    }
                    if (!WbUtil.canAccess(fullModule, Session.getRoles(this.request))) {
                        if ("d".equals(Var.getString("sys.app.versionType")))
                            throw new AccessDeniedException("您没有权限访问该功能，请联系 <EMAIL> 获取完整版。");
                        throw new AccessDeniedException(Str.format(this.request, "forbidden",
                                StringUtil.select(Str.getText(this.request, fullModule.optString("title")),
                                        FileUtil.getFilename(moduleFile)),
                                moduleFile));
                    }
                }
            }
        }
        JSONObject events = (JSONObject) module.opt("events");
        JSONObject emptyJson = new JSONObject();
        JSONObject moduleGeneral = (JSONObject) Controls.get("module").opt("general");
        String theme = null;
        String touchTheme = null;
        boolean[] libTypes = null;
        boolean hasChildren = module.has("children");
        boolean hasEvents = events != null;
        HttpSession session = null;

        if (mode == RunMode.INNER) {
            runNormal = true;
        } else if (mode == RunMode.INNER_INVOKE) {
            runInvoke = true;
        }
        String content = getString(configs, "logMessage");
        if (!content.isEmpty()) {
            LogUtil.info(this.request, content);
        }
        // initScript在importModule之前运行，serverScript在importModule之后运行
        content = ServerScript.getScript(configs, "initScript");
        if (!content.isEmpty())
            ScriptBuffer.run(StringUtil.concat((String) configs.opt("id"), ".is"), content, this.request, this.response,
                    moduleFile);
        content = getString(configs, "serverMethod");
        if (!content.isEmpty())
            SysUtil.executeMethod(content, this.request, this.response);
        boolean createFrame = getBool(configs, "createFrame", true);
        if ((createFrame) && (runNormal)) {
            this.headerHtml.append(
                    "<!DOCTYPE html>\n<html>\n<head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\"/>\n<meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\">\n<title>");
            String title = getString(configs, "title");
            if (title.isEmpty())
                title = Str.getText(this.request, fullModule.optString("title")); // 默认页面标题为模块的标题
            else if ("-".equals(title))
                title = null; // "-"表示标题为空
            if (!StringUtil.isEmpty(title))
                this.headerHtml.append(title);
            this.headerHtml.append("</title>");
            appendScript(this.headerHtml, getString(configs, "head"));
            session = this.request.getSession(false);
            theme = session == null ? null : (String) session.getAttribute("sys.theme");
            if (theme == null)
                theme = Var.getString("sys.app.theme");
            touchTheme = session == null ? null : (String) session.getAttribute("sys.touchTheme");
            if (touchTheme == null)
                touchTheme = Var.getString("sys.app.touchTheme");
            libTypes = setLinks(configs, theme, touchTheme);
            String tagConfigs = getString(configs, "tagConfigs");
            appendScript(this.headerHtml, getString(configs, "headLast"));
            if (tagConfigs.isEmpty()) {
                this.headerHtml.append("\n</head>\n<body>");
            } else {
                this.headerHtml.append("\n</head>\n<body ");
                this.headerHtml.append(tagConfigs);
                this.headerHtml.append('>');
            }
            this.headerScript.append("<script language=\"javascript\" type=\"text/javascript\">");
        }

        if ((createFrame) && (runInvoke))
            addLinksScript(configs);
        appendScript(this.headerHtml, getString(configs, "initHtml"));
        if (createFrame) {
            if (this.headerScript.length() > 0)
                this.headerScript.append('\n');
            if ((runNormal) && (libTypes[1])) {
                // 加载了Ext
                this.headerScript.append("Ext.onReady(function(contextOptions,contextOwner){");
            } else if ((runNormal) && (libTypes[2])) {
                // 加载了touch
                this.headerScript.append("Ext.setup({");
                if (hasChildren)
                    this.headerScript
                            .append(getTouchViewport((JSONArray) module.opt("children"), moduleGeneral, runNormal));
                this.headerScript.append("onReady:function(contextOptions,contextOwner){");
            } else {
                this.headerScript.append("(function(contextOptions,contextOwner){");
            }
            String namespace = (String) configs.opt("itemId");
            // 如果模块itemId未改名则创建内部命名空间。
            if ("module".equals(namespace)) {
                this.headerScript.append("\nvar app={isXNS:\"");
                this.headerScript.append(SysUtil.getId());
                this.headerScript.append("\"};");
            } else {
                this.headerScript.append("\nWb.ns(\"");
                this.headerScript.append(namespace);
                this.headerScript.append("\");\nvar app=");
                this.headerScript.append(namespace);
                this.headerScript.append(";\napp.isXNS=\"");
                this.headerScript.append(SysUtil.getId());
                this.headerScript.append("\";");
            }
            if ((runNormal) && (libTypes[2])) {
                this.headerScript.append("\nthis.appScope=app;\napp[this.itemId]=this;");
            }
            this.headerScript.append("\napp.contextOwner=contextOwner;");
            if (runNormal) {
                // 设置常用变量
                this.headerScript.append("\nwindow.app=app;\nWb.init({zo:");
                if (Var.useLocalTime) {
                    Calendar cal = Calendar.getInstance();
                    this.headerScript.append((cal.get(15) + cal.get(16)) / 60000);
                } else {
                    this.headerScript.append("-1");
                }
                this.headerScript.append(",lang:\"");
                this.headerScript.append(Str.getLanguage(this.request));
                this.headerScript.append('"');
                if (Var.maskTimeout != 2000) {
                    this.headerScript.append(",mask:");
                    this.headerScript.append(Var.maskTimeout);
                }
                if (Var.ajaxTimeout != 0) {
                    this.headerScript.append(",timeout:");
                    this.headerScript.append(Var.ajaxTimeout);
                }
                if (!"modern".equals(theme)) {
                    this.headerScript.append(",theme:\"");
                    this.headerScript.append(theme);
                    this.headerScript.append('"');
                }
                if (!"classic".equals(touchTheme)) {
                    this.headerScript.append(",touchTheme:\"");
                    this.headerScript.append(touchTheme);
                    this.headerScript.append('"');
                }
                theme = session == null ? null : (String) session.getAttribute("sys.editTheme");
                if (theme == null)
                    theme = Var.getString("sys.ide.editTheme");
                if (!"default".equals(theme)) {
                    this.headerScript.append(",editTheme:\"");
                    this.headerScript.append(theme);
                    this.headerScript.append('"');
                }
                this.headerScript.append("});");
            } else if (mode == RunMode.CONTROL || mode == RunMode.MODULE) {
                // 添加xwl控件有xwlId，导入方法无xwlId
                if (xwlId != null) {
                    this.headerScript.append("\ncontextOwner[");
                    this.headerScript.append(StringUtil.quote(xwlId));
                    this.headerScript.append("]=app;");
                }
            }
        }
        // 导入模块，在解析子控件之前执行
        content = getString(configs, "importModules");
        if (!content.isEmpty())
            importModules(content);
        // serverScript在导入模块之后运行，在开始运行可使用initScript
        content = ServerScript.getScript(configs, "serverScript");
        if (!content.isEmpty())
            ScriptBuffer.run(StringUtil.concat((String) configs.opt("id"), ".ss"), content, request, response,
                    moduleFile);
        if (hasEvents) {
            String beforeunload = getString(events, "beforeunload");
            if (!beforeunload.isEmpty())
                appendScript(headerScript,
                        StringUtil.concat("Wb.onUnload(function(){\n", beforeunload, "\n},contextOwner);"));
            appendScript(this.headerScript, getString(events, "initialize"));
        }
        if (hasChildren) {
            // 直接使用迭代方法，不再使用递归
            scan(module, moduleGeneral, emptyJson, runNormal);
        }
        // 如果是true，记录操作日志
        boolean logRecord = getBool(configs, "logRecord", false);
        if (getBool(configs, "logRecord", false)) {
            boolean logData = getBool(configs, "logData", false);
            String logType = getString(configs, "logType");
            String moduleName = getString(fullModule, "title");
            JSONObject logParams = new JSONObject();
            logParams.put("logRecord", logRecord);
            logParams.put("logData", logData);
            logParams.put("logType", logType);
            logParams.put("moduleName", moduleName);
            WbUtil.run("log_process", logParams, request, false);
        }

        if (this.response.isCommitted())
            return;
        appendScript(this.headerHtml, getString(configs, "finalHtml"));
        if (hasEvents)
            appendScript(this.headerScript, getString(events, "finalize"));
        if (createFrame)
            if (runNormal) {
                if (libTypes[1])
                    this.headerScript.append("\n});");
                else if (libTypes[2])
                    this.headerScript.append("\n}});");
                else
                    this.headerScript.append("\n})({});");
            } else if (mode == RunMode.CONTROL) {
                this.headerScript.append("\nreturn Wb.optMain(app);\n})(");
                this.headerScript.append(params == null ? "{}" : params);
                this.headerScript.append(",app)");
            } else if (mode == RunMode.MODULE) {
                this.headerScript.append("\n})({},app);");
            } else {
                // RUN_INVOKE
                this.headerScript.append("\nreturn app;\n})();");// 不可带参数，在引用时自动传入
            }
        if (runNormal) {
            if (createFrame)
                this.headerScript.append("\n</script>\n</body>\n</html>");
            output();
        } else if (runInvoke) {
            output();
        }
    }

    /**
     * 检查当前请求url中带的_token参数是否有效。
     *
     * @param tokens 模块设置的token列表。
     * @return true有效，false无效。
     */
    private boolean checkToken(String tokens) {
        String token = this.request.getParameter("_token");
        if (StringUtil.isEmpty(token))
            return false;
        String[] ls = StringUtil.split(tokens, ",");
        for (String s : ls) {
            String trimmed = s.trim();
            // 使用常量时间比较，防止时序攻击
            if (MessageDigest.isEqual(token.getBytes(), trimmed.getBytes())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 立即把脚本输出至客户端。
     *
     * @throws IOException 异常
     */
    public void output() throws IOException {
        if ((this.headerHtml.length() > 0) && (this.headerScript.length() > 0))
            this.headerHtml.append('\n');
        // 把headerScript合并到headerHtml中并输出
        this.headerHtml.append(this.headerScript);
        if (this.headerHtml.length() > 0)
            if (WebUtil.jsonResponse(this.request))
                WebUtil.send(this.response, this.headerHtml.toString(), true);
            else
                WebUtil.send(this.response, this.headerHtml);
    }

    /**
     * 解析xwl模块控件。
     *
     * @param jo         控件数据对象。
     * @param rootParent 父控件是否为根控件。
     */
    private void addModule(JSONObject jo, boolean rootParent) throws Exception {
        JSONObject configs = (JSONObject) jo.opt("configs");
        String file = getString(configs, "file");

        if (file != null)
            execute(FileUtil.getModulePath(file), rootParent ? RunMode.MODULE : RunMode.CONTROL,
                    (String) configs.opt("itemId"), (String) configs.opt("params"));
    }

    /**
     * 按顺序解析模块中所有节点内容并执行，使用混合递归/迭代方式处理所有节点。
     * 此方法会先使用递归处理浅层节点，当递归深度超过阈值时切换到迭代方式，平衡性能与安全。
     *
     * @param parentNode    当前节点对象。
     * @param parentGeneral 父级点元数据常规配置信息。
     * @param emptyJson     空JSON对象，用于指定父控件空元数据。
     * @param normalType    是否为普通运行模式。
     */
    private void scan(JSONObject parentNode, JSONObject parentGeneral, JSONObject emptyJson, boolean normalType)
            throws Exception {
        // 使用深度限制的重载方法
        scan(parentNode, parentGeneral, emptyJson, normalType, 0);
    }

    /**
     * 带递归深度限制的scan方法实现 当深度超过阈值会自动切换到迭代方式处理，平衡性能与安全
     *
     * @param parentNode    当前节点对象。
     * @param parentGeneral 父级点元数据常规配置信息。
     * @param emptyJson     空JSON对象，用于指定父控件空元数据。
     * @param normalType    是否为普通运行模式。
     * @param depth         当前递归深度
     */
    private void scan(JSONObject parentNode, JSONObject parentGeneral, JSONObject emptyJson, boolean normalType,
            int depth) throws Exception {
        // 检查参数有效性
        if (parentNode == null) {
            return;
        }

        // 递归深度检查，超过最大深度时切换到迭代方法
        if (depth > MAX_RECURSION_DEPTH) {
            // 记录日志，监控深度切换情况
            LogUtil.warn(request, "递归深度(" + depth + ")超过阈值(" + MAX_RECURSION_DEPTH + ")，切换到迭代方式处理，当前节点类型: "
                    + parentNode.optString("type"));
            // 不抛出异常，而是切换到迭代方法继续处理
            scanIterative(parentNode, parentGeneral, emptyJson, normalType);
            return;
        }

        JSONArray ja = (JSONArray) parentNode.opt("children");
        if (ja == null || ja.length() == 0) {
            return;
        }

        int j = ja.length();
        int k = j - 1;

        for (int i = 0; i < j; i++) {
            JSONObject jo = (JSONObject) ja.opt(i);
            if (jo == null) {
                continue; // 跳过无效节点
            }

            String type = (String) jo.opt("type");
            if (type == null) {
                continue; // 跳过无类型节点
            }

            JSONObject meta = Controls.get(type);
            if (meta == null) {
                LogUtil.warn(request, "未找到控件类型: " + type);
                continue; // 跳过未知控件类型
            }

            JSONObject general = (JSONObject) meta.opt("general");
            String className = general != null ? (String) general.opt("class") : null;
            boolean isScriptControl;
            Control control;

            try {
                if ("null".equals(className)) {
                    // 类名指定为null
                    control = null;
                    isScriptControl = false;
                    if ("xwl".equals(type)) {
                        boolean rootParent = Boolean.TRUE.equals(parentGeneral.opt("root"));
                        addModule(jo, rootParent);
                        if ((!rootParent) && (i < j - 1))
                            this.headerScript.append(',');
                    }
                } else {
                    // 使用缓存获取控件实例
                    control = getControlInstance(className);
                    isScriptControl = control instanceof ScriptControl;
                }

                if (control != null) {
                    control.init(this.request, this.response, jo, meta, parentGeneral, i == k, normalType);
                    control.create();
                }

                if (isScriptControl) {
                    ScriptControl sc = (ScriptControl) control;
                    String headerHtmlContent = sc.getHeaderHtml();
                    if (headerHtmlContent != null && !headerHtmlContent.isEmpty()) {
                        if (this.headerHtml.length() > 0) {
                            this.headerHtml.append('\n');
                        }
                        this.headerHtml.append(headerHtmlContent);
                    }

                    pushHtml(sc.getFooterHtml());

                    String headerScriptContent = sc.getHeaderScript();
                    if (headerScriptContent != null && !headerScriptContent.isEmpty()) {
                        if (this.headerScript.length() > 0) {
                            this.headerScript.append('\n');
                        }
                        this.headerScript.append(headerScriptContent);
                    }

                    pushScript(sc.getFooterScript());
                }

                // 配置的子项作为控件，递归调用时增加深度
                if (jo.has("children")) {
                    if (depth > MAX_RECURSION_DEPTH - 5) { // 接近深度限制时记录日志
                        LogUtil.info(request,
                                "递归深度(" + (depth + 1) + ")接近阈值(" + MAX_RECURSION_DEPTH + ")，当前节点类型: " + type);
                    }
                    scan(jo, general, emptyJson, normalType, depth + 1);
                }

                if (isScriptControl) {
                    String htmlContent = popHtml();
                    if (htmlContent != null && !htmlContent.isEmpty()) {
                        if (this.headerHtml.length() > 0) {
                            this.headerHtml.append('\n');
                        }
                        this.headerHtml.append(htmlContent);
                    }

                    String lastScript = popScript();
                    // 安全检查
                    if (lastScript == null) {
                        LogUtil.error(request, "脚本堆栈错误，可能是pushScript/popScript调用不匹配");
                        continue;
                    }

                    int quoteIndex = lastScript.lastIndexOf('}');
                    JSONObject configItems;
                    if ((quoteIndex != -1) && ((configItems = (JSONObject) jo.opt("__configs")) != null)) {
                        // 注入在XwlBuffer.optimize中自动生成的子项作为配置项
                        if (this.headerScript.length() > 0) {
                            this.headerScript.append(lastScript.substring(0, quoteIndex));
                            this.headerScript.append(',');
                            scan((JSONObject) configItems, emptyJson, emptyJson, normalType, depth + 1);
                            this.headerScript.append(lastScript.substring(quoteIndex));
                        }
                    } else if (lastScript != null && !lastScript.isEmpty()) {
                        if (this.headerScript.length() > 0) {
                            this.headerScript.append('\n');
                        }
                        this.headerScript.append(lastScript);
                    }
                }
            } catch (Exception e) {
                LogUtil.error(request, "处理控件失败: " + type, e);
                throw e; // 重新抛出异常供上层处理
            }
        }
    }

    /**
     * 使用迭代方式处理模块节点，用于递归深度超过阈值时的回退处理
     */
    private void scanIterative(JSONObject rootNode, JSONObject rootGeneral, JSONObject emptyJson, boolean normalType)
            throws Exception {
        if (rootNode == null || !rootNode.has("children")) {
            return;
        }

        // 使用Deque替代Stack，性能更好
        Deque<ScanContext> stack = new ArrayDeque<>();
        // 记录初始堆栈状态，便于后续验证
        int initialScriptPointer = this.scriptPointer;
        int initialHtmlPointer = this.htmlPointer;
        
        stack.push(new ScanContext(rootNode, rootGeneral, emptyJson, 0, null, false, ScanContext.PHASE_NORMAL,
                normalType));

        while (!stack.isEmpty()) {
            ScanContext context = stack.pop();
            JSONObject node = context.node;
            JSONObject general = context.general;

            if (context.phase == ScanContext.PHASE_POST_CHILDREN) {
                // 处理子节点后的阶段
                if (context.isScriptControl) {
                    String htmlContent = popHtml();
                    if (htmlContent != null && !htmlContent.isEmpty()) {
                        if (this.headerHtml.length() > 0) {
                            this.headerHtml.append('\n');
                        }
                        this.headerHtml.append(htmlContent);
                    }

                    String lastScript = popScript();
                    if (lastScript == null || lastScript.isEmpty()) {
                        LogUtil.error(request, "脚本堆栈异常: popScript返回空值或空字符串，节点类型=" + context.jo.optString("type"));
                        continue;
                    }

                    int quoteIndex = lastScript.lastIndexOf('}');
                    JSONObject configItems;
                    if ((quoteIndex != -1) && ((configItems = (JSONObject) context.jo.opt("__configs")) != null)) {
                        // 验证括号匹配
                        if (!validateBrackets(lastScript)) {
                            LogUtil.warn(request, "括号不匹配: " + lastScript);
                        }
                        
                        // 注入在XwlBuffer.optimize中自动生成的子项作为配置项
                        this.headerScript.append(lastScript.substring(0, quoteIndex));
                        this.headerScript.append(',');
                        
                        // 为__configs创建一个特殊上下文，确保其处理结果正确附加到输出中
                        // 先放入配置处理后的回调任务，然后再放入配置处理任务
                        stack.push(new ScanContext(node, // 保留原节点
                                general, // 保留原元数据
                                emptyJson, // 保留空JSON
                                context.index, // 保留原索引
                                lastScript.substring(quoteIndex), // 保存结尾部分脚本
                                context.isScriptControl, // 保留脚本控件标识
                                ScanContext.PHASE_POST_CONFIG, // 配置处理后阶段
                                normalType // 运行模式
                        ));

                        // 处理__configs节点
                        stack.push(new ScanContext(configItems, // 配置项节点
                                emptyJson, // 空元数据
                                emptyJson, // 空JSON
                                0, // 初始索引
                                null, // 无待处理脚本
                                false, // 非脚本控件
                                ScanContext.PHASE_NORMAL, // 正常处理
                                normalType // 运行模式
                        ));
                        continue;
                    } else if (lastScript != null && !lastScript.isEmpty()) {
                        // 验证括号匹配
                        if (!validateBrackets(lastScript)) {
                            LogUtil.warn(request, "括号不匹配: " + lastScript);
                        }
                        
                        if (this.headerScript.length() > 0) {
                            this.headerScript.append('\n');
                        }
                        this.headerScript.append(lastScript);
                    }
                }
                continue;
            } else if (context.phase == ScanContext.PHASE_POST_CONFIG) {
                // 配置项处理完成后，添加结尾脚本
                if (context.pendingScript != null) {
                    // 验证括号匹配
                    if (!validateBrackets(context.pendingScript)) {
                        LogUtil.warn(request, "结尾脚本括号不匹配: " + context.pendingScript);
                    }
                    
                    // 处理完配置项后添加结尾的右花括号
                    this.headerScript.append(context.pendingScript);
                }
                continue;
            }

            // 主要处理阶段
            JSONArray ja = (JSONArray) node.opt("children");
            if (ja == null || ja.length() == 0) {
                continue;
            }

            int j = ja.length();
            int k = j - 1;

            // 获取当前要处理的子节点
            if (context.index >= j) {
                continue;
            }

            JSONObject jo = (JSONObject) ja.opt(context.index);
            if (jo == null) {
                // 处理当前节点的下一个子节点
                stack.push(new ScanContext(node, general, emptyJson, context.index + 1, null, false,
                        ScanContext.PHASE_NORMAL, normalType));
                continue;
            }

            String type = (String) jo.opt("type");
            if (type == null) {
                // 处理当前节点的下一个子节点
                stack.push(new ScanContext(node, general, emptyJson, context.index + 1, null, false,
                        ScanContext.PHASE_NORMAL, normalType));
                continue;
            }

            JSONObject meta = Controls.get(type);
            if (meta == null) {
                LogUtil.warn(request, "未找到控件类型: " + type);
                // 处理当前节点的下一个子节点
                stack.push(new ScanContext(node, general, emptyJson, context.index + 1, null, false,
                        ScanContext.PHASE_NORMAL, normalType));
                continue;
            }

            JSONObject nodeGeneral = (JSONObject) meta.opt("general");
            String className = nodeGeneral != null ? (String) nodeGeneral.opt("class") : null;
            boolean isScriptControl = false;
            Control control = null;

            try {
                if ("null".equals(className)) {
                    // 类名指定为null
                    isScriptControl = false;
                    if ("xwl".equals(type)) {
                        boolean rootParent = Boolean.TRUE.equals(context.general.opt("root"));
                        addModule(jo, rootParent);
                        if ((!rootParent) && (context.index < j - 1))
                            this.headerScript.append(',');
                    }
                } else {
                    // 使用缓存获取控件实例
                    control = getControlInstance(className);
                    isScriptControl = control instanceof ScriptControl;
                }

                if (control != null) {
                    control.init(this.request, this.response, jo, meta, context.general, context.index == k,
                            context.normalType);
                    control.create();
                }

                if (isScriptControl) {
                    ScriptControl sc = (ScriptControl) control;
                    String headerHtmlContent = sc.getHeaderHtml();
                    if (headerHtmlContent != null && !headerHtmlContent.isEmpty()) {
                        if (this.headerHtml.length() > 0) {
                            this.headerHtml.append('\n');
                        }
                        this.headerHtml.append(headerHtmlContent);
                    }

                    pushHtml(sc.getFooterHtml());

                    String headerScriptContent = sc.getHeaderScript();
                    if (headerScriptContent != null && !headerScriptContent.isEmpty()) {
                        if (this.headerScript.length() > 0) {
                            this.headerScript.append('\n');
                        }
                        this.headerScript.append(headerScriptContent);
                    }

                    pushScript(sc.getFooterScript());
                }
            } catch (Exception e) {
                LogUtil.error(request, "处理控件失败: " + type, e);
                throw e; // 重新抛出异常供上层处理
            }

            // 处理当前节点的下一个子节点
            stack.push(new ScanContext(node, general, emptyJson, context.index + 1, null, false,
                    ScanContext.PHASE_NORMAL, normalType));

            // 如果当前节点有子节点，先处理子节点
            if (jo.has("children")) {
                // 添加子节点处理完后的回调
                stack.push(new ScanContext(jo, nodeGeneral, emptyJson, 0, null, isScriptControl,
                        ScanContext.PHASE_POST_CHILDREN, normalType));
                stack.push(new ScanContext(jo, nodeGeneral, emptyJson, 0, null, isScriptControl,
                        ScanContext.PHASE_NORMAL, normalType));
            } else if (isScriptControl) {
                // 如果没有子节点但是ScriptControl，也需要后处理
                stack.push(new ScanContext(jo, nodeGeneral, emptyJson, 0, null, isScriptControl,
                        ScanContext.PHASE_POST_CHILDREN, normalType));
            }
        }
        
        // 验证结束时堆栈状态
        if (this.scriptPointer != initialScriptPointer) {
            LogUtil.warn(request, "脚本堆栈不平衡: 开始=" + initialScriptPointer + ", 结束=" + this.scriptPointer);
        }
        
        if (this.htmlPointer != initialHtmlPointer) {
            LogUtil.warn(request, "HTML堆栈不平衡: 开始=" + initialHtmlPointer + ", 结束=" + this.htmlPointer);
        }
    }
    
    /**
     * 验证脚本中的括号是否匹配
     * 
     * @param script 需要验证的脚本
     * @return 括号是否匹配
     */
    private boolean validateBrackets(String script) {
        if (script == null) {
            return false;
        }
        
        Deque<Character> stack = new ArrayDeque<>();
        for (int i = 0; i < script.length(); i++) {
            char c = script.charAt(i);
            switch (c) {
                case '{':
                case '[':
                case '(':
                    stack.push(c);
                    break;
                case '}':
                    if (stack.isEmpty() || stack.pop() != '{') {
                        return false;
                    }
                    break;
                case ']':
                    if (stack.isEmpty() || stack.pop() != '[') {
                        return false;
                    }
                    break;
                case ')':
                    if (stack.isEmpty() || stack.pop() != '(') {
                        return false;
                    }
                    break;
            }
        }
        return stack.isEmpty();
    }

    /**
     * 导入在importModules属性中指定的子模块列表。
     *
     * @param modules 导入的模块列表。
     * @throws Exception 导入过程发生异常。
     */
    private void importModules(String modules) throws Exception {
        if (StringUtil.isEmpty(modules))
            return;

        JSONArray moduleArray = new JSONArray(modules);
        int length = moduleArray.length();

        // 总是顺序执行模块导入，避免数据库连接冲突问题
        for (int i = 0; i < length; i++) {
            execute(FileUtil.getModulePath((String) moduleArray.opt(i)), RunMode.MODULE, "importXwl" + (i + 1), null);
        }
    }

    /**
     * 设置模块的js和css链接。
     *
     * @param configs 模块的配置项。
     * @param theme   界面方案名称。
     * @return 加载的库列表。0 未知， 1 Ext, 2 Touch, 3 BS。
     */
    private boolean[] setLinks(JSONObject configs, String theme, String touchTheme) {
        List<String> cssArray = new ArrayList<String>(5);
        List<String> jsArray = new ArrayList<String>(5);
        JSONArray cssLinks = null;
        JSONArray jsLinks = null;

        String loadJS = getString(configs, "loadJS");
        String lang = Str.getClientLanguage(this.request);

        boolean[] libTypes = new boolean[4];
        String debugSuffix = Var.debug ? "-debug" : "";
        this.request.setAttribute("debugSuffix", debugSuffix);

        // 处理CSS链接
        String cssLinksText = getString(configs, "cssLinks");
        if (cssLinksText.isEmpty()) {
            cssLinks = Var.cssLinks;
        } else {
            cssLinks = new JSONArray();
            if (Var.cssLinks != null)
                JsonUtil.addAll(cssLinks, Var.cssLinks);
            JsonUtil.addAll(cssLinks, new JSONArray(cssLinksText));
        }

        // 处理JS链接
        String jsLinksText = getString(configs, "jsLinks");
        if (jsLinksText.isEmpty()) {
            jsLinks = Var.jsLinks;
        } else {
            jsLinks = new JSONArray();
            if (Var.jsLinks != null)
                JsonUtil.addAll(jsLinks, Var.jsLinks);
            JsonUtil.addAll(jsLinks, new JSONArray(jsLinksText));
        }

        this.notLoadNone = !"none".equals(loadJS);
        if (loadJS.isEmpty())
            loadJS = "ext";

        // 预分配StringBuilder容量，避免多次扩容
        StringBuilder linkBuilder = new StringBuilder(256);

        // 添加语言脚本
        jsArray.add(linkBuilder.append("wb/script/locale/wb-lang-").append(Str.getLanguage(this.request))
                .append(debugSuffix).append(".js").toString());
        linkBuilder.setLength(0);

        // 根据不同库类型添加不同链接
        if (loadJS.contains("ext")) {
            libTypes[1] = true;
            cssArray.add(linkBuilder.append("wb/libs/ext/resources/ext-theme-").append(theme).append("/ext-theme-")
                    .append(theme).append("-all").append(debugSuffix).append(".css").toString());
            linkBuilder.setLength(0);

            jsArray.add(linkBuilder.append("wb/libs/ext/ext-all").append(debugSuffix).append(".js").toString());
            linkBuilder.setLength(0);

            jsArray.add(linkBuilder.append("wb/libs/ext/locale/ext-lang-").append(Str.optExtLanguage(lang))
                    .append(debugSuffix).append(".js").toString());
            linkBuilder.setLength(0);
        }

        if (loadJS.contains("touch")) {
            libTypes[2] = true;
            cssArray.add(linkBuilder.append("wb/libs/touch/resources/css/").append(touchTheme).append(debugSuffix)
                    .append(".css").toString());
            linkBuilder.setLength(0);

            jsArray.add(linkBuilder.append("wb/libs/touch/locale/t-lang-").append(Str.optTouchLanguage(lang))
                    .append(debugSuffix).append(".js").toString());
            linkBuilder.setLength(0);

            jsArray.add(
                    linkBuilder.append("wb/libs/touch/sencha-touch-all").append(debugSuffix).append(".js").toString());
            linkBuilder.setLength(0);
        }

        if (loadJS.contains("bootstrap")) {
            libTypes[3] = true;
            cssArray.add(linkBuilder.append("wb/libs/bs/css/bootstrap").append(debugSuffix).append(".css").toString());
            linkBuilder.setLength(0);

            jsArray.add(linkBuilder.append("wb/libs/jquery/jquery").append(debugSuffix).append(".js").toString());
            linkBuilder.setLength(0);

            jsArray.add(linkBuilder.append("wb/libs/bs/js/bootstrap").append(debugSuffix).append(".js").toString());
            linkBuilder.setLength(0);
        }

        if (loadJS.contains("jquery")) {
            jsArray.add(linkBuilder.append("wb/libs/jquery/jquery").append(debugSuffix).append(".js").toString());
            linkBuilder.setLength(0);
        }

        cssArray.add(linkBuilder.append("wb/css/style").append(debugSuffix).append(".css").toString());
        linkBuilder.setLength(0);

        jsArray.add(linkBuilder.append("wb/script/wb").append(debugSuffix).append(".js?t=")
                .append(System.currentTimeMillis()).toString());

        // 处理自定义CSS链接
        if (cssLinks != null) {
            int j = cssLinks.length();
            for (int i = 0; i < j; i++) {
                String value = cssLinks.getString(i);
                int index = cssArray.indexOf(value);
                // 允许重新设置css加载顺序
                if (index != -1)
                    cssArray.remove(index);
                cssArray.add(value);
            }
        }

        // 处理自定义JS链接
        if (jsLinks != null) {
            int j = jsLinks.length();
            for (int i = 0; i < j; i++) {
                String value = jsLinks.getString(i);
                int index = jsArray.indexOf(value);
                // 允许重新设置js加载顺序
                if (index != -1)
                    jsArray.remove(index);
                jsArray.add(value);
            }
        }

        // 构建CSS链接标签
        for (String css : cssArray) {
            this.headerHtml.append("\n<link type=\"text/css\" rel=\"stylesheet\" href=\"").append(css).append("\">");
        }

        // 构建JS链接标签
        for (String js : jsArray) {
            this.headerHtml.append("\n<script type=\"text/javascript\" src=\"").append(js).append("\"></script>");
        }

        return libTypes;
    }

    private void addLinksScript(JSONObject configs) {
        this.request.setAttribute("debugSuffix", Var.debug ? "-debug" : "");
        String cssLinks = getString(configs, "cssLinks");
        String jsLinks = getString(configs, "jsLinks");
        boolean emptyCss = cssLinks.isEmpty();
        boolean emptyJs = jsLinks.isEmpty();
        if ((!emptyCss) || (!emptyJs)) {
            this.headerScript.append("$$@blink{");
            if (!emptyCss) {
                this.headerScript.append("\"css\":");
                this.headerScript.append(cssLinks);
            }
            if (!emptyJs) {
                if (!emptyCss)
                    this.headerScript.append(',');
                this.headerScript.append("\"js\":");
                this.headerScript.append(jsLinks);
            }
            if (getBool(configs, "recursiveLoad", false)) {
                this.headerScript.append(",\"recursive\":true");
            }
            this.headerScript.append("}$$@elink");
        }
    }

    /**
     * 获取对象中指定名称的替换参数后的字符串值。
     *
     * @param object JSNObject对象。
     * @param name   名称。
     * @return 获取的值。如果值为空返回空字符串。
     */
    private String getString(JSONObject object, String name) {
        String value = (String) object.opt(name);
        if (value == null) {
            return "";
        }
        value = WebUtil.replaceParams(this.request, value);
        if (Var.getBool("sys.enableMinify") && this.isMinify
                && StringUtil.indexOf(
                        new String[] { "initialize", "finalize", "beforeunload", "finalHtml", "initHtml", "headLast" },
                        name) != -1) {
            value = ScriptMinifier.minify(value);
        }
        return value;
    }

    /**
     * 获取对象中指定名称的替换参数后的布尔值。
     *
     * @param object       JSNObject对象。
     * @param name         名称。
     * @param defaultValue 默认值。
     * @return 获取的值。如果值为空返回默认值。
     */
    private boolean getBool(JSONObject object, String name, boolean defaultValue) {
        String value = getString(object, name);
        if (value.isEmpty()) {
            return defaultValue;
        }
        return Boolean.parseBoolean(value);
    }

    /**
     * 关闭存储在map中的对象。
     *
     * @param map          存储的map对象。
     * @param hasException 是否有异常，如果存在未提交事务且有异常将回滚事务否则提交事务。
     */
    private void closeObjects(ConcurrentHashMap<String, Object> map, boolean hasException) {
        if (map == null || map.isEmpty()) {
            return;
        }

        Set<Entry<String, Object>> es = map.entrySet();
        List<Entry<String, Object>> resultSetEntries = new ArrayList<>();
        List<Entry<String, Object>> stmtEntries = new ArrayList<>();
        List<Entry<String, Object>> psEntries = new ArrayList<>(); // PreparedStatement
        List<Entry<String, Object>> csEntries = new ArrayList<>(); // CallableStatement
        List<Entry<String, Object>> connEntries = new ArrayList<>();
        List<Entry<String, Object>> isEntries = new ArrayList<>();
        List<Entry<String, Object>> osEntries = new ArrayList<>();
        List<Entry<String, Object>> blobEntries = new ArrayList<>(); // Blob
        List<Entry<String, Object>> clobEntries = new ArrayList<>(); // Clob

        // 收集需要关闭的资源
        for (Entry<String, Object> e : es) {
            Object object = e.getValue();
            if (object != null) {
                if (object instanceof ResultSet) {
                    resultSetEntries.add(e);
                } else if (object instanceof CallableStatement) {
                    // CallableStatement是PreparedStatement的子类，需要先检查
                    csEntries.add(e);
                } else if (object instanceof PreparedStatement) {
                    psEntries.add(e);
                } else if (object instanceof Statement) {
                    stmtEntries.add(e);
                } else if (object instanceof Connection) {
                    connEntries.add(e);
                } else if (object instanceof InputStream) {
                    isEntries.add(e);
                } else if (object instanceof OutputStream) {
                    osEntries.add(e);
                } else if (object instanceof Blob) {
                    blobEntries.add(e);
                } else if (object instanceof Clob) {
                    clobEntries.add(e);
                }
            }
        }

        // 关闭Blob并从map中移除
        for (Entry<String, Object> e : blobEntries) {
            try {
                Blob blob = (Blob) e.getValue();
                // 释放Blob资源
                blob.free();
                map.remove(e.getKey());
            } catch (SQLException ex) {
                LogUtil.error(request, "关闭Blob资源失败", ex);
            }
        }

        // 关闭Clob并从map中移除
        for (Entry<String, Object> e : clobEntries) {
            try {
                Clob clob = (Clob) e.getValue();
                // 释放Clob资源
                clob.free();
                map.remove(e.getKey());
            } catch (SQLException ex) {
                LogUtil.error(request, "关闭Clob资源失败", ex);
            }
        }

        // 关闭ResultSet并从map中移除
        for (Entry<String, Object> e : resultSetEntries) {
            DbUtil.close((ResultSet) e.getValue());
            map.remove(e.getKey());
        }

        // 关闭CallableStatement并从map中移除
        for (Entry<String, Object> e : csEntries) {
            DbUtil.close((CallableStatement) e.getValue());
            map.remove(e.getKey());
        }

        // 关闭PreparedStatement并从map中移除
        for (Entry<String, Object> e : psEntries) {
            DbUtil.close((PreparedStatement) e.getValue());
            map.remove(e.getKey());
        }

        // 关闭Statement并从map中移除
        for (Entry<String, Object> e : stmtEntries) {
            DbUtil.close((Statement) e.getValue());
            map.remove(e.getKey());
        }

        // 处理数据库连接并从map中移除
        for (Entry<String, Object> e : connEntries) {
            Connection conn = (Connection) e.getValue();
            try {
                if (hasException) {
                    DbUtil.close(conn);
                } else {
                    DbUtil.closeCommit(conn);
                }
                map.remove(e.getKey());
            } catch (Exception ex) {
                LogUtil.error(request, "关闭连接异常", ex);
            }
        }

        // 关闭输入流并从map中移除
        for (Entry<String, Object> e : isEntries) {
            InputStream is = (InputStream) e.getValue();
            try {
                is.close();
                map.remove(e.getKey());
            } catch (IOException ex) {
                // 忽略关闭异常
            }
        }

        // 关闭输出流并从map中移除
        for (Entry<String, Object> e : osEntries) {
            OutputStream os = (OutputStream) e.getValue();
            try {
                os.close();
                map.remove(e.getKey());
            } catch (IOException ex) {
                // 忽略关闭异常
            }
        }
    }

    /**
     * 获取touch模式viewport控件的脚本。
     *
     * @param items         模块根控件列表。
     * @param parentGeneral 模块控件general属性。
     * @param normalType    是否是普通运行模式。
     * @return viewport控件脚本，如果不存在viewport控件返回空串。
     */
    private String getTouchViewport(JSONArray items, JSONObject parentGeneral, boolean normalType) throws Exception {
        if (items == null)
            return "";
        JSONObject meta = Controls.get("tviewport");
        StringBuilder script = new StringBuilder(512);

        int j = items.length();
        int k = j - 1;

        for (int i = 0; i < j; i++) {
            JSONObject jo = (JSONObject) items.opt(i);
            if ("tviewport".equals(jo.opt("type"))) {
                ExtControl control = new ExtControl();
                control.normalMode = false;
                control.init(this.request, this.response, jo, meta, parentGeneral, i == k, normalType);
                control.create();
                script.append("\nviewport:");
                script.append(control.getHeaderScript());
                script.append(control.getFooterScript());
                script.append(',');
                return script.toString();
            }
        }
        return "";
    }

    /**
     * 创建会话， 并存储当前用户数据至会话Attribute。
     *
     * @param session
     * @param smartStr
     * @throws Exception
     */
    public void createSessionUserSmart(HttpSession session, String smartStr) throws Exception {
        this.sessionSmart.setAttribute("smart", smartStr);
        this.request.setAttribute("sys.user", session.getAttribute("sys.user"));
        this.request.setAttribute("sys.username", session.getAttribute("sys.username"));
        this.request.setAttribute("sys.dispname", session.getAttribute("sys.dispname"));
        Cookie cookie = new Cookie("JSESSIONID", session.getId());
        this.response.addCookie(cookie);
    }

    /**
     * 把指定footerHtml脚本添加到堆栈中。此方法较Stack类更高效。
     *
     * @param script 添加的footerHtml脚本。
     */
    private void pushHtml(String script) {
        this.htmlPointer += 1;
        if (this.footerHtml.size() < this.htmlPointer)
            this.footerHtml.add(script);
        else
            this.footerHtml.set(this.htmlPointer - 1, script);
    }

    /**
     * 提取堆栈中最后一项footerHtml脚本。
     *
     * @return footerHtml脚本。如果堆栈为空，返回空字符串。
     */
    private String popHtml() {
        // 安全检查，防止堆栈下溢
        if (this.htmlPointer <= 0) {
            LogUtil.error(request, "HTML堆栈下溢：当前指针=" + this.htmlPointer);
            return "";
        }
        this.htmlPointer -= 1;
        return (String) this.footerHtml.get(this.htmlPointer);
    }

    /**
     * 把指定footerScript脚本添加到堆栈中。此方法较Stack类更高效。
     *
     * @param script 添加的footerScript脚本。
     */
    private void pushScript(String script) {
        this.scriptPointer += 1;
        if (this.footerScript.size() < this.scriptPointer)
            this.footerScript.add(script);
        else
            this.footerScript.set(this.scriptPointer - 1, script);
    }

    /**
     * 提取堆栈中最后一项footerScript脚本。
     *
     * @return footerScript脚本。如果堆栈为空，返回空字符串。
     */
    private String popScript() {
        // 安全检查，防止堆栈下溢
        if (this.scriptPointer <= 0) {
            LogUtil.error(request, "脚本堆栈下溢：当前指针=" + this.scriptPointer);
            return "";
        }
        this.scriptPointer -= 1;
        return (String) this.footerScript.get(this.scriptPointer);
    }

    /**
     * 添加脚本至指定StringBuilder对象。如果添加之前已经存在脚本，则换行后添加脚本。
     *
     * @param buf    需要被添加脚本的StringBuilder对象。
     * @param script 添加的脚本。
     */
    private void appendScript(StringBuilder buf, String script) {
        if (script != null && !script.isEmpty()) {
            if (buf.length() > 0)
                buf.append('\n');
            buf.append(script);
        }
    }

    /**
     * 获取控件类的实例，使用缓存提高性能
     * 
     * @param className 控件类名
     * @return 控件类实例
     * @throws Exception 实例化失败时抛出异常
     */
    private Control getControlInstance(String className) throws Exception {
        if (className == null) {
            return new ExtControl();
        }

        if ("null".equals(className)) {
            return null;
        }

        // 处理类名
        String fullClassName = className;
        if (className.indexOf('.') == -1) {
            fullClassName = "com.wb.controls." + className;
        }

        // 从缓存获取Class对象
        Class<?> controlClass = CONTROL_CLASS_CACHE.get(fullClassName);
        if (controlClass == null) {
            // 缓存未命中，加载类并放入缓存
            controlClass = Class.forName(fullClassName);
            CONTROL_CLASS_CACHE.putIfAbsent(fullClassName, controlClass);
        }

        // 实例化控件
        try {
            return (Control) controlClass.getDeclaredConstructor().newInstance();
        } catch (NoSuchMethodException | InstantiationException | IllegalAccessException
                | java.lang.reflect.InvocationTargetException e) {
            throw new ServletException("实例化控件失败: " + fullClassName, e);
        }
    }

    /**
     * 扫描上下文，用于迭代实现
     */
    private static class ScanContext {
        // 节点处理阶段：前处理
        public static final int PHASE_NORMAL = 0;
        // 节点处理阶段：子节点处理后
        public static final int PHASE_POST_CHILDREN = 1;
        // 节点处理阶段：配置项处理后
        public static final int PHASE_POST_CONFIG = 2;

        final JSONObject node; // 当前节点
        final JSONObject general; // 节点元数据
        final JSONObject emptyJson; // 空JSON对象
        final int index; // 当前索引
        final JSONObject jo; // 当前处理的子节点
        final boolean isScriptControl; // 是否脚本控件
        final String pendingScript; // 待处理脚本
        final int phase; // 处理阶段
        final boolean normalType; // 是否为普通运行模式

        ScanContext(JSONObject node, JSONObject general, JSONObject emptyJson, int index, String pendingScript,
                boolean isScriptControl, int phase, boolean normalType) {
            this.node = node;
            this.general = general;
            this.emptyJson = emptyJson;
            this.index = index;
            this.pendingScript = pendingScript;
            this.isScriptControl = isScriptControl;
            this.jo = node;
            this.phase = phase;
            this.normalType = normalType;
        }
    }

    /**
     * 请求包装器，用于在异步线程中安全访问请求参数和属性
     */
    private static class RequestWrapper extends HttpServletRequestWrapper {
        private final Map<String, String[]> parameters;
        private final Map<String, Object> attributes;
        private final String method;
        private final String requestURI;
        private final StringBuffer requestURL;
        private final String contextPath;
        private final String servletPath;
        private final Cookie[] cookies;

        public RequestWrapper(HttpServletRequest request) {
            super(request);
            // 复制请求参数
            parameters = new HashMap<>(request.getParameterMap());

            // 复制请求属性
            attributes = new HashMap<>();
            Enumeration<String> attrNames = request.getAttributeNames();
            while (attrNames.hasMoreElements()) {
                String name = attrNames.nextElement();
                attributes.put(name, request.getAttribute(name));
            }

            // 复制请求元数据
            method = request.getMethod();
            requestURI = request.getRequestURI();
            requestURL = request.getRequestURL();
            contextPath = request.getContextPath();
            servletPath = request.getServletPath();
            cookies = request.getCookies() != null ? request.getCookies().clone() : null;
        }

        @Override
        public String getParameter(String name) {
            String[] values = parameters.get(name);
            return values != null && values.length > 0 ? values[0] : null;
        }

        @Override
        public Map<String, String[]> getParameterMap() {
            return Collections.unmodifiableMap(parameters);
        }

        @Override
        public Enumeration<String> getParameterNames() {
            return Collections.enumeration(parameters.keySet());
        }

        @Override
        public String[] getParameterValues(String name) {
            return parameters.get(name);
        }

        @Override
        public Object getAttribute(String name) {
            return attributes.get(name);
        }

        @Override
        public Enumeration<String> getAttributeNames() {
            return Collections.enumeration(attributes.keySet());
        }

        @Override
        public void setAttribute(String name, Object value) {
            attributes.put(name, value);
        }

        @Override
        public void removeAttribute(String name) {
            attributes.remove(name);
        }

        @Override
        public String getMethod() {
            return method;
        }

        @Override
        public String getRequestURI() {
            return requestURI;
        }

        @Override
        public StringBuffer getRequestURL() {
            return new StringBuffer(requestURL);
        }

        @Override
        public String getContextPath() {
            return contextPath;
        }

        @Override
        public String getServletPath() {
            return servletPath;
        }

        @Override
        public Cookie[] getCookies() {
            return cookies != null ? cookies.clone() : null;
        }
    }

    /**
     * 响应包装器，用于在异步线程中安全处理响应
     */
    private static class ResponseWrapper extends HttpServletResponseWrapper {
        private boolean committed = false;

        public ResponseWrapper(HttpServletResponse response) {
            super(response);
        }

        @Override
        public void sendError(int sc, String msg) throws IOException {
            if (!committed) {
                super.sendError(sc, msg);
                committed = true;
            }
        }

        @Override
        public void sendError(int sc) throws IOException {
            if (!committed) {
                super.sendError(sc);
                committed = true;
            }
        }

        @Override
        public void sendRedirect(String location) throws IOException {
            if (!committed) {
                super.sendRedirect(location);
                committed = true;
            }
        }

        @Override
        public boolean isCommitted() {
            return committed || super.isCommitted();
        }

        @Override
        public void flushBuffer() throws IOException {
            if (!committed) {
                super.flushBuffer();
                committed = true;
            }
        }
    }
}