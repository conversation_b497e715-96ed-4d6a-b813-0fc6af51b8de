package com.wb.rocketmq.listener;

import com.wb.rocketmq.util.MessageUtil;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.consumer.MessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.json.JSONObject;

public class TimeDelayMessageListener implements MessageListener {
    @Override
    public ConsumeResult consume(MessageView messageView) {
        JSONObject params = MessageUtil.ByteBufferToJsonObject(messageView.getBody());
        return MessageUtil.onSuccess(params, messageView.getMessageId().toString());
    }
}
