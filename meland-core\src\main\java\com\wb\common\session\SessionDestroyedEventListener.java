package com.wb.common.session;

import com.wb.cache.RedisCache;
import com.wb.common.Base;
import com.wb.common.UserList;
import com.wb.common.Var;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.session.FindByIndexNameSessionRepository;
import org.springframework.session.Session;
import org.springframework.session.events.SessionDestroyedEvent;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 会话销毁事件监听器，用于处理会话销毁时的清理逻辑。
 * 实现 ApplicationListener 接口，监听 SessionDestroyedEvent 事件。
 */
@Component
public class SessionDestroyedEventListener implements ApplicationListener<SessionDestroyedEvent> {

    private static final Logger logger = LoggerFactory.getLogger(SessionDestroyedEventListener.class);
    private static final String USER_ID_KEY = "sys.user";
    private static final String USERNAME_KEY = "sys.username";
    private static final String IP_KEY = "sys.ip";

    /**
     * 处理 SessionDestroyedEvent 事件的方法。
     * 在会话销毁时触发，执行清理和日志记录的相关逻辑。
     *
     * @param event 传入的会话销毁事件，提供会话信息以便处理相关逻辑。
     */
    @Override
    public void onApplicationEvent(SessionDestroyedEvent event) {
        Session session = event.getSession();
        logger.debug("捕获到会话销毁事件：{}", session.getId());
        
        // 清理主身份索引
        cleanupPrincipalIndexes(session);
        
        // 记录用户登出日志
        logUserLogout(session);
        
        // 处理在线用户列表
        handleOnlineUsersList(session);
        
        // 如果有WebSocket连接，处理它们的关闭
        closeWebSocketConnections(session);
        
        logger.debug("会话销毁事件处理完成，会话ID：{}", session.getId());
    }

    /**
     * 清理与当前会话中主身份名称相关的索引。
     * 如果会话中没有主身份名称属性，则方法直接返回。
     * 如果存在主身份名称，会根据该名称生成 Redis 索引模式并删除相关的 Redis 键。
     *
     * @param session 当前会话对象，用于获取主身份名称及进行索引清理
     */
    private void cleanupPrincipalIndexes(Session session) {
        logger.debug("开始清理主身份索引，会话ID：{}", session.getId());
        String principalName = session.getAttribute(FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME);
        if (principalName == null) {
            logger.debug("会话中没有主身份名称，跳过索引清理");
            return;
        }

        logger.debug("主身份名称：{}", principalName);
        String pattern = buildRedisIndexPattern(principalName);
        logger.debug("删除Redis索引模式：{}", pattern);
        deleteRedisKeys(pattern);
    }

    /**
     * 构建 Redis 索引模式字符串，该模式用于根据会话中存储的主体名称（principalName）查找相关会话索引。
     *
     * @param principalName 会话关联的主体名称，用于构建 Redis 的索引模式。
     * @return Redis 索引模式字符串，形式为 "spring:session:index:{索引名称}:{主体名称}"。
     */
    private String buildRedisIndexPattern(String principalName) {
        String pattern = String.format("spring:session:index:%s:%s",
                FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME,
                principalName);
        return pattern;
    }

    /**
     * 删除 Redis 中符合指定模式的所有键。
     *
     * @param pattern 键的匹配模式，用于查找需要删除的 Redis 键。
     */
    private void deleteRedisKeys(String pattern) {
        List<String> keys = Base.map.scan(pattern);
        if (keys != null && !keys.isEmpty()) {
            Base.map.delKeys(keys);
        }
    }
    
    /**
     * 记录用户登出日志
     *
     * @param session 当前会话
     */
    private void logUserLogout(Session session) {
        if (Var.log) {
            String username = session.getAttribute(USERNAME_KEY);
            String ip = session.getAttribute(IP_KEY);
            if (username != null && ip != null) {
                LogUtil.log(username, ip, LogUtil.INFO, "logout");
            }
        }
    }
    
    /**
     * 处理在线用户列表，如果用户没有其它会话则从在线列表移除
     *
     * @param session 当前会话
     */
    private void handleOnlineUsersList(Session session) {
        String userId = session.getAttribute(USER_ID_KEY);
        if (!StringUtil.isEmpty(userId)) {
            logger.debug("用户ID：{}", userId);
            // 如果用户没有任何会话，才做移除操作
            int sessionCount = UserList.getSessions(userId).length;
            logger.debug("用户剩余会话数：{}", sessionCount);
            if (sessionCount == 0) {
                logger.debug("用户没有其他会话，从在线用户列表中移除");
                Base.map.zsetDel(RedisCache.ONLINE_USERS, userId);
            } else {
                logger.debug("用户还有其他会话，保留在在线用户列表中");
            }
        }
    }
    
    /**
     * 关闭与会话关联的所有WebSocket连接
     *
     * @param session 当前会话
     */
    private void closeWebSocketConnections(Session session) {
        // 检查是否有WebSocket相关的标记
        Boolean hasWebSocket = session.getAttribute("sysx.hasWebSocket");
        if (Boolean.TRUE.equals(hasWebSocket)) {
            try {
                // 使用SessionBridge的逻辑处理WebSocket连接
                Object socketBridge = session.getAttribute("sysx.socket");
                if (socketBridge != null) {
                    // 这里将间接调用WebSocketSessionManager的removeSession方法
                    // 通过session的ID来查找和关闭相关的WebSocket连接
                    String sessionId = session.getId();
                    logger.debug("准备关闭会话{}的WebSocket连接", sessionId);
                }
            } catch (Exception e) {
                LogUtil.error("关闭WebSocket连接时出错: " + e.getMessage(), e);
            }
        }
    }
} 