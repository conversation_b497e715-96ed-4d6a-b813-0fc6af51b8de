package com.wb.flow;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.json.JSONArray;
import org.json.JSONObject;

import com.wb.common.ScriptBuffer;
import com.wb.common.Str;
import com.wb.message.MessageSender;
import com.wb.util.DateUtil;
import com.wb.util.DbUtil;
import com.wb.util.JsonUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;
import com.wb.util.WbUtil;
import com.wb.util.WebUtil;

public class FlowObject {
	private static final String startName = "开始";
	private static final String endName = "结束";
	public JSONObject flowJson;
	private JSONObject flowProp;
	private JSONObject params;
	private String activeNodeName;
	private JSONObject activeNode;
	private JSONArray history;
	private HttpServletRequest request;
	private String userId;
	private String userName;
	private String userDispName;
	private String flowId;
	private JSONArray nodes;
	private JSONArray links;
	private Timestamp actionDate;
	private boolean nodeChanged;
	private boolean setProcessed = true;
	private String startFlowId;
	private ArrayList<String> newUserList = new ArrayList<String>();

	public FlowObject(JSONObject flowJson, JSONObject extraParams, HttpServletRequest request, String flowName,
			boolean isStart, String startFlowId) {
		this.actionDate = DateUtil.now();
		this.flowJson = flowJson;
		this.startFlowId = startFlowId;
		this.flowProp = flowJson.getJSONObject("flow");
		if ((isStart) && (StringUtil.isEmpty(this.flowProp.optString("flowName"))) && (flowName != null))
			this.flowProp.put("flowName", flowName);
		this.nodes = flowJson.getJSONArray("nodes");
		this.links = flowJson.getJSONArray("links");
		this.history = flowJson.optJSONArray("history");
		if (this.history == null) {
			this.history = new JSONArray();
			flowJson.put("history", this.history);
		}
		this.params = flowJson.optJSONObject("params");
		if (this.params == null) {
			this.params = new JSONObject();
			flowJson.put("params", this.params);
		}
		if (extraParams != null) {
			applyExtraParams(extraParams);
		}
		this.activeNodeName = flowJson.optString("activeNode", startName);
		this.activeNode = getNode(this.activeNodeName);
		this.request = request;
		setUserVar();
		if (isStart) {
			verifyStartNode();
			setStartParams();
		}
		this.params.put("flow.lastDate", this.actionDate);
		this.params.put("flow.lastUserId", this.userId);
		this.params.put("flow.lastUserName", this.userName);
		this.params.put("flow.lastUserDispName", this.userDispName);
		this.params.put("flow.lastNodeName", this.activeNodeName);
		this.params.put("flow.lastNodeTitle", getTitle(this.activeNode));
		this.flowId = this.params.getString("flow.id");
	}

	private void applyExtraParams(JSONObject extraParams) {
		Set<Entry<String, Object>> es = extraParams.entrySet();

		for (Entry<String, Object> e : es) {
			String key = (String) e.getKey();
			if (key.indexOf('.') != -1)
				throw new IllegalArgumentException("Invalid parameter name \"" + key + "\".");
			this.params.put(key, e.getValue());
		}
	}

	private void verifyStartNode() {
		JSONObject startNode = getNode(startName);
		if (!StringUtil.isEmpty(startNode.optString("doUser")))
			throw new IllegalArgumentException("开始节点不能指定处理人员。");
		if (!StringUtil.isEmpty(startNode.optString("passUsers")))
			throw new IllegalArgumentException("开始节点不能指定通过人数。");
		JSONObject doUser = new JSONObject();
		doUser.put("userId", new JSONArray().put(this.userId));
		startNode.put("doUser", doUser);
	}

	private void setStartParams() {
		this.params.put("flow.id", this.startFlowId);
		this.params.put("flow.nodeName", this.activeNodeName);
		this.params.put("flow.nodeTitle", getTitle(this.activeNode));
		this.params.put("flow.startDate", this.actionDate);
		this.params.put("flow.userId", this.userId);
		this.params.put("flow.userName", this.userName);
		this.params.put("flow.userDispName", this.userDispName);
		this.params.put("flow.flowName", getFlowAttribute("flowName"));
		this.params.put("flow.action", "pass");

		int j = this.nodes.length();
		for (int i = 0; i < j; i++) {
			JSONObject node = this.nodes.getJSONObject(i);
			node.put("doUserCount", 0);
		}

		this.flowJson.put("rawLinks", this.links.toString());
	}

	private void setUserVar() {
		if (this.request == null) {
			this.userId = "-";
			this.userName = "-";
			this.userDispName = "-";
		} else {
			HttpSession session = this.request.getSession(false);
			if (session == null)
				return;
			this.userId = ((String) session.getAttribute("sys.user"));
			this.userName = ((String) session.getAttribute("sys.username"));
			this.userDispName = ((String) session.getAttribute("sys.dispname"));
		}
	}

	public JSONObject getParams() {
		return this.params;
	}

	public void forward() throws Exception {
		checkEnd();
		String passUsers = getAttribute(this.activeNode, "passUsers");
		int doUserCount = this.activeNode.getInt("doUserCount");


		recordHistory("pass");
		this.activeNode.put("doUserCount", ++doUserCount);
		if (!StringUtil.isEmpty(passUsers)) {
			boolean isPercent = passUsers.endsWith("%");
			String passValue;
			if (isPercent)
				passValue = passUsers.substring(0, passUsers.length() - 1);
			else
				passValue = passUsers;
			int countValue;
			if (StringUtil.isInteger(passValue))
				countValue = Integer.parseInt(passValue);
			else
				throw new RuntimeException("节点“" + this.activeNodeName + "”的通过人数属性值“" + passUsers + "”无效。");
			if (isPercent) {
				int totalUserCount = this.activeNode.getInt("totalUserCount");

				if (doUserCount * 100.0D / totalUserCount >= countValue) {

				}
			} else if (doUserCount < countValue) {
				return;
			}
			this.activeNode.put("doUserCount", 0);
		}
		//动态传参，判断是否执行
		Object isPassObj = JsonUtil.opt(this.params, "isPass");
		if (!this.activeNodeName.equals(startName)){
			if (isPassObj != null) {
				if (isPassObj instanceof Boolean) {
					boolean isPass = ((Boolean) isPassObj).booleanValue();
					int totalUserCount = this.activeNode.getInt("totalUserCount");
					if (!isPass && (doUserCount < totalUserCount)) {
						return;
					}
				} else {
					throw new RuntimeException("节点“" + this.activeNodeName + "”的 isPass 值“" + isPassObj + "”无效。");
				}
			}
	    }
		doForward(new HashMap<String, Integer>());
	}

	public void forward(String approvalOpinion) throws Exception {
		checkEnd();
		String passUsers = getAttribute(this.activeNode, "passUsers");
		int doUserCount = this.activeNode.getInt("doUserCount");


		recordHistory("pass",approvalOpinion);
		this.activeNode.put("doUserCount", ++doUserCount);
		if (!StringUtil.isEmpty(passUsers)) {
			boolean isPercent = passUsers.endsWith("%");
			String passValue;
			if (isPercent)
				passValue = passUsers.substring(0, passUsers.length() - 1);
			else
				passValue = passUsers;
			int countValue;
			if (StringUtil.isInteger(passValue))
				countValue = Integer.parseInt(passValue);
			else
				throw new RuntimeException("节点“" + this.activeNodeName + "”的通过人数属性值“" + passUsers + "”无效。");
			if (isPercent) {
				int totalUserCount = this.activeNode.getInt("totalUserCount");

				if (doUserCount * 100.0D / totalUserCount >= countValue) {

				}
			} else if (doUserCount < countValue) {
				return;
			}
			this.activeNode.put("doUserCount", 0);
		}
		//动态传参，判断是否执行
		Object isPassObj = JsonUtil.opt(this.params, "isPass");
		if (!this.activeNodeName.equals(startName)){
			if (isPassObj != null) {
				if (isPassObj instanceof Boolean) {
					boolean isPass = ((Boolean) isPassObj).booleanValue();
					int totalUserCount = this.activeNode.getInt("totalUserCount");
					if (!isPass && (doUserCount < totalUserCount)) {
						return;
					}
				} else {
					throw new RuntimeException("节点“" + this.activeNodeName + "”的 isPass 值“" + isPassObj + "”无效。");
				}
			}
		}
		doForward(new HashMap<String, Integer>());
	}

	private void doForward(HashMap<String, Integer> forwardNodes) throws Exception {
		int j = this.links.length();

		boolean forwarded = false;
		boolean hasDefaultDialog = !StringUtil.isEmpty(getFlowAttribute("defaultDialog"));

		String fromNodeName = this.activeNodeName;
		for (int i = 0; i < j; i++) {
			JSONObject link = this.links.getJSONObject(i);
			if (fromNodeName.equals(link.optString("from"))) {
				String condition = link.optString("condition");
				if ((StringUtil.isEmpty(condition)) || (ScriptBuffer.evalCondition(condition, this.params))) {
					if (forwarded)
						throw new RuntimeException("节点 “" + fromNodeName + "”可以流转到多个节点。");
					String toNodeName = link.optString("to");
					setActiveNode(toNodeName);
					forwarded = true;

					String dialog = this.activeNode.optString("dialog");
					if ((("-".equals(dialog)) || ((!hasDefaultDialog) && (StringUtil.isEmpty(dialog))))
							&& (!endName.endsWith(toNodeName))) {
						Integer forwardTimes = (Integer) forwardNodes.get(toNodeName);
						if (forwardTimes == null)
							forwardTimes = Integer.valueOf(0);
						forwardTimes = Integer.valueOf(forwardTimes.intValue() + 1);
						if (forwardTimes.intValue() > 10)
							throw new RuntimeException(
									"节点“" + fromNodeName + "”到“" + toNodeName + "”的流转已经多次，请检查是否存在无限循环。");
						forwardNodes.put(toNodeName, forwardTimes);
						doForward(forwardNodes);
					}
				}
			}
		}
		if (!forwarded)
			throw new RuntimeException("没有找到节点 “" + fromNodeName + "”的下一个节点。");
	}

	private void setActiveNode(String nodeName) {
		if (nodeName.equals(this.activeNodeName))
			return;
		this.activeNode = getNode(nodeName);
		this.activeNodeName = nodeName;
		this.flowJson.put("activeNode", nodeName);
		this.params.put("flow.nodeName", nodeName);
		this.params.put("flow.nodeTitle", getTitle(this.activeNode));
		this.nodeChanged = true;
	}

	private HashSet<String> getUsers(Connection conn, boolean isCc) throws Exception {
		String users = replaceParams(this.activeNode.optString(isCc ? "ccUser" : "doUser"));
		HashSet<String> hs = new HashSet<String>();

		String errorMsg = "节点“" + this.activeNodeName + "”无有效处理人员";

		if (StringUtil.isEmpty(users)) {
			if ((!isCc) && (!"结束".equals(this.activeNodeName)))
				throw new IllegalArgumentException(errorMsg);
			return null;
		}
		JSONObject userData = new JSONObject(users);

		JSONArray ja = userData.optJSONArray("userId");
		if (ja != null) {
			JsonUtil.addAll(hs, ja);
		}

		ja = userData.optJSONArray("roleId");
		if (ja != null) {
			int j = ja.length();
			for (int i = 0; i < j; i++) {
				addUsers(conn, hs, "select distinct USER_ID from WB_USER_ROLE where ROLE_ID in ("
						+ StringUtil.joinQuote(JsonUtil.createArray(ja)) + ")");
			}
		}
		ja = userData.optJSONArray("orgId");
		if (ja != null) {
			int j = ja.length();
			for (int i = 0; i < j; i++) {
				addUsers(conn, hs, "select distinct id from base_org where id in ("
						+ StringUtil.joinQuote(JsonUtil.createArray(ja)) + ")");
			}
		}
		ja = userData.optJSONArray("jobId");
		if (ja != null) {
			int j = ja.length();
			for (int i = 0; i < j; i++) {
				addUsers(conn, hs, "select distinct id from base_job where id in ("
						+ StringUtil.joinQuote(JsonUtil.createArray(ja)) + ")");
			}
		}
		ja = userData.optJSONArray("postId");
		if (ja != null) {
			int j = ja.length();
			for (int i = 0; i < j; i++) {
				addUsers(conn, hs, "select distinct id from base_post where id in ("
						+ StringUtil.joinQuote(JsonUtil.createArray(ja)) + ")");
			}
		}
		String module = userData.optString("module");
		if (!StringUtil.isEmpty(module)) {
			JSONObject allParams = this.params;

			if (module.endsWith("}")) {
				int pos = module.lastIndexOf('{');
				String extraParam = module.substring(pos);
				module = module.substring(0, pos);
				allParams = JsonUtil.applyIf(new JSONObject(extraParam), this.params);
			}
			String respText;
			if (this.request == null) {
				respText = WbUtil.run(module, allParams, false);
			} else {
				String jsonresp = WebUtil.fetch(this.request, "_jsonresp");
				this.request.setAttribute("_jsonresp", Integer.valueOf(0));
				respText = Flow.executeModule(module, this.request, allParams, false);
				this.request.setAttribute("_jsonresp", jsonresp);
			}
			if ((!"undefined".equals(respText)) && (!StringUtil.isEmpty(respText))) {
				JsonUtil.addAll(hs, new JSONArray(respText));
			}
		}
		if ((!isCc) && (hs.isEmpty()) && (!"结束".equals(this.activeNodeName)))
			throw new IllegalArgumentException(errorMsg);
		return hs;
	}

	private void addUsers(Connection conn, HashSet<String> users, String sql) throws Exception {
		Statement st = null;
		ResultSet rs = null;
		try {
			st = conn.createStatement();
			rs = st.executeQuery(sql);
			while (rs.next())
				users.add(rs.getString(1));
		} finally {
			DbUtil.close(rs);
			DbUtil.close(st);
		}
	}

	private JSONObject getNode(String nodeName, boolean silent) {
		if (StringUtil.isEmpty(nodeName)) {
			throw new NullPointerException("Node name is empty.");
		}
		int j = this.nodes.length();

		for (int i = 0; i < j; i++) {
			JSONObject node = this.nodes.getJSONObject(i);
			if (nodeName.equals(node.getString("name"))) {
				return node;
			}
		}
		if (silent) {
			return null;
		}
		throw new NullPointerException("Node name \"" + nodeName + "\" is not found.");
	}

	public JSONObject getNode(String nodeName) {
		return getNode(nodeName, false);
	}

	private String replaceParams(String value) {
		return StringUtil.replaceParams(this.params, value);
	}

	public String getAttribute(JSONObject node, String attributeName) {
		return replaceParams(node.optString(attributeName, null));
	}

	public String getFlowAttribute(String attributeName) {
		return replaceParams(this.flowProp.optString(attributeName, null));
	}

	private String getTitle(JSONObject node) {
		String title = getAttribute(node, "nodeTitle");
		return StringUtil.select(new String[] { title, node.getString("name") });
	}

	private Connection getRequestConnection() throws Exception {
		if (this.request == null)
			return null;
		Connection conn = DbUtil.getConnection(this.request);
		if (conn.getAutoCommit())
			conn.setAutoCommit(false);
		return conn;
	}

	public void insert(Connection conn) throws Exception {
		PreparedStatement st = null;
		boolean newConn;
		if (conn == null) {
			newConn = true;
			conn = DbUtil.getConnection();
		} else {
			newConn = false;
		}
		try {
			if (newConn) {
				conn.setAutoCommit(false);
			}

			st = conn.prepareStatement("insert into WB_FLOW_LIST values(?,?,?,?,?,?,?,?,?,?,?,?)");
			st.setString(1, SysUtil.getId());
			st.setString(2, this.flowId);
			st.setString(3, this.activeNodeName);
			st.setString(4, getTitle(this.activeNode));
			st.setTimestamp(5, this.actionDate);
			DbUtil.setObject(st, 6, 12, this.userId);
			DbUtil.setObject(st, 7, 12, this.userDispName);
			st.setTimestamp(8, this.actionDate);
			DbUtil.setObject(st, 9, 12, this.userId);
			DbUtil.setObject(st, 10, 12, this.userDispName);
			st.setString(11, getFlowAttribute("flowName"));
			st.setInt(12, 1);
			st.executeUpdate();
			st.close();

			reloadUsers(conn, null, true);

			st = conn.prepareStatement("insert into WB_FLOW values(?,?)");
			JSONObject flowData = removeException(this.flowJson.toString()) ;
			st.setString(1, this.flowId);
			DbUtil.setText(st, 2, flowData.toString());
			st.executeUpdate();
			st.close();

			if (newConn)
				conn.commit();
		} finally {
			DbUtil.close(st);
			if (newConn)
				DbUtil.close(conn);
		}
	}

	public void viewDone() throws Exception {
		Connection conn = null;
		PreparedStatement st = null;
		try {
			conn = DbUtil.getConnection();
			st = conn.prepareStatement(
					"update WB_FLOW_USER set NEED_PROCESS=0 where FLOW_ID=? and USER_ID=? and IS_CC=1");
			st.setString(1, this.flowId);
			st.setString(2, this.userId);
			st.executeUpdate();
		} finally {
			DbUtil.close(st);
			DbUtil.close(conn);
		}
	}


	public JSONObject updateDraft(Connection conn) throws Exception {
		PreparedStatement st = null;
		PreparedStatement delSt = null;

		JSONObject result = new JSONObject();
		boolean newConn;
		if (conn == null) {
			newConn = true;
			conn = DbUtil.getConnection();
		} else {
			newConn = false;
		}
		int status;
		String title;
		try {
			if (newConn) {
				conn.setAutoCommit(false);
			}

			st = conn.prepareStatement(
					"update WB_FLOW_LIST set NODE_NAME=?,TITLE=?,LAST_MODIFY_DATE=?, LAST_USER_ID=?, LAST_USER_DISP_NAME=?, STATUS=? where FLOW_ID=?");
			st.setString(1, this.activeNodeName);
			title = getTitle(this.activeNode);
			st.setString(2, title);
			st.setTimestamp(3, this.actionDate);
			DbUtil.setObject(st, 4, 12, this.userId);
			DbUtil.setObject(st, 5, 12, this.userDispName);
			status = endName.equals(this.activeNodeName) ? 2 : 1;
			st.setInt(6, status);
			st.setString(7, this.flowId);
			st.executeUpdate();
			st.close();
			if (endName.equals(this.activeNodeName)) { //流程结束后，给发起人发送消息
				JSONObject msgData = new JSONObject();
				msgData.put("flowId", this.flowId);
				MessageSender.send("my_task", Str.format("okMessageTitle", getFlowAttribute("flowName")),
						Str.format("okMessage", getFlowAttribute("flowName")), params.getString("flow.userId"),
						msgData.toString());
			}
			result.put("NODE_NAME", this.activeNodeName);
			result.put("TITLE", title);
			result.put("LAST_MODIFY_DATE", this.actionDate);
			result.put("LAST_USER_ID", this.userId);
			result.put("LAST_USER_DISP_NAME", this.userDispName);
			result.put("STATUS", status);

			st = conn.prepareStatement("update WB_FLOW set FLOW_DATA=? where FLOW_ID=?");
			JSONObject flowData = removeException(this.flowJson.toString()) ;
			DbUtil.setText(st, 1, flowData.toString());
			st.setString(2, this.flowId);
			st.executeUpdate();
			st.close();

			if (newConn)
				conn.commit();
		} finally {
			DbUtil.close(st);
			DbUtil.close(delSt);
			if (newConn)
				DbUtil.close(conn);
		}
		return result;
	}

	public JSONObject update(Connection conn) throws Exception {
		PreparedStatement st = null;
		PreparedStatement delSt = null;

		JSONObject result = new JSONObject();
		boolean newConn;
		if (conn == null) {
			newConn = true;
			conn = DbUtil.getConnection();
		} else {
			newConn = false;
		}
		int status;
		String title;
		try {
			if (newConn) {
				conn.setAutoCommit(false);
			}

			st = conn.prepareStatement(
					"update WB_FLOW_LIST set NODE_NAME=?,TITLE=?,LAST_MODIFY_DATE=?, LAST_USER_ID=?, LAST_USER_DISP_NAME=?, STATUS=? where FLOW_ID=?");
			st.setString(1, this.activeNodeName);
			title = getTitle(this.activeNode);
			st.setString(2, title);
			st.setTimestamp(3, this.actionDate);
			DbUtil.setObject(st, 4, 12, this.userId);
			DbUtil.setObject(st, 5, 12, this.userDispName);
			status = endName.equals(this.activeNodeName) ? 2 : 1;
			st.setInt(6, status);
			st.setString(7, this.flowId);
			st.executeUpdate();
			st.close();
			if (endName.equals(this.activeNodeName)) { //流程结束后，给发起人发送消息
				JSONObject msgData = new JSONObject();
				msgData.put("flowId", this.flowId);
				MessageSender.send("my_task", Str.format("okMessageTitle", getFlowAttribute("flowName")),
						Str.format("okMessage", getFlowAttribute("flowName")), params.getString("flow.userId"),
						msgData.toString());
			}
			result.put("NODE_NAME", this.activeNodeName);
			result.put("TITLE", title);
			result.put("LAST_MODIFY_DATE", this.actionDate);
			result.put("LAST_USER_ID", this.userId);
			result.put("LAST_USER_DISP_NAME", this.userDispName);
			result.put("STATUS", status);

			if (this.setProcessed) {
				st = conn.prepareStatement(
						"update WB_FLOW_USER set NEED_PROCESS=0,IS_PROCESSED=1 where FLOW_ID=? and USER_ID=? and IS_CC=0");
				st.setString(1, this.flowId);
				st.setString(2, this.userId);
				int k = st.executeUpdate();
				st.close();
				if (k != 1)
					throw new RuntimeException("您不是该流程的处理人员。");
				result.put("NEED_PROCESS", 0);
				result.put("IS_PROCESSED", 1);
			}

			if (this.nodeChanged) {
				st = conn.prepareStatement("delete from WB_FLOW_USER where FLOW_ID=? and IS_PROCESSED=0");
				st.setString(1, this.flowId);
				st.executeUpdate();
				st.close();

				st = conn.prepareStatement("update WB_FLOW_USER set NEED_PROCESS=0 where FLOW_ID=?");
				st.setString(1, this.flowId);
				st.executeUpdate();
				st.close();
				reloadUsers(conn, result, false);
			} else {
				int j = this.newUserList.size();
				if (j > 0) {
					delSt = conn.prepareStatement("delete from WB_FLOW_USER where FLOW_ID=? and USER_ID=?");
					delSt.setString(1, this.flowId);
					st = conn.prepareStatement("insert into WB_FLOW_USER values(?,?,?,1,0,0)");
					st.setString(2, this.flowId);
					for (int i = 0; i < j; i++) {
						String newUserId = (String) this.newUserList.get(i);
						if (newUserId.equals(this.userId))
							result.put("NEED_PROCESS", 1);
						delSt.setString(2, newUserId);
						delSt.addBatch();
						st.setString(1, SysUtil.getId());
						st.setString(3, newUserId);
						st.addBatch();
						if (i % 1000 == 999) {
							delSt.executeBatch();
							st.executeBatch();
						}
					}
					delSt.executeBatch();
					st.executeBatch();
					delSt.close();
					st.close();
					//发送待办消息
					if (newUserList.size() > 0)
						sendTodoMsg(StringUtil.join(newUserList, ","));
				}
			}

			st = conn.prepareStatement("update WB_FLOW set FLOW_DATA=? where FLOW_ID=?");
			JSONObject flowData = removeException(this.flowJson.toString()) ;
			DbUtil.setText(st, 1, flowData.toString());
			st.setString(2, this.flowId);
			st.executeUpdate();
			st.close();

			if (newConn)
				conn.commit();
		} finally {
			DbUtil.close(st);
			DbUtil.close(delSt);
			if (newConn)
				DbUtil.close(conn);
		}
		return result;
	}

	public void remove(Connection conn) throws Exception {
		PreparedStatement st = null;
		boolean newConn;
		if (conn == null) {
			newConn = true;
			conn = DbUtil.getConnection();
		} else {
			newConn = false;
		}
		try {
			if (newConn) {
				conn.setAutoCommit(false);
			}
			st = conn.prepareStatement("delete from  WB_FLOW_LIST where FLOW_ID=?");
			st.setString(1, this.flowId);
			st.executeUpdate();
			st.close();

			st = conn.prepareStatement("delete from WB_FLOW_USER where FLOW_ID=?");
			st.setString(1, this.flowId);
			st.executeUpdate();
			st.close();

			st = conn.prepareStatement("delete from WB_FLOW where FLOW_ID=?");
			st.setString(1, this.flowId);
			st.executeUpdate();
			st.close();

			if (newConn)
				conn.commit();
		} finally {
			DbUtil.close(st);
			if (newConn)
				DbUtil.close(conn);
		}
	}

	private void reloadUsers(Connection conn, JSONObject result, boolean isInsert) throws Exception {
		HashSet<String> doUsers = null;
		int index = 1;
		HashSet<String> updateUsers = new HashSet<String>();
		PreparedStatement st1 = null;
		PreparedStatement st2 = null;
		PreparedStatement st3 = null;
		ResultSet rs = null;
		boolean hasResult = result != null;
		boolean needUpdate;
		int i;
		try {
			if (isInsert) {
				needUpdate = false;
			} else {
				st3 = conn.prepareStatement("select USER_ID from WB_FLOW_USER where FLOW_ID=?");
				st3.setString(1, this.flowId);
				rs = st3.executeQuery();
				while (rs.next()) {
					updateUsers.add(rs.getString(1));
				}
				rs.close();
				st3.close();
				needUpdate = !updateUsers.isEmpty();
			}
			st1 = conn.prepareStatement("insert into WB_FLOW_USER values(?,?,?,?,?,?)");
			st1.setString(2, this.flowId);
			if (needUpdate) {
				st2 = conn.prepareStatement(
						"update WB_FLOW_USER set NEED_PROCESS=?,IS_CC=? where FLOW_ID=? and USER_ID=?");
				st2.setString(3, this.flowId);
			}
			//待办用户和抄送用户
			List<String> todoUsers = new ArrayList<>(), ccUsers = new ArrayList<>();
			for (i = 0; i < 2; i++) {
				st1.setInt(6, i);
				st1.setInt(4, i == 0 ? 1 : 0);
				st1.setInt(5, i);
				if (needUpdate) {
					st2.setInt(1, i == 0 ? 1 : 0);
					st2.setInt(2, i);
				}
				HashSet<String> users = getUsers(conn, i != 0);
				if ((isInsert) && (i == 1)) {
					if (users == null)
						users = new HashSet<String>();
					users.add(this.userId);
				}
				if (i == 0)
					doUsers = users;
				else if ((users != null) && (doUsers != null))
					users.removeAll(doUsers);
				if (users != null) {
					if (i == 0) {
						this.activeNode.put("totalUserCount", users.size());
					}
					for (String user : users) {
						if (needUpdate && updateUsers.contains(user)) {
							if (hasResult && user.equals(this.userId)) {
								result.put("NEED_PROCESS", 1);
								result.put("IS_CC", i);
							}
							st2.setString(4, user);
							st2.addBatch();
							if (index % 1000 == 0)
								st2.executeBatch();
						} else {
							st1.setString(1, SysUtil.getId());
							st1.setString(3, user);
							st1.addBatch();
							if (index % 1000 == 0)
								st1.executeBatch();
						}
						if (i == 1)
							ccUsers.add(user);
						else
							todoUsers.add(user);
						index++;
					}
				}
			}
			st1.executeBatch();
			if (needUpdate)
				st2.executeBatch();
			//发送待办消息
			if (todoUsers.size() > 0)
				sendTodoMsg(StringUtil.join(todoUsers, ","));
			//发送抄送消息
			if (ccUsers.size() > 0)
				sendCCMsg(StringUtil.join(ccUsers, ","));
		} finally {
			DbUtil.close(rs);
			DbUtil.close(st1);
			DbUtil.close(st2);
			DbUtil.close(st3);
		}
	}

	private void checkEnd() {
		if (endName.equals(this.activeNodeName))
			throw new RuntimeException("该流程已经结束。");
	}

	public void insert() throws Exception {
		insert(getRequestConnection());
	}

	public JSONObject update() throws Exception {
		return update(getRequestConnection());
	}

	public JSONObject updateDraft() throws Exception {
		return updateDraft(getRequestConnection());
	}

	public JSONObject getDialog() throws Exception {
		String dialog = getAttribute(this.activeNode, "dialog");
		if (StringUtil.isEmpty(dialog)) {
			dialog = getFlowAttribute("defaultDialog");
			if (StringUtil.isEmpty(dialog))
				return null;
		} else if ("-".equals(dialog)) {
			return null;
		}
		JSONObject result = new JSONObject();
		JSONArray selActions = new JSONArray();
		JSONArray allActions = JsonUtil.getArray(getAttribute(this.activeNode, "actionType"));

		if (allActions != null) {
			int j = allActions.length();
			for (int i = 0; i < j; i++) {
				JSONObject action = allActions.getJSONObject(i);
				String iconCls = action.optString("iconCls");
				if (!iconCls.isEmpty())
					action.put("iconCls", iconCls + "_icon");
				if (action.getInt("selected") == 1) {
					action.remove("selected");
					selActions.put(action);
				}
			}
		}
		result.put("module", Flow.executeModule(dialog, this.request, this.params, true));
		result.put("path", dialog);
		Integer minWidth = (Integer) JsonUtil.opt(this.activeNode, "minWidth");
		if (minWidth == null)
			minWidth = (Integer) JsonUtil.opt(this.flowProp, "defaultMinWidth");
		if (minWidth != null)
			result.put("minWidth", minWidth);
		result.put("actions", selActions);
		result.put("flowParams", this.params);
		result.put("history", this.history);
		return result;
	}

	public JSONObject getViewDialog() throws Exception {
		String dialog = getAttribute(this.activeNode, "viewDialog");
		if (StringUtil.isEmpty(dialog)) {
			dialog = getFlowAttribute("defaultViewDialog");
			if (StringUtil.isEmpty(dialog))
				return null;
		} else if ("-".equals(dialog)) {
			return null;
		}
		JSONObject result = new JSONObject();

		result.put("module", Flow.executeModule(dialog, this.request, this.params, true));
		result.put("path", dialog);
		result.put("flowParams", this.params);
		result.put("history", this.history);
		return result;
	}

	public void checkActiveNode(String nodeName) {
		if (!this.activeNodeName.equals(nodeName))
			throw new IllegalArgumentException("流程当前节点不是“" + nodeName + "”。");
	}

	public void reject(String itemId,String approvalOpinion) {
		JSONObject item = JsonUtil.findObject(this.history, "itemId", itemId);
		if (item == null)
			throw new RuntimeException("无法退回到指定节点，因为该节点不存在。");
		String nodeName = item.getString("node");
		if (!inHistory(nodeName))
			throw new RuntimeException("无法退回到指定节点，因为该节点不在处理历史记录中。");
		if (nodeName.equals(this.activeNodeName))
			throw new RuntimeException("无法退回到当前节点。");
		recordHistory("reject",approvalOpinion);
		setActiveNode(nodeName);
	}

	private void recordHistory(String actionName) {
		JSONObject item = new JSONObject();

		item.put("itemId", SysUtil.getId());
		item.put("date", this.actionDate);
		item.put("userId", this.userId);
		item.put("userName", this.userName);
		item.put("userDispName", this.userDispName);
		item.put("node", this.activeNodeName);
		item.put("title", getTitle(this.activeNode));
		item.put("action", actionName);
		this.history.put(item);
		this.params.put("flow.action", actionName);
	}

	private void recordHistory(String actionName,String approvalOpinion) {
		JSONObject item = new JSONObject();

		item.put("itemId", SysUtil.getId());
		item.put("date", this.actionDate);
		item.put("userId", this.userId);
		item.put("userName", this.userName);
		item.put("userDispName", this.userDispName);
		item.put("node", this.activeNodeName);
		item.put("title", getTitle(this.activeNode));
		//加入审批意见
		if(!StringUtil.isEmpty(approvalOpinion)){
			item.put("approval_opinion", approvalOpinion);
		}
		item.put("action", actionName);
		this.history.put(item);
		this.params.put("flow.action", actionName);
	}

	private boolean inHistory(String nodeName) {
		int j = this.history.length();

		for (int i = 0; i < j; i++) {
			if (nodeName.equals(this.history.getJSONObject(i).getString("node")))
				return true;
		}
		return false;
	}

	private void insertNode(String newNodeName, String actionText, String nodeTitle, String passUsers,
			JSONObject userData, boolean insertBefore) {
		JSONObject node = getNode(newNodeName, true);

		if (node != null) {
			throw new RuntimeException("节点名称“" + newNodeName + "”已经存在，请重新指定一个节点名称。");
		}
		node = new JSONObject();
		this.nodes.put(node);
		JSONArray actions = new JSONArray();
		JSONObject action = new JSONObject();
		action.put("selected", 1);
		action.put("name", "pass");
		action.put("text", actionText);
		action.put("iconCls", "ok");
		actions.put(action);
		action = new JSONObject();
		action.put("selected", 1);
		action.put("name", "cancel");
		action.put("text", Str.format(this.request, "cancel", new Object[0]));
		action.put("iconCls", "cancel");
		actions.put(action);
		node.put("actionType", actions);
		node.put("dialog", getAttribute(this.activeNode, "dialog"));
		node.put("doUser", userData);
		node.put("name", newNodeName);
		node.put("nodeTitle", nodeTitle);
		node.put("passUsers", passUsers);
		node.put("doUserCount", 0);

		insertLink(newNodeName, insertBefore);
	}

	private void insertLink(String newNodeName, boolean insertBefore) {
		int j = this.links.length();

		for (int i = 0; i < j; i++) {
			JSONObject link = this.links.getJSONObject(i);
			if (insertBefore) {
				if (this.activeNodeName.equals(link.getString("to")))
					link.put("to", newNodeName);
			} else if (this.activeNodeName.equals(link.getString("from"))) {
				link.put("from", newNodeName);
			}
		}
		JSONObject link = new JSONObject();
		this.links.put(link);
		if (insertBefore) {
			link.put("from", newNodeName);
			link.put("to", this.activeNodeName);
		} else {
			link.put("from", this.activeNodeName);
			link.put("to", newNodeName);
		}
	}

	public void beforeSign(String newNodeName, String passName, String nodeTitle, String passUsers,
			JSONObject userData) {
		recordHistory("beforeSign");
		insertNode(newNodeName, passName, nodeTitle, passUsers, userData, true);
		setActiveNode(newNodeName);
	}

	public void afterSign(String newNodeName, String passName, String nodeTitle, String passUsers,
			JSONObject userData) {
		recordHistory("afterSign");
		this.setProcessed = false;
		insertNode(newNodeName, passName, nodeTitle, passUsers, userData, false);
	}

	private void addNewUsers(JSONObject userData) {
		JSONArray newUsers = userData.getJSONArray("userId");
		JsonUtil.addAll(this.newUserList, newUsers);
		this.activeNode.put("totalUserCount", this.activeNode.optInt("totalUserCount", 0) + newUsers.length());
	}

	public void plusSign(JSONObject userData) {
		recordHistory("plusSign");
		this.setProcessed = false;
		addNewUsers(userData);
	}

	public void turn(JSONObject userData,String approvalOpinion) {
		recordHistory("turn",approvalOpinion);
		addNewUsers(userData);
	}

	public void draft() {
		setActiveNode(this.activeNodeName);
	}


	/**
	 * 发送待办消息。
	 * 
	 * @param userIds 消息接收人ID，逗号分割
	 */
	private void sendTodoMsg(String userIds) {
		String content = Str.format("todoMessage", this.userDispName, getFlowAttribute("flowName"),
				getTitle(this.activeNode));
		String title = Str.format("todoMessageTitle", getFlowAttribute("flowName"));

		JSONObject msgData = new JSONObject(), params = new JSONObject();
		msgData.put("flowId", this.flowId);
		params.put("flowName", getFlowAttribute("flowName"));
		params.put("startTime", this.actionDate);
		params.put("flowRemark", content);
		msgData.put("temparms", params); //小程序消息需要的模版参数

		MessageSender.send("my_task", title, content, userIds, msgData.toString());
	}

	/**
	 * 发送抄送消息。
	 * 
	 * @param userIds 消息接收人ID，逗号分割
	 */
	private void sendCCMsg(String userIds) {
		JSONObject msgData = new JSONObject();
		msgData.put("flowId", this.flowId);
		MessageSender.send("my_task", Str.format("ccMessageTitle", getFlowAttribute("flowName")),
				Str.format("ccMessage", this.userDispName, getFlowAttribute("flowName"), getTitle(this.activeNode)),
				userIds, msgData.toString());
	}

	/**
	 * 移出例外的参数，不存入 FLOW_DATA
	 * @param json
	 * @return
	 */
	private JSONObject removeException(String json){
		JSONObject jsonObject = JsonUtil.getObject(json) ;
		JSONObject params  = (JSONObject) JsonUtil.opt(jsonObject,"params");
		if(params != null){
			Object obj = JsonUtil.opt(params,"exception") ;
			if(obj != null){
				if(obj instanceof JSONArray){
					JSONArray array = (JSONArray)obj ;
					for(Object key : array){
						params.remove((String)key);
					}
				}
			}
		}
		return jsonObject ;
	}
}