package com.wb.exception;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

public class ErrorHandlingServlet extends HttpServlet {
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processError(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processError(request, response);
    }

    private void processError(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 获取错误码和错误信息
        Integer statusCode = (Integer) request.getAttribute("javax.servlet.error.status_code");
        Throwable throwable = (Throwable) request.getAttribute("javax.servlet.error.exception");
        String errorMessage = (String) request.getAttribute("javax.servlet.error.message");
        // 设置响应内容类型
        response.setContentType("text/html;charset=utf-8");
        PrintWriter out = response.getWriter();
        out.write("<html>\n" +
                "<head>\n" +
                "  <meta charset=\"UTF-8\">\n" +
                "  <meta name=\"viewport\" content=\"width=device-width,initial-scale=1\">\n" +
                "  <title>好像遇到了一个问题</title>\n" +
                "\n" +
                "  <style type=\"text/css\">\n" +
                "    body {\n" +
                "      padding: 30px 20px;\n" +
                "      font-family: -apple-system, BlinkMacSystemFont,\n" +
                "        \"Segoe UI\", \"Roboto\", \"Oxygen\", \"Ubuntu\", \"Cantarell\",\n" +
                "        \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\", sans-serif;\n" +
                "      color: #727272;\n" +
                "      line-height: 1.6;\n" +
                "    }\n" +
                "\n" +
                "    .container {\n" +
                "      max-width: 500px;\n" +
                "      margin: 0 auto;\n" +
                "    }\n" +
                "\n" +
                "    h1 {\n" +
                "      margin: 10 0 40px;\n" +
                "      font-size: 30px;\n" +
                "      line-height: 1;\n" +
                "      color: #252427;\n" +
                "      font-weight: 700;\n" +
                "    }\n" +
                "\n" +
                "    h2 {\n" +
                "      margin: 100px 0 0;\n" +
                "      font-size: 20px;\n" +
                "      font-weight: 600;\n" +
                "      letter-spacing: 0.1em;\n" +
                "      color: #A299AC;\n" +
                "      text-transform: uppercase;\n" +
                "    }\n" +
                "\n" +
                "    p {\n" +
                "      font-size: 16px;\n" +
                "      margin: 1em 0;\n" +
                "    }\n" +
                "\n" +
                "    .go-back a {\n" +
                "      display: inline-block;\n" +
                "      margin-top: 3em;\n" +
                "      padding: 10px;\n" +
                "      color: #1B1A1A;\n" +
                "      font-weight: 700;\n" +
                "      border: solid 2px #e7e7e7;\n" +
                "      text-decoration: none;\n" +
                "      font-size: 16px;\n" +
                "      text-transform: uppercase;\n" +
                "      letter-spacing: 0.1em;\n" +
                "    }\n" +
                "\n" +
                "    .go-back a:hover {\n" +
                "      border-color: #1B1A1A;\n" +
                "    }\n" +
                "\n" +
                "    @media screen and (min-width: 768px) {\n" +
                "      body {\n" +
                "        padding: 50px;\n" +
                "      }\n" +
                "    }\n" +
                "\n" +
                "    @media screen and (max-width: 480px) {\n" +
                "      h1 {\n" +
                "        font-size: 48px;\n" +
                "      }\n" +
                "    }\n" +
                "  </style>\n" +
                "</head>\n" +
                "<body><div class=\"container\">\n" +
                "    <h2>" + statusCode + "</h2>\n" +
                "    <h1>以下信息可以通知管理人员：</h1>\n" +
                "    <p>" + errorMessage + "</p>\n" +
                "    <span class=\"go-back\"><a href=\"/\">返回</a></span>\n" +
                "  </div>");
        out.write("</body></html>");
    }
}

