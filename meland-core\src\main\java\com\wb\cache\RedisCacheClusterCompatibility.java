package com.wb.cache;

import com.wb.util.LogUtil;

/**
 * Redis集群模式兼容性工具类
 * 用于处理阿里云Redis集群代理模式下的特殊命令限制
 */
public class RedisCacheClusterCompatibility {

    /**
     * 检查Redis集群模式下是否支持HASH_TAG
     * 在集群环境中，可以使用Hash Tag来确保多个Key分配到同一个槽位
     * 
     * 例如：{user}profile 和 {user}address 都会被分配到同一个槽位
     * 
     * @param key 原始键
     * @return 添加了Hash Tag的键，如果已经有Hash Tag则直接返回原键
     */
    public static String ensureHashTag(String key) {
        // 如果键已经包含Hash Tag则直接返回
        if (key.contains("{") && key.contains("}")) {
            return key;
        }
        
        // 添加一个Hash Tag前缀，确保相关键落在同一槽位
        // 使用键的前缀部分作为Hash Tag
        int prefixEndIndex = key.indexOf(':');
        if (prefixEndIndex > 0) {
            String prefix = key.substring(0, prefixEndIndex);
            return "{" + prefix + "}" + key;
        }
        
        // 如果没有合适的前缀，则使用整个键作为Hash Tag
        return "{" + key + "}";
    }
    
    /**
     * 为相关的多个键创建一致的Hash Tag，确保它们在Redis集群中分配到同一个槽位
     * 
     * @param keys 相关的多个键
     * @return 添加了一致Hash Tag的键数组
     */
    public static String[] createConsistentHashTags(String... keys) {
        if (keys == null || keys.length == 0) {
            return new String[0];
        }
        
        // 如果只有一个键，直接使用ensureHashTag方法
        if (keys.length == 1) {
            return new String[]{ensureHashTag(keys[0])};
        }
        
        // 寻找所有键共同的前缀作为Hash Tag
        String commonPrefix = findCommonPrefix(keys);
        String hashTag = commonPrefix.isEmpty() ? "common" : commonPrefix;
        
        // 为所有键添加相同的Hash Tag
        String[] result = new String[keys.length];
        for (int i = 0; i < keys.length; i++) {
            result[i] = "{" + hashTag + "}" + keys[i];
        }
        
        return result;
    }
    
    /**
     * 查找字符串数组中的共同前缀
     * 
     * @param strings 字符串数组
     * @return 共同前缀，如果没有则返回空字符串
     */
    private static String findCommonPrefix(String[] strings) {
        if (strings == null || strings.length == 0) {
            return "";
        }
        
        // 以第一个字符串作为基准
        String first = strings[0];
        if (first.isEmpty()) {
            return "";
        }
        
        // 查找第一个冒号之前的部分作为前缀
        int colonIndex = first.indexOf(':');
        if (colonIndex > 0) {
            String prefix = first.substring(0, colonIndex);
            
            // 检查其他字符串是否都具有相同的前缀
            for (int i = 1; i < strings.length; i++) {
                if (!strings[i].startsWith(prefix)) {
                    return "";
                }
            }
            
            return prefix;
        }
        
        return "";
    }
    
    /**
     * 记录集群模式下不兼容的命令使用
     * 
     * @param command 命令名称
     * @param key 相关的键
     */
    public static void logIncompatibleCommand(String command, String key) {
        LogUtil.warn("Redis集群模式下不完全兼容的命令: " + command + " [key=" + key + "]");
    }
    
    /**
     * 检查一组键是否对于集群模式友好
     * 在集群模式下，多个键操作要求键在同一个槽位
     * 
     * @param operation 操作名称
     * @param keys 要检查的键集合
     * @return 如果所有键都有一致的Hash Tag，则返回true；否则返回false
     */
    public static boolean isClusterFriendly(String operation, String... keys) {
        if (keys == null || keys.length <= 1) {
            return true;
        }
        
        // 检查是否所有键都包含相同的Hash Tag
        String firstHashTag = extractHashTag(keys[0]);
        if (firstHashTag == null) {
            LogUtil.warn("Redis集群模式下的多键操作 " + operation + " 未使用一致的Hash Tag");
            return false;
        }
        
        for (int i = 1; i < keys.length; i++) {
            String currentHashTag = extractHashTag(keys[i]);
            if (currentHashTag == null || !currentHashTag.equals(firstHashTag)) {
                LogUtil.warn("Redis集群模式下的多键操作 " + operation + " 未使用一致的Hash Tag");
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 从键中提取Hash Tag
     * 
     * @param key 键
     * @return Hash Tag，如果没有则返回null
     */
    private static String extractHashTag(String key) {
        if (key == null) {
            return null;
        }
        
        int openBraceIndex = key.indexOf('{');
        int closeBraceIndex = key.indexOf('}', openBraceIndex + 1);
        
        if (openBraceIndex >= 0 && closeBraceIndex > openBraceIndex) {
            return key.substring(openBraceIndex + 1, closeBraceIndex);
        }
        
        return null;
    }
} 