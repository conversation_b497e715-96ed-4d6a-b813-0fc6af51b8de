#!/bin/sh

# ==================== 系统级调优 ====================
# 注意：某些设置在非特权容器中可能无法执行，会自动跳过
# 检测是否在容器中运行
if [ -f /.dockerenv ] || grep -q 'docker' /proc/1/cgroup; then
    echo "容器环境优化模式启动..."
    # 容器专用参数
    echo 1 > /proc/sys/net/ipv4/tcp_tw_recycle 2>/dev/null || true
fi

# 增加文件描述符限制 - 测试环境设置较保守值
if [ -w /proc/sys/fs/file-max ]; then
    echo 262144 > /proc/sys/fs/file-max
    echo "系统级调优: 增加最大文件描述符数量"
fi

# 设置进程限制 (ulimit)
ulimit -n 32768 2>/dev/null || echo "无法设置文件描述符限制，可能需要特权模式"
ulimit -c unlimited 2>/dev/null || echo "无法设置core dump限制"

# 网络参数优化 - 适用于高并发Web应用 - 测试环境版本
if [ -w /proc/sys/net/ipv4/tcp_fin_timeout ]; then
    # 减少TCP FIN超时时间
    echo 15 > /proc/sys/net/ipv4/tcp_fin_timeout
    
    # SAE负载均衡环境中的TIME_WAIT处理策略
    # 禁用tcp_tw_recycle（在负载均衡环境中会导致问题）
    echo 0 > /proc/sys/net/ipv4/tcp_tw_recycle 2>/dev/null || true
    
    # 在SAE负载均衡环境中，保守设置tw_reuse
    if [ -f /.dockerenv ] && [ -n "$DISABLE_TCP_TW_REUSE" ]; then
        # 如果显式设置了禁用标志，则关闭重用
        echo 0 > /proc/sys/net/ipv4/tcp_tw_reuse
        echo "SAE环境: 已禁用tcp_tw_reuse，避免负载均衡冲突"
    else
        # 否则启用，但增加安全配置
        echo 1 > /proc/sys/net/ipv4/tcp_tw_reuse
        # 增加timestamp更新频率，减少序列号重叠风险
        echo 1 > /proc/sys/net/ipv4/tcp_timestamps 2>/dev/null || true
        echo "SAE环境: 已启用tcp_tw_reuse，配合timestamps减少风险"
    fi
    
    # 增加本地端口范围 - 降低TIME_WAIT端口耗尽风险
    echo "1024 65535" > /proc/sys/net/ipv4/ip_local_port_range
    
    # 优化TIME_WAIT连接数量 - 测试环境使用较小值
    echo 131072 > /proc/sys/net/ipv4/tcp_max_tw_buckets 2>/dev/null || true
    
    # SYN洪水保护
    echo 1 > /proc/sys/net/ipv4/tcp_syncookies
    
    # TCP快速打开 - 在负载均衡环境中保守使用
    echo 1 > /proc/sys/net/ipv4/tcp_fastopen
    
    # 增加TCP最大syn队列长度
    echo 4096 > /proc/sys/net/ipv4/tcp_max_syn_backlog
    
    # 设置TCP读/写缓冲区大小 - 测试环境使用较小值
    echo 8388608 > /proc/sys/net/core/rmem_max
    echo 8388608 > /proc/sys/net/core/wmem_max
    
    # 增加最大连接队列长度 - 应对高并发 - 测试环境使用较小值
    echo 32768 > /proc/sys/net/core/somaxconn 2>/dev/null || true
    
    echo "系统级调优: 优化TCP网络参数 (SAE测试环境优化)"
fi

# 内存管理优化 - 测试环境
if [ -w /proc/sys/vm/swappiness ]; then
    # 测试环境允许更多的swap使用，减少OOM风险
    echo 20 > /proc/sys/vm/swappiness
    
    # 增加文件系统缓存回收效率 - 适合测试环境
    echo 100 > /proc/sys/vm/vfs_cache_pressure  # 平衡缓存保留时间
    
    # 增加脏页比例阈值 - 测试环境使用保守值
    echo 15 > /proc/sys/vm/dirty_ratio
    echo 5 > /proc/sys/vm/dirty_background_ratio
    
    echo "系统级调优: 优化内存管理参数 (测试环境)"
fi

# 禁用透明大页面 (Transparent Huge Pages)
if [ -d /sys/kernel/mm/transparent_hugepage ]; then
    echo never > /sys/kernel/mm/transparent_hugepage/enabled 2>/dev/null
    echo never > /sys/kernel/mm/transparent_hugepage/defrag 2>/dev/null
    echo "系统级调优: 禁用透明大页面"
fi

# I/O调度器优化
if [ -w /sys/block/sda/queue/scheduler ]; then
    # 使用截止时间调度器(deadline)提高I/O性能
    # 动态检测设备
    DISK_DEV=$(lsblk -d -no pkname $(df / | awk 'END{print $1}') 2>/dev/null)
    if [ -n "$DISK_DEV" ] && [ -w "/sys/block/${DISK_DEV}/queue/scheduler" ]; then
        echo deadline > /sys/block/${DISK_DEV}/queue/scheduler
        echo "系统级调优: 设置I/O调度器为deadline"
    fi
fi

# 测试环境不启用大页面支持，降低资源占用
# echo 128 > /proc/sys/vm/nr_hugepages 2>/dev/null || echo "无法设置大页面"

# 优化系统进程调度 - 测试环境
if [ -w /proc/sys/kernel/sched_migration_cost_ns ]; then
    # 增加调度程序迁移成本，减少CPU缓存失效
    echo 5000000 > /proc/sys/kernel/sched_migration_cost_ns
    
    # 调整调度器自动组调整间隔
    echo 0 > /proc/sys/kernel/sched_autogroup_enabled 2>/dev/null
    
    echo "系统级调优: 优化进程调度参数"
fi

# Nashorn脚本引擎性能优化 - 内核参数 - 测试环境适配
if [ -w /proc/sys/kernel/numa_balancing ]; then
    # 禁用NUMA平衡，减少脚本执行延迟
    echo 0 > /proc/sys/kernel/numa_balancing
    
    # 优化内核同步延迟 - 测试环境使用保守值
    if [ -w /proc/sys/kernel/sched_min_granularity_ns ]; then
        echo 5000000 > /proc/sys/kernel/sched_min_granularity_ns
        echo 10000000 > /proc/sys/kernel/sched_wakeup_granularity_ns
    fi
    
    echo "系统级调优: 优化NUMA和调度器参数，提升Nashorn脚本性能"
fi

# 文件系统优化 - 提高I/O吞吐量，对脚本加载和动态类生成有益 - 测试环境适配
if [ -w /proc/sys/fs/inotify/max_user_watches ]; then
    # 增加inotify监视限制 - 测试环境适用较小值
    echo 131072 > /proc/sys/fs/inotify/max_user_watches
    
    # 增加inotify实例数量限制
    echo 4096 > /proc/sys/fs/inotify/max_user_instances
    
    # 提高目录缓存限制
    echo 32768 > /proc/sys/fs/dir-notify-enable 2>/dev/null
    
    echo "系统级调优: 优化文件系统参数，提升类加载性能"
fi

echo "===== 系统调优设置完成，部分设置可能需要特权容器才能生效 ====="
# ==================== 系统调优结束 ====================

# 兼容cgroups v1和v2进行容器内存检测
if [ -f /sys/fs/cgroup/memory/memory.limit_in_bytes ]; then
    # cgroups v1 检测逻辑
    CONTAINER_MEM_LIMIT=$(cat /sys/fs/cgroup/memory/memory.limit_in_bytes)
    if [ "${CONTAINER_MEM_LIMIT}" = "9223372036854771712" ] || [ ${CONTAINER_MEM_LIMIT} -gt 1152921504606846976 ]; then
        # 处理无限制或超大值情况（1152921504606846976 = 2^60）
        AVAILABLE_MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
        AVAILABLE_MEMORY_MB=$(( AVAILABLE_MEMORY_KB / 1024 ))
        echo "检测到cgroups v1环境，容器无内存限制，使用系统内存: ${AVAILABLE_MEMORY_MB}MB"
    else
        # 正常容器限制情况
        AVAILABLE_MEMORY_MB=$(( CONTAINER_MEM_LIMIT / 1024 / 1024 ))
        echo "检测到cgroups v1环境，容器内存限制: ${AVAILABLE_MEMORY_MB}MB"
    fi
elif [ -f /sys/fs/cgroup/memory.max ]; then
    # cgroups v2 检测逻辑
    CONTAINER_MEM_LIMIT=$(cat /sys/fs/cgroup/memory.max)
    if [ "${CONTAINER_MEM_LIMIT}" = "max" ] || [ "${CONTAINER_MEM_LIMIT}" = "9223372036854771712" ]; then
        # 处理无限制情况
        AVAILABLE_MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
        AVAILABLE_MEMORY_MB=$(( AVAILABLE_MEMORY_KB / 1024 ))
        echo "检测到cgroups v2环境，容器无内存限制，使用系统内存: ${AVAILABLE_MEMORY_MB}MB"
    else
        # 正常容器限制情况
        AVAILABLE_MEMORY_MB=$(( CONTAINER_MEM_LIMIT / 1024 / 1024 ))
        echo "检测到cgroups v2环境，容器内存限制: ${AVAILABLE_MEMORY_MB}MB"
    fi
else
    # 回退到/proc/meminfo
    AVAILABLE_MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    AVAILABLE_MEMORY_MB=$(( AVAILABLE_MEMORY_KB / 1024 ))
    echo "未检测到容器环境，使用系统内存: ${AVAILABLE_MEMORY_MB}MB"
fi

# 容器CPU配额检测 - 确保在容器环境中获取正确的CPU限制
if [ -f /sys/fs/cgroup/cpu/cpu.shares ] || [ -f /sys/fs/cgroup/cpu.max ]; then
    # 尝试获取CPU配额限制
    if [ -f /sys/fs/cgroup/cpu/cpu.cfs_quota_us ] && [ -f /sys/fs/cgroup/cpu/cpu.cfs_period_us ]; then
        # cgroups v1
        CPU_QUOTA=$(cat /sys/fs/cgroup/cpu/cpu.cfs_quota_us)
        CPU_PERIOD=$(cat /sys/fs/cgroup/cpu/cpu.cfs_period_us)
        if [ "${CPU_QUOTA}" != "-1" ]; then
            CONTAINER_CPU_COUNT=$(( (CPU_QUOTA + CPU_PERIOD - 1) / CPU_PERIOD ))
            echo "检测到cgroups v1 CPU限制: ${CONTAINER_CPU_COUNT}个核心"
            PROCESSOR_COUNT=${CONTAINER_CPU_COUNT}
        fi
    elif [ -f /sys/fs/cgroup/cpu.max ]; then
        # cgroups v2
        CPU_MAX=$(cat /sys/fs/cgroup/cpu.max)
        CPU_QUOTA=$(echo "${CPU_MAX}" | cut -d' ' -f1)
        CPU_PERIOD=$(echo "${CPU_MAX}" | cut -d' ' -f2)
        if [ "${CPU_QUOTA}" != "max" ]; then
            CONTAINER_CPU_COUNT=$(( (CPU_QUOTA + CPU_PERIOD - 1) / CPU_PERIOD ))
            echo "检测到cgroups v2 CPU限制: ${CONTAINER_CPU_COUNT}个核心"
            PROCESSOR_COUNT=${CONTAINER_CPU_COUNT}
        fi
    fi
fi

# 仍需检测实际处理器核心数，作为后备方案
if [ -z "${PROCESSOR_COUNT}" ]; then
    PROCESSOR_COUNT=$(grep -c ^processor /proc/cpuinfo)
    echo "未检测到CPU限制，使用实际处理器核心数: ${PROCESSOR_COUNT}"
fi

# 系统资源自动感知（适配SAE弹性资源）
# 测试环境内存占用率设置为60%，为系统和其他进程留出充足内存
DEFAULT_HEAP_SIZE=$((AVAILABLE_MEMORY_MB * 60 / 100))
echo "设置默认堆内存大小: ${DEFAULT_HEAP_SIZE}MB，处理器核心数: ${PROCESSOR_COUNT}"

# JVM 堆大小设置
export CATALINA_OPTS="$CATALINA_OPTS -Xms${JVM_XMS:-${DEFAULT_HEAP_SIZE}m}"
export CATALINA_OPTS="$CATALINA_OPTS -Xmx${JVM_XMX:-${DEFAULT_HEAP_SIZE}m}"

# 线程栈大小调整 - 增大以解决栈溢出
export CATALINA_OPTS="$CATALINA_OPTS -Xss1m"

# 元空间优化 - 适配4u8g环境并兼容ARMS代理
export CATALINA_OPTS="$CATALINA_OPTS -XX:MetaspaceSize=96m -XX:MaxMetaspaceSize=320m"
export CATALINA_OPTS="$CATALINA_OPTS -XX:CompressedClassSpaceSize=128m"

# JIT优化 - 适配4u8g环境
export CATALINA_OPTS="$CATALINA_OPTS -XX:InitialCodeCacheSize=128m -XX:ReservedCodeCacheSize=256m"
export CATALINA_OPTS="$CATALINA_OPTS -XX:CompileThreshold=5000 -XX:+TieredCompilation"

# Nashorn专用
export CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.compiler.deoptimizeThreshold=-1"
# Nashorn代码生成优化
export CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.codegen.maxmethod.size=65535"
export CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.compiler.recursion.limit=1000"
export CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.classloader.loadersource=parent"
export CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.args.prepend=--persistent-code-cache"
export CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.typeInfo.cacheSize=1024"

# 关闭偏向锁：减少多线程环境下的锁撤销开销
export CATALINA_OPTS="$CATALINA_OPTS -XX:-UseBiasedLocking"

# JVM 垃圾回收器设置 - 使用CMS (对于4u8g测试环境CMS仍是最佳选择)
if [ -z "$JVM_DISABLE_CMSGC" ]; then
    # 基础配置
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseConcMarkSweepGC"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+CMSClassUnloadingEnabled"
    
    # 停顿优化
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+CMSParallelRemarkEnabled"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+CMSScavengeBeforeRemark"
    
    # 内存碎片防御
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseCMSCompactAtFullCollection"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:CMSFullGCsBeforeCompaction=${JVM_CMS_FULL_GC_BEFORE_COMPACTION:-2}"
    
    # 触发阈值 - 测试环境降低以减少Full GC
    export CATALINA_OPTS="$CATALINA_OPTS -XX:CMSInitiatingOccupancyFraction=${JVM_CMS_INIT_OCCUPANCY:-55}"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseCMSInitiatingOccupancyOnly"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+ExplicitGCInvokesConcurrent"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+ParallelRefProcEnabled"
    
    # 根据4核CPU定制
    export CATALINA_OPTS="$CATALINA_OPTS -XX:ParallelGCThreads=${JVM_PARALLEL_GC_THREADS:-$((PROCESSOR_COUNT / 2 > 1 ? PROCESSOR_COUNT / 2 : 1))}"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:ConcGCThreads=${JVM_CONC_GC_THREADS:-$((PROCESSOR_COUNT / 4 > 1 ? PROCESSOR_COUNT / 4 : 1))}"
    
    # 年轻代优化
    export CATALINA_OPTS="$CATALINA_OPTS -XX:NewRatio=${JVM_NEW_RATIO:-2}"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:SurvivorRatio=${JVM_SURVIVOR_RATIO:-6}"
fi

# 启用字符串去重 - 对脚本生成的重复字符串有显著帮助
if [ -z "$JVM_DISABLE_STRING_DEDUP" ]; then
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseStringDeduplication"
fi

# 禁止显式GC - 默认启用
if [ -z "$JVM_ENABLE_EXPLICIT_GC" ]; then
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+DisableExplicitGC"
fi

# 内存预分配 - 默认启用
if [ -z "$JVM_DISABLE_PRETOUCH" ]; then
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+AlwaysPreTouch"
fi

# 堆转储设置 - 默认启用
if [ -z "$JVM_DISABLE_HEAP_DUMP" ]; then
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:HeapDumpPath=${JVM_HEAP_DUMP_PATH:-/usr/local/tomcat/logs/heapdump.hprof}"
fi

# GC日志配置 - 默认启用基本记录，详细日志需额外开启
export CATALINA_OPTS="$CATALINA_OPTS -XX:+PrintGCDetails"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+PrintGCDateStamps"
export CATALINA_OPTS="$CATALINA_OPTS -Xloggc:${GC_LOG_PATH:-/usr/local/tomcat/logs/gc.log}"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseGCLogFileRotation"
export CATALINA_OPTS="$CATALINA_OPTS -XX:NumberOfGCLogFiles=${JVM_GC_LOG_FILES:-10}"
export CATALINA_OPTS="$CATALINA_OPTS -XX:GCLogFileSize=${JVM_GC_LOG_SIZE:-100M}"

# 更多诊断详情（可选）
if [ ! -z "$JVM_GC_DETAILS" ]; then
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+PrintGCApplicationStoppedTime"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+PrintHeapAtGC"
    export CATALINA_OPTS="$CATALINA_OPTS -XX:+PrintTenuringDistribution"
fi

# JVM 系统属性
export CATALINA_OPTS="$CATALINA_OPTS -Dfile.encoding=${FILE_ENCODING:-UTF-8}"
export CATALINA_OPTS="$CATALINA_OPTS -Duser.timezone=${USER_TIMEZONE:-Asia/Shanghai}"

# 线程控制参数 - 防止线程爆炸
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UnlockDiagnosticVMOptions"
export CATALINA_OPTS="$CATALINA_OPTS -XX:GuaranteedSafepointInterval=60000"
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseCountedLoopSafepoints"

# 测试环境特定优化 - 降低资源占用
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UseGCOverheadLimit"
export CATALINA_OPTS="$CATALINA_OPTS -XX:GCTimeLimit=15"
export CATALINA_OPTS="$CATALINA_OPTS -XX:GCHeapFreeLimit=15"

# 实验性JVM选项支持
export CATALINA_OPTS="$CATALINA_OPTS -XX:+UnlockExperimentalVMOptions"

# 保留JVM安全参数
export CATALINA_OPTS="$CATALINA_OPTS -Djdk.tls.ephemeralDHKeySize=2048"
export CATALINA_OPTS="$CATALINA_OPTS -Djava.security.egd=file:/dev/./urandom"

# 添加ARMS监控兼容性优化
export CATALINA_OPTS="$CATALINA_OPTS -Darms.logger.level=INFO"

# 启动Tomcat
exec catalina.sh run