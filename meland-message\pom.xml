<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>lon<PERSON>sky</groupId>
		<artifactId>meland</artifactId>
		<version>1.0.0</version>
	</parent>
	<artifactId>meland-message</artifactId>
	<version>1.0.0</version>
	<packaging>jar</packaging>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>lon<PERSON>sky</groupId>
				<artifactId>meland-dependencies</artifactId>
				<version>1.0.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>

		<!-- 开放平台相关依赖 -->
		<dependency>
			<groupId>lon<PERSON><PERSON></groupId>
			<artifactId>meland-open-platform</artifactId>
			<version>1.0.0</version>
		</dependency>

		<!-- 阿里云相关包 -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-ecs</artifactId>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
		</dependency>

	</dependencies>

	<!-- 构建相关参数 -->
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.1.0</version>
			</plugin>
		</plugins>
	</build>
</project>
