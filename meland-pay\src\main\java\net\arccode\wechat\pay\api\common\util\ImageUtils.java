package net.arccode.wechat.pay.api.common.util;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;

import javax.imageio.ImageIO;

/**
 * 描述: 图片工具类
 *
 * <AUTHOR>
 * @since 2015-05-28
 */
public class ImageUtils {

	/**
	 * <pre>
	 * Base64编码的字符解码为图片
	 *
	 * </pre>
	 *
	 * @param imageString
	 * @return
	 */
	public static BufferedImage decodeToImage(String imageString) {
		BufferedImage image = null;
		byte[] imageByte;
		try {
			imageByte = Base64.getDecoder().decode(imageString);
			ByteArrayInputStream bis = new ByteArrayInputStream(imageByte);
			image = ImageIO.read(bis);
			bis.close();
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return image;
	}

	/**
	 * <pre>
	 * Base64编码的字符解码为字节数组流
	 *
	 * </pre>
	 *
	 * @param imageString
	 * @return
	 */
	public static ByteArrayInputStream decodeToStream(String imageString) {
		ByteArrayInputStream bis = null;
		byte[] imageByte;
		try {
			imageByte = Base64.getDecoder().decode(imageString);
			bis = new ByteArrayInputStream(imageByte);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return bis;
	}

	/**
	 * <pre>
	 * 图片文件转化为Base64编码字符串
	 *
	 * </pre>
	 *
	 * @param image
	 * @param type
	 * @return
	 */
	public static String encodeToString(BufferedImage image, String type) {
		String imageString = null;
		ByteArrayOutputStream bos = new ByteArrayOutputStream();

		try {
			ImageIO.write(image, type, bos);
			byte[] imageBytes = bos.toByteArray();

			imageString = Base64.getEncoder().encodeToString(imageBytes);

			bos.close();
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		return imageString.replaceAll("\\n", "");
	}
}
