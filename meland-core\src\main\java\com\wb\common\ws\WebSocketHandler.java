package com.wb.common.ws;

import com.wb.common.UserList;
import com.wb.common.Var;
import com.wb.util.LogUtil;
import com.wb.util.SysUtil;
import com.wb.util.WbUtil;
import com.wb.util.WebUtil;
import org.json.JSONObject;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.session.Session;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Map;

/**
 * WebSocket处理器，支持集群环境
 */
@Component
public class WebSocketHandler extends TextWebSocketHandler {

	private String httpSessionId;
	private String userId;
	private String xwl;
	private String name;
	
	// 缓存常用会话属性
	private String[] roles;
	private String dispname;
	private String username;
	private String userLang;

	/**
	 * 处理收到的消息
	 */
	@Override
	protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
		try {
			// 更新最后活动时间
			WebSocketSessionManager.getInstance().updateSessionLastActive(session.getId());
			
			JSONObject params = new JSONObject();
			params.put("data", message.getPayload());
			params.put("xwl", this.xwl);
			params.put("session", session);
			params.put("httpSession", this.httpSessionId);
			
			// 使用缓存的会话属性
			params.put("userId", this.userId);
			
			// 添加已缓存的会话属性
			if (this.roles != null) {
				params.put("roles", this.roles);
			}
			
			if (this.dispname != null) {
				params.put("dispname", this.dispname);
			}
			
			if (this.username != null) {
				params.put("username", this.username);
			}
			
			if (this.userLang != null) {
				params.put("lang", this.userLang);
			}
			
			WebUtil.sendSocketText(session, WbUtil.run(this.xwl, params, true), true, true);
		} catch (Throwable e) {
			showException(e);
			WebUtil.sendSocketText(session, SysUtil.getRootError(e), false, true);
		}
	}

	@Override
	public void afterConnectionEstablished(WebSocketSession session) throws Exception {
		try {
			Map<String, Object> map = session.getAttributes();

			Object patam = map.get("xwl");
			if (patam != null) {
				this.xwl = patam.toString() + ".xwl";
			} else {
				patam = map.get("xwls");
				if (patam != null)
					this.xwl = patam.toString();
				else
					this.xwl = null;
			}
			patam = map.get("name");
			if (patam != null)
				this.name = patam.toString();
			else
				this.name = this.xwl;
				
			// 获取HttpSession ID
			this.httpSessionId = (String) session.getAttributes().get("HTTP_SESSION_ID");
			Session springSession = UserList.getSessionBySessionId(this.httpSessionId);
			
			if (springSession != null) {
				// 缓存所有需要的会话属性
				this.userId = (String) springSession.getAttribute("sys.user");
				this.roles = (String[]) springSession.getAttribute("sys.roles");
				this.dispname = (String) springSession.getAttribute("sys.dispname");
				this.username = (String) springSession.getAttribute("sys.username");
				this.userLang = (String) springSession.getAttribute("sys.lang");
				
				if (WbUtil.canAccess(springSession, this.xwl)) {
					// 在连接建立后，添加到集群WebSocket管理
					SessionBridge.addSession(springSession, session, this.name);
					session.getAttributes().put("xwl", this.xwl);
					
					// 发送连接成功消息
					WebUtil.sendSocketText(session, "{\"cate\":\"system\",\"type\":\"connection\"}", true, false);
				} else {
					session.close(CloseStatus.NOT_ACCEPTABLE
							.withReason("您没有权限访问 \"" + this.xwl + "\"."));
				}
			} else {
				session.close(CloseStatus.NOT_ACCEPTABLE.withReason("会话未找到或已过期"));
			}
		} catch (Throwable e) {
			try {
				session.close(CloseStatus.NOT_ACCEPTABLE.withReason(SysUtil.getRootError(e)));
			} catch (Throwable localThrowable) {
				// 忽略关闭异常
			}
			showException(e);
		}
	}

	@Override
	public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
		if (this.httpSessionId != null) {
			try {
				Session springSession = UserList.getSessionBySessionId(this.httpSessionId);
				// 在连接关闭后，将WebSocketSession从集群管理中移除
				SessionBridge.removeSession(springSession, session, this.name);
			} catch (Throwable localThrowable) {
				// 忽略关闭异常
			}
			if ((Var.autoLogout) && ("sys.home".equals(this.name)))
				UserList.remove("", this.httpSessionId);
			this.httpSessionId = null;
		}
	}
	
	/**
	 * 显示异常信息
	 * 
	 * @param e 异常对象
	 */
	private void showException(Throwable e) {
		if (Var.debug) {
			try {
				StringWriter errors = new StringWriter();
				e.printStackTrace(new PrintWriter(errors));
				LogUtil.error("WebSocket error: " + errors.toString());
			} catch (Throwable localThrowable) {
				// 忽略打印异常
			}
		}
	}
	
	/**
	 * 心跳检测，每30秒执行一次
	 */
	@Scheduled(fixedRate = 30000, initialDelay = 30 * 1000)
	public void sendHeartbeat() {
		if (this.userId != null) {
			try {
				// 使用WebUtil发送心跳，这样会自动包装成符合格式的JSON
				WebUtil.sendSocketTextToUser(this.userId, "{\"cate\":\"system\",\"type\":\"heartbeat\"}", true, false);
			} catch (Exception e) {
				// 记录心跳异常
				LogUtil.error("WebSocket心跳异常: " + e.getMessage());
			}
		}
	}
}
