package com.wb.openplatform.accounts.util;

import com.wb.common.Var;

/**
 * 微信公众号参数
 * 
 * <AUTHOR>
 *
 */
public class AccountParamesUtil {
	// 1.微信公众号参数
	// token
	public static String token = Var.getString("sys.config.accounts.Token");
	// EncodingAESKey
	public static String EncodingAESKey = Var.getString("sys.config.accounts.EncodingAESKey");
	// 微信公众号应用AppId
	public static String appId = Var.getString("sys.config.accounts.AppId");
	// 微信公众号应用密匙
	public static String appSecret = Var.getString("sys.config.accounts.AppSecret");
	// 登录授权类型ID
	public static String grant_Type = Var.getString("sys.config.accounts.Grant_Type");
	// 微信支付商户号
	public static String partner = Var.getString("sys.config.accounts.Partner");
	// 微信支付商户密匙
	public static String partnerKey = Var.getString("sys.config.accounts.PartnerKey");
	// 微信支付交易类型
	public static String trade_Type = Var.getString("sys.config.accounts.Trade_Type");

	public static void init(){
		// token
		token = Var.getString("sys.config.accounts.Token");
		// EncodingAESKey
		EncodingAESKey = Var.getString("sys.config.accounts.EncodingAESKey");
		// 微信公众号应用AppId
		appId = Var.getString("sys.config.accounts.AppId");
		// 微信公众号应用密匙
		appSecret = Var.getString("sys.config.accounts.AppSecret");
		// 登录授权类型ID
		grant_Type = Var.getString("sys.config.accounts.Grant_Type");
		// 微信支付商户号
		partner = Var.getString("sys.config.accounts.Partner");
		// 微信支付商户密匙
		partnerKey = Var.getString("sys.config.accounts.PartnerKey");
		// 微信支付交易类型
		trade_Type = Var.getString("sys.config.accounts.Trade_Type");
		System.err.println("static 被调用了");
	}
}
