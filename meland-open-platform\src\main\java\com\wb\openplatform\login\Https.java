package com.wb.openplatform.login;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.URL;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;

import org.json.JSONArray;
import org.json.JSONObject;

import com.wb.util.StringUtil;

import net.arccode.wechat.pay.api.service.MyX509TrustManager;
import sun.net.www.protocol.https.Handler;

/**
 * 
 * <AUTHOR>
 *
 */
@SuppressWarnings("restriction")
public class Https {

	/**
	 * 1.发起https请求并获取结果
	 * 
	 * @param requestUrl    请求地址
	 * @param requestMethod 请求方式（GET）
	 * @param outputStr     提交的数据
	 * @return JSONObject(通过JSONObject.get(key)的方式获取json对象的属性值)
	 */
	public static JSONObject GetRequest(String requestUrl, JSONArray headers, JSONArray params) {
		JSONObject jsonObject = null;
		String requestMethod = "GET";
		String param = "";
		StringBuffer buffer = new StringBuffer();
		try {
			// 创建SSLContext对象，并使用我们指定的信任管理器初始化
			TrustManager[] tm = { new MyX509TrustManager() };
			SSLContext sslContext = SSLContext.getInstance("SSL", "SunJSSE");
			sslContext.init(null, tm, new java.security.SecureRandom());
			// 从上述SSLContext对象中得到SSLSocketFactory对象
			SSLSocketFactory ssf = sslContext.getSocketFactory();
			// 构建参数
			if (params.length() > 0) {
				for (Object data : params) {
					JSONObject dt = (JSONObject) data;
					if (dt.has("key") && dt.has("value") && dt.get("key").toString() != ""
							&& dt.get("value").toString() != "") {
						String key = dt.get("key").toString();
						String value = dt.get("value").toString();
						if (param != "") {
							param += "&";
						} else {
							param += "?";
						}
						param += StringUtil.format("[{0}]=[{1}]", key, value);
					}
				}
			}

			URL url = new URL(null, requestUrl + param, new Handler());
			HttpsURLConnection httpUrlConn = (HttpsURLConnection) url.openConnection();
			httpUrlConn.setSSLSocketFactory(ssf);

			httpUrlConn.setDoOutput(true);
			httpUrlConn.setDoInput(true);
			httpUrlConn.setUseCaches(false);
			// 协议
			httpUrlConn.setRequestProperty("accept", "*/*");
			httpUrlConn.setRequestProperty("Accept-Charset", "UTF-8");
			httpUrlConn.setRequestProperty("contentType", "UTF-8");
			httpUrlConn.setRequestProperty("Content-Type", "application/json");
			if (headers.length() > 0) {
				for (Object header : headers) {
					JSONObject head = (JSONObject) header;
					if (head.has("key") && head.has("value") && head.get("key").toString() != ""
							&& head.get("value").toString() != "") {
						String key = head.get("key").toString();
						String value = head.get("value").toString();
						httpUrlConn.setRequestProperty(key, value);
					}
				}
			}
			// 设置请求方式（GET/POST）
			httpUrlConn.setRequestMethod(requestMethod);

			if ("GET".equalsIgnoreCase(requestMethod))
				httpUrlConn.connect();

			// 当有数据需要提交时
			// if (null != outputStr) {
			// OutputStream outputStream = httpUrlConn.getOutputStream();
			// // 注意编码格式，防止中文乱码
			// outputStream.write(outputStr.getBytes("UTF-8"));
			// outputStream.close();
			// }

			// 将返回的输入流转换成字符串
			InputStream inputStream = httpUrlConn.getInputStream();
			InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
			BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

			String str = null;
			while ((str = bufferedReader.readLine()) != null) {
				buffer.append(str);
			}
			bufferedReader.close();
			inputStreamReader.close();
			// 释放资源
			inputStream.close();
			inputStream = null;
			httpUrlConn.disconnect();
			jsonObject = new JSONObject(buffer.toString());
		} catch (ConnectException ce) {
			System.out.println("GET请求失败");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return jsonObject;
	}

	/**
	 * 1.发起https请求并获取结果
	 * 
	 * @param requestUrl 请求地址
	 * @param outputStr  提交的数据
	 * @return JSONObject(通过JSONObject.get(key)的方式获取json对象的属性值)
	 */
	public static JSONObject PostRequest(String requestUrl, JSONArray headers, String outputStr) {
		StringBuffer result = new StringBuffer();
		JSONObject jsonObject = null;
		try {
			URL realUrl = new URL(requestUrl);
			// 打开和URL之间的连接
			HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
			// 设置通用的请求属性
			conn.setRequestProperty("accept", "*/*");
			conn.setRequestProperty("Accept-Charset", "UTF-8");
			conn.setRequestProperty("contentType", "UTF-8");
			conn.setRequestProperty("Content-type", "application/json;charset=UTF-8");
			if (headers.length() > 0) {
				for (Object header : headers) {
					JSONObject head = (JSONObject) header;
					if (head.has("key") && head.has("value") && head.get("key").toString() != ""
							&& head.get("value").toString() != "") {
						String key = head.get("key").toString();
						String value = head.get("value").toString();
						conn.setRequestProperty(key, value);
					}
				}
			}
			// 发送POST请求必须设置如下两行
			conn.setDoOutput(true);
			conn.setDoInput(true);
			/// 当有数据需要提交时
			if (null != outputStr) {
				OutputStream outputStream = conn.getOutputStream();
				// 注意编码格式，防止中文乱码
				outputStream.write(outputStr.getBytes("UTF-8"));
				outputStream.close();
			}
			// 将返回的输入流转换成字符串
			InputStream inputStream = conn.getInputStream();
			InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
			BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

			String str = null;
			while ((str = bufferedReader.readLine()) != null) {
				result.append(str);
			}
			bufferedReader.close();
			inputStreamReader.close();
			// 释放资源
			inputStream.close();
			inputStream = null;
			conn.disconnect();
			jsonObject = new JSONObject(result.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}

		return jsonObject;
	}
}
