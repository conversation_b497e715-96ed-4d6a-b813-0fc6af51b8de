package com.wb.common.session;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.session.FindByIndexNameSessionRepository;
import org.springframework.session.Session;
import org.springframework.data.redis.core.RedisTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import com.wb.cache.RedisCache;
import com.wb.common.Base;
import com.wb.config.SessionRepositoryConfig;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;

/**
 * 简化版在线用户管理器
 * 通过Redis管理用户在线状态，不再依赖节点信息
 */
@Component
public class OnlineUserManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(OnlineUserManager.class);

    private static final String USER_ID_KEY = "sys.user";
    
    // 用户状态常量
    public static final String USER_STATUS_ONLINE = "online";   // 10分钟内活跃
    public static final String USER_STATUS_IDLE = "idle";       // 10-30分钟未活跃
    public static final String USER_STATUS_AWAY = "away";       // 30分钟以上未活跃

    // 清理锁定键
    private static final String CLEANUP_LOCK_KEY = "lock:cleanup:online_users";
    
    // 清理不活跃用户的时间阈值（默认1小时）
    private static long inactiveUserThreshold = 60 * 60 * 1000;
    
    // 用于记录最后一次更新索引过期时间的时间戳
    private Map<String, Long> lastIndexUpdateTime = new ConcurrentHashMap<>();
    
    // 索引更新间隔时间（毫秒）- 5分钟
    private static final long INDEX_UPDATE_INTERVAL = 5 * 60 * 1000;
    
    @Autowired
    private FindByIndexNameSessionRepository<? extends Session> sessionRepository;

    /**
     * 更新用户活跃状态
     */
    public void updateUserActivity(String userId) {
        if (StringUtil.isEmpty(userId)) {
            return;
        }
        try {
            // 更新用户活跃时间 - 使用TypedTuple和zsetSet方法
            Set<TypedTuple<Object>> tuples = new HashSet<>();
            tuples.add(new DefaultTypedTuple<>(userId, (double) System.currentTimeMillis()));
            Base.map.zsetSet(RedisCache.ONLINE_USERS, tuples);
            
            // 设置过期时间（24小时）
            Base.map.expire(RedisCache.ONLINE_USERS, 24 * 60 * 60, TimeUnit.SECONDS);
        } catch (Exception e) {
            LOGGER.error("更新用户[{}]活跃状态时出错", userId, e);
        }
    }

    /**
     * 处理HTTP请求中的会话，更新用户活跃度
     */
    public void handleHttpRequest(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                String userId = (String) session.getAttribute(USER_ID_KEY);
                if (!StringUtil.isEmpty(userId)) {
                    updateUserActivity(userId);
                    
                    // 更新用户会话索引过期时间
                    updatePrincipalIndexExpiration(session, userId);
                }
            }
        } catch (Exception e) {
            LOGGER.error("处理HTTP请求更新用户活跃状态时出错", e);
        }
    }
    
    /**
     * 更新用户会话索引的过期时间
     * 为避免频繁更新Redis，添加了时间间隔控制
     * 
     * @param httpSession HTTP会话
     * @param userId 用户ID
     */
    private void updatePrincipalIndexExpiration(HttpSession httpSession, String userId) {
        if (httpSession == null || StringUtil.isEmpty(userId)) {
            return;
        }
        
        try {
            // 检查是否需要更新（避免频繁更新）
            Long lastUpdate = lastIndexUpdateTime.get(userId);
            long now = System.currentTimeMillis();
            
            if (lastUpdate != null && (now - lastUpdate) < INDEX_UPDATE_INTERVAL) {
                // 如果距离上次更新不足指定间隔，则跳过
                return;
            }
            
            // 从sessionRepository获取会话
            String sessionId = httpSession.getId();
            Session session = sessionRepository.findById(sessionId);
            
            if (session != null) {
                // 更新索引过期时间
                SessionRepositoryConfig.updatePrincipalIndex(userId, session);
                // 记录更新时间
                lastIndexUpdateTime.put(userId, now);
                LOGGER.debug("已更新用户[{}]的会话索引过期时间", userId);
            }
        } catch (Exception e) {
            LOGGER.warn("更新会话索引过期时间时出错: {}", e.getMessage());
        }
    }

    /**
     * 获取用户状态
     */
    public String getUserStatus(String userId) {
        try {
            Double lastActiveTime = Base.map.zsetScore(RedisCache.ONLINE_USERS, userId);
            if (lastActiveTime == null) {
                return null; // 用户不在线
            }
            
            long inactiveTime = System.currentTimeMillis() - lastActiveTime.longValue();
            if (inactiveTime < 10 * 60 * 1000) {
                return USER_STATUS_ONLINE; // 10分钟内活跃
            }
            if (inactiveTime < 30 * 60 * 1000) {
                return USER_STATUS_IDLE; // 10-30分钟未活跃
            }
            return USER_STATUS_AWAY; // 30分钟以上未活跃
        } catch (Exception e) {
            LOGGER.error("获取用户[{}]状态时出错", userId, e);
            return null;
        }
    }
    
    /**
     * 用户登出
     */
    public void userLogout(String userId) {
        if (StringUtil.isEmpty(userId)) {
            return;
        }
        try {
            Base.map.zsetDel(RedisCache.ONLINE_USERS, userId);
            // 清理缓存
            lastIndexUpdateTime.remove(userId);
        } catch (Exception e) {
            LOGGER.error("用户[{}]登出时出错", userId, e);
        }
    }
    
    /**
     * 获取在线用户数量
     */
    public long getOnlineUserCount() {
        try {
            return Base.map.zsetSize(RedisCache.ONLINE_USERS);
        } catch (Exception e) {
            LOGGER.error("获取在线用户数量时出错", e);
            return 0;
        }
    }
    
    /**
     * 获取指定范围的在线用户，按最近活跃时间降序排序
     * 
     * @param start 起始索引（从0开始）
     * @param end 结束索引（包含）
     * @return 指定范围的在线用户列表
     */
    public List<Object> getOnlineUsersByRange(long start, long end) {
        try {
            // 使用zsetReverseRangeWithScores按分数降序返回，活跃度最高（最近活跃）的用户将排在前面
            Set<TypedTuple<Object>> usersWithScores = Base.map.zsetReverseRangeWithScores(RedisCache.ONLINE_USERS, start, end);
            if (usersWithScores == null) {
                return new ArrayList<>();
            }
            
            // 只返回用户ID列表
            List<Object> users = new ArrayList<>(usersWithScores.size());
            for (TypedTuple<Object> tuple : usersWithScores) {
                if (tuple.getValue() != null) {
                    users.add(tuple.getValue());
                }
            }
            return users;
        } catch (Exception e) {
            LOGGER.error("获取在线用户列表时出错, start={}, end={}", start, end, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取所有在线用户，按最近活跃时间降序排序
     * 注意：当在线用户数量很多时，此方法可能会消耗较多资源，建议使用getOnlineUsersByRange
     */
    public List<Object> getAllOnlineUsers() {
        return getOnlineUsersByRange(0, -1); // 使用0到-1表示获取所有元素
    }
    
    /**
     * 获取用户最后活跃时间
     */
    public Date getUserLastActiveTime(String userId) {
        try {
            Double score = Base.map.zsetScore(RedisCache.ONLINE_USERS, userId);
            return score != null ? new Date(score.longValue()) : null;
        } catch (Exception e) {
            LOGGER.error("获取用户[{}]最后活跃时间时出错", userId, e);
            return null;
        }
    }

    /**
     * 清理不活跃用户
     */
    @Scheduled(fixedRate = 15 * 60 * 1000, initialDelay = 30 * 1000)
    public void cleanupInactiveUsers() {
        // 尝试获取分布式锁
        boolean locked = false;
        try {
            locked = Base.map.tryLock(CLEANUP_LOCK_KEY, 3000, 5 * 60 * 1000);
            if (!locked) {
                // 其他节点已经在执行清理任务
                return;
            }

            LOGGER.info("开始执行不活跃用户清理");

            // 获取不活跃用户阈值时间戳
            long inactiveTimestamp = System.currentTimeMillis() - inactiveUserThreshold;

            // 查找不活跃用户
            Set<Object> inactiveUsers = Base.map.zsetRangeByScore(RedisCache.ONLINE_USERS, 0, inactiveTimestamp);
            
            if (inactiveUsers != null && !inactiveUsers.isEmpty()) {
                // 删除不活跃用户
                int removedCount = 0;
                for (Object userId : inactiveUsers) {
                    Base.map.zsetDel(RedisCache.ONLINE_USERS, userId);
                    // 清理lastIndexUpdateTime中的记录
                    lastIndexUpdateTime.remove(userId.toString());
                    removedCount++;
                }
                
                LOGGER.info("已清理 {} 个不活跃用户", removedCount);
            }
            
            // 定期清理lastIndexUpdateTime，避免内存泄漏
            cleanupLastIndexUpdateTime();
        } catch (Exception e) {
            LOGGER.error("清理不活跃用户时出错", e);
        } finally {
            if (locked) {
                // 释放锁
                Base.map.unLock(CLEANUP_LOCK_KEY);
            }
        }
    }
    
    /**
     * 清理lastIndexUpdateTime缓存，防止内存泄漏
     */
    private void cleanupLastIndexUpdateTime() {
        try {
            if (lastIndexUpdateTime.size() > 10000) { // 如果缓存过大，进行清理
                LOGGER.info("开始清理lastIndexUpdateTime缓存，当前大小: {}", lastIndexUpdateTime.size());
                
                long now = System.currentTimeMillis();
                long expireThreshold = now - (24 * 60 * 60 * 1000); // 一天前的时间戳
                
                int removedCount = 0;
                for (String key : new ArrayList<>(lastIndexUpdateTime.keySet())) {
                    Long timestamp = lastIndexUpdateTime.get(key);
                    if (timestamp != null && timestamp < expireThreshold) {
                        lastIndexUpdateTime.remove(key);
                        removedCount++;
                    }
                }
                
                LOGGER.info("lastIndexUpdateTime缓存清理完成，移除了 {} 条记录，当前大小: {}", 
                        removedCount, lastIndexUpdateTime.size());
            }
        } catch (Exception e) {
            LOGGER.error("清理lastIndexUpdateTime缓存时出错", e);
        }
    }
    
    /**
     * 设置不活跃用户清理的时间阈值
     * @param thresholdMillis 不活跃时间阈值（毫秒）
     */
    public void setInactiveUserThreshold(long thresholdMillis) {
        if (thresholdMillis >= 10 * 60 * 1000) { // 最小10分钟
            inactiveUserThreshold = thresholdMillis;
            LOGGER.info("设置不活跃用户清理阈值为: {}ms", thresholdMillis);
        } else {
            LOGGER.warn("不活跃用户清理阈值设置过小，忽略此次设置: {}ms", thresholdMillis);
        }
    }
    
    /**
     * 获取当前不活跃用户清理的时间阈值
     * @return 不活跃时间阈值（毫秒）
     */
    public long getInactiveUserThreshold() {
        return inactiveUserThreshold;
    }
} 