package com.wb.aliyun.mqtt;

import com.aliyun.onsmqtt20200420.Client;
import com.aliyun.onsmqtt20200420.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;

public class CredentialInfo {
    private static String accessKeyId;
    private static String accessKeySecret;
    private static String endpoint;
    private String clientId;
    private String instanceId;
    private Client client;

    /**
     * @param accessKeyId
     * @param accessKeySecret
     * @param endpoint
     * @param instanceId
     * @param clientId
     * @throws Exception
     */
    public CredentialInfo(String accessKeyId, String accessKeySecret, String endpoint, String instanceId, String clientId) throws Exception {
        this.accessKeyId = accessKeyId;
        this.accessKeySecret = accessKeySecret;
        this.endpoint = endpoint;

        this.instanceId = instanceId;
        this.clientId = clientId;

        this.client = createClient();
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    /**
     * 使用AK&SK初始化账号Client
     *
     * @return Client
     * @throws Exception
     */
    public static Client createClient() throws Exception {
        // 请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例使用环境变量获取 AccessKey 的方式进行调用，仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html
        Config config = new Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/OnsMqtt
        config.endpoint = endpoint;
        return new Client(config);
    }

    /**
     * 注册设备访问凭证
     *
     * @return
     * @throws Exception
     */
    public RegisterDeviceCredentialResponseBody.RegisterDeviceCredentialResponseBodyDeviceCredential registerCredential() throws Exception {
        RegisterDeviceCredentialRequest registerDeviceCredentialRequest = new RegisterDeviceCredentialRequest()
                .setClientId(clientId)
                .setInstanceId(instanceId);
        RuntimeOptions runtime = new RuntimeOptions();
        RegisterDeviceCredentialResponseBody.RegisterDeviceCredentialResponseBodyDeviceCredential credential = null;
        try {
            RegisterDeviceCredentialResponse response = client.registerDeviceCredentialWithOptions(registerDeviceCredentialRequest, runtime);
            //完成注册返回注册信息
            credential = response.body.getDeviceCredential();

        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            Common.assertAsString(error.message);
        }
        return credential;

    }

    /**
     * 获取设备访问凭证
     *
     * @return
     * @throws Exception
     */
    public GetDeviceCredentialResponseBody.GetDeviceCredentialResponseBodyDeviceCredential getCredential() throws Exception {
        GetDeviceCredentialRequest getDeviceCredentialRequest = new GetDeviceCredentialRequest()
                .setClientId(clientId)
                .setInstanceId(instanceId);
        RuntimeOptions runtime = new RuntimeOptions();
        GetDeviceCredentialResponseBody.GetDeviceCredentialResponseBodyDeviceCredential credential = null;
        try {
            GetDeviceCredentialResponse response = client.getDeviceCredentialWithOptions(getDeviceCredentialRequest, runtime);
            //获取凭证信息
            credential = response.getBody().getDeviceCredential();

        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            Common.assertAsString(error.message);
        }
        return credential;
    }

    /**
     * 更新设备访问凭证
     *
     * @return
     * @throws Exception
     */
    public RefreshDeviceCredentialResponseBody.RefreshDeviceCredentialResponseBodyDeviceCredential refreshCredential() throws Exception {
        RefreshDeviceCredentialRequest refreshDeviceCredentialRequest = new RefreshDeviceCredentialRequest()
                .setClientId(clientId)
                .setInstanceId(instanceId);
        RuntimeOptions runtime = new RuntimeOptions();
        RefreshDeviceCredentialResponseBody.RefreshDeviceCredentialResponseBodyDeviceCredential credential = null;
        try {
            // 复制代码运行请自行打印 API 的返回值
            RefreshDeviceCredentialResponse response = client.refreshDeviceCredentialWithOptions(refreshDeviceCredentialRequest, runtime);
            credential = response.body.getDeviceCredential();
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            Common.assertAsString(error.message);
        }
        return credential;
    }

    /**
     * 注销设备访问凭证
     *
     * @return
     * @throws Exception
     */
    public Boolean cancelCredential() throws Exception {
        UnRegisterDeviceCredentialRequest unRegisterDeviceCredentialRequest = new UnRegisterDeviceCredentialRequest()
                .setClientId(clientId)
                .setInstanceId(instanceId);
        RuntimeOptions runtime = new RuntimeOptions();
        Boolean isSuccess = false;
        try {

            UnRegisterDeviceCredentialResponse response = client.unRegisterDeviceCredentialWithOptions(unRegisterDeviceCredentialRequest, runtime);
            String requestId = response.getBody().getRequestId();
            if (!requestId.isEmpty()) {//凭证取消成功
                isSuccess = true;
            }

        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            Common.assertAsString(error.message);
        }
        return isSuccess;
    }

    /**
     * 获取一机一密模式下需要的userName和password
     * @param deviceAccessKeyId
     * @param deviceAccessKeySecret
     * @return
     */
    public HashMap<String, String> getUserDeviceCredential(String deviceAccessKeyId, String deviceAccessKeySecret) {
        HashMap<String, String> map = new HashMap<>();
        //签名模式的UserName应该设置成 DeviceCredential|DeviceAccessKeyId|Client ID。
        String userName = "DeviceCredential|" + deviceAccessKeyId + "|" + instanceId;
        map.put("userName", userName);
        //DeviceAccessKeySecret作为密钥
        map.put("password", createPassword(deviceAccessKeySecret));
        return map;
    }

    /**
     * 获取签名模式下需要的userName和password
     * @return
     */
    public HashMap<String, String> getUserSignature() {
        HashMap<String, String> map = new HashMap<>();
        //签名模式的 Username 应该设置成Signature|AccessKey ID|实例 ID
        String userName = "Signature|" + accessKeyId + "|" + instanceId;
        map.put("userName", userName);
        //AccessKey Secret作为密钥
        map.put("password", createPassword(accessKeySecret));
        return map;
    }

    /**
     * 获取鉴权模式下需要加密的password
     *
     * @return
     */
    public String createPassword(String KeySecret) {
        String password = null;
        try {
            //Secret作为密钥，使用HMAC-SHA1方法对待签名字符串Client ID做签名计算得到一个二进制数组，再对该二进制数组做Base64编码得到最终的Password字符串。
            byte[] keyBytes = KeySecret.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(keySpec);
            byte[] macBytes = mac.doFinal(clientId.getBytes(StandardCharsets.UTF_8));
            password = Base64.getEncoder().encodeToString(macBytes);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return password;
    }
}
