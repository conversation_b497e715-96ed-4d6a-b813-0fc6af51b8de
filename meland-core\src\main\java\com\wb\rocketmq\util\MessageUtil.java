package com.wb.rocketmq.util;

import com.wb.common.Base;
import com.wb.common.Var;
import com.wb.rocketmq.config.MqConfig;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import com.wb.util.WbUtil;
import org.apache.rocketmq.client.apis.*;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.producer.Producer;
import org.json.JSONObject;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * RocketMQ消息工具类
 * 提供RocketMQ消息处理的常用工具方法
 */
public class MessageUtil {

    /** MQ配置 */
    private static final MqConfig config = new MqConfig();

    /** 客户端连接配置 */
    public static ClientConfiguration configuration = null;

    /**
     * 将ByteBuffer转换为JSONObject
     * 用于解析RocketMQ消息体
     *
     * @param byteBuffer 字节缓冲区
     * @return JSON对象
     */
    public static JSONObject ByteBufferToJsonObject(ByteBuffer byteBuffer) {
        try {
            Charset charset = StandardCharsets.UTF_8;
            String results = charset.decode(byteBuffer).toString();
            return new JSONObject(results);
        } catch (Exception e) {
            LogUtil.error(StringUtil.format("方法【ByteBufferToJsonObject】解析失败, 异常信息：{0}", e));
        }
        return new JSONObject();
    }

    /**
     * 初始化RocketMQ客户端连接配置
     * 在使用RocketMQ前必须调用此方法
     */
    public static void linker() {
        // 创建客户端配置构建器
        ClientConfigurationBuilder builder = ClientConfiguration.newBuilder()
                .setEndpoints(config.getServerAddress());
        
        // 如果是使用公网接入点访问，需要设置实例的用户名和密码
        // 用户名和密码在控制台实例详情页获取
        // 如果是在阿里云ECS内网访问，无需填写该配置，服务端会根据内网VPC信息智能获取
        if (config.getIsPublic()) {
            builder.setCredentialProvider(
                    new StaticSessionCredentialsProvider(config.getAccessKey(), config.getSecretKey()));
        }
        
        // 构建并保存配置
        configuration = builder.build();
        
        if (configuration == null) {
            LogUtil.error("RocketMQ实例接入失败");
        } else {
            LogUtil.info("RocketMQ客户端配置初始化成功");
        }
    }

    /**
     * 获取消息生产者实例
     *
     * @param provider 客户端服务提供者
     * @param topic 消息主题
     * @return 生产者实例
     * @throws ClientException 客户端异常
     */
    public static Producer getProducer(ClientServiceProvider provider, String topic) throws ClientException {
        return provider.newProducerBuilder()
                .setTopics(topic)
                .setClientConfiguration(MessageUtil.configuration)
                .build();
    }

    /**
     * 统一消息消费处理方法
     * 处理流程:
     * 1. 检查消息是否已经处理过(防止重复消费)
     * 2. 调用指定的业务模块方法处理消息
     * 3. 记录处理状态和日志
     *
     * @param params 消息参数
     * @param messageId 消息ID
     * @return 消费结果
     */
    public static ConsumeResult onSuccess(JSONObject params, String messageId) {
        // 创建消息处理标识，用于防止重复消费
        String ROCKETMQ_KEY = "ROCKETMQ_CONSUME_" + messageId;
        
        try {
            // 检查消息是否已经处理过
            if (Base.map.get(ROCKETMQ_KEY) == null) {
                // 检查消息是否包含model参数
                if (params.has("model")) {
                    // 调用指定模块方法处理消息
                    WbUtil.run(params.getString("model"), params, false);
                    
                    // 记录处理成功状态，有效期2分钟
                    Base.map.put(ROCKETMQ_KEY, "success", 120000);
                    
                    // 记录调试日志
                    if (Var.getBool("sys.config.aliyun.logDebug")) {
                        LogUtil.info(StringUtil.format("消息消费成功, messageId={0}", messageId));
                    }
                    
                    return ConsumeResult.SUCCESS;
                } else {
                    // 记录警告：消息缺少model参数
                    LogUtil.warn(StringUtil.format("消息没有model参数, messageId={0}, 消息参数：{1}", 
                            messageId, params.toString()));
                    return ConsumeResult.SUCCESS;
                }
            } else {
                // 消息已经处理过，返回失败以防止重复消费
                if (Var.getBool("sys.config.aliyun.logDebug")) {
                    LogUtil.info(StringUtil.format("消息重复处理, messageId={0}", messageId));
                }
                return ConsumeResult.FAILURE;
            }
        } catch (Throwable t) {
            // 记录异常信息
            LogUtil.error(StringUtil.format("消息处理异常, messageId={0}, 参数：{1}, 异常：{2}", 
                    messageId, params.toString(), t));
        }
        
        // 发生异常时返回失败
        return ConsumeResult.FAILURE;
    }
}
