/**Released Version v3.1.2,BUILD 8,Time 1681293328100. Fengmap Javascript SDK , see: https://www.fengmap.com for details**/
!function(t,i){"object"==typeof exports&&"object"==typeof module?module.exports=i(require("fs")):"function"==typeof define&&define.amd?define(["fs"],i):"object"==typeof exports?exports.fengmap=i(require("fs")):t.fengmap=i(t.fs)}(this,function(__WEBPACK_EXTERNAL_MODULE__47__){return d=[function(t,i,n){"use strict";var r,e,s=t.exports=n(2),o=n(16);s.codegen=n(40),s.fetch=n(41),s.path=n(42),s.fs=s.inquire("fs"),s.toArray=function(t){if(t){for(var i=Object.keys(t),n=new Array(i.length),r=0;r<i.length;)n[r]=t[i[r++]];return n}return[]},s.toObject=function(t){for(var i={},n=0;n<t.length;){var r=t[n++],e=t[n++];void 0!==e&&(i[r]=e)}return i};var u=/\\/g,h=/"/g;s.isReserved=function(t){return/^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(t)},s.safeProp=function(t){return!/^[$\w_]+$/.test(t)||s.isReserved(t)?'["'+t.replace(u,"\\\\").replace(h,'\\"')+'"]':"."+t},s.ucFirst=function(t){return t.charAt(0).toUpperCase()+t.substring(1)};var a=/_([a-z])/g;s.camelCase=function(t){return t.substring(0,1)+t.substring(1).replace(a,function(t,i){return i.toUpperCase()})},s.compareFieldsById=function(t,i){return t.id-i.id},s.decorateType=function(t,i){if(t.$type)return i&&t.$type.name!==i&&(s.decorateRoot.remove(t.$type),t.$type.name=i,s.decorateRoot.add(t.$type)),t.$type;i=new(r=r||n(18))(i||t.name);return s.decorateRoot.add(i),i.ctor=t,Object.defineProperty(t,"$type",{value:i,enumerable:!1}),Object.defineProperty(t.prototype,"$type",{value:i,enumerable:!1}),i};var f=0;s.decorateEnum=function(t){if(t.$type)return t.$type;var i=new(e=e||n(3))("Enum"+f++,t);return s.decorateRoot.add(i),Object.defineProperty(t,"$type",{value:i,enumerable:!1}),i},s.setProperty=function(t,i,n){if("object"!=typeof t)throw TypeError("dst must be an object");if(!i)throw TypeError("path must be specified");return function t(i,n,r){var e=n.shift();return 0<n.length?i[e]=t(i[e]||{},n,r):((n=i[e])&&(r=[].concat(n).concat(r)),i[e]=r),i}(t,i=i.split("."),n)},Object.defineProperty(s,"decorateRoot",{get:function(){return o.decorated||(o.decorated=new(n(26)))}})},function(t,i,n){"use strict";!function(o){function u(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var h=null;i.a=function(){function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t)}var i,n,r,e;function s(){return e.apply(this,arguments)}return i=t,n=null,r=[{key:"global",value:(e=function(){if("undefined"!=typeof self&&null!==self&&0!==Object.keys(self).length)return self;if("undefined"!=typeof window&&null!==window&&0!==Object.keys(window).length)return window;if(null!=o&&0!==Object.keys(o).length)return o;if("undefined"==typeof my)throw new Error("unable to locate global object");var t;return h||(t=my.getAppIdSync(),h={location:{host:t.appId+".hybrid.alipay-eco.com"}}),h},s.toString=function(){return e.toString()},s)}],n&&u(i.prototype,n),r&&u(i,r),t}()}.call(this,n(8))},function(t,n,s){"use strict";!function(t){var r=n;function e(t,i,n){for(var r=Object.keys(i),e=0;e<r.length;++e)void 0!==t[r[e]]&&n||(t[r[e]]=i[r[e]]);return t}function i(t){function n(t,i){if(!(this instanceof n))return new n(t,i);Object.defineProperty(this,"message",{get:function(){return t}}),Error.captureStackTrace?Error.captureStackTrace(this,n):Object.defineProperty(this,"stack",{value:(new Error).stack||""}),i&&e(this,i)}return(n.prototype=Object.create(Error.prototype)).constructor=n,Object.defineProperty(n.prototype,"name",{get:function(){return t}}),n.prototype.toString=function(){return this.name+": "+this.message},n}r.asPromise=s(13),r.base64=s(31),r.EventEmitter=s(32),r.float=s(33),r.inquire=s(14),r.utf8=s(34),r.pool=s(35),r.LongBits=s(36),r.isNode=Boolean(void 0!==t&&t&&t.process&&t.process.versions&&t.process.versions.node),r.global=r.isNode&&t||"undefined"!=typeof window&&window||"undefined"!=typeof self&&self||this,r.emptyArray=Object.freeze?Object.freeze([]):[],r.emptyObject=Object.freeze?Object.freeze({}):{},r.isInteger=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t},r.isString=function(t){return"string"==typeof t||t instanceof String},r.isObject=function(t){return t&&"object"==typeof t},r.isset=r.isSet=function(t,i){var n=t[i];return!(null==n||!t.hasOwnProperty(i))&&("object"!=typeof n||0<(Array.isArray(n)?n:Object.keys(n)).length)},r.Buffer=function(){try{var t=r.inquire("buffer").Buffer;return t.prototype.utf8Write?t:null}catch(t){return null}}(),r.u=null,r.v=null,r.newBuffer=function(t){return"number"==typeof t?r.Buffer?r.v(t):new r.Array(t):r.Buffer?r.u(t):"undefined"==typeof Uint8Array?t:new Uint8Array(t)},r.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,r.Long=r.global.dcodeIO&&r.global.dcodeIO.Long||r.global.Long||r.inquire("long"),r.key2Re=/^true|false|0|1$/,r.key32Re=/^-?(?:0|[1-9][0-9]*)$/,r.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,r.longToHash=function(t){return t?r.LongBits.from(t).toHash():r.LongBits.zeroHash},r.longFromHash=function(t,i){t=r.LongBits.fromHash(t);return r.Long?r.Long.fromBits(t.lo,t.hi,i):t.toNumber(Boolean(i))},r.merge=e,r.lcFirst=function(t){return t.charAt(0).toLowerCase()+t.substring(1)},r.newError=i,r.ProtocolError=i("ProtocolError"),r.oneOfGetter=function(t){for(var n={},i=0;i<t.length;++i)n[t[i]]=1;return function(){for(var t=Object.keys(this),i=t.length-1;-1<i;--i)if(1===n[t[i]]&&void 0!==this[t[i]]&&null!==this[t[i]])return t[i]}},r.oneOfSetter=function(n){return function(t){for(var i=0;i<n.length;++i)n[i]!==t&&delete this[n[i]]}},r.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},r._=function(){var n=r.Buffer;n?(r.u=n.from!==Uint8Array.from&&n.from||function(t,i){return new n(t,i)},r.v=n.allocUnsafe||function(t){return new n(t)}):r.u=r.v=null}}.call(this,s(8))},function(t,i,n){"use strict";t.exports=s;var u=n(4);((s.prototype=Object.create(u.prototype)).constructor=s).className="Enum";var r=n(6),e=n(0);function s(t,i,n,r,e){if(u.call(this,t,n),i&&"object"!=typeof i)throw TypeError("values must be an object");if(this.valuesById={},this.values=Object.create(this.valuesById),this.comment=r,this.comments=e||{},this.reserved=void 0,i)for(var s=Object.keys(i),o=0;o<s.length;++o)"number"==typeof i[s[o]]&&(this.valuesById[this.values[s[o]]=i[s[o]]]=s[o])}s.fromJSON=function(t,i){t=new s(t,i.values,i.options,i.comment,i.comments);return t.reserved=i.reserved,t},s.prototype.toJSON=function(t){t=!!t&&Boolean(t.keepComments);return e.toObject(["options",this.options,"values",this.values,"reserved",this.reserved&&this.reserved.length?this.reserved:void 0,"comment",t?this.comment:void 0,"comments",t?this.comments:void 0])},s.prototype.add=function(t,i,n){if(!e.isString(t))throw TypeError("name must be a string");if(!e.isInteger(i))throw TypeError("id must be an integer");if(void 0!==this.values[t])throw Error("duplicate name '"+t+"' in "+this);if(this.isReservedId(i))throw Error("id "+i+" is reserved in "+this);if(this.isReservedName(t))throw Error("name '"+t+"' is reserved in "+this);if(void 0!==this.valuesById[i]){if(!this.options||!this.options.allow_alias)throw Error("duplicate id "+i+" in "+this);this.values[t]=i}else this.valuesById[this.values[t]=i]=t;return this.comments[t]=n||null,this},s.prototype.remove=function(t){if(!e.isString(t))throw TypeError("name must be a string");var i=this.values[t];if(null==i)throw Error("name '"+t+"' does not exist in "+this);return delete this.valuesById[i],delete this.values[t],delete this.comments[t],this},s.prototype.isReservedId=function(t){return r.isReservedId(this.reserved,t)},s.prototype.isReservedName=function(t){return r.isReservedName(this.reserved,t)}},function(t,i,n){"use strict";(t.exports=e).className="ReflectionObject";var r,o=n(0);function e(t,i){if(!o.isString(t))throw TypeError("name must be a string");if(i&&!o.isObject(i))throw TypeError("options must be an object");this.options=i,this.parsedOptions=null,this.name=t,this.parent=null,this.resolved=!1,this.comment=null,this.filename=null}Object.defineProperties(e.prototype,{root:{get:function(){for(var t=this;null!==t.parent;)t=t.parent;return t}},fullName:{get:function(){for(var t=[this.name],i=this.parent;i;)t.unshift(i.name),i=i.parent;return t.join(".")}}}),e.prototype.toJSON=function(){throw Error()},e.prototype.onAdd=function(t){this.parent&&this.parent!==t&&this.parent.remove(this),this.parent=t,this.resolved=!1;t=t.root;t instanceof r&&t.O(this)},e.prototype.onRemove=function(t){t=t.root;t instanceof r&&t.M(this),this.parent=null,this.resolved=!1},e.prototype.resolve=function(){return this.resolved||this.root instanceof r&&(this.resolved=!0),this},e.prototype.getOption=function(t){if(this.options)return this.options[t]},e.prototype.setOption=function(t,i,n){return n&&this.options&&void 0!==this.options[t]||((this.options||(this.options={}))[t]=i),this},e.prototype.setParsedOption=function(i,t,n){this.parsedOptions||(this.parsedOptions=[]);var r,e,s=this.parsedOptions;return n?(e=s.find(function(t){return Object.prototype.hasOwnProperty.call(t,i)}))?(r=e[i],o.setProperty(r,n,t)):((e={})[i]=o.setProperty({},n,t),s.push(e)):((e={})[i]=t,s.push(e)),this},e.prototype.setOptions=function(t,i){if(t)for(var n=Object.keys(t),r=0;r<n.length;++r)this.setOption(n[r],t[n[r]],i);return this},e.prototype.toString=function(){var t=this.constructor.className,i=this.fullName;return i.length?t+" "+i:t},e._=function(t){r=t}},function(t,i,n){"use strict";t.exports=o;var u=n(4);((o.prototype=Object.create(u.prototype)).constructor=o).className="Field";var r,e=n(3),h=n(7),a=n(0),f=/^required|optional|repeated$/;function o(t,i,n,r,e,s,o){if(a.isObject(r)?(o=e,s=r,r=e=void 0):a.isObject(e)&&(o=s,s=e,e=void 0),u.call(this,t,s),!a.isInteger(i)||i<0)throw TypeError("id must be a non-negative integer");if(!a.isString(n))throw TypeError("type must be a string");if(void 0!==r&&!f.test(r=r.toString().toLowerCase()))throw TypeError("rule must be a string rule");if(void 0!==e&&!a.isString(e))throw TypeError("extend must be a string");this.rule=r&&"optional"!==r?r:void 0,this.type=n,this.id=i,this.extend=e||void 0,this.required="required"===r,this.optional=!this.required,this.repeated="repeated"===r,this.map=!1,this.message=null,this.partOf=null,this.typeDefault=null,this.defaultValue=null,this.long=!!a.Long&&void 0!==h.long[n],this.bytes="bytes"===n,this.resolvedType=null,this.extensionField=null,this.declaringField=null,this.j=null,this.comment=o}o.fromJSON=function(t,i){return new o(t,i.id,i.type,i.rule,i.extend,i.options,i.comment)},Object.defineProperty(o.prototype,"packed",{get:function(){return null===this.j&&(this.j=!1!==this.getOption("packed")),this.j}}),o.prototype.setOption=function(t,i,n){return"packed"===t&&(this.j=null),u.prototype.setOption.call(this,t,i,n)},o.prototype.toJSON=function(t){t=!!t&&Boolean(t.keepComments);return a.toObject(["rule","optional"!==this.rule&&this.rule||void 0,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",t?this.comment:void 0])},o.prototype.resolve=function(){return this.resolved?this:(void 0===(this.typeDefault=h.defaults[this.type])&&(this.resolvedType=(this.declaringField||this).parent.lookupTypeOrEnum(this.type),this.resolvedType instanceof r?this.typeDefault=null:this.typeDefault=this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]),this.options&&null!=this.options.default&&(this.typeDefault=this.options.default,this.resolvedType instanceof e&&"string"==typeof this.typeDefault&&(this.typeDefault=this.resolvedType.values[this.typeDefault])),this.options&&(!0!==this.options.packed&&(void 0===this.options.packed||!this.resolvedType||this.resolvedType instanceof e)||delete this.options.packed,Object.keys(this.options).length||(this.options=void 0)),this.long?(this.typeDefault=a.Long.fromNumber(this.typeDefault,"u"===this.type.charAt(0)),Object.freeze&&Object.freeze(this.typeDefault)):this.bytes&&"string"==typeof this.typeDefault&&(a.base64.test(this.typeDefault)?a.base64.decode(this.typeDefault,t=a.newBuffer(a.base64.length(this.typeDefault)),0):a.utf8.write(this.typeDefault,t=a.newBuffer(a.utf8.length(this.typeDefault)),0),this.typeDefault=t),this.map?this.defaultValue=a.emptyObject:this.repeated?this.defaultValue=a.emptyArray:this.defaultValue=this.typeDefault,this.parent instanceof r&&(this.parent.ctor.prototype[this.name]=this.defaultValue),u.prototype.resolve.call(this));var t},o.d=function(n,r,e,s){return"function"==typeof r?r=a.decorateType(r).name:r&&"object"==typeof r&&(r=a.decorateEnum(r).name),function(t,i){a.decorateType(t.constructor).add(new o(i,n,r,e,{default:s}))}},o._=function(t){r=t}},function(t,i,n){"use strict";t.exports=f;var r=n(4);((f.prototype=Object.create(r.prototype)).constructor=f).className="Namespace";var e,s,o,u=n(5),h=n(0);function a(t,i){if(t&&t.length){for(var n={},r=0;r<t.length;++r)n[t[r].name]=t[r].toJSON(i);return n}}function f(t,i){r.call(this,t,i),this.nested=void 0,this.k=null}function c(t){return t.k=null,t}f.fromJSON=function(t,i){return new f(t,i.options).addJSON(i.nested)},f.arrayToJSON=a,f.isReservedId=function(t,i){if(t)for(var n=0;n<t.length;++n)if("string"!=typeof t[n]&&t[n][0]<=i&&t[n][1]>i)return!0;return!1},f.isReservedName=function(t,i){if(t)for(var n=0;n<t.length;++n)if(t[n]===i)return!0;return!1},Object.defineProperty(f.prototype,"nestedArray",{get:function(){return this.k||(this.k=h.toArray(this.nested))}}),f.prototype.toJSON=function(t){return h.toObject(["options",this.options,"nested",a(this.nestedArray,t)])},f.prototype.addJSON=function(t){if(t)for(var i,n=Object.keys(t),r=0;r<n.length;++r)i=t[n[r]],this.add((void 0!==i.fields?e:void 0!==i.values?o:void 0!==i.methods?s:void 0!==i.id?u:f).fromJSON(n[r],i));return this},f.prototype.get=function(t){return this.nested&&this.nested[t]||null},f.prototype.getEnum=function(t){if(this.nested&&this.nested[t]instanceof o)return this.nested[t].values;throw Error("no such enum: "+t)},f.prototype.add=function(t){if(!(t instanceof u&&void 0!==t.extend||t instanceof e||t instanceof o||t instanceof s||t instanceof f))throw TypeError("object must be a valid nested object");if(this.nested){var i=this.get(t.name);if(i){if(!(i instanceof f&&t instanceof f)||i instanceof e||i instanceof s)throw Error("duplicate name '"+t.name+"' in "+this);for(var n=i.nestedArray,r=0;r<n.length;++r)t.add(n[r]);this.remove(i),this.nested||(this.nested={}),t.setOptions(i.options,!0)}}else this.nested={};return(this.nested[t.name]=t).onAdd(this),c(this)},f.prototype.remove=function(t){if(!(t instanceof r))throw TypeError("object must be a ReflectionObject");if(t.parent!==this)throw Error(t+" is not a member of "+this);return delete this.nested[t.name],Object.keys(this.nested).length||(this.nested=void 0),t.onRemove(this),c(this)},f.prototype.define=function(t,i){if(h.isString(t))t=t.split(".");else if(!Array.isArray(t))throw TypeError("illegal path");if(t&&t.length&&""===t[0])throw Error("path must be relative");for(var n=this;0<t.length;){var r=t.shift();if(n.nested&&n.nested[r]){if(!((n=n.nested[r])instanceof f))throw Error("path conflicts with non-namespace objects")}else n.add(n=new f(r))}return i&&n.addJSON(i),n},f.prototype.resolveAll=function(){for(var t=this.nestedArray,i=0;i<t.length;)t[i]instanceof f?t[i++].resolveAll():t[i++].resolve();return this.resolve()},f.prototype.lookup=function(t,i,n){if("boolean"==typeof i?(n=i,i=void 0):i&&!Array.isArray(i)&&(i=[i]),h.isString(t)&&t.length){if("."===t)return this.root;t=t.split(".")}else if(!t.length)return this;if(""===t[0])return this.root.lookup(t.slice(1),i);var r=this.get(t[0]);if(r){if(1===t.length){if(!i||-1<i.indexOf(r.constructor))return r}else if(r instanceof f&&(r=r.lookup(t.slice(1),i,!0)))return r}else for(var e=0;e<this.nestedArray.length;++e)if(this.k[e]instanceof f&&(r=this.k[e].lookup(t,i,!0)))return r;return null===this.parent||n?null:this.parent.lookup(t,i)},f.prototype.lookupType=function(t){var i=this.lookup(t,[e]);if(!i)throw Error("no such type: "+t);return i},f.prototype.lookupEnum=function(t){var i=this.lookup(t,[o]);if(!i)throw Error("no such Enum '"+t+"' in "+this);return i},f.prototype.lookupTypeOrEnum=function(t){var i=this.lookup(t,[e,o]);if(!i)throw Error("no such Type or Enum '"+t+"' in "+this);return i},f.prototype.lookupService=function(t){var i=this.lookup(t,[s]);if(!i)throw Error("no such Service '"+t+"' in "+this);return i},f._=function(t,i,n){e=t,s=i,o=n}},function(t,i,n){"use strict";var i=i,n=n(0),e=["double","float","int32","uint32","sint32","fixed32","sfixed32","int64","uint64","sint64","fixed64","sfixed64","bool","string","bytes"];function r(t,i){var n=0,r={};for(i|=0;n<t.length;)r[e[n+i]]=t[n++];return r}i.basic=r([1,5,0,0,0,5,5,0,0,0,1,1,0,2,2]),i.defaults=r([0,0,0,0,0,0,0,0,0,0,0,0,!1,"",n.emptyArray,null]),i.long=r([0,0,0,1,1],7),i.mapKey=r([0,0,0,5,5,0,0,0,1,1,0,2],2),i.packed=r([1,5,0,0,0,5,5,0,0,0,1,1,0])},function(t,i){var n=function(){return this}();try{n=n||new Function("return this")()}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,i,n){"use strict";t.exports=c;var r,e=n(2),s=e.LongBits,o=e.base64,u=e.utf8;function h(t,i,n){this.fn=t,this.len=i,this.next=void 0,this.val=n}function a(){}function f(t){this.head=t.head,this.tail=t.tail,this.len=t.len,this.next=t.states}function c(){this.len=0,this.head=new h(a,0,0),this.tail=this.head,this.states=null}function l(){return e.Buffer?function(){return(c.create=function(){return new r})()}:function(){return new c}}function v(t,i,n){i[n]=255&t}function d(t,i){this.len=t,this.next=void 0,this.val=i}function y(t,i,n){for(;t.hi;)i[n++]=127&t.lo|128,t.lo=(t.lo>>>7|t.hi<<25)>>>0,t.hi>>>=7;for(;127<t.lo;)i[n++]=127&t.lo|128,t.lo=t.lo>>>7;i[n++]=t.lo}function b(t,i,n){i[n]=255&t,i[n+1]=t>>>8&255,i[n+2]=t>>>16&255,i[n+3]=t>>>24}c.create=l(),c.alloc=function(t){return new e.Array(t)},e.Array!==Array&&(c.alloc=e.pool(c.alloc,e.Array.prototype.subarray)),c.prototype.R=function(t,i,n){return this.tail=this.tail.next=new h(t,i,n),this.len+=i,this},(d.prototype=Object.create(h.prototype)).fn=function(t,i,n){for(;127<t;)i[n++]=127&t|128,t>>>=7;i[n]=t},c.prototype.uint32=function(t){return this.len+=(this.tail=this.tail.next=new d((t>>>=0)<128?1:t<16384?2:t<2097152?3:t<268435456?4:5,t)).len,this},c.prototype.int32=function(t){return t<0?this.R(y,10,s.fromNumber(t)):this.uint32(t)},c.prototype.sint32=function(t){return this.uint32((t<<1^t>>31)>>>0)},c.prototype.int64=c.prototype.uint64=function(t){t=s.from(t);return this.R(y,t.length(),t)},c.prototype.sint64=function(t){t=s.from(t).zzEncode();return this.R(y,t.length(),t)},c.prototype.bool=function(t){return this.R(v,1,t?1:0)},c.prototype.sfixed32=c.prototype.fixed32=function(t){return this.R(b,4,t>>>0)},c.prototype.sfixed64=c.prototype.fixed64=function(t){t=s.from(t);return this.R(b,4,t.lo).R(b,4,t.hi)},c.prototype.float=function(t){return this.R(e.float.writeFloatLE,4,t)},c.prototype.double=function(t){return this.R(e.float.writeDoubleLE,8,t)};var p=e.Array.prototype.set?function(t,i,n){i.set(t,n)}:function(t,i,n){for(var r=0;r<t.length;++r)i[n+r]=t[r]};c.prototype.bytes=function(t){var i,n=t.length>>>0;return n?(e.isString(t)&&(i=c.alloc(n=o.length(t)),o.decode(t,i,0),t=i),this.uint32(n).R(p,n,t)):this.R(v,1,0)},c.prototype.string=function(t){var i=u.length(t);return i?this.uint32(i).R(u.write,i,t):this.R(v,1,0)},c.prototype.fork=function(){return this.states=new f(this),this.head=this.tail=new h(a,0,0),this.len=0,this},c.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new h(a,0,0),this.len=0),this},c.prototype.ldelim=function(){var t=this.head,i=this.tail,n=this.len;return this.reset().uint32(n),n&&(this.tail.next=t.next,this.tail=i,this.len+=n),this},c.prototype.finish=function(){for(var t=this.head.next,i=this.constructor.alloc(this.len),n=0;t;)t.fn(t.val,i,n),n+=t.len,t=t.next;return i},c._=function(t){r=t,c.create=l(),r._()}},function(t,i,n){"use strict";t.exports=h;var r,e=n(2),s=e.LongBits,o=e.utf8;function u(t,i){return RangeError("index out of range: "+t.pos+" + "+(i||1)+" > "+t.len)}function h(t){this.buf=t,this.pos=0,this.len=t.length}function a(){return e.Buffer?function(t){return(h.create=function(t){return e.Buffer.isBuffer(t)?new r(t):c(t)})(t)}:c}var f,c="undefined"!=typeof Uint8Array?function(t){if(t instanceof Uint8Array||Array.isArray(t))return new h(t);throw Error("illegal buffer")}:function(t){if(Array.isArray(t))return new h(t);throw Error("illegal buffer")};function l(){var t=new s(0,0),i=0;if(!(4<this.len-this.pos)){for(;i<3;++i){if(this.pos>=this.len)throw u(this);if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*i)>>>0,this.buf[this.pos++]<128)return t}return t.lo=(t.lo|(127&this.buf[this.pos++])<<7*i)>>>0,t}for(;i<4;++i)if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*i)>>>0,this.buf[this.pos++]<128)return t;if(t.lo=(t.lo|(127&this.buf[this.pos])<<28)>>>0,t.hi=(t.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return t;if(i=0,4<this.len-this.pos){for(;i<5;++i)if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*i+3)>>>0,this.buf[this.pos++]<128)return t}else for(;i<5;++i){if(this.pos>=this.len)throw u(this);if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*i+3)>>>0,this.buf[this.pos++]<128)return t}throw Error("invalid varint encoding")}function v(t,i){return(t[i-4]|t[i-3]<<8|t[i-2]<<16|t[i-1]<<24)>>>0}function d(){if(this.pos+8>this.len)throw u(this,8);return new s(v(this.buf,this.pos+=4),v(this.buf,this.pos+=4))}h.create=a(),h.prototype.T=e.Array.prototype.subarray||e.Array.prototype.slice,h.prototype.uint32=(f=4294967295,function(){if(f=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128)return f;if(f=(f|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)return f;if(f=(f|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)return f;if(f=(f|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)return f;if(f=(f|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)return f;if((this.pos+=5)>this.len)throw this.pos=this.len,u(this,10);return f}),h.prototype.int32=function(){return 0|this.uint32()},h.prototype.sint32=function(){var t=this.uint32();return t>>>1^-(1&t)|0},h.prototype.bool=function(){return 0!==this.uint32()},h.prototype.fixed32=function(){if(this.pos+4>this.len)throw u(this,4);return v(this.buf,this.pos+=4)},h.prototype.sfixed32=function(){if(this.pos+4>this.len)throw u(this,4);return 0|v(this.buf,this.pos+=4)},h.prototype.float=function(){if(this.pos+4>this.len)throw u(this,4);var t=e.float.readFloatLE(this.buf,this.pos);return this.pos+=4,t},h.prototype.double=function(){if(this.pos+8>this.len)throw u(this,4);var t=e.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,t},h.prototype.bytes=function(){var t=this.uint32(),i=this.pos,n=this.pos+t;if(n>this.len)throw u(this,t);return this.pos+=t,Array.isArray(this.buf)?this.buf.slice(i,n):i===n?new this.buf.constructor(0):this.T.call(this.buf,i,n)},h.prototype.string=function(){var t=this.bytes();return o.read(t,0,t.length)},h.prototype.skip=function(t){if("number"==typeof t){if(this.pos+t>this.len)throw u(this,t);this.pos+=t}else do{if(this.pos>=this.len)throw u(this)}while(128&this.buf[this.pos++]);return this},h.prototype.skipType=function(t){switch(t){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(t=7&this.uint32());)this.skipType(t);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+t+" at offset "+this.pos)}return this},h._=function(t){r=t,h.create=a(),r._();var i=e.Long?"toLong":"toNumber";e.merge(h.prototype,{int64:function(){return l.call(this)[i](!1)},uint64:function(){return l.call(this)[i](!0)},sint64:function(){return l.call(this).zzDecode()[i](!1)},fixed64:function(){return d.call(this)[i](!0)},sfixed64:function(){return d.call(this)[i](!1)}})}},function(t,i,n){"use strict";t.exports=o;var e=n(4);((o.prototype=Object.create(e.prototype)).constructor=o).className="OneOf";var r=n(5),s=n(0);function o(t,i,n,r){if(Array.isArray(i)||(n=i,i=void 0),e.call(this,t,n),void 0!==i&&!Array.isArray(i))throw TypeError("fieldNames must be an Array");this.oneof=i||[],this.fieldsArray=[],this.comment=r}function u(t){if(t.parent)for(var i=0;i<t.fieldsArray.length;++i)t.fieldsArray[i].parent||t.parent.add(t.fieldsArray[i])}o.fromJSON=function(t,i){return new o(t,i.oneof,i.options,i.comment)},o.prototype.toJSON=function(t){t=!!t&&Boolean(t.keepComments);return s.toObject(["options",this.options,"oneof",this.oneof,"comment",t?this.comment:void 0])},o.prototype.add=function(t){if(!(t instanceof r))throw TypeError("field must be a Field");return t.parent&&t.parent!==this.parent&&t.parent.remove(t),this.oneof.push(t.name),this.fieldsArray.push(t),u(t.partOf=this),this},o.prototype.remove=function(t){if(!(t instanceof r))throw TypeError("field must be a Field");var i=this.fieldsArray.indexOf(t);if(i<0)throw Error(t+" is not a member of "+this);return this.fieldsArray.splice(i,1),-1<(i=this.oneof.indexOf(t.name))&&this.oneof.splice(i,1),t.partOf=null,this},o.prototype.onAdd=function(t){e.prototype.onAdd.call(this,t);for(var i=0;i<this.oneof.length;++i){var n=t.get(this.oneof[i]);n&&!n.partOf&&(n.partOf=this).fieldsArray.push(n)}u(this)},o.prototype.onRemove=function(t){for(var i,n=0;n<this.fieldsArray.length;++n)(i=this.fieldsArray[n]).parent&&i.parent.remove(i);e.prototype.onRemove.call(this,t)},o.d=function(){for(var n=new Array(arguments.length),t=0;t<arguments.length;)n[t]=arguments[t++];return function(t,i){s.decorateType(t.constructor).add(new o(i,n)),Object.defineProperty(t,i,{get:s.oneOfGetter(n),set:s.oneOfSetter(n)})}}},function(t,i,n){"use strict";t.exports=e;var r=n(2);function e(t){if(t)for(var i=Object.keys(t),n=0;n<i.length;++n)this[i[n]]=t[i[n]]}e.create=function(t){return this.$type.create(t)},e.encode=function(t,i){return this.$type.encode(t,i)},e.encodeDelimited=function(t,i){return this.$type.encodeDelimited(t,i)},e.decode=function(t){return this.$type.decode(t)},e.decodeDelimited=function(t){return this.$type.decodeDelimited(t)},e.verify=function(t){return this.$type.verify(t)},e.fromObject=function(t){return this.$type.fromObject(t)},e.toObject=function(t,i){return this.$type.toObject(t,i)},e.prototype.toJSON=function(){return this.$type.toObject(this,r.toJSONOptions)}},function(t,i,n){"use strict";t.exports=function(t,i){var n=new Array(arguments.length-1),s=0,r=2,o=!0;for(;r<arguments.length;)n[s++]=arguments[r++];return new Promise(function(r,e){n[s]=function(t){if(o)if(o=!1,t)e(t);else{for(var i=new Array(arguments.length-1),n=0;n<i.length;)i[n++]=arguments[n];r.apply(null,i)}};try{t.apply(i||null,n)}catch(t){o&&(o=!1,e(t))}})}},function(module,exports,__webpack_require__){"use strict";function inquire(moduleName){try{var mod=eval("quire".replace(/^/,"re"))(moduleName);if(mod&&(mod.length||Object.keys(mod).length))return mod}catch(e){}return null}module.exports=inquire},function(t,i,n){"use strict";i.Service=n(39)},function(t,i,n){"use strict";t.exports={}},function(t,i,n){"use strict";t.exports=function(t){for(var i,n=c.codegen(["m","w"],t.name+"$encode")("if(!w)")("w=Writer.create()"),r=t.fieldsArray.slice().sort(c.compareFieldsById),e=0;e<r.length;++e){var s=r[e].resolve(),o=t.A.indexOf(s),u=s.resolvedType instanceof a?"int32":s.type,h=f.basic[u];i="m"+c.safeProp(s.name),s.map?(n("if(%s!=null&&Object.hasOwnProperty.call(m,%j)){",i,s.name)("for(var ks=Object.keys(%s),i=0;i<ks.length;++i){",i)("w.uint32(%i).fork().uint32(%i).%s(ks[i])",(s.id<<3|2)>>>0,8|f.mapKey[s.keyType],s.keyType),void 0===h?n("types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()",o,i):n(".uint32(%i).%s(%s[ks[i]]).ldelim()",16|h,u,i),n("}")("}")):s.repeated?(n("if(%s!=null&&%s.length){",i,i),s.packed&&void 0!==f.packed[u]?n("w.uint32(%i).fork()",(s.id<<3|2)>>>0)("for(var i=0;i<%s.length;++i)",i)("w.%s(%s[i])",u,i)("w.ldelim()"):(n("for(var i=0;i<%s.length;++i)",i),void 0===h?l(n,s,o,i+"[i]"):n("w.uint32(%i).%s(%s[i])",(s.id<<3|h)>>>0,u,i)),n("}")):(s.optional&&n("if(%s!=null&&Object.hasOwnProperty.call(m,%j))",i,s.name),void 0===h?l(n,s,o,i):n("w.uint32(%i).%s(%s)",(s.id<<3|h)>>>0,u,i))}return n("return w")};var a=n(3),f=n(7),c=n(0);function l(t,i,n,r){return i.resolvedType.group?t("types[%i].encode(%s,w.uint32(%i)).uint32(%i)",n,r,(i.id<<3|3)>>>0,(i.id<<3|4)>>>0):t("types[%i].encode(%s,w.uint32(%i).fork()).ldelim()",n,r,(i.id<<3|2)>>>0)}},function(t,i,n){"use strict";t.exports=m;var o=n(6);((m.prototype=Object.create(o.prototype)).constructor=m).className="Type";var u=n(3),h=n(11),a=n(5),f=n(19),c=n(20),e=n(12),s=n(10),l=n(9),v=n(0),d=n(17),y=n(22),b=n(23),p=n(24),x=n(25);function m(t,i){o.call(this,t,i),this.fields={},this.oneofs=void 0,this.extensions=void 0,this.reserved=void 0,this.group=void 0,this.S=null,this.A=null,this.N=null,this.I=null}function r(t){return t.S=t.A=t.N=null,delete t.encode,delete t.decode,delete t.verify,t}Object.defineProperties(m.prototype,{fieldsById:{get:function(){if(this.S)return this.S;this.S={};for(var t=Object.keys(this.fields),i=0;i<t.length;++i){var n=this.fields[t[i]],r=n.id;if(this.S[r])throw Error("duplicate id "+r+" in "+this);this.S[r]=n}return this.S}},fieldsArray:{get:function(){return this.A||(this.A=v.toArray(this.fields))}},oneofsArray:{get:function(){return this.N||(this.N=v.toArray(this.oneofs))}},ctor:{get:function(){return this.I||(this.ctor=m.generateConstructor(this)())},set:function(t){var i=t.prototype;i instanceof e||((t.prototype=new e).constructor=t,v.merge(t.prototype,i)),t.$type=t.prototype.$type=this,v.merge(t,e,!0),this.I=t;for(var n=0;n<this.fieldsArray.length;++n)this.A[n].resolve();for(var r={},n=0;n<this.oneofsArray.length;++n)r[this.N[n].resolve().name]={get:v.oneOfGetter(this.N[n].oneof),set:v.oneOfSetter(this.N[n].oneof)};n&&Object.defineProperties(t.prototype,r)}}}),m.generateConstructor=function(t){for(var i,n=v.codegen(["p"],t.name),r=0;r<t.fieldsArray.length;++r)(i=t.A[r]).map?n("this%s={}",v.safeProp(i.name)):i.repeated&&n("this%s=[]",v.safeProp(i.name));return n("if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)")("this[ks[i]]=p[ks[i]]")},m.fromJSON=function(t,i){var n=new m(t,i.options);n.extensions=i.extensions,n.reserved=i.reserved;for(var r=Object.keys(i.fields),e=0;e<r.length;++e)n.add((void 0!==i.fields[r[e]].keyType?f:a).fromJSON(r[e],i.fields[r[e]]));if(i.oneofs)for(r=Object.keys(i.oneofs),e=0;e<r.length;++e)n.add(h.fromJSON(r[e],i.oneofs[r[e]]));if(i.nested)for(r=Object.keys(i.nested),e=0;e<r.length;++e){var s=i.nested[r[e]];n.add((void 0!==s.id?a:void 0!==s.fields?m:void 0!==s.values?u:void 0!==s.methods?c:o).fromJSON(r[e],s))}return i.extensions&&i.extensions.length&&(n.extensions=i.extensions),i.reserved&&i.reserved.length&&(n.reserved=i.reserved),i.group&&(n.group=!0),i.comment&&(n.comment=i.comment),n},m.prototype.toJSON=function(t){var i=o.prototype.toJSON.call(this,t),n=!!t&&Boolean(t.keepComments);return v.toObject(["options",i&&i.options||void 0,"oneofs",o.arrayToJSON(this.oneofsArray,t),"fields",o.arrayToJSON(this.fieldsArray.filter(function(t){return!t.declaringField}),t)||{},"extensions",this.extensions&&this.extensions.length?this.extensions:void 0,"reserved",this.reserved&&this.reserved.length?this.reserved:void 0,"group",this.group||void 0,"nested",i&&i.nested||void 0,"comment",n?this.comment:void 0])},m.prototype.resolveAll=function(){for(var t=this.fieldsArray,i=0;i<t.length;)t[i++].resolve();for(var n=this.oneofsArray,i=0;i<n.length;)n[i++].resolve();return o.prototype.resolveAll.call(this)},m.prototype.get=function(t){return this.fields[t]||this.oneofs&&this.oneofs[t]||this.nested&&this.nested[t]||null},m.prototype.add=function(t){if(this.get(t.name))throw Error("duplicate name '"+t.name+"' in "+this);if(t instanceof a&&void 0===t.extend){if((this.S||this.fieldsById)[t.id])throw Error("duplicate id "+t.id+" in "+this);if(this.isReservedId(t.id))throw Error("id "+t.id+" is reserved in "+this);if(this.isReservedName(t.name))throw Error("name '"+t.name+"' is reserved in "+this);return t.parent&&t.parent.remove(t),(this.fields[t.name]=t).message=this,t.onAdd(this),r(this)}return t instanceof h?(this.oneofs||(this.oneofs={}),(this.oneofs[t.name]=t).onAdd(this),r(this)):o.prototype.add.call(this,t)},m.prototype.remove=function(t){if(t instanceof a&&void 0===t.extend){if(!this.fields||this.fields[t.name]!==t)throw Error(t+" is not a member of "+this);return delete this.fields[t.name],t.parent=null,t.onRemove(this),r(this)}if(t instanceof h){if(!this.oneofs||this.oneofs[t.name]!==t)throw Error(t+" is not a member of "+this);return delete this.oneofs[t.name],t.parent=null,t.onRemove(this),r(this)}return o.prototype.remove.call(this,t)},m.prototype.isReservedId=function(t){return o.isReservedId(this.reserved,t)},m.prototype.isReservedName=function(t){return o.isReservedName(this.reserved,t)},m.prototype.create=function(t){return new this.ctor(t)},m.prototype.setup=function(){for(var t=this.fullName,i=[],n=0;n<this.fieldsArray.length;++n)i.push(this.A[n].resolve().resolvedType);this.encode=d(this)({Writer:l,types:i,util:v}),this.decode=y(this)({Reader:s,types:i,util:v}),this.verify=b(this)({types:i,util:v}),this.fromObject=p.fromObject(this)({types:i,util:v}),this.toObject=p.toObject(this)({types:i,util:v});var r=x[t];return r&&((t=Object.create(this)).fromObject=this.fromObject,this.fromObject=r.fromObject.bind(t),t.toObject=this.toObject,this.toObject=r.toObject.bind(t)),this},m.prototype.encode=function(t,i){return this.setup().encode(t,i)},m.prototype.encodeDelimited=function(t,i){return this.encode(t,i&&i.len?i.fork():i).ldelim()},m.prototype.decode=function(t,i){return this.setup().decode(t,i)},m.prototype.decodeDelimited=function(t){return t instanceof s||(t=s.create(t)),this.decode(t,t.uint32())},m.prototype.verify=function(t){return this.setup().verify(t)},m.prototype.fromObject=function(t){return this.setup().fromObject(t)},m.prototype.toObject=function(t,i){return this.setup().toObject(t,i)},m.d=function(i){return function(t){v.decorateType(t,i)}}},function(t,i,n){"use strict";t.exports=s;var o=n(5);((s.prototype=Object.create(o.prototype)).constructor=s).className="MapField";var r=n(7),u=n(0);function s(t,i,n,r,e,s){if(o.call(this,t,i,r,void 0,void 0,e,s),!u.isString(n))throw TypeError("keyType must be a string");this.keyType=n,this.resolvedKeyType=null,this.map=!0}s.fromJSON=function(t,i){return new s(t,i.id,i.keyType,i.type,i.options,i.comment)},s.prototype.toJSON=function(t){t=!!t&&Boolean(t.keepComments);return u.toObject(["keyType",this.keyType,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",t?this.comment:void 0])},s.prototype.resolve=function(){if(this.resolved)return this;if(void 0===r.mapKey[this.keyType])throw Error("invalid key type: "+this.keyType);return o.prototype.resolve.call(this)},s.d=function(n,r,e){return"function"==typeof e?e=u.decorateType(e).name:e&&"object"==typeof e&&(e=u.decorateEnum(e).name),function(t,i){u.decorateType(t.constructor).add(new s(i,n,r,e))}}},function(t,i,n){"use strict";t.exports=o;var r=n(6);((o.prototype=Object.create(r.prototype)).constructor=o).className="Service";var s=n(21),u=n(0),h=n(15);function o(t,i){r.call(this,t,i),this.methods={},this.L=null}function e(t){return t.L=null,t}o.fromJSON=function(t,i){var n=new o(t,i.options);if(i.methods)for(var r=Object.keys(i.methods),e=0;e<r.length;++e)n.add(s.fromJSON(r[e],i.methods[r[e]]));return i.nested&&n.addJSON(i.nested),n.comment=i.comment,n},o.prototype.toJSON=function(t){var i=r.prototype.toJSON.call(this,t),n=!!t&&Boolean(t.keepComments);return u.toObject(["options",i&&i.options||void 0,"methods",r.arrayToJSON(this.methodsArray,t)||{},"nested",i&&i.nested||void 0,"comment",n?this.comment:void 0])},Object.defineProperty(o.prototype,"methodsArray",{get:function(){return this.L||(this.L=u.toArray(this.methods))}}),o.prototype.get=function(t){return this.methods[t]||r.prototype.get.call(this,t)},o.prototype.resolveAll=function(){for(var t=this.methodsArray,i=0;i<t.length;++i)t[i].resolve();return r.prototype.resolve.call(this)},o.prototype.add=function(t){if(this.get(t.name))throw Error("duplicate name '"+t.name+"' in "+this);return t instanceof s?e((this.methods[t.name]=t).parent=this):r.prototype.add.call(this,t)},o.prototype.remove=function(t){if(t instanceof s){if(this.methods[t.name]!==t)throw Error(t+" is not a member of "+this);return delete this.methods[t.name],t.parent=null,e(this)}return r.prototype.remove.call(this,t)},o.prototype.create=function(t,i,n){for(var r,e=new h.Service(t,i,n),s=0;s<this.methodsArray.length;++s){var o=u.lcFirst((r=this.L[s]).resolve().name).replace(/[^$\w_]/g,"");e[o]=u.codegen(["r","c"],u.isReserved(o)?o+"_":o)("return this.rpcCall(m,q,s,r,c)")({m:r,q:r.resolvedRequestType.ctor,s:r.resolvedResponseType.ctor})}return e}},function(t,i,n){"use strict";t.exports=r;var a=n(4);((r.prototype=Object.create(a.prototype)).constructor=r).className="Method";var f=n(0);function r(t,i,n,r,e,s,o,u,h){if(f.isObject(e)?(o=e,e=s=void 0):f.isObject(s)&&(o=s,s=void 0),void 0!==i&&!f.isString(i))throw TypeError("type must be a string");if(!f.isString(n))throw TypeError("requestType must be a string");if(!f.isString(r))throw TypeError("responseType must be a string");a.call(this,t,o),this.type=i||"rpc",this.requestType=n,this.requestStream=!!e||void 0,this.responseType=r,this.responseStream=!!s||void 0,this.resolvedRequestType=null,this.resolvedResponseType=null,this.comment=u,this.parsedOptions=h}r.fromJSON=function(t,i){return new r(t,i.type,i.requestType,i.responseType,i.requestStream,i.responseStream,i.options,i.comment,i.parsedOptions)},r.prototype.toJSON=function(t){t=!!t&&Boolean(t.keepComments);return f.toObject(["type","rpc"!==this.type&&this.type||void 0,"requestType",this.requestType,"requestStream",this.requestStream,"responseType",this.responseType,"responseStream",this.responseStream,"options",this.options,"comment",t?this.comment:void 0,"parsedOptions",this.parsedOptions])},r.prototype.resolve=function(){return this.resolved?this:(this.resolvedRequestType=this.parent.lookupType(this.requestType),this.resolvedResponseType=this.parent.lookupType(this.responseType),a.prototype.resolve.call(this))}},function(t,i,n){"use strict";t.exports=function(t){var i=a.codegen(["r","l"],t.name+"$decode")("if(!(r instanceof Reader))")("r=Reader.create(r)")("var c=l===undefined?r.len:r.pos+l,m=new this.ctor"+(t.fieldsArray.filter(function(t){return t.map}).length?",k,value":""))("while(r.pos<c){")("var t=r.uint32()");t.group&&i("if((t&7)===4)")("break");i("switch(t>>>3){");for(var n=0;n<t.fieldsArray.length;++n){var r=t.A[n].resolve(),e=r.resolvedType instanceof u?"int32":r.type,s="m"+a.safeProp(r.name);i("case %i:",r.id),r.map?(i("if(%s===util.emptyObject)",s)("%s={}",s)("var c2 = r.uint32()+r.pos"),void 0!==h.defaults[r.keyType]?i("k=%j",h.defaults[r.keyType]):i("k=null"),void 0!==h.defaults[e]?i("value=%j",h.defaults[e]):i("value=null"),i("while(r.pos<c2){")("var tag2=r.uint32()")("switch(tag2>>>3){")("case 1: k=r.%s(); break",r.keyType)("case 2:"),void 0===h.basic[e]?i("value=types[%i].decode(r,r.uint32())",n):i("value=r.%s()",e),i("break")("default:")("r.skipType(tag2&7)")("break")("}")("}"),void 0!==h.long[r.keyType]?i('%s[typeof k==="object"?util.longToHash(k):k]=value',s):i("%s[k]=value",s)):r.repeated?(i("if(!(%s&&%s.length))",s,s)("%s=[]",s),void 0!==h.packed[e]&&i("if((t&7)===2){")("var c2=r.uint32()+r.pos")("while(r.pos<c2)")("%s.push(r.%s())",s,e)("}else"),void 0===h.basic[e]?i(r.resolvedType.group?"%s.push(types[%i].decode(r))":"%s.push(types[%i].decode(r,r.uint32()))",s,n):i("%s.push(r.%s())",s,e)):void 0===h.basic[e]?i(r.resolvedType.group?"%s=types[%i].decode(r)":"%s=types[%i].decode(r,r.uint32())",s,n):i("%s=r.%s()",s,e),i("break")}for(i("default:")("r.skipType(t&7)")("break")("}")("}"),n=0;n<t.A.length;++n){var o=t.A[n];o.required&&i("if(!m.hasOwnProperty(%j))",o.name)("throw util.ProtocolError(%j,{instance:m})","missing required '"+o.name+"'")}return i("return m")};var u=n(3),h=n(7),a=n(0)},function(t,i,n){"use strict";t.exports=function(t){var i=h.codegen(["m"],t.name+"$verify")('if(typeof m!=="object"||m===null)')("return%j","object expected"),n=t.oneofsArray,r={};n.length&&i("var p={}");for(var e=0;e<t.fieldsArray.length;++e){var s,o=t.A[e].resolve(),u="m"+h.safeProp(o.name);o.optional&&i("if(%s!=null&&m.hasOwnProperty(%j)){",u,o.name),o.map?(i("if(!util.isObject(%s))",u)("return%j",a(o,"object"))("var k=Object.keys(%s)",u)("for(var i=0;i<k.length;++i){"),function(t,i,n){switch(i.keyType){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":t("if(!util.key32Re.test(%s))",n)("return%j",a(i,"integer key"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":t("if(!util.key64Re.test(%s))",n)("return%j",a(i,"integer|Long key"));break;case"bool":t("if(!util.key2Re.test(%s))",n)("return%j",a(i,"boolean key"))}}(i,o,"k[i]"),f(i,o,e,u+"[k[i]]")("}")):o.repeated?(i("if(!Array.isArray(%s))",u)("return%j",a(o,"array"))("for(var i=0;i<%s.length;++i){",u),f(i,o,e,u+"[i]")("}")):(o.partOf&&(s=h.safeProp(o.partOf.name),1===r[o.partOf.name]&&i("if(p%s===1)",s)("return%j",o.partOf.name+": multiple values"),r[o.partOf.name]=1,i("p%s=1",s)),f(i,o,e,u)),o.optional&&i("}")}return i("return null")};var o=n(3),h=n(0);function a(t,i){return t.name+": "+i+(t.repeated&&"array"!==i?"[]":t.map&&"object"!==i?"{k:"+t.keyType+"}":"")+" expected"}function f(t,i,n,r){if(i.resolvedType)if(i.resolvedType instanceof o){t("switch(%s){",r)("default:")("return%j",a(i,"enum value"));for(var e=Object.keys(i.resolvedType.values),s=0;s<e.length;++s)t("case %i:",i.resolvedType.values[e[s]]);t("break")("}")}else t("{")("var e=types[%i].verify(%s);",n,r)("if(e)")("return%j+e",i.name+".")("}");else switch(i.type){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":t("if(!util.isInteger(%s))",r)("return%j",a(i,"integer"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":t("if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))",r,r,r,r)("return%j",a(i,"integer|Long"));break;case"float":case"double":t('if(typeof %s!=="number")',r)("return%j",a(i,"number"));break;case"bool":t('if(typeof %s!=="boolean")',r)("return%j",a(i,"boolean"));break;case"string":t("if(!util.isString(%s))",r)("return%j",a(i,"string"));break;case"bytes":t('if(!(%s&&typeof %s.length==="number"||util.isString(%s)))',r,r,r)("return%j",a(i,"buffer"))}return t}},function(t,i,n){"use strict";var i=i,l=n(3),v=n(0);function o(t,i,n,r){if(i.resolvedType)if(i.resolvedType instanceof l){t("switch(d%s){",r);for(var e=i.resolvedType.values,s=Object.keys(e),o=0;o<s.length;++o)i.repeated&&e[s[o]]===i.typeDefault&&t("default:"),t("case%j:",s[o])("case %i:",e[s[o]])("m%s=%j",r,e[s[o]])("break");t("}")}else t('if(typeof d%s!=="object")',r)("throw TypeError(%j)",i.fullName+": object expected")("m%s=types[%i].fromObject(d%s)",r,n,r);else{var u=!1;switch(i.type){case"double":case"float":t("m%s=Number(d%s)",r,r);break;case"uint32":case"fixed32":t("m%s=d%s>>>0",r,r);break;case"int32":case"sint32":case"sfixed32":t("m%s=d%s|0",r,r);break;case"uint64":u=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":t("if(util.Long)")("(m%s=util.Long.fromValue(d%s)).unsigned=%j",r,r,u)('else if(typeof d%s==="string")',r)("m%s=parseInt(d%s,10)",r,r)('else if(typeof d%s==="number")',r)("m%s=d%s",r,r)('else if(typeof d%s==="object")',r)("m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)",r,r,r,u?"true":"");break;case"bytes":t('if(typeof d%s==="string")',r)("util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)",r,r,r)("else if(d%s.length)",r)("m%s=d%s",r,r);break;case"string":t("m%s=String(d%s)",r,r);break;case"bool":t("m%s=Boolean(d%s)",r,r)}}return t}function d(t,i,n,r){if(i.resolvedType)i.resolvedType instanceof l?t("d%s=o.enums===String?types[%i].values[m%s]:m%s",r,n,r,r):t("d%s=types[%i].toObject(m%s,o)",r,n,r);else{var e=!1;switch(i.type){case"double":case"float":t("d%s=o.json&&!isFinite(m%s)?String(m%s):m%s",r,r,r,r);break;case"uint64":e=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":t('if(typeof m%s==="number")',r)("d%s=o.longs===String?String(m%s):m%s",r,r,r)("else")("d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s",r,r,r,r,e?"true":"",r);break;case"bytes":t("d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s",r,r,r,r,r);break;default:t("d%s=m%s",r,r)}}return t}i.fromObject=function(t){var i=t.fieldsArray,n=v.codegen(["d"],t.name+"$fromObject")("if(d instanceof this.ctor)")("return d");if(!i.length)return n("return new this.ctor");n("var m=new this.ctor");for(var r=0;r<i.length;++r){var e=i[r].resolve(),s=v.safeProp(e.name);e.map?(n("if(d%s){",s)('if(typeof d%s!=="object")',s)("throw TypeError(%j)",e.fullName+": object expected")("m%s={}",s)("for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){",s),o(n,e,r,s+"[ks[i]]")("}")("}")):e.repeated?(n("if(d%s){",s)("if(!Array.isArray(d%s))",s)("throw TypeError(%j)",e.fullName+": array expected")("m%s=[]",s)("for(var i=0;i<d%s.length;++i){",s),o(n,e,r,s+"[i]")("}")("}")):(e.resolvedType instanceof l||n("if(d%s!=null){",s),o(n,e,r,s),e.resolvedType instanceof l||n("}"))}return n("return m")},i.toObject=function(t){var i=t.fieldsArray.slice().sort(v.compareFieldsById);if(!i.length)return v.codegen()("return {}");for(var n=v.codegen(["m","o"],t.name+"$toObject")("if(!o)")("o={}")("var d={}"),r=[],e=[],s=[],o=0;o<i.length;++o)i[o].partOf||(i[o].resolve().repeated?r:i[o].map?e:s).push(i[o]);if(r.length){for(n("if(o.arrays||o.defaults){"),o=0;o<r.length;++o)n("d%s=[]",v.safeProp(r[o].name));n("}")}if(e.length){for(n("if(o.objects||o.defaults){"),o=0;o<e.length;++o)n("d%s={}",v.safeProp(e[o].name));n("}")}if(s.length){for(n("if(o.defaults){"),o=0;o<s.length;++o){var u,h=s[o],a=v.safeProp(h.name);h.resolvedType instanceof l?n("d%s=o.enums===String?%j:%j",a,h.resolvedType.valuesById[h.typeDefault],h.typeDefault):h.long?n("if(util.Long){")("var n=new util.Long(%i,%i,%j)",h.typeDefault.low,h.typeDefault.high,h.typeDefault.unsigned)("d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n",a)("}else")("d%s=o.longs===String?%j:%i",a,h.typeDefault.toString(),h.typeDefault.toNumber()):h.bytes?(u="["+Array.prototype.slice.call(h.typeDefault).join(",")+"]",n("if(o.bytes===String)d%s=%j",a,String.fromCharCode.apply(String,h.typeDefault))("else{")("d%s=%s",a,u)("if(o.bytes!==Array)d%s=util.newBuffer(d%s)",a,a)("}")):n("d%s=%j",a,h.typeDefault)}n("}")}for(var f=!1,o=0;o<i.length;++o){var h=i[o],c=t.A.indexOf(h),a=v.safeProp(h.name);h.map?(f||(f=!0,n("var ks2")),n("if(m%s&&(ks2=Object.keys(m%s)).length){",a,a)("d%s={}",a)("for(var j=0;j<ks2.length;++j){"),d(n,h,c,a+"[ks2[j]]")("}")):h.repeated?(n("if(m%s&&m%s.length){",a,a)("d%s=[]",a)("for(var j=0;j<m%s.length;++j){",a),d(n,h,c,a+"[j]")("}")):(n("if(m%s!=null&&m.hasOwnProperty(%j)){",a,h.name),d(n,h,c,a),h.partOf&&n("if(o.oneofs)")("d%s=%j",v.safeProp(h.partOf.name),h.name)),n("}")}return n("return d")}},function(t,i,n){"use strict";var i=i,s=n(12);i[".google.protobuf.Any"]={fromObject:function(t){if(t&&t["@type"]){var i=t["@type"].substring(t["@type"].lastIndexOf("/")+1),n=this.lookup(i);if(n){i="."===t["@type"].charAt(0)?t["@type"].substr(1):t["@type"];return-1===i.indexOf("/")&&(i="/"+i),this.create({type_url:i,value:n.encode(n.fromObject(t)).finish()})}}return this.fromObject(t)},toObject:function(t,i){var n,r="",e="";if(i&&i.json&&t.type_url&&t.value&&(e=t.type_url.substring(t.type_url.lastIndexOf("/")+1),r=t.type_url.substring(0,t.type_url.lastIndexOf("/")+1),(n=this.lookup(e))&&(t=n.decode(t.value))),t instanceof this.ctor||!(t instanceof s))return this.toObject(t,i);i=t.$type.toObject(t,i),t="."===t.$type.fullName[0]?t.$type.fullName.substr(1):t.$type.fullName;return i["@type"]=e=(r=""===r?"type.googleapis.com/":r)+t,i}}},function(t,i,n){"use strict";t.exports=h;var r=n(6);((h.prototype=Object.create(r.prototype)).constructor=h).className="Root";var e,v,d,s=n(5),o=n(3),u=n(11),y=n(0);function h(t){r.call(this,"",t),this.deferred=[],this.files=[]}function b(){}h.fromJSON=function(t,i){return i=i||new h,t.options&&i.setOptions(t.options),i.addJSON(t.nested)},h.prototype.resolvePath=y.path.resolve,h.prototype.fetch=y.fetch,h.prototype.load=function t(i,s,e){"function"==typeof s&&(e=s,s=void 0);var o=this;if(!e)return y.asPromise(t,o,i,s);var u=e===b;function h(t,i){if(e){var n=e;if(e=null,u)throw t;n(t,i)}}function a(t){var i=t.lastIndexOf("google/protobuf/");if(-1<i){i=t.substring(i);if(i in d)return i}return null}function f(t,i){try{if(y.isString(i)&&"{"===i.charAt(0)&&(i=JSON.parse(i)),y.isString(i)){v.filename=t;var n,r=v(i,o,s),e=0;if(r.imports)for(;e<r.imports.length;++e)(n=a(r.imports[e])||o.resolvePath(t,r.imports[e]))&&c(n);if(r.weakImports)for(e=0;e<r.weakImports.length;++e)(n=a(r.weakImports[e])||o.resolvePath(t,r.weakImports[e]))&&c(n,!0)}else o.setOptions(i.options).addJSON(i.nested)}catch(t){h(t)}u||l||h(null,o)}function c(n,r){if(!(-1<o.files.indexOf(n)))if(o.files.push(n),n in d)u?f(n,d[n]):(++l,setTimeout(function(){--l,f(n,d[n])}));else if(u){var t;try{t=y.fs.readFileSync(n).toString("utf8")}catch(t){return void(r||h(t))}f(n,t)}else++l,o.fetch(n,function(t,i){--l,e&&(t?r?l||h(null,o):h(t):f(n,i))})}var l=0;y.isString(i)&&(i=[i]);for(var n,r=0;r<i.length;++r)(n=o.resolvePath("",i[r]))&&c(n);if(u)return o;l||h(null,o)},h.prototype.loadSync=function(t,i){if(!y.isNode)throw Error("not supported");return this.load(t,i,b)},h.prototype.resolveAll=function(){if(this.deferred.length)throw Error("unresolvable extensions: "+this.deferred.map(function(t){return"'extend "+t.extend+"' in "+t.parent.fullName}).join(", "));return r.prototype.resolveAll.call(this)};var a=/^[A-Z]/;function f(t,i){var n=i.parent.lookup(i.extend);if(n){var r=new s(i.fullName,i.id,i.type,i.rule,void 0,i.options);return(r.declaringField=i).extensionField=r,n.add(r),1}}h.prototype.O=function(t){if(t instanceof s)void 0===t.extend||t.extensionField||f(0,t)||this.deferred.push(t);else if(t instanceof o)a.test(t.name)&&(t.parent[t.name]=t.values);else if(!(t instanceof u)){if(t instanceof e)for(var i=0;i<this.deferred.length;)f(0,this.deferred[i])?this.deferred.splice(i,1):++i;for(var n=0;n<t.nestedArray.length;++n)this.O(t.k[n]);a.test(t.name)&&(t.parent[t.name]=t)}},h.prototype.M=function(t){var i;if(t instanceof s)void 0!==t.extend&&(t.extensionField?(t.extensionField.parent.remove(t.extensionField),t.extensionField=null):-1<(i=this.deferred.indexOf(t))&&this.deferred.splice(i,1));else if(t instanceof o)a.test(t.name)&&delete t.parent[t.name];else if(t instanceof r){for(var n=0;n<t.nestedArray.length;++n)this.M(t.k[n]);a.test(t.name)&&delete t.parent[t.name]}},h._=function(t,i,n){e=t,v=i,d=n}},function(t,i,n){"use strict";t.exports=n(29)},function(t,i,n){"use strict";!function(s){function e(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var o=n(47),t=(n(48),function(){function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.D=null,this.P=400,this.F=null,this.C=null,this.U=new Map}var i,n,r;return i=t,(n=[{key:"url",get:function(){return this.D}},{key:"status",get:function(){return this.P}},{key:"responseType",get:function(){return this.F},set:function(t){this.F=t}},{key:"response",get:function(){return this.C}},{key:"open",value:function(t,i){"GET"==t&&(this.D=i)}},{key:"addEventListener",value:function(t,i){var n=this.U.get(t);n||this.U.set(t,n=[]),n.push(i)}},{key:"send",value:function(){var i=this,t=this.D,n=o.createReadStream(t),r=[],e=0;this.C=null,n.on("readable",function(){var t=n.read();null!=t&&(r.push(t),e+=t.length,i.C?i.C=s.concat([i.C,new s(t)],e):i.C=new s(t))}),n.on("end",function(){i.P=200,i.B("load")}),n.on("error",function(t){i.P=400,i.B("error",t)})}}])&&e(i.prototype,n),r&&e(i,r),t}());Object.assign(t.prototype,{B:function(t,i){if(0!=this.U.size){var n=this.U.get(t);if(n)for(var r=0;r<n.length;r++)n[r]&&n[r](i)}}}),i.a=t}.call(this,n(43).Buffer)},function(t,i,n){"use strict";var r=t.exports=n(30);r.build="light",r.load=function(t,i,n){return(i="function"==typeof i?(n=i,new r.Root):i||new r.Root).load(t,n)},r.loadSync=function(t,i){return(i=i||new r.Root).loadSync(t)},r.encoder=n(17),r.decoder=n(22),r.verifier=n(23),r.converter=n(24),r.ReflectionObject=n(4),r.Namespace=n(6),r.Root=n(26),r.Enum=n(3),r.Type=n(18),r.Field=n(5),r.OneOf=n(11),r.MapField=n(19),r.Service=n(20),r.Method=n(21),r.Message=n(12),r.wrappers=n(25),r.types=n(7),r.util=n(0),r.ReflectionObject._(r.Root),r.Namespace._(r.Type,r.Service,r.Enum),r.Root._(r.Type),r.Field._(r.Type)},function(t,i,n){"use strict";var r=i;function e(){r.util._(),r.Writer._(r.BufferWriter),r.Reader._(r.BufferReader)}r.build="minimal",r.Writer=n(9),r.BufferWriter=n(37),r.Reader=n(10),r.BufferReader=n(38),r.util=n(2),r.rpc=n(15),r.roots=n(16),r.configure=e,e()},function(t,i,n){"use strict";i.length=function(t){var i=t.length;if(!i)return 0;for(var n=0;1<--i%4&&"="===t.charAt(i);)++n;return Math.ceil(3*t.length)/4-n};for(var a=new Array(64),h=new Array(123),r=0;r<64;)h[a[r]=r<26?r+65:r<52?r+71:r<62?r-4:r-59|43]=r++;i.encode=function(t,i,n){for(var r,e=null,s=[],o=0,u=0;i<n;){var h=t[i++];switch(u){case 0:s[o++]=a[h>>2],r=(3&h)<<4,u=1;break;case 1:s[o++]=a[r|h>>4],r=(15&h)<<2,u=2;break;case 2:s[o++]=a[r|h>>6],s[o++]=a[63&h],u=0}8191<o&&((e=e||[]).push(String.fromCharCode.apply(String,s)),o=0)}return u&&(s[o++]=a[r],s[o++]=61,1===u&&(s[o++]=61)),e?(o&&e.push(String.fromCharCode.apply(String,s.slice(0,o))),e.join("")):String.fromCharCode.apply(String,s.slice(0,o))};var f="invalid encoding";i.decode=function(t,i,n){for(var r,e=n,s=0,o=0;o<t.length;){var u=t.charCodeAt(o++);if(61===u&&1<s)break;if(void 0===(u=h[u]))throw Error(f);switch(s){case 0:r=u,s=1;break;case 1:i[n++]=r<<2|(48&u)>>4,r=u,s=2;break;case 2:i[n++]=(15&r)<<4|(60&u)>>2,r=u,s=3;break;case 3:i[n++]=(3&r)<<6|u,s=0}}if(1===s)throw Error(f);return n-e},i.test=function(t){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(t)}},function(t,i,n){"use strict";function r(){this.U={}}(t.exports=r).prototype.on=function(t,i,n){return(this.U[t]||(this.U[t]=[])).push({fn:i,ctx:n||this}),this},r.prototype.off=function(t,i){if(void 0===t)this.U={};else if(void 0===i)this.U[t]=[];else for(var n=this.U[t],r=0;r<n.length;)n[r].fn===i?n.splice(r,1):++r;return this},r.prototype.emit=function(t){var i=this.U[t];if(i){for(var n=[],r=1;r<arguments.length;)n.push(arguments[r++]);for(r=0;r<i.length;)i[r].fn.apply(i[r++].ctx,n)}return this}},function(t,i,n){"use strict";function r(t){function i(t,i,n,r){var e=i<0?1:0;0===(i=e?-i:i)?t(0<1/i?0:2147483648,n,r):isNaN(i)?t(2143289344,n,r):t(34028234663852886e22<i?(e<<31|2139095040)>>>0:i<11754943508222875e-54?(e<<31|Math.round(i/1401298464324817e-60))>>>0:(e<<31|(e=Math.floor(Math.log(i)/Math.LN2))+127<<23|8388607&Math.round(i*Math.pow(2,-e)*8388608))>>>0,n,r)}function n(t,i,n){t=t(i,n),i=2*(t>>31)+1,n=t>>>23&255,t&=8388607;return 255==n?t?NaN:1/0*i:0==n?1401298464324817e-60*i*t:i*Math.pow(2,n-150)*(8388608+t)}function r(t,i,n){u[0]=t,i[n]=h[0],i[n+1]=h[1],i[n+2]=h[2],i[n+3]=h[3]}function e(t,i,n){u[0]=t,i[n]=h[3],i[n+1]=h[2],i[n+2]=h[1],i[n+3]=h[0]}function s(t,i){return h[0]=t[i],h[1]=t[i+1],h[2]=t[i+2],h[3]=t[i+3],u[0]}function o(t,i){return h[3]=t[i],h[2]=t[i+1],h[1]=t[i+2],h[0]=t[i+3],u[0]}var u,h,a,f,c;function l(t,i,n,r,e,s){var o,u,h=r<0?1:0;0===(r=h?-r:r)?(t(0,e,s+i),t(0<1/r?0:2147483648,e,s+n)):isNaN(r)?(t(0,e,s+i),t(2146959360,e,s+n)):17976931348623157e292<r?(t(0,e,s+i),t((h<<31|2146435072)>>>0,e,s+n)):r<22250738585072014e-324?(t((o=r/5e-324)>>>0,e,s+i),t((h<<31|o/4294967296)>>>0,e,s+n)):(1024===(u=Math.floor(Math.log(r)/Math.LN2))&&(u=1023),t(4503599627370496*(o=r*Math.pow(2,-u))>>>0,e,s+i),t((h<<31|u+1023<<20|1048576*o&1048575)>>>0,e,s+n))}function v(t,i,n,r,e){i=t(r,e+i),r=t(r,e+n),e=2*(r>>31)+1,n=r>>>20&2047,i=4294967296*(1048575&r)+i;return 2047==n?i?NaN:1/0*e:0==n?5e-324*e*i:e*Math.pow(2,n-1075)*(i+4503599627370496)}function d(t,i,n){a[0]=t,i[n]=f[0],i[n+1]=f[1],i[n+2]=f[2],i[n+3]=f[3],i[n+4]=f[4],i[n+5]=f[5],i[n+6]=f[6],i[n+7]=f[7]}function y(t,i,n){a[0]=t,i[n]=f[7],i[n+1]=f[6],i[n+2]=f[5],i[n+3]=f[4],i[n+4]=f[3],i[n+5]=f[2],i[n+6]=f[1],i[n+7]=f[0]}function b(t,i){return f[0]=t[i],f[1]=t[i+1],f[2]=t[i+2],f[3]=t[i+3],f[4]=t[i+4],f[5]=t[i+5],f[6]=t[i+6],f[7]=t[i+7],a[0]}function p(t,i){return f[7]=t[i],f[6]=t[i+1],f[5]=t[i+2],f[4]=t[i+3],f[3]=t[i+4],f[2]=t[i+5],f[1]=t[i+6],f[0]=t[i+7],a[0]}return"undefined"!=typeof Float32Array?(u=new Float32Array([-0]),h=new Uint8Array(u.buffer),c=128===h[3],t.writeFloatLE=c?r:e,t.writeFloatBE=c?e:r,t.readFloatLE=c?s:o,t.readFloatBE=c?o:s):(t.writeFloatLE=i.bind(null,x),t.writeFloatBE=i.bind(null,m),t.readFloatLE=n.bind(null,w),t.readFloatBE=n.bind(null,_)),"undefined"!=typeof Float64Array?(a=new Float64Array([-0]),f=new Uint8Array(a.buffer),c=128===f[7],t.writeDoubleLE=c?d:y,t.writeDoubleBE=c?y:d,t.readDoubleLE=c?b:p,t.readDoubleBE=c?p:b):(t.writeDoubleLE=l.bind(null,x,0,4),t.writeDoubleBE=l.bind(null,m,4,0),t.readDoubleLE=v.bind(null,w,0,4),t.readDoubleBE=v.bind(null,_,4,0)),t}function x(t,i,n){i[n]=255&t,i[n+1]=t>>>8&255,i[n+2]=t>>>16&255,i[n+3]=t>>>24}function m(t,i,n){i[n]=t>>>24,i[n+1]=t>>>16&255,i[n+2]=t>>>8&255,i[n+3]=255&t}function w(t,i){return(t[i]|t[i+1]<<8|t[i+2]<<16|t[i+3]<<24)>>>0}function _(t,i){return(t[i]<<24|t[i+1]<<16|t[i+2]<<8|t[i+3])>>>0}t.exports=r(r)},function(t,i,n){"use strict";i.length=function(t){for(var i,n=0,r=0;r<t.length;++r)(i=t.charCodeAt(r))<128?n+=1:i<2048?n+=2:55296==(64512&i)&&56320==(64512&t.charCodeAt(r+1))?(++r,n+=4):n+=3;return n},i.read=function(t,i,n){if(n-i<1)return"";for(var r,e=null,s=[],o=0;i<n;)(r=t[i++])<128?s[o++]=r:191<r&&r<224?s[o++]=(31&r)<<6|63&t[i++]:239<r&&r<365?(r=((7&r)<<18|(63&t[i++])<<12|(63&t[i++])<<6|63&t[i++])-65536,s[o++]=55296+(r>>10),s[o++]=56320+(1023&r)):s[o++]=(15&r)<<12|(63&t[i++])<<6|63&t[i++],8191<o&&((e=e||[]).push(String.fromCharCode.apply(String,s)),o=0);return e?(o&&e.push(String.fromCharCode.apply(String,s.slice(0,o))),e.join("")):String.fromCharCode.apply(String,s.slice(0,o))},i.write=function(t,i,n){for(var r,e,s=n,o=0;o<t.length;++o)(r=t.charCodeAt(o))<128?i[n++]=r:(r<2048?i[n++]=r>>6|192:(55296==(64512&r)&&56320==(64512&(e=t.charCodeAt(o+1)))?(++o,i[n++]=(r=65536+((1023&r)<<10)+(1023&e))>>18|240,i[n++]=r>>12&63|128):i[n++]=r>>12|224,i[n++]=r>>6&63|128),i[n++]=63&r|128);return n-s}},function(t,i,n){"use strict";t.exports=function(i,n,t){var r=t||8192,e=r>>>1,s=null,o=r;return function(t){if(t<1||e<t)return i(t);r<o+t&&(s=i(r),o=0);t=n.call(s,o,o+=t);return 7&o&&(o=1+(7|o)),t}}},function(t,i,n){"use strict";t.exports=e;var r=n(2);function e(t,i){this.lo=t>>>0,this.hi=i>>>0}var s=e.zero=new e(0,0);s.toNumber=function(){return 0},s.zzEncode=s.zzDecode=function(){return this},s.length=function(){return 1};e.zeroHash="\0\0\0\0\0\0\0\0";e.fromNumber=function(t){if(0===t)return s;var i=t<0,n=(t=i?-t:t)>>>0,t=(t-n)/4294967296>>>0;return i&&(t=~t>>>0,n=~n>>>0,4294967295<++n&&(n=0,4294967295<++t&&(t=0))),new e(n,t)},e.from=function(t){if("number"==typeof t)return e.fromNumber(t);if(r.isString(t)){if(!r.Long)return e.fromNumber(parseInt(t,10));t=r.Long.fromString(t)}return t.low||t.high?new e(t.low>>>0,t.high>>>0):s},e.prototype.toNumber=function(t){if(!t&&this.hi>>>31){var i=1+~this.lo>>>0,t=~this.hi>>>0;return-(i+4294967296*(t=!i?t+1>>>0:t))}return this.lo+4294967296*this.hi},e.prototype.toLong=function(t){return r.Long?new r.Long(0|this.lo,0|this.hi,Boolean(t)):{low:0|this.lo,high:0|this.hi,unsigned:Boolean(t)}};var o=String.prototype.charCodeAt;e.fromHash=function(t){return"\0\0\0\0\0\0\0\0"===t?s:new e((o.call(t,0)|o.call(t,1)<<8|o.call(t,2)<<16|o.call(t,3)<<24)>>>0,(o.call(t,4)|o.call(t,5)<<8|o.call(t,6)<<16|o.call(t,7)<<24)>>>0)},e.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},e.prototype.zzEncode=function(){var t=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^t)>>>0,this.lo=(this.lo<<1^t)>>>0,this},e.prototype.zzDecode=function(){var t=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^t)>>>0,this.hi=(this.hi>>>1^t)>>>0,this},e.prototype.length=function(){var t=this.lo,i=(this.lo>>>28|this.hi<<4)>>>0,n=this.hi>>>24;return 0==n?0==i?t<16384?t<128?1:2:t<2097152?3:4:i<16384?i<128?5:6:i<2097152?7:8:n<128?9:10}},function(t,i,n){"use strict";t.exports=s;var r=n(9);(s.prototype=Object.create(r.prototype)).constructor=s;var e=n(2);function s(){r.call(this)}function o(t,i,n){t.length<40?e.utf8.write(t,i,n):i.utf8Write?i.utf8Write(t,n):i.write(t,n)}s._=function(){s.alloc=e.v,s.writeBytesBuffer=e.Buffer&&e.Buffer.prototype instanceof Uint8Array&&"set"===e.Buffer.prototype.set.name?function(t,i,n){i.set(t,n)}:function(t,i,n){if(t.copy)t.copy(i,n,0,t.length);else for(var r=0;r<t.length;)i[n++]=t[r++]}},s.prototype.bytes=function(t){var i=(t=e.isString(t)?e.u(t,"base64"):t).length>>>0;return this.uint32(i),i&&this.R(s.writeBytesBuffer,i,t),this},s.prototype.string=function(t){var i=e.Buffer.byteLength(t);return this.uint32(i),i&&this.R(o,i,t),this},s._()},function(t,i,n){"use strict";t.exports=s;var r=n(10);(s.prototype=Object.create(r.prototype)).constructor=s;var e=n(2);function s(t){r.call(this,t)}s._=function(){e.Buffer&&(s.prototype.T=e.Buffer.prototype.slice)},s.prototype.string=function(){var t=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+t,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+t,this.len))},s._()},function(t,i,n){"use strict";t.exports=r;var u=n(2);function r(t,i,n){if("function"!=typeof t)throw TypeError("rpcImpl must be a function");u.EventEmitter.call(this),this.rpcImpl=t,this.requestDelimited=Boolean(i),this.responseDelimited=Boolean(n)}((r.prototype=Object.create(u.EventEmitter.prototype)).constructor=r).prototype.rpcCall=function t(n,i,r,e,s){if(!e)throw TypeError("request must be specified");var o=this;if(!s)return u.asPromise(t,o,n,i,r,e);if(o.rpcImpl)try{return o.rpcImpl(n,i[o.requestDelimited?"encodeDelimited":"encode"](e).finish(),function(t,i){if(t)return o.emit("error",t,n),s(t);if(null!==i){if(!(i instanceof r))try{i=r[o.responseDelimited?"decodeDelimited":"decode"](i)}catch(t){return o.emit("error",t,n),s(t)}return o.emit("data",i,n),s(null,i)}o.end(!0)})}catch(t){return o.emit("error",t,n),void setTimeout(function(){s(t)},0)}else setTimeout(function(){s(Error("already ended"))},0)},r.prototype.end=function(t){return this.rpcImpl&&(t||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}},function(t,i,n){"use strict";function r(i,n){"string"==typeof i&&(n=i,i=void 0);var h=[];function a(t){if("string"!=typeof t){var i="return "+(i=f());if(t){for(var n=Object.keys(t),r=new Array(n.length+1),e=new Array(n.length),s=0;s<n.length;)r[s]=n[s],e[s]=t[n[s++]];return r[s]=i,Function.apply(null,r).apply(null,e)}return Function(i)()}for(var o=new Array(arguments.length-1),u=0;u<o.length;)o[u]=arguments[++u];if(u=0,t=t.replace(/%([%dfijs])/g,function(t,i){var n=o[u++];switch(i){case"d":case"f":return String(Number(n));case"i":return String(Math.floor(n));case"j":return JSON.stringify(n);case"s":return String(n)}return"%"}),u!==o.length)throw Error("parameter count mismatch");return h.push(t),a}function f(t){return"function "+(t||n||"")+"("+(i&&i.join(",")||"")+"){\n  "+h.join("\n  ")+"\n}"}return a.toString=f,a}(t.exports=r).verbose=!1},function(t,i,n){"use strict";t.exports=u;var s=n(13),o=n(14)("fs");function u(n,r,e){return r="function"==typeof r?(e=r,{}):r||{},e?!r.xhr&&o&&o.readFile?o.readFile(n,function(t,i){return t&&"undefined"!=typeof XMLHttpRequest?u.xhr(n,r,e):t?e(t):e(null,r.binary?i:i.toString("utf8"))}):u.xhr(n,r,e):s(u,this,n,r)}u.xhr=function(t,n,r){var e=new XMLHttpRequest;e.onreadystatechange=function(){if(4===e.readyState){if(0!==e.status&&200!==e.status)return r(Error("status "+e.status));if(n.binary){if(!(t=e.response))for(var t=[],i=0;i<e.responseText.length;++i)t.push(255&e.responseText.charCodeAt(i));return r(null,"undefined"!=typeof Uint8Array?new Uint8Array(t):t)}return r(null,e.responseText)}},n.binary&&("overrideMimeType"in e&&e.overrideMimeType("text/plain; charset=x-user-defined"),e.responseType="arraybuffer"),e.open("GET",t),e.send()}},function(t,i,n){"use strict";var i=i,e=i.isAbsolute=function(t){return/^(?:\/|\w+:)/.test(t)},r=i.normalize=function(t){var i=(t=t.replace(/\\/g,"/").replace(/\/{2,}/g,"/")).split("/"),n=e(t),t="";n&&(t=i.shift()+"/");for(var r=0;r<i.length;)".."===i[r]?0<r&&".."!==i[r-1]?i.splice(--r,2):n?i.splice(r,1):++r:"."===i[r]?i.splice(r,1):++r;return t+i.join("/")};i.resolve=function(t,i,n){return n||(i=r(i)),!e(i)&&(t=(t=!n?r(t):t).replace(/(?:\/|^)[^/]+$/,"")).length?r(t+"/"+i):i}},function(t,N,I){"use strict";!function(t){var u=I(44),s=I(45),o=I(46);function n(){return c.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function e(t,i){if(n()<i)throw new RangeError("Invalid typed array length");return c.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(i)).__proto__=c.prototype:(t=null===t?new c(i):t).length=i,t}function c(t,i,n){if(!(c.TYPED_ARRAY_SUPPORT||this instanceof c))return new c(t,i,n);if("number"!=typeof t)return r(this,t,i,n);if("string"==typeof i)throw new Error("If encoding is specified then the first argument must be a string");return a(this,t)}function r(t,i,n,r){if("number"==typeof i)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&i instanceof ArrayBuffer?function(t,i,n,r){if(i.byteLength,n<0||i.byteLength<n)throw new RangeError("'offset' is out of bounds");if(i.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");i=void 0===n&&void 0===r?new Uint8Array(i):void 0===r?new Uint8Array(i,n):new Uint8Array(i,n,r);c.TYPED_ARRAY_SUPPORT?(t=i).__proto__=c.prototype:t=f(t,i);return t}(t,i,n,r):"string"==typeof i?function(t,i,n){"string"==typeof n&&""!==n||(n="utf8");if(!c.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|v(i,n),n=(t=e(t,r)).write(i,n);n!==r&&(t=t.slice(0,n));return t}(t,i,n):function(t,i){if(c.isBuffer(i)){var n=0|l(i.length);return 0===(t=e(t,n)).length?t:(i.copy(t,0,0,n),t)}if(i){if("undefined"!=typeof ArrayBuffer&&i.buffer instanceof ArrayBuffer||"length"in i)return"number"!=typeof i.length||function(t){return t!=t}(i.length)?e(t,0):f(t,i);if("Buffer"===i.type&&o(i.data))return f(t,i.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,i)}function h(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function a(t,i){if(h(i),t=e(t,i<0?0:0|l(i)),!c.TYPED_ARRAY_SUPPORT)for(var n=0;n<i;++n)t[n]=0;return t}function f(t,i){var n=i.length<0?0:0|l(i.length);t=e(t,n);for(var r=0;r<n;r+=1)t[r]=255&i[r];return t}function l(t){if(t>=n())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+n().toString(16)+" bytes");return 0|t}function v(t,i){if(c.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;var n=(t="string"!=typeof t?""+t:t).length;if(0===n)return 0;for(var r=!1;;)switch(i){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return T(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return A(t).length;default:if(r)return T(t).length;i=(""+i).toLowerCase(),r=!0}}function i(t,i,n){var r,e,s,o=!1;if((i=void 0===i||i<0?0:i)>this.length)return"";if((n=void 0===n||n>this.length?this.length:n)<=0)return"";if((n>>>=0)<=(i>>>=0))return"";for(t=t||"utf8";;)switch(t){case"hex":return function(t,i,n){var r=t.length;(!i||i<0)&&(i=0);(!n||n<0||r<n)&&(n=r);for(var e="",s=i;s<n;++s)e+=function(t){return t<16?"0"+t.toString(16):t.toString(16)}(t[s]);return e}(this,i,n);case"utf8":case"utf-8":return m(this,i,n);case"ascii":return function(t,i,n){var r="";n=Math.min(t.length,n);for(var e=i;e<n;++e)r+=String.fromCharCode(127&t[e]);return r}(this,i,n);case"latin1":case"binary":return function(t,i,n){var r="";n=Math.min(t.length,n);for(var e=i;e<n;++e)r+=String.fromCharCode(t[e]);return r}(this,i,n);case"base64":return r=this,s=n,0===(e=i)&&s===r.length?u.fromByteArray(r):u.fromByteArray(r.slice(e,s));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,i,n){for(var r=t.slice(i,n),e="",s=0;s<r.length;s+=2)e+=String.fromCharCode(r[s]+256*r[s+1]);return e}(this,i,n);default:if(o)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),o=!0}}function d(t,i,n){var r=t[i];t[i]=t[n],t[n]=r}function y(t,i,n,r,e){if(0===t.length)return-1;if("string"==typeof n?(r=n,n=0):2147483647<n?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,(n=(n=isNaN(n)?e?0:t.length-1:n)<0?t.length+n:n)>=t.length){if(e)return-1;n=t.length-1}else if(n<0){if(!e)return-1;n=0}if("string"==typeof i&&(i=c.from(i,r)),c.isBuffer(i))return 0===i.length?-1:b(t,i,n,r,e);if("number"==typeof i)return i&=255,c.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?(e?Uint8Array.prototype.indexOf:Uint8Array.prototype.lastIndexOf).call(t,i,n):b(t,[i],n,r,e);throw new TypeError("val must be string, number or Buffer")}function b(t,i,n,r,e){var s=1,o=t.length,u=i.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||i.length<2)return-1;o/=s=2,u/=2,n/=2}function h(t,i){return 1===s?t[i]:t.readUInt16BE(i*s)}if(e)for(var a=-1,f=n;f<o;f++)if(h(t,f)===h(i,-1===a?0:f-a)){if(f-(a=-1===a?f:a)+1===u)return a*s}else-1!==a&&(f-=f-a),a=-1;else for(f=n=o<n+u?o-u:n;0<=f;f--){for(var c=!0,l=0;l<u;l++)if(h(t,f+l)!==h(i,l)){c=!1;break}if(c)return f}return-1}function p(t,i,n,r){return S(function(t){for(var i=[],n=0;n<t.length;++n)i.push(255&t.charCodeAt(n));return i}(i),t,n,r)}function x(t,i,n,r){return S(function(t,i){for(var n,r,e=[],s=0;s<t.length&&!((i-=2)<0);++s)r=t.charCodeAt(s),n=r>>8,r=r%256,e.push(r),e.push(n);return e}(i,t.length-n),t,n,r)}function m(t,i,n){n=Math.min(t.length,n);for(var r=[],e=i;e<n;){var s,o,u,h,a=t[e],f=null,c=239<a?4:223<a?3:191<a?2:1;if(e+c<=n)switch(c){case 1:a<128&&(f=a);break;case 2:128==(192&(s=t[e+1]))&&127<(h=(31&a)<<6|63&s)&&(f=h);break;case 3:s=t[e+1],o=t[e+2],128==(192&s)&&128==(192&o)&&2047<(h=(15&a)<<12|(63&s)<<6|63&o)&&(h<55296||57343<h)&&(f=h);break;case 4:s=t[e+1],o=t[e+2],u=t[e+3],128==(192&s)&&128==(192&o)&&128==(192&u)&&65535<(h=(15&a)<<18|(63&s)<<12|(63&o)<<6|63&u)&&h<1114112&&(f=h)}null===f?(f=65533,c=1):65535<f&&(f-=65536,r.push(f>>>10&1023|55296),f=56320|1023&f),r.push(f),e+=c}return function(t){var i=t.length;if(i<=w)return String.fromCharCode.apply(String,t);var n="",r=0;for(;r<i;)n+=String.fromCharCode.apply(String,t.slice(r,r+=w));return n}(r)}N.Buffer=c,N.SlowBuffer=function(t){+t!=t&&(t=0);return c.alloc(+t)},N.INSPECT_MAX_BYTES=50,c.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),N.kMaxLength=n(),c.poolSize=8192,c.G=function(t){return t.__proto__=c.prototype,t},c.from=function(t,i,n){return r(null,t,i,n)},c.TYPED_ARRAY_SUPPORT&&(c.prototype.__proto__=Uint8Array.prototype,c.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&c[Symbol.species]===c&&Object.defineProperty(c,Symbol.species,{value:null,configurable:!0})),c.alloc=function(t,i,n){return r=null,i=i,n=n,h(t=t),!(t<=0)&&void 0!==i?"string"==typeof n?e(r,t).fill(i,n):e(r,t).fill(i):e(r,t);var r},c.allocUnsafe=function(t){return a(null,t)},c.allocUnsafeSlow=function(t){return a(null,t)},c.isBuffer=function(t){return!(null==t||!t.Z)},c.compare=function(t,i){if(!c.isBuffer(t)||!c.isBuffer(i))throw new TypeError("Arguments must be Buffers");if(t===i)return 0;for(var n=t.length,r=i.length,e=0,s=Math.min(n,r);e<s;++e)if(t[e]!==i[e]){n=t[e],r=i[e];break}return n<r?-1:r<n?1:0},c.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},c.concat=function(t,i){if(!o(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return c.alloc(0);if(void 0===i)for(e=i=0;e<t.length;++e)i+=t[e].length;for(var n=c.allocUnsafe(i),r=0,e=0;e<t.length;++e){var s=t[e];if(!c.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(n,r),r+=s.length}return n},c.byteLength=v,c.prototype.Z=!0,c.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var i=0;i<t;i+=2)d(this,i,i+1);return this},c.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var i=0;i<t;i+=4)d(this,i,i+3),d(this,i+1,i+2);return this},c.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var i=0;i<t;i+=8)d(this,i,i+7),d(this,i+1,i+6),d(this,i+2,i+5),d(this,i+3,i+4);return this},c.prototype.toString=function(){var t=0|this.length;return 0==t?"":0===arguments.length?m(this,0,t):i.apply(this,arguments)},c.prototype.equals=function(t){if(!c.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===c.compare(this,t)},c.prototype.inspect=function(){var t="",i=N.INSPECT_MAX_BYTES;return 0<this.length&&(t=this.toString("hex",0,i).match(/.{2}/g).join(" "),this.length>i&&(t+=" ... ")),"<Buffer "+t+">"},c.prototype.compare=function(t,i,n,r,e){if(!c.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===e&&(e=this.length),(i=void 0===i?0:i)<0||n>t.length||r<0||e>this.length)throw new RangeError("out of range index");if(e<=r&&n<=i)return 0;if(e<=r)return-1;if(n<=i)return 1;if(this===t)return 0;for(var s=(e>>>=0)-(r>>>=0),o=(n>>>=0)-(i>>>=0),u=Math.min(s,o),h=this.slice(r,e),a=t.slice(i,n),f=0;f<u;++f)if(h[f]!==a[f]){s=h[f],o=a[f];break}return s<o?-1:o<s?1:0},c.prototype.includes=function(t,i,n){return-1!==this.indexOf(t,i,n)},c.prototype.indexOf=function(t,i,n){return y(this,t,i,n,!0)},c.prototype.lastIndexOf=function(t,i,n){return y(this,t,i,n,!1)},c.prototype.write=function(t,i,n,r){if(void 0===i)r="utf8",n=this.length,i=0;else if(void 0===n&&"string"==typeof i)r=i,n=this.length,i=0;else{if(!isFinite(i))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");i|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var e=this.length-i;if((void 0===n||e<n)&&(n=e),0<t.length&&(n<0||i<0)||i>this.length)throw new RangeError("Attempt to write outside buffer bounds");r=r||"utf8";for(var s,o,u,h=!1;;)switch(r){case"hex":return function(t,i,n,r){n=Number(n)||0;var e=t.length-n;if((!r||e<(r=Number(r)))&&(r=e),(e=i.length)%2!=0)throw new TypeError("Invalid hex string");e/2<r&&(r=e/2);for(var s=0;s<r;++s){var o=parseInt(i.substr(2*s,2),16);if(isNaN(o))return s;t[n+s]=o}return s}(this,t,i,n);case"utf8":case"utf-8":return o=i,u=n,S(T(t,(s=this).length-o),s,o,u);case"ascii":return p(this,t,i,n);case"latin1":case"binary":return p(this,t,i,n);case"base64":return s=this,o=i,u=n,S(A(t),s,o,u);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return x(this,t,i,n);default:if(h)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),h=!0}},c.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this.W||this,0)}};var w=4096;function _(t,i,n){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(n<t+i)throw new RangeError("Trying to access beyond buffer length")}function g(t,i,n,r,e,s){if(!c.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e<i||i<s)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}function O(t,i,n,r){i<0&&(i=65535+i+1);for(var e=0,s=Math.min(t.length-n,2);e<s;++e)t[n+e]=(i&255<<8*(r?e:1-e))>>>8*(r?e:1-e)}function E(t,i,n,r){i<0&&(i=4294967295+i+1);for(var e=0,s=Math.min(t.length-n,4);e<s;++e)t[n+e]=i>>>8*(r?e:3-e)&255}function M(t,i,n,r){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function j(t,i,n,r,e){return e||M(t,0,n,4),s.write(t,i,n,r,23,4),n+4}function k(t,i,n,r,e){return e||M(t,0,n,8),s.write(t,i,n,r,52,8),n+8}c.prototype.slice=function(t,i){var n=this.length;if((t=~~t)<0?(t+=n)<0&&(t=0):n<t&&(t=n),(i=void 0===i?n:~~i)<0?(i+=n)<0&&(i=0):n<i&&(i=n),i<t&&(i=t),c.TYPED_ARRAY_SUPPORT)(e=this.subarray(t,i)).__proto__=c.prototype;else for(var r=i-t,e=new c(r,void 0),s=0;s<r;++s)e[s]=this[s+t];return e},c.prototype.readUIntLE=function(t,i,n){t|=0,i|=0,n||_(t,i,this.length);for(var r=this[t],e=1,s=0;++s<i&&(e*=256);)r+=this[t+s]*e;return r},c.prototype.readUIntBE=function(t,i,n){t|=0,i|=0,n||_(t,i,this.length);for(var r=this[t+--i],e=1;0<i&&(e*=256);)r+=this[t+--i]*e;return r},c.prototype.readUInt8=function(t,i){return i||_(t,1,this.length),this[t]},c.prototype.readUInt16LE=function(t,i){return i||_(t,2,this.length),this[t]|this[t+1]<<8},c.prototype.readUInt16BE=function(t,i){return i||_(t,2,this.length),this[t]<<8|this[t+1]},c.prototype.readUInt32LE=function(t,i){return i||_(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},c.prototype.readUInt32BE=function(t,i){return i||_(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},c.prototype.readIntLE=function(t,i,n){t|=0,i|=0,n||_(t,i,this.length);for(var r=this[t],e=1,s=0;++s<i&&(e*=256);)r+=this[t+s]*e;return(e*=128)<=r&&(r-=Math.pow(2,8*i)),r},c.prototype.readIntBE=function(t,i,n){t|=0,i|=0,n||_(t,i,this.length);for(var r=i,e=1,s=this[t+--r];0<r&&(e*=256);)s+=this[t+--r]*e;return(e*=128)<=s&&(s-=Math.pow(2,8*i)),s},c.prototype.readInt8=function(t,i){return i||_(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},c.prototype.readInt16LE=function(t,i){i||_(t,2,this.length);t=this[t]|this[t+1]<<8;return 32768&t?4294901760|t:t},c.prototype.readInt16BE=function(t,i){i||_(t,2,this.length);t=this[t+1]|this[t]<<8;return 32768&t?4294901760|t:t},c.prototype.readInt32LE=function(t,i){return i||_(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},c.prototype.readInt32BE=function(t,i){return i||_(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},c.prototype.readFloatLE=function(t,i){return i||_(t,4,this.length),s.read(this,t,!0,23,4)},c.prototype.readFloatBE=function(t,i){return i||_(t,4,this.length),s.read(this,t,!1,23,4)},c.prototype.readDoubleLE=function(t,i){return i||_(t,8,this.length),s.read(this,t,!0,52,8)},c.prototype.readDoubleBE=function(t,i){return i||_(t,8,this.length),s.read(this,t,!1,52,8)},c.prototype.writeUIntLE=function(t,i,n,r){t=+t,i|=0,n|=0,r||g(this,t,i,n,Math.pow(2,8*n)-1,0);var e=1,s=0;for(this[i]=255&t;++s<n&&(e*=256);)this[i+s]=t/e&255;return i+n},c.prototype.writeUIntBE=function(t,i,n,r){t=+t,i|=0,n|=0,r||g(this,t,i,n,Math.pow(2,8*n)-1,0);var e=n-1,s=1;for(this[i+e]=255&t;0<=--e&&(s*=256);)this[i+e]=t/s&255;return i+n},c.prototype.writeUInt8=function(t,i,n){return t=+t,i|=0,n||g(this,t,i,1,255,0),c.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[i]=255&t,i+1},c.prototype.writeUInt16LE=function(t,i,n){return t=+t,i|=0,n||g(this,t,i,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[i]=255&t,this[i+1]=t>>>8):O(this,t,i,!0),i+2},c.prototype.writeUInt16BE=function(t,i,n){return t=+t,i|=0,n||g(this,t,i,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[i]=t>>>8,this[i+1]=255&t):O(this,t,i,!1),i+2},c.prototype.writeUInt32LE=function(t,i,n){return t=+t,i|=0,n||g(this,t,i,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[i+3]=t>>>24,this[i+2]=t>>>16,this[i+1]=t>>>8,this[i]=255&t):E(this,t,i,!0),i+4},c.prototype.writeUInt32BE=function(t,i,n){return t=+t,i|=0,n||g(this,t,i,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[i]=t>>>24,this[i+1]=t>>>16,this[i+2]=t>>>8,this[i+3]=255&t):E(this,t,i,!1),i+4},c.prototype.writeIntLE=function(t,i,n,r){t=+t,i|=0,r||g(this,t,i,n,(r=Math.pow(2,8*n-1))-1,-r);var e=0,s=1,o=0;for(this[i]=255&t;++e<n&&(s*=256);)t<0&&0===o&&0!==this[i+e-1]&&(o=1),this[i+e]=(t/s>>0)-o&255;return i+n},c.prototype.writeIntBE=function(t,i,n,r){t=+t,i|=0,r||g(this,t,i,n,(r=Math.pow(2,8*n-1))-1,-r);var e=n-1,s=1,o=0;for(this[i+e]=255&t;0<=--e&&(s*=256);)t<0&&0===o&&0!==this[i+e+1]&&(o=1),this[i+e]=(t/s>>0)-o&255;return i+n},c.prototype.writeInt8=function(t,i,n){return t=+t,i|=0,n||g(this,t,i,1,127,-128),c.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[i]=255&(t=t<0?255+t+1:t),i+1},c.prototype.writeInt16LE=function(t,i,n){return t=+t,i|=0,n||g(this,t,i,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[i]=255&t,this[i+1]=t>>>8):O(this,t,i,!0),i+2},c.prototype.writeInt16BE=function(t,i,n){return t=+t,i|=0,n||g(this,t,i,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[i]=t>>>8,this[i+1]=255&t):O(this,t,i,!1),i+2},c.prototype.writeInt32LE=function(t,i,n){return t=+t,i|=0,n||g(this,t,i,4,2147483647,-2147483648),c.TYPED_ARRAY_SUPPORT?(this[i]=255&t,this[i+1]=t>>>8,this[i+2]=t>>>16,this[i+3]=t>>>24):E(this,t,i,!0),i+4},c.prototype.writeInt32BE=function(t,i,n){return t=+t,i|=0,n||g(this,t,i,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),c.TYPED_ARRAY_SUPPORT?(this[i]=t>>>24,this[i+1]=t>>>16,this[i+2]=t>>>8,this[i+3]=255&t):E(this,t,i,!1),i+4},c.prototype.writeFloatLE=function(t,i,n){return j(this,t,i,!0,n)},c.prototype.writeFloatBE=function(t,i,n){return j(this,t,i,!1,n)},c.prototype.writeDoubleLE=function(t,i,n){return k(this,t,i,!0,n)},c.prototype.writeDoubleBE=function(t,i,n){return k(this,t,i,!1,n)},c.prototype.copy=function(t,i,n,r){if(n=n||0,r||0===r||(r=this.length),i>=t.length&&(i=t.length),(r=0<r&&r<n?n:r)===n)return 0;if(0===t.length||0===this.length)return 0;if((i=i||0)<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length);var e,s=(r=t.length-i<r-n?t.length-i+n:r)-n;if(this===t&&n<i&&i<r)for(e=s-1;0<=e;--e)t[e+i]=this[e+n];else if(s<1e3||!c.TYPED_ARRAY_SUPPORT)for(e=0;e<s;++e)t[e+i]=this[e+n];else Uint8Array.prototype.set.call(t,this.subarray(n,n+s),i);return s},c.prototype.fill=function(t,i,n,r){if("string"==typeof t){var e;if("string"==typeof i?(r=i,i=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1!==t.length||(e=t.charCodeAt(0))<256&&(t=e),void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!c.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof t&&(t&=255);if(i<0||this.length<i||this.length<n)throw new RangeError("Out of range index");if(n<=i)return this;if(i>>>=0,n=void 0===n?this.length:n>>>0,"number"==typeof(t=t||0))for(u=i;u<n;++u)this[u]=t;else for(var s=c.isBuffer(t)?t:T(new c(t,r).toString()),o=s.length,u=0;u<n-i;++u)this[u+i]=s[u%o];return this};var R=/[^+\/0-9A-Za-z-_]/g;function T(t,i){var n;i=i||1/0;for(var r=t.length,e=null,s=[],o=0;o<r;++o){if(55295<(n=t.charCodeAt(o))&&n<57344){if(!e){if(56319<n){-1<(i-=3)&&s.push(239,191,189);continue}if(o+1===r){-1<(i-=3)&&s.push(239,191,189);continue}e=n;continue}if(n<56320){-1<(i-=3)&&s.push(239,191,189),e=n;continue}n=65536+(e-55296<<10|n-56320)}else e&&-1<(i-=3)&&s.push(239,191,189);if(e=null,n<128){if(--i<0)break;s.push(n)}else if(n<2048){if((i-=2)<0)break;s.push(n>>6|192,63&n|128)}else if(n<65536){if((i-=3)<0)break;s.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((i-=4)<0)break;s.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return s}function A(t){return u.toByteArray(function(t){var i;if((t=((i=t).trim?i.trim():i.replace(/^\s+|\s+$/g,"")).replace(R,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function S(t,i,n,r){for(var e=0;e<r&&!(e+n>=i.length||e>=t.length);++e)i[e+n]=t[e];return e}}.call(this,I(8))},function(t,i,n){"use strict";i.byteLength=function(t){var i=f(t),t=i[0],i=i[1];return 3*(t+i)/4-i},i.toByteArray=function(t){var i,n,r=f(t),e=r[0],r=r[1],s=new a(function(t,i){return 3*(t+i)/4-i}(e,r)),o=0,u=0<r?e-4:e;for(n=0;n<u;n+=4)i=h[t.charCodeAt(n)]<<18|h[t.charCodeAt(n+1)]<<12|h[t.charCodeAt(n+2)]<<6|h[t.charCodeAt(n+3)],s[o++]=i>>16&255,s[o++]=i>>8&255,s[o++]=255&i;2===r&&(i=h[t.charCodeAt(n)]<<2|h[t.charCodeAt(n+1)]>>4,s[o++]=255&i);1===r&&(i=h[t.charCodeAt(n)]<<10|h[t.charCodeAt(n+1)]<<4|h[t.charCodeAt(n+2)]>>2,s[o++]=i>>8&255,s[o++]=255&i);return s},i.fromByteArray=function(t){for(var i,n=t.length,r=n%3,e=[],s=0,o=n-r;s<o;s+=16383)e.push(function(t,i,n){for(var r,e=[],s=i;s<n;s+=3)r=(t[s]<<16&16711680)+(t[s+1]<<8&65280)+(255&t[s+2]),e.push(function(t){return u[t>>18&63]+u[t>>12&63]+u[t>>6&63]+u[63&t]}(r));return e.join("")}(t,s,o<s+16383?o:s+16383));1==r?(i=t[n-1],e.push(u[i>>2]+u[i<<4&63]+"==")):2==r&&(i=(t[n-2]<<8)+t[n-1],e.push(u[i>>10]+u[i>>4&63]+u[i<<2&63]+"="));return e.join("")};for(var u=[],h=[],a="undefined"!=typeof Uint8Array?Uint8Array:Array,r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",e=0,s=r.length;e<s;++e)u[e]=r[e],h[r.charCodeAt(e)]=e;function f(t){var i=t.length;if(0<i%4)throw new Error("Invalid string. Length must be a multiple of 4");t=t.indexOf("=");return[t=-1===t?i:t,t===i?0:4-t%4]}h["-".charCodeAt(0)]=62,h["_".charCodeAt(0)]=63},function(t,i){i.read=function(t,i,n,r,e){var s,o,u=8*e-r-1,h=(1<<u)-1,a=h>>1,f=-7,c=n?e-1:0,l=n?-1:1,n=t[i+c];for(c+=l,s=n&(1<<-f)-1,n>>=-f,f+=u;0<f;s=256*s+t[i+c],c+=l,f-=8);for(o=s&(1<<-f)-1,s>>=-f,f+=r;0<f;o=256*o+t[i+c],c+=l,f-=8);if(0===s)s=1-a;else{if(s===h)return o?NaN:1/0*(n?-1:1);o+=Math.pow(2,r),s-=a}return(n?-1:1)*o*Math.pow(2,s-r)},i.write=function(t,i,n,r,e,s){var o,u,h=8*s-e-1,a=(1<<h)-1,f=a>>1,c=23===e?Math.pow(2,-24)-Math.pow(2,-77):0,l=r?0:s-1,v=r?1:-1,s=i<0||0===i&&1/i<0?1:0;for(i=Math.abs(i),isNaN(i)||i===1/0?(u=isNaN(i)?1:0,o=a):(o=Math.floor(Math.log(i)/Math.LN2),i*(r=Math.pow(2,-o))<1&&(o--,r*=2),2<=(i+=1<=o+f?c/r:c*Math.pow(2,1-f))*r&&(o++,r/=2),a<=o+f?(u=0,o=a):1<=o+f?(u=(i*r-1)*Math.pow(2,e),o+=f):(u=i*Math.pow(2,f-1)*Math.pow(2,e),o=0));8<=e;t[n+l]=255&u,l+=v,u/=256,e-=8);for(o=o<<e|u,h+=e;0<h;t[n+l]=255&o,l+=v,o/=256,h-=8);t[n+l-v]|=128*s}},function(t,i){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},function(t,i){t.exports=__WEBPACK_EXTERNAL_MODULE__47__},function(t,a,i){!function(e){function s(t,i){for(var n=0,r=t.length-1;0<=r;r--){var e=t[r];"."===e?t.splice(r,1):".."===e?(t.splice(r,1),n++):n&&(t.splice(r,1),n--)}if(i)for(;n--;)t.unshift("..");return t}function o(t,i){if(t.filter)return t.filter(i);for(var n=[],r=0;r<t.length;r++)i(t[r],r,t)&&n.push(t[r]);return n}a.resolve=function(){for(var t="",i=!1,n=arguments.length-1;-1<=n&&!i;n--){var r=0<=n?arguments[n]:e.cwd();if("string"!=typeof r)throw new TypeError("Arguments to path.resolve must be strings");r&&(t=r+"/"+t,i="/"===r.charAt(0))}return(i?"/":"")+(t=s(o(t.split("/"),function(t){return!!t}),!i).join("/"))||"."},a.normalize=function(t){var i=a.isAbsolute(t),n="/"===r(t,-1);return(t=!(t=s(o(t.split("/"),function(t){return!!t}),!i).join("/"))&&!i?".":t)&&n&&(t+="/"),(i?"/":"")+t},a.isAbsolute=function(t){return"/"===t.charAt(0)},a.join=function(){var t=Array.prototype.slice.call(arguments,0);return a.normalize(o(t,function(t,i){if("string"!=typeof t)throw new TypeError("Arguments to path.join must be strings");return t}).join("/"))},a.relative=function(t,i){function n(t){for(var i=0;i<t.length&&""===t[i];i++);for(var n=t.length-1;0<=n&&""===t[n];n--);return n<i?[]:t.slice(i,n-i+1)}t=a.resolve(t).substr(1),i=a.resolve(i).substr(1);for(var r=n(t.split("/")),e=n(i.split("/")),s=Math.min(r.length,e.length),o=s,u=0;u<s;u++)if(r[u]!==e[u]){o=u;break}for(var h=[],u=o;u<r.length;u++)h.push("..");return(h=h.concat(e.slice(o))).join("/")},a.sep="/",a.delimiter=":",a.dirname=function(t){if("string"!=typeof t&&(t+=""),0===t.length)return".";for(var i=t.charCodeAt(0),n=47===i,r=-1,e=!0,s=t.length-1;1<=s;--s)if(47===t.charCodeAt(s)){if(!e){r=s;break}}else e=!1;return-1===r?n?"/":".":n&&1===r?"/":t.slice(0,r)},a.basename=function(t,i){t=function(t){"string"!=typeof t&&(t+="");for(var i=0,n=-1,r=!0,e=t.length-1;0<=e;--e)if(47===t.charCodeAt(e)){if(!r){i=e+1;break}}else-1===n&&(r=!1,n=e+1);return-1===n?"":t.slice(i,n)}(t);return t=i&&t.substr(-1*i.length)===i?t.substr(0,t.length-i.length):t},a.extname=function(t){"string"!=typeof t&&(t+="");for(var i=-1,n=0,r=-1,e=!0,s=0,o=t.length-1;0<=o;--o){var u=t.charCodeAt(o);if(47===u){if(e)continue;n=o+1;break}-1===r&&(e=!1,r=o+1),46===u?-1===i?i=o:1!==s&&(s=1):-1!==i&&(s=-1)}return-1===i||-1===r||0===s||1===s&&i===r-1&&i===n+1?"":t.slice(i,r)};var r="b"==="ab".substr(-1)?function(t,i,n){return t.substr(i,n)}:function(t,i,n){return i<0&&(i=t.length+i),t.substr(i,n)}}.call(this,i(49))},function(t,i){var n,r,t=t.exports={};function e(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function o(i){if(n===setTimeout)return setTimeout(i,0);if((n===e||!n)&&setTimeout)return n=setTimeout,setTimeout(i,0);try{return n(i,0)}catch(t){try{return n.call(null,i,0)}catch(t){return n.call(this,i,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:e}catch(t){n=e}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(t){r=s}}();var u,h=[],a=!1,f=-1;function c(){a&&u&&(a=!1,u.length?h=u.concat(h):f=-1,h.length&&l())}function l(){if(!a){var t=o(c);a=!0;for(var i=h.length;i;){for(u=h,h=[];++f<i;)u&&u[f].run();f=-1,i=h.length}u=null,a=!1,function(i){if(r===clearTimeout)return clearTimeout(i);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(i);try{r(i)}catch(t){try{return r.call(null,i)}catch(t){return r.call(this,i)}}}(t)}}function v(t,i){this.fun=t,this.array=i}function d(){}t.nextTick=function(t){var i=new Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)i[n-1]=arguments[n];h.push(new v(t,i)),1!==h.length||a||o(l)},v.prototype.run=function(){this.fun.apply(null,this.array)},t.title="browser",t.browser=!0,t.env={},t.argv=[],t.version="",t.versions={},t.on=d,t.addListener=d,t.once=d,t.off=d,t.removeListener=d,t.removeAllListeners=d,t.emit=d,t.prependListener=d,t.prependOnceListener=d,t.listeners=function(t){return[]},t.binding=function(t){throw new Error("process.binding is not supported")},t.cwd=function(){return"/"},t.chdir=function(t){throw new Error("process.chdir is not supported")},t.umask=function(){return 0}},function(t,i,n){"use strict";n.r(i);var u="v3.1.2",h=9,r={NONE:0,EXTRUDE_MARKER:2,IMAGE_MARKER:8,LOCATION_MARKER:16,POLYGON_MARKER:32,TEXT_MARKER:64,LINE_MARKER:128,LINE3D_MARKER:256,HEAT_MAP_MARKER:512,EXTENT:1024,EXTERNAL_MODEL:2048,MODEL:4096,FACILITY:8192,LABEL:16384,DYNAMIC_MODEL_MARKER:32768,DOM_MARKER:65536,SPHERE_MARKER:1<<17,EXTENT_LAYER:1<<20,EXTERNAL_MODEL_LAYER:1<<21,MODEL_LAYER:1<<22,FACILITY_LAYER:1<<23,LABEL_LAYER:1<<24,MARKER_GROUP:1<<25,FLOW_LINE_LAYER:1<<27,FLOW_LINE_MARKER:1<<27,FIRE_MARKER:1<<28,WALL_MARKER:1<<29,TUBE_MARKER:1<<30};r.LAYER_NODE_TYPE=new Map,r.LAYER_NODE_TYPE.set(r.EXTENT_LAYER,r.EXTENT),r.LAYER_NODE_TYPE.set(r.EXTERNAL_MODEL_LAYER,r.EXTERNAL_MODEL),r.LAYER_NODE_TYPE.set(r.MODEL_LAYER,r.MODEL),r.LAYER_NODE_TYPE.set(r.FACILITY_LAYER,r.FACILITY),r.LAYER_NODE_TYPE.set(r.LABEL_LAYER,r.LABEL),r.LAYER_NODE_TYPE.set(r.DOM_MARKER,r.DOM_MARKER),r.LAYER_NODE_TYPE.set(r.DYNAMIC_MODEL_MARKER,r.DYNAMIC_MODEL_MARKER),r.LAYER_NODE_TYPE.set(r.EXTRUDE_MARKER,r.EXTRUDE_MARKER),r.LAYER_NODE_TYPE.set(r.HEAT_MAP_MARKER,r.HEAT_MAP_MARKER),r.LAYER_NODE_TYPE.set(r.LINE_MARKER,r.LINE_MARKER),r.LAYER_NODE_TYPE.set(r.LOCATION_MARKER,r.LOCATION_MARKER),r.LAYER_NODE_TYPE.set(r.POLYGON_MARKER,r.POLYGON_MARKER),r.LAYER_NODE_TYPE.set(r.TEXT_MARKER,r.TEXT_MARKER),r.LAYER_NODE_TYPE.set(r.IMAGE_MARKER,r.IMAGE_MARKER),r.LAYER_NODE_TYPE.set(r.FLOW_LINE_LAYER,r.FLOW_LINE_MARKER),r.LAYER_NODE_TYPE.set(r.FIRE_MARKER,r.FIRE_MARKER),r.LAYER_NODE_TYPE.set(r.WALL_MARKER,r.WALL_MARKER),r.LAYER_NODE_TYPE.set(r.TUBE_MARKER,r.TUBE_MARKER),r.LAYER_NODE_TYPE.set(r.LINE3D_MARKER,r.LINE3D_MARKER),r.LAYER_NODE_TYPE.set(r.SPHERE_MARKER,r.SPHERE_MARKER);var e=r,g={MODULE_SHORTEST:1,MODULE_BEST:2},m={PRIORITY_DEFAULT:1,PRIORITY_LIFTFIRST:2,PRIORITY_ESCALATORFIRST:3,PRIORITY_STAIRFIRST:4,PRIORITY_LIFTONLY:5,PRIORITY_ESCALATORONLY:6,PRIORITY_STAIRONLY:7,PRIORITY_ACCESSIBLEONLY:8,PRIORITY_LIFTFIRST1:9,PRIORITY_ESCALATORFIRST1:10,PRIORITY_STAIRFIRST1:11},S={ROUTE_SUCCESS:1,ROUTE_FAILED_NO_DATA_START:2,ROUTE_FAILED_NO_DATA_DEST:3,ROUTE_FAILED_CANNOT_ARRIVE:4,ROUTE_FAILED_WAYPOINT_CALCULATE_ERROR:5,ROUTE_FAILED_NO_START_ARRIVAL:6,ROUTE_FAILED_NO_DEST_ARRIVAL:7,ROUTE_FAILED_OUTLINE:8,ROUTE_FAILED_ENTRANCE:9},a={ZH:"zh",EN:"en"},f={TRANSFER_LIFT:1,TRANSFER_STAIR:2,TRANSFER_ESCALATOR:3,TRANSFER_ACCESSIBLE:4,TRANSFER_RAMP:5,TRANSFER_ENTRANCE:6},c={NONE_RODE_NETWORK:1,NONE_WALK_RODE_NETWORK:2,NONE_DRIVE_RODE_NETWORK:4};var s=function t(i,n){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.V=i,this.Y=n};Object.assign(s.prototype,{X:function(t){return!1},H:function(t,i){}});var o=s;function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t,i){return(v=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function d(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=y(n);return t=r?(t=y(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==l(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function y(t){return(y=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var b=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&v(t,i)}(r,o);var n=d(r);function r(t,i){return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,r),n.call(this,t,i)}return r}();Object.assign(b.prototype,{X:function(t){return this.Y==t.fid}});var p=b;function x(t){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function w(t,i){return(w=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function _(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=O(n);return t=r?(t=O(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==x(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function O(t){return(O=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var E=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&w(t,i)}(r,o);var n=_(r);function r(t,i){return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,r),"object"==x((i=n.call(this,t,i)).Y)?(i.Q=i.Y.text,i.K=i.Y.fuzzy,i.J=void 0===i.Y.matchCase||i.Y.matchCase):(i.Q=i.Y,i.J=!0),i}return r}();Object.assign(E.prototype,{X:function(t){if(t.name)if(this.K){if(this.J){if(-1!=t.name.indexOf(this.Q))return!0}else if(-1!=t.name.toLocaleLowerCase().indexOf(this.Q.toLocaleLowerCase()))return!0}else if(this.J){if(this.Q==t.name)return!0}else if(this.Q.toLocaleLowerCase()==t.name.toLocaleLowerCase())return!0;return!1}});var M=E;function j(t){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function k(t,i){return(k=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function R(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=T(n);return t=r?(t=T(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==j(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function T(t){return(T=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var A=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&k(t,i)}(r,o);var n=R(r);function r(t,i){return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,r),"object"==j((i=n.call(this,t,i)).Y)?(i.Q=i.Y.text,i.K=i.Y.fuzzy,i.J=void 0===i.Y.matchCase||i.Y.matchCase):(i.Q=i.Y,i.J=!0),i}return r}();Object.assign(A.prototype,{X:function(t){if(t.ename)if(this.K){if(this.J){if(-1!=t.ename.indexOf(this.Q))return!0}else if(-1!=t.ename.toLocaleLowerCase().indexOf(this.Q.toLocaleLowerCase()))return!0}else if(this.J){if(this.Q==t.ename)return!0}else if(this.Q.toLocaleLowerCase()==t.ename.toLocaleLowerCase())return!0;return!1}});var N=A;function I(t){return(I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function L(t,i){return(L=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function D(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=P(n);return t=r?(t=P(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==I(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function P(t){return(P=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var F=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&L(t,i)}(r,o);var n=D(r);function r(t,i){return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,r),(t=n.call(this,t)).Y=Array.isArray(i)?i:[i],t}return r}();Object.assign(F.prototype,{X:function(t){return-1!=this.Y.indexOf(t.type)}});var C=F;function U(t){return(U="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function B(t,i){return(B=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function G(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=Z(n);return t=r?(t=Z(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==U(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function Z(t){return(Z=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}r=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&B(t,i)}(r,o);var n=G(r);function r(t,i){return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,r),"object"==U((i=n.call(this,t,i)).Y)?(i.Q=i.Y.text,i.K=void 0===i.Y.fuzzy||i.Y.fuzzy,i.J=void 0===i.Y.matchCase||i.Y.matchCase):(i.Q=i.Y,i.K=!0,i.J=!0),i}return r}();Object.assign(r.prototype,{X:function(t){if(this.K){if(this.J){if(t.name&&-1!=t.name.indexOf(this.Q)||t.ename&&-1!=t.ename.indexOf(this.Q))return!0}else if(t.name&&-1!=t.name.toLocaleLowerCase().indexOf(this.Q.toLocaleLowerCase())||t.ename&&-1!=t.ename.toLocaleLowerCase().indexOf(this.Q.toLocaleLowerCase()))return!0}else if(this.J){if(t.name&&t.name==this.Q||t.ename&&t.ename==this.Q)return!0}else if(t.name&&t.name.toLocaleLowerCase()==this.Q.toLocaleLowerCase()||t.ename&&t.ename.toLocaleLowerCase()==this.Q.toLocaleLowerCase())return!0;return!1}});var W=r,s={NONE:0,POINT:1,LINE:2,POLYGON:4,MULTIPOINT:8,MULTILINE:16,MULTIPOLYGON:32,GROUP:64,EXTENT:1024,EXTERNALMODEL:2048,MODEL:4096,FACILITY:8192,LABEL:16384,EXTENTGROUP:1<<20,EXTERNALMODELGROUP:1<<21,MODELGROUP:1<<22,FACILITYGROUP:1<<23,LABELGROUP:1<<24};Object.freeze(s);var V=s,Y=function(t,i,n){n=n||2;var r,e,s,o,u,h=i&&i.length,a=h?i[0]*n:t.length,f=X(t,0,a,n,!0),c=[];if(!f||f.next===f.prev)return c;if(h&&(f=function(t,i,n,r){var e,s,o,u,h=[];for(e=0,s=i.length;e<s;e++)o=i[e]*r,u=e<s-1?i[e+1]*r:t.length,(u=X(t,o,u,r,!1))===u.next&&(u.steiner=!0),h.push(function(t){var i=t,n=t;for(;(i.x<n.x||i.x===n.x&&i.y<n.y)&&(n=i),i=i.next,i!==t;);return n}(u));for(h.sort(H),e=0;e<h.length;e++)!function(t,i){(i=function(t,i){var n,r=i,e=t.x,s=t.y,o=-1/0;do{if(s<=r.y&&s>=r.next.y&&r.next.y!==r.y){var u=r.x+(s-r.y)*(r.next.x-r.x)/(r.next.y-r.y);if(u<=e&&o<u){if((o=u)===e){if(s===r.y)return r;if(s===r.next.y)return r.next}n=r.x<r.next.x?r:r.next}}}while(r=r.next,r!==i);if(!n)return null;if(e===o)return n;var h,a=n,f=n.x,c=n.y,l=1/0;r=n;for(;e>=r.x&&r.x>=f&&e!==r.x&&K(s<c?e:o,s,f,c,s<c?o:e,s,r.x,r.y)&&(h=Math.abs(s-r.y)/(e-r.x),rt(r,t)&&(h<l||h===l&&(r.x>n.x||r.x===n.x&&function(t,i){return J(t.prev,t,i.prev)<0&&J(i.next,t,t.next)<0}(n,r)))&&(n=r,l=h)),r=r.next,r!==a;);return n}(t,i))&&(t=et(i,t),z(i,i.next),z(t,t.next))}(h[e],n),n=z(n,n.next);return n}(t,i,f,n)),t.length>80*n){for(var l=r=t[0],v=e=t[1],d=n;d<a;d+=n)(s=t[d])<l&&(l=s),(o=t[d+1])<v&&(v=o),r<s&&(r=s),e<o&&(e=o);u=0!==(u=Math.max(r-l,e-v))?1/u:0}return q(f,c,n,l,v,u),c};function X(t,i,n,r,e){var s,o;if(e===0<function(t,i,n,r){for(var e=0,s=i,o=n-r;s<n;s+=r)e+=(t[o]-t[s])*(t[s+1]+t[o+1]),o=s;return e}(t,i,n,r))for(s=i;s<n;s+=r)o=st(s,t[s],t[s+1],o);else for(s=n-r;i<=s;s-=r)o=st(s,t[s],t[s+1],o);return o&&$(o,o.next)&&(ot(o),o=o.next),o}function z(t,i){if(!t)return t;i=i||t;var n,r=t;do{if(n=!1,r.steiner||!$(r,r.next)&&0!==J(r.prev,r,r.next))r=r.next;else{if(ot(r),(r=i=r.prev)===r.next)break;n=!0}}while(n||r!==i);return i}function q(t,i,n,r,e,s,o){if(t){!o&&s&&function(t,i,n,r){var e=t;for(;null===e.z&&(e.z=Q(e.x,e.y,i,n,r)),e.prevZ=e.prev,e.nextZ=e.next,e=e.next,e!==t;);e.prevZ.nextZ=null,e.prevZ=null,function(t){var i,n,r,e,s,o,u,h,a=1;do{for(n=t,s=t=null,o=0;n;){for(o++,r=n,i=u=0;i<a&&(u++,r=r.nextZ);i++);for(h=a;0<u||0<h&&r;)0!==u&&(0===h||!r||n.z<=r.z)?(n=(e=n).nextZ,u--):(r=(e=r).nextZ,h--),s?s.nextZ=e:t=e,e.prevZ=s,s=e;n=r}}while(s.nextZ=null,a*=2,1<o)}(e)}(t,r,e,s);for(var u,h,a=t;t.prev!==t.next;)if(u=t.prev,h=t.next,s?function(t,i,n,r){var e=t.prev,s=t,o=t.next;if(0<=J(e,s,o))return!1;var u=(e.x<s.x?e.x<o.x?e:o:s.x<o.x?s:o).x,h=(e.y<s.y?e.y<o.y?e:o:s.y<o.y?s:o).y,a=(e.x>s.x?e.x>o.x?e:o:s.x>o.x?s:o).x,f=(e.y>s.y?e.y>o.y?e:o:s.y>o.y?s:o).y,c=Q(u,h,i,n,r),l=Q(a,f,i,n,r),v=t.prevZ,d=t.nextZ;for(;v&&v.z>=c&&d&&d.z<=l;){if(v!==t.prev&&v!==t.next&&K(e.x,e.y,s.x,s.y,o.x,o.y,v.x,v.y)&&0<=J(v.prev,v,v.next))return!1;if(v=v.prevZ,d!==t.prev&&d!==t.next&&K(e.x,e.y,s.x,s.y,o.x,o.y,d.x,d.y)&&0<=J(d.prev,d,d.next))return!1;d=d.nextZ}for(;v&&v.z>=c;){if(v!==t.prev&&v!==t.next&&K(e.x,e.y,s.x,s.y,o.x,o.y,v.x,v.y)&&0<=J(v.prev,v,v.next))return!1;v=v.prevZ}for(;d&&d.z<=l;){if(d!==t.prev&&d!==t.next&&K(e.x,e.y,s.x,s.y,o.x,o.y,d.x,d.y)&&0<=J(d.prev,d,d.next))return!1;d=d.nextZ}return!0}(t,r,e,s):function(t){var i=t.prev,n=t,r=t.next;if(0<=J(i,n,r))return!1;var e=t.next.next;for(;e!==t.prev;){if(K(i.x,i.y,n.x,n.y,r.x,r.y,e.x,e.y)&&0<=J(e.prev,e,e.next))return!1;e=e.next}return!0}(t))i.push(u.i/n),i.push(t.i/n),i.push(h.i/n),ot(t),t=h.next,a=h.next;else if((t=h)===a){o?1===o?q(t=function(t,i,n){var r=t;do{var e=r.prev,s=r.next.next}while(!$(e,s)&&tt(e,r,r.next,s)&&rt(e,s)&&rt(s,e)&&(i.push(e.i/n),i.push(r.i/n),i.push(s.i/n),ot(r),ot(r.next),r=t=s),r=r.next,r!==t);return z(r)}(z(t),i,n),i,n,r,e,s,2):2===o&&function(t,i,n,r,e,s){var o=t;do{for(var u=o.next.next;u!==o.prev;){if(o.i!==u.i&&function(t,i){return t.next.i!==i.i&&t.prev.i!==i.i&&!function(t,i){var n=t;do{if(n.i!==t.i&&n.next.i!==t.i&&n.i!==i.i&&n.next.i!==i.i&&tt(n,n.next,t,i))return!0}while(n=n.next,n!==t);return!1}(t,i)&&(rt(t,i)&&rt(i,t)&&function(t,i){var n=t,r=!1,e=(t.x+i.x)/2,s=(t.y+i.y)/2;for(;n.y>s!=n.next.y>s&&n.next.y!==n.y&&e<(n.next.x-n.x)*(s-n.y)/(n.next.y-n.y)+n.x&&(r=!r),n=n.next,n!==t;);return r}(t,i)&&(J(t.prev,t,i.prev)||J(t,i.prev,i))||$(t,i)&&0<J(t.prev,t,t.next)&&0<J(i.prev,i,i.next))}(o,u)){var h=et(o,u);return o=z(o,o.next),h=z(h,h.next),q(o,i,n,r,e,s),q(h,i,n,r,e,s)}u=u.next}}while(o=o.next,o!==t)}(t,i,n,r,e,s):q(z(t),i,n,r,e,s,1);break}}}function H(t,i){return t.x-i.x}function Q(t,i,n,r,e){return(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=32767*(t-n)*e)|t<<8))|t<<4))|t<<2))|t<<1))|(i=1431655765&((i=858993459&((i=252645135&((i=16711935&((i=32767*(i-r)*e)|i<<8))|i<<4))|i<<2))|i<<1))<<1}function K(t,i,n,r,e,s,o,u){return 0<=(e-o)*(i-u)-(t-o)*(s-u)&&0<=(t-o)*(r-u)-(n-o)*(i-u)&&0<=(n-o)*(s-u)-(e-o)*(r-u)}function J(t,i,n){return(i.y-t.y)*(n.x-i.x)-(i.x-t.x)*(n.y-i.y)}function $(t,i){return t.x===i.x&&t.y===i.y}function tt(t,i,n,r){var e=nt(J(t,i,n)),s=nt(J(t,i,r)),o=nt(J(n,r,t)),u=nt(J(n,r,i));return e!==s&&o!==u||(0===e&&it(t,n,i)||(0===s&&it(t,r,i)||(0===o&&it(n,t,r)||!(0!==u||!it(n,i,r)))))}function it(t,i,n){return i.x<=Math.max(t.x,n.x)&&i.x>=Math.min(t.x,n.x)&&i.y<=Math.max(t.y,n.y)&&i.y>=Math.min(t.y,n.y)}function nt(t){return 0<t?1:t<0?-1:0}function rt(t,i){return J(t.prev,t,t.next)<0?0<=J(t,i,t.next)&&0<=J(t,t.prev,i):J(t,i,t.prev)<0||J(t,t.next,i)<0}function et(t,i){var n=new ut(t.i,t.x,t.y),r=new ut(i.i,i.x,i.y),e=t.next,s=i.prev;return(t.next=i).prev=t,(n.next=e).prev=n,(r.next=n).prev=r,(s.next=r).prev=s,r}function st(t,i,n,r){n=new ut(t,i,n);return r?(n.next=r.next,(n.prev=r).next.prev=n,r.next=n):(n.prev=n).next=n,n}function ot(t){t.next.prev=t.prev,t.prev.next=t.next,t.prevZ&&(t.prevZ.nextZ=t.nextZ),t.nextZ&&(t.nextZ.prevZ=t.prevZ)}function ut(t,i,n){this.i=t,this.x=i,this.y=n,this.prev=null,this.next=null,this.z=null,this.prevZ=null,this.nextZ=null,this.steiner=!1}function ht(t){return function(t){if(Array.isArray(t))return at(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,i){if(t){if("string"==typeof t)return at(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?at(t,i):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function at(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}var ft={area:function(t){for(var i=t.length,n=0,r=i-1,e=0;e<i;r=e++)n+=t[r].x*t[e].y-t[e].x*t[r].y;return.5*n},isClockWise:function(t){return ft.area(t)<0},triangulateShape:function(t,i){var n=[],r=[],e=[];ct(t),lt(n,t);var s=t.length;i.forEach(ct);for(var o=0;o<i.length;o++)r.push(s),s+=i[o].length,lt(n,i[o]);for(var u=Y(n,r),h=0;h<u.length;h+=3)e.push(u.slice(h,h+3));return e},triangulate:function(t){(e=[]).push.apply(e,ht(t[0]));for(var i=[],n=1;n<t.length;n++){var r=[];r.push.apply(r,ht(t[n])),i.push(r)}if(!ft.isClockWise(e))for(var e=e.reverse(),s=0,o=i.length;s<o;s++){var u=i[s];ft.isClockWise(u)&&(i[s]=u.reverse())}return ft.triangulateShape(e,i)}};function ct(t){var i,n=t.length;2<n&&(i=t[n-1],n=t[0],i.x===n.x&&i.y===n.y)&&t.pop()}function lt(t,i){for(var n=0;n<i.length;n++)t.push(i[n].x),t.push(i[n].y)}var vt=1e-5,dt=.001,yt=Math.PI/180,bt=180/Math.PI,b=function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t)};Object.assign(b.prototype,{$:function(){return vt},tt:function(){return yt},it:function(){return bt},nt:function(t){return t<vt&&-vt<t},rt:function(t){return vt<t},et:function(t){return t<-vt},st:function(t,i,n){n=(t.x-n.x)*(i.y-n.y)-(i.x-n.x)*(t.y-n.y);return Math.abs(n)<vt?0:n},distanceOfTwoPoints:function(t,i){return t&&i?Math.sqrt((t.x-i.x)*(t.x-i.x)+(t.y-i.y)*(t.y-i.y)):Number.NaN},distance:function(t,i){return Math.sqrt(Math.pow(i.x-t.x,2)+Math.pow(i.y-t.y,2))},isPolygonContainPoint:function(t,i){for(var n=0,r=0;r<t.length;r++){var e=t[r].x,s=t[r].y,o=0,u=0,u=r<t.length-1?(o=t[r+1].x,t[r+1].y):(o=t[0].x,t[0].y);if(e===i.x&&s===i.y||o===i.x&&u===i.y)return!0;if(u!==s){var h=e+(i.y-s)*(o-e)/(u-s);if(h===i.x)return!0;e!==o?h<=Math.max(e,o)&&h>=Math.min(e,o)&&h>i.x&&h!==e&&(h===o&&0<(s-i.y)*(u-i.y)?n+=2:n++):h<=Math.max(e,o)&&h>=Math.min(e,o)&&h>i.x&&(h===o&&0<(s-i.y)*(u-i.y)?n+=2:n++)}}return 0<n%2},ot:function(t,i,n,r){return!(Math.max(t.x,i.x)<Math.min(n.x,r.x))&&(!(Math.max(t.y,i.y)<Math.min(n.y,r.y))&&(!(Math.max(n.x,r.x)<Math.min(t.x,i.x))&&!(Math.max(n.y,r.y)<Math.min(t.y,i.y))))},ut:function(t,i,n,r){return!(Math.max(t.x,i.x)<Math.min(n.x,r.x))&&(!(Math.max(t.y,i.y)<Math.min(n.y,r.y))&&(!(Math.max(n.x,r.x)<Math.min(t.x,i.x))&&(!(Math.max(n.y,r.y)<Math.min(t.y,i.y))&&((0!==this.st(n,i,t)||0!==this.st(i,r,t))&&(!(this.st(n,i,t)*this.st(i,r,t)<0)&&!(this.st(t,r,n)*this.st(r,i,n)<0))))))},ht:function(t,i,n){return!(t.x<i.x)&&(!(t.y<i.y)&&(!(t.x>n.x)&&!(t.y>n.y)))},at:function(t,i,n){var r=Math.min(i.x,n.x),e=Math.max(i.x,n.x),s=Math.min(i.y,n.y),n=Math.max(i.y,n.y);return!(t.x<r||t.x>e||t.y<s||t.y>n)},ft:function(t,i,n){var r=Math.min(n.x,i.x),e=Math.max(n.x,i.x),s=Math.min(n.y,i.y),i=Math.max(n.y,i.y);return!(t.x<r||t.x>e||t.y<s||t.y>i)},ct:function(t,i,n){for(var r,e,s=!1,o=0;o<n;o++)r=i[o],e=i[(o+1)%n],(r.y<t.y&&e.y>=t.y||e.y<t.y&&r.y>=t.y)&&(r.x<=t.x||e.x<=t.x)&&r.x+(t.y-r.y)/(e.y-r.y)*(e.x-r.x)<t.x&&(s=!s);return s},lt:function(t,i,n){return(t.x-i.x)*(n.y-i.y)==(n.x-i.x)*(t.y-i.y)&&Math.min(i.x,n.x)<=t.x&&t.x<=Math.max(i.x,n.x)&&Math.min(i.y,n.y)<=t.y&&t.y<=Math.max(i.y,n.y)},vt:function(t,i){return Math.sqrt((t.x-i.x)*(t.x-i.x)+(t.y-i.y)*(t.y-i.y))},dt:function(t,i){return(t.x-i.x)*(t.x-i.x)+(t.y-i.y)*(t.y-i.y)},yt:function(t,i,n){var r=i.x,e=i.y,s=n.x,o=n.y,u=t.x,h=t.y,a=s-r,f=o-e,c=a*a+f*f,i=u-r,n=h-e,t=i*i+n*n;if(this.nt(c))return l=this.nt(t)?0:t;var l,c=Math.sqrt(c),a=a/c,f=f/c,n=a*i+f*n;return l=this.rt(n)?this.et(n-c)?(a=u-(r+n*a))*a+(f=h-(e+n*f))*f:(s=u-s)*s+(o=h-o)*o:t,this.nt(l)?0:l},bt:function(t,i,n,r){var e=i.x,s=i.y,o=n.x,u=n.y,h=t.x,a=t.y,f=o-e,c=u-s,l=Math.sqrt(f*f+c*c),v=h-e,d=a-s,t=Math.sqrt(v*v+d*d);if(this.nt(l))return r.x=i.x,r.y=i.y,this.nt(y=t)?0:y;var f=f/l,c=c/l,d=f*v+c*d,y=this.rt(d)?this.et(d-l)?(f=h-(e=e+d*f),c=a-(d=s+d*c),c=Math.sqrt(f*f+c*c),r.x=e,r.y=d,c):(o=h-o,u=a-u,u=Math.sqrt(o*o+u*u),r.x=n.x,r.y=n.y,u):(r.x=i.x,r.y=i.y,t);return this.nt(y)?0:y},pt:function(t,i){for(var n=0,r=0,e=0,s=0;s<i;s++){var o=t[s],u=t[(s+1)%i],h=(o.x*u.y-o.y*u.x)/2;n+=h,r+=h*(o.x+u.x)/3,e+=h*(o.y+u.y)/3}return{x:r/=n,y:e/=n}},xt:function(t){for(var i=ft.triangulate([t]),n=0,r=0,e=0,s=0;s<i.length;s++){var o=t[i[s][0]],u=t[i[s][1]],h=t[i[s][2]],a=u.x-o.x,f=u.y-o.y,c=h.x-o.x,f=(a*(h.y-o.y)-c*f)/2;n+=f,r+=(o.x+u.x+h.x)/3*f,e+=(o.y+u.y+h.y)/3*f}return{x:r/n,y:e/n}},mt:function(t,i){if(i<3)return 0;for(var n=0,r=0;r<i;++r){var e=t[r],s=t[(r+1)%i];n+=e.x*s.y-e.y*s.x}return Math.abs(n/2)},wt:function(t,i,n,r,e){if(this.ut(t,i,n,r)){var s=(r.x-n.x)*(t.y-i.y)-(i.x-t.x)*(n.y-r.y),o=(t.y-n.y)*(i.x-t.x)*(r.x-n.x)+n.x*(r.y-n.y)*(i.x-t.x)-t.x*(i.y-t.y)*(r.x-n.x);return e.x=o/s,s=(t.x-i.x)*(r.y-n.y)-(i.y-t.y)*(n.x-r.x),o=i.y*(t.x-i.x)*(r.y-n.y)+(r.x-i.x)*(r.y-n.y)*(t.y-i.y)-r.y*(n.x-r.x)*(i.y-t.y),e.y=o/s,1}return 0},_t:function(t,i,n,r,e){var s=Math.min(n.x,r.x),o=Math.max(n.x,r.x),u=Math.min(n.y,r.y),h=Math.max(n.y,r.y),a=t.x,f=t.y,c=n.x,l=n.y,v=r.x,d=r.y;if(90==i){if(t.x<s||t.x>o)return!1;if(Math.abs(o-s<dt))return t.y>=l&&t.y<=d||t.y>=d&&t.y<=l?(e.x=t.x,e.y=t.y,!0):l<d&&t.x<l?(e.x=c,e.y=l,!0):d<l&&t.x<d&&(e.x=v,e.y=d,!0);var y=(d-l)/(v-c)*(a-c)+l;return e.y=y,e.x=a,y<f?!1:!!this.ft(e,n,r)}if(270==i){if(t.x<s||t.x>o)return!1;if(Math.abs(o-s<dt))return t.y>=l&&t.y<=d||t.y>=d&&t.y<=l?(e.x=t.x,e.y=t.y,!0):l<d&&t.y>d?(e.x=v,e.y=d,!0):d<l&&t.y>l&&(e.x=c,e.y=l,!0);t=(d-l)/(v-c)*(a-c)+l;return e.y=t,e.x=a,f<t?!1:!!this.ft(e,n,r)}if(270!=i&&90!=i&&Math.abs(o-s<dt)){o=s,s=Math.tan(i/180*Math.PI)*(o-a)+f;if(e.y=s,e.x=o,this.ft(e,n,r)){o=o-a,s=s-f;return 0<o/Math.sqrt(o*o+s*s)*Math.cos(i/180*Math.PI)}}if(Math.abs(h-u<=dt)){h=u,u=1/Math.tan(i/180*Math.PI)*(h-f)+a;if(e.x=u,e.y=h,this.ft(e,n,r)){var u=u-a,b=h-f;return 0<u/Math.sqrt(u*u+b*b)*Math.cos(i/180*Math.PI)}return!1}b=Math.tan(i/180*Math.PI),v=(d-l)/(v-c),v=(l-f-(v*c-b*a))/(b-v),b=f+b*(v-a);if(e.x=v,e.y=b,this.ft(e,n,r)){a=v-a,f=b-f;return Math.abs(a)<=dt&&Math.abs(f)<=dt?!0:0<a/Math.sqrt(a*a+f*f)*Math.cos(i/180*Math.PI)}return!1},gt:function(t,i){return Math.abs(t.x-i.x)<vt&&Math.abs(t.y-i.y)<vt},Ot:function(t,i,n,r){var e=t.x,s=t.y,o=i.x,u=i.y,h=n.x,a=n.y;if(this.gt(t,i)||this.gt(t,n))return r.x=e,r.y=s,!0;var f=Math.min(i.x,n.x),c=Math.max(i.x,n.x),l=Math.min(i.y,n.y),v=Math.max(i.y,n.y);if(Math.abs(o-h)<1e-5)return r.x=o,l<=(r.y=s)&&s<=v;if(Math.abs(u-a)<1e-5)return r.x=e,r.y=u,f<=e&&e<=c;c=-1/((a-u)/(h-o)),h=(a-u)/(h-o),h=(u-s-(h*o-c*e))/(c-h),e=s+c*(h-e);return r.x=h,r.y=e,!!this.ft(r,i,n)||!(!this.gt(t,i)&&!this.gt(t,n))},Et:function(t,i){return{x:t.y*i.z-t.z*i.y,y:t.z*i.x-t.x*i.z,z:t.x*i.y-t.y*i.x}},Mt:function(t,i){return t.x*i.x+t.y*i.y+t.z*i.z},jt:function(t){return Math.sqrt(t.x*t.x+t.y*t.y+t.z*t.z)},kt:function(t,i){return t.x*i.x+t.y*i.y},Rt:function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},Tt:function(t,i){if(0==t.x&&0==t.y||0==i.x&&0==i.y)return 0;var n={x:t.x,y:t.y,z:0},r={x:i.x,y:i.y,z:0},n=this.Et(n,r),r=this.kt(t,i),i=Math.acos(r/(this.Rt(t)*this.Rt(i)));return 0<n.z?i/Math.PI*180:360-i/Math.PI*180},At:function(t,i,n,r){for(var e=0;e<n.length;e++){var s=n[e],o=n[(e+1)%n.length];if(1==this.wt(t,i,s,o,r))return!0}return!1},St:function(t,i,n,r,e){var s=Math.min(t.x,n.x,r.x),o=Math.max(t.x,n.x,r.x),u=Math.min(t.y,n.y,r.y),h=Math.max(t.y,n.y,r.y),h=Math.sqrt((o-s)*(o-s)+(h-u)*(h-u)),u=i/180*Math.PI,i=h*Math.cos(u)+t.x,u=h*Math.sin(u)+t.y;return 1==this.wt(t,{x:i,y:u},n,r,e)},Nt:function(t){for(var i={x:null,y:null},n=0,r=0,e=0;e<t.length;e++)n+=t[e].x,r+=t[e].y;return i.x=n/t.length,i.y=r/t.length,i},It:function(t,i,n,r){var e=[],s=t.x-i,o=t.x+i,u=t.y-i,h=t.y+i,a=Math.min(n.x,r.x),f=Math.max(n.x,r.x),c=Math.min(n.y,r.y),l=Math.max(n.y,r.y);if(f<s||o<a||l<u||h<c)return e;s=r.x-n.x,o=r.y-n.y,u=t.x-n.x,h=t.y-n.y,r=s*s+o*o,t=(s*u+o*h)/r,i=t*t-(u*u+h*h-i*i)/r;if(i<0)return e;r=Math.sqrt(i),i=-t+r,t=-t-r,r={x:n.x-s*i,y:n.y-o*i},o={x:n.x-s*t,y:n.y-o*t};return i===t||(a<=r.x&&r.x<=f&&c<=r.y&&r.y<=l&&e.push(r),a<=o.x&&o.x<=f&&c<=o.y&&o.y<=l&&e.push(o)),e}});var pt=new b;function xt(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}E=function(){function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.Lt={x:Number.MAX_VALUE,y:Number.MAX_VALUE},this.Dt={x:-Number.MAX_VALUE,y:-Number.MAX_VALUE}}var i,n,r;return i=t,(n=[{key:"min",get:function(){return this.Lt}},{key:"max",get:function(){return this.Dt}},{key:"center",get:function(){var t={x:Number.NaN,y:Number.NaN};return this.valid()&&(t.x=(this.min.x+this.max.x)/2,t.y=(this.min.y+this.max.y)/2),t}},{key:"size",get:function(){var t={x:Number.NaN,y:Number.NaN};return this.valid()&&(t.x=this.max.x-this.min.x,t.y=this.max.y-this.min.y),t}},{key:"copy",value:function(t){return this.Dt.x=t.max.x,this.Dt.y=t.max.y,this.Lt.x=t.min.x,this.Lt.y=t.min.y,this}},{key:"clone",value:function(){return(new t).copy(this)}},{key:"reset",value:function(){this.Lt={x:Number.MAX_VALUE,y:Number.MAX_VALUE},this.Dt={x:-Number.MAX_VALUE,y:-Number.MAX_VALUE}}},{key:"toObject",value:function(){return{min:this.min,max:this.max,center:this.center,size:this.size}}},{key:"expand",value:function(t){this.Pt(t)}},{key:"expandByCoords",value:function(t){this.Ft(t)}},{key:"valid",value:function(){return this.Ct()}},{key:"isCross",value:function(t){var i=!0;return t.max.x<this.min.x&&(i=!1),t.min.x>this.max.x&&(i=!1),t.max.y<this.min.y&&(i=!1),i=t.min.y>this.max.y?!1:i}}])&&xt(i.prototype,n),r&&xt(i,r),t}();Object.assign(E.prototype,{Ct:function(){return!(this.min.x>this.max.x||this.min.y>this.max.y)},Pt:function(t){t&&t.valid()&&(t.min.x<this.min.x&&(this.min.x=t.min.x),t.min.y<this.min.y&&(this.min.y=t.min.y),t.max.x>this.max.x&&(this.max.x=t.max.x),t.max.y>this.max.y&&(this.max.y=t.max.y))},Ft:function(t){var i=this;t&&0!=t.length&&t.forEach(function(t){t.x<i.min.x&&(i.min.x=t.x),t.y<i.min.y&&(i.min.y=t.y),t.x>i.max.x&&(i.max.x=t.x),t.y>i.max.y&&(i.max.y=t.y)})}});var mt=E;function wt(t){return(wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _t(t,i){return(_t=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function gt(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=Ot(n);return t=r?(t=Ot(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==wt(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function Ot(t){return(Ot=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}A=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&_t(t,i)}(r,o);var n=gt(r);function r(t,i){return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,r),(i=n.call(this,t,i)).Ut=i.Y.center,i.Bt=i.Y.radius,i.Gt=i.Y.radius*i.Y.radius,i.Zt=new mt,i.Zt.Ft([{x:i.Ut.x-i.Bt,y:i.Ut.y-i.Bt},{x:i.Ut.x+i.Bt,y:i.Ut.y+i.Bt}]),i}return r}();Object.assign(A.prototype,{X:function(t){return t.getType()!=V.MODEL?this.Wt(t):this.Vt(t)},Vt:function(t){var i=t.getBound();if(!pt.ot(this.Zt.Lt,this.Zt.Dt,i.min,i.max))return!1;for(var n=t.getCoordinates(),r=0,e=n.length;r<e;r++)for(var s=1,o=n[r].length;s<o;s++){var u=n[r][s];if(pt.dt(u,this.Ut)<this.Gt)return!0;var h=n[r][s-1];if(1==s&&pt.dt(h,this.Ut)<this.Gt)return!0;if(pt.yt(this.Ut,h,u)<this.Gt)return!0}return!1},Wt:function(t){t=t.getCoordinates()[0];return!!pt.ht(t,this.Zt.Lt,this.Zt.Dt)&&pt.dt(t,this.Ut)<this.Gt},H:function(t,i){var n=t.getCoordinates();if(t.getType()==V.MODEL){for(var r=Number.MAX_VALUE,e=0,s=n.length;e<s;e++)for(var o=1,u=n[e].length;o<u;o++){var h=n[e][o-1],a=n[e][o],a=pt.yt(this.Ut,h,a);a<r&&(r=a)}i.vt=Math.sqrt(r)}else i.vt=Math.sqrt(pt.dt(n[0],this.Ut))}});var Et=A;function Mt(t){return(Mt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function jt(t,i){return(jt=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function kt(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=Rt(n);return t=r?(t=Rt(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==Mt(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function Rt(t){return(Rt=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}F=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&jt(t,i)}(r,o);var n=kt(r);function r(t,i){return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,r),(i=n.call(this,t,i)).Yt=i.Y,i.Xt=i.Yt.length-1,i.Ut=pt.xt(i.Yt),i.Zt=new mt,i.Zt.Ft(i.Yt),i}return r}();Object.assign(F.prototype,{X:function(t){return t.getType()!=V.MODEL?this.Wt(t):this.Vt(t)},Vt:function(t){var i=t.getBound();if(!pt.ot(this.Zt.Lt,this.Zt.Dt,i.min,i.max))return!1;for(var n=t.getCoordinates(),r=0,e=n.length;r<e;r++)for(var s=0,o=n[r].length-1;s<o;s++)if(pt.ct(n[r][s],this.Yt,this.Yt.length))return!0;for(var u=0;u<this.Xt;u++){for(var h=!1,a=0,f=n.length;a<f;a++)pt.ct(this.Yt[u],n[a],n[a].length-1)&&(h=0==a);if(h)return!0}for(var c=0;c<this.Xt;c++)for(var l=this.Yt[c],v=this.Yt[c+1],d=0,y=n.length;d<y;d++)for(var b=0,p=n[d].length-1;b<p;b++){var x=n[d][b],m=n[d][b+1];if(pt.ut(l,v,x,m))return!0}for(var w=this.Yt[0],_=this.Yt[this.Yt.length-1],g=0,O=n.length;g<O;g++)for(var E=0,M=n[g].length-1;E<M;E++){var j=n[g][E],k=n[g][E+1];if(pt.ut(w,_,j,k))return!0}return!1},Wt:function(t){t=t.getCoordinates()[0];return!!pt.ht(t,this.Zt.Lt,this.Zt.Dt)&&pt.ct(t,this.Yt,this.Yt.length-1)},H:function(t,i){var n=t.getCoordinates();if(t.getType()===V.MODEL){for(var r=Number.MAX_VALUE,e=0,s=n.length;e<s;e++)for(var o=1,u=n[e].length;o<u;o++){var h=n[e][o-1],a=n[e][o],a=pt.yt(this.Ut,h,a);a<r&&(r=a)}i.vt=Math.sqrt(r)}else i.vt=Math.sqrt(pt.dt(n[0],this.Ut))}});var Tt=F;function At(t){return(At="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function St(t,i){return(St=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function Nt(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=It(n);return t=r?(t=It(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==At(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function It(t){return(It=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}r=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&St(t,i)}(r,o);var n=Nt(r);function r(t,i){return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,r),n.call(this,t,i)}return r}();Object.assign(r.prototype,{X:function(t){return!1}});var Lt=r;function Dt(t){return(Dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Pt(t,i){return(Pt=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function Ft(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=Ct(n);return t=r?(t=Ct(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==Dt(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function Ct(t){return(Ct=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}s=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&Pt(t,i)}(r,o);var n=Ft(r);function r(t,i){return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,r),(i=n.call(this,t,i)).zt=i.Y.point,i}return r}();Object.assign(s.prototype,{X:function(t){return t.getType()==V.MODEL&&this.Vt(t)},Vt:function(t){var i=t.getBound();if(!pt.ht(this.zt,i.min,i.max))return!1;for(var n=t.getCoordinates(),r=0,e=0,s=n.length;e<s&&(pt.ct(this.zt,n[e],n[e].length-1)&&r++,!(1<r));e++);return 1==r}});var Ut=s;function Bt(t){return(Bt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Gt(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var Zt=new(function(){function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t)}var i,n,r;return i=t,(n=[{key:"create",value:function(t){if(!t||"object"!=Bt(t))return null;var i,n=[];for(i in t)switch(i){case"FID":n.push(new p(i,t[i]));break;case"name":n.push(new M(i,t[i]));break;case"ename":n.push(new N(i,t[i]));break;case"keyword":n.push(new W(i,t[i]));break;case"typeID":n.push(new C(i,t[i]));break;case"circle":n.push(new Et(i,t[i]));break;case"polygon":n.push(new Tt(i,t[i]));break;case"buffer":n.push(new Lt(i,t[i]));break;case"contain":n.push(new Ut(i,t[i]))}return n}}])&&Gt(i.prototype,n),r&&Gt(i,r),t}());function Wt(t){return function(t){if(Array.isArray(t))return Vt(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,i){if(t){if("string"==typeof t)return Vt(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Vt(t,i):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Vt(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}function Yt(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var b=function(){function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.qt=null,this.Ht=e.MODEL,this.Qt=null,this.Kt=[],this.Jt=[],this.$t=!1,this.ti=null}var i,n,r;return i=t,(n=[{key:"levels",get:function(){return this.qt},set:function(t){this.qt=t}},{key:"type",get:function(){return this.Ht},set:function(t){this.Ht=t}},{key:"addCondition",value:function(t){var i,n=Zt.create(t);(i=this.Jt).push.apply(i,Wt(n)),this.Kt.push(t)}},{key:"onlyInBuilding",get:function(){return this.$t},set:function(t){this.$t=t}},{key:"buildings",get:function(){return this.ti},set:function(t){this.ti=t}},{key:"buildingID",get:function(){return this.Qt},set:function(t){this.Qt=t}}])&&Yt(i.prototype,n),r&&Yt(i,r),t}(),Xt=n(1),zt={NAME_KEY_ERROR:0,MAP_ID_URL_ERROR:2,THEME_ID_URL_ERROR:4,LICENSE_ERROR:8,PATH_ERROR:404,INFO:32,DEFAULT_LEVEL_ERROR:64};function qt(t){return(qt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ht(i,t){var n,r=Object.keys(i);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(i),t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(i,t).enumerable})),r.push.apply(r,n)),r}function Qt(r){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{};t%2?Ht(Object(e),!0).forEach(function(t){var i,n;i=r,t=e[n=t],n in i?Object.defineProperty(i,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):i[n]=t}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(e)):Ht(Object(e)).forEach(function(t){Object.defineProperty(r,t,Object.getOwnPropertyDescriptor(e,t))})}return r}E=function t(i){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.ii=Qt({data:null,method:"GET",responseType:"json"},i),this.ni=null};Object.assign(E.prototype,{ri:function(t,i,n){var r=this.ii,e=r.data,s=r.method,o=r.url,u=r.responseType,h=r.header,a=null;if((a=new("undefined"!=typeof XMLHttpRequest?XMLHttpRequest:Xt.a.global().fengmap.XMLHttpRequest)).responseType=u,a.open(s,o),"object"==qt(h))for(var f in h)a.setRequestHeader(f,h[f]);a.addEventListener("load",function(){200===a.status||0===a.status||201===a.status?t&&t(a.response):i&&(404===a.status?i(zt.PATH_ERROR):2069===a.response.error_code?i(zt.NAME_KEY_ERROR):2063===a.response.error_code?i(zt.MAP_ID_URL_ERROR):400===a.response.error_code?i(zt.THEME_ID_URL_ERROR):i(a.response))}),a.addEventListener("timeout",function(t){n&&n(t)}),a.addEventListener("error",function(t){i&&i(t)}),a.send(JSON.stringify(e)),this.ni=a},ei:function(){this.ni&&this.ni.abort&&(this.ni.abort(),this.ni=null)}});var Kt=E,A=n(27),Jt=n.n(A);var $t,ti=["TmF2aVNlZ21lbg==","YWNjZXNzaWJsZQ==","Zmlk","bmZv","Rmxvb3JHZW8=","bGlmdFR5cGU=","bGVu","dmVs","ZXh0ZW50TGF5ZQ==","RXh0ZW50Qml6SQ==","cmVhZE9ubHk=","LnByb3RvYnVm","aW50MzI=","ZG9vcnR5cGU=","R0VPUE9JTlRfTQ==","TmF2aVpvbmU=","ZW50cmFuY2VGbA==","bGVuZ3Ro","ZWxCaXpJbmZv","c3RhaXJMYXllcg==","dG9CaWQ=","R2F0ZQ==","b2Zmc2V0WQ==","Zmlkcw==","aXNTZWxlY3RhYg==","ZmxhZw==","ZXh0ZXJuYWxNbw==","bGlmdEVudHJ5","VklTRUdNRU5U","SW5kZXhJbmZv","ZGF0YQ==","ZGVsQml6SW5mbw==","bWlk","cG9pTGF5ZXI=","bW9kZWxpZA==","cGFja2Vk","dGhyb3VnaA==","bmF2aVNlZ21lbg==","bGlmdExheWVy","cmVwZWF0ZWQ=","ZGVmQ2VuWQ==","b29y","bm9kZUlk","TmF2aU1vZGVs","cmFuaw==","c25vZGU=","cHRz","ZGVmR2lk","aGFzaENvZGU=","bmVzdGVk","Ymlk","cmVxdWlyZWQ=","R0VPTElORV9OQQ==","b2Zmc2V0WA==","TmF2aU5vZGU=","bHR5cGU=","T1ZFUkxBWURBVA==","ZGVmQ2VuWA==","b3B0aW9ucw==","YmFzZUxldmVs","YXJlYQ==","TGF5ZXJHcm91cA==","ZmllbGRz","cGFzcw==","Zmxvb3Jz","SUZU","Rmxvb3JOYXZp","amF2YV9vdXRlcg==","UG9seWdvbkxhYg==","ZW50cmFuY2VUeQ==","VHlwZQ==","bmF2aVpvbmVz","ZWxMYXllcg==","YWxpYXM=","d2lkdGg=","YXllcg==","ZW50cnl0eXBlcw==","TGlmdEJpekluZg==","bm9kZXR5cGVz","amF2YV9wYWNrYQ==","bmF2aVR5cGU=","ZGF0ZVZlcg==","bGlmdEZsYWc=","Z2lk","bWF4WA==","bW5hbWU=","bWluU2NhbGVMZQ==","Zmxvb3JJZA==","X2NsYXNzbmFtZQ==","TW9kZWxCaXpJbg==","Zmxvb3I=","bmFtZQ==","c2NhbGVMZXZlbA==","RXh0ZXJuYWxNbw==","cGdpZA==","ZmlsZVZlcg==","VEFJUg==","bWlubGV2ZWw=","Rmxvb3JDb25maQ==","R2VuZXJhbEdlbw==","ZHJpdmU=","Ym9vbA==","a2V5","R0VPUE9MWUdPTg==","dG9HaWQ=","ZGVmQ2VuWg==","YnR5cGU=","R0VPUE9JTlRfUw==","cC5wbGF0Zm9ybQ==","TmF2aUZsb29y","ZmxvYXQ=","bmF2aUV4dGVudA==","UE9JQml6SW5mbw==","TGFiZWxCaXpJbg==","TGF5ZXI=","cnVsZQ==","bGlk","Y29tLmZlbmdtYQ==","c2NhbGU=","c2VnbWVudElk","ZGVzYw==","ZGVsTGF5ZXI=","R0VPUE9JTlRfUA==","cm90YXRlQW5nbA==","X0xBQkVM","cG9seWdvbkxheQ==","ZmlsZURhdGU=","R0VPUE9JTlRfTA==","U3RhaXJCaXpJbg==","ZXNjYWxhdG9yTA==","bGlua1NlZw==","QnVmZmVy","TmF2aUV4dGVudA==","Z2Vv","R0VPTElORQ==","bmF2aU5vZGVz","cmVqZWN0cw==","UG9seWdvbkJpeg==","aXNWaXNpYmxl","b25zaGlw","YnVmZmVycw==","bWF4bGV2ZWw=","ZG91Ymxl","R0VPUE9JTlRfRQ==","bmF2aQ==","Rmxvb3JCaXo=","Z1Byb3RvQnVm","T0RFTA==","ZW5hbWU=","bW9kZWxMYXllcg==","a2V5cw==","ZWlk","bGF5ZXJz","QU5PUkFNQQ==","UkRFUg==","R0VPUE9JTlQ=","QnVpbGRpbmc=","bWluWA==","TWFw","aGVpZ2h0","c3RyaW5n","dHlwZQ==","ZGVmYXVsdA==","cG9seWdvbkxhYg==","TmF2aVJlbGF0aQ==","dWludDY0","aWR4cw==","SW5mbw==","X0VYVEVOVA=="];$t=ti,function(t){for(;--t;)$t.push($t.shift())}(218);function ii(t,i){var n=ti[t=+t];void 0===ii.ngQRZk&&((r=function(){var i;try{i=Function('return (function() {}.constructor("return this")( ));')()}catch(t){i=window}return i}()).atob||(r.atob=function(t){for(var i,n,r=String(t).replace(/=+$/,""),e="",s=0,o=0;n=r.charAt(o++);~n&&(i=s%4?64*i+n:n,s++%4)&&(e+=String.fromCharCode(255&i>>(-2*s&6))))n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(n);return e}),ii.bOCUYG=function(t){for(var i=atob(t),n=[],r=0,e=i.length;r<e;r++)n+="%"+("00"+i.charCodeAt(r).toString(16)).slice(-2);return decodeURIComponent(n)},ii.TcYoxp={},ii.ngQRZk=!0);var r=ii.TcYoxp[t];return void 0===r?(n=ii.bOCUYG(n),ii.TcYoxp[t]=n):n=r,n}var ni=function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t);var i={};i[ii("0x1f")+"ge"]=ii("0x45")+ii("0x3c")+ii("0x84"),i[ii("0x13")+ii("0x28")]=ii("0x32")+ii("0x62");var n={};n[ii("0x43")]="required",n[ii("0x71")]=ii("0x70"),n.id=1;var r={};r.rule=ii("0x3"),r[ii("0x71")]=ii("0x85"),r.id=2;var e={};e.rule=ii("0x3"),e.type=ii("0x75"),e.id=3;var s={};s[ii("0x43")]=ii("0x3"),s[ii("0x71")]=ii("0x70"),s.id=4;var o={};o[ii("0x43")]=ii("0x3"),o[ii("0x71")]=ii("0x70"),o.id=5;var u={};u[ii("0x71")]=ii("0x70"),u.id=6;var h={};h[ii("0x71")]=ii("0x70"),h.id=7;var a={};a[ii("0x71")]=ii("0x70"),a.id=8;var f={};f[ii("0x72")]=!1;var c={type:"bool",id:9};c[ii("0xa")]=f;var l={};l[ii("0x43")]="repeated",l[ii("0x71")]=ii("0x53"),l.id=10;var v={rule:"repeated"};v.type=ii("0x70"),v.id=11;var d={};d[ii("0x99")]=n,d.fileVer=r,d[ii("0x21")]=e,d[ii("0x25")]=s,d.hashCode=o,d[ii("0x36")]=u,d[ii("0x4e")]=h,d.desc=a,d.readOnly=c,d[ii("0x5c")]=l,d[ii("0x66")]=v;var y={};y[ii("0x43")]=ii("0x3"),y[ii("0x71")]="int32",y.id=1;var b={};b[ii("0x43")]=ii("0x3"),b.type=ii("0x85"),b.id=2;var p={};p[ii("0x43")]="required",p[ii("0x71")]="int32",p.id=3;var x={rule:"required"};x[ii("0x71")]=ii("0x75"),x.id=4;var m={};m.rule=ii("0x3"),m[ii("0x71")]="bytes",m.id=5;var w={};w[ii("0x23")]=y,w[ii("0x2f")]=b,w[ii("0x3a")]=p,w[ii("0x7f")]=x,w[ii("0x97")]=m;var _={};_[ii("0xe")]=w;var g={};g.Buffer=_;var O={};O[ii("0xe")]=d,O.nested=g;var E={};E[ii("0x43")]=ii("0x3"),E[ii("0x71")]="string",E.id=19;var M={};M.rule=ii("0x3"),M.type=ii("0x85"),M.id=20;var j={};j[ii("0x43")]=ii("0x3"),j[ii("0x71")]="uint64",j.id=21;var k={};k[ii("0x43")]=ii("0x3"),k[ii("0x71")]=ii("0x70"),k.id=22;var R={};R.rule=ii("0x3"),R[ii("0x71")]=ii("0x70"),R.id=23;var T={type:"string",id:24},A={};A[ii("0x71")]=ii("0x70"),A.id=25;var S={};S[ii("0x72")]=!1;f={};f[ii("0x71")]=ii("0x35"),f.id=26,f[ii("0xa")]=S;n={};n[ii("0x71")]=ii("0x3e"),n.id=1;r={};r[ii("0x71")]="float",r.id=2;e={};e[ii("0x71")]="float",e.id=3;s={};s[ii("0x71")]=ii("0x3e"),s.id=4;o={};o.type=ii("0x3e"),o.id=5;u={};u.type=ii("0x3e"),u.id=6;h={};h.type=ii("0x3e"),h.id=7;a={};a[ii("0x71")]=ii("0x70"),a.id=8;c={};c.type=ii("0x3e"),c.id=9;l={};l[ii("0x71")]=ii("0x3e"),l.id=10;v={};v[ii("0x71")]=ii("0x3e"),v.id=11;y={};y.type=ii("0x3e"),y.id=12;b={};b[ii("0x71")]=ii("0x3e"),b.id=13;p={};p[ii("0x71")]=ii("0x3e"),p.id=14;x={};x.type=ii("0x3e"),x.id=15;m={};m[ii("0x71")]=ii("0x3e"),m.id=16;w={};w[ii("0x71")]=ii("0x70"),w.id=17;_={};_[ii("0x71")]="int32",_.id=28;d={};d.rule=ii("0xa0"),d[ii("0x71")]="LayerGroup",d.id=18;g={};g[ii("0x43")]="repeated",g[ii("0x71")]=ii("0x6c"),g.id=27;S={};S.mid=E,S.fileVer=M,S[ii("0x21")]=j,S[ii("0x25")]=k,S[ii("0x0")]=R,S[ii("0x36")]=T,S[ii("0x48")]=A,S[ii("0x83")]=f,S.x=n,S.y=r,S.z=e,S[ii("0x6d")]=s,S.minY=o,S[ii("0x24")]=u,S.maxY=h,S[ii("0xa8")]=a,S[ii("0x9")]=c,S[ii("0xa1")]=l,S[ii("0x39")]=v,S[ii("0x6f")]=y,S[ii("0x4b")+"eX"]=b,S.rotateAngleY=p,S[ii("0x4b")+"eZ"]=x,S[ii("0x46")]=m,S[ii("0x2c")]=w,S[ii("0x20")]=_,S.layerGroups=d,S.buildings=g;r={};r[ii("0x71")]=ii("0x85"),r.id=1;e={};e[ii("0x71")]=ii("0x70"),e.id=2;s={};s[ii("0x71")]=ii("0x70"),s.id=3;o={};o[ii("0x71")]=ii("0x16"),o.id=4;u={};u.type=ii("0x3e"),u.id=5;h={};h[ii("0x71")]=ii("0x3e"),h.id=6;a={};a[ii("0x71")]=ii("0x3e"),a.id=7;c={};c[ii("0x71")]=ii("0x3e"),c.id=8;l={};l[ii("0x71")]=ii("0x3e"),l.id=9;v={};v[ii("0x71")]=ii("0x3e"),v.id=10;y={};y[ii("0x71")]=ii("0x3e"),y.id=11;b={};b[ii("0x71")]=ii("0x3e"),b.id=12;p={};p[ii("0x71")]=ii("0x35"),p.id=13;x={};x[ii("0x71")]=ii("0x35"),x.id=14;m={};m[ii("0x71")]=ii("0x35"),m.id=15;w={};w.type=ii("0x70"),w.id=16;_={};_[ii("0x44")]=r,_.lname=e,_[ii("0x19")]=s,_[ii("0x7")]=o,_[ii("0x5")]=u,_[ii("0x8f")]=h,_[ii("0x6f")]=a,_[ii("0x4b")+"eX"]=c,_.rotateAngleY=l,_[ii("0x4b")+"eZ"]=v,_[ii("0x26")+ii("0x80")]=y,_["maxScaleLe"+ii("0x80")]=b,_[ii("0x5a")]=p,_[ii("0x91")+"le"]=x,_.isEditable=m,_[ii("0x48")]=w;d={};d[ii("0x6b")]=1,d[ii("0x5f")+"SCALATOR"]=2,d[ii("0x4f")+ii("0x11")]=3,d[ii("0x3b")+ii("0x30")]=4,d[ii("0x4a")+ii("0x69")]=5,d[ii("0x4a")+"OI"]=6,d[ii("0x3b")+"TORELABEL"]=7,d.GEOPOINT_NAVINODE=8,d[ii("0x87")+ii("0x63")]=9,d[ii("0x56")]=20,d[ii("0x4")+ii("0x95")]=21,d["GEOLINE_BO"+ii("0x6a")]=22,d[ii("0x37")]=30,d[ii("0x37")+ii("0x78")]=31,d.GEOPOLYGON_STORE=32,d[ii("0x37")+ii("0x4c")]=33,d.RASTERATA=40,d[ii("0x8")+"A"]=50;g={};g.values=d;r={};r[ii("0x16")]=g;e={};e[ii("0xe")]=_,e[ii("0x1")]=r;s={};s[ii("0x71")]="int32",s.id=1;o={};o[ii("0x71")]="string",o.id=2;u={};u[ii("0x71")]="string",u.id=3;h={};h[ii("0x71")]=ii("0x3e"),h.id=4;a={};a[ii("0x71")]=ii("0x70"),a.id=5;c={};c[ii("0x43")]="repeated",c[ii("0x71")]=ii("0x42"),c.id=6;l={};l[ii("0x71")]=ii("0x70"),l.id=7;v={};v[ii("0x71")]="int32",v.id=8;y={};y.gid=s,y.gname=o,y[ii("0x19")]=u,y[ii("0x6f")]=h,y[ii("0x48")]=a,y[ii("0x68")]=c,y[ii("0x27")]=l,y[ii("0x20")]=v;b={};b[ii("0xe")]=y;p={};p[ii("0x71")]=ii("0x85"),p.id=1;x={};x[ii("0x71")]=ii("0x3e"),x.id=2;m={};m[ii("0x71")]=ii("0x3e"),m.id=3;w={};w.type=ii("0x3e"),w.id=4;d={};d.type=ii("0x3e"),d.id=5;g={};g[ii("0x71")]="float",g.id=6;_={};_[ii("0x71")]=ii("0x3e"),_.id=7;r={};r[ii("0x43")]=ii("0xa0"),r.type=ii("0x70"),r.id=8;s={};s.type=ii("0x85"),s.id=9;o={type:"bool",id:10},u={};u[ii("0x71")]=ii("0x85"),u.id=11;h={};h[ii("0x71")]="int32",h.id=12;a={};a[ii("0x71")]=ii("0x70"),a.id=13;c={rule:"repeated"};c[ii("0x71")]=ii("0x96"),c.id=14;l={};l[ii("0x9c")]=!1;v={};v[ii("0x43")]=ii("0xa0"),v.type=ii("0x5e"),v.id=15,v[ii("0xa")]=l;y={};y[ii("0x23")]=p,y.x=x,y.y=m,y.offsetX=w,y[ii("0x8f")]=d,y.angle=g,y.scale=_,y[ii("0x90")]=r,y[ii("0x2e")]=s,y[ii("0xb")]=o,y.minlevel=u,y[ii("0x5d")]=h,y.floorId=a,y.idxs=c,y[ii("0xa7")]=v;l={};l[ii("0x9c")]=!1;p={};p.rule=ii("0xa0"),p[ii("0x71")]=ii("0x85"),p.id=1,p[ii("0xa")]=l;x={};x[ii("0x76")]=p;m={};m[ii("0xe")]=x;w={};w.IndexInfo=m;d={};d[ii("0xe")]=y,d[ii("0x1")]=w;g={};g[ii("0x71")]=ii("0x70"),g.id=1;_={type:"string",id:2},r={};r.type=ii("0x70"),r.id=3;s={};s[ii("0x71")]=ii("0x70"),s.id=4;o={type:"float",id:5},u={};u[ii("0x71")]=ii("0x3e"),u.id=6;h={};h.type=ii("0x70"),h.id=7;a={};a[ii("0x71")]=ii("0x85"),a.id=8;c={};c[ii("0x43")]="repeated",c[ii("0x71")]="Floor",c.id=9;v={};v[ii("0x71")]="int32",v.id=10;l={};l[ii("0x2")]=g,l[ii("0x2b")]=_,l[ii("0x64")]=r,l[ii("0x19")]=s,l.x=o,l.y=u,l.mid=h,l.version=a,l.floors=c,l.bcode=v;p={};p.fields=l;x={};x[ii("0x42")]=e,x[ii("0xd")]=b,x.Floor=d,x[ii("0x6c")]=p;m={};m[ii("0xe")]=S,m[ii("0x1")]=x;y={};y[ii("0x43")]=ii("0x3"),y[ii("0x71")]=ii("0x70"),y.id=1;w={};w[ii("0x43")]="repeated",w[ii("0x71")]=ii("0x3d"),w.id=2;g={};g.mid=y,g[ii("0x10")]=w;_={};_[ii("0x71")]="int32",_.id=1;r={};r[ii("0x71")]="string",r.id=2;s={};s[ii("0x71")]=ii("0x70"),s.id=3;o={};o[ii("0x71")]=ii("0x70"),o.id=4;u={};u[ii("0x43")]=ii("0xa0"),u.type=ii("0x74")+ii("0x5b"),u.id=5;h={};h[ii("0x43")]=ii("0xa0"),h[ii("0x71")]=ii("0x74")+ii("0x5b"),h.id=6;a={};a[ii("0x43")]=ii("0xa0"),a[ii("0x71")]=ii("0x74")+ii("0x5b"),a.id=7;c={};c.gid=_,c[ii("0x27")]=r,c[ii("0x2")]=s,c[ii("0x99")]=o,c[ii("0x60")]=u,c[ii("0x34")]=h,c.accessible=a;v={};v[ii("0xe")]=c;l={};l[ii("0x43")]=ii("0x3"),l[ii("0x71")]="int32",l.id=1;e={};e[ii("0x71")]=ii("0x85"),e.id=2;b={};b[ii("0x71")]=ii("0x85"),b.id=3;d={};d[ii("0x43")]=ii("0xa0"),d[ii("0x71")]="IndexInfo",d.id=4;p={};p[ii("0x9c")]=!1;S={};S[ii("0x43")]=ii("0xa0"),S[ii("0x71")]=ii("0x5e"),S.id=5,S.options=p;x={};x.type=ii("0x85"),x.id=6;y={};y[ii("0x43")]=ii("0xa0"),y.type=ii("0x70"),y.id=7;w={};w[ii("0x43")]="repeated",w[ii("0x71")]="string",w.id=8;_={};_[ii("0x67")]=l,_[ii("0x71")]=e,_[ii("0x7a")]=b,_[ii("0x76")]=d,_[ii("0xa7")]=S,_[ii("0x92")]=x,_[ii("0x8d")]=y,_[ii("0x38")]=w;r={};r[ii("0x9c")]=!1;s={};s[ii("0x43")]=ii("0xa0"),s[ii("0x71")]=ii("0x85"),s.id=1,s.options=r;o={};o[ii("0x76")]=s;u={};u[ii("0xe")]=o;h={};h[ii("0x96")]=u;a={};a[ii("0xe")]=_,a.nested=h;c={};c[ii("0x3d")]=v,c[ii("0x74")+ii("0x5b")]=a;p={};p.fields=g,p[ii("0x1")]=c;l={};l[ii("0x43")]=ii("0x3"),l[ii("0x71")]=ii("0x70"),l.id=1;e={};e[ii("0x43")]="required",e[ii("0x71")]=ii("0x85"),e.id=2;b={};b.type=ii("0x3e"),b.id=3;d={};d[ii("0x43")]=ii("0xa0"),d[ii("0x71")]=ii("0x33")+ii("0x77"),d.id=4;S={};S.rule=ii("0xa0"),S[ii("0x71")]=ii("0x33")+"Info",S.id=5;x={rule:"repeated"};x[ii("0x71")]="GeneralGeo"+ii("0x77"),x.id=6;y={};y[ii("0x43")]="repeated",y[ii("0x71")]="GeneralGeoInfo",y.id=7;w={};w[ii("0x43")]="repeated",w[ii("0x71")]=ii("0x33")+ii("0x77"),w.id=8;r={};r.rule=ii("0xa0"),r[ii("0x71")]=ii("0x33")+"Info",r.id=9;s={};s[ii("0x43")]=ii("0xa0"),s[ii("0x71")]="GeneralGeoInfo",s.id=10;o={};o[ii("0x43")]=ii("0xa0"),o.type=ii("0x33")+ii("0x77"),o.id=11;u={};u[ii("0x43")]=ii("0xa0"),u[ii("0x71")]=ii("0x33")+"Info",u.id=12;_={};_[ii("0x43")]=ii("0xa0"),_[ii("0x71")]=ii("0x33")+ii("0x77"),_.id=13;h={};h[ii("0x99")]=l,h[ii("0x23")]=e,h[ii("0x6f")]=b,h[ii("0x81")+"r"]=d,h[ii("0x65")]=S,h.labelLayer=x,h[ii("0x9a")]=y,h[ii("0x4d")+"er"]=w,h[ii("0x73")+"elLayer"]=r,h[ii("0x9f")]=s,h[ii("0x8c")]=o,h[ii("0x51")+ii("0x1b")]=u,h[ii("0x93")+ii("0x49")]=_;v={};v[ii("0x43")]=ii("0x3"),v[ii("0x71")]=ii("0x85"),v.id=1;a={};a[ii("0x71")]="string",a.id=2;g={};g[ii("0x71")]=ii("0x3e"),g.id=3;c={type:"float",id:4},l={};l[ii("0x43")]="repeated",l[ii("0x71")]=ii("0x96"),l.id=5;e={};e[ii("0x9c")]=!1;b={};b[ii("0x43")]=ii("0xa0"),b.type=ii("0x5e"),b.id=6,b[ii("0xa")]=e;d={};d.eid=v,d[ii("0x55")]=a,d[ii("0x6f")]=g,d[ii("0xc")]=c,d.idxs=l,d[ii("0xa7")]=b;S={packed:!1},x={};x[ii("0x43")]=ii("0xa0"),x.type=ii("0x85"),x.id=1,x[ii("0xa")]=S;y={};y[ii("0x76")]=x;w={};w[ii("0xe")]=y;r={};r[ii("0x96")]=w;s={};s.fields=d,s[ii("0x1")]=r;o={};o["GeneralGeo"+ii("0x77")]=s;u={};u[ii("0xe")]=h,u[ii("0x1")]=o;_={};_.rule=ii("0x3"),_[ii("0x71")]=ii("0x70"),_.id=1;e={};e[ii("0x43")]=ii("0x3"),e[ii("0x71")]=ii("0x85"),e.id=2;v={};v[ii("0x43")]="repeated",v[ii("0x71")]=ii("0x82")+ii("0x7c"),v.id=3;a={rule:"repeated"};a.type=ii("0x29")+"fo",a.id=4;g={};g[ii("0x43")]=ii("0xa0"),g.type=ii("0x41")+"fo",g.id=5;c={};c.rule=ii("0xa0"),c.type=ii("0x40"),c.id=6;l={};l[ii("0x43")]=ii("0xa0"),l.type=ii("0x59")+"Info",l.id=7;b={};b[ii("0x43")]=ii("0xa0"),b[ii("0x71")]=ii("0x1d")+"o",b.id=8;S={};S[ii("0x43")]=ii("0xa0"),S.type=ii("0x50")+"fo",S.id=9;x={};x[ii("0x43")]=ii("0xa0"),x[ii("0x71")]=ii("0x14")+ii("0x8b"),x.id=10;y={};y[ii("0x43")]=ii("0xa0"),y[ii("0x71")]=ii("0x2d")+ii("0x98"),y.id=11;w={};w[ii("0x99")]=_,w[ii("0x23")]=e,w[ii("0x81")+"r"]=v,w[ii("0x65")]=a,w.labelLayer=g,w[ii("0x9a")]=c,w[ii("0x4d")+"er"]=l,w.liftLayer=b,w[ii("0x8c")]=S,w["polygonLab"+ii("0x18")]=x,w[ii("0x93")+"delLayer"]=y;d={};d.rule=ii("0x3"),d[ii("0x71")]=ii("0x85"),d.id=1;r={};r.type=ii("0x70"),r.id=2;s={};s[ii("0x71")]=ii("0x85"),s.id=3;h={};h[ii("0x71")]=ii("0x70"),h.id=4;o={};o.type=ii("0x70"),o.id=5;_={};_[ii("0x71")]="int32",_.id=6;e={};e[ii("0x71")]="int32",e.id=7;v={};v.eid=d,v[ii("0x7b")]=r,v.type=s,v[ii("0x2b")]=h,v[ii("0x64")]=o,v[ii("0x31")]=_,v[ii("0x5d")]=e;a={};a.fields=v;g={};g[ii("0x43")]="required",g[ii("0x71")]=ii("0x85"),g.id=1;c={};c[ii("0x71")]="string",c.id=2;l={};l[ii("0x71")]="int32",l.id=3;b={};b[ii("0x71")]=ii("0x70"),b.id=4;S={};S[ii("0x71")]=ii("0x70"),S.id=5;x={};x.type=ii("0x85"),x.id=6;y={};y[ii("0x71")]=ii("0x85"),y.id=7;d={};d[ii("0x71")]="int32",d.id=8;r={};r[ii("0x67")]=g,r[ii("0x7b")]=c,r[ii("0x71")]=l,r[ii("0x2b")]=b,r[ii("0x64")]=S,r[ii("0x31")]=x,r[ii("0x5d")]=y,r[ii("0xf")]=d;s={};s[ii("0xe")]=r;h={};h[ii("0x43")]=ii("0x3"),h[ii("0x71")]=ii("0x85"),h.id=1;o={};o[ii("0x71")]=ii("0x70"),o.id=2;_={};_[ii("0x71")]=ii("0x85"),_.id=3;e={};e[ii("0x71")]=ii("0x70"),e.id=4;v={};v.type=ii("0x70"),v.id=5;g={};g[ii("0x71")]=ii("0x85"),g.id=6;c={};c.type=ii("0x85"),c.id=7;l={};l[ii("0x67")]=h,l[ii("0x7b")]=o,l[ii("0x71")]=_,l[ii("0x2b")]=e,l[ii("0x64")]=v,l[ii("0x31")]=g,l.maxlevel=c;b={};b.fields=l;S={};S[ii("0x43")]=ii("0x3"),S[ii("0x71")]=ii("0x85"),S.id=1;x={};x[ii("0x71")]=ii("0x70"),x.id=2;y={};y[ii("0x71")]="int32",y.id=3;d={};d[ii("0x71")]=ii("0x70"),d.id=4;r={};r[ii("0x71")]=ii("0x70"),r.id=5;h={type:"int32",id:6},o={};o[ii("0x71")]=ii("0x85"),o.id=7;_={};_[ii("0x67")]=S,_.fid=x,_[ii("0x71")]=y,_[ii("0x2b")]=d,_[ii("0x64")]=r,_[ii("0x31")]=h,_[ii("0x5d")]=o;e={};e[ii("0xe")]=_;v={};v[ii("0x43")]=ii("0x3"),v.type=ii("0x85"),v.id=1;g={};g.type=ii("0x70"),g.id=2;c={};c[ii("0x71")]=ii("0x85"),c.id=3;l={};l[ii("0x71")]=ii("0x70"),l.id=4;S={};S.type=ii("0x70"),S.id=5;x={};x[ii("0x71")]="int32",x.id=6;y={type:"int32",id:7},d={};d.eid=v,d[ii("0x7b")]=g,d[ii("0x71")]=c,d.name=l,d[ii("0x64")]=S,d[ii("0x31")]=x,d[ii("0x5d")]=y;r={};r[ii("0xe")]=d;h={};h[ii("0x43")]="required",h[ii("0x71")]=ii("0x85"),h.id=1;o={};o.type=ii("0x70"),o.id=2;_={};_.type=ii("0x85"),_.id=3;v={};v.type=ii("0x85"),v.id=4;g={};g[ii("0x71")]=ii("0x70"),g.id=5;c={};c.type=ii("0x85"),c.id=6;l={};l[ii("0x71")]=ii("0x85"),l.id=7;S={};S[ii("0x67")]=h,S[ii("0x7b")]=o,S[ii("0x71")]=_,S[ii("0x92")]=v,S[ii("0x2a")]=g,S[ii("0x31")]=c,S[ii("0x5d")]=l;x={};x[ii("0xe")]=S;y={};y[ii("0x43")]=ii("0x3"),y[ii("0x71")]="int32",y.id=1;d={};d[ii("0x71")]=ii("0x70"),d.id=2;h={};h[ii("0x71")]=ii("0x85"),h.id=3;o={};o[ii("0x71")]=ii("0x85"),o.id=4;_={};_[ii("0x71")]="int32",_.id=5;v={};v[ii("0x71")]=ii("0x85"),v.id=6;g={};g[ii("0x67")]=y,g[ii("0x7b")]=d,g[ii("0x71")]=h,g[ii("0x92")]=o,g[ii("0x31")]=_,g.maxlevel=v;c={};c[ii("0xe")]=g;l={};l.rule=ii("0x3"),l[ii("0x71")]=ii("0x85"),l.id=1;S={};S.type=ii("0x70"),S.id=2;y={};y[ii("0x71")]="int32",y.id=3;d={};d[ii("0x71")]="float",d.id=4;h={};h[ii("0x71")]=ii("0x70"),h.id=5;o={};o[ii("0x71")]="string",o.id=6;_={};_[ii("0x71")]=ii("0x3e"),_.id=7;v={};v[ii("0x67")]=l,v[ii("0x7b")]=S,v[ii("0x71")]=y,v[ii("0x1a")]=d,v[ii("0x2b")]=h,v[ii("0x64")]=o,v.angle=_;g={};g.fields=v;l={};l[ii("0x43")]=ii("0x3"),l[ii("0x71")]="int32",l.id=1;S={type:"string",id:2},y={};y[ii("0x71")]=ii("0x85"),y.id=3;d={};d[ii("0x71")]=ii("0x70"),d.id=4;h={};h[ii("0x71")]=ii("0x70"),h.id=5;o={};o[ii("0x71")]="int32",o.id=6;_={};_.type=ii("0x85"),_.id=7;v={};v[ii("0x67")]=l,v[ii("0x7b")]=S,v[ii("0x71")]=y,v.name=d,v[ii("0x64")]=h,v[ii("0x31")]=o,v[ii("0x5d")]=_;l={};l[ii("0xe")]=v;S={};S[ii("0x82")+"nfo"]=a,S[ii("0x29")+"fo"]=s,S[ii("0x41")+"fo"]=b,S[ii("0x59")+ii("0x77")]=e,S.POIBizInfo=r,S[ii("0x1d")+"o"]=x,S[ii("0x50")+"fo"]=c,S[ii("0x14")+ii("0x8b")]=g,S[ii("0x2d")+"delBizInfo"]=l;y={};y[ii("0xe")]=w,y.nested=S;d={};d[ii("0x43")]="required",d.type=ii("0x70"),d.id=1;h={};h[ii("0x43")]="required",h.type=ii("0x85"),h.id=2;o={};o[ii("0x43")]=ii("0xa0"),o[ii("0x71")]=ii("0x6"),o.id=3;_={};_[ii("0x43")]="repeated",_[ii("0x71")]=ii("0x79")+"t",_.id=4;v={};v[ii("0x43")]=ii("0xa0"),v[ii("0x71")]=ii("0x88"),v.id=5;a={};a[ii("0x43")]=ii("0xa0"),a[ii("0x71")]="NaviModel",a.id=6;s={};s[ii("0x9c")]=!1;b={};b[ii("0x43")]=ii("0xa0"),b[ii("0x71")]=ii("0x85"),b.id=7,b[ii("0xa")]=s;e={};e[ii("0x43")]=ii("0xa0"),e[ii("0x71")]="NaviExtent",e.id=8;r={};r[ii("0x99")]=d,r[ii("0x23")]=h,r[ii("0x57")]=o,r[ii("0x9e")+"ts"]=_,r[ii("0x17")]=v,r.naviModels=a,r.nextFloors=b,r[ii("0x3f")+"s"]=e;x={};x[ii("0x71")]="int32",x.id=1;c={};c.type=ii("0x85"),c.id=2;g={};g[ii("0x71")]=ii("0x85"),g.id=3;l={};l[ii("0x71")]=ii("0x85"),l.id=4;w={};w[ii("0x71")]="int32",w.id=5;S={};S[ii("0x71")]=ii("0x70"),S.id=6;s={type:"string",id:7},d={};d[ii("0x71")]=ii("0x70"),d.id=8;h={};h[ii("0x71")]="int32",h.id=9;o={};o[ii("0x71")]=ii("0x70"),o.id=10;_={};_[ii("0x71")]=ii("0x85"),_.id=11;v={};v[ii("0x9c")]=!1;a={};a[ii("0x43")]=ii("0xa0"),a.type=ii("0x85"),a.id=12,a[ii("0xa")]=v;b={};b[ii("0x9c")]=!1;e={};e[ii("0x43")]=ii("0xa0"),e[ii("0x71")]=ii("0x5e"),e.id=13,e[ii("0xa")]=b;v={default:0},b={type:"int32",id:14};b[ii("0xa")]=v;v={};v[ii("0xa3")]=x,v.nodeType=c,v[ii("0x7e")]=g,v[ii("0x22")]=l,v[ii("0x94")]=w,v.liftFloor=S,v[ii("0x52")]=s,v[ii("0x55")]=d,v[ii("0x15")+"pe"]=h,v[ii("0x89")+ii("0xa2")]=o,v[ii("0x86")]=_,v[ii("0x76")]=a,v.pts=e,v[ii("0x7a")]=b;c={};c[ii("0xe")]=v;g={};g[ii("0x71")]=ii("0x85"),g.id=1;l={};l[ii("0x71")]="int32",l.id=2;w={};w[ii("0x71")]=ii("0x85"),w.id=3;S={};S[ii("0x71")]=ii("0x5e"),S.id=4;s={};s[ii("0x71")]=ii("0x85"),s.id=5;d={};d[ii("0x71")]=ii("0x70"),d.id=6;h={};h[ii("0x71")]=ii("0x85"),h.id=7;o={};o[ii("0x71")]=ii("0x70"),o.id=8;_={};_[ii("0x71")]=ii("0x70"),_.id=9;a={};a[ii("0x71")]=ii("0x85"),a.id=10;e={};e[ii("0x9c")]=!1;b={};b[ii("0x43")]="repeated",b[ii("0x71")]=ii("0x85"),b.id=11,b[ii("0xa")]=e;v={};v[ii("0x9c")]=!1;e={};e[ii("0x43")]=ii("0xa0"),e[ii("0x71")]=ii("0x5e"),e.id=12,e[ii("0xa")]=v;v={};v[ii("0x47")]=g,v[ii("0xa6")]=l,v.enode=w,v[ii("0x8a")]=S,v[ii("0xa5")]=s,v[ii("0x2b")]=d,v.entry=h,v[ii("0x48")]=o,v[ii("0x55")]=_,v[ii("0x9d")]=a,v[ii("0x76")]=b,v[ii("0xa7")]=e;d={};d[ii("0xe")]=v;h={};h.rule=ii("0x3"),h[ii("0x71")]=ii("0x85"),h.id=1;o={};o[ii("0x72")]=0;_={};_[ii("0x43")]=ii("0x3"),_.type=ii("0x85"),_.id=2,_[ii("0xa")]=o;a={};a.type=ii("0x70"),a.id=3;b={};b[ii("0x43")]=ii("0xa0"),b[ii("0x71")]=ii("0x70"),b.id=4;e={};e[ii("0x9c")]=!1;v={};v[ii("0x43")]=ii("0xa0"),v[ii("0x71")]="int32",v.id=5,v[ii("0xa")]=e;o={};o[ii("0x9c")]=!1;e={};e[ii("0x43")]="repeated",e[ii("0x71")]=ii("0x5e"),e.id=6,e[ii("0xa")]=o;o={};o.id=h,o[ii("0x71")]=_,o[ii("0x55")]=a,o[ii("0x58")]=b,o[ii("0x76")]=v,o.pts=e;_={};_[ii("0xe")]=o;a={};a[ii("0x43")]="required",a[ii("0x71")]=ii("0x85"),a.id=1;b={};b[ii("0x9c")]=!1;v={};v[ii("0x43")]=ii("0xa0"),v.type=ii("0x85"),v.id=2,v.options=b;e={packed:!1},o={};o[ii("0x43")]=ii("0xa0"),o[ii("0x71")]="int32",o.id=3,o[ii("0xa")]=e;b={};b[ii("0x9c")]=!1;e={};e[ii("0x43")]=ii("0xa0"),e.type=ii("0x85"),e.id=4,e[ii("0xa")]=b;b={};b[ii("0x9b")]=a,b.doorids=v,b[ii("0x1e")]=o,b[ii("0x1c")]=e;a={};a[ii("0xe")]=b;v={};v[ii("0x43")]=ii("0x3"),v[ii("0x71")]=ii("0x85"),v.id=1;o={};o[ii("0x9c")]=!1;e={};e.rule=ii("0xa0"),e[ii("0x71")]="double",e.id=2,e.options=o;b={};b[ii("0x43")]="repeated",b[ii("0x71")]=ii("0x96"),b.id=3;o={};o.id=v,o[ii("0xa7")]=e,o[ii("0x76")]=b;e={packed:!1},b={};b[ii("0x43")]=ii("0xa0"),b[ii("0x71")]=ii("0x85"),b.id=1,b.options=e;e={};e[ii("0x76")]=b;b={};b[ii("0xe")]=e;e={};e[ii("0x96")]=b;b={};b[ii("0xe")]=o,b[ii("0x1")]=e;e={};e[ii("0x6")]=c,e[ii("0x79")+"t"]=d,e[ii("0x88")]=_,e[ii("0xa4")]=a,e[ii("0x54")]=b;b={};b.fields=r,b[ii("0x1")]=e;e={};e[ii("0x6e")]=O,e.Scene=m,e[ii("0x8e")]=p,e[ii("0x7d")]=u,e[ii("0x61")]=y,e[ii("0x12")]=b;b={};b[ii("0xa")]=i,b[ii("0x1")]=e;e={};e.protobuf=b;b={};b[ii("0x1")]=e,this.id=b};F=function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.si=null};Object.assign(F.prototype,{oi:function(){this.si||(this.si=Jt.a.Root.fromJSON((new("undefined"!=typeof KjWzhWlSj&&KjWzhWlSj.ProtoDef?KjWzhWlSj.ProtoDef:ni)).id))},ui:function(t){return this.oi(),this.si.lookup("Map").decode(t)},ai:function(t){for(var i=0;i<t.buffers.length;i++){var n=t.buffers[i];if(1==n.btype)return this.fi(n.data)}return null},ci:function(t){for(var i=0;i<t.buffers.length;i++){var n=t.buffers[i];if(7==n.btype)return this.li(n.data)}return null},vi:function(t,i){for(var n=0;n<t.buffers.length;n++){var r=t.buffers[n];if(2==r.btype&&r.gid==i)return this.di(r.data)}return null},yi:function(t,i){for(var n=0;n<t.buffers.length;n++){var r=t.buffers[n];if(3==r.btype&&r.gid==i)return this.bi(r.data)}return null},pi:function(t,i){for(var n=0;n<t.buffers.length;n++){var r=t.buffers[n];if(4==r.btype&&r.gid==i)return this.xi(r.data)}return null},mi:function(t,i){for(var n=0;n<t.buffers.length;n++){var r=t.buffers[n];if(5===r.btype&&r.gid===i)return this.xi(r.data)}return null},wi:function(t,i){for(var n=0;n<t.buffers.length;n++){var r=t.buffers[n];if(6===r.btype&&r.gid===i)return this.xi(r.data)}return null},fi:function(t){this.oi();var i=this.si.lookup("Scene").decode(t);return i.levels=[],i.layerGroups.forEach(function(t){i.levels.push(t.gid)}),i},li:function(t){return this.oi(),this.si.lookup("Gate").decode(t)},di:function(t){return this.oi(),this.si.lookup("FloorGeo").decode(t)},bi:function(t){return this.oi(),this.si.lookup("FloorBiz").decode(t)},xi:function(t){return this.oi(),this.si.lookup("FloorNavi").decode(t)}});var ri=F;r=function t(){var i;!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),void 0!==Xt.a.global().KjWzhWlSj&&Xt.a.global().KjWzhWlSj.ProtoReaderStatic?(i=Xt.a.global().KjWzhWlSj.ProtoReaderStatic,this._i=new i):this._i=new ri};Object.assign(r.prototype,{ui:function(t){return this._i.ui(t)},ai:function(t){return this._i.ai(t)},ci:function(t){return this._i.ci(t)},vi:function(t,i){return this._i.vi(t,i)},yi:function(t,i){return this._i.yi(t,i)},pi:function(t,i){return this._i.pi(t,i)},mi:function(t,i){return this._i.mi(t,i)},wi:function(t,i){return this._i.wi(t,i)},fi:function(t){return this._i.fi(t)},li:function(t){return this._i.li(t)},di:function(t){return this._i.di(t)},bi:function(t){return this._i.bi(t)},xi:function(t){return this._i.xi(t)}});var ei,si,oi,ui,hi,ai=r,s="https://console.fengmap.com/api-s/",fi={domain:s,online:s+"sdk/check",check:s+"sdk/auth/web",download:s+"sdk/auth/obtainMapRoute",downloadTile:s+"sdk/authLayered/obtainMapRoute",collect:s+"sdk/collect",themeUrl:s+"webtheme/",externalModelURL:s+"webmodel/"};function ci(t,i){for(var n,r=0,e=0,e=0;e<8;e++)1&i&&(r^=t),n=128&t,t<<=1,n&&(t^=27),i>>=1;return r}function li(t,i,n){for(var r=0;r<4;r++)t[0+r]^=i[16*n+4*r],t[4+r]^=i[16*n+4*r+1],t[8+r]^=i[16*n+4*r+2],t[12+r]^=i[16*n+4*r+3]}function vi(t){for(var i,n,r,e=1;e<4;e++)for(n=0;n<e;){for(r=t[4*e+4-1],i=3;0<i;i--)t[4*e+i]=t[4*e+i-1];t[4*e+0]=r,n++}}function di(t){for(var i,n,r,e=0;e<4;e++)for(i=0;i<4;i++)n=(240&t[4*e+i])>>4,r=15&t[4*e+i],t[4*e+i]=si[16*n+r]}function yi(t){for(var i=0;i<4;i++)t[i]=ei[16*((240&t[i])>>4)+(15&t[i])]}function bi(t){return parseInt(t,16)}var pi=(ei=[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],si=[82,9,106,213,48,54,165,56,191,64,163,158,129,243,215,251,124,227,57,130,155,47,255,135,52,142,67,68,196,222,233,203,84,123,148,50,166,194,35,61,238,76,149,11,66,250,195,78,8,46,161,102,40,217,36,178,118,91,162,73,109,139,209,37,114,248,246,100,134,104,152,22,212,164,92,204,93,101,182,146,108,112,72,80,253,237,185,218,94,21,70,87,167,141,157,132,144,216,171,0,140,188,211,10,247,228,88,5,184,179,69,6,208,44,30,143,202,63,15,2,193,175,189,3,1,19,138,107,58,145,17,65,79,103,220,234,151,242,207,206,240,180,230,115,150,172,116,34,231,173,53,133,226,249,55,232,28,117,223,110,71,241,26,113,29,41,197,137,111,183,98,14,170,24,190,27,252,86,62,75,198,210,121,32,154,219,192,254,120,205,90,244,31,221,168,51,136,7,199,49,177,18,16,89,39,128,236,95,96,81,127,169,25,181,74,13,45,229,122,159,147,201,156,239,160,224,59,77,174,42,245,176,200,235,187,60,131,83,153,97,23,43,4,126,186,119,214,38,225,105,20,99,85,33,12,125],hi=[2,ui=oi=0,0,0],{decryption:function(t,i){for(var n=Array(16),r=0;r<16;r++)n[r]=16*bi(i.substr(2*r,1))+bi(i.substr(2*r+1,1));var e=Array(16);for(r=0;r<16;r++)e[r]=16*bi(t.substr(2*r,1))+bi(t.substr(2*r+1,1));var s=Array(16);switch(r=[],n.length){default:case 16:oi=4,ui=10;break;case 24:oi=6,ui=12;break;case 32:oi=8,ui=14}for(var o=r=Array(16*(ui+1)),u=Array(4),h=4*(ui+1),a=0;a<oi;a++)o[4*a+0]=n[4*a+0],o[4*a+1]=n[4*a+1],o[4*a+2]=n[4*a+2],o[4*a+3]=n[4*a+3];for(a=oi;a<h;a++){if(u[0]=o[4*(a-1)+0],u[1]=o[4*(a-1)+1],u[2]=o[4*(a-1)+2],u[3]=o[4*(a-1)+3],0==a%oi){for(var f=void 0,c=void 0,f=(n=u)[0],c=0;c<3;c++)n[c]=n[c+1];if(n[3]=f,yi(u),1==(n=a/oi))hi[0]=1;else if(1<n)for(hi[0]=2,n--;0<n-1;)hi[0]=ci(hi[0],2),n--;(c=n=u)[0]=n[0]^(f=hi)[0],c[1]=n[1]^f[1],c[2]=n[2]^f[2],c[3]=n[3]^f[3]}else 6<oi&&4==a%oi&&yi(u);o[4*a+0]=o[4*(a-oi)+0]^u[0],o[4*a+1]=o[4*(a-oi)+1]^u[1],o[4*a+2]=o[4*(a-oi)+2]^u[2],o[4*a+3]=o[4*(a-oi)+3]^u[3]}for(o=Array(16),u=0;u<4;u++)for(a=0;a<4;a++)o[4*u+a]=e[u+4*a];for(li(o,r,ui),e=ui-1;1<=e;e--)for(vi(o),di(o),li(o,r,e),u=o,a=[14,9,13,11],h=c=void 0,n=Array(4),f=Array(4),h=0;h<4;h++){for(c=0;c<4;c++)n[c]=u[4*c+h];var l=n,v=f;for(v[0]=ci((c=a)[0],l[0])^ci(c[3],l[1])^ci(c[2],l[2])^ci(c[1],l[3]),v[1]=ci(c[1],l[0])^ci(c[0],l[1])^ci(c[3],l[2])^ci(c[2],l[3]),v[2]=ci(c[2],l[0])^ci(c[1],l[1])^ci(c[0],l[2])^ci(c[3],l[3]),v[3]=ci(c[3],l[0])^ci(c[2],l[1])^ci(c[1],l[2])^ci(c[0],l[3]),c=0;c<4;c++)u[4*c+h]=f[c]}for(vi(o),di(o),li(o,r,0),u=0;u<4;u++)for(a=0;a<4;a++)s[u+4*a]=o[4*u+a];for(e="",r=0;r<16;++r)e+=o=(o=s[r].toString(16)).length<2?"0"+o:o;return e}});function xi(t,i){var n=(65535&t)+(65535&i);return(t>>16)+(i>>16)+(n>>16)<<16|65535&n}function mi(t,i,n,r,e,s){return xi((s=xi(xi(i,t),xi(r,s)))<<(e=e)|s>>>32-e,n)}function wi(t,i,n,r,e,s,o){return mi(i&n|~i&r,t,i,e,s,o)}function _i(t,i,n,r,e,s,o){return mi(i&r|n&~r,t,i,e,s,o)}function gi(t,i,n,r,e,s,o){return mi(i^n^r,t,i,e,s,o)}function Oi(t,i,n,r,e,s,o){return mi(n^(i|~r),t,i,e,s,o)}function Ei(t,i){var n,r,e,s;t[i>>5]|=128<<i%32,t[14+(i+64>>>9<<4)]=i;for(var o=1732584193,u=-271733879,h=-1732584194,a=271733878,f=0;f<t.length;f+=16)o=wi(n=o,r=u,e=h,s=a,t[f],7,-680876936),a=wi(a,o,u,h,t[f+1],12,-389564586),h=wi(h,a,o,u,t[f+2],17,606105819),u=wi(u,h,a,o,t[f+3],22,-1044525330),o=wi(o,u,h,a,t[f+4],7,-176418897),a=wi(a,o,u,h,t[f+5],12,1200080426),h=wi(h,a,o,u,t[f+6],17,-1473231341),u=wi(u,h,a,o,t[f+7],22,-45705983),o=wi(o,u,h,a,t[f+8],7,1770035416),a=wi(a,o,u,h,t[f+9],12,-1958414417),h=wi(h,a,o,u,t[f+10],17,-42063),u=wi(u,h,a,o,t[f+11],22,-1990404162),o=wi(o,u,h,a,t[f+12],7,1804603682),a=wi(a,o,u,h,t[f+13],12,-40341101),h=wi(h,a,o,u,t[f+14],17,-1502002290),o=_i(o,u=wi(u,h,a,o,t[f+15],22,1236535329),h,a,t[f+1],5,-165796510),a=_i(a,o,u,h,t[f+6],9,-1069501632),h=_i(h,a,o,u,t[f+11],14,643717713),u=_i(u,h,a,o,t[f],20,-373897302),o=_i(o,u,h,a,t[f+5],5,-701558691),a=_i(a,o,u,h,t[f+10],9,38016083),h=_i(h,a,o,u,t[f+15],14,-660478335),u=_i(u,h,a,o,t[f+4],20,-405537848),o=_i(o,u,h,a,t[f+9],5,568446438),a=_i(a,o,u,h,t[f+14],9,-1019803690),h=_i(h,a,o,u,t[f+3],14,-187363961),u=_i(u,h,a,o,t[f+8],20,1163531501),o=_i(o,u,h,a,t[f+13],5,-1444681467),a=_i(a,o,u,h,t[f+2],9,-51403784),h=_i(h,a,o,u,t[f+7],14,1735328473),o=gi(o,u=_i(u,h,a,o,t[f+12],20,-1926607734),h,a,t[f+5],4,-378558),a=gi(a,o,u,h,t[f+8],11,-2022574463),h=gi(h,a,o,u,t[f+11],16,1839030562),u=gi(u,h,a,o,t[f+14],23,-35309556),o=gi(o,u,h,a,t[f+1],4,-1530992060),a=gi(a,o,u,h,t[f+4],11,1272893353),h=gi(h,a,o,u,t[f+7],16,-155497632),u=gi(u,h,a,o,t[f+10],23,-1094730640),o=gi(o,u,h,a,t[f+13],4,681279174),a=gi(a,o,u,h,t[f],11,-358537222),h=gi(h,a,o,u,t[f+3],16,-722521979),u=gi(u,h,a,o,t[f+6],23,76029189),o=gi(o,u,h,a,t[f+9],4,-640364487),a=gi(a,o,u,h,t[f+12],11,-421815835),h=gi(h,a,o,u,t[f+15],16,530742520),o=Oi(o,u=gi(u,h,a,o,t[f+2],23,-995338651),h,a,t[f],6,-198630844),a=Oi(a,o,u,h,t[f+7],10,1126891415),h=Oi(h,a,o,u,t[f+14],15,-1416354905),u=Oi(u,h,a,o,t[f+5],21,-57434055),o=Oi(o,u,h,a,t[f+12],6,1700485571),a=Oi(a,o,u,h,t[f+3],10,-1894986606),h=Oi(h,a,o,u,t[f+10],15,-1051523),u=Oi(u,h,a,o,t[f+1],21,-2054922799),o=Oi(o,u,h,a,t[f+8],6,1873313359),a=Oi(a,o,u,h,t[f+15],10,-30611744),h=Oi(h,a,o,u,t[f+6],15,-1560198380),u=Oi(u,h,a,o,t[f+13],21,1309151649),o=Oi(o,u,h,a,t[f+4],6,-145523070),a=Oi(a,o,u,h,t[f+11],10,-1120210379),h=Oi(h,a,o,u,t[f+2],15,718787259),u=Oi(u,h,a,o,t[f+9],21,-343485551),o=xi(o,n),u=xi(u,r),h=xi(h,e),a=xi(a,s);return[o,u,h,a]}function Mi(t){for(var i="",n=32*t.length,r=0;r<n;r+=8)i+=String.fromCharCode(t[r>>5]>>>r%32&255);return i}function ji(t){var i=[];for(i[(t.length>>2)-1]=void 0,r=0;r<i.length;r+=1)i[r]=0;for(var n=8*t.length,r=0;r<n;r+=8)i[r>>5]|=(255&t.charCodeAt(r/8))<<r%32;return i}function ki(t){for(var i,n="0123456789abcdef",r="",e=0;e<t.length;e+=1)i=t.charCodeAt(e),r+=n.charAt(i>>>4&15)+n.charAt(15&i);return r}function Ri(t){return unescape(encodeURIComponent(t))}function Ti(t){return Mi(Ei(ji(t=Ri(t)),8*t.length))}function Ai(t,i){return function(t,i){var n,r=ji(t),e=[],s=[];for(e[15]=s[15]=void 0,16<r.length&&(r=Ei(r,8*t.length)),n=0;n<16;n+=1)e[n]=909522486^r[n],s[n]=**********^r[n];return i=Ei(e.concat(ji(i)),512+8*i.length),Mi(Ei(s.concat(i),640))}(Ri(t),Ri(i))}function Si(t,i,n){return i?n?Ai(i,t):ki(Ai(i,t)):n?Ti(t):ki(Ti(t))}var Ni={encryption:function(t){return Si(t)}};var Ii="0.0.0.0",E=function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t)};Object.assign(E.prototype,{gi:function(t,i,n,r,e){"undefined"==typeof KjWzhWlSj||!KjWzhWlSj.license||!KjWzhWlSj.license?r&&r():"string"==typeof i&&0!=i.length?(i=pi.decryption(i,"026685bf295f587b5dffc1f18d5dc27c"),n=Ni.encryption(n),i=pi.decryption(i,n),-1!==t.indexOf(i)?r&&r():e&&e()):e&&e()},Oi:function(t,i,n,r){var e;-1===i.indexOf("")&&-1===i.indexOf("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa")?(e=Ni.encryption("fengmap.localhost:"+t.appName),e=pi.decryption(t.key,e),-1!==i.indexOf(e)?this.gi(i,t.license,t.buildingID,function(){n&&n()},function(){r&&r(zt.LICENSE_ERROR)}):r&&r(zt.NAME_KEY_ERROR)):n&&n()},Ei:function(t,i,n){var r=this;void 0===t.mapURL?(t.enableCollect&&this.Mi(t),this.ji(function(){new Kt({method:"POST",url:fi.check,header:{"X-Requested-With":"XMLHttpRequest","Content-type":"application/json;charset=utf-8"},data:r.ki(t)}).ri(function(){i&&i()},function(t){n&&n(t)},function(){i&&i()})},function(){i&&i()})):i&&i()},ji:function(t,i){new Kt({method:"GET",url:fi.online}).ri(function(){t&&t()},function(){i&&i()},function(){i&&i()})},ki:function(t,i){var n=Xt.a.global().location.host,r=n.indexOf(":");-1!==r&&(n=n.slice(0,r));r={mapId:t.buildingID,appName:t.appName};return i?r.keyValue=t.key:r.appKey=t.key,i?r.webUrl=n||Ii:r.webURL=n||Ii,r},Mi:function(t){if("undefined"!=typeof navigator&&"undefined"!=typeof document){var i={};-1!==navigator.userAgent.indexOf("Opera")?i.userAgent="Opera":-1!==navigator.userAgent.indexOf("Firefox")?i.userAgent="FF":-1!==navigator.userAgent.indexOf("Chrome")?i.userAgent="Chrome":-1!==navigator.userAgent.indexOf("Safari")&&0===navigator.userAgent.indexOf("Chrome")?i.userAgent="Safari":-1!==navigator.userAgent.indexOf("compatible")&&-1!==navigator.userAgent.indexOf("MSIE")?i.userAgent="IE":-1!==navigator.userAgent.indexOf("Trident")&&(i.userAgent="Edge"),i.product="JS",i.sdkVersion=u+"."+h,i.appName=t.appName,i.appkey=t.key;var n=document.createElement("canvas").getContext("experimental-webgl"),t=n.getExtension("WEBGL_debug_renderer_info");i.gpu=t?n.getParameter(t.UNMASKED_RENDERER_WEBGL):null,i.os=navigator.platform;for(var r=!0,e=navigator.userAgent,s=["Android","iPhone","SymbianOS","Windows Phone","iPad","iPod"],o=0;o<s.length;o++)if(0<e.indexOf(s[o])){r=!1;break}i.device=r?"pc":"mobile",new Kt({method:"POST",url:fi.collect,header:{"X-Requested-With":"XMLHttpRequest","Content-type":"application/json;charset=utf-8"},data:i}).ri()}}});var Li=new E;function Di(t){return function(t){if(Array.isArray(t))return Pi(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,i){if(t){if("string"==typeof t)return Pi(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Pi(t,i):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Pi(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}A=function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this._i=new ai,this.Ri=[]};Object.assign(A.prototype,{ei:function(){this.Ri.forEach(function(t){t.ei()}),this.Ri.length=0},Ti:function(t,i,n,r){var e;t.mapURL?(e=t.isPreview?t.isOutdoor?t.tile?t.mapURL+t.buildingID+"/separate/"+t.buildingID+i+".fmap":t.mapURL+t.buildingID+"/"+t.buildingID+i+".fmap":t.tile?t.mapURL+t.mapID+"/"+t.bid+"/"+t.buildingID+"/separate/"+t.buildingID+i+".fmap":t.mapURL+t.mapID+"/"+t.bid+"/"+t.buildingID+"/"+t.buildingID+i+".fmap":t.mapURLAbsolute?t.mapURL+t.buildingID+i+".fmap":t.isOutdoor?t.mapURL+t.buildingID+"/"+t.buildingID+i+".fmap":t.mapURL+t.mapID+"/"+t.bid+"/"+t.buildingID+"/"+t.buildingID+i+".fmap",n&&n(e)):(e={newFlag:"2",keyType:3,isSeparate:!1},t.isOutdoor||(e.bId=t.bid,e.rootMid=t.mapID),t=new Kt({method:"POST",url:""===i?fi.download:fi.downloadTile+"/"+t.buildingID+i+".fmap",header:{"X-Requested-With":"XMLHttpRequest","Content-type":"application/json;charset=utf-8"},data:Object.assign(e,Li.ki(t,!0))}),this.Ri.push(t),t.ri(function(t){n&&n(t.replace('"',"").replace('"',""))},function(t){r&&r(t)},function(t){r&&r(t)}))},Ai:function(n,r,e){var s=this;n.enableCollect=!0,Li.Ei(n,function(){s.Ti(n,"",function(t){t=new Kt({method:"GET",responseType:"arraybuffer",url:t});s.Ri.push(t),t.ri(function(t){var t=new Uint8Array(t),i=s._i.ui(t);n.isOutdoor?(t=[],i.keys&&0!==i.keys.length?t.push.apply(t,Di(i.keys)):t.push(i.key),n.isPreview?r&&r(i):Li.Oi(n,t,function(){r&&r(i)},function(t){e&&e(t)})):r&&r(i)},function(t){e&&e(t===zt.PATH_ERROR?zt.MAP_ID_URL_ERROR:t)},function(t){e&&e(t)})},function(t){e&&e(t)})},function(t){e&&e(t)})},Si:function(n,r,e){var s=this;n.enableCollect=!0,Li.Ei(n,function(){s.Ti(n,".scene",function(t){t=new Kt({method:"GET",responseType:"arraybuffer",url:t});s.Ri.push(t),t.ri(function(t){var t=new Uint8Array(t),i=s._i.fi(t);n.isOutdoor?(t=[],i.keys&&0!==i.keys.length?t.push.apply(t,Di(i.keys)):t.push(i.key),n.isPreview?r&&r(i):Li.Oi(n,t,function(){r&&r(i)},function(t){e&&e(t)})):r&&r(i)},function(t){e&&e(t)},function(t){e&&e(t)})},function(t){e&&e(t)})},function(t){e&&e(t)})},Ni:function(t,i,n){var r=this;this.Ti(t,".gate",function(t){t=new Kt({method:"GET",responseType:"arraybuffer",url:t});r.Ri.push(t),t.ri(function(t){t=new Uint8Array(t),t=r._i.li(t);i&&i(t)},function(t){n&&n(t)},function(t){n&&n(t)})},function(t){n&&n(t)})},Ii:function(t,i,n,r){var e=this;this.Ti(t,".floor."+i+".geo",function(t){t=new Kt({method:"GET",responseType:"arraybuffer",url:t});e.Ri.push(t),t.ri(function(t){t=new Uint8Array(t),t=e._i.di(t);n&&n(t)},function(t){r&&r(t)},function(t){r&&r(t)})},function(t){r&&r(t)})},Li:function(t,i,n,r){var e=this;this.Ti(t,".floor."+i+".biz",function(t){t=new Kt({method:"GET",responseType:"arraybuffer",url:t});e.Ri.push(t),t.ri(function(t){t=new Uint8Array(t),t=e._i.bi(t);n&&n(t)},function(t){r&&r(t)},function(t){r&&r(t)})},function(t){r&&r(t)})},Di:function(t,i,n,r){var e=this;this.Ti(t,".floor."+i+".navi",function(t){t=new Kt({method:"GET",responseType:"arraybuffer",url:t});e.Ri.push(t),t.ri(function(t){t=new Uint8Array(t),t=e._i.xi(t);n&&n(t)},function(t){r&&r(t)})},function(t){r&&r(t)},function(t){r&&r(t)})},Pi:function(t,i,n,r){var e=this;this.Ti(t,".floor."+i+".drive.navi",function(t){t=new Kt({method:"GET",responseType:"arraybuffer",url:t});e.Ri.push(t),t.ri(function(t){t=new Uint8Array(t),t=e._i.xi(t);n&&n(t)},function(t){r&&r(t)})},function(t){r&&r(t)},function(t){r&&r(t)})},Fi:function(t,i,n,r){var e=this;this.Ti(t,".floor."+i+".accessible.navi",function(t){t=new Kt({method:"GET",responseType:"arraybuffer",url:t});e.Ri.push(t),t.ri(function(t){t=new Uint8Array(t),t=e._i.xi(t);n&&n(t)},function(t){r&&r(t)})},function(t){r&&r(t)},function(t){r&&r(t)})}});var Fi=A;F=function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t)};Object.assign(F.prototype,{Ci:function(t,i){for(var n=[],r=0,e=0;e<t.length;e++){var s=t[e].idxs;if("number"==typeof(s=s||t[e]))for(;r<s;)n.push({x:i[r],y:i[r+1]}),r+=2;else for(var o=0;o<s.length;o++)for(var u=s[o];r<u;)n.push({x:i[r],y:i[r+1]}),r+=2}return n},Ui:function(t,i){for(var n=[],r=0,e=0;e<t.length;e++){var s=t[e].idxs;if("number"==typeof(s=s||t[e])){for(var o=[];r<s;)o.push({x:i[r],y:i[r+1]}),r+=2;n.push(o)}else for(var u=0;u<s.length;u++){for(var h=s[u],a=[];r<h;)a.push({x:i[r],y:i[r+1]}),r+=2;n.push(a)}}return n},Bi:function(t,i){for(var n=[],r=[],e=0,s=0;s<t.length;s++){var o=t[s].idxs;if("number"==typeof(o=o||t[s]))for(;e<o;)n.push({x:i[e],y:i[e+1]}),e+=2;else for(var u=0;u<o.length;u++)if(0===u)for(var h=o[u];e<h;)n.push({x:i[e],y:i[e+1]}),e+=2;else{for(var a=[],f=o[u];e<f;)a.push({x:i[e],y:i[e+1]}),e+=2;r.push(a)}}var c=[];return c.push(n),c.push.apply(c,r),c},Gi:function(t){for(var i=[],n=t.length,r=0;r<n;r++){for(var e=t[r],s={x:this.Zi(e.x),y:this.Zi(e.y)},o=i.length,u=!1,h=0;h<o;h++){var a=i[h];if(a.x==s.x&&a.y==s.y){u=!0;break}}u||i.push(s)}return i},Zi:function(t){return+t.toFixed(2)}});var Ci=new F;var Ui=Ui||{};Ui.G1="MULTIPOLYGON(((",Ui.G2="POINT(",Ui.G3="MULTILINESTRING((",Ui.GLEN1="MULTIPOLYGON(((".length,Ui.GLEN2="POINT(".length,Ui.GLEN3="MULTILINESTRING((".length;r=function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t)};Object.assign(r.prototype,{Ci:function(t){var i=[];if(0!=t.indexOf(Ui.G2))return[];t=t.substring(Ui.GLEN2,t.length-1).split(" ");return i.push({x:parseFloat(t[0]),y:parseFloat(t[1])}),i},Ui:function(t){if(0!=t.indexOf(Ui.G3))return[];for(var i=t.substring(Ui.GLEN3,t.length-2).split(","),n=[],r=0;r<i.length;r++){var e=i[r].split(" ");n.push({x:parseFloat(e[0]),y:parseFloat(e[1])})}return[n]},Bi:function(t){var i=[],n=[];if(0!=t.indexOf(Ui.G1))return[];for(var r=t.substring(Ui.GLEN1,t.length-3).split(/\),\(/),e=0;e<r.length;e++){var s=r[e].split(/,/);if(0==e)for(var o=0;o<s.length;o++){var u=s[o].split(" ");i.push({x:parseFloat(u[0]),y:parseFloat(u[1])})}else{for(var h=[],a=0;a<s.length;a++){var f=s[a].split(" ");h.push({x:parseFloat(f[0]),y:parseFloat(f[1])})}n.push(h)}}t=[];return t.push(i),t.push.apply(t,n),t},Gi:function(t){for(var i=[],n=t.length,r=0;r<n;r++){for(var e=t[r],s={x:this.Zi(e.x),y:this.Zi(e.y)},o=i.length,u=!1,h=0;h<o;h++){var a=i[h];if(a.x==s.x&&a.y==s.y){u=!0;break}}u||i.push(s)}return i},Zi:function(t){return+t.toFixed(2)}});var Bi=new r,s={STOP:0,PASS:1,PUSH:2};Object.freeze(s);var Gi=s;function Zi(t,i){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,i){if(t){if("string"==typeof t)return Wi(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Wi(t,i):void 0}}(t))||i&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var e,s=!0,o=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){o=!0,e=t},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw e}}}}function Wi(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}function Vi(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}E=function(){function i(t){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,i),this.Ht=V.GROUP,this.Wi=new Map,this.Vi=-1,this.Yi=null,this.Zt=null,Object.assign(this,t)}var t,n,r;return t=i,(n=[{key:"getType",value:function(){return this.Ht}},{key:"getCoordinates",value:function(){return this.Yi}},{key:"getBound",value:function(){return this.Zt||this.Xi(),this.Zt}},{key:"getChildren",value:function(){return this.Wi}}])&&Vi(t.prototype,n),r&&Vi(t,r),i}();Object.assign(E.prototype,{ei:function(){var t,i=Zi(this.Wi.values());try{for(i.s();!(t=i.n()).done;){var n=t.value;n.ei&&n.ei()}}catch(t){i.e(t)}finally{i.f()}this.Wi.clear()},zi:function(i){var t=i.qi(this);t==Gi.PASS?this.Wi.forEach(function(t){t.zi(i)}):t==Gi.PUSH&&i.Hi.push(this)},Xi:function(){var i=this;this.Zt=new mt,0!=this.Wi.size&&this.Wi.forEach(function(t){i.Zt.Pt(t.getBound())})}});var Yi=E;function Xi(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}A=function(){function i(t){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,i),this.Vi=t.eid,this.Ht=V.POINT,this.Yi=null,this.Zt=null,Object.assign(this,t)}var t,n,r;return t=i,(n=[{key:"getType",value:function(){return this.Ht}},{key:"getCoordinates",value:function(){return this.Yi}},{key:"getBound",value:function(){return this.Zt||this.Xi(),this.Zt}}])&&Xi(t.prototype,n),r&&Xi(t,r),i}();Object.assign(A.prototype,{Xi:function(){this.Zt=new mt,this.Yi&&this.Zt.Ft(this.Yi)},zi:function(t){t.qi(this)==Gi.PUSH&&t.Hi.push(this)}});var zi=A;function qi(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}F=function(){function i(t){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,i),this.Vi=t.eid,this.Ht=V.POLYGON,this.Ut=null,this.Yi=null,this.Zt=null,Object.assign(this,t)}var t,n,r;return t=i,(n=[{key:"getType",value:function(){return this.Ht}},{key:"getCenter",value:function(){return this.Ut}},{key:"getCoordinates",value:function(){return this.Yi}},{key:"getBound",value:function(){return this.Zt||this.Xi(),this.Zt}}])&&qi(t.prototype,n),r&&qi(t,r),i}();Object.assign(F.prototype,{Xi:function(){var i=this;this.Zt=new mt,this.Yi&&this.Yi.forEach(function(t){i.Zt.Ft(t)})},zi:function(t){t.qi(this)==Gi.PUSH&&t.Hi.push(this)}});var Hi=F;function Qi(t,i){if(null==t)return{};var n,r=function(t,i){if(null==t)return{};var n,r,e={},s=Object.keys(t);for(r=0;r<s.length;r++)n=s[r],0<=i.indexOf(n)||(e[n]=t[n]);return e}(t,i);if(Object.getOwnPropertySymbols)for(var e=Object.getOwnPropertySymbols(t),s=0;s<e.length;s++)n=e[s],0<=i.indexOf(n)||Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n]);return r}r=function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.Qi=[1,2,3,4],this.Ki=[6,7,9],this.Ji=[31,32],this.$i=new Map,this.$i.set(31,V.EXTENTGROUP),this.$i.set(32,V.MODELGROUP),this.$i.set(6,V.FACILITYGROUP),this.$i.set(7,V.LABELGROUP),this.$i.set(9,V.EXTERNALMODELGROUP),this.tn=new Map,this.tn.set(31,["extentLayer",null]),this.tn.set(32,["modelLayer","labelLayer"]),this.tn.set(6,["poiLayer",null]),this.tn.set(7,["labelLayer",null]),this.tn.set(9,["externalModelLayer",null]),this._i=new ai};Object.assign(r.prototype,{in:function(t){if(!t)return null;var i=t.scene;return this.nn(i,t)},nn:function(t,n){var r=this,i=t.layerGroups,e=Qi(t,["layerGroups"]),s=t.fileVer,o=new Yi(e);return i.forEach(function(t){var i=r.rn(t,n,s);o.Wi.set(t.gid,i)}),o},rn:function(t,i,n){var r=this,e=t.layers,s=Qi(t,["layers"]),o=new Yi(s),u=i&&i.floors?i.floors.get(t.gid).geo[0]:null,h=i&&i.floors?i.floors.get(t.gid).biz[0]:null;return e.forEach(function(t){6===t.ltype&&-1!==t.lname.indexOf("storelabel")&&(t.ltype=7);var i=r.sn(t,u,h,n);null!==i&&o.Wi.set(r.$i.get(t.ltype),i)}),this.un(o),o},un:function(t){var i=Array.from(t.Wi);i.sort(function(t,i){return t[0]-i[0]}),t.Wi=new Map(i)},sn:function(t,i,n,r){if(-1!==this.Qi.indexOf(t.ltype))return null;if(!this.tn.get(t.ltype))return null;var e=new Yi(t);return this.hn(e,i,n,r),e&&(e.Ht=this.$i.get(t.ltype)),e},hn:function(t,i,n,r){if(t){var e=this.tn.get(t.ltype);if(e){var s=i?i[e[0]]:[],o=n?n[e[0]]:[];switch(t.ltype){case 6:this.an(t,s,o,r,V.FACILITY);break;case 7:this.an(t,s,o,r,V.LABEL);break;case 9:this.an(t,s,o,r,V.EXTERNALMODEL);break;case 31:this.cn(t,s,o,r,V.EXTENT,null);break;case 32:this.cn(t,s,o,r,V.MODEL,i?i[e[1]]:null)}}}},an:function(t,i,n,r,e){for(var s=0;s<i.length;s++){var o=this.ln(i[s],n[s],r);o.Ht=e,t.Wi.set(i[s].eid,o)}},ln:function(t,i,n){i=Object.assign({},i,{area:t.area,eid:t.eid,height:t.height}),i=new zi(i);return t.coordinates?i.Yi=t.coordinates:(i.Yi=1==n?Bi.Ci(t.geo):Ci.Ci(t.idxs,t.pts),t.coordinates=i.Yi),i},cn:function(t,i,n,r,e,s){for(var o=0;o<i.length;o++){var u=this.vn(i[o],n[o],r,s?s[o]:null);u.Ht=e,t.Wi.set(i[o].eid,u)}},vn:function(t,i,n,r){i=Object.assign({},i,{area:t.area,eid:t.eid,height:t.height}),i=new Hi(i);return i.Yi=1===n?Bi.Bi(t.geo):Ci.Bi(t.idxs,t.pts),r&&(r.coordinates||(r.coordinates=1===n?Bi.Ci(r.geo):Ci.Ci(r.idxs,r.pts)),i.Ut=r.coordinates[0]),i}});var Ki=r,Ji={NONE_RODE_NETWORK:1,WALK_RODE_NETWORK:2,DRIVE_RODE_NETWORK:4,ACCESSIBLE_RODE_NETWORK:8};s=function t(){var i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.dn=i.disposeCallback,this.yn=!1,this.bn=!1,this.pn=void 0!==i.enable&&i.enable,this.name=void 0!==i.name?i.name:"",this.xn=new Map,this.mn=i.mapCreateTime};Object.assign(s.prototype,{wn:function(){this.pn&&(this.bn||this.yn&&0===this.xn.size&&(this.dn({mapCreateTime:this.mn}),this.bn=!0))},_n:function(t){this.pn&&(this.yn=t,this.wn())},gn:function(t){this.pn&&(this.xn.has(t)&&console.warn("添加的引用已经存在..."),this.xn.set(t,0))},On:function(t){this.pn&&(this.xn.has(t)?(this.xn.delete(t),this.wn()):console.warn("移除的引用不存在"))}});for(var $i=s,tn=[],nn=0;nn<256;nn++)tn[nn]=(nn<16?"0":"")+nn.toString(16);var rn={DEG2RAD:Math.PI/180,RAD2DEG:180/Math.PI,generateUUID:function(){var t=4294967295*Math.random()|0,i=4294967295*Math.random()|0,n=4294967295*Math.random()|0,r=4294967295*Math.random()|0;return(tn[255&t]+tn[t>>8&255]+tn[t>>16&255]+tn[t>>24&255]+"-"+tn[255&i]+tn[i>>8&255]+"-"+tn[i>>16&15|64]+tn[i>>24&255]+"-"+tn[63&n|128]+tn[n>>8&255]+"-"+tn[n>>16&255]+tn[n>>24&255]+tn[255&r]+tn[r>>8&255]+tn[r>>16&255]+tn[r>>24&255]).toUpperCase()},clamp:function(t,i,n){return Math.max(i,Math.min(n,t))},euclideanModulo:function(t,i){return(t%i+i)%i},mapLinear:function(t,i,n,r,e){return r+(t-i)*(e-r)/(n-i)},lerp:function(t,i,n){return(1-n)*t+n*i},smoothstep:function(t,i,n){return t<=i?0:n<=t?1:(t=(t-i)/(n-i))*t*(3-2*t)},smootherstep:function(t,i,n){return t<=i?0:n<=t?1:(t=(t-i)/(n-i))*t*t*(t*(6*t-15)+10)},randInt:function(t,i){return t+Math.floor(Math.random()*(i-t+1))},randFloat:function(t,i){return t+Math.random()*(i-t)},randFloatSpread:function(t){return t*(.5-Math.random())},degToRad:function(t){return t*rn.DEG2RAD},radToDeg:function(t){return t*rn.RAD2DEG},isPowerOfTwo:function(t){return 0==(t&t-1)&&0!==t},ceilPowerOfTwo:function(t){return Math.pow(2,Math.ceil(Math.log(t)/Math.LN2))},floorPowerOfTwo:function(t){return Math.pow(2,Math.floor(Math.log(t)/Math.LN2))},setQuaternionFromProperEuler:function(t,i,n,r,e){var s=Math.cos,o=Math.sin,u=s(n/2),h=o(n/2),a=s((i+r)/2),f=o((i+r)/2),c=s((i-r)/2),n=o((i-r)/2),s=s((r-i)/2),i=o((r-i)/2);"XYX"===e?t.set(u*f,h*c,h*n,u*a):"YZY"===e?t.set(h*n,u*f,h*c,u*a):"ZXZ"===e?t.set(h*c,h*n,u*f,u*a):"XZX"===e?t.set(u*f,h*i,h*s,u*a):"YXY"===e?t.set(h*s,u*f,h*i,u*a):"ZYZ"===e?t.set(h*i,h*s,u*f,u*a):console.warn("THREE.MathUtils: .setQuaternionFromProperEuler() encountered an unknown order.")}};function en(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}E=function(){function i(t){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,i),this.En=new Map,this.Mn=new Map,this.ii=new Map,this.jn=new Map,this.kn=new Map,this.Rn=new Map,this.Tn=[2,3],this.An=!1,this._i=new ai,this.Sn=new Ki,this.Nn=new Fi,this.In=null,this.Ln=this.Ln.bind(this),this.Dn=new $i({disposeCallback:this.Ln,enable:!0,name:"DataManager",mapCreateTime:(null==t?void 0:t.mapCreateTime)||(new Date).getTime()})}var t,n,r;return t=i,(n=[{key:"load",value:function(t,i){this.Pn(t,i)}},{key:"loadWhole",value:function(t,i){this.Fn(t,i)}},{key:"getDecode",value:function(t){t=void 0!==t.buildingID?t.buildingID:t.mapID;return this.Cn({buildingID:t})}},{key:"getData",value:function(t){t=void 0!==t.buildingID?t.buildingID:t.mapID;return this.En.get(t)}},{key:"setCache",value:function(t){this.En=t[0],this.Mn=t[1]}},{key:"getCache",value:function(){return[this.En,this.Mn]}}])&&en(t.prototype,n),r&&en(t,r),i}();Object.assign(E.prototype,{ei:function(t){this.In=t,this.Nn.ei(),this.Dn._n(!0)},Ln:function(){this.En.clear(),this.En=null,this.Mn.clear(),this.Mn=null,this.ii.clear(),this.ii=null,this.jn.clear(),this.jn=null,this.kn.clear(),this.kn=null,this.Rn.clear(),this.Rn=null,this.In&&this.In({name:"DataManager"})},Un:function(t){this.jn.set(t.buildingID+t.uuid,null),this.kn.set(t.buildingID+t.uuid,null),this.Rn.set(t.buildingID+t.uuid,0)},Pn:function(u,h){var a=this;this.Un(u),this.Si(u,function(i,t){h&&h(i,t);var o=a.Bn(u),t=u.loadNavi&&o.buildings&&0<o.buildings.length;a.Ni(u,t,function(t,i){h&&h(t,i);var n=void 0!==u.level?u.level:parseInt(o.defGid);a.Gn(u,n,h);for(var r=u.visibleLevels||o.levels,e=0;e<r.length;e++)r[e]!==n&&(o.levels.indexOf(r[e])<0||a.Gn(u,r[e],h));if(u.preLoad)for(var s=0;s<o.levels.length;s++)o.levels[s]!==n&&-1===r.indexOf(o.levels[s])&&a.Gn(u,o.levels[s],h)},function(t){h&&h(i,t)})},function(t,i){h&&h(t,i)})},Ni:function(n,t,r,e){var s,o=this;t?(t=this.Zn(n))?r&&r("gate",t):(s="_sendGate"+rn.generateUUID(),this.Dn.gn(s),this.Nn.Ni(n,function(t){o.Mn.get(n.buildingID).gate=t,r&&r("gate",t),o.Dn.On(s)},function(t){var i;n.navigationData?((i=o.Mn.get(n.buildingID)).gate=n.navigationData,r&&r("gate",i.gate)):e("error",t),o.Dn.On(s)})):r&&r("gate",null)},Si:function(n,r,i){var e,s=this,t=this.Bn(n);t?(r&&r("scene",t),n.justDecode||(t=this.En.get(n.buildingID),r&&r("building",t))):(e="_sendScene"+rn.generateUUID(),this.Dn.gn(e),this.Nn.Si(n,function(t){var i;n.isOutdoor&&(i=t.fileVer,t.buildings&&1<i&&t.buildings.forEach(function(i){i.floors&&(i.levelChart=[],i.fids=[],i.floors.forEach(function(t){t.Yi=Ci.Bi(t.idxs,t.pts),t.pgid&&i.levelChart.push(t.pgid,t.gid),t.baseLevel&&(i.minlevel=t.minlevel,i.maxlevel=t.maxlevel),t.fids&&i.fids.push.apply(i.fids,t.fids)}))})),s.Mn.set(n.buildingID,{scene:t}),r&&r("scene",t),n.justDecode||(t=s.Sn.nn(t),s.En.set(n.buildingID,t),r&&r("building",t)),s.Dn.On(e)},function(t){i&&i("error",t===zt.PATH_ERROR?zt.MAP_ID_URL_ERROR:t),s.Dn.On(e)}))},Gn:function(t,i,n,r){var e=this.jn.get(t.buildingID+t.uuid);e||this.jn.set(t.buildingID+t.uuid,e=[]),r?e.unshift(i):e.push(i),this.An&&1!==e.length||this.Wn(t,n)},Wn:function(r,e){var s=this,o=this.Bn(r);if(o){var u=this.jn.get(r.buildingID+r.uuid);if(u&&0!==u.length){var t=this.kn.get(r.buildingID+r.uuid);t||this.kn.set(r.buildingID+r.uuid,t=[]);var h=u[0];if(-1!==t.indexOf(h))return u.splice(u.indexOf(h),1),void this.Wn(r,e);t.push(h),this.Vn(r,h,function(t,i){var n=s.Rn.get(r.buildingID+r.uuid);"layerGroup"===t&&(e&&e(t,i),s.Rn.set(r.buildingID+r.uuid,++n),n===o.layerGroups.length&&e&&e("decode",s.Cn(r)),u.splice(u.indexOf(h),1),s.Wn(r,e)),"layer"===t&&e&&e(t,i),"floor"===t&&(e&&e(t,i),n===o.layerGroups.length&&e&&e("complete",s.Yn(r)))},function(t,i){e&&e(t,i)})}}},Vn:function(e,s,o,i){var t,u,h,a,f,n,r=this,c=this.Bn(e),l=this.Xn(c,s);l?(t=l.gname,l=l.naviType||0,u=this.Tn[0],h=0,a=new Map,f=e.merge,n=function(i){var n,r,t;u===++h&&(n=a.get("geo"),r=a.get("biz"),f&&f.merge(s,n,r,c.fileVer),o&&o("layerGroup",a),e.justDecode||((t=i.zn(e,s)).Wi.forEach(function(t){i.Sn.hn(t,n,r,c.fileVer),o&&o("layer",t)}),o&&o("floor",t)))},e.loadNavi&&((0!=(l&Ji.WALK_RODE_NETWORK)||c.fileVer<3)&&(u++,this.Di(e,s,t,function(t,i){a.set(t,i),o&&o(t,i),n(r)},function(t){i&&i(t)})),0!=(l&Ji.DRIVE_RODE_NETWORK)&&(u++,this.Pi(e,s,t,function(t,i){a.set(t,i),o&&o(t,i),n(r)},function(t){i&&i(t)})),0!=(l&Ji.ACCESSIBLE_RODE_NETWORK)&&(u++,this.Fi(e,s,t,function(t,i){a.set(t,i),o&&o(t,i),n(r)},function(t){i&&i(t)}))),this.Ii(e,s,t,function(t,i){a.set(t,i),o&&o(t,i),n(r)},function(t){i&&i(t)}),this.Li(e,s,t,function(t,i){a.set(t,i),o&&o(t,i),n(r)},function(t){i&&i(t)})):i&&i("error",zt.DEFAULT_LEVEL_ERROR)},Ii:function(i,n,t,r,e){var s,o=this,u=this.qn(i,n,"geo");u?r&&r("geo",u):(s="_sendFloorGeo"+rn.generateUUID(),this.Dn.gn(s),this.Nn.Ii(i,t,function(t){o.Hn(i,n,"geo",t),r&&r("geo",t),o.Dn.On(s)},function(t){e&&e("error",t),o.Dn.On(s)}))},Li:function(i,n,t,r,e){var s,o=this,u=this.qn(i,n,"biz");u?r&&r("biz",u):(s="_sendFloorBiz"+rn.generateUUID(),this.Dn.gn(s),this.Nn.Li(i,t,function(t){o.Hn(i,n,"biz",t),r&&r("biz",t),o.Dn.On(s)},function(t){e&&e("error",t),o.Dn.On(s)}))},Di:function(i,n,t,r,e){var s,o=this,u=this.qn(i,n,"navi");u?r&&r("navi",u):(s="_sendFloorNavi"+rn.generateUUID(),this.Dn.gn(s),this.Nn.Di(i,t,function(t){o.Hn(i,n,"navi",t),r&&r("navi",t),o.Dn.On(s)},function(t){e&&e("error",t),o.Dn.On(s)}))},Pi:function(i,n,t,r,e){var s,o=this,u=this.qn(i,n,"drive_navi");u?r&&r("drive_navi",u):(s="_sendFloorNaviDrive"+rn.generateUUID(),this.Dn.gn(s),this.Nn.Pi(i,t,function(t){o.Hn(i,n,"drive_navi",t),r&&r("drive_navi",t),o.Dn.On(s)},function(t){e&&e("error",t),o.Dn.On(s)}))},Fi:function(i,n,t,r,e){var s,o=this,u=this.qn(i,n,"accessible_navi");u?r&&r("drive_navi",u):(s="_sendFloorNaviAccessible"+rn.generateUUID(),this.Dn.gn(s),this.Nn.Fi(i,t,function(t){o.Hn(i,n,"accessible_navi",t),r&&r("accessible_navi",t),o.Dn.On(s)},function(t){e&&e("error",t),o.Dn.On(s)}))},Fn:function(t,n){this.Ai(t,function(t,i){n&&n(t,i)},function(t,i){n&&n(t,i)})},Ai:function(i,n,r){var e,s=this,o=this.Cn(i);o?n&&n("decode",o):(e="_sendWhole"+rn.generateUUID(),this.Dn.gn(e),this.Nn.Ai(i,function(t){s.Qn(i,t),o=s.Cn(i),n&&n("decode",o),i.justDecode||(t=s.Sn.in(o),s.En.set(i.buildingID,t),n&&n("complete",t)),s.Dn.On(e)},function(t){r&&r("error",t),s.Dn.On(e)}))},Yn:function(t){if(this.En){t=this.En.get(t.buildingID);return t||null}},zn:function(t,i){t=this.En.get(t.buildingID);return t?t.Wi.get(i):null},Qn:function(t,i){var n,r={scene:null,floors:new Map},e=this._i.ai(i);t.isOutdoor&&(n=e.fileVer,e.buildings&&1<n&&e.buildings.forEach(function(i){i.floors&&(i.levelChart=[],i.fids=[],i.floors.forEach(function(t){t.Yi=Ci.Bi(t.idxs,t.pts),t.pgid&&i.levelChart.push(t.pgid,t.gid),t.baseLevel&&(i.minlevel=t.minlevel,i.maxlevel=t.maxlevel),t.fids&&i.fids.push.apply(i.fids,t.fids)}))})),r.scene=e,r.gate=this._i.ci(i);for(var s=0;s<e.layerGroups.length;s++){var o=e.layerGroups[s],u=this._i.vi(i,o.gid),h=this._i.yi(i,o.gid),a=this._i.pi(i,o.gid),f=this._i.mi(i,o.gid),c=this._i.wi(i,o.gid);t.merge&&t.merge.merge(o.gid,u,h,e.fileVer);var l=new Map;l.set("geo",u),l.set("biz",h),l.set("navi",a),l.set("drive_navi",f),l.set("accessible_navi",c),r.floors.set(o.gid,l)}this.Mn.set(t.buildingID,r)},Cn:function(t){var i=this.Mn.get(t.buildingID);if(!i)return null;for(var n=i.scene,i=i.gate,r=new Map,i={scene:n,floors:r,gate:i},e=0;e<n.layerGroups.length;e++){var s=n.layerGroups[e],o=s.gid,u={gid:o,gname:s.gname,level:o,geo:[],biz:[],navi:[],naviDrive:[],naviAccessible:[]};u.geo.push(this.qn(t,o,"geo")),u.biz.push(this.qn(t,o,"biz"));var h=this.qn(t,o,"navi");h&&u.navi.push(h);h=this.qn(t,o,"drive_navi");h&&u.naviDrive.push(h);o=this.qn(t,o,"accessible_navi");o&&u.naviAccessible.push(o),r.set(s.gid,u)}return i},Bn:function(t){t=this.Mn.get(t.buildingID);return t?t.scene:null},Zn:function(t){t=this.Mn.get(t.buildingID);return t?t.gate:null},Hn:function(t,i,n,r){var e=this.Mn.get(t.buildingID);e&&((t=e.floors)||(t=new Map,e.floors=t),(e=t.get(i))||(e=new Map,t.set(i,e)),e.set(n,r))},qn:function(t,i,n){t=this.Mn.get(t.buildingID);if(!t)return null;t=t.floors;if(!t)return null;i=t.get(i);return i?i.get(n):null},Xn:function(t,i){if(null==t)return null;for(var n=t.layerGroups,r=0;r<n.length;r++)if(n[r].gid==i)return n[r];return null}});var sn=E,on={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074},un={h:0,s:0,l:0},hn={h:0,s:0,l:0};function an(t,i,n){return void 0===i&&void 0===n?this.set(t):this.setRGB(t,i,n)}function fn(t,i,n){return n<0&&(n+=1),1<n&&--n,n<1/6?t+6*(i-t)*n:n<.5?i:n<2/3?t+6*(i-t)*(2/3-n):t}function cn(t){return t<.04045?.0773993808*t:Math.pow(.9478672986*t+.0521327014,2.4)}function ln(t){return t<.0031308?12.92*t:1.055*Math.pow(t,.41666)-.055}function vn(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}Object.assign(an.prototype,{isColor:!0,r:1,g:1,b:1,set:function(t){return t&&t.isColor?this.copy(t):"number"==typeof t?this.setHex(t):"string"==typeof t&&this.setStyle(t),this},setScalar:function(t){return this.r=t,this.g=t,this.b=t,this},setHex:function(t){return t=Math.floor(t),this.r=(t>>16&255)/255,this.g=(t>>8&255)/255,this.b=(255&t)/255,this},setRGB:function(t,i,n){return this.r=t,this.g=i,this.b=n,this},setHSL:function(t,i,n){return t=rn.euclideanModulo(t,1),i=rn.clamp(i,0,1),n=rn.clamp(n,0,1),0===i?this.r=this.g=this.b=n:(this.r=fn(i=2*n-(n=n<=.5?n*(1+i):n+i-n*i),n,t+1/3),this.g=fn(i,n,t),this.b=fn(i,n,t-1/3)),this},setStyle:function(i){function t(t){void 0!==t&&parseFloat(t)<1&&console.warn("THREE.Color: Alpha component of "+i+" will be ignored.")}if(h=/^((?:rgb|hsl)a?)\(\s*([^\)]*)\)/.exec(i)){var n,r=h[1],e=h[2];switch(r){case"rgb":case"rgba":if(n=/^(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(,\s*([0-9]*\.?[0-9]+)\s*)?$/.exec(e))return this.r=Math.min(255,parseInt(n[1],10))/255,this.g=Math.min(255,parseInt(n[2],10))/255,this.b=Math.min(255,parseInt(n[3],10))/255,t(n[5]),this;if(n=/^(\d+)\%\s*,\s*(\d+)\%\s*,\s*(\d+)\%\s*(,\s*([0-9]*\.?[0-9]+)\s*)?$/.exec(e))return this.r=Math.min(100,parseInt(n[1],10))/100,this.g=Math.min(100,parseInt(n[2],10))/100,this.b=Math.min(100,parseInt(n[3],10))/100,t(n[5]),this;break;case"hsl":case"hsla":if(n=/^([0-9]*\.?[0-9]+)\s*,\s*(\d+)\%\s*,\s*(\d+)\%\s*(,\s*([0-9]*\.?[0-9]+)\s*)?$/.exec(e)){var s=parseFloat(n[1])/360,o=parseInt(n[2],10)/100,u=parseInt(n[3],10)/100;return t(n[5]),this.setHSL(s,o,u)}}}else if(h=/^\#([A-Fa-f0-9]+)$/.exec(i)){var r=h[1],h=r.length;if(3===h)return this.r=parseInt(r.charAt(0)+r.charAt(0),16)/255,this.g=parseInt(r.charAt(1)+r.charAt(1),16)/255,this.b=parseInt(r.charAt(2)+r.charAt(2),16)/255,this;if(6===h)return this.r=parseInt(r.charAt(0)+r.charAt(1),16)/255,this.g=parseInt(r.charAt(2)+r.charAt(3),16)/255,this.b=parseInt(r.charAt(4)+r.charAt(5),16)/255,this}return i&&0<i.length?this.setColorName(i):this},setColorName:function(t){var i=on[t];return void 0!==i?this.setHex(i):console.warn("THREE.Color: Unknown color "+t),this},clone:function(){return new this.constructor(this.r,this.g,this.b)},copy:function(t){return this.r=t.r,this.g=t.g,this.b=t.b,this},copyGammaToLinear:function(t,i){return void 0===i&&(i=2),this.r=Math.pow(t.r,i),this.g=Math.pow(t.g,i),this.b=Math.pow(t.b,i),this},copyLinearToGamma:function(t,i){i=0<(i=void 0===i?2:i)?1/i:1;return this.r=Math.pow(t.r,i),this.g=Math.pow(t.g,i),this.b=Math.pow(t.b,i),this},convertGammaToLinear:function(t){return this.copyGammaToLinear(this,t),this},convertLinearToGamma:function(t){return this.copyLinearToGamma(this,t),this},copySRGBToLinear:function(t){return this.r=cn(t.r),this.g=cn(t.g),this.b=cn(t.b),this},copyLinearToSRGB:function(t){return this.r=ln(t.r),this.g=ln(t.g),this.b=ln(t.b),this},convertSRGBToLinear:function(){return this.copySRGBToLinear(this),this},convertLinearToSRGB:function(){return this.copyLinearToSRGB(this),this},getHex:function(){return 255*this.r<<16^255*this.g<<8^255*this.b<<0},getHexString:function(){return("000000"+this.getHex().toString(16)).slice(-6)},getHSL:function(t){void 0===t&&(console.warn("THREE.Color: .getHSL() target is now required"),t={h:0,s:0,l:0});var i,n=this.r,r=this.g,e=this.b,s=Math.max(n,r,e),o=Math.min(n,r,e),u=(o+s)/2;if(o===s)a=i=0;else{var h=s-o,a=u<=.5?h/(s+o):h/(2-s-o);switch(s){case n:i=(r-e)/h+(r<e?6:0);break;case r:i=(e-n)/h+2;break;case e:i=(n-r)/h+4}i/=6}return t.h=i,t.s=a,t.l=u,t},getStyle:function(){return"rgb("+(255*this.r|0)+","+(255*this.g|0)+","+(255*this.b|0)+")"},offsetHSL:function(t,i,n){return this.getHSL(un),un.h+=t,un.s+=i,un.l+=n,this.setHSL(un.h,un.s,un.l),this},add:function(t){return this.r+=t.r,this.g+=t.g,this.b+=t.b,this},addColors:function(t,i){return this.r=t.r+i.r,this.g=t.g+i.g,this.b=t.b+i.b,this},addScalar:function(t){return this.r+=t,this.g+=t,this.b+=t,this},sub:function(t){return this.r=Math.max(0,this.r-t.r),this.g=Math.max(0,this.g-t.g),this.b=Math.max(0,this.b-t.b),this},multiply:function(t){return this.r*=t.r,this.g*=t.g,this.b*=t.b,this},multiplyScalar:function(t){return this.r*=t,this.g*=t,this.b*=t,this},lerp:function(t,i){return this.r+=(t.r-this.r)*i,this.g+=(t.g-this.g)*i,this.b+=(t.b-this.b)*i,this},lerpHSL:function(t,i){this.getHSL(un),t.getHSL(hn);var n=rn.lerp(un.h,hn.h,i),t=rn.lerp(un.s,hn.s,i),i=rn.lerp(un.l,hn.l,i);return this.setHSL(n,t,i),this},equals:function(t){return t.r===this.r&&t.g===this.g&&t.b===this.b},fromArray:function(t,i){return this.r=t[i=void 0===i?0:i],this.g=t[i+1],this.b=t[i+2],this},toArray:function(t,i){return(t=void 0===t?[]:t)[i=void 0===i?0:i]=this.r,t[i+1]=this.g,t[i+2]=this.b,t}}),an.NAMES=on;var dn=function(){function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t)}var i,n,r;return i=t,r=[{key:"round",value:function(t){return 0<t.toFixed(2)?Math.ceil(t.toFixed(2)):Math.floor(t.toFixed(2))}},{key:"generateUUID",value:function(){for(var t=[],i=0;i<256;i++)t[i]=(i<16?"0":"")+i.toString(16);var n=4294967295*Math.random()|0,r=4294967295*Math.random()|0,e=4294967295*Math.random()|0,s=4294967295*Math.random()|0,s=t[255&n]+t[n>>8&255]+t[n>>16&255]+t[n>>24&255]+"-"+t[255&r]+t[r>>8&255]+"-"+t[r>>16&15|64]+t[r>>24&255]+"-"+t[63&e|128]+t[e>>8&255]+"-"+t[e>>16&255]+t[e>>24&255]+t[255&s]+t[s>>8&255]+t[s>>16&255]+t[s>>24&255];return t.length=0,s.toUpperCase()}},{key:"findNearNthPowerOfTwo",value:function(t){t-=1;return t|=t>>1,t|=t>>2,t|=t>>4,t|=t>>8,(t|=t>>16)<0?1:1+t}},{key:"toRgba",value:function(t){if(void 0===t)return t;if("number"==typeof t)return"rgba("+parseInt(255*new an(t).r)+","+parseInt(255*new an(t).g)+","+parseInt(255*new an(t).b)+",1)";var i=t.split(",");return 3<=i.length?"rgba("+parseInt(i[0])+","+parseInt(i[1])+","+parseInt(i[2])+",1)":t}},{key:"toColor",value:function(t){if("number"==typeof t)return t;var i=t.split(",");return 3<=i.length?parseInt(i[0])<<16|parseInt(i[1])<<8|parseInt(i[2]):(console.error("color format error",t),0)}}],(n=null)&&vn(i.prototype,n),r&&vn(i,r),t}();function yn(t,i){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,i){if(t){if("string"==typeof t)return bn(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?bn(t,i):void 0}}(t))||i&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var e,s=!0,o=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){o=!0,e=t},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw e}}}}function bn(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}function pn(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}A=function(){function r(t,i,n){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,r),this.ii=Object.assign({tile:!0,isPreview:!1},t),this.ii.buildingID=t.mapID,this.Kn=!!t.map,this.Jn=t.map?t.map.getDataManager():new sn,this.ti=[],this.$n={},this.tr=t.statistics||null,this.ir=i,this.nr=n,this.rr=dn.generateUUID(),this.er()}var t,i,n;return t=r,(i=[{key:"dispose",value:function(){this.ii=null,this.Kn||this.Jn.ei(),this.Jn=null,this.ir=null,this.nr=null}},{key:"query",value:function(t,i){if(this.sr(),null===t.buildings){for(var n=[],r=0;r<this.ti.length;r++)n.push({buildingID:this.ti[r].bid});t.buildings=n}var e=[];if(!t.onlyInBuilding){t.buildingID=this.ii.mapID,null===t.buildingID&&(t.buildingID=this.ii.mapID);for(var s=this.or(t),o=0;o<s.length;o++)s[o].buildingID=t.buildingID,e.push(s[o])}for(var u=t.buildings,h=0;h<u.length;h++){var a=this.ur(u[h].buildingID);t.buildingID=a,t.levels=u[h].levels;for(var f=this.or(t),c=0;c<f.length;c++)f[c].buildingID=u[h].buildingID,e.push(f[c])}i&&i(e)}}])&&pn(t.prototype,i),n&&pn(t,n),r}();Object.assign(A.prototype,{er:function(){this.ti=[],this.Kn&&(t=this.ii.map.getMapOptions(),this.ii.mapID=t.mapID,this.ii.buildingID=t.mapID,this.ii.appName=t.appName,this.ii.key=t.key,this.ii.license=t.license,this.ii.mapURL=t.mapURL,this.ii.mapURLAbsolute=t.mapURLAbsolute,this.ii.buildings=t.buildings,this.ii.buildingOptions=t.buildingOptions,this.ii.isPreview="undefined"!=typeof KjWzhWlSj&&!!KjWzhWlSj.ProtoDef,this.ii.tile=t.tile);var t={mapID:this.ii.mapID,buildingID:this.ii.mapID,appName:this.ii.appName,key:this.ii.key,buildings:this.ii.buildings,license:this.ii.license,mapURL:this.ii.mapURL,mapURLAbsolute:this.ii.mapURLAbsolute,merge:this.ii.merge,tile:this.ii.tile,preLoad:!0,isPreview:"undefined"!=typeof KjWzhWlSj&&!!KjWzhWlSj.ProtoDef,uuid:this.rr,bid:this.ii.buildingID||null,isOutdoor:!0};this.hr(t)},hr:function(n){var r=this;setTimeout(function(){r.ii.tile?r.Jn.load(n,function(t,i){r.ar(t,i,n.isOutdoor),r.tr&&r.tr.fr(t,i,n)}):r.Jn.loadWhole(n,function(t,i){r.ar(t,i,n.isOutdoor),r.tr&&r.tr.fr(t,i,n)})},0)},sr:function(){if(!(0<this.ti.length)){var t=this.ii.mapID,i=this.Jn.getData({buildingID:t});if(i&&i.buildings)for(var n=0;n<i.buildings.length;n++)this.ti.push(i.buildings[n])}},ar:function(t,i,n){if("error"==t&&this.nr&&this.nr(i),"decode"===t){if(n&&(this.$n[""+i.scene.mid]=!1,i.scene.buildings&&0<i.scene.buildings.length))for(var r=0;r<i.scene.buildings.length;r++){var e=i.scene.buildings[r],s={mapID:this.ii.mapID,buildingID:null,appName:this.ii.appName,key:this.ii.key,license:this.ii.license,mapURL:this.ii.mapURL,mapURLAbsolute:this.ii.mapURLAbsolute,merge:this.ii.merge,loadNavi:!0,preLoad:!0,isPreview:"undefined"!=typeof KjWzhWlSj,tile:this.ii.tile,type:this.ii.type,bid:null,isOutdoor:!1,uuid:this.rr};s.buildingID=e.mid,s.bid=e.bid;s=Object.assign({},s);this.$n[""+e.mid]=!1,this.hr(s)}if(this.Kn&&!this.ii.tile){var o,u=this.$n[""+i.scene.mid]=!0;for(o in this.$n)this.$n[o]||(u=!1);u&&this.ir&&this.ir()}}if("complete"==t){var h,a=this.$n[""+i.mid]=!0;for(h in this.$n)this.$n[h]||(a=!1);a&&this.ir&&this.ir()}},cr:function(t){var i=V.NONE;return 0!=(t.Ht&V.MODEL)&&(i|=V.MODELGROUP),0!=(t.Ht&V.LABEL)&&(i|=V.LABELGROUP),0!=(t.Ht&V.FACILITY)&&(i|=V.FACILITYGROUP),0!=(t.Ht&V.EXTERNALMODEL)&&(i|=V.EXTERNALMODELGROUP),i},lr:function(t){if(Array.isArray(t.qt))return t.qt;var i=this.ii;null!==t.buildingID&&(i={buildingID:t.buildingID,mapID:t.buildingID});i=this.Jn.getData(i);if(!i)return[];var n,r=[],e=yn(i.getChildren());try{for(e.s();!(n=e.n()).done;){var s=n.value;r.push(s[1].gid)}}catch(t){e.e(t)}finally{e.f()}return r},vr:function(t,i,n,r){if(0==i)return!0;for(var e=0;e<i;e++){if(!t[e].X(n))return!1;t[e].H(n,r)}return!0},dr:function(t,i,n){return{level:t,type:i.getType(),FID:i.fid,name:i.name,ename:i.ename,eid:i.eid,typeID:i.type,center:void 0!==i.getCenter?Object.assign({z:i.height},i.getCenter()):Object.assign({z:i.height},i.getCoordinates()[0]),distance:n.vt}},yr:function(t){t.sort(function(t,i){return t.level==i.level&&t.type==i.type&&t.distance&&i.distance?t.distance-i.distance:0})},or:function(t,i,n){var r=this.ii;null!==t.buildingID&&(r={buildingID:t.buildingID,mapID:t.buildingID});r=this.Jn.getData(r);if(!r)return[];var e=this.lr(t),s=this.cr(t);if(s===V.NONE)return[];var o,u=t.Jt,h=u.length,a=[],f=yn(r.getChildren());try{for(f.s();!(o=f.n()).done;){var c=o.value,l=c[0];if(-1!==e.indexOf(l)){var v,d=yn(c[1].getChildren());try{for(d.s();!(v=d.n()).done;){var y=v.value[1];if(0!=(y.getType()&s)){var b,p=yn(y.getChildren());try{for(p.s();!(b=p.n()).done;){var x=b.value[1],m={vt:void 0};this.vr(u,h,x,m)&&a.push(this.dr(l,x,m))}}catch(t){p.e(t)}finally{p.f()}}}}catch(t){d.e(t)}finally{d.f()}}}}catch(t){f.e(t)}finally{f.f()}return a},ur:function(t){for(var i=null,n=this.ti,r=0;r<n.length;r++)if(n[r].bid===t){i=n[r].mid;break}return i}});var F=A,xn={NaviNodeType_NULL:-1,NaviNodeType_COMMON:0,NaviNodeType_SIDE:1,NaviNodeType_FLOOR:2,NaviNodeType_EXTENT:3,NaviLiftType_NULL:0,NaviLiftType_Lift:1,NaviLiftType_Stair:2,NaviLiftType_Escalator:3,NaviLiftType_Accessible:4,NaviLiftType_Ramp:5,NaviLiftEntry_BOTH:0,NaviLiftEntry_UP:1,NaviLiftEntry_DOWN:2,NaviLiftEntry_FORBID:3,NaviEntranceType_NULL:0,NaviEntranceType_EXIT:1,NaviEntranceType_ENTRANCE:2,NaviEntranceType_ACCESS:3,NaviRoadRank_MAIN:1,NaviRoadRank_MINOR:2,NaviRoadRank_NARROW:3,NaviRoadEntry_BOTH:0,NaviRoadEntry_FORWARD:1,NaviRoadEntry_BACK:2,NaviRoadEntry_FORBID:3,NaviRoadPass_NULL:-1,NaviRoadPass_NOT_THROUGH:0,NaviRoadPass_THROUGH:1,NaviZoneType_NULL:-1,NaviZoneType_PASS_UNRESTRAINT:0,NaviZoneType_PASS_THROUGH:1,NaviZoneType_PASS_NOT_THROUGH:2,NaviZoneType_NO_ENTRY:3,NaviZoneType_CORRIDOR:4,NaviModelPassType_PASS_THROUGH:0,NaviModelPassType_PASS_NOT_THROUGH:1,NaviModelPassType_NOT_PASS:2,NaviModelPassType_DECORATE:3,NaviRoadHinderType_HINDER_GENERAL:0,NaviRoadHinderType_HINDER_HIGH:1,NaviObstructType_MODEL:0,NaviObstructType_EXTENT:1},mn={Drive:1,Walk:0},wn={Entry_BOTH:0,Entry_FORWARD:1,Entry_BACK:2,Entry_FORBID:3};function _n(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}r=function(){function e(t,i,n,r){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,e),this.br=t,this.pr=i,this.xr=n||[],this.mr=r||[]}var t,i,n;return t=e,(i=[{key:"level",get:function(){return this.br}},{key:"length",get:function(){return this.pr}},{key:"pointList",get:function(){for(var t=[],i=0,n=this.xr.length;i<n;i++)t.push({x:this.xr[i].x,y:this.xr[i].y,level:this.br});return t}}])&&_n(t.prototype,i),n&&_n(t,n),e}();Object.assign(r.prototype,{ei:function(){this.xr=[],this.mr=[]}});for(var gn=r,On=[],En=0;En<256;En++)On[En]=(En<16?"0":"")+En.toString(16);var Mn=1234567,jn={DEG2RAD:Math.PI/180,RAD2DEG:180/Math.PI,generateUUID:function(){var t=4294967295*Math.random()|0,i=4294967295*Math.random()|0,n=4294967295*Math.random()|0,r=4294967295*Math.random()|0;return(On[255&t]+On[t>>8&255]+On[t>>16&255]+On[t>>24&255]+"-"+On[255&i]+On[i>>8&255]+"-"+On[i>>16&15|64]+On[i>>24&255]+"-"+On[63&n|128]+On[n>>8&255]+"-"+On[n>>16&255]+On[n>>24&255]+On[255&r]+On[r>>8&255]+On[r>>16&255]+On[r>>24&255]).toUpperCase()},clamp:function(t,i,n){return Math.max(i,Math.min(n,t))},euclideanModulo:function(t,i){return(t%i+i)%i},mapLinear:function(t,i,n,r,e){return r+(t-i)*(e-r)/(n-i)},lerp:function(t,i,n){return(1-n)*t+n*i},smoothstep:function(t,i,n){return t<=i?0:n<=t?1:(t=(t-i)/(n-i))*t*(3-2*t)},smootherstep:function(t,i,n){return t<=i?0:n<=t?1:(t=(t-i)/(n-i))*t*t*(t*(6*t-15)+10)},randInt:function(t,i){return t+Math.floor(Math.random()*(i-t+1))},randFloat:function(t,i){return t+Math.random()*(i-t)},randFloatSpread:function(t){return t*(.5-Math.random())},seededRandom:function(t){return((Mn=16807*(Mn=void 0!==t?t%2147483647:Mn)%2147483647)-1)/2147483646},degToRad:function(t){return t*jn.DEG2RAD},radToDeg:function(t){return t*jn.RAD2DEG},isPowerOfTwo:function(t){return 0==(t&t-1)&&0!==t},ceilPowerOfTwo:function(t){return Math.pow(2,Math.ceil(Math.log(t)/Math.LN2))},floorPowerOfTwo:function(t){return Math.pow(2,Math.floor(Math.log(t)/Math.LN2))},setQuaternionFromProperEuler:function(t,i,n,r,e){var s=Math.cos,o=Math.sin,u=s(n/2),h=o(n/2),a=s((i+r)/2),f=o((i+r)/2),c=s((i-r)/2),l=o((i-r)/2),v=s((r-i)/2),d=o((r-i)/2);switch(e){case"XYX":t.set(u*f,h*c,h*l,u*a);break;case"YZY":t.set(h*l,u*f,h*c,u*a);break;case"ZXZ":t.set(h*c,h*l,u*f,u*a);break;case"XZX":t.set(u*f,h*d,h*v,u*a);break;case"YXY":t.set(h*v,u*f,h*d,u*a);break;case"ZYZ":t.set(h*d,h*v,u*f,u*a);break;default:console.warn("THREE.MathUtils: .setQuaternionFromProperEuler() encountered an unknown order: "+e)}}};function kn(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}s=function(){function e(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:0,r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:1;!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,e),Object.defineProperty(this,"isQuaternion",{value:!0}),this.wr=t,this._r=i,this.gr=n,this.Or=r}var t,i,n;return t=e,n=[{key:"slerp",value:function(t,i,n,r){return n.copy(t).slerp(i,r)}},{key:"slerpFlat",value:function(t,i,n,r,e,s,o){var u=n[r+0],h=n[r+1],a=n[r+2],f=n[r+3],c=e[s+0],l=e[s+1],v=e[s+2],d=e[s+3];f===d&&u===c&&h===l&&a===v||(n=1-o,e=0<=(r=u*c+h*l+a*v+f*d)?1:-1,(s=1-r*r)>Number.EPSILON&&(s=Math.sqrt(s),r=Math.atan2(s,r*e),n=Math.sin(n*r)/s,o=Math.sin(o*r)/s),u=u*n+c*(e=o*e),h=h*n+l*e,a=a*n+v*e,f=f*n+d*e,n===1-o&&(u*=o=1/Math.sqrt(u*u+h*h+a*a+f*f),h*=o,a*=o,f*=o)),t[i]=u,t[i+1]=h,t[i+2]=a,t[i+3]=f}},{key:"multiplyQuaternionsFlat",value:function(t,i,n,r,e,s){var o=n[r],u=n[r+1],h=n[r+2],a=n[r+3],f=e[s],n=e[s+1],r=e[s+2],s=e[s+3];return t[i]=o*s+a*f+u*r-h*n,t[i+1]=u*s+a*n+h*f-o*r,t[i+2]=h*s+a*r+o*n-u*f,t[i+3]=a*s-o*f-u*n-h*r,t}}],(i=[{key:"x",get:function(){return this.wr},set:function(t){this.wr=t,this.Er()}},{key:"y",get:function(){return this._r},set:function(t){this._r=t,this.Er()}},{key:"z",get:function(){return this.gr},set:function(t){this.gr=t,this.Er()}},{key:"w",get:function(){return this.Or},set:function(t){this.Or=t,this.Er()}},{key:"set",value:function(t,i,n,r){return this.wr=t,this._r=i,this.gr=n,this.Or=r,this.Er(),this}},{key:"clone",value:function(){return new this.constructor(this.wr,this._r,this.gr,this.Or)}},{key:"copy",value:function(t){return this.wr=t.x,this._r=t.y,this.gr=t.z,this.Or=t.w,this.Er(),this}},{key:"setFromEuler",value:function(t,i){if(!t||!t.isEuler)throw new Error("THREE.Quaternion: .setFromEuler() now expects an Euler rotation rather than a Vector3 and order.");var n=t.wr,r=t._r,e=t.gr,s=t.Mr,o=Math.cos,t=Math.sin,u=o(n/2),h=o(r/2),a=o(e/2),f=t(n/2),c=t(r/2),l=t(e/2);switch(s){case"XYZ":this.wr=f*h*a+u*c*l,this._r=u*c*a-f*h*l,this.gr=u*h*l+f*c*a,this.Or=u*h*a-f*c*l;break;case"YXZ":this.wr=f*h*a+u*c*l,this._r=u*c*a-f*h*l,this.gr=u*h*l-f*c*a,this.Or=u*h*a+f*c*l;break;case"ZXY":this.wr=f*h*a-u*c*l,this._r=u*c*a+f*h*l,this.gr=u*h*l+f*c*a,this.Or=u*h*a-f*c*l;break;case"ZYX":this.wr=f*h*a-u*c*l,this._r=u*c*a+f*h*l,this.gr=u*h*l-f*c*a,this.Or=u*h*a+f*c*l;break;case"YZX":this.wr=f*h*a+u*c*l,this._r=u*c*a+f*h*l,this.gr=u*h*l-f*c*a,this.Or=u*h*a-f*c*l;break;case"XZY":this.wr=f*h*a-u*c*l,this._r=u*c*a-f*h*l,this.gr=u*h*l+f*c*a,this.Or=u*h*a+f*c*l;break;default:console.warn("THREE.Quaternion: .setFromEuler() encountered an unknown order: "+s)}return!1!==i&&this.Er(),this}},{key:"setFromAxisAngle",value:function(t,i){var n=i/2,i=Math.sin(n);return this.wr=t.x*i,this._r=t.y*i,this.gr=t.z*i,this.Or=Math.cos(n),this.Er(),this}},{key:"setFromRotationMatrix",value:function(t){var i,n=t.elements,r=n[0],e=n[4],s=n[8],o=n[1],u=n[5],h=n[9],a=n[2],f=n[6],t=n[10],n=r+u+t;return 0<n?(n=.5/Math.sqrt(n+1),this.Or=.25/n,this.wr=(f-h)*n,this._r=(s-a)*n,this.gr=(o-e)*n):u<r&&t<r?(i=2*Math.sqrt(1+r-u-t),this.Or=(f-h)/i,this.wr=.25*i,this._r=(e+o)/i,this.gr=(s+a)/i):t<u?(i=2*Math.sqrt(1+u-r-t),this.Or=(s-a)/i,this.wr=(e+o)/i,this._r=.25*i,this.gr=(h+f)/i):(u=2*Math.sqrt(1+t-r-u),this.Or=(o-e)/u,this.wr=(s+a)/u,this._r=(h+f)/u,this.gr=.25*u),this.Er(),this}},{key:"setFromUnitVectors",value:function(t,i){var n=t.dot(i)+1;return n<1e-6?(n=0,Math.abs(t.x)>Math.abs(t.z)?(this.wr=-t.y,this._r=t.x,this.gr=0):(this.wr=0,this._r=-t.z,this.gr=t.y)):(this.wr=t.y*i.z-t.z*i.y,this._r=t.z*i.x-t.x*i.z,this.gr=t.x*i.y-t.y*i.x),this.Or=n,this.normalize()}},{key:"angleTo",value:function(t){return 2*Math.acos(Math.abs(jn.clamp(this.dot(t),-1,1)))}},{key:"rotateTowards",value:function(t,i){var n=this.angleTo(t);if(0===n)return this;n=Math.min(1,i/n);return this.slerp(t,n),this}},{key:"identity",value:function(){return this.set(0,0,0,1)}},{key:"invert",value:function(){return this.conjugate()}},{key:"conjugate",value:function(){return this.wr*=-1,this._r*=-1,this.gr*=-1,this.Er(),this}},{key:"dot",value:function(t){return this.wr*t.wr+this._r*t._r+this.gr*t.gr+this.Or*t.Or}},{key:"lengthSq",value:function(){return this.wr*this.wr+this._r*this._r+this.gr*this.gr+this.Or*this.Or}},{key:"length",value:function(){return Math.sqrt(this.wr*this.wr+this._r*this._r+this.gr*this.gr+this.Or*this.Or)}},{key:"normalize",value:function(){var t=this.length();return 0===t?(this.wr=0,this._r=0,this.gr=0,this.Or=1):(this.wr=this.wr*(t=1/t),this._r=this._r*t,this.gr=this.gr*t,this.Or=this.Or*t),this.Er(),this}},{key:"multiply",value:function(t,i){return void 0!==i?(console.warn("THREE.Quaternion: .multiply() now only accepts one argument. Use .multiplyQuaternions( a, b ) instead."),this.multiplyQuaternions(t,i)):this.multiplyQuaternions(this,t)}},{key:"premultiply",value:function(t){return this.multiplyQuaternions(t,this)}},{key:"multiplyQuaternions",value:function(t,i){var n=t.wr,r=t._r,e=t.gr,s=t.Or,o=i.wr,u=i._r,t=i.gr,i=i.Or;return this.wr=n*i+s*o+r*t-e*u,this._r=r*i+s*u+e*o-n*t,this.gr=e*i+s*t+n*u-r*o,this.Or=s*i-n*o-r*u-e*t,this.Er(),this}},{key:"slerp",value:function(t,i){if(0===i)return this;if(1===i)return this.copy(t);var n=this.wr,r=this._r,e=this.gr,s=this.Or,o=s*t.Or+n*t.wr+r*t._r+e*t.gr;if(o<0?(this.Or=-t.Or,this.wr=-t.wr,this._r=-t._r,this.gr=-t.gr,o=-o):this.copy(t),1<=o)return this.Or=s,this.wr=n,this._r=r,this.gr=e,this;t=1-o*o;if(t<=Number.EPSILON){var u=1-i;return this.Or=u*s+i*this.Or,this.wr=u*n+i*this.wr,this._r=u*r+i*this._r,this.gr=u*e+i*this.gr,this.normalize(),this.Er(),this}u=Math.sqrt(t),t=Math.atan2(u,o),o=Math.sin((1-i)*t)/u,u=Math.sin(i*t)/u;return this.Or=s*o+this.Or*u,this.wr=n*o+this.wr*u,this._r=r*o+this._r*u,this.gr=e*o+this.gr*u,this.Er(),this}},{key:"equals",value:function(t){return t.wr===this.wr&&t._r===this._r&&t.gr===this.gr&&t.Or===this.Or}},{key:"fromArray",value:function(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;return this.wr=t[i],this._r=t[i+1],this.gr=t[i+2],this.Or=t[i+3],this.Er(),this}},{key:"toArray",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[],i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;return t[i]=this.wr,t[i+1]=this._r,t[i+2]=this.gr,t[i+3]=this.Or,t}},{key:"fromBufferAttribute",value:function(t,i){return this.wr=t.getX(i),this._r=t.getY(i),this.gr=t.getZ(i),this.Or=t.getW(i),this}}])&&kn(t.prototype,i),n&&kn(t,n),e}();function Rn(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}Object.assign(s.prototype,{jr:function(t){return this.Er=t,this},Er:function(){}});var Tn=function(){function r(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:0;!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,r),Object.defineProperty(this,"isVector3",{value:!0}),this.x=t,this.y=i,this.z=n}var t,i,n;return t=r,(i=[{key:"set",value:function(t,i,n){return void 0===n&&(n=this.z),this.x=t,this.y=i,this.z=n,this}},{key:"setScalar",value:function(t){return this.x=t,this.y=t,this.z=t,this}},{key:"setX",value:function(t){return this.x=t,this}},{key:"setY",value:function(t){return this.y=t,this}},{key:"setZ",value:function(t){return this.z=t,this}},{key:"setComponent",value:function(t,i){switch(t){case 0:this.x=i;break;case 1:this.y=i;break;case 2:this.z=i;break;default:throw new Error("index is out of range: "+t)}return this}},{key:"getComponent",value:function(t){switch(t){case 0:return this.x;case 1:return this.y;case 2:return this.z;default:throw new Error("index is out of range: "+t)}}},{key:"clone",value:function(){return new this.constructor(this.x,this.y,this.z)}},{key:"copy",value:function(t){return this.x=t.x,this.y=t.y,this.z=t.z,this}},{key:"add",value:function(t,i){return void 0!==i?(console.warn("THREE.Vector3: .add() now only accepts one argument. Use .addVectors( a, b ) instead."),this.addVectors(t,i)):(this.x+=t.x,this.y+=t.y,this.z+=t.z,this)}},{key:"addScalar",value:function(t){return this.x+=t,this.y+=t,this.z+=t,this}},{key:"addVectors",value:function(t,i){return this.x=t.x+i.x,this.y=t.y+i.y,this.z=t.z+i.z,this}},{key:"addScaledVector",value:function(t,i){return this.x+=t.x*i,this.y+=t.y*i,this.z+=t.z*i,this}},{key:"sub",value:function(t,i){return void 0!==i?(console.warn("THREE.Vector3: .sub() now only accepts one argument. Use .subVectors( a, b ) instead."),this.subVectors(t,i)):(this.x-=t.x,this.y-=t.y,this.z-=t.z,this)}},{key:"subScalar",value:function(t){return this.x-=t,this.y-=t,this.z-=t,this}},{key:"subVectors",value:function(t,i){return this.x=t.x-i.x,this.y=t.y-i.y,this.z=t.z-i.z,this}},{key:"multiply",value:function(t,i){return void 0!==i?(console.warn("THREE.Vector3: .multiply() now only accepts one argument. Use .multiplyVectors( a, b ) instead."),this.multiplyVectors(t,i)):(this.x*=t.x,this.y*=t.y,this.z*=t.z,this)}},{key:"multiplyScalar",value:function(t){return this.x*=t,this.y*=t,this.z*=t,this}},{key:"multiplyVectors",value:function(t,i){return this.x=t.x*i.x,this.y=t.y*i.y,this.z=t.z*i.z,this}},{key:"applyEuler",value:function(t){return t&&t.isEuler||console.error("THREE.Vector3: .applyEuler() now expects an Euler rotation rather than a Vector3 and order."),this.applyQuaternion(Sn.setFromEuler(t))}},{key:"applyAxisAngle",value:function(t,i){return this.applyQuaternion(Sn.setFromAxisAngle(t,i))}},{key:"applyMatrix3",value:function(t){var i=this.x,n=this.y,r=this.z,t=t.elements;return this.x=t[0]*i+t[3]*n+t[6]*r,this.y=t[1]*i+t[4]*n+t[7]*r,this.z=t[2]*i+t[5]*n+t[8]*r,this}},{key:"applyNormalMatrix",value:function(t){return this.applyMatrix3(t).normalize()}},{key:"applyMatrix4",value:function(t){var i=this.x,n=this.y,r=this.z,e=t.elements,t=1/(e[3]*i+e[7]*n+e[11]*r+e[15]);return this.x=(e[0]*i+e[4]*n+e[8]*r+e[12])*t,this.y=(e[1]*i+e[5]*n+e[9]*r+e[13])*t,this.z=(e[2]*i+e[6]*n+e[10]*r+e[14])*t,this}},{key:"applyQuaternion",value:function(t){var i=this.x,n=this.y,r=this.z,e=t.x,s=t.y,o=t.z,u=t.w,h=u*i+s*r-o*n,a=u*n+o*i-e*r,t=u*r+e*n-s*i,r=-e*i-s*n-o*r;return this.x=h*u+r*-e+a*-o-t*-s,this.y=a*u+r*-s+t*-e-h*-o,this.z=t*u+r*-o+h*-s-a*-e,this}},{key:"project",value:function(t){return this.applyMatrix4(t.matrixWorldInverse).applyMatrix4(t.projectionMatrix)}},{key:"unproject",value:function(t){return this.applyMatrix4(t.projectionMatrixInverse).applyMatrix4(t.matrixWorld)}},{key:"transformDirection",value:function(t){var i=this.x,n=this.y,r=this.z,t=t.elements;return this.x=t[0]*i+t[4]*n+t[8]*r,this.y=t[1]*i+t[5]*n+t[9]*r,this.z=t[2]*i+t[6]*n+t[10]*r,this.normalize()}},{key:"divide",value:function(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z,this}},{key:"divideScalar",value:function(t){return this.multiplyScalar(1/t)}},{key:"min",value:function(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this}},{key:"max",value:function(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this}},{key:"clamp",value:function(t,i){return this.x=Math.max(t.x,Math.min(i.x,this.x)),this.y=Math.max(t.y,Math.min(i.y,this.y)),this.z=Math.max(t.z,Math.min(i.z,this.z)),this}},{key:"clampScalar",value:function(t,i){return this.x=Math.max(t,Math.min(i,this.x)),this.y=Math.max(t,Math.min(i,this.y)),this.z=Math.max(t,Math.min(i,this.z)),this}},{key:"clampLength",value:function(t,i){var n=this.length();return this.divideScalar(n||1).multiplyScalar(Math.max(t,Math.min(i,n)))}},{key:"floor",value:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this}},{key:"ceil",value:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this}},{key:"round",value:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this}},{key:"roundToZero",value:function(){return this.x=this.x<0?Math.ceil(this.x):Math.floor(this.x),this.y=this.y<0?Math.ceil(this.y):Math.floor(this.y),this.z=this.z<0?Math.ceil(this.z):Math.floor(this.z),this}},{key:"negate",value:function(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this}},{key:"dot",value:function(t){return this.x*t.x+this.y*t.y+this.z*t.z}},{key:"lengthSq",value:function(){return this.x*this.x+this.y*this.y+this.z*this.z}},{key:"length",value:function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}},{key:"manhattanLength",value:function(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)}},{key:"normalize",value:function(){return this.divideScalar(this.length()||1)}},{key:"setLength",value:function(t){return this.normalize().multiplyScalar(t)}},{key:"lerp",value:function(t,i){return this.x+=(t.x-this.x)*i,this.y+=(t.y-this.y)*i,this.z+=(t.z-this.z)*i,this}},{key:"lerpVectors",value:function(t,i,n){return this.x=t.x+(i.x-t.x)*n,this.y=t.y+(i.y-t.y)*n,this.z=t.z+(i.z-t.z)*n,this}},{key:"cross",value:function(t,i){return void 0!==i?(console.warn("THREE.Vector3: .cross() now only accepts one argument. Use .crossVectors( a, b ) instead."),this.crossVectors(t,i)):this.crossVectors(this,t)}},{key:"crossVectors",value:function(t,i){var n=t.x,r=t.y,e=t.z,s=i.x,t=i.y,i=i.z;return this.x=r*i-e*t,this.y=e*s-n*i,this.z=n*t-r*s,this}},{key:"projectOnVector",value:function(t){var i=t.lengthSq();if(0===i)return this.set(0,0,0);i=t.dot(this)/i;return this.copy(t).multiplyScalar(i)}},{key:"projectOnPlane",value:function(t){return An.copy(this).projectOnVector(t),this.sub(An)}},{key:"reflect",value:function(t){return this.sub(An.copy(t).multiplyScalar(2*this.dot(t)))}},{key:"angleTo",value:function(t){var i=Math.sqrt(this.lengthSq()*t.lengthSq());if(0===i)return Math.PI/2;i=this.dot(t)/i;return Math.acos(jn.clamp(i,-1,1))}},{key:"distanceTo",value:function(t){return Math.sqrt(this.distanceToSquared(t))}},{key:"distanceToSquared",value:function(t){var i=this.x-t.x,n=this.y-t.y,t=this.z-t.z;return i*i+n*n+t*t}},{key:"manhattanDistanceTo",value:function(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)+Math.abs(this.z-t.z)}},{key:"setFromSpherical",value:function(t){return this.setFromSphericalCoords(t.radius,t.phi,t.theta)}},{key:"setFromSphericalCoords",value:function(t,i,n){var r=Math.sin(i)*t;return this.x=r*Math.sin(n),this.y=Math.cos(i)*t,this.z=r*Math.cos(n),this}},{key:"setFromCylindrical",value:function(t){return this.setFromCylindricalCoords(t.radius,t.theta,t.y)}},{key:"setFromCylindricalCoords",value:function(t,i,n){return this.x=t*Math.sin(i),this.y=n,this.z=t*Math.cos(i),this}},{key:"setFromMatrixPosition",value:function(t){t=t.elements;return this.x=t[12],this.y=t[13],this.z=t[14],this}},{key:"setFromMatrixScale",value:function(t){var i=this.setFromMatrixColumn(t,0).length(),n=this.setFromMatrixColumn(t,1).length(),t=this.setFromMatrixColumn(t,2).length();return this.x=i,this.y=n,this.z=t,this}},{key:"setFromMatrixColumn",value:function(t,i){return this.fromArray(t.elements,4*i)}},{key:"setFromMatrix3Column",value:function(t,i){return this.fromArray(t.elements,3*i)}},{key:"equals",value:function(t){return t.x===this.x&&t.y===this.y&&t.z===this.z}},{key:"fromArray",value:function(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;return this.x=t[i],this.y=t[i+1],this.z=t[i+2],this}},{key:"toArray",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[],i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;return t[i]=this.x,t[i+1]=this.y,t[i+2]=this.z,t}},{key:"fromBufferAttribute",value:function(t,i,n){return void 0!==n&&console.warn("THREE.Vector3: offset has been removed from .fromBufferAttribute()."),this.x=t.getX(i),this.y=t.getY(i),this.z=t.getZ(i),this}},{key:"random",value:function(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this}}])&&Rn(t.prototype,i),n&&Rn(t,n),r}(),An=new Tn,Sn=new s;E=function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.Vi=0,this.zt={},this.Ht=0,this.kr=[],this.Rr=[],this.Tr=0,this.Ar=[],this.Sr=!1,this.Nr=!1,this.Ir=0,this.Lr=!1,this.Dr=[]};Object.assign(E.prototype,{set Pr(t){-1!=t&&(this.Ht|=t)},get Pr(){return 3&this.Ht},set Fr(t){this.Ht|=t<<2},get Fr(){return this.Ht>>2&7},set Cr(t){this.Ht|=t<<5},get Cr(){return this.Ht>>5&3},set Ur(t){this.Ht|=t<<7},get Ur(){return this.Ht>>7&3},set Br(t){this.Ht|=t<<9},get Br(){return this.Ht>>9&3},er:function(t){this.Vi=t,this.zt={x:0,y:0},this.Ht=0,this.Tr=0,this.Sr=!1,this.Nr=!1},Gr:function(t){return this.Vi==t.Vi},Zr:function(t){this.Vi=t.Vi,this.zt=Object.assign({},t.zt),this.Ht=t.Ht,this.kr=[];for(var i=0;i<t.kr.length;i++)this.kr[i]=t.kr[i];this.Rr=[];for(var n=0;n<t.Rr.length;n++)this.Rr[n]=t.Rr[n];this.Tr=t.Tr,this.Ar=[];for(var r=0;r<t.Ar.length;r++)this.Ar[r]=t.Ar[r];this.Sr=t.Sr,this.Nr=t.Nr},Wr:function(){return this.kr.length},Vr:function(){return this.Rr.length},Yr:function(t){if(this.Fr==xn.NaviLiftType_Stair)return!0;for(var i=this.kr.length-1;0<=i;i--)if(this.kr[i]==t)return!0;return!1}});var Nn=E;function In(t){return(In="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ln(t,i){return(Ln=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function Dn(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=Pn(n);return t=r?(t=Pn(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==In(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function Pn(t){return(Pn=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var Fn=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&Ln(t,i)}(n,Nn);var i=Dn(n);function n(){var t;return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,n),(t=i.call(this)).Xr=0,t.br=0,t.zr=null,t.Ir=0,t.qr=null,t.Fr=null,t}return n}();A=function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.Vi=0,this.Hr=-1,this.Qr={},this.Kr=-1,this.Jr={},this.pr=0,this.Ht=0,this.$r=[],this.te=-1,this.ie=!1,this.ne=0};Object.assign(A.prototype,{set re(t){this.Ht|=t},get re(){return 3&this.Ht},set ee(t){this.Ht|=t<<2},get ee(){return this.Ht>>2&3},set Br(t){this.Ht|=t<<4},get Br(){return this.Ht>>4&3},set se(t){t?this.Ht|=16:this.Ht&=-17},get se(){return 16&this.Ht?1:0},oe:function(t){this.Vi=t.Vi,this.Hr=t.Hr,this.Qr=Object.assign({},t.Qr),this.Kr=t.Kr,this.Jr=Object.assign({},t.Jr),this.pr=t.pr,this.Ht=t.Ht,this.$r=[],this.te=t.te;for(var i=0;i<t.$r.pr;i++)this.$r[i]=t.$r[i]},ue:function(){this.pr=pt.distanceOfTwoPoints(this.Qr,this.Jr)},he:function(){var t=this.Qr.x-this.Jr.x,i=this.Qr.y-this.Jr.y;this.pr=Math.abs(t)+Math.abs(i)},ae:function(){var t={};return t.x=(this.Qr.x+this.Jr.x)/2,t.y=(this.Qr.y+this.Jr.y)/2,t}});var Cn=A;function Un(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}r=function(){function e(t,i,n,r){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,e),this.Vi=t,this.br=i||-1,this.Ht=n,this.fe=r,this.ce=r&&r.length?r.length-1:0,this.le=0,this.ve=null,this.de=null,this.ye=[],this.be=[],this.pe=null,this.Zt=new mt,this.Zt.Ft(this.fe)}var t,i,n;return t=e,(i=[{key:"_pass",get:function(){return 3&this.Ht},set:function(t){this.Ht|=t}},{key:"_area",get:function(){return null==this.pe&&(this.pe=pt.mt(this.fe,this.ce)),this.pe}}])&&Un(t.prototype,i),n&&Un(t,n),e}();Object.assign(r.prototype,{xe:function(t){return!!pt.ht(t,this.Zt.Lt,this.Zt.Dt)&&pt.ct(this.fe,t,this.ce)},wt:function(t,i){for(var n={},r=0;r<this.ce;r++){var e=this.fe[r],s=this.fe[(r+1)%this.ce];if(1==pt.wt(e,s,t,i,n))break}return n}});var Bn=r;function Gn(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}s=function(){function o(t,i,n,r,e,s){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,o),this.Vi=t,this.me="",this.fe=r,this.ce=r&&r.length?r.length-1:0,this.Ut=e,this.we=s,this.Ht=n,this.br=i,this._e=[],this.ge=[],this.Oe=[],this.ve=null,this.de=null,this.ye=[],this.Ee=0,this.le=0,this.pe=null,this.Me=!1,this.Zt=new mt,this.Zt.Ft(this.fe)}var t,i,n;return t=o,(i=[{key:"pass",get:function(){return 3&this.Ht},set:function(t){this.Ht|=t}},{key:"area",get:function(){return null==this.pe&&(this.pe=pt.mt(this.fe,this.ce)),this.pe}}])&&Gn(t.prototype,i),n&&Gn(t,n),o}();Object.assign(s.prototype,{xe:function(t){for(var i=0;i<this.we.length;i++)if(pt.ct(t,this.we[i],this.we[i].length-1))return!1;return pt.ct(t,this.fe,this.ce)}});var Zn=s;function Wn(t){return(Wn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Vn(t,i){return(Vn=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function Yn(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=Xn(n);return t=r?(t=Xn(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==Wn(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function Xn(t){return(Xn=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}E=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&Vn(t,i)}(n,Nn);var i=Yn(n);function n(){var t;return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,n),(t=i.call(this)).br=0,t.te=-1,t.je=[],t.zr=null,t}return n}();Object.assign(E.prototype,{ke:function(t,i,n){if(!t)return[];var r=[];n=n||this.je;for(var e=0;e<n.length;e++){var s=n[e];t==s.Fr&&(i&&(s.Ir=s.Ir+i),r.push(s))}return r}});var zn=E;function qn(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var Hn=function(){function e(t,i,n,r){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,e),this.br=0,this.Ht=r,this.Re=t,this.Te=i,this.Ae=[],this.Se=[],this.Ne=[],this.Ie=[],this.Le=new Map,this.De=new Map,this.Pe=[],this.Fe=new Map,this.Ce=new Map,this.Ue=0,this.Be=0,this.Ge()}var t,i,n;return t=e,(i=[{key:"queryContainIdOfExtent",value:function(t){return this.Ze(t)}},{key:"queryContainExtentID",value:function(t){return this.We(t)}}])&&qn(t.prototype,i),n&&qn(t,n),e}();Object.assign(Hn.prototype,{ei:function(){this.Re=null,this.Te=null,this.Ae=null,this.Se=null,this.Ne=null,(this.Ie=null)!=this.Le&&(this.Le.clear(),this.Le=null),null!=this.De&&(this.De.clear(),this.De=null),(this.Pe=null)!=this.Fe&&(this.Fe.clear(),this.Fe=null),null!=this.Ce&&(this.Ce.clear(),this.Ce=null)},Ve:function(){this.Ie=[];for(var t=0;t<this.Se.length;t++){var i=this.Se[t].toZone();this.Ie.push(i)}for(var n=0;n<this.Ae.length;n++){var r=this.Se[n].toZone();this.Ie.push(r)}},Ye:function(t){for(var i=0;i<this.Ie.length;i++){var n=this.Ie[i];if(n&&n.Vi==t)return n}return null},Xe:function(t){for(var i=0;i<this.Ie.length;i++){var n=this.Ie[i];if(n&&1==n.contain(t))return n}return null},Ze:function(t){if(0===this.Se.length||!t)return 0;for(var i=0,n=this.Se.length;i<n;i++){var r=this.Se[i];if(r.xe(t))return r.Vi}return 0},We:function(t){if(0===this.Se.length||!t)return 0;for(var i=0,n=this.Se.length;i<n;i++){var r=this.Se[i];if(r.xe(t))return r.te}return 0},ze:function(t,i){if(0==this.Ne.length||!t||!i)return!1;for(var n=0;n<this.Ne.length;n++)if(this.Ne[n].isCross(t,i))return!0;return!1},Ge:function(){this.Ue=0;for(var t=this.Be=0;t<this.Te.length;)null==this.Te[t]?this.Te.splice(t,1):(this.Te[t].Vi>this.Be&&(this.Be=this.Te[t].Vi),t++);for(var i=0;i<this.Re.length;)null==this.Re[i]?this.Re.splice(i,1):(this.Re[i].Vi>this.Ue&&(this.Ue=this.Re[i].Vi),i++)},qe:function(t){t=this.Fe.get(t);return void 0===t?null:t},He:function(t){t=this.Ce.get(t);return void 0===t?null:t},Qe:function(t){for(var i=Number.MAX_VALUE,n=null,r=0;r<this.Se.length;++r){var e=this.Se[r];e.contain(t)&&e.area<i&&(i=cur_area,n=e)}for(var s=0;s<this.Ae.size();++s){var o=this.Ae[s];o.contain(t)&&o.area<i&&(i=cur_area,n=o)}return n||null},Ke:function(t){for(var i=Number.MAX_VALUE,n=null,r=0;r<this.Ae.length;++r){var e=this.Ae[r];e.xe(t)&&e.area<i&&(i=e.area,n=e)}return n||null},Je:function(){return++this.Ue},$e:function(){return++this.Be},ts:function(t){this.Ue=t},ns:function(t){this.Be=t},rs:function(){for(var t=0;t<this.Re.length;t++){var i=this.Re[t];i&&(i instanceof zn?this.Fe.set(i.te,t):this.Fe.set(i.Vi,t))}},es:function(){for(var t=0;t<this.Te.length;t++){var i=this.Te[t];i&&this.Ce.set(i.Vi,t)}},ss:function(){var e=this;this.De.clear();for(var t=0;t<this.Te.length;t++)(function(t){var i=e.Te[t];if(null==i)return;var n,r=i.ee;xn.NaviRoadEntry_BOTH!=r&&xn.NaviRoadEntry_FORWARD!=r||(t=i.Hr,(n=e.De.get(t))||e.De.set(t,n=[]),null==n.find(function(t){return t.us==i.Kr})&&n.push({us:i.Kr,pr:i.pr})),xn.NaviRoadEntry_BOTH!=r&&xn.NaviRoadEntry_BACK!=r||(n=i.Kr,(r=e.De.get(n))||e.De.set(n,r=[]),null==r.find(function(t){return t.us==i.Hr})&&r.push({us:i.Hr,pr:i.pr}))})(t)},hs:function(t){for(var i=0;i<this.Re.length;i++)if(this.Re[i].Vi==t)return i;return-1},as:function(t){for(var i=9;i<this.Te.length;i++)if(this.Te[i].Vi==t)return i;return-1},cs:function(t){for(var i=[],n=this.Te,r=0;r<n.length;r++){var e=n[r],s=e.Qr,o=e.Jr,u=[];t.ls([s,o],u)?i.push({road:e,crss:u,index:r}):t.vs(s)&&i.push({road:e,crss:[],index:r})}return i},ds:function(t){for(var i=null,n=Number.MAX_VALUE,r=0;r<this.Ae.length;r++){var e=this.Ae[r];pt.ct(t,e.fe,e.ce)&&n>e.area&&(n=(i=e).area)}return i},ys:function(t){for(var i=-1,n=0;n<this.Ie.length;n++){var r=this.Ie[n];if(pt.ct(t,r.fe,r.ce)){i=r.Vi;break}}return i},bs:function(){var t=this.Ae.length,t=parseInt(Math.random()*t);return this.Ae[t]},ps:function(){for(var t=0;t<this.Pe.length;t++)this.Pe[t]=!1},xs:function(){for(var t=[],i=0;i<this.Re.length;i++){var n=this.Re[i];n.Fr!=xn.NaviLiftType_NULL&&t.push(n)}return t},ms:function(t){for(var i=null,n=0;n<this.Se.length;n++)if(t==this.Se[n].Vi){i=this.Se[n];break}if(!i)return null;for(var r=[],e=0;e<this.Re.length;e++){var s=this.Re[e];pt.ct(s.zt,i.fe,i.ce)&&r.push(s)}for(var o=[],u=0;u<this.Te.length;u++){var h=this.Te[u];pt.ct(h.Qr,i.fe,i.ce)&&o.push(h)}for(var a=[],f=0;f<this.Ae.length;f++){var c=this.Ae[f];pt.ct(c.Ut,i.fe,i.ce)&&a.push(c)}var l=new Hn(r,o,!0,1);return l.Ae=a,l.Se=[i],l}});var Qn=Hn;A=function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.ws=null};Object.assign(A.prototype,{ei:function(){this.ws=null},_s:function(t){this.ws=t},gs:function(t,i){var n=null;return 0==i?n=Bi.Ci(t):1==i?n=Bi.Ui(t):2==i&&(n=Bi.Bi(t)),{points:n[0],holes:n.slice(1)}},Os:function(t,i,n){var r=null;return 0==n?r=Ci.Ci(t,i):1==n?r=Ci.Ui(t,i):2==n&&(r=Ci.Bi(t,i)),{points:r[0],holes:r.slice(1)}},Es:function(t){if(t&&"string"!=typeof t)for(var i in t){var n=t[i];if("vertices"==i||"vertex"==i)for(var r in n)r%2==0&&(n[r]=-n[r]);else"scene_data"==i?(n.x=-n.x,n.defCenX=-n.defCenX):this.Es(n)}},Ms:function(t){if(""==t||!t)return[];var i=[],n=t,t=[];return 0<=n.indexOf("|")||0<=n.indexOf("-")||0<=n.indexOf(",")?(t=n.split(/[|,-]/),i.push.apply(i,t)):i.push(n),i},js:function(t,i,n,r){for(var e={},s=[],o=[],u=[],h=[],a=[],f=this.ws.fileVer,c=r.naviExtents&&0<r.naviExtents.length?r.naviExtents:i.extentLayer,l=0,v=c.length;l<v;l++){var d=c[l],y=null,y=1===f?this.gs(d.geo,2):this.Os(d.idxs,d.pts,2),y={eid:d.id||d.eid,geoArr:y.points,holes:y.holes};o.push(y)}for(var b=0,p=i.labelLayer.length;b<p;b++)e[i.labelLayer[b].eid]=i.labelLayer[b];for(var x=0,m=i.modelLayer.length;x<m;x++){var w=i.modelLayer[x],_=n.modelLayer[x],g={};g.fid=_.fid,g.eid=w.eid,g.pass=_.pass,g.Ut=(1===f?this.gs(e[w.eid].geo,0):this.Os(e[w.eid].idxs,e[w.eid].pts,0)).points;_=null,_=1===f?this.gs(w.geo,2):this.Os(w.idxs,w.pts,2);g.geoArr=_.points,g.holes=_.holes,s.push(g)}if(r&&r.naviNodes)for(var O=0,E=r.naviNodes.length;O<E;O++){var M=r.naviNodes[O];M.geoArr=(1===f?this.gs(M.geo,0):this.Os(M.idxs,M.pts,0)).points,M.linkSegArr=this.Ms(M.linkSeg),M.liftFloorArr=this.Ms(M.liftFloor),u.push(M)}if(r&&r.naviSegments)for(var j=0,k=r.naviSegments.length;j<k;j++){var R=r.naviSegments[j];R.geoArr=(1===f?this.gs(R.geo,1):this.Os(R.idxs,R.pts,1)).points,R.m_Id=r.mid,h.push(R)}if(r&&r.naviZones)for(var T=0,A=r.naviZones.length;T<A;T++){var S=r.naviZones[T],N=null,N=1===f?this.gs(S.geo,2):this.Os(S.idxs,S.pts,2);S.geoArr=N.points,S.holes=N.holes,S.m_Id=r.mid,a.push(S)}return{level:t,extentLayer:o,modelLayer:s,naviSegments:h,naviNodes:u,naviZones:a}}});var Kn=A;function Jn(t,i){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,i){if(t){if("string"==typeof t)return $n(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?$n(t,i):void 0}}(t))||i&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var e,s=!0,o=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){o=!0,e=t},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw e}}}}function $n(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}r=function t(i){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.ks=i,this.Rs=null,this.Ts=null,this.As=-1,this.Ss=-1,this.Ns=g.MODULE_SHORTEST,this.Is=[],this.ks.rs(),this.ks.es(),this.ks.ss(),this.Ls=5e-4,this.Ds=0};Object.assign(r.prototype,{Ps:function(t,i){if(null!=this.ks.Re&&0!=this.ks.Re.length&&this.ks.Re[0]instanceof zn){this.Ds=10;for(var r={},n=0;n<this.ks.Re.length;n++){var e=this.ks.Re[n],s=e.Fs.fe,o=s.length-1,u=this.ks.Re[n].te;r[u]=[1,1,Number.MAX_VALUE],pt.ct(t,s,o)&&(r[u][0]=0,t.level==e.br&&e.te),pt.ct(i,s,o)&&(r[u][1]=0,i.level==e.br&&e.te);s=pt.Nt(s),s=pt.vt(t,s)+pt.vt(i,s);r[u][2]=s}var h,a=this.ks.De,f=Jn(a.keys());try{for(f.s();!(h=f.n()).done;){var c=h.value;a.get(c).sort(function(t,i){var n=r[t.us][0]+r[t.us][1]-(r[i.us][0]+r[i.us][1]);return 0==n?r[t.us][2]-r[i.us][2]:n})}}catch(t){f.e(t)}finally{f.f()}}},Cs:function(t,i,n,r,e){this.Us(n),this.Rs=t,this.Ts=i,this.Ps(r,e),this.As=t.Vi,this.Ss=i.Vi;e=this.ks.qe(this.As);if(null==e)return S.ROUTE_FAILED_CANNOT_ARRIVE;t=this.ks.qe(i.Vi);if(null==t)return S.ROUTE_FAILED_CANNOT_ARRIVE;i=[];return i.push(t),this.Bs(this.ks,e,i)?S.ROUTE_SUCCESS:S.ROUTE_FAILED_CANNOT_ARRIVE},Gs:function(t,i,n,r){n&&r&&this.Ps(n,r),this.As=t,this.Ss=i;r=this.ks.qe(t);if(null==r)return S.ROUTE_FAILED_CANNOT_ARRIVE;t=this.ks.qe(i);if(null==t)return S.ROUTE_FAILED_CANNOT_ARRIVE;i=[];return i.push(t),this.Bs(this.ks,r,i)?S.ROUTE_SUCCESS:S.ROUTE_FAILED_CANNOT_ARRIVE},Zs:function(){var t=[],i=this.ks.qe(this.Ss),n=[];n.push(i);i=[];return this.Ws(n,this.ks.Re,i,[]),t.push.apply(t,i),t},ei:function(){this.ks.ei(),this.ks=null,this.Rs=null,this.Ts=null},Bs:function(t,i,n){if(null==t)return!1;var r=t.Re.length;if(i<0||r<=i)return!1;this.Vs(n,i);for(var e=0;e<r;++e)t.Re[e].Ir=Number.MAX_VALUE,t.Re[e].Lr=!1,t.Re[e].Dr=[];t.Re[i].Ir=0;var s=n.length,o=0,u=[];u.push(t.Re[i]);for(var h=this.Ys.bind(this);0<u.length;){var a=null,a=u[0]instanceof zn?u[0].te:u[0].Vi;u.splice(0,1);var f=t.qe(a);if(null!=f){var c=t.Re[f];if(!c.Lr){c.Lr=!0;var l=n.indexOf(f);if(-1!=l&&++o==s)return!0;if(0!=t.De.size){var v=t.De.get(a);if(v)for(var d=0;d<v.length;d++){var y,b=v[d],p=b.us,x=t.qe(p);null!=x&&(y=b.pr,p=t.Re[x],b=this.Ds+y+c.Ir,b=Number(b.toFixed(3)),p.Lr||(p.Ir>b?(p.Ir=b,p.Dr=[],p.Dr.push(f),u.push(t.Re[x]),u.sort(h)):Math.abs(p.Ir-(y+c.Ir))<1e-12&&-1==p.Dr.indexOf(f)&&p.Dr.push(f)))}}}}}return!(o<s)},Ys:function(t,i){return t.Ir-i.Ir},Vs:function(t,i){for(var n=t.indexOf(i);-1!=n;)t.splice(n,1),n=t.indexOf(i)},Us:function(t){if(this.Ns!=t){for(var i={},n=0;n<this.ks.Te.length;n++){var r=this.ks.Te[n],e=r.Hr,s=r.Kr,r=r.re;i.hasOwnProperty(e)||(i[e]={}),i[e][s]=r,i.hasOwnProperty(s)||(i[s]={}),i[s][e]=r}if(g.MODULE_SHORTEST==t){var o,u=Jn(this.ks.De.keys());try{for(u.s();!(o=u.n()).done;){var h,a=o.value,f=Jn(this.ks.De.get(a));try{for(f.s();!(h=f.n()).done;){var c=h.value,l=c.us,v=i[a][l];c.pr=c.pr/v}}catch(t){f.e(t)}finally{f.f()}}}catch(t){u.e(t)}finally{u.f()}}else if(g.MODULE_BEST==t){var d,y=Jn(this.ks.De.keys());try{for(y.s();!(d=y.n()).done;){var b,p=d.value,x=Jn(this.ks.De.get(p));try{for(x.s();!(b=x.n()).done;){var m=b.value,w=m.us,_=i[p][w];m.pr=m.pr*_}}catch(t){x.e(t)}finally{x.f()}}}catch(t){y.e(t)}finally{y.f()}}this.Ns=t}},Xs:function(t,i,n,r){0!=i[t].Dr.length&&(t=i[t].Dr[0],n.push(i[t]),r.count++,this.Xs(t,i,n,r))},Ws:function(t,i,n,r){for(var e=0;e<t.length;e++){var s=t[e];n.push(i[s]);var o={count:1};this.Xs(s,i,n,o),r.push(o.count)}}});var tr=r;function ir(t){return(ir="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nr(t,i){return(nr=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function rr(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=er(n);return t=r?(t=er(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==ir(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function er(t){return(er=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var sr=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&nr(t,i)}(n,Zn);var i=rr(n);function n(){var t;return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,n),(t=i.call(this)).Vi=-1,t.zs=[],t}return n}();var or=function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.st=null,this.qs=null,this.Hs=null,this.vt=0,this.Qs=!1,this.Ks=xn.NaviObstructType_MODEL};function ur(t,i){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=ar(t))||i&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var e,s=!0,o=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){o=!0,e=t},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw e}}}}function hr(t){return function(t){if(Array.isArray(t))return fr(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||ar(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ar(t,i){if(t){if("string"==typeof t)return fr(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?fr(t,i):void 0}}function fr(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}function cr(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}s=function(){function i(t){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,i),this.Js=null,this.$s=null,this.ks=t,this.to=new Map,this.io=new Map,this.no=new Map,this.ro=new Map,this.eo=new Map,this.so=[],this.oo=Number.INT_MIN,this.uo=Number.INT_MIN,this.ho=-1,this.Rs=null,this.Ts=null,this.Ns=g.MODULE_SHORTEST,this.ao=new Map,this.fo=new Map,this.co=new Map,this.vo=[],this.do=[],this.yo=[],this.bo=[],this.po=100,this.xo=-1,this.mo=15,this.wo=.1,this._o=[],this.go=[],this.Oo=[],this.Eo=[],this.Mo=!0,this.jo=[],this.Ht=null,this.ko(),this.Ro(),this.To(),this.Ao(),this.So()}var t,n,r;return t=i,(n=[{key:"assignedPts",get:function(){return this.go},set:function(t){this.go=t}},{key:"obstruct",get:function(){return this.Mo},set:function(t){this.Mo=t}}])&&cr(t.prototype,n),r&&cr(t,r),i}();Object.assign(s.prototype,{Cs:function(t,i,n){if(this.oo=t instanceof Nn?this.No(t.zt):this.No(t),this.uo=i instanceof Nn?this.No(i.zt):this.No(i),n&&(this.Ns=n),t instanceof Nn)if(null!=t.Io){if(t=t.zt,S.ROUTE_FAILED_NO_START_ARRIVAL==this.Lo(t))return S.ROUTE_FAILED_NO_START_ARRIVAL}else this.Rs=t;else if(S.ROUTE_FAILED_NO_START_ARRIVAL==this.Lo(t))return S.ROUTE_FAILED_NO_START_ARRIVAL;if(i instanceof Nn)if(null!=i.Io){if(i=i.zt,S.ROUTE_FAILED_NO_DEST_ARRIVAL==this.Do(i))return S.ROUTE_FAILED_NO_DEST_ARRIVAL}else this.Ts=i;else if(S.ROUTE_FAILED_NO_DEST_ARRIVAL==this.Do(i))return S.ROUTE_FAILED_NO_DEST_ARRIVAL;return null==this.Rs?S.ROUTE_FAILED_NO_DATA_START:null==this.Ts?S.ROUTE_FAILED_NO_DATA_DEST:this.Po()},Fo:function(){var t;this.Eo=[];for(var i=0;i<this.go.length;i++){var n={x:(n=this.go[i])[0],y:n[1]},r=this.No(n),n=this.Co(r,n);this.Eo.push(n)}(t=this.so).push.apply(t,hr(this.Eo))},Zs:function(){return this.so},ei:function(){this.Js=null,this.$s=null,this.ks.ei(),this.ks=null,this.to.clear(),this.to=null;var t,i=ur(this.io);try{for(i.s();!(t=i.n()).done;)t.value[1].ei()}catch(t){i.e(t)}finally{i.f()}this.io.clear(),this.io=null;var n,r=ur(this.ro);try{for(r.s();!(n=r.n()).done;)n.value[1].ei()}catch(t){r.e(t)}finally{r.f()}this.ro.clear(),this.ro=null;var e,s=ur(this.no);try{for(s.s();!(e=s.n()).done;)e.value[1].ei()}catch(t){s.e(t)}finally{s.f()}this.no.clear(),this.no=null;var o,u=ur(this.eo);try{for(u.s();!(o=u.n()).done;)o.value[1].ei()}catch(t){u.e(t)}finally{u.f()}this.eo.clear(),this.eo=null,this.so=[],this.Rs=null,this.Ts=null,this.ao.clear(),this.ao=null,this.fo.clear(),this.fo=null,this.co.clear(),this.vo=[],this.do=[],this.yo=[],this.bo=[],this.jo=[],this.Mo=!1},Un:function(){this.so=[],this.Uo(-1),this.Uo(this.oo),this.oo=Number.INT_MIN,this.Uo(this.uo),this.uo=Number.INT_MIN,this.Bo(),this._o=[];var t=this.ks.Te.length;this.ho<-2*t&&(this.ho=-1);for(var i=0;i<this.vo.length;i++){var n=this.vo[i];this.Go(n.Zo,n.Hs,xn.NaviRoadHinderType_HINDER_HIGH)}this.vo=[],this.do=[],this.yo=[],this.bo=[]},Wo:function(t){this.jo=t,this.no.clear(),this.eo.clear();for(var i=0;i<this.jo.length;i++)for(var n=this.jo[i],r=this.Vo(n),e=0;e<r.length;e++){var s=r[e],o=this.no.get(s);o||(o=this.Yo(this.io.get(s)),this.no.set(s,o)),this.Xo(o,n)}var u,h=ur(this.no);try{for(h.s();!(u=h.n()).done;){var a=u.value;this.eo.set(a[0],new tr(a[1]))}}catch(t){h.e(t)}finally{h.f()}},Xo:function(t,i){for(var n=t.cs(i),r=0;r<n.length;r++){var e=n[r],s=new Cn;s.oe(e.road);var o=s.ee;s.ee=xn.NaviRoadEntry_FORBID,t.Te[e.index]=s,1==e.crss.length&&(i.vs(s.Qr)?this.zo(e.crss[0],this.Je(),s.Jr,this.Je(),o,t):this.zo(s.Qr,this.Je(),e.crss[0],this.Je(),o,t)),2<=e.crss.length&&(this.zo(s.Qr,this.Je(),e.crss[0],this.Je(),o,t),this.zo(e.crss[e.crss.length-1],this.Je(),s.Jr,this.Je(),o,t))}var u=new sr;u.Vi=this.qo(),u.fe=i.fe,u.pass=xn.NaviModelPassType_NOT_PASS,t.Ae.push(u)},zo:function(t,i,n,r,e,s){var o,u;t instanceof Nn?o=t:((o=new Nn).Vi=i,o.zt=t,s.Re.push(o)),n instanceof Nn?u=n:((u=new Nn).Vi=r,u.zt=n,s.Re.push(u));n=new Cn;n.Vi=this.$e(),n.Hr=o.Vi,n.Qr=o.zt,n.Kr=u.Vi,n.Jr=u.zt,n.ee=e,n.Vi,n.Hr,n.Qr.x.toFixed(3),n.Qr.y.toFixed(3),n.Kr,n.Jr.x.toFixed(3),n.Jr.y.toFixed(3),s.Te.push(n)},qo:function(){return this.ho--},Ho:function(t){var i={te:-1,Hs:-1},n=this.ks.We(t);if(n<=0)return i;if(i.te=n,1===this.Ht)return i.Hs=0,i;var r=this.No(t),e=this.Qo(r).Ke(t,-0);if(!e)return i.Hs=0,i;if((i.Hs=e).pass==xn.NaviModelPassType_NOT_PASS)return i.Hs=-1,i;if(e.pass===xn.NaviModelPassType_PASS_THROUGH||e.pass===xn.NaviModelPassType_PASS_NOT_THROUGH){if(0<e._e.length)return i;for(var s={},o=[],u=[],h=this.Qo(r).Te,a=!0,f=0;f<e.ce;f++)for(var c=e.fe[f],l=e.fe[f+1],v=0;v<h.length;v++){var d=h[v];1===pt.wt(c,l,d.Qr,d.Jr,s)&&xn.NaviRoadEntry_FORBID!==d.ee&&(a=!1,(d={}).x=s.x,d.y=s.y,o.push(d),u.push(h[v]))}if(!a){e._e=[],e.Oe=[],e.ge=[];for(var y=0;y<o.length;y++){var b=o[y],p=u[y],b=this.Ko(r,b,p);b.Jo.push(p.Vi),b.Pr=xn.NaviNodeType_COMMON,p.ee===xn.NaviRoadEntry_BOTH?b.entranceType=xn.NaviEntranceType_ACCESS:pt.ct(p.Qr,e.fe,e.ce)?(p.ee===xn.NaviRoadEntry_FORWARD&&(b.entranceType=xn.NaviEntranceType_EXIT),p.ee===xn.NaviRoadEntry_BACK&&(b.entranceType=xn.NaviEntranceType_ENTRANCE)):(p.ee===xn.NaviRoadEntry_FORWARD&&(b.entranceType=xn.NaviEntranceType_ENTRANCE),p.ee===xn.NaviRoadEntry_BACK&&(b.entranceType=xn.NaviEntranceType_EXIT)),e._e.push(b),e.Oe.push(b.entranceType),e.ge.push(b.Pr)}}}return i},$o:function(t){var i,n=null,r=Number.MAX_VALUE,e=ur(t.values());try{for(e.s();!(i=e.n()).done;){var s=i.value;0<s.length&&s[0].vt<r&&(n=s[0],r=s[0].vt)}}catch(t){e.e(t)}finally{e.f()}return n},Lo:function(t){var i=this;if(1!==this.Ht){var n=null,r=!0,n=null!=this.Js&&this.Js.pass==xn.NaviModelPassType_PASS_THROUGH&&0==this.Js._e.length?this.tu(this.oo,t,30,this.Js):this.tu(this.oo,t);if(this.Rs=this.Co(this.oo,t),null==this.Js&&(r=!1),null!=n&&0<n.size){var e,s=!1,o=[],r=!1,u=ur(n);try{for(u.s();!(e=u.n()).done;)for(var h=e.value[1],a=0;a<h.length;a++)0!=h[a].qs.ee&&(r=!0)}catch(t){u.e(t)}finally{u.f()}var f,c=ur(n);try{for(c.s();!(f=c.n()).done;){var l,v=f.value[1],d=v[0].qs,y=this.iu(this.oo,t,d,v,this.Js);if(0==y.length){var b,p=ur(v);try{for(p.s();!(b=p.n()).done;){var x=b.value,m=this.nu(this.oo,x.st,x.qs);this.ru(this.oo,this.Rs,m)}}catch(t){p.e(t)}finally{p.f()}}else Math.abs(y[0].eu)>pt.$()?o.push.apply(o,hr(y)):(s=!0,l=this.nu(this.oo,y[0].st,y[0].qs),this.ru(this.oo,this.Rs,l))}}catch(t){c.e(t)}finally{c.f()}s||o.forEach(function(t){t=i.nu(i.uo,t.st,t.qs);i.ru(i.oo,i.Rs,t)})}if(r&&null!=this.Js&&0<this.Js._e.length)for(var w=null,_=this.io.get(this.oo),g=0;g<this.Js._e.length;g++){var w=this.Js._e[g],O=this.Js.Oe[g];if(O==xn.NaviEntranceType_EXIT||O==xn.NaviEntranceType_ACCESS){for(var E=this.Js.we,M=!0,j=0;j<E.length;j++){for(var k=E[j],R=0;R<k.length-1;R++)if(pt.ut(t,w.zt,k[R],k[R+1])){M=!1;break}if(0==M)break}if(0!=M){for(var T=0;T<_.Te.length;T++){var A=_.Te[T];if(0!=A.ee)if(pt.ut(t,w.zt,A.Qr,A.Jr)&&0<A.Vi&&0==w.Jo.includes(A.Vi)){M=!1;break}}0!=M&&this.ru(this.oo,this.Rs,w)}}}}else{n=this.io.get(this.oo),n=this.su(t,n);if(!n.road)return S.ROUTE_FAILED_NO_START_ARRIVAL;n=this.nu(this.oo,n.coords,n.road);this.Rs=n}},Do:function(t){var i=this;if(1!==this.Ht){var n=null,r=!0,n=null!=this.$s&&this.$s.pass==xn.NaviModelPassType_PASS_THROUGH&&0==this.$s._e.length?this.tu(this.uo,t,30,this.$s):this.tu(this.uo,t);if(this.Ts=this.Co(this.uo,t),null==this.$s&&(r=!1),null!=n&&0<n.size){var e,s=!1,o=[],r=!1,u=ur(n);try{for(u.s();!(e=u.n()).done;)for(var h=e.value[1],a=0;a<h.length;a++)0!=h[a].qs.ee&&(r=!0)}catch(t){u.e(t)}finally{u.f()}var f,c=ur(n);try{for(c.s();!(f=c.n()).done;){var l,v=f.value[1],d=v[0].qs,y=this.iu(this.uo,t,d,v,this.$s);if(0==y.length){var b,p=ur(v);try{for(p.s();!(b=p.n()).done;){var x=b.value,m=this.nu(this.uo,x.st,x.qs);this.ru(this.uo,this.Ts,m)}}catch(t){p.e(t)}finally{p.f()}}else Math.abs(y[0].eu)>pt.$()?o.push.apply(o,hr(y)):(s=!0,l=this.nu(this.uo,y[0].st,y[0].qs),this.ru(this.uo,this.Ts,l))}}catch(t){c.e(t)}finally{c.f()}s||o.forEach(function(t){t=i.nu(i.uo,t.st,t.qs);i.ru(i.uo,i.Ts,t)})}if(r&&null!=this.$s&&0<this.$s._e.length)for(var w=null,_=this.io.get(this.uo),g=0;g<this.$s._e.length;g++){var w=this.$s._e[g],O=this.$s.Oe[g];if(O==xn.NaviEntranceType_ENTRANCE||O==xn.NaviEntranceType_ACCESS){for(var E=this.$s.we,M=!0,j=0;j<E.length;j++){for(var k=E[j],R=0;R<k.length-1;R++)if(pt.ut(t,w.zt,k[R],k[R+1])){M=!1;break}if(0==M)break}if(0!=M){for(var T=0;T<_.Te.length;T++){var A=_.Te[T];if(0!=A.ee)if(pt.ut(t,w.zt,A.Qr,A.Jr)&&0<A.Vi&&0==w.Jo.includes(A.Vi)){M=!1;break}}0!=M&&this.ru(this.uo,this.Ts,w)}}}}else{n=this.io.get(this.uo),n=this.su(t,n);if(!n.road)return S.ROUTE_FAILED_NO_DEST_ARRIVAL;n=this.nu(this.uo,n.coords,n.road);this.Ts=n}},Po:function(){var t=this.ou(this.Rs.zt);t&&(this.Go(this.oo,t,xn.NaviRoadHinderType_HINDER_GENERAL),this.vo.push({Zo:this.oo,Hs:t}));t=this.ou(this.Ts.zt);if(t&&(this.Go(this.uo,t,xn.NaviRoadHinderType_HINDER_GENERAL),this.vo.push({Zo:this.uo,Hs:t})),this.so=[],this.oo===this.uo){var i=this.uu(this.oo),n=i.Cs(this.Rs,this.Ts,this.Ns);if(n!==S.ROUTE_SUCCESS)return n;var r=i.Zs();this.so=[],r.reverse(),(n=this.so).push.apply(n,hr(r))}else{if(-1!==this.oo&&-1===this.uo){var i=this.hu(this.Rs.zt,this.oo,xn.NaviEntranceType_EXIT),n=this.uu(this.oo),r=n.Cs(this.Rs,i,this.Ns);if(r!=S.ROUTE_SUCCESS)return r;var e=n.Zs(e),n=this.uu(-1);if((r=n.Cs(i,this.Ts,this.Ns))!=S.ROUTE_SUCCESS)return r;r=n.Zs();e.reverse(),r.reverse(),(n=this.so).push.apply(n,hr(e)),(e=this.so).push.apply(e,hr(r))}if(-1===this.oo&&-1!==this.uo){var s=this.hu(this.Ts.zt,this.uo,xn.NaviEntranceType_ENTRANCE),o=this.uu(this.uo),u=o.Cs(s,this.Ts,this.Ns);if(u!=S.ROUTE_SUCCESS)return u;var h=o.Zs(),o=this.uu(-1);if((u=o.Cs(this.Rs,s,this.Ns))!=S.ROUTE_SUCCESS)return u;s=o.Zs();s.reverse(),h.reverse(),(u=this.so).push.apply(u,hr(s)),(o=this.so).push.apply(o,hr(h))}if(-1!==this.oo&&-1!==this.uo){var u=this.hu(this.Rs.zt,this.oo,xn.NaviEntranceType_EXIT),s=this.uu(this.oo),o=s.Cs(this.Rs,u,this.Ns);if(o!=S.ROUTE_SUCCESS)return o;var a=s.Zs(a),h=this.hu(this.Ts.zt,this.uo,xn.NaviEntranceType_ENTRANCE),s=this.uu(this.uo);if((o=s.Cs(h,this.Ts,this.Ns))!=S.ROUTE_SUCCESS)return o;var f=s.Zs(f),s=this.uu(-1);if((o=s.Cs(u,h,this.Ns))!=S.ROUTE_SUCCESS)return o;o=s.Zs();a.reverse(),o.reverse(),f.reverse(),(s=this.so).push.apply(s,hr(a)),(a=this.so).push.apply(a,hr(o)),(o=this.so).push.apply(o,hr(f))}}return S.ROUTE_SUCCESS},au:function(t,i,n){for(var r=0;r<i;r++)if(null!=t[r]&&t[r].Vi==n)return r;return-1},No:function(t){for(var i=this.ks.Ie,n=0;n<i.length;n++){var r=i[n];if(r.xe(t))return r.Vi}return-1},ko:function(){for(var t=this.ks.Ie,i=0;i<t.length;i++){var n=t[i],r=this.fu(this.ks,n);this.io.set(n.Vi,r)}this.io.set(-1,this.ks);var e,s=ur(this.io);try{for(s.s();!(e=s.n()).done;){var o=e.value;o[1].Ge(),this.ro.set(o[0],new tr(o[1]))}}catch(t){s.e(t)}finally{s.f()}},fu:function(t,i){if(t&&i){for(var n,r=0,e=0,s=t.Te.length,o=t.Re.length,u=t.Te,h=t.Re,a=0,f=new Int8Array(s),c=0;c<s;c++)u[c]&&u[c].ee!=xn.NaviRoadEntry_FORBID&&(n=u[c],r=i.contain(n.Qr),e=i.contain(n.Jr),0==r&&0==e||(a=!0),1==r&&1==e&&(f[c]=1),1==r&&0==e&&(f[c]=2),0==r&&1==e&&(f[c]=3),0==r&&0==e&&(f[c]=4),2==r&&0==e&&(f[c]=6),2==r&&1==e&&(f[c]=7),0==r&&2==e&&(f[c]=8),1==r&&2==e&&(f[c]=9));if(0==a)return null;for(var l,v,d,y,b,p,x=new Array(o),m=new Array(s),w=0,_=new Int8Array(o),g=f.length,O=0,E=g;O<E;O++)0!=f[O]&&6!=f[O]&&8!=f[O]&&1!=f[O]&&u[O]&&(v=(l=u[O]).ee,b=[l.Vi],2==f[O]||9==f[O]?(d=null,d=2==f[O]?i.wt(l.Qr,l.Jr):Object.assign({},l.Jr),(p=new Nn).Vi=this.Je(),p.Pr=xn.NaviNodeType_COMMON,p.Fr=xn.NaviLiftType_NULL,p.entranceType=xn.NaviEntranceType_ACCESS,p.zt=Object.assign({},d),(y=new Cn).setRoad(l),y.Vi=this.$e(),b.push(y.Vi),p.segLinks=b,(d=new Nn).setNode(p),v==xn.NaviRoadEntry_FORWARD?d.entranceType=xn.NaviEntranceType_EXIT:v==xn.NaviRoadEntry_BACK&&(d.entranceType=xn.NaviEntranceType_ENTRANCE),i.be.push(d),w=this.au(h,o,y.Kr),y.Jr=Object.assign({},d.zt),y.$r[1]=Object.assign({},d.zt),y.Kr=d.Vi,y.ue(),m[O]=y,x[w]=d,x[w=this.au(h,o,y.Hr)]=h[w],x[w].segLinks.splice(x[w].segLinks.indexOf(l.Vi),1,y.Vi),(y=new Nn).setNode(p),l.Hr=y.Vi,l.Qr=Object.assign({},y.zt),l.$r[0]=Object.assign({},y.zt),l.ue(),h[w].segLinks.splice(h[w].segLinks.indexOf(l.Vi),1),h.push(y),_[w]=2):3!=f[O]&&7!=f[O]||(p=null,p=3==f[O]?i.wt(l.Qr,l.Jr):Object.assign({},l.Qr),(y=new Nn).Vi=this.Je(),y.Pr=xn.NaviNodeType_COMMON,y.Fr=xn.NaviLiftType_NULL,y.entranceType=xn.NaviEntranceType_ACCESS,y.zt=Object.assign({},p),(p=new Cn).setRoad(l),p.Vi=this.$e(),b.push(p.Vi),y.segLinks=b,(b=new Nn).setNode(y),v==xn.NaviRoadEntry_FORWARD?b.entranceType=xn.NaviEntranceType_ENTRANCE:v==xn.NaviRoadEntry_BACK&&(b.entranceType=xn.NaviEntranceType_EXIT),i.be.push(b),w=this.au(h,o,p.Hr),p.Qr=Object.assign({},b.zt),p.$r[0]=Object.assign({},b.zt),p.Hr=b.Vi,p.ue(),m[O]=p,x[w]=b,x[w=this.au(h,o,p.Kr)]=h[w],x[w].segLinks.splice(x[w].segLinks.indexOf(l.Vi),1,p.Vi),(p=new Nn).setNode(y),l.Kr=p.Vi,l.Jr=Object.assign({},p.zt),l.$r[1]=Object.assign({},p.zt),l.ue(),h[w].segLinks.splice(h[w].segLinks.indexOf(l.Vi),1),h.push(p),_[w]=2));for(var M,j=0,k=g;j<k;j++)1==f[j]&&((M=u[j])&&(m[j]=M,2!=_[w=this.au(h,o,M.Hr)]&&(x[w]=h[w],_[w]=1),2!=_[w=this.au(h,o,M.Kr)]&&(x[w]=h[w],_[w]=1),u[j]=null));for(var R=0;R<o;R++)1==_[R]&&(h[R]=null);for(var g=new Qn(x,m,!0,1),T=[],A=t.Ae,S=0;S<A.length;S++){var N=A[S];N&&this.cu(N,i)&&T.push(N)}return(t=g.Ae).push.apply(t,T),g}},Ro:function(){var t,i=ur(this.io);try{for(i.s();!(t=i.n()).done;)for(var n=t.value,r=n[0],e=n[1].Ae,s=0;s<e.length;s++){var o,u=e[s];xn.NaviModelPassType_PASS_NOT_THROUGH==u.pass&&((o=this.ao.get(r))||(o=[],this.ao.set(r,o)),o.push(u))}}catch(t){i.e(t)}finally{i.f()}},To:function(){var t,i=ur(this.io);try{for(i.s();!(t=i.n()).done;)for(var n=t.value,r=n[0],e=n[1].Ae,s=0;s<e.length;s++){var o,u=e[s];xn.NaviModelPassType_NOT_PASS==u.pass&&((o=this.fo.get(r))||(o=[],this.fo.set(r,o)),o.push(u))}}catch(t){i.e(t)}finally{i.f()}},Ao:function(){var t,i=ur(this.ao);try{for(i.s();!(t=i.n()).done;)for(var n=t.value,r=n[0],e=n[1],s=this.Qo(r).Te,o=0;o<e.length;o++){for(var u=e[o],h=0;h<s.length;h++){var a,f=s[h].ae();pt.ct(f,u.fe,u.ce)&&(s[h].pr=s[h].pr*this.po,(a=this.co.get(u.Vi))||(a=[],this.co.set(u.Vi,a)),a.push(s[h]))}this.Go(r,u,xn.NaviRoadHinderType_HINDER_HIGH)}}catch(t){i.e(t)}finally{i.f()}},So:function(){var t,i=ur(this.fo);try{for(i.s();!(t=i.n()).done;)for(var n=t.value,r=n[0],e=n[1],s=this.Qo(r).Te,o=0;o<e.length;o++)for(var u=e[o],h=0;h<s.length;h++){var a=s[h],f=a.ee,c=a.ae();if(pt.ct(c,u.fe,u.ce)){if(a.pr=Number.MAX_VALUE,f==xn.NaviRoadEntry_BOTH||f==xn.NaviRoadEntry_FORWARD){var l=this.Qo(r).De.get(a.Hr);if(l)for(var v=0;v<l.length;v++)l[v].us==a.Kr&&(l[v].pr=a.pr)}if(a.ee==xn.NaviRoadEntry_BOTH||a.ee==xn.NaviRoadEntry_BACK){var d=this.Qo(r).De.get(a.Kr);if(d)for(var y=0;y<d.length;y++)d[y].us==a.Hr&&(d[y].pr=a.pr)}}}}catch(t){i.e(t)}finally{i.f()}},Go:function(t,i,n){var r=this.co.get(i.Vi);if(r){if(xn.NaviRoadHinderType_HINDER_HIGH==n)for(var e=0;e<r.length;e++){var s=r[e];if(s.ue(),s.pr=r.length*this.po,s.ee==xn.NaviRoadEntry_BOTH||s.ee==xn.NaviRoadEntry_FORWARD){var o=this.Qo(t).De.get(s.Hr);if(o)for(var u=0;u<o.length;u++)o[u].us==s.Kr&&(o[u].pr=s.pr)}if(s.ee==xn.NaviRoadEntry_BOTH||s.ee==xn.NaviRoadEntry_BACK){var h=this.Qo(t).De.get(s.Kr);if(h)for(var a=0;a<h.length;a++)h[a].us==s.Hr&&(h[a].pr=s.pr)}}if(xn.NaviRoadHinderType_HINDER_GENERAL==n)for(var f=0;f<r.length;f++){var c=r[f];if(c.ue(),c.ee==xn.NaviRoadEntry_BOTH||c.ee==xn.NaviRoadEntry_FORWARD){var l=this.Qo(t).De.get(c.Hr);if(l)for(var v=0;v<l.length;v++)l[v].us==c.Kr&&(l[v].pr=c.pr)}if(c.ee==xn.NaviRoadEntry_BOTH||c.ee==xn.NaviRoadEntry_BACK){var d=this.Qo(t).De.get(c.Kr);if(d)for(var y=0;y<d.length;y++)d[y].us==c.Hr&&(d[y].pr=c.pr)}}}},hu:function(t,i,n){var r=this.ks.Ye(i);if(null==r)return null;for(var e=this.Qo(i).Ke(t,void 0),s=(e.area,Number.MAX_VALUE),o=null,u=0;u<r.be.length;u++){var h,a=r.be[u],f=pt.vt(a.zt,t);a.entranceType!=xn.NaviEntranceType_ACCESS&&a.entranceType!=n||f<s&&((h=this.lu(i,a.zt))!=e&&h.pass==xn.NaviModelPassType_PASS_NOT_THROUGH||(s=f,o=a))}return o},lu:function(t,i){for(var n=null,t=this.Qo(t),r=Number.MAX_VALUE,e=Number.MAX_VALUE,s=null,o=t.Ae,u=0;u<o.length;u++){var h,a=o[u];(h=pt.vu(a.fe,i,a.fe.length-1))<e&&(e=h,s=a),this.wo>h&&((h=a.area)<r&&(r=h,n=a))}return null==n?s:n},cu:function(t,i){if(t.bb.isSeparate(i.bb))return!1;t=t.Ut;return!!pt.ct(t,i.fe,i.ce)},nu:function(t,i,n){var r=this._o.find(function(t){return t==n.Hr}),e=this._o.find(function(t){return t==n.Kr});null==r&&this._o.push(n.Hr),null==e&&this._o.push(n.Kr);for(var s=this.bo,o=null,u=0;u<s.length;u++)if(s[u].du==n){o=s[u];break}if(null==o)(o={}).du=n,s.push(o);else for(;null!=o.yu;)var h=o.yu.du,o=pt.at(i,h.Qr,h.Jr)?o.yu:o.bu;n=o.du;var a=new Nn;a.Vi=this.qo(),a.zt=i;var f=new Cn;f.Vi=this.qo(),f.Hr=n.Hr,f.Qr=n.Qr,f.Kr=a.Vi,f.Jr=a.zt,f.$r=[n.Qr,a.zt],f.re=n.re;var c=new Cn;c.Vi=this.qo(),c.Hr=a.Vi,c.Qr=a.zt,c.Kr=n.Kr,c.Jr=n.Jr,c.$r=[a.zt,n.Jr],c.re=n.re,this.do.push(f),this.do.push(c);r={};r.du=f;e={};e.du=c,o.yu=r,o.bu=e,a.tempType=1,f.tempType=1,c.tempType=1,f.ue(),c.ue(),f.ee=n.ee,c.ee=n.ee;e=this.Qo(t);e.Re.push(a),e.Te.push(f),e.Te.push(c);var l,t=this.uu(t).ks;return t.Fe.set(a.Vi,e.Re.length-1),n.ee==xn.NaviRoadEntry_BOTH&&((e=[]).push({us:n.Hr,pr:f.pr}),e.push({us:n.Kr,pr:c.pr}),t.De.set(a.Vi,e),(e=t.De.get(n.Hr))||t.De.set(n.Hr,e=[]),e.push({us:a.Vi,pr:f.pr}),(e=t.De.get(n.Kr))||t.De.set(n.Kr,e=[]),e.push({us:a.Vi,pr:c.pr})),n.ee==xn.NaviRoadEntry_FORWARD&&((l=[]).push({us:n.Kr,pr:c.pr}),t.De.set(a.Vi,l),(l=t.De.get(n.Hr))||t.De.set(n.Hr,l=[]),l.push({us:a.Vi,pr:f.pr})),n.ee==xn.NaviRoadEntry_BACK&&((l=[]).push({us:n.Hr,pr:f.pr}),t.De.set(a.Vi,l),(l=t.De.get(n.Kr))||t.De.set(n.Kr,l=[]),l.push({us:a.Vi,pr:c.pr})),a},pu:function(t,i){for(var n=i.length-1;0<=n;n--)if(i[n].Vi&&0<i[n].Vi)return i.splice(n+1,0,t),n+1;return-1},Ko:function(t,i,n,r){if(r)return this.nu(t,i,n);var e=this.Qo(t),s=new Nn;s.Vi=this.Je(),s.zt=i;var o=new Cn;o.Vi=this.$e(),o.Hr=n.Hr,o.Qr=n.Qr,o.Kr=s.Vi,o.Jr=s.zt,o.re=n.re,o.$r=[n.Qr,s.zt];r=new Cn;r.Vi=this.$e(),r.Hr=s.Vi,r.Qr=s.zt,r.Kr=n.Kr,r.Jr=n.Jr,r.re=n.re,r.$r=[s.zt,n.Jr];t=[];t.push(o.Vi),t.push(r.Vi),s.segLinks=t,o.ue(),r.ue(),o.ee=n.ee,r.ee=n.ee;var u,i=e.Re,t=e.Te,i=this.pu(s,i);return this.pu(o,t),this.pu(r,t),e.Fe.set(s.Vi,i),n.ee==xn.NaviRoadEntry_BOTH&&((i=[]).push({us:n.Hr,pr:o.pr}),i.push({us:n.Kr,pr:r.pr}),e.De.set(s.Vi,i),(i=e.De.get(n.Hr))||e.De.set(n.Hr,i=[]),i.push({us:s.Vi,pr:o.pr}),(i=e.De.get(n.Kr))||e.De.set(n.Kr,i=[]),i.push({us:s.Vi,pr:r.pr})),n.ee==xn.NaviRoadEntry_FORWARD&&((u=[]).push({us:n.Kr,pr:r.pr}),e.De.set(s.Vi,u),(u=e.De.get(n.Hr))||e.De.set(n.Hr,u=[]),u.push({us:s.Vi,pr:o.pr})),n.ee==xn.NaviRoadEntry_BACK&&((u=[]).push({us:n.Hr,pr:o.pr}),e.De.set(s.Vi,u),(u=e.De.get(n.Kr))||e.De.set(n.Kr,u=[]),u.push({us:s.Vi,pr:r.pr})),s.Jo=[],s.Jo.push(o.Vi),s.Jo.push(r.Vi),s},xu:function(t){if(null!=t){for(var i=this.Qo(t),n=i.Re.length-1;0<=n;n--){var r=i.Re[n];if(null==r||0<=r.Vi)break;i.Re.splice(n,1)}for(var e=i.Te.length-1;0<=e;e--){var s=i.Te[e];if(null==s||0<=s.Vi)break;i.Te.splice(e,1)}}},mu:function(t,i){var n=t.get(i);if(n)for(var r=0;r<n.length;r++)n[r].us<0&&(n.splice(r,1),r--)},wu:function(t,i){for(var n=t.get(i),r=0;r<n.length;r++)this.mu(t,n[r].us);t.delete(i)},_u:function(t,i,n,r,e){r.gu=Number.MAX_VALUE,r.Qs=!1,r.eu=n;for(var s=Number.MAX_VALUE,o={},u=this.Qo(t),h=Number.MAX_VALUE,a=0;a<u.Te.length;a++){var f,c=u.Te[a];null!=c&&(c.Vi<0||(c.ee!=xn.NaviRoadEntry_FORBID?!pt._t(i,n,c.Qr,c.Jr,o)||(f=pt.vt(i,o))<s&&(s=f,r.st=Object.assign({},o),r.vt=s,r.qs=c,r.Qs=!0):!pt._t(i,n,c.Qr,c.Jr,o)||(c=pt.vt(i,o))<=h&&(h=c)))}if(r.vt>=h)return r.vt=Number.MAX_VALUE,r.Qs=!1;if(!r.Qs)return!1;for(var l={},v=0;v<u.Ae.length;v++){var d=u.Ae[v];if(null!=d&&d!=e&&(xn.NaviModelPassType_DECORATE!=d.pass&&(null==e||e.pass==xn.NaviModelPassType_DECORATE||!pt.ct(e.Ut,d.fe,d.fe.length-1))))for(var y=d.fe.length,b=0;b<y-1;b++)if(pt._t(i,n,d.fe[b],d.fe[b+1],l)){var p=pt.vt(i,l);if(p<s)return s=p,r.Hs=d,r.vt=Number.MAX_VALUE,r.Qs=!1,r.Ks=xn.NaviObstructType_MODEL,!1}}for(var x={},m=u.Se,w=0;w<m.length;w++){for(var _=m[w],g=m[w].we,O=0;O<g.length;O++)for(var E=g[O],M=0;M<E.length-1;M++)if(pt._t(i,n,E[M],E[M+1],x)){var j=pt.vt(i,x);if(j<s)return s=j,r.Hs=_,r.vt=Number.MAX_VALUE,r.Qs=!1,r.Ks=xn.NaviObstructType_EXTENT,!1}for(var k=m[w].fe,R=0;R<k.length-1;R++)if(pt._t(i,n,k[R],k[R+1],x)){var T=pt.vt(i,x);if(T<s)return s=T,r.Hs=_,r.vt=Number.MAX_VALUE,r.Qs=!1,r.Ks=xn.NaviObstructType_EXTENT,!1}}return!0},Ou:function(t,i){return t.vt-i.vt},Eu:function(t,i){return i.eu-t.eu},Uo:function(t){t=this.Qo(t);if(t){for(var i=t.Re,n=t.Fe,r=i.length-1;0<=r;r--){var e=i[r];if(null==e||0<=e.Vi)break;n.delete(e.Vi),i.splice(r,1)}for(var s=t.Te,o=t.Ce,u=s.length-1;0<=u;u--){var h=s[u];if(null==h||0<=h.Vi)break;o.delete(h.Vi),s.splice(u,1)}}},Bo:function(){this.Mu(this.eo),this.Mu(this.ro)},Mu:function(t){var i,n=ur(t);try{for(n.s();!(i=n.n()).done;){var r,e=i.value[1],s=[],o=e.ks.De,u=ur(o);try{for(u.s();!(r=u.n()).done;){var h=r.value;h[0]<0&&s.push(h[0])}}catch(t){u.e(t)}finally{u.f()}for(var a=0;a<s.length;a++)this.wu(e.ks.De,s[a]);for(var f=0;f<this._o.length;f++)this.mu(o,this._o[f])}}catch(t){n.e(t)}finally{n.f()}},iu:function(t,i,n,r,e){var s=[],o={x:0,y:0};if(pt.Ot(i,n.Qr,n.Jr,o)&&this.ju(i,o,{},e)){e={};return e.st=o,e.Qs=!0,e.qs=n,e.eu=0,e.vt=pt.vt(i,o),s.push(e),s}for(var u={x:o.x-i.x,y:o.y-i.y},h=0;h<r.length;h++){var a=r[h],f={x:a.st.x-i.x,y:a.st.y-i.y},f=pt.Tt(f,u);a.eu=f=270<f?f-360:f}r.sort(this.Eu);var c,l=null;if(r[r.length-1].eu<0)return l=r[r.length-1],s.push(l),s;for(var v=0;v<r.length;v++)if(0<r[v].eu)return l=r[v],0==v?s.push(l):(c=r[v-1],pt.vt(l.st,n.Qr)>pt.vt(c.st,n.Qr)?(s.push(c),s.push(l)):(s.push(l),s.push(c))),s;return s},ku:function(t,i,n,r,e){for(var s=r||30,o=0;o<360;o+=s){var u=new or;this._u(t,i,o,u,e),1==u.Qs&&n.push(u)}n.sort(this.Ou)},tu:function(t,i,n,r){var e=new Map,s=[];for(this.ku(t,i,s,n,r);0!=s.length;){var o=s[0],u=e.get(o.qs.Vi);u?u.push(o):((u=[]).push(o),e.set(o.qs.Vi,u)),s.splice(0,1)}return e},Co:function(t,i){var n=new Nn;n.Vi=this.qo(),n.zt=Object.assign({},i);i=this.Qo(t);return i.Re.push(n),i.Fe.set(n.Vi,i.Re.length-1),this.uu(t).ks=i,n},Ru:function(t,i,n){if(n)return this.Co(t,i);n=new Nn;n.Vi=this.Je(),n.zt=Object.assign({},i);i=this.Qo(t),t=i.Re,t=this.pu(n,t);return i.Fe.set(n.Vi,t),n},ju:function(t,i,n,r){for(var e,s=this.Qo(-1),o=0;o<s.Ae.length;o++)if(e=s.Ae[o],null!=e&&e!=r&&xn.NaviModelPassType_DECORATE!=e.pass)for(var u=e.fe,h=0;h<u.length-1;h++)if(pt.wt(t,i,u[h],u[h+1],n)){var a=pt.vt(t,n),f=pt.vt(i,n);if(!(a<=pt.$()||f<=pt.$()))return!1}for(var c=s.Se,l=0;l<c.length;l++){for(var v=c[l],d=v.we,y=0;y<d.length;y++)for(var b=d[y],p=0;p<b.length-1;p++)if(pt.wt(t,i,b[p],b[p+1],n))return!1;for(var x=v.fe,m=0;m<x.length-1;m++)if(pt.wt(t,i,x[m],x[m+1],n))return!1}return!0},ru:function(t,i,n,r,e,s){for(var o=0;o<this.yo.length;o++){var u=this.yo[o],h={};if(1==pt.wt(u.Qr,u.Jr,i.zt,n.zt,h)&&pt.vt(i.zt,h)>pt.$()&&pt.vt(n.zt,h)>pt.$())return null}void 0===r&&(r=xn.NaviRoadEntry_BOTH),void 0===e&&(e=!0),void 0===s&&(s=!0);var a=new Cn;a.Hr=i.Vi,a.Qr=i.zt,a.Kr=n.Vi,a.Jr=n.zt,a.$r=[i.zt,n.zt],a.re=1,this._o.push(i.Vi),this._o.push(n.Vi),a.Vi=e?this.qo():this.$e(),a.ee=r,a.ue(),s&&this.yo.push(a),this.Qo(t).Te.push(a);var f,c,s=this.uu(t);return xn.NaviRoadEntry_BOTH==r&&(t={us:n.Vi,pr:a.pr},(f=s.ks.De.get(i.Vi))?f.push(t):((f=[]).push(t),s.ks.De.set(i.Vi,f)),t={us:i.Vi,pr:a.pr},(f=s.ks.De.get(n.Vi))?f.push(t):((f=[]).push(t),s.ks.De.set(n.Vi,f))),xn.NaviRoadEntry_FORWARD==r&&(f={us:n.Vi,pr:a.pr},(c=s.ks.De.get(i.Vi))?c.push(f):((c=[]).push(f),s.ks.De(i.Vi,c))),xn.NaviRoadEntry_BACK==r&&(c={us:i.Vi,pr:a.pr},(r=s.ks.De.get(n.Vi))?r.push(c):((r=[]).push(c),s.ks.De.set(n.Vi,r))),a},ou:function(t){var i=this.No(t),n=this.ao.get(i);if(n)for(var r=0;r<n.length;r++){var e=n[r];if(pt.ct(t,e.fe,e.ce))return e}return null},Qo:function(t){var i=null;return i=(i=this.Mo?this.no.get(t):i)||this.io.get(t)},uu:function(t){var i=null;return i=(i=this.Mo?this.eo.get(t):i)||this.ro.get(t)},Tu:function(){return this.Mo?this.eo:this.ro},Je:function(){return this.ks.Je()},$e:function(){return this.ks.$e()},Yo:function(t){var i=[];i.push.apply(i,hr(t.Re));var n=[];n.push.apply(n,hr(t.Te));i=new Qn(i,n,!0,1);return(n=i.Ae).push.apply(n,hr(t.Ae)),i},Vo:function(t){for(var i=[-1],n=this.ks.Ie,r=0;r<n.length;r++){var e=n[r];t.Au(e.Zt.Lt,e.Zt.Dt)&&i.push(e.Vi)}return i},su:function(t,i,n){var r=null,e=i.Te;if(!e||e.length<=0)return r;var s=i.Ze(t);if(s<=0)return r;var o,u,h={},r={distance:Number.MAX_VALUE,road:null,coords:{level:t.level}},a=ur(e);try{for(a.s();!(u=a.n()).done;){var f=u.value;f.ee!==xn.NaviRoadEntry_FORBID&&s===f.te&&(n&&n.indexOf(f.re)<0||(o=pt.bt(t,f.Qr,f.Jr,h))<r.distance&&(r.distance=o,r.road=f,Object.assign(r.coords,h)))}}catch(t){a.e(t)}finally{a.f()}return r}});var lr=s;function vr(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}function dr(t){if(vr(this,dr),t instanceof Number){this.Su=t,this.Nu=[];for(var i=0;i<t;i++)this.Nu.push(null)}if(t instanceof Array){this.Iu=t,this.Nu=[];for(var n=0;n<t.length;n++)this.Nu.push(null)}}Object.assign(dr.prototype,{Lu:function(){return this.Nu.length},Du:function(t,i){if(t<0||t>=this.Lu())throw"positon is out of bounder";this.Nu[t]=i},Pu:function(t){var i=t;return"string"==typeof t&&(i=this.Iu.indexOf(t)),this.Nu[i]},Fu:function(){for(var t=[],i=0;i<this.Nu.length;i++)t.push(this.Nu[i]);return t},Gr:function(t){if(this.Nu.length!=t.Nu.length)return!1;if(this.Iu.length!=t.Iu.length)return!1;for(var i=0;i<this.Iu.length;i++)if(this.Iu[i]!=t.Iu[i])return!1;return!0},Cu:function(){for(var t=0==this.Iu.length?new dr(this.Nu.length):new dr(this.Iu),i=0;i<this.Nu.length;i++)t.Nu[i]=this.Nu[i];return t}});function yr(t){vr(this,yr),this.Iu=t,this.Uu=[]}Object.assign(yr.prototype,{Bu:function(t){if(t instanceof dr){if(!new dr(t.Iu).Gr(t))throw"the appended item is not compatible to the TupleTable";this.Uu.push(t)}else{var i,n=new dr(this.Iu);for(i in t){var r=this.Iu.indexOf(i);0<=r&&n.Du(r,t[i])}this.Uu.push(n)}},or:function(t,i){for(var n=0;n<this.Uu.length;n++){var r=this.Uu[n];if(r.Pu(t)==i)return r}return null},Gu:function(t,i){i=i||-1;for(var n=0;n<this.Uu.length;n++)t(i<0?this.Uu[n]:this.Uu[n].Pu(i))},Lu:function(){return this.Uu.length},Cu:function(){for(var t=new yr(this.Iu),i=0;i<this.Uu.length;i++)t.Bu(this.Uu[i].Cu());return t},Zu:function(t){for(var i=0;i<this.Uu.length;i++){var n,r=!0;for(n in t)if(this.Uu[i].Pu(n)!=t[n]){r=!1;break}r&&(this.Uu.splice(i,1),i--)}},Wu:function(){this.Uu=[]}});var br=yr;function pr(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}Object.assign(function t(){pr(this,t),this.items=[]}.prototype,{Vu:function(t){this.items.push(t)},Yu:function(){return this.isEmpty()?"Underflow":this.items.shift()},Xu:function(){return this.isEmpty()?"No elements in Queue":this.items[0]},zu:function(){return 0==this.items.length},qu:function(){for(var t="",i=0;i<this.items.length;i++)t+=this.items[i]+" ";return t}});var xr=function t(){pr(this,t),this.queue=[],this.offset=0};function mr(t){return function(t){if(Array.isArray(t))return gr(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||_r(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wr(t,i){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=_r(t))||i&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var e,s=!0,o=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){o=!0,e=t},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw e}}}}function _r(t,i){if(t){if("string"==typeof t)return gr(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?gr(t,i):void 0}}function gr(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}function Or(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}Object.assign(xr.prototype,{Hu:function(){return this.queue.length-this.offset},zu:function(){return 0==this.queue.length},Vu:function(t){this.queue.push(t)},Yu:function(){if(0!=this.queue.length){var t=this.queue[this.offset];return 2*++this.offset>=this.queue.length&&(this.queue=this.queue.slice(this.offset),this.offset=0),t}},Qu:function(){return 0<this.queue.length?this.queue[this.offset]:void 0}});E=function(){function n(t,i){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,n),this.Vi=t,this.Js=null,this.$s=null,this.ks=i,this.to=new Map,this.io=new Map,this.Ku=null,this.Ju=new Map,this.so=[],this.oo=Number.INT_MIN,this.uo=Number.INT_MIN,this.ho=-1,this.Rs=null,this.Ts=null,this.Ns=g.MODULE_SHORTEST,this.ao=new Map,this.fo=new Map,this.co=new Map,this.vo=[],this.do=[],this.yo=[],this.bo=[],this.po=100,this.xo=-1,this.mo=15,this.wo=.1,this.$u=[],this.th=[],this.ih=[],this.ko(),this.Ro(),this.To(),this.Ao(),this.So(),this.nh=new br(["num","id","idx","node"]),this.rh=new Map,this.eh=new xr,this.Lr=!1,this.sh=[],this.Ht=null}var t,i,r;return t=n,(i=[{key:"level",get:function(){return this.br},set:function(t){this.br=t}}])&&Or(t.prototype,i),r&&Or(t,r),n}();Object.assign(E.prototype,{oh:function(t){var i,n=null==t.uh?null:t.uh;t instanceof Nn?(this.Rs=t,this.nh.Bu({num:this.nh.Lu(),id:t.Vi,idx:this.ks.hs(t.Vi),node:t})):(i=new Nn,this.hh(t,i,n)||this.th.push(t),this.Rs=i,this.nh.Bu({num:this.nh.Lu(),id:i.Vi,idx:this.ks.hs(i.Vi),node:i}))},ah:function(t){var i,n=null==t.uh?null:t.uh;t instanceof Nn?(this.Ts=t,this.nh.Bu({num:this.nh.Lu(),id:t.Vi,idx:this.ks.hs(t.Vi),node:t})):(i=new Nn,this.hh(t,i,n)||this.th.push(t),this.Ts=i,this.nh.Bu({num:this.nh.Lu(),id:i.Vi,idx:this.ks.hs(i.Vi),node:i}))},fh:function(t){var i=this.ks.Se[0].fe;return!!pt.ct(t,i,i.length-1)},Zs:function(){return this.so},ei:function(){this.Js=null,this.$s=null,this.ks.ei(),this.ks=null,this.to.clear(),this.to=null;var t,i=wr(this.io);try{for(i.s();!(t=i.n()).done;)t.value[1].ei()}catch(t){i.e(t)}finally{i.f()}this.io.clear(),(this.io=null)!=this.Ku&&(this.Ku.ei(),this.Ku=null),this.Ju.each(function(t,i){i.ei()}),this.Ju.clear(),this.Ju=null,this.so=[],this.Rs=null,this.Ts=null,this.ao.clear(),this.ao=null,this.fo.clear(),this.fo=null,this.co.clear(),this.fo=null,this.vo=[],this.do=[],this.yo=[],this.bo=[]},Un:function(){this.so=[],this.Uo(-1),this.Uo(this.oo),this.oo=Number.INT_MIN,this.Uo(this.uo),this.uo=Number.INT_MIN,this.Bo(),this.$u=[],this.ho=-1;for(var t=0;t<this.vo.length;t++){var i=this.vo[t];this.Go(i.Zo,i.Hs,xn.NaviRoadHinderType_HINDER_HIGH)}this.vo=[],this.do=[],this.yo=[],this.bo=[]},Ho:function(t){var i={te:-1,Hs:-1},n=this.ks.We(t);if(n<=0)return i;if(i.te=n,1===this.Ht)return i.Hs=0,i;var r=this.No(t),e=this.Qo(r).Ke(t,-0);if(!e)return i.Hs=0,i;if((i.Hs=e).pass==xn.NaviModelPassType_NOT_PASS)return i.Hs=-1,i;if(e.pass===xn.NaviModelPassType_PASS_THROUGH||e.pass===xn.NaviModelPassType_PASS_NOT_THROUGH){if(0<e._e.length)return i;for(var s={},o=[],u=[],h=this.Qo(r).Te,a=!0,f=0;f<e.ce;f++)for(var c=e.fe[f],l=e.fe[f+1],v=0;v<h.length;v++){var d=h[v];1===pt.wt(c,l,d.Qr,d.Jr,s)&&xn.NaviRoadEntry_FORBID!==d.ee&&(a=!1,(d={}).x=s.x,d.y=s.y,o.push(d),u.push(h[v]))}if(!a){e._e=[],e.Oe=[],e.ge=[];for(var y=0;y<o.length;y++){var b=o[y],p=u[y],b=this.Ko(r,b,p);b.Jo.push(p.Vi),b.Pr=xn.NaviNodeType_COMMON,p.ee===xn.NaviRoadEntry_BOTH?b.entranceType=xn.NaviEntranceType_ACCESS:pt.ct(p.Qr,e.fe,e.ce)?(p.ee===xn.NaviRoadEntry_FORWARD&&(b.entranceType=xn.NaviEntranceType_EXIT),p.ee===xn.NaviRoadEntry_BACK&&(b.entranceType=xn.NaviEntranceType_ENTRANCE)):(p.ee===xn.NaviRoadEntry_FORWARD&&(b.entranceType=xn.NaviEntranceType_ENTRANCE),p.ee===xn.NaviRoadEntry_BACK&&(b.entranceType=xn.NaviEntranceType_EXIT)),e._e.push(b),e.Oe.push(b.entranceType),e.ge.push(b.Pr)}}}return i},$o:function(t){var i,n=null,r=Number.MAX_VALUE,e=wr(t.values());try{for(e.s();!(i=e.n()).done;){var s=i.value;0<s.length&&s[0].vt<r&&(n=s[0],r=s[0].vt)}}catch(t){e.e(t)}finally{e.f()}return n},Lo:function(t){var i=this;if(1!==this.Ht){var n=null,r=!0,n=null!=this.Js&&this.Js.pass==xn.NaviModelPassType_PASS_THROUGH&&0==this.Js._e.length?this.tu(this.oo,t,30,this.Js):this.tu(this.oo,t);if(this.Rs=this.Co(this.oo,t),null==this.Js&&(r=!1),null!=n&&0<n.size){var e,s=!1,o=[],r=!1,u=wr(n);try{for(u.s();!(e=u.n()).done;)for(var h=e.value[1],a=0;a<h.length;a++)0!=h[a].qs.ee&&(r=!0)}catch(t){u.e(t)}finally{u.f()}var f,c=wr(n);try{for(c.s();!(f=c.n()).done;){var l,v=f.value[1],d=v[0].qs,y=this.iu(this.oo,t,d,v,this.Js);if(0==y.length){var b,p=wr(v);try{for(p.s();!(b=p.n()).done;){var x=b.value,m=this.nu(this.oo,x.st,x.qs);this.ru(this.oo,this.Rs,m)}}catch(t){p.e(t)}finally{p.f()}}else Math.abs(y[0].eu)>pt.$()?o.push.apply(o,mr(y)):(s=!0,l=this.nu(this.oo,y[0].st,y[0].qs),this.ru(this.oo,this.Rs,l))}}catch(t){c.e(t)}finally{c.f()}s||o.forEach(function(t){t=i.nu(i.uo,t.st,t.qs);i.ru(i.oo,i.Rs,t)})}if(r&&null!=this.Js&&0<this.Js._e.length)for(var w=null,_=this.io.get(this.oo),g=0;g<this.Js._e.length;g++){var w=this.Js._e[g],O=this.Js.Oe[g];if(O==xn.NaviEntranceType_EXIT||O==xn.NaviEntranceType_ACCESS){for(var E=this.Js.we,M=!0,j=0;j<E.length;j++){for(var k=E[j],R=0;R<k.length-1;R++)if(pt.ut(t,w.zt,k[R],k[R+1])){M=!1;break}if(0==M)break}if(0!=M){for(var T=0;T<_.Te.length;T++){var A=_.Te[T];if(0!=A.ee)if(pt.ut(t,w.zt,A.Qr,A.Jr)&&0<A.Vi&&0==w.Jo.includes(A.Vi)){M=!1;break}}0!=M&&this.ru(this.oo,this.Rs,w)}}}}else{n=this.io.get(this.oo),n=this.su(t,n);if(!n.road)return S.ROUTE_FAILED_NO_START_ARRIVAL;n=this.nu(this.oo,n.coords,n.road);this.Rs=n}},Do:function(t){var i=this;if(1!==this.Ht){var n=null,r=!0,n=null!=this.$s&&this.$s.pass==xn.NaviModelPassType_PASS_THROUGH&&0==this.$s._e.length?this.tu(this.uo,t,30,this.$s):this.tu(this.uo,t);if(this.Ts=this.Co(this.uo,t),null==this.$s&&(r=!1),null!=n&&0<n.size){var e,s=!1,o=[],r=!1,u=wr(n);try{for(u.s();!(e=u.n()).done;)for(var h=e.value[1],a=0;a<h.length;a++)0!=h[a].qs.ee&&(r=!0)}catch(t){u.e(t)}finally{u.f()}var f,c=wr(n);try{for(c.s();!(f=c.n()).done;){var l,v=f.value[1],d=v[0].qs,y=this.iu(this.uo,t,d,v,this.$s);if(0==y.length){var b,p=wr(v);try{for(p.s();!(b=p.n()).done;){var x=b.value,m=this.nu(this.uo,x.st,x.qs);this.ru(this.uo,this.Ts,m)}}catch(t){p.e(t)}finally{p.f()}}else Math.abs(y[0].eu)>pt.$()?o.push.apply(o,mr(y)):(s=!0,l=this.nu(this.uo,y[0].st,y[0].qs),this.ru(this.uo,this.Ts,l))}}catch(t){c.e(t)}finally{c.f()}s||o.forEach(function(t){t=i.nu(i.uo,t.st,t.qs);i.ru(i.uo,i.Ts,t)})}if(r&&null!=this.$s&&0<this.$s._e.length)for(var w=null,_=this.io.get(this.uo),g=0;g<this.$s._e.length;g++){var w=this.$s._e[g],O=this.$s.Oe[g];if(O==xn.NaviEntranceType_ENTRANCE||O==xn.NaviEntranceType_ACCESS){for(var E=this.$s.we,M=!0,j=0;j<E.length;j++){for(var k=E[j],R=0;R<k.length-1;R++)if(pt.ut(t,w.zt,k[R],k[R+1])){M=!1;break}if(0==M)break}if(0!=M){for(var T=0;T<_.Te.length;T++){var A=_.Te[T];if(0!=A.ee)if(pt.ut(t,w.zt,A.Qr,A.Jr)&&0<A.Vi&&0==w.Jo.includes(A.Vi)){M=!1;break}}0!=M&&this.ru(this.uo,this.Ts,w)}}}}else{n=this.io.get(this.uo),n=this.su(t,n);if(!n.road)return S.ROUTE_FAILED_NO_DEST_ARRIVAL;n=this.nu(this.uo,n.coords,n.road);this.Ts=n}},hh:function(t,i,n){var r=this;t instanceof Array&&2==t.length&&(t={x:t[0],y:t[1]});var e=null,s=!0,o=this.ks.ds(t),u=this.ks.ys(t),e=null!=o&&o.pass==xn.NaviModelPassType_PASS_THROUGH&&0==o._e.length?this.tu(u,t,30,o):this.tu(u,t);if(1===this.Ht){var h=this.$o(e);if(!h)return;var a=h.qs,f=null,a=this.iu(u,t,a,[h],o);return(f=0==a.length?this.nu(u,h.st,h.qs):this.nu(u,a[0].st,a[0].qs)).uh=void 0!==n&&n,n&&(Object.assign(t,f.zt),f.zt=t),Object.assign(i,f),!0}f=this.Co(u,t);if(f.uh=void 0!==n&&n,Object.assign(i,f),null==o&&(s=!1),0<e.size){var c,s=!1,l=wr(e);try{for(l.s();!(c=l.n()).done;)for(var v=c.value[1],d=0;d<v.length;d++)0!=v[d].qs.ee&&(s=!0)}catch(t){l.e(t)}finally{l.f()}var y,b=!1,p=[],x=wr(e);try{for(x.s();!(y=x.n()).done;){var m,w=y.value[1],_=w[0].qs,g=this.iu(u,t,_,w,o);if(0==g.length){var O,E=wr(w);try{for(E.s();!(O=E.n()).done;){var M=O.value,j=this.nu(u,M.st,M.qs);this.ru(u,i,j)}}catch(t){E.e(t)}finally{E.f()}}else Math.abs(g[0].eu)>pt.$()?p.push.apply(p,mr(g)):(b=!0,m=this.nu(u,g[0].st,g[0].qs),this.ru(u,i,m))}}catch(t){x.e(t)}finally{x.f()}b||p.forEach(function(t){t=r.nu(u,t.st,t.qs);r.ru(u,i,t)})}if(s&&null!=o&&0<o._e.length){for(var k=!1,R=0;R<o._e.length;R++){var T=o._e[R],A=o.Oe[R];A!=xn.NaviEntranceType_EXIT&&A!=xn.NaviEntranceType_ACCESS||(this.ru(u,i,T),k=!0)}if(!k)return!1}return!0},Cs:function(){var t=this.ou(this.Rs.zt);t&&(this.Go(this.oo,t,xn.NaviRoadHinderType_HINDER_GENERAL),this.vo.push({Zo:this.oo,Hs:t}));t=this.ou(this.Ts.zt);if(t&&(this.Go(this.uo,t,xn.NaviRoadHinderType_HINDER_GENERAL),this.vo.push({Zo:this.uo,Hs:t})),this.so=[],this.oo==this.uo){var i=this.Ju.get(this.oo),n=i.Cs(this.Rs,this.Ts,this.Ns);if(n!=S.ROUTE_SUCCESS)return n;var r=i.Zs();this.so=[],r.reverse(),(n=this.so).push.apply(n,mr(r))}else{if(-1!=this.oo&&-1==this.uo){var i=this.hu(this.Rs.zt,this.oo,xn.NaviEntranceType_EXIT),n=this.Ju.get(this.oo),r=n.Cs(this.Rs,i,this.Ns);if(r!=S.ROUTE_SUCCESS)return r;var e=n.Zs(e),n=this.Ju.get(-1);if((r=n.Cs(i,this.Ts,this.Ns))!=S.ROUTE_SUCCESS)return r;r=n.Zs();e.reverse(),r.reverse(),(n=this.so).push.apply(n,mr(e)),(e=this.so).push.apply(e,mr(r))}if(-1==this.oo&&-1!=this.uo){var s=this.hu(this.Ts.zt,this.uo,xn.NaviEntranceType_ENTRANCE),o=this.Ju.get(this.uo),u=o.Cs(s,this.Ts,this.Ns);if(u!=S.ROUTE_SUCCESS)return u;var h=o.Zs(),o=this.Ju.get(-1);if((u=o.Cs(this.Rs,s,this.Ns))!=S.ROUTE_SUCCESS)return u;s=o.Zs();s.reverse(),h.reverse(),(u=this.so).push.apply(u,mr(s)),(o=this.so).push.apply(o,mr(h))}if(-1!=this.oo&&-1!=this.uo){var u=this.hu(this.Rs.zt,this.oo,xn.NaviEntranceType_EXIT),s=this.Ju.get(this.oo),o=s.Cs(this.Rs,u,this.Ns);if(o!=S.ROUTE_SUCCESS)return o;var a=s.Zs(a),h=this.hu(this.Ts.zt,this.uo,xn.NaviEntranceType_ENTRANCE),s=this.Ju.get(this.uo);if((o=s.Cs(h,this.Ts,this.Ns))!=S.ROUTE_SUCCESS)return o;var f=s.Zs(f),s=this.Ju.get(-1);if((o=s.Cs(u,h,this.Ns))!=S.ROUTE_SUCCESS)return o;o=s.Zs();a.reverse(),o.reverse(),f.reverse(),(s=this.so).push.apply(s,mr(a)),(a=this.so).push.apply(a,mr(o)),(o=this.so).push.apply(o,mr(f))}}return S.ROUTE_SUCCESS},au:function(t,i,n){for(var r=0;r<i;r++)if(null!=t[r]&&t[r].Vi==n)return r;return-1},ch:function(t){for(var i=this.ks.Ie,n=0;n<i.length;n++){var r=i[n];if(r.xe(t))return r.Vi}return-1},ko:function(){for(var t=this.ks.Ie,i=0;i<t.length;i++){var n=t[i],r=this.fu(this.ks,n);this.io.set(n.Vi,r)}this.io.set(-1,this.ks);var e,s=wr(this.io);try{for(s.s();!(e=s.n()).done;){var o=e.value;o[1].Ge(),this.Ju.set(o[0],new tr(o[1]))}}catch(t){s.e(t)}finally{s.f()}},fu:function(t,i){if(t&&i){for(var n,r=0,e=0,s=t.Te.length,o=t.Re.length,u=t.Te,h=t.Re,a=0,f=new Int8Array(s),c=0;c<s;c++)u[c]&&u[c].ee!=xn.NaviRoadEntry_FORBID&&(n=u[c],r=i.contain(n.Qr),e=i.contain(n.Jr),0==r&&0==e||(a=!0),1==r&&1==e&&(f[c]=1),1==r&&0==e&&(f[c]=2),0==r&&1==e&&(f[c]=3),0==r&&0==e&&(f[c]=4),2==r&&0==e&&(f[c]=6),2==r&&1==e&&(f[c]=7),0==r&&2==e&&(f[c]=8),1==r&&2==e&&(f[c]=9));if(0==a)return null;for(var l,v,d,y,b,p,x=new Array(o),m=new Array(s),w=0,_=new Int8Array(o),g=f.length,O=0,E=g;O<E;O++)0!=f[O]&&6!=f[O]&&8!=f[O]&&1!=f[O]&&u[O]&&(v=(l=u[O]).ee,b=[l.Vi],2==f[O]||9==f[O]?(d=null,d=2==f[O]?i.wt(l.Qr,l.Jr):Object.assign({},l.Jr),(p=new Nn).Vi=this.ks.Je(),p.Pr=xn.NaviNodeType_COMMON,p.Fr=xn.NaviLiftType_NULL,p.entranceType=xn.NaviEntranceType_ACCESS,p.zt=Object.assign({},d),(y=new Cn).setRoad(l),y.Vi=this.ks.$e(),b.push(y.Vi),p.segLinks=b,(d=new Nn).setNode(p),v==xn.NaviRoadEntry_FORWARD?d.entranceType=xn.NaviEntranceType_EXIT:v==xn.NaviRoadEntry_BACK&&(d.entranceType=xn.NaviEntranceType_ENTRANCE),i.be.push(d),w=this.au(h,o,y.Kr),y.Jr=Object.assign({},d.zt),y.$r[1]=Object.assign({},d.zt),y.Kr=d.Vi,y.ue(),m[O]=y,x[w]=d,x[w=this.au(h,o,y.Hr)]=h[w],x[w].segLinks.splice(x[w].segLinks.indexOf(l.Vi),1,y.Vi),(y=new Nn).setNode(p),l.Hr=y.Vi,l.Qr=Object.assign({},y.zt),l.$r[0]=Object.assign({},y.zt),l.ue(),h[w].segLinks.splice(h[w].segLinks.indexOf(l.Vi),1),h.push(y),_[w]=2):3!=f[O]&&7!=f[O]||(p=null,p=3==f[O]?i.wt(l.Qr,l.Jr):Object.assign({},l.Qr),(y=new Nn).Vi=this.ks.Je(),y.Pr=xn.NaviNodeType_COMMON,y.Fr=xn.NaviLiftType_NULL,y.entranceType=xn.NaviEntranceType_ACCESS,y.zt=Object.assign({},p),(p=new Cn).setRoad(l),p.Vi=this.ks.$e(),b.push(p.Vi),y.segLinks=b,(b=new Nn).setNode(y),v==xn.NaviRoadEntry_FORWARD?b.entranceType=xn.NaviEntranceType_ENTRANCE:v==xn.NaviRoadEntry_BACK&&(b.entranceType=xn.NaviEntranceType_EXIT),i.be.push(b),w=this.au(h,o,p.Hr),p.Qr=Object.assign({},b.zt),p.$r[0]=Object.assign({},b.zt),p.Hr=b.Vi,p.ue(),m[O]=p,x[w]=b,x[w=this.au(h,o,p.Kr)]=h[w],x[w].segLinks.splice(x[w].segLinks.indexOf(l.Vi),1,p.Vi),(p=new Nn).setNode(y),l.Kr=p.Vi,l.Jr=Object.assign({},p.zt),l.$r[1]=Object.assign({},p.zt),l.ue(),h[w].segLinks.splice(h[w].segLinks.indexOf(l.Vi),1),h.push(p),_[w]=2));for(var M,j=0,k=g;j<k;j++)1==f[j]&&((M=u[j])&&(m[j]=M,2!=_[w=this.au(h,o,M.Hr)]&&(x[w]=h[w],_[w]=1),2!=_[w=this.au(h,o,M.Kr)]&&(x[w]=h[w],_[w]=1),u[j]=null));for(var R=0;R<o;R++)1==_[R]&&(h[R]=null);for(var g=new Qn(x,m,!0,1),T=[],A=t.Ae,S=0;S<A.length;S++){var N=A[S];N&&this.cu(N,i)&&T.push(N)}return(t=g.Ae).push.apply(t,T),g}},Ro:function(){var t,i=wr(this.io);try{for(i.s();!(t=i.n()).done;)for(var n=t.value,r=n[0],e=n[1].Ae,s=0;s<e.length;s++){var o,u=e[s];xn.NaviModelPassType_PASS_NOT_THROUGH==u.pass&&((o=this.ao.get(r))||(o=[],this.ao.set(r,o)),o.push(u))}}catch(t){i.e(t)}finally{i.f()}},To:function(){var t,i=wr(this.io);try{for(i.s();!(t=i.n()).done;)for(var n=t.value,r=n[0],e=n[1].Ae,s=0;s<e.length;s++){var o,u=e[s];xn.NaviModelPassType_NOT_PASS==u.pass&&((o=this.fo.get(r))||(o=[],this.fo.set(r,o)),o.push(u))}}catch(t){i.e(t)}finally{i.f()}},Ao:function(){var t,i=wr(this.ao);try{for(i.s();!(t=i.n()).done;)for(var n=t.value,r=n[0],e=n[1],s=this.io.get(r).Te,o=0;o<e.length;o++){for(var u=e[o],h=0;h<s.length;h++){var a,f=s[h].ae();pt.ct(f,u.fe,u.ce)&&(s[h].pr=s[h].pr*this.po,(a=this.co.get(u.Vi))||(a=[],this.co.set(u.Vi,a)),a.push(s[h]))}this.Go(r,u,xn.NaviRoadHinderType_HINDER_HIGH)}}catch(t){i.e(t)}finally{i.f()}},So:function(){var t,i=wr(this.fo);try{for(i.s();!(t=i.n()).done;)for(var n=t.value,r=n[0],e=n[1],s=this.io.get(r).Te,o=0;o<e.length;o++)for(var u=e[o],h=0;h<s.length;h++){var a=s[h],f=a.ee,c=a.ae();if(pt.ct(c,u.fe,u.ce-1)){if(a.pr=Number.MAX_VALUE,f==xn.NaviRoadEntry_BOTH||f==xn.NaviRoadEntry_FORWARD){var l=this.io.get(r).De.get(a.Hr);if(l)for(var v=0;v<l.length;v++)l[v].us==a.Kr&&(l[v].pr=a.pr)}if(a.ee==xn.NaviRoadEntry_BOTH||a.ee==xn.NaviRoadEntry_BACK){var d=this.io.get(r).De.get(a.Kr);if(d)for(var y=0;y<d.length;y++)d[y].us==a.Hr&&(d[y].pr=a.pr)}}}}catch(t){i.e(t)}finally{i.f()}},Go:function(t,i,n){var r=this.co.get(i.Vi);if(r){if(xn.NaviRoadHinderType_HINDER_HIGH==n)for(var e=0;e<r.length;e++){var s=r[e];if(s.ue(),s.pr=r.length*this.po,s.ee==xn.NaviRoadEntry_BOTH||s.ee==xn.NaviRoadEntry_FORWARD){var o=this.io.get(t).De.get(s.Hr);if(o)for(var u=0;u<o.length;u++)o[u].us==s.Kr&&(o[u].pr=s.pr)}if(s.ee==xn.NaviRoadEntry_BOTH||s.ee==xn.NaviRoadEntry_BACK){var h=this.io.get(t).De.get(s.Kr);if(h)for(var a=0;a<h.length;a++)h[a].us==s.Hr&&(h[a].pr=s.pr)}}if(xn.NaviRoadHinderType_HINDER_GENERAL==n)for(var f=0;f<r.length;f++){var c=r[f];if(c.ue(),c.ee==xn.NaviRoadEntry_BOTH||c.ee==xn.NaviRoadEntry_FORWARD){var l=this.io.get(t).De.get(c.Hr);if(l)for(var v=0;v<l.length;v++)l[v].us==c.Kr&&(l[v].pr=c.pr)}if(c.ee==xn.NaviRoadEntry_BOTH||c.ee==xn.NaviRoadEntry_BACK){var d=this.io.get(t).De.get(c.Kr);if(d)for(var y=0;y<d.length;y++)d[y].us==c.Hr&&(d[y].pr=c.pr)}}}},hu:function(t,i,n){var r=this.ks.getZone(i);if(null==r)return null;for(var e=this.io.get(i).Ke(t,void 0),s=(e.area,Number.MAX_VALUE),o=null,u=0;u<r.be.length;u++){var h,a=r.be[u],f=pt.vt(a.zt,t);a.entranceType!=xn.NaviEntranceType_ACCESS&&a.entranceType!=n||f<s&&((h=this.lu(i,a.zt))!=e&&h.pass==xn.NaviModelPassType_PASS_NOT_THROUGH||(s=f,o=a))}return o},lu:function(t,i){for(var n=null,t=this.io.get(t),r=Number.MAX_VALUE,e=Number.MAX_VALUE,s=null,o=t.Ae,u=0;u<o.length;u++){var h,a=o[u];(h=pt.ct(i,a.fe,a.fe.length-1))<e&&(e=h,s=a),this.wo>h&&((h=a.area)<r&&(r=h,n=a))}return null==n?s:n},cu:function(t,i){if(t.bb.isSeparate(i.bb))return!1;t=t.Ut;return!!pt.ct(t,i.fe,i.ce)},nu:function(t,i,n){var r=this.$u.find(function(t){return t==n.Hr}),e=this.$u.find(function(t){return t==n.Kr});null==r&&this.$u.push(n.Hr),null==e&&this.$u.push(n.Kr);for(var s=this.bo,o=null,u=0;u<s.length;u++)if(s[u].du==n){o=s[u];break}if(null==o)(o={}).du=n,s.push(o);else for(;null!=o.yu;)var h=o.yu.du,o=pt.at(i,h.Qr,h.Jr)?o.yu:o.bu;n=o.du;var a=new Nn;a.Vi=this.lh(),a.zt=i;var f=new Cn;f.Vi=this.lh(),f.Hr=n.Hr,f.Qr=n.Qr,f.Kr=a.Vi,f.Jr=a.zt,f.$r=[n.Qr,a.zt];var c=new Cn;c.Vi=this.lh(),c.Hr=a.Vi,c.Qr=a.zt,c.Kr=n.Kr,c.Jr=n.Jr,c.$r=[a.zt,n.Jr],this.do.push(f),this.do.push(c);r={};r.du=f;e={};e.du=c,o.yu=r,o.bu=e,a.tempType=1,f.tempType=1,c.tempType=1,f.ue(),c.ue(),f.ee=n.ee,c.ee=n.ee;e=this.io.get(t);e.Re.push(a),e.Te.push(f),e.Te.push(c);var l,t=this.Ju.get(t).ks;return t.Fe.set(a.Vi,e.Re.length-1),n.ee==xn.NaviRoadEntry_BOTH&&((e=[]).push({us:n.Hr,pr:f.pr}),e.push({us:n.Kr,pr:c.pr}),t.De.set(a.Vi,e),(e=t.De.get(n.Hr))||t.De.set(n.Hr,e=[]),e.push({us:a.Vi,pr:f.pr}),(e=t.De.get(n.Kr))||t.De.set(n.Kr,e=[]),e.push({us:a.Vi,pr:c.pr})),n.ee==xn.NaviRoadEntry_FORWARD&&((l=[]).push({us:n.Kr,pr:c.pr}),t.De.set(a.Vi,l),(l=t.De.get(n.Hr))||t.De.set(n.Hr,l=[]),l.push({us:a.Vi,pr:f.pr})),n.ee==xn.NaviRoadEntry_BACK&&((l=[]).push({us:n.Hr,pr:f.pr}),t.De.set(a.Vi,l),(l=t.De.get(n.Kr))||t.De.set(n.Kr,l=[]),l.push({us:a.Vi,pr:c.pr})),a},pu:function(t,i){for(var n=i.length-1;0<=n;n--)if(i[n].Vi&&0<i[n].Vi)return i.splice(n+1,0,t),n+1;return-1},Ko:function(t,i,n,r){if(r)return this.nu(t,i,n);var e=this.io.get(t),s=new Nn;s.Vi=e.Je(),s.zt=i;var o=new Cn;o.Vi=e.$e(),o.Hr=n.Hr,o.Qr=n.Qr,o.Kr=s.Vi,o.Jr=s.zt,o.$r=[n.Qr,s.zt];r=new Cn;r.Vi=e.$e(),r.Hr=s.Vi,r.Qr=s.zt,r.Kr=n.Kr,r.Jr=n.Jr,r.$r=[s.zt,n.Jr];t=[];t.push(o.Vi),t.push(r.Vi),s.segLinks=t,o.ue(),r.ue(),o.ee=n.ee,r.ee=n.ee;var u,i=e.Re,t=e.Te,i=this.pu(s,i);return this.pu(o,t),this.pu(r,t),e.Fe.set(s.Vi,i),n.ee==xn.NaviRoadEntry_BOTH&&((i=[]).push({us:n.Hr,pr:o.pr}),i.push({us:n.Kr,pr:r.pr}),e.De.set(s.Vi,i),(i=e.De.get(n.Hr))||e.De.set(n.Hr,i=[]),i.push({us:s.Vi,pr:o.pr}),(i=e.De.get(n.Kr))||e.De.set(n.Kr,i=[]),i.push({us:s.Vi,pr:r.pr})),n.ee==xn.NaviRoadEntry_FORWARD&&((u=[]).push({us:n.Kr,pr:r.pr}),e.De.set(s.Vi,u),(u=e.De.get(n.Hr))||e.De.set(n.Hr,u=[]),u.push({us:s.Vi,pr:o.pr})),n.ee==xn.NaviRoadEntry_BACK&&((u=[]).push({us:n.Hr,pr:o.pr}),e.De.set(s.Vi,u),(u=e.De.get(n.Kr))||e.De.set(n.Kr,u=[]),u.push({us:s.Vi,pr:r.pr})),s},xu:function(t){if(null!=t){for(var i=this.io.get(t),n=i.Re.length-1;0<=n;n--){var r=i.Re[n];if(null==r||0<=r.Vi)break;i.Re.splice(n,1)}for(var e=i.Te.length-1;0<=e;e--){var s=i.Te[e];if(null==s||0<=s.Vi)break;i.Te.splice(e,1)}}},mu:function(t,i){var n=t.get(i);if(n)for(var r=0;r<n.length;r++)n[r].us<0&&(n.splice(r,1),r--)},wu:function(t,i){for(var n=t.get(i),r=0;r<n.length;r++)this.mu(t,n[r].us);t.delete(i)},vh:function(t,i,n,r,e){r.gu=Number.MAX_VALUE,r.Qs=!1,r.eu=n;for(var s=Number.MAX_VALUE,o={},u=this.io.get(t),h=Number.MAX_VALUE,a=0;a<u.Te.length;a++){var f,c=u.Te[a];null!=c&&(c.Vi<0||(c.ee!=xn.NaviRoadEntry_FORBID?!pt._t(i,n,c.Qr,c.Jr,o)||(f=pt.vt(i,o))<s&&(s=f,r.st=Object.assign({},o),r.vt=s,r.qs=c,r.Qs=!0):!pt._t(i,n,c.Qr,c.Jr,o)||(c=pt.vt(i,o))<=h&&(h=c)))}if(r.vt>=h)return r.vt=Number.MAX_VALUE,r.Qs=!1;if(!r.Qs)return!1;if(1!=this.Ht)for(var l={},v=0;v<u.Ae.length;v++){var d=u.Ae[v];if(null!=d&&d!=e&&(xn.NaviModelPassType_DECORATE!=d.pass&&(null==e||e.pass==xn.NaviModelPassType_DECORATE||!pt.ct(e.Ut,d.fe,d.fe.length-1))))for(var y=d.fe.length,b=0;b<y-1;b++)if(pt._t(i,n,d.fe[b],d.fe[b+1],l)){var p=pt.vt(i,l);if(p<s)return s=p,r.Hs=d,r.vt=Number.MAX_VALUE,r.Qs=!1,r.Ks=xn.NaviObstructType_MODEL,!1}}for(var x={},m=u.Se,w=0;w<m.length;w++){for(var _=m[w],g=m[w].we,O=0;O<g.length;O++)for(var E=g[O],M=0;M<E.length-1;M++)if(pt._t(i,n,E[M],E[M+1],x)){var j=pt.vt(i,x);if(j<s)return s=j,r.Hs=_,r.vt=Number.MAX_VALUE,r.Qs=!1,r.Ks=xn.NaviObstructType_EXTENT,!1}for(var k=m[w].fe,R=0;R<k.length-1;R++)if(pt._t(i,n,k[R],k[R+1],x)){var T=pt.vt(i,x);if(T<s)return s=T,r.Hs=_,r.vt=Number.MAX_VALUE,r.Qs=!1,r.Ks=xn.NaviObstructType_EXTENT,!1}}return!0},Ou:function(t,i){return t.vt-i.vt},Eu:function(t,i){return i.eu-t.eu},Uo:function(t){t=this.io.get(t);if(t){for(var i=t.Re,n=t.Fe,r=i.length-1;0<=r;r--){var e=i[r];if(null==e||0<=e.Vi)break;n.delete(e.Vi),i.splice(r,1)}for(var s=t.Te,o=t.Ce,u=s.length-1;0<=u;u--){var h=s[u];if(null==h||0<=h.Vi)break;o.delete(h.Vi),s.splice(u,1)}}},Bo:function(){var t,i=wr(this.Ju);try{for(i.s();!(t=i.n()).done;){var n,r=t.value[1],e=[],s=r.ks.De,o=wr(s);try{for(o.s();!(n=o.n()).done;){var u=n.value;u[0]<0&&e.push(u[0])}}catch(t){o.e(t)}finally{o.f()}for(var h=0;h<e.length;h++)this.wu(r.ks.De,e[h]);for(var a=0;a<this.$u.length;a++)this.mu(s,this.$u[a])}}catch(t){i.e(t)}finally{i.f()}},iu:function(t,i,n,r,e){for(var s=[],o=0;o<r.length;o++)if(r[o].vt<pt.$())return s.push(r[o]),s;var u={x:0,y:0};if(pt.Ot(i,n.Qr,n.Jr,u)&&this.ju(i,u,{},e)){e={};return e.st=u,e.Qs=!0,e.qs=n,e.eu=0,e.dist=pt.vt(i,u),s.push(e),s}for(var h={x:u.x-i.x,y:u.y-i.y},a=0;a<r.length;a++){var f=r[a],c={x:f.st.x-i.x,y:f.st.y-i.y},c=pt.Tt(c,h);f.eu=c=270<c?c-360:c}r.sort(this.Eu);var l,v=null;if(r[r.length-1].eu<0)return v=r[r.length-1],s.push(v),s;for(var d=0;d<r.length;d++)if(0<r[d].eu)return v=r[d],0==d?s.push(v):(l=r[d-1],pt.vt(v.st,n.Qr)>pt.vt(l.st,n.Qr)?(s.push(l),s.push(v)):(s.push(v),s.push(l))),s;return s},ku:function(t,i,n,r,e){for(var s=r||30,o=0;o<360;o+=s){var u=new or;this.vh(t,i,o,u,e),1==u.Qs&&n.push(u)}n.sort(this.Ou)},tu:function(t,i,n,r){var e=new Map,s=[];for(this.ku(t,i,s,n,r);0!=s.length;){var o=s[0],u=e.get(o.qs.Vi);u?u.push(o):((u=[]).push(o),e.set(o.qs.Vi,u)),s.splice(0,1)}return e},Co:function(t,i){var n=new Nn;n.Vi=this.lh(),n.zt=Object.assign({},i);t=this.io.get(t);return t.Re.push(n),t.Fe.set(n.Vi,t.Re.length-1),n},Ru:function(t,i,n){if(n)return this.Co(t,i);n=new Nn;n.Vi=this.ks.Je(),n.zt=Object.assign({},i);i=this.io.get(t),t=i.Re,t=this.pu(n,t);return i.Fe.set(n.Vi,t),n},ju:function(t,i,n,r){for(var e,s=this.io.get(-1),o=0;o<s.Ae.length;o++)if(e=s.Ae[o],null!=e&&e!=r&&xn.NaviModelPassType_DECORATE!=e.pass)for(var u=e.fe,h=0;h<u.length-1;h++)if(pt.wt(t,i,u[h],u[h+1],n)){var a=pt.vt(t,n),f=pt.vt(i,n);if(!(a<=pt.$()||f<=pt.$()))return!1}for(var c=s.Se,l=0;l<c.length;l++){for(var v=c[l],d=v.we,y=0;y<d.length;y++)for(var b=d[y],p=0;p<b.length-1;p++)if(pt.wt(t,i,b[p],b[p+1],n))return!1;for(var x=v.fe,m=0;m<x.length-1;m++)if(pt.wt(t,i,x[m],x[m+1],n))return!1}return!0},ru:function(t,i,n,r,e,s){void 0===r&&(r=xn.NaviRoadEntry_BOTH),void 0===e&&(e=!0),void 0===s&&(s=!0);var o=new Cn;o.Hr=i.Vi,o.Qr=i.zt,o.Kr=n.Vi,o.Jr=n.zt,o.$r=[i.zt,n.zt],this.$u.push(i.Vi),this.$u.push(n.Vi),o.Vi=e?this.lh():this.ks.$e(),o.ee=r,o.ue(),s&&this.yo.push(o),this.io.get(t).Te.push(o);var u,h,t=this.Ju.get(t);return xn.NaviRoadEntry_BOTH==r&&(u={us:n.Vi,pr:o.pr},(h=t.ks.De.get(i.Vi))?h.push(u):((h=[]).push(u),t.ks.De.set(i.Vi,h)),u={us:i.Vi,pr:o.pr},(h=t.ks.De.get(n.Vi))?h.push(u):((h=[]).push(u),t.ks.De.set(n.Vi,h))),xn.NaviRoadEntry_FORWARD==r&&(u={us:n.Vi,pr:o.pr},(h=t.ks.De.get(i.Vi))?h.push(u):((h=[]).push(u),t.ks.De(i.Vi,h))),xn.NaviRoadEntry_BACK==r&&(r={us:i.Vi,pr:o.pr},(i=t.ks.De.get(n.Vi))?i.push(r):((i=[]).push(r),t.ks.De.set(n.Vi,i))),o},ou:function(t){var i=this.ch(t),n=this.ao.get(i);if(n)for(var r=0;r<n.length;r++){var e=n[r];if(pt.ct(t,e.fe,e.ce))return e}return null},lh:function(){return this.ho--},su:function(t,i,n){var r=null,e=i.Te;if(!e||e.length<=0)return r;var s=i.Ze(t);if(s<=0)return r;var o,u,h={},r={distance:Number.MAX_VALUE,road:null,coords:{level:t.level}},a=wr(e);try{for(a.s();!(u=a.n()).done;){var f=u.value;f.ee!==xn.NaviRoadEntry_FORBID&&s===f.te&&(n&&n.indexOf(f.re)<0||(o=pt.bt(t,f.Qr,f.Jr,h))<r.distance&&(r.distance=o,r.road=f,Object.assign(r.coords,h)))}}catch(t){a.e(t)}finally{a.f()}return r}});var Er=E;function Mr(t,i){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=function(t,i){if(t){if("string"==typeof t)return jr(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?jr(t,i):void 0}}(t))||i&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var e,s=!0,o=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){o=!0,e=t},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw e}}}}function jr(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}A=function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.dh=[],this.Ht=null};Object.assign(A.prototype,{yh:function(t){var i,n=Mr(t);try{for(n.s();!(i=n.n()).done;)for(var r=i.value,e=r[1],s=e.Se,o=0;o<s.length;o++){var u=s[o].Vi,h=e.ms(u),a=s[o].te,f=new Er(a,h);f.Ht=this.Ht,f.br=r[0],this.dh.push(f)}}catch(t){n.e(t)}finally{n.f()}},pr:function(){return this.dh.length},bh:function(t){if(!this.dh)return null;for(var i=this.dh,n=0;n<i.length;n++){var r=i[n];if(t.level==r.br&&r.fh(t))return r}return null},ph:function(t){var i,n=Mr(this.dh);try{for(n.s();!(i=n.n()).done;){var r=i.value;if(r.Vi==t)return r}}catch(t){n.e(t)}finally{n.f()}return null}});var kr=A;function Rr(t){return function(t){if(Array.isArray(t))return Sr(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||Ar(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tr(t,i){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=Ar(t))||i&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var e,s=!0,o=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){o=!0,e=t},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw e}}}}function Ar(t,i){if(t){if("string"==typeof t)return Sr(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Sr(t,i):void 0}}function Sr(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}function Nr(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}r=function(){function i(t){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,i),this.ii=t,this.Qr=null,this.Jr=null,this.Ns=g.MODULE_SHORTEST,this.xh=m.PRIORITY_DEFAULT,this.mh=new Map,this.wh=new Map,this._h=null,this.gh=null,this.Oh=new Map,this.Eh=new Map,this.Mh=[],this.jh=[],this.kh=[],this.Rh=[],this.Th=[],this.Ah=[],this.Hi=[],this.Sh=.1,this.Nh=1,this.Ih=10,this.Lh=.1,this.Dh=1,this.Ph=10,this.Fh=.1,this.Ch=1,this.Uh=10,this.Bh=1,this.Gh=10,this.ws=null,this.Zh=null,this.Wh=null,this.Vh=null,this.Js=null,this.$s=null,this.Yh=null,this.Xh=!1,this.qh=[],this.Mo=!0,this.jo=new Map,this.Hh=null,this.Ht=t.type,this.barrierFree=!1,this.Qh=!1,this.Io=0,this.Kh=0}var t,n,r;return t=i,(n=[{key:"assginedInfos",get:function(){return this.qh},set:function(t){this.qh=t}},{key:"passPts",set:function(t){this.Jh=t}},{key:"failedInsertedPts",get:function(){return this.$h}},{key:"toDoors",get:function(){return this.Xh},set:function(t){this.Xh=t}},{key:"obstruct",get:function(){return this.Mo},set:function(t){this.Mo=t}}])&&Nr(t.prototype,n),r&&Nr(t,r),i}();Object.assign(r.prototype,{er:function(t){if(t&&t.scene&&t.floors){this.ws=t.scene,this.Zh=new Kn;t=t.floors;if(0!=t.size){this.ta(t),this.ia(),this.Wh=new kr,this.Wh.Ht=this.Ht,this.Wh.yh(this.mh);var i,n=Tr(t);try{for(n.s();!(i=n.n()).done;){var r=i.value;if(null!=r[1].gid){var e=r[1].naviAccessible;if(e&&e[0]&&0<e[0].naviNodes.length){this.Qh=!0;break}}}}catch(t){n.e(t)}finally{n.f()}this.Qh&&(this.ta(t),this.ia(),this.Vh=new kr,this.Vh.Ht=this.Ht,this.Vh.yh(this.wh))}}},ei:function(){this.ii=null,this.ws=null,this.Zh&&(this.Zh.ei(),this.Zh=null);var t,i=Tr(this.mh);try{for(i.s();!(t=i.n()).done;)t.value[1].ei()}catch(t){i.e(t)}finally{i.f()}this.mh.clear(),this.mh=null,this._h.ei(),this._h=null;var n,r=Tr(this.Oh);try{for(r.s();!(n=r.n()).done;)n.value[1].ei()}catch(t){r.e(t)}finally{r.f()}if(this.Oh.clear(),this.Oh=null,this.Mh=null,this.kh=null,this.Qh){var e,s=Tr(this.wh);try{for(s.s();!(e=s.n()).done;)e.value[1].ei()}catch(t){s.e(t)}finally{s.f()}this.wh.clear(),this.wh=null,this.gh.ei(),this.gh=null;var o,u=Tr(this.Eh);try{for(u.s();!(o=u.n()).done;)o.value[1].ei()}catch(t){u.e(t)}finally{u.f()}this.Eh.clear(),this.Eh=null,this.jh=null,this.Rh=null}this.Th=null,this.Ah=null,this.Hi=null},na:function(t,i){this.Un();var n=this.ra([t]);if(n.length<1)return S.ROUTE_FAILED_NO_DATA_START;var r=n[0];t.extentID=r;n=this.ra([i]);if(n.length<1)return S.ROUTE_FAILED_NO_DATA_DEST;n=n[0];i.endExtentID=n;for(var e=this.Jh,s=0;s<e.length;s++)e[s].uh=!0;var o=this.ra(e),n=this.ea(r,n,o);if(n.rst!=S.ROUTE_SUCCESS)return n.rst;for(var u=new Map,h=0;h<o.length;h++)u.has(o[h])?u.get(o[h]).push(e[h]):u.set(o[h],[e[h]]);n=n.extentPath,n=this.sa(t,i,u,n);return this.oa(n.nodePath),n.rst},ua:function(t,i,n,r){this.Un();var e=null,s=(e=this.Qh&&this.barrierFree?this.Eh:this.Oh).get(t.level);if(!s)return S.ROUTE_FAILED_NO_DATA_START;var o=s.Ho(t);if(o.te<0)return S.ROUTE_FAILED_NO_DATA_START;if(o.Hs<0)return S.ROUTE_FAILED_NO_START_ARRIVAL;var u=e.get(i.level);if(!u)return S.ROUTE_FAILED_NO_DATA_DEST;var h=u.Ho(i);if(h.te<0)return S.ROUTE_FAILED_NO_DATA_DEST;if(!h.Hs<0)return S.ROUTE_FAILED_NO_DEST_ARRIVAL;this.Ns=n,this.Qr=t,this.Js=o.Hs,this.Jr=i,this.$s=h.Hs;var a=o.te,f=h.te,s=o.Hs,u=h.Hs;if(this.Hi=[],1!==this.Ht&&this.ha(r),a===f){h=t.level,r=e.get(h);if(r.Js=s,r.$s=u,(o=r.Cs(t,i,n))!==S.ROUTE_SUCCESS)return o;r=r.Zs();this.Hi.push({br:h,Hi:r})}else{if(this.aa(a,f,t,i)!=S.ROUTE_SUCCESS)return S.ROUTE_FAILED_OUTLINE;this.Th.reverse();i=this.fa(e,t,i);if(i!=S.ROUTE_SUCCESS)return i}return S.ROUTE_SUCCESS},fa:function(t,i,n){this.Hi=[];for(var r=[],e=[this.Qr],s=S.ROUTE_FAILED_CANNOT_ARRIVE,o=0;o<this.Th.length-1;o++){var u=[],h=[];if(null==this.Ht||0===this.Ht){if(!this.ca(this.Th[o],this.Th[o+1],e[e.length-1],u,h))return S.ROUTE_FAILED_CANNOT_ARRIVE}else if(!this.la(this.Th[o],this.Th[o+1],e[e.length-1],u,h))return S.ROUTE_FAILED_CANNOT_ARRIVE;for(var a=!1,f=0;f<u.length;f++){var c,l=u[f].qr,v=h[f].qr,d=u[f].br,y=h[f].br;if(s=0==o?((c=t.get(d)).Js=this.Js,c.Cs(i,l,this.Ns)):t.get(d).Cs(r[r.length-1],l,this.Ns),S.ROUTE_SUCCESS==s){l=t.get(d).Zs();if(this.Hi.push({br:d,Hi:l}),r.push(v),e.push(v.zt),a=!0,this.Th.length-2==o){l=t.get(y);if(l.$s=this.$s,s=l.Cs(v,n,this.Ns),S.ROUTE_SUCCESS!=s){this.Hi.splice(this.Hi.length-1,1),a=!1,e.splice(e.length-1,1),r.splice(r.length-1,1);continue}l=l.Zs();this.Hi.push({br:y,Hi:l})}if(S.ROUTE_SUCCESS==s)break}}if(!a)return S.ROUTE_FAILED_CANNOT_ARRIVE}return s},va:function(){if(this.Xh&&(this.Yh=null,this.$s&&0<this.Hi.length)){for(var t,i=this.Hi[this.Hi.length-1].Hi,n=null,r={x:-1,y:-1},e=i.length-1;0<e;e--){var s=i[e],o=i[e-1];if(pt.At(s.zt,o.zt,this.$s.fe,r)){n=e;break}}null!=n&&(i.splice(n),(t=new Nn).Vi=-1,t.zt=r,i.push(t),this.Yh=r)}for(var u=[],h=0;h<this.Hi.length;h++)1<this.Hi[h].Hi.length&&u.push(this.Hi[h]);for(var a=[],f=0;f<u.length;f++){this.da(u[f]);for(var c=u[f],l=c.br,v=c.Hi,d=[],y=0,b=0;b<v.length;b++)d.push(v[b].zt),b!=v.length-1&&(y+=pt.vt(v[b].zt,v[b+1].zt));c=new gn;c.br=l,c.mr=v,c.xr=d,c.pr=y,a.push(c),f!=u.length-1&&(l=this.ya(u[f]),c=this.ba(u[f+1]),a.push(l),a.push(c))}return a},da:function(t){if(null!=t&&t.Hi){var i=t.Hi;if((u=i.length)&&!(u<2)){var n=[];n.push(i[0]);var r=i[0],e=i[u-1];pt.vt(r.zt,e.zt),pt.$();for(var s=1;s<u-1;s++){var o=i[s];null!=r&&pt.vt(r.zt,o.zt)<pt.$()||null!=e&&pt.vt(e.zt,o.zt)<pt.$()||n.push(o)}n.push(e);for(var u=n.length,h=new Array(u),a=0;a<u;a++)h[a]=0;for(var f=1;f<u-1;f++){for(var c=n[f],l=f-1;0!=h[l];)l--;var v=n[l],d=n[f+1];pt.lt(c.zt,v.zt,d.zt)&&(c.uh||(h[f]=1))}for(var y=[],b=0;b<u;b++)0==h[b]&&y.push(n[b]);t.Hi=y}}},Un:function(){this.Th=[],this.Ah=[];var t,i=Tr(this.Oh);try{for(i.s();!(t=i.n()).done;){var n=t.value;n[1].Un(),n[1].obstruct=this.Mo}}catch(t){i.e(t)}finally{i.f()}var r,e=Tr(this.Eh);try{for(e.s();!(r=e.n()).done;){var s=r.value;s[1].Un(),s[1].obstruct=this.Mo}}catch(t){e.e(t)}finally{e.f()}},pa:function(t,i){var n=null,r=null;if(this.Qh&&this.barrierFree){if(this.Eh.size<=0)return n;r=this.Eh}else{if(this.Oh.size<=0)return n;r=this.Oh}var e=r.get(t.level);if(!e||null==e||!e.ks)return n;var r=e.ks,s=r.Ze(t);if(s<=0)return n;var o=[];o.push.apply(o,Rr(r.Te));var u,h=Tr(e.io);try{for(h.s();!(u=h.n()).done;){var a=u.value;o.push.apply(o,Rr(a[1].Te))}}catch(t){h.e(t)}finally{h.f()}for(var n={distance:Number.MAX_VALUE,road:null,angle:null,coords:{level:t.level}},f={},c=0;c<o.length;c++){var l,v=o[c];s===v.te&&v.ee!==xn.NaviRoadEntry_FORBID&&(Array.isArray(i)&&0<i.length&&i.indexOf(v.re)<0||(l=pt.bt(t,v.Qr,v.Jr,f))<n.distance&&(n.distance=l,n.road=v,n.angle=this.xa(v.Qr,v.Jr),Object.assign(n.coords,f)))}return null!=n.road&&(e={id:(e=n.road).Vi,extentID:e.te,roadEntry:e.ee,roadRank:e.re,startID:e.Hr,startPoint:e.Qr,endID:e.Kr,endPoint:e.Jr},n.road=e),n},xa:function(t,i){var n=new Tn(0,0,1),t=new Tn(t.x,0,t.y),i=new Tn(i.x,0,i.y).clone().sub(t).normalize(),t=i.angleTo(n)*pt.it();return 0<i.clone().cross(n).y?360-t:t},ta:function(t){this.Qh?this.wh=new Map:this.mh=new Map,this.Zh._s(this.ws);var i,n=Tr(t);try{for(n.s();!(i=n.n()).done;){var r=i.value,e=r[1].gid;if(null!=e){var s=r[1].geo[0],o=r[1].biz[0],u=null;if(1==this.Ht?0<r[1].naviDrive.length&&(u=r[1].naviDrive[0]):this.Qh?0<r[1].naviAccessible.length&&(u=r[1].naviAccessible[0]):0<r[1].navi.length&&(u=r[1].navi[0]),u){var h=this.Zh.js(e,s,o,u);if(0!=h.naviNodes.length&&0!=h.naviSegments.length){var a=new Map,f=this.ma(h,a),c=this.wa(h),l=this._a(h),v=this.ga(h),d=this.Oa(h);this.Ea(d,f,u,a);var y=new Qn(f,c,!0,1);y.br=e,0!=v.length&&(y.Se=v),0!=l.length&&(y.Ie=l),0!=d.length&&(y.Ae=d);for(var b=0;b<c.length;b++)c[b].te=y.Ze(c[b].Qr);(0==this.Qh?this.mh:this.wh).set(h.level,y)}}}}}catch(t){n.e(t)}finally{n.f()}},ma:function(r,e){var t,s=[];for(t in r.naviNodes)(function(t){var i=r.naviNodes[t];if(null==i.nodeId)return;var n=new Nn;n.Vi=i.nodeId,n.zt={x:parseFloat(i.geoArr.x.toFixed(3)),y:parseFloat(i.geoArr.y.toFixed(3))},n.Pr=i.nodeType,n.Fr=i.liftType,n.Tr=i.liftFlag,n.Cr=i.liftEntry,i.accessible&&(n.Ma=i.accessible),i.linkSegArr.forEach(function(t){n.Rr.push(parseInt(t))}),i.liftFloorArr.forEach(function(t){n.kr.push(parseInt(t))}),s.push(n),e.set(i.nodeId,t)})(t);return s},wa:function(t){var i,n=[];for(i in t.naviSegments){var r,e=t.naviSegments[i],s=e.geoArr;s&&((r=new Cn).Vi=e.segmentId,r.Hr=e.snode,r.Qr={x:parseFloat(s[0].x.toFixed(3)),y:parseFloat(s[0].y.toFixed(3))},r.Kr=e.enode,r.Jr={x:parseFloat(s[1].x.toFixed(3)),y:parseFloat(s[1].y.toFixed(3))},r.pr=e.length,r.ee=e.entry,r.re=e.rank,n.push(r))}return n},ga:function(t){for(var i=[],n=0,r=t.extentLayer.length;n<r;n++){var e=t.extentLayer[n],s=e.geoArr,s=new Zn(e.eid,t.gid,0,s=s||[],e.Ut,e.holes);s.fid=e.fid,i.push(s)}return i},Oa:function(t){for(var i=[],n=0,r=t.modelLayer.length;n<r;n++){var e=t.modelLayer[n],s=e.geoArr,s=new Zn(e.eid,t.gid,0,s=s||[],e.Ut,e.holes);s.fid=e.fid,s.pass=e.pass,i.push(s)}return i},_a:function(t){for(var i=[],n=0,r=t.naviZones.length;n<r;n++){var e=t.naviZones[n],e=new Bn(e.Vi,t.gid,e.type,e.geoArr);i.push(e)}return i},Ea:function(t,i,n,r){for(var e=new Map,s=0;s<t.length;s++){var o=t[s];e.set(o.Vi,s)}for(var u=0;u<n.naviModels.length;u++)for(var h=n.naviModels[u],a=h.modelid,f=t[e.get(a)],c=0;c<h.doorids.length;c++){var l=h.doorids[c],v=i[r.get(l)];f._e.push(v);l=h.nodetypes[c],v=h.entrytypes[c];f.ge.push(l),f.Oe.push(v)}},ia:function(){this.ja(),this.ka();var t=new Qn(this.Mh,this.kh);this._h=new tr(t);var i,n=Tr(this.mh);try{for(n.s();!(i=n.n()).done;){var r=i.value,e=new lr(r[1]);e.Ht=this.Ht,this.Oh.set(r[0],e)}}catch(t){n.e(t)}finally{n.f()}if(this.Qh){this.ja(),this.ka();t=new Qn(this.jh,this.Rh);this.gh=new tr(t);var s,o=Tr(this.wh);try{for(o.s();!(s=o.n()).done;){var u=s.value;this.Eh.set(u[0],new lr(u[1]))}}catch(t){o.e(t)}finally{o.f()}}},ja:function(){var t,i=0,n=0,r=Tr(this.Qh?(this.jh=[],this.wh):(this.Mh=[],this.mh));try{for(r.s();!(t=r.n()).done;)for(var e=t.value,s=e[1],o=s.Se,u=0;u<o.length;u++){var h=o[u],a=new zn;a.Vi=h.Vi,a.br=e[0],a.te=++i,h.te=a.te,a.Fs=h;for(var f=0;f<s.Re.length;f++){var c,l,v=s.Re[f];v.Fr!=xn.NaviLiftType_NULL&&pt.ct(v.zt,h.fe,h.ce)&&(c=new Fn,l=s.Xe(v.zt),c.zr=l,c.Zr(v),c.Xr=++n,c.br=e[0],c.qr=v,c.Cr=v.Cr,c.Fr=v.Fr,c.Tr=v.Tr,c.te=a.te,this.Qh?1===v.Ma&&(c.Ma=v.Ma,a.je.push(c)):(1===v.Ma&&(c.Ma=v.Ma),a.je.push(c)))}(this.Qh?this.jh:this.Mh).push(a)}}catch(t){r.e(t)}finally{r.f()}},ka:function(){for(var t=null,i=0,t=this.Qh?this.jh:this.Mh,n=[],r=0;r<t.length;r++)for(var e=t[r],s=0;s<t.length;s++){var o,u,h=t[s];r!=s&&(u=void 0,null!=(u=null==this.Ht||0==this.Ht?this.Ra(e,h):this.Ta(e,h))&&(u*=0==(o=Math.abs(e.br-h.br))?1:o,(o=new Cn).Hr=e.te,o.Kr=h.te,o.pr=u,o.ee=xn.NaviRoadEntry_FORWARD,o.Vi=i,i+=1,n.push(o)))}this.Qh?this.Rh=n:this.kh=n},Ra:function(t,i){for(var n=Number.MAX_VALUE,r=!1,e=t.br,s=i.br,o=e<s?xn.NaviLiftEntry_DOWN:xn.NaviLiftEntry_UP,u=0;u<t.je.length;u++){var h=t.je[u],a=h.Cr;if(a!=xn.NaviLiftEntry_FORBID&&a!=o){var f=h.Fr;if(m.PRIORITY_LIFTONLY==this.xh){if(xn.NaviLiftType_Lift!=f)continue}else if(m.PRIORITY_ESCALATORONLY==this.xh){if(xn.NaviLiftType_Escalator!=f)continue}else if(m.PRIORITY_STAIRONLY==this.xh){if(xn.NaviLiftType_Stair!=f)continue}else if(m.PRIORITY_ACCESSIBLEONLY==this.xh&&xn.NaviLiftType_Accessible!=f)continue;if(f==xn.NaviLiftType_Stair||-1!=h.kr.indexOf(s))for(var c=0;c<i.je.length;c++){var l=i.je[c];if(null!=l&&h.Tr==l.Tr)if(m.PRIORITY_LIFTFIRST==this.xh||m.PRIORITY_DEFAULT==this.xh){if(xn.NaviLiftType_Lift==f||xn.NaviLiftType_Accessible==f)return this.Sh;if(xn.NaviLiftType_Escalator==f){var v=this.Dh;v<n&&(n=v),r=!0;break}if(xn.NaviLiftType_Stair==f){v=this.Uh;v<n&&(n=v),r=!0;break}}else if(m.PRIORITY_ESCALATORFIRST==this.xh){if(xn.NaviLiftType_Lift==f||xn.NaviLiftType_Accessible==f){var d=this.Nh;d<n&&(n=d),r=!0;break}if(xn.NaviLiftType_Escalator==f)return this.Lh;if(xn.NaviLiftType_Stair==f){d=this.Uh;d<n&&(n=d),r=!0;break}}else if(m.PRIORITY_STAIRFIRST==this.xh){if(xn.NaviLiftType_Lift==f){var y=this.Nh;y<n&&(n=y),r=!0;break}if(xn.NaviLiftType_Escalator==f||xn.NaviLiftType_Accessible==f){y=this.Ph;y<n&&(n=y),r=!0;break}if(xn.NaviLiftType_Stair==f)return this.Fh}else{if(m.PRIORITY_LIFTFIRST1==this.xh||m.PRIORITY_STAIRFIRST1==this.xh||m.PRIORITY_ESCALATORFIRST1==this.xh)return this.Bh;if(m.PRIORITY_LIFTONLY==this.xh||m.PRIORITY_ESCALATORONLY==this.xh||m.PRIORITY_STAIRONLY==this.xh||m.PRIORITY_ACCESSIBLEONLY==this.xh)return this.Bh}}}}return r?n:null},Ta:function(t,i){for(var n=Number.MAX_VALUE,r=!1,e=t.br<i.br?xn.NaviLiftEntry_DOWN:xn.NaviLiftEntry_UP,s=0;s<t.je.length;s++){var o=t.je[s],u=o.Cr;if(u!=xn.NaviLiftEntry_FORBID&&u!=e){for(var h=0;h<i.je.length;h++){var a=i.je[h];if(o.Tr==a.Tr){n=this.Gh,r=!0;break}}if(r)break}}return r?n:null},ha:function(t){var i;this.xh!==t&&(this.xh=t,this.barrierFree?(this.ka(),i=new Qn(this.jh,this.Rh),this.gh=new tr(i)):(this.ka(),i=new Qn(this.Mh,this.kh),this._h=new tr(i)))},ba:function(t){var i=t.Hi,n=new gn;return n.br=t.br,n.mr.push(i[0]),n.xr.push(i[0].zt),n.pr=0,n},ya:function(t){var i=t.Hi,n=new gn;return n.br=t.br,n.mr.push(i[i.length-1]),n.xr.push(i[i.length-1].zt),n.pr=0,n},Aa:function(t,i){for(var n=null,n=this.Qh&&this.barrierFree?this.jh:this.Mh,r=0;r<n.length;r++)if(null!=t&&n[r].br==t&&n[r].Vi==i)return n[r];return null},Sa:function(t){for(var i=null,n=null,n=this.Qh&&this.barrierFree?this.jh:this.Mh,r=0;r<n.length;r++)if(n[r].te==t)return i=n[r];return i},aa:function(t,i,n,r){var e,s=null,o=null;if((s=(o=this.Qh&&this.barrierFree?this.gh:this._h).Gs(t,i,n,r))!=S.ROUTE_SUCCESS)return s;e=o.Zs(),this.Th=[];for(var u=0;u<e.length;u++)this.Th.push(e[u]);return s},ca:function(n,t,i,r,e){this.Qh&&this.barrierFree?this.Hh=this.Eh.get(n.br).ks.Xe(i):this.Hh=this.Oh.get(n.br).ks.Xe(i);var s=Math.abs(n.br-t.br),o=[];function u(t,i){i=n.ke(t,i,h);o.push.apply(o,Rr(i))}var h=[];if(0==this.Qh&&1==this.barrierFree)for(var a=0;a<n.je.length;a++)1==n.je[a].Ma&&h.push(n.je[a]);else h=n.je;for(var f=0;f<h.length;f++){var c=h[f];c.Ir=pt.vt(c.zt,this.Jr)+pt.vt(c.zt,i)}var l=n.Fs.Zt.size,v=l.x+l.y;m.PRIORITY_LIFTONLY!==this.xh&&m.PRIORITY_ESCALATORONLY!==this.xh&&m.PRIORITY_STAIRONLY!==this.xh&&m.PRIORITY_ACCESSIBLEONLY!==this.xh||(l=null,m.PRIORITY_LIFTONLY===this.xh&&(l=xn.NaviLiftType_Lift),m.PRIORITY_ESCALATORONLY===this.xh&&(l=xn.NaviLiftType_Escalator),m.PRIORITY_STAIRONLY===this.xh&&(l=xn.NaviLiftType_Stair),m.PRIORITY_ACCESSIBLEONLY===this.xh&&(l=xn.NaviLiftType_Accessible),o=n.ke(l,null,h)),m.PRIORITY_LIFTFIRST!==this.xh&&m.PRIORITY_ESCALATORFIRST!==this.xh&&m.PRIORITY_STAIRFIRST!==this.xh&&m.PRIORITY_DEFAULT!==this.xh||(m.PRIORITY_LIFTFIRST!==this.xh&&m.PRIORITY_DEFAULT!==this.xh||(u(xn.NaviLiftType_Lift,-15*s),u(xn.NaviLiftType_Escalator,-8*s),u(xn.NaviLiftType_Stair,0)),m.PRIORITY_ESCALATORFIRST===this.xh&&(u(xn.NaviLiftType_Lift,-8*s),u(xn.NaviLiftType_Escalator,-15*s),u(xn.NaviLiftType_Stair,0)),m.PRIORITY_STAIRFIRST===this.xh&&(u(xn.NaviLiftType_Lift,-8*s),u(xn.NaviLiftType_Escalator,0),u(xn.NaviLiftType_Stair,-15*s))),m.PRIORITY_LIFTFIRST1!==this.xh&&m.PRIORITY_ESCALATORFIRST1!==this.xh&&m.PRIORITY_STAIRFIRST1!==this.xh||(m.PRIORITY_LIFTFIRST1===this.xh&&(u(xn.NaviLiftType_Lift,-v),u(xn.NaviLiftType_Escalator,0),u(xn.NaviLiftType_Stair,0)),m.PRIORITY_ESCALATORFIRST1===this.xh&&(u(xn.NaviLiftType_Lift,0),u(xn.NaviLiftType_Escalator,-v),u(xn.NaviLiftType_Stair,0)),m.PRIORITY_STAIRFIRST1===this.xh&&(u(xn.NaviLiftType_Lift,0),u(xn.NaviLiftType_Escalator,0),u(xn.NaviLiftType_Stair,-v)));var d=this;o.sort(function(t,i){if(t.zone!==i.zone){if(t.zone===d.Hh)return-1;if(i.zone===d.Hh)return 1}return t.Ir-i.Ir});for(var y=0;y<o.length;y++){var b=o[y];if(!(1===b.Cr&&t.br<n.br)&&!(2===b.Cr&&t.br>n.br))for(var p=0;p<t.je.length;p++){var x=t.je[p];if(b.Tr===x.Tr){r.push(b),e.push(x);break}}}return 0!==r.length},la:function(t,i,n,r,e){this.Hh=this.Oh.get(t.br).ks.Xe(n);for(var s=[],o=t.je,u=0;u<o.length;u++){var h=o[u];h.Ir=pt.vt(h.zt,this.Jr)+pt.vt(h.zt,n)}var s=t.ke(xn.NaviLiftType_Ramp),a=this;s.sort(function(t,i){if(t.zone!==i.zone){if(t.zone===a.Hh)return-1;if(i.zone===a.Hh)return 1}return t.Ir-i.Ir});for(var f=0;f<s.length;f++){var c=s[f];if(!(1===c.Cr&&i.br<t.br)&&!(2===c.Cr&&i.br>t.br))for(var l=0;l<i.je.length;l++){var v=i.je[l];if(c.Tr===v.Tr){r.push(c),e.push(v);break}}}return 0!==r.length},ra:function(t){var i=[],n=null;if(!(n=this.barrierFree?this.Vh:this.Wh))return i;var r,e=Tr(t);try{for(e.s();!(r=e.n()).done;){var s=r.value,o=n.bh(s),u=null==o?void 0:o.Vi;u&&i.push(u)}}catch(t){e.e(t)}finally{e.f()}return i},ea:function(t,i,n){var r=null,e=Array.from(new Set(n)),n=e.indexOf(t);-1!=n&&e.splice(n,1);n=e.indexOf(i);if(-1!=n&&e.splice(n,1),e.length<=0){if(r={rst:S.ROUTE_FAILED_OUTLINE,extentPath:[]},t==i)return r.extentPath.push(t),r.rst=S.ROUTE_SUCCESS,r;if(r.rst=this.aa(t,i),r.rst!=S.ROUTE_SUCCESS)return r;this.Th.reverse();for(var s=0;s<this.Th.length;s++)r.extentPath.push(this.Th[s].te)}else r=this.Na(t,i,e);return r},Ia:function(t){var i=0;if(null==t||t.length<=0)return i;for(var n=this._h.ks.De,r=1;r<t.length;r++)for(var e=t[r].Vi,s=t[r-1].Vi,o=n.get(s),u=0;u<o.length;u++)if(o[u].us==e){i+=o[u].pr;break}return i},Na:function(t,i,n){for(var r={rst:null,extentPath:[]},e=[],s=t;0<n.length;){for(var o=Number.MAX_VALUE,u=null,h=null,a=0;a<n.length;a++){var f,c=n[a];this._h.Gs(s,c)==S.ROUTE_SUCCESS&&((f=this._h.Zs()).reverse(),(c=this.Ia(f))<o&&(o=c,u=a,h=f))}if(null==u)return r;s=n[u],h.splice(h.length-1,1),e.push.apply(e,Rr(h)),n.splice(u,1)}if(r.rst=this._h.Gs(s,i),r.rst!=S.ROUTE_SUCCESS)return r;i=this._h.Zs();i.reverse(),e.push.apply(e,Rr(i));for(var l=0;l<e.length;l++)r.extentPath.push(e[l].te);return r},sa:function(t,i,n,r){this.Un();for(var e=[],s=t,o=0;o<r.length;o++){var u=r[o],h=null,h=(this.barrierFree?this.Vh:this.Wh).ph(u),a=[],f=n.get(r[o]);if(null!=f)for(;0<f.length;){var c=null,l=null,v=Number.MAX_VALUE;this.La(s,f);for(var d=4<=Math.ceil(f.length/2)?4:Math.ceil(f.length/2),y=0;y<d;y++){var b=this.Da(s,f[y],h);b.rst==S.ROUTE_SUCCESS&&b.dist<v&&(c=b.nodePath,l=y,v=b.dist)}if(null==l)return{rst:S.ROUTE_FAILED_WAYPOINT_CALCULATE_ERROR,nodePath:e};s=f[l],f.splice(l,1),c.splice(c.length-1,1),a.push.apply(a,Rr(c))}if(o<r.length-1){var p=this.Sa(u),x=s instanceof Nn?s.br:s.level,m=r[o+1],u=this.Sa(m),w=[],m=[];if(!this.Pa(p,u,s,w,m))return{rst:S.ROUTE_FAILED_WAYPOINT_CALCULATE_ERROR,nodePath:e};for(var _=null,g=0;g<w.length;g++){var O=this.Da(s,w[g],h);if(O.rst==S.ROUTE_SUCCESS){a.push.apply(a,Rr(O.nodePath)),_=g;break}}if(null==_)return{rst:S.ROUTE_FAILED_WAYPOINT_CALCULATE_ERROR,nodePath:e};s=m[_],e.push({level:x,path:a})}if(o==r.length-1){x=this.Da(s,i,h);return x.rst!=S.ROUTE_SUCCESS?{rst:S.ROUTE_FAILED_WAYPOINT_CALCULATE_ERROR,nodePath:e}:(a.push.apply(a,Rr(x.nodePath)),e.push({level:i.level,path:a}),{rst:S.ROUTE_SUCCESS,nodePath:e})}}return{rst:S.ROUTE_SUCCESS,nodePath:e}},oa:function(t){for(var i=[],n=0;n<t.length;n++)i.push({br:t[n].level,Hi:t[n].path});this.Hi=i},Da:function(t,i,n){var r,e;Number.MAX_VALUE;return n.Un(),n.oh(t),n.ah(i),n.oo=n.ch(t),n.uo=n.ch(i),r=n.Cs(),e=n.so,{rst:r,dist:this.Fa(e),nodePath:e}},Fa:function(t){var i=0;if(null==t||t.length<=1)return i;for(var n=1;n<t.length;n++)i+=pt.vt(t[n].zt,t[n-1].zt);return i},Pa:function(n,t,i,r,e){var s=Math.abs(n.br-t.br),o=[];function u(t,i){i=n.ke(t,i,h);o.push.apply(o,Rr(i))}var h=[];if(0==this.Qh&&1==this.barrierFree)for(var a=0;a<n.je.length;a++)1==n.je[a].Ma&&h.push(n.je[a]);else h=n.je;for(var f=0;f<h.length;f++){var c=h[f];c.Ir=pt.vt(i,c.zt)}var l=n.Fs.Zt.size,v=l.x+l.y;m.PRIORITY_LIFTONLY!==this.xh&&m.PRIORITY_ESCALATORONLY!==this.xh&&m.PRIORITY_STAIRONLY!==this.xh&&m.PRIORITY_ACCESSIBLEONLY!==this.xh||(l=null,m.PRIORITY_LIFTONLY===this.xh&&(l=xn.NaviLiftType_Lift),m.PRIORITY_ESCALATORONLY===this.xh&&(l=xn.NaviLiftType_Escalator),m.PRIORITY_STAIRONLY===this.xh&&(l=xn.NaviLiftType_Stair),m.PRIORITY_ACCESSIBLEONLY===this.xh&&(l=xn.NaviLiftType_Accessible),o=n.ke(l,null,h)),m.PRIORITY_LIFTFIRST!==this.xh&&m.PRIORITY_ESCALATORFIRST!==this.xh&&m.PRIORITY_STAIRFIRST!==this.xh&&m.PRIORITY_DEFAULT!==this.xh||(m.PRIORITY_LIFTFIRST!==this.xh&&m.PRIORITY_DEFAULT!==this.xh||(u(xn.NaviLiftType_Lift,-15*s),u(xn.NaviLiftType_Escalator,-8*s),u(xn.NaviLiftType_Stair,0)),m.PRIORITY_ESCALATORFIRST===this.xh&&(u(xn.NaviLiftType_Lift,-8*s),u(xn.NaviLiftType_Escalator,-15*s),u(xn.NaviLiftType_Stair,0)),m.PRIORITY_STAIRFIRST===this.xh&&(u(xn.NaviLiftType_Lift,-8*s),u(xn.NaviLiftType_Escalator,0),u(xn.NaviLiftType_Stair,-15*s))),m.PRIORITY_LIFTFIRST1!==this.xh&&m.PRIORITY_ESCALATORFIRST1!==this.xh&&m.PRIORITY_STAIRFIRST1!==this.xh||(m.PRIORITY_LIFTFIRST1===this.xh&&(u(xn.NaviLiftType_Lift,-v),u(xn.NaviLiftType_Escalator,0),u(xn.NaviLiftType_Stair,0)),m.PRIORITY_ESCALATORFIRST1===this.xh&&(u(xn.NaviLiftType_Lift,0),u(xn.NaviLiftType_Escalator,-v),u(xn.NaviLiftType_Stair,0)),m.PRIORITY_STAIRFIRST1===this.xh&&(u(xn.NaviLiftType_Lift,0),u(xn.NaviLiftType_Escalator,0),u(xn.NaviLiftType_Stair,-v)));var d=this;o.sort(function(t,i){if(t.zone!==i.zone){if(t.zone===d.Hh)return-1;if(i.zone===d.Hh)return 1}return t.Ir-i.Ir});for(var y=0;y<o.length;y++){var b=o[y];if(!(1===b.Cr&&t.br<n.br)&&!(2===b.Cr&&t.br>n.br))for(var p=0;p<t.je.length;p++){var x=t.je[p];if(b.Tr===x.Tr){r.push(b),e.push(x);break}}}return 0!==r.length},La:function(n,t){t.sort(function(t,i){return pt.dt(n,t)-pt.dt(n,i)})},Wo:function(t){for(var i=new Map,n=0;n<t.length;n++){var r=t[n],e=i.get(r.br);e||i.set(r.br,e=[]),e.push(r)}var s,o=Tr(i);try{for(o.s();!(s=o.n()).done;){var u=s.value,h=this.jo.get(u[0]);h&&!this.Ca(u[1],h)||(this.Oh.get(u[0]).Wo(u[1]),this.Eh.get(u[0]).Wo(u[1]),this.jo.set(u[0],u[1]),0)}}catch(t){o.e(t)}finally{o.f()}},Ua:function(){var t,i=Tr(this.jo);try{for(i.s();!(t=i.n()).done;){var n=t.value;this.Oh.get(n[0]).Ua()}}catch(t){i.e(t)}finally{i.f()}this.jo.clear()},Ca:function(t,i){if(t.length!=i.length)return!0;for(var n=0;n<t.length;n++){for(var r=!1,e=0;e<i.length;e++)i[e].Ba(t[n])&&(r=!0);if(!r)return!0}return!1},Fo:function(){for(var t in this.qh){var i=parseInt(t),t=(this.Qh&&this.barrierFree?this.Eh:this.Oh).get(i).get(i);t.assignedPts=this.qh[i],t.Fo(),this.Hi.push({br:i,Hi:t.Zs()})}}});var Ir=r,Lr={zh:{straight:"直行",still_go_straight:"继续直行",back_to:"往回走",turn_right:"右转",turn_left:"左转",right_front:"右前方 继续直行",left_front:"左前方 继续直行",turn_right_rear:"右后方转",turn_left_rear:"左后方转",up:"上",down:"下",up_to:"上行到达",down_to:"下行到达",drive_up:"上坡到达",drive_down:"下坡到达",meter:"米",pass:"经过",arrived:"到达目的地",front:"前方",stair:"楼梯",stair_pure:"楼梯",escalator_pure:"扶梯",lift_pure:"直梯",ramp_pure:"坡道",take:"乘",along:"沿",take_stair:"乘梯",along_ramp:"沿坡道",front_pure:"前",right_front_pure:"右前",right_pure:"右",right_back_pure:"右后",back_pure:"后",left_back_pure:"左后",left_pure:"左",left_front_pure:"左前",east:"东",west:"西",south:"南",north:"北",southeast:"东南",northeast:"东北",southwest:"西南",northwest:"西北",face_to:"向",start:"起",dest:"终",enter:"前往",outdoor:"室外",entrance:"入口",exit:"出口",reach:"到达"},en:{straight:"go straight",still_go_straight:"go straight",back_to:"turn back",turn_right:"turn right",turn_left:"turn left",right_front:"go straight right front ",left_front:"go straight left front ",turn_right_rear:"turn right rear",turn_left_rear:"turn left rear",up:"up",down:"up",up_to:"up to",down_to:"down to",drive_up:"drive up",drive_down:"drive down",meter:"meter",pass:"pass",arrived:"arrived",front:"front",stair:"stair",stair_pure:"stair",escalator_pure:"escalator",lift_pure:"lift",ramp_pure:"ramp",take:"take",along:"along",take_stair:"take stair",along_ramp:"along ramp",front_pure:"front",right_front_pure:"right front",right_pure:"right",right_back_pure:"right back",back_pure:"back",left_back_pure:"left back",left_pure:"left",left_front_pure:"left front",east:"east",west:"west",south:"south",north:"north",southeast:"southeast",northeast:"northeast",southwest:"southwest",northwest:"northwest",face_to:"face to",start:"start",dest:"dest",enter:"enter",outdoor:"outdoor",entrance:"entrance",exit:"exit",reach:"reach"}};s=function t(i){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.Ga=i.data||null,this.Za=i.directionAssert||10,this.Wa=i.combineAsset||5,this.Va=i.combineDistance||.1,this.Ya=i.language||a.ZH,this.Ht=i.type||mn.Walk,this.Xa=["north","northeast","east","southeast","south","southwest","west","northwest","north"],this.za=["front_pure","right_front_pure","right_pure","right_back_pure","back_pure","left_back_pure","left_pure","left_front_pure","front_pure"],this.qa=["still_go_straight","right_front","turn_right","turn_right_rear","back_to","turn_left_rear","turn_left","left_front","still_go_straight"],this.Ha=0,this.Qa=[],this.Ka=!1,this.Ja=0,Object.assign(this,i)};Object.assign(s.prototype,{$a:function(t){for(var i=[],n=0;n<t.length;n++)i=i.concat(t[n].mr);return i},tf:function(t,i){var n=Math.PI/4,r=t/n,t=parseInt(r),r=r%1*n;return t%2==0?i<r&&(t+=1):n-i<=r&&(t+=1),t},if:function(t,i){var n=i.angleTo(t);return n=new Tn(i.x,i.z,i.y).cross(new Tn(t.x,t.z,t.y)).z<0?2*Math.PI-n:n},Bn:function(){var t={center:{x:0,y:0,z:0}};return this.Ga&&(t.center.x=this.Ga.scene.defCenX,t.center.y=this.Ga.scene.defCenY,t.center.z=this.Ga.scene.defCenZ),t},nf:function(t,i){var n=0;return i.level&&(n=this.zn(i.level).height),new Tn(i.x-t.x,t.y+n,-i.y+t.y)},rf:function(t){t=Lr[this.Ya][t];return t||""},zn:function(t){var i=null;if(!this.Ga)return i;for(var n=this.Ga.scene,i={level:t,height:0,gname:"",desc:""},r=0;r<n.layerGroups.length;r++){var e=n.layerGroups[r];if(e.gid===t){i.height=e.height,i.gname=e.gname,i.desc=e.desc;break}}return i},ef:function(t){if(-1!=t)switch(t){case 2:return" "+this.rf("stair_pure");case 3:return" "+this.rf("escalator_pure");case 1:return" "+this.rf("lift_pure");case 5:return" "+this.rf("ramp_pure");default:return""}return this.rf("stair")},sf:function(t,i){if(i==t.length-1)return{index:t[t.length-1].index};var n=new Tn(0,0,1),r=n;0!=i&&((r=t[i].clone().sub(t[i-1]).normalize()).y=0,r.z*=-1);var e=t[i+1].clone().sub(t[i]);e.y=0;var s=e.length(),e=e.normalize();e.z*=-1;n=this.tf(this.if(n,e),22.5*pt.tt()),r=this.if(r,e),e=this.tf(r,this.Za*pt.tt());return{relField:e,relDesc:this.rf(this.za[e]),relAngle:r*pt.it(),absField:n,absDesc:this.rf(this.Xa[n]),distance:s,index:t[i].index}},uf:function(t,i){for(var n=this,r=this.Bn().center,e=t.map(function(t,i){t=n.nf(r,t);return t.index=i,t}),s=[],o=0;o<e.length;o++){var u=this.sf(e,o);u.point=t[o],u.level=i,s.push(u)}for(var h=[s[0]],a=1;a<s.length;a++)1==a&&s[0].distance<=this.Va?(s[0].distance+=s[a].distance,s[0].relDesc=s[a].relDesc,s[0].relAngle=s[a].relAngle,s[0].absField=s[a].absField,s[0].absDesc=s[a].absDesc):s[a].distance<=this.Va||s[a].relAngle<=this.Wa||360-s[a].relAngle<=this.Wa?h[h.length-1].distance+=s[a].distance:h.push(s[a]);return h},hf:function(t,i){var n=[],r=0;if(i.forEach(function(t){t.uh&&n.push(r),r++}),0!=n.length)for(;0!=n.length;){for(var e=0;e<t.length-1;e++)if(t[e].index<=n[0]&&t[e+1].index>=n[0]){t[e+1].uh=!0;break}n.splice(0,1)}},af:function(t){var i="",t=this.zn(t);return null!=t&&(""+(i=this.Ya==a.ZH?t.desc:t.gname.toUpperCase())!="null"&&""+i!=""&&""+i!="undefined"||(i=t.gname.toUpperCase())),i},ff:function(t,i,n,r,e,s){var o,u,h=t[i],a=t[i+1],f=null,c=this.rf(this.qa[a.relField]);this.Ka=i==t.length-3&&a.distance<this.Ha,c&&!this.Ka||(r?(this.Ka=!1,c||(f=this.cf(t[i+1].point,e),o=this.ef(f),u=null,e=this.af(r),c=this.Ht==mn.Drive?(u=n<r?this.rf("drive_up"):this.rf("drive_down"),this.rf("along")+o+" "+u+" "+e):(u=n<r?this.rf("up_to"):this.rf("down_to"),this.rf("take")+o+" "+u+" "+e))):(this.Ka&&(this.Ja=a.distance),s||(c=this.rf("arrived"))));s=null,s=0==i?2==t.length?this.rf("face_to")+" "+h.absDesc+" "+this.rf("straight")+" "+Math.ceil(h.distance)+" "+this.rf("meter")+" "+c:this.rf("face_to")+" "+h.absDesc+" "+this.rf("straight")+" "+Math.ceil(h.distance)+" "+this.rf("meter")+" "+this.rf(this.qa[t[i+1].relField]):this.rf("straight")+" "+Math.ceil(h.distance+(this.Ka&&!r?a.distance:0))+" "+this.rf("meter")+" "+c;return a.uh&&(s+=" ",s+=this.rf("pass")),[s,f]},lf:function(t,i,n,r,e){var s=t[i],o=t[i+1];return this.rf(this.qa[t[i+1].relField])&&!this.Ka||(r?(n=n<r?this.rf("up"):this.rf("down"),o.relDesc=n):o.relDesc=e?this.rf("front_pure"):this.rf("dest")),{startPoint:s.point,startLevel:s.level,startIndex:s.index,endPoint:o.point,endLevel:o.level,endIndex:o.index,startDirection:s.absDesc,endDirection:o.relDesc,distance:s.distance+(this.Ka&&!r?o.distance:0)}},cf:function(t,i){for(var n,r=Number.MAX_SAFE_INTEGER,e=-1,s=0;s<i.length;s++)i[s]&&((n=Math.pow(i[s].zt.x-t.x,2)+Math.pow(i[s].zt.y-t.y,2))<r&&(r=n,e=i[s].Fr));return e},vf:function(t){for(var i,n,r=t.df,e=t.yf,s=t.result,o=[],u=[],h=[],a=null,f=0,c=[],l=!(this.Qa=[]),v=null,d=null,t=t.buildingName||this.rf("outdoor"),y=this.$a(s),b=0;b<s.length;b++){if(i=(n=s[b]).level,a=null,b<s.length-1)for(var p=b;p<s.length;p++)if(s[p].level!=i){a=s[p].level;break}var x,m,w=n.pointList,_=this.uf(w,i);if(this.hf(_,n.mr),1<_.length&&(this.Qa=this.Qa.concat(_)),1==_.length)(a||l)&&(l?u.length&&(b==s.length-1&&(e?(o[o.length-1]+=" "+this.rf("arrived"),u[u.length-1].endDirection=this.rf("dest")):u[u.length-1].endDirection=this.rf("front_pure")),u[u.length-1].endPoint=w[0],u[u.length-1].endLevel=i):v!=i&&null!=v||(x=null,m=this.af(a),d=this.Ht===mn.Drive?(x=i<a?this.rf("drive_up"):this.rf("drive_down"),this.rf("along_ramp")+" "+x+" "+m):(x=i<a?this.rf("up_to"):this.rf("down_to"),this.rf("take_stair")+" "+x+" "+m),o.push(d),u.push({startPoint:w[0],startDirection:i<a?this.rf("up"):this.rf("down"),startLevel:i})),l=!l);else for(var v=i,g=0;g<_.length-1&&!this.Ka;g++){var O=this.ff(_,g,i,a,y,e),E=O[0],O=O[1];O&&h.push(O),o.push(E),u.push(this.lf(_,g,i,a,e))}1<_.length&&(m=_.reduce(function(t,i){return t+(i.distance||0)},0),c.push({level:i,distance:m}),f+=m)}return r&&0<o.length&&(o[0]=this.rf("enter")+" "+t+" "+o[0]),e&&0<o.length&&(t===this.rf("outdoor")?o[o.length-1]=o[o.length-1]+this.rf("reach")+" "+e+" "+this.rf("entrance"):o[o.length-1]=o[o.length-1]+this.rf("reach")+" "+t+" "+this.rf("exit")),[o,u,f,c,this.Ja,h]}});var Dr=s;function Pr(t,i){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=Cr(t))||i&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var e,s=!0,o=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){o=!0,e=t},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw e}}}}function Fr(t){return function(t){if(Array.isArray(t))return Ur(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||Cr(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Cr(t,i){if(t){if("string"==typeof t)return Ur(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ur(t,i):void 0}}function Ur(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}E=function t(i){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.bf=i.gate,this.pf=i.bidToOutline,this.xf=i.bidToOutlineAccess,this.mf={},this.wf={},this._f=-1,this.gf=-1,this.Of=[],this.Ef=[],this.Mf=new Map,this.jf=new Map,this.kf=[],this.Rf=new Map,this.Tf=[],this.Af=new Map,this.Sf=new Map,this.Nf=new Map,this.Hr=null,this.Kr=null,this.If=null,this.Lf=null,this.Ht=i.type,this.Df=i.barrierFree,this.Pf=!1,this.Ff=null,this.Cf=10,this.Uf=2,this.Bf()};Object.assign(E.prototype,{Bf:function(){this.bf&&(this.Gf(),this.Zf(),this.Wf(this.Of,this.Sf),this.Wf(this.Ef,this.Nf))},Gf:function(){for(var t=0;t<this.bf.length;t++){var i=null,n=null,i=null==this.Ht||0===this.Ht?(n=this.bf[t].accessible,this.bf[t].navi):this.bf[t].drive,r=""==this.bf[t].bid?null:this.bf[t].bid,e=this.bf[t].gid,i=this.Vf(i,e,r),n=this.Vf(n,e,r);(e=this.kf).push.apply(e,Fr(i)),(e=this.Tf).push.apply(e,Fr(n)),null==this.mf[r]&&(this.mf[r]=[]),(e=this.mf[r]).push.apply(e,Fr(i)),null==this.wf[r]&&(this.wf[r]=[]),(r=this.wf[r]).push.apply(r,Fr(n))}for(var s=0;s<this.kf.length;s++)this.kf[s].Vi=s,this.Rf.set(s,this.kf[s]);for(var o=0;o<this.Tf.length;o++)this.Tf[o].Vi=o,this.Af.set(o,this.Tf[o])},Vf:function(t,i,n){if(null==t)return[];for(var r=[],e=0;e<t.length;e++){var s=t[e];s.level=i,s.bid=n,r.push(s)}return r},Zf:function(){this.Of=this.Yf(this.Rf);this.Ef=this.Yf(this.Af,!0);for(var t=0;t<this.Of.length;t++)this.Of[t].Vi=t,this.Mf.set(t,this.Of[t]);for(var i=0;i<this.Ef.length;i++)this.Ef[i].Vi=i,this.jf.set(i,this.Ef[i])},Yf:function(t,i){for(var n=[],r=Array.from(t.keys()),e=0;e<r.length;e++)for(var s=e+1;s<r.length;s++){var o,u=r[e],h=r[s],a=t.get(u),f=t.get(h);t.get(u).flag==t.get(h).flag&&(o=this.Xf(a,f),n.push(o)),t.get(u).bid==t.get(h).bid&&this.zf(a,f,i)&&(f=this.Xf(a,f),n.push(f))}return n},Xf:function(t,i){var n,r,e,s=0,o={};return o.startPoint=t.Vi,o.endPoint=i.Vi,o.Vi=null,t.bid==i.bid?(n={x:t.pts[0],y:t.pts[1]},r={x:i.pts[0],y:i.pts[1]},s=0!=(e=Math.abs(t.level-i.level)*this.Cf)||t.extentID!==i.extentID?(pt.distance(n,r)+e)*this.Uf:pt.distance(n,r)+e,o.dist=Number(s.toFixed(5))):o.dist=s,o},zf:function(t,i,n){var r=t.bid,e=this.qf(t),s=this.qf(i);return(t.extentID=e)==(i.extentID=s)||(n?this.xf:this.pf).get(r).Gs(e,s)===S.ROUTE_SUCCESS},qf:function(t){for(var i,n=null===(i=this.pf.get(t.bid).ks)||void 0===i?void 0:i.Re,r=0;r<n.length;r++){var e=n[r].Fs,s=n[r].br;if(t.level===s){s={x:t.pts[0],y:t.pts[1]};if(e.xe(s))return e.te}}return 0},Hf:function(t){this.Hr=this._f;for(var i,n=t.buildingID||null,r=[],e={type:null,pts:[t.x,t.y],bid:n,level:t.level,Vi:this.Hr},s=[],s=(this.Df?this.wf:this.mf)[n],o=0;o<s.length;o++)this.zf(e,s[o])&&((i=this.Xf(e,s[o]))&&(i.Vi=this.gf,r.push(i),this.Df?(this.Ef.push(i),this.jf.set(this.gf,i)):(this.Of.push(i),this.Mf.set(this.gf,i)),--this.gf));this.Df?(this.Tf.push(e),this.Af.set(this.Hr,e),this.Wf(r,this.Nf)):(this.kf.push(e),this.Rf.set(this.Hr,e),this.Wf(r,this.Sf)),--this._f},Qf:function(t){this.Kr=this._f;var i=t.buildingID||null,n=[],r={type:null,pts:[t.x,t.y],bid:i,level:t.level,Vi:this.Kr},e=[],e=(this.Df?this.wf:this.mf)[i];this.kf.push(r),this.Rf.set(this.Kr,r);for(var s,o=0;o<e.length;o++)this.zf(r,e[o])&&((s=this.Xf(r,e[o]))&&(s.Vi=this.gf,n.push(s),this.Df?(this.Ef.push(s),this.jf.set(this.gf,s)):(this.Of.push(s),this.Mf.set(this.gf,s)),--this.gf));this.Df?(this.Tf.push(r),this.Af.set(this.Kr,r),this.Wf(n,this.Nf)):(this.kf.push(r),this.Rf.set(this.Kr,r),this.Wf(n,this.Sf)),--this._f},Wf:function(t,i){for(var n=0;n<t.length;n++){var r=t[n],e=r.startPoint,s=r.endPoint;i.has(e)||i.set(e,new Map),i.get(e).set(s,r.dist),i.has(s)||i.set(s,new Map),i.get(s).set(e,r.dist)}return i},Kf:function(t,i,n){var r=null,e=null;if(this.Df){if(!this.Nf.has(t))return!1;for(var s=0;s<i.length;s++)if(!this.Nf.has(i[s]))return!1;r=this.Af,e=this.Nf}else{if(!this.Sf.has(t))return!1;for(var o=0;o<i.length;o++)if(!this.Sf.has(i[o]))return!1;r=this.Rf,e=this.Sf}this.Vs(i,t);var u,h=i.length,a=0,f=Pr(r.keys());try{for(f.s();!(u=f.n()).done;){var c=u.value;r.get(c).Ir=c==t?0:Number.MAX_VALUE,r.get(c).Lr=!1,r.get(c).Dr=[]}}catch(t){f.e(t)}finally{f.f()}var l=[];for(l.push(t);0<l.length;){var v=l[0];l.splice(0,1);var d=r.get(v);if(!d.Lr){d.Lr=!0;var y=i.indexOf(v);if(-1!=y&&++a==h)return!0;var b=e.get(v);if(b){var p,x=Pr(b.keys());try{for(x.s();!(p=x.n()).done;){var m=p.value,w=d.Ir+b.get(m),_=r.get(m);n instanceof Array&&n.includes(m)||_.Lr||_.Ir>w&&(_.Ir=w,_.Dr=[],_.Dr.push(v),l.push(m))}}catch(t){x.e(t)}finally{x.f()}l.sort(function(t,i){r.get(t).Ir,r.get(i).Ir})}}}return!(a<h)},Vs:function(t,i){for(var n=t.indexOf(i);-1!=n;)t.splice(n,1),n=t.indexOf(i)},Cs:function(){if(this.Df){if(!this.Nf.has(this.Hr))return S.ROUTE_FAILED_CANNOT_CALCULATE;if(!this.Nf.has(this.Kr))return S.ROUTE_FAILED_CANNOT_CALCULATE}else{if(!this.Sf.has(this.Hr))return S.ROUTE_FAILED_CANNOT_CALCULATE;if(!this.Sf.has(this.Kr))return S.ROUTE_FAILED_CANNOT_CALCULATE}var t=[];return t.push(this.Kr),this.Kf(this.Hr,t)?S.ROUTE_SUCCESS:S.ROUTE_FAILED_CANNOT_ARRIVE},Zs:function(){var t=[],i=[];i.push(this.Kr),this.Ws(i,t);for(var n=0;n<t.length;n++)t[n].paths.reverse();return t},Ws:function(t,i){for(var n=0;n<t.length;n++){var r={paths:[],distance:0};r.paths.push(t[n]),this.Xs(t[n],r),i.push(r)}},Xs:function(t,i){var n=null,r=null;if(this.Df){if(0==this.Af.get(t).Dr.length)return;n=this.Af.get(t).Dr[0],r=this.Af.get(t).Ir}else{if(0==this.Rf.get(t).Dr.length)return;n=this.Rf.get(t).Dr[0],r=this.Rf.get(t).Ir}null!=n&&(i.paths.push(n),i.distance+=r),this.Xs(n,i)},Jf:function(t){var i,n=new Map,r=Pr(t.keys());try{for(r.s();!(i=r.n()).done;){var e=i.value,s=pt.Nt(t.get(e));s.bid=e;for(var o=0,u=0;u<t.get(e).length;u++)o+=t.get(e)[u].level;o=Math.floor(o/t.get(e).length),s.level=o,n.set(e,s)}}catch(t){r.e(t)}finally{r.f()}return n},$f:function(t){var i,n=null,r=null,e=[],s=Pr(t.keys());try{for(s.s();!(i=s.n()).done;){var o=i.value,u=this.tc(t.get(o));u.bid!=this.If?u.bid!=this.Lf?e.push(u):r=u:n=u}}catch(t){s.e(t)}finally{s.f()}return null!=n&&e.unshift(n),null!=r&&e.push(r),e},tc:function(t){for(var i,n=t.bid||null,r=[],e={type:null,pts:[t.x,t.y],bid:t.bid,level:t.level,Vi:this._f},s=[],s=(this.Df?this.wf:this.mf)[n],o=0;o<s.length;o++)this.zf(e,s[o])&&((i=this.Xf(e,s[o]))&&(i.Vi=this.gf,r.push(i),this.Df?(this.Ef.push(i),this.jf.set(this.gf,i)):(this.Of.push(i),this.Mf.set(this.gf,i)),--this.gf));return this.Df?(this.Tf.push(e),this.Af.set(this._f,e),this.Wf(r,this.Nf)):(this.kf.push(e),this.Rf.set(this._f,e),this.Wf(r,this.Sf)),--this._f,e},ic:function(){if(!this.bf)return null;var t=null;if(this.Pf){for(var i=this.Jf(this.Ff),n=this.$f(i),r=[],e=1;e<n.length-1;e++)r.push(n[e].Vi);var s=n[0].Vi,o=[s],u=[],i=n[n.length-1].Vi;if(this.nc(s,r,o,u),this.nc(o[o.length-1],[i],o,u),o.length==n.length)return t=this.rc(u)}return t},rc:function(t){for(var i=[],n=-1,r=[],e=0;e<t.length;e++)for(var s,o=t[e].paths,u=null,h=0;h<o.length;h++)0<o[h]&&(s=o[h],u=this.Rf.get(s),-1==n&&(n=u.bid),u.bid==n?r.push(u):(i.push({bid:n,entrances:r}),r=[u]),n=u.bid),e==t.length-1&&h==o.length-1&&u&&i.push({bid:u.bid,entrances:r});return i},nc:function(t,i,n,r){if(0!=i.length){for(var e,s=null,o=Number.MAX_VALUE,u=null,h=0,a=0;a<i.length;a++)this.Kf(t,[i[a]])&&(this.Xs(i[a],e={paths:[],distance:0}),e.distance<o&&(o=e.distance,s=i[a],u=e,h=a));s&&(n.push(s),u.paths.reverse(),r.push(u),i.splice(h,1),this.nc(s,i,n,r))}},Un:function(){this.kf=this.kf.filter(function(t){return 0<=t.Vi}),this.Tf=this.Tf.filter(function(t){return 0<=t.Vi}),this.Of=this.Of.filter(function(t){return 0<=t.Vi}),this.Ef=this.Ef.filter(function(t){return 0<=t.Vi});var t,i=Pr(this.Rf.keys());try{for(i.s();!(t=i.n()).done;){var n=t.value;n<0&&this.Rf.delete(n)}}catch(t){i.e(t)}finally{i.f()}var r,e=Pr(this.Af.keys());try{for(e.s();!(r=e.n()).done;){var s=r.value;s<0&&this.Af.delete(s)}}catch(t){e.e(t)}finally{e.f()}var o,u=Pr(this.Mf.keys());try{for(u.s();!(o=u.n()).done;){var h=o.value;h<0&&this.Mf.delete(h)}}catch(t){u.e(t)}finally{u.f()}var a,f=Pr(this.jf.keys());try{for(f.s();!(a=f.n()).done;){var c=a.value;c<0&&this.jf.delete(c)}}catch(t){f.e(t)}finally{f.f()}var l,v=Pr(this.Sf.keys());try{for(v.s();!(l=v.n()).done;){var d=l.value;if(d<0)this.Sf.delete(d);else{var y,b=Pr(this.Sf.get(d).keys());try{for(b.s();!(y=b.n()).done;){var p=y.value;p<0&&this.Sf.get(d).delete(p)}}catch(t){b.e(t)}finally{b.f()}}}}catch(t){v.e(t)}finally{v.f()}var x,m=Pr(this.Nf.keys());try{for(m.s();!(x=m.n()).done;){var w=x.value;if(w<0)this.Nf.delete(w);else{var _,g=Pr(this.Nf.get(w).keys());try{for(g.s();!(_=g.n()).done;){var O=_.value;O<0&&this.Nf.get(w).delete(O)}}catch(t){g.e(t)}finally{g.f()}}}}catch(t){m.e(t)}finally{m.f()}this._f=-1,this.gf=-1}});var Br=E;function Gr(t){return function(t){if(Array.isArray(t))return Vr(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||Wr(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Zr(t,i){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=Wr(t))||i&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var e,s=!0,o=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){o=!0,e=t},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw e}}}}function Wr(t,i){if(t){if("string"==typeof t)return Vr(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Vr(t,i):void 0}}function Vr(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}A=function t(){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.ii=null,this.Qr=null,this.Jr=null,this.Ns=null,this.xh=null,this.Ht=null,this.mh=new Map,this.ec=new Map,this.Hi=S.ROUTE_FAILED_CANNOT_ARRIVE,this.sc=[],this.oc=null,this.uc=[],this.Yh=null,this.qh=[],this.Xh=!1,this.barrierFree=!1,this.Jh=[],this.hc=null};Object.assign(A.prototype,{er:function(t){this.ii=t,this.Ht=t.type,this.hc=t.bid||null;var i,n=new Map,r=Zr(this.ec.keys());try{for(r.s();!(i=r.n()).done;){var e=i.value;n.set(e,this.ec.get(e)._h)}}catch(t){r.e(t)}finally{r.f()}var s,o=new Map,u=Zr(this.ec.keys());try{for(u.s();!(s=u.n()).done;){var h=s.value;o.set(h,this.ec.get(h).gh)}}catch(t){u.e(t)}finally{u.f()}t={gate:this.ii.gate,bidToOutline:n,bidToOutlineAccess:o,type:this.Ht,barrierFree:this.barrierFree};this.ac=new Br(t)},ua:function(t,i,n,r){this.Un(),t.buildingID=void 0!==t.buildingID?t.buildingID:this.hc,i.buildingID=void 0!==i.buildingID?i.buildingID:this.hc,this.Qr=t,this.Jr=i,this.Ns=n,this.xh=r;var e=[];if(this.Qr.buildingID==this.Jr.buildingID){var s,n={pts:[t.x,t.y],bid:t.buildingID||null,level:t.level},r={pts:[i.x,i.y],bid:i.buildingID||null,level:i.level};if(this.Hi=this.fc(s=[[n,r]]),this.Hi==S.ROUTE_SUCCESS)return this.Hi;if(null==this.ac)return this.Hi;s=this.cc(t,i)}else s=this.cc(t,i);if(0==s.length)return this.Hi=S.ROUTE_FAILED_ENTRANCE,this.Hi;for(var o=0;o<s.length;o++)e.push(s[o][0].bid);i=this.fc(s);return this.Hi=i,this.Hi},Un:function(){this.Hi=S.ROUTE_FAILED_CANNOT_ARRIVE,this.sc=[],this.oc=null,this.uc=[]},cc:function(t,i){var n=[];if(this.lc(t,i)===S.ROUTE_SUCCESS)for(var r=this.vc()[0],e=[],s=0;s<r.length-1;s++){var o=r[s],u=r[s+1];0===e.length&&e.push(o),o.bid===u.bid?(e.push(u),s==r.length-2&&n.push(e)):(n.push(e),e=[])}return n},fc:function(t){var i,n=new Map,r=new Map;null!==(i=this.ac)&&void 0!==i&&i.mf&&(r=this.ac.mf);for(var e=0;e<t.length;){var s={};s.x=t[e][0].pts[0],s.y=t[e][0].pts[1],s.level=t[e][0].level;var o=t[e][0].Vi,u={};u.x=t[e][1].pts[0],u.y=t[e][1].pts[1],u.level=t[e][1].level;var h,a=t[e][1].Vi,f=t[e][0].bid,c=this.ec.get(f);0===e||(h=c.pa(s))&&h.road&&(s=h.coords),e===t.length-1||(l=c.pa(u))&&l.road&&(u=l.coords);var l=this.dc(s,u,f);if(l===S.ROUTE_SUCCESS){var v=this.yc(f);this.uc.push({bid:f,result:v}),e+=1}else{s=r[f];if(1==t.length)return l;if(e==t.length-1){u=t[e-1][0].Vi,v=a;n[f]=n[f]||[],n[f].push(o);var d=n[f];if(d.length===s.length)return l;d=this.bc(u,[v],d);if(d.length<=1)return l;t.splice(e,1),t.splice(e-1,1),t.splice.apply(t,[e-1,0].concat(Gr(d))),e-=1}else{d=o,o=t[e+1][1].Vi;n[f]=n[f]||[],n[f].push(a);f=n[f];if(f.length===s.length)return l;f=this.bc(d,[o],f);if(f.length<=1)return l;t.splice(e+1,1),t.splice(e,1),t.splice.apply(t,[e,0].concat(Gr(f)))}this.uc.pop()}}return S.ROUTE_SUCCESS},bc:function(t,i,n){var r=[],e=[],s=[],o=[];if(!this.ac.Kf(t,i,n))return o;this.ac.Ws(i,r);for(var u=0;u<r.length;u++)r[u].paths.reverse();for(var h=0;h<r.length;h++){for(var a=[],f=0;f<r[h].paths.length;f++)a.push(this.ac.Rf.get(r[h].paths[f]));e.push(a)}for(var c=e[0],l=0;l<c.length-1;l++){var v=c[l],d=c[l+1];0===s.length&&s.push(v),v.bid===d.bid?(s.push(d),l==c.length-2&&o.push(s)):(o.push(s),s=[])}return o},dc:function(t,i,n){return this.ec.get(n=void 0===n?null:n).ua(t,i,this.Ns,this.xh)},pc:function(){return this.uc},xc:function(t){for(var i=[],n=0;n<t.length;n++){var r=this.yc(t[n]);i.push({bid:t[n],result:r})}return i},yc:function(t){var i=this.ec.get(t=void 0===t?null:t),t=i.va();return this.Yh=i.Yh,t},lc:function(t,i){return this.ac.Un(),this.ac.Hf(t),this.ac.Qf(i),this.ac.Cs()},vc:function(){for(var t=this.ac.Zs(),i=[],n=0;n<t.length;n++){for(var r=[],e=0;e<t[n].paths.length;e++)r.push(this.ac.Rf.get(t[n].paths[e]));i.push(r)}return i},pa:function(t,i){var n=void 0!==t.buildingID?t.buildingID:this.hc,i=this.ec.get(n).pa(t,i);return i&&void 0!==t.buildingID&&(i.coords.buildingID=t.buildingID),i},Wo:function(t){for(var i=new Map,n=0;n<t.length;n++){var r=t[n],e=i.get(r.hc);e||i.set(r.hc,e=[]),e.push(r)}var s,o=Zr(i.keys());try{for(o.s();!(s=o.n()).done;){var u=s.value;this.ec.get(u).Wo(t)}}catch(t){o.e(t)}finally{o.f()}},Fo:function(){for(var t in this.qh){var i=parseInt(t),i=this.ec.get(i);i.qh=this.qh[t],i.Fo();t=i.va();(i=this.uc).push.apply(i,Gr(t))}},na:function(t,i,n,r,e){this.Qr=t,this.Jr=i,this.Ns=n,this.xh=r,this.Jh.unshift(t),this.Jh.push(i);for(var s=0;s<this.Jh.length;s++)this.Jh[s].buildingID=void 0!==this.Jh[s].buildingID?this.Jh[s].buildingID:this.hc;return void 0!==e?this.mc(passPts):(r=this.wc(this.Jh),e=null,this.ac&&(this.ac.Ff=r,this.ac.Pf=!0,this.ac.If=t.buildingID,this.ac.Lf=i.buildingID,e=this.ac.ic()),this._c(t,i,e,this.Jh))},wc:function(t){for(var i=new Map,n=0;n<t.length;n++){var r=t[n].buildingID||null;i.has(r)||i.set(r,[]),i.get(r).push(t[n])}return i},_c:function(t,i,n,r){var e=[],s=null;if(r.shift(),r.pop(),n)for(var o=this.wc(r),u=0;u<n.length;u++){var h,a,f,c,l=n[u].bid;if(0==u?(a={x:n[u].entrances[0].pts[0],y:n[u].entrances[0].pts[1],level:n[u].entrances[0].level},o.has(l)?(h=o.get(l),s=this.gc(t,a,l,h),o.delete(l)):s=this.dc(t,a,l)):u==n.length-1?(c={x:n[u].entrances[0].pts[0],y:n[u].entrances[0].pts[1],level:n[u].entrances[0].level},o.has(l)?(f=o.get(l),s=this.gc(c,i,l,f),o.delete(l)):s=this.dc(c,i,l)):(a={x:n[u].entrances[0].pts[0],y:n[u].entrances[0].pts[1],level:n[u].entrances[0].level},f={x:n[u].entrances[1].pts[0],y:n[u].entrances[1].pts[1],level:n[u].entrances[1].level},o.has(l)?(c=o.get(l),s=this.gc(a,f,l,c),o.delete(l)):s=this.dc(a,f,l)),s!==S.ROUTE_SUCCESS)break;d=this.yc(l),e.push({bid:l,result:d})}else{var v=t.buildingID||null,s=this.gc(t,i,v,r),d=this.yc(v);e.push({bid:v,result:d})}return this.uc=e,this.Hi=s},mc:function(t){for(var i=null,n=null,r=0;r<t.length-1;r++)if(t[r].buildingID==t[r+1].buildingID){var e=t[r].buildingID;if((n=this.dc(t[r],t[r+1],e))!=S.ROUTE_SUCCESS)return n;e={bid:e,result:this.yc(e)},i.push(e)}else{var s=this.cc(this.Jh[r],this.Jh[r+1]);if((n=this.fc(s))!=S.ROUTE_SUCCESS)return n;for(var o=[],u=0;u<s.length;u++)o.push(s[u][0].bid);e=this.xc(o),i.push.apply(i,Gr(e))}return this.uc=i,this.Hi=n},gc:function(t,i,n,r){n=this.ec.get(n);n.Jh=r;i=n.na(t,i);return n.Jh=null,i},ei:function(){this.ii=null,this.uc=null,this.sc=null,this.oc=null;var t,i=Zr(this.ec.keys());try{for(i.s();!(t=i.n()).done;){var n=t.value;this.ec.get(n).ei()}}catch(t){i.e(t)}finally{i.f()}}});var Yr=A;function Xr(t,i){var n;if("undefined"==typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=qr(t))||i&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var e,s=!0,o=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return s=t.done,t},e:function(t){o=!0,e=t},f:function(){try{s||null==n.return||n.return()}finally{if(o)throw e}}}}function zr(t){return function(t){if(Array.isArray(t))return Hr(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||qr(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qr(t,i){if(t){if("string"==typeof t)return Hr(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Hr(t,i):void 0}}function Hr(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}function Qr(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}r=function(){function r(t,i,n){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,r),this.ii=Object.assign({tile:!0,isPreview:!1},t),this.Oc=new Map,this.Ec=null,this.Mc=[],this.jc=[],this.ac=null,this.kc=void 0!==t.mapID?t.mapID:null,this.Rc=null,this.Kn=!!t.map,this.Jn=t.map?t.map.getDataManager():new sn,this.Tc=1,this.Ac={radius:.5,dash:{size:4,gap:2}},this.ir=i,this.nr=n,this.Ht=t.type,this.Sc=new Map,this.Nc=new Map,this.rr=dn.generateUUID(),this.Ic=null,this.er()}var t,i,n;return t=r,(i=[{key:"setObstructions",value:function(t){this.Ec&&this.Ec.Wo(t)}},{key:"route",value:function(t,i,n){this.Ec.toDoors=void 0!==t.toDoors&&t.toDoors,this.Ec.obstruct=void 0===t.obstruct||t.obstruct;var r=S.ROUTE_FAILED_CANNOT_ARRIVE;if((r=t.viapoints&&0<t.viapoints.length?(this.Ec.Jh=t.viapoints,this.Ec.na(t.start,t.dest,t.mode||g.MODULE_SHORTEST,t.priority||m.PRIORITY_DEFAULT)):this.Ec.ua(t.start,t.dest,t.mode||g.MODULE_SHORTEST,t.priority||m.PRIORITY_DEFAULT))===S.ROUTE_SUCCESS){this.Mc=this.Ec.pc();var e=null,s=null;this.jc=[];for(var o=0;o<this.Mc.length;o++)0<o&&(e=this.ii.bidToName.get(this.Mc[o-1].bid)?this.ii.bidToName.get(this.Mc[o-1].bid):"outdoor"),o<this.Mc.length-1&&(s=this.ii.bidToName.get(this.Mc[o+1].bid)?this.ii.bidToName.get(this.Mc[o+1].bid):"outdoor"),this.Mc[o].df=e,this.Mc[o].yf=s,s=e=null,this.Mc[o].buildingName=this.ii.bidToName.get(this.Mc[o].bid),this.jc.push([this.Lc(this.Mc[o],{language:a.ZH}),this.Lc(this.Mc[o],{language:a.EN})]);this.Rc=this.Ec.Yh,i&&i(this.dr())}else n&&n(r)}},{key:"dispose",value:function(){this.Dc=null,this.Mc=[],this.jc=[],this.Ec&&(this.Ec.ei(),this.Ec=null)}},{key:"pathConstraint",value:function(t,i){return this.Ec?this.Ec.pa(t,i):null}},{key:"getNaviGraph",value:function(t){var i=t.buildingID,t=t.level;return null!=i?null!=t?this.Ec.mh.get(i).get(t):this.Ec.mh.get(i):null!=t?this.Ec.mh.get(null).get(t):zr(this.Ec.mh.values())}},{key:"query",value:function(t,i,n,r){var e=[],s=null,s=n?this.Ec.ec.get(n):this.Ec.ec.get(null),o=r?s.wh:s.mh;if(!o)return e;if(null==t){var u,h=Xr(o.keys());try{for(h.s();!(u=h.n()).done;){var a=u.value;e.push({level:a,roadRanks:i,roadVector:o.get(a).Te.slice(0)})}}catch(t){h.e(t)}finally{h.f()}}else if(null==i)e.push({level:t,roadRanks:i,roadVector:o.get(t).Te.slice(0)});else{for(var f=[],c=o.get(t).Te,l=0;l<i.length;l++)for(var v=0;v<c.length;v++)c[v].re===i[l]&&f.push(c[v]);e.push({level:t,roadRanks:i,roadVector:f})}for(var d=0;d<e.length;d++)for(var y=0;y<e[d].roadVector.length;y++)e[d].roadVector[y]=Object.assign({},this.Pc(e[d].roadVector[y])),n&&(e[d].roadVector[y].buildingID=n);return e}},{key:"intersectionRoadsByCircle",value:function(t,i,n){if(!n||!n.roadVector||n.roadVector.length<=0)return null;var r,e=null==n.roadVector[0].buildingID?null:n.roadVector[0].buildingID,s={center:t,radius:i,points:[]},o=t.x-i,u=t.x+i,h=t.y-i,a=t.y+i,f=[],c=new Map,l=Xr(n.roadVector);try{for(l.s();!(r=l.n()).done;){var v,d=r.value,y=pt.It(t,i,d.startPoint,d.endPoint);if(!(y.length<1)){var b=d.roadEntry,p=d.endId;if(pt.distance(d.endPoint,t)>i&&((b=b===xn.Entry_FORWARD?wn.Entry_BACK:b)===xn.Entry_BACK&&(b=wn.Entry_FORWARD),p=d.startId),b===wn.Entry_FORWARD||b===wn.Entry_BOTH){var x,m=Xr(y);try{for(m.s();!(x=m.n()).done;){var w=x.value;w.direction=b,w.Fc=p}}catch(t){m.e(t)}finally{m.f()}(v=s.points).push.apply(v,zr(y)),d.startPoint.x>=o&&d.startPoint.x<=u&&d.startPoint.y>=h&&d.startPoint.y<=a&&f.push(d.startId),d.endPoint.x>=o&&d.endPoint.x<=u&&d.endPoint.y>=h&&d.endPoint.y<=a&&f.push(d.endId)}}}}catch(t){l.e(t)}finally{l.f()}for(var _=n.level,g=(null===(n=this.Ec.ec)||void 0===n?void 0:n.get(e)).mh.get(_).De,O=0,E=f;O<E.length;O++){var M=E[O];c.set(M,g.get(M))}var j,k=Xr(s.points);try{for(k.s();!(j=k.n()).done;){var R=j.value;R.branch=this.Cc(c,R.Fc,t.nodeID)}}catch(t){k.e(t)}finally{k.f()}return s}},{key:"queryNode",value:function(t){var i=t.buildingID?this.Ec.ec.get(buildingID):this.Ec.ec.get(null);if(null==i)return null;var n=(i=(t.barrierFree?i.wh:i.mh).get(t.level)).Ze(t.point);if(n<=0)return null;for(var r=i.Te,e=new Set,s=0;s<r.length;s++)if(n===r[s].te){if(!e.has(r[s].Hr)&&pt.Uc(t.point,r[s].Qr))return t.nodeID=r[s].Hr,t;if(!e.has(r[s].Kr)&&pt.Uc(t.point,r[s].Jr))return t.nodeID=r[s].Kr,t;e.add(r[s].Hr),e.add(r[s].Kr)}return null}},{key:"constraintTrack",value:function(t){var i=[];if(null==t||t.length<=0)return i;for(var n=new Map,r=0;r<t.length;r++){var e=void 0===t[r].coords.buildingID?null:t[r].coords.buildingID;n.has(e)?n.get(e).push(t[r]):n.set(e,[t[r]])}if(n.size<=0)return i;var s,o=Xr(n.keys());try{for(o.s();!(s=o.n()).done;){for(var u=s.value,h=n.get(u),a=new Set,f=new Set,c=0;c<h.length;c++)a.add(h[c].coords.level),f.add(h[c].road.roadRank);if(a.size<=0)return i;var l=Array.from(a),v=Array.from(f);this.Bc(l,v,u)}}catch(t){o.e(t)}finally{o.f()}for(var d=t.length-1,y=0;y<d;y++){var b=this.Gc(t[y],t[y+1]);i.push(t[y].coords),i.push.apply(i,zr(b)),y+1==d&&i.push(t[y+1].coords)}return i}}])&&Qr(t.prototype,i),n&&Qr(t,n),r}();Object.assign(r.prototype,{er:function(){this.Kn&&(t=this.ii.map.getMapOptions(),this.ii.mapID=t.mapID,this.ii.appName=t.appName,this.ii.key=t.key,this.ii.license=t.license,this.ii.mapURL=t.mapURL,this.ii.mapURLAbsolute=t.mapURLAbsolute,this.ii.navigationData=t.navigationData,this.ii.buildingOptions=t.buildingOptions,this.ii.type=this.Ht);var t={buildingID:this.ii.mapID,appName:this.ii.appName,key:this.ii.key,license:this.ii.license,mapURL:this.ii.mapURL,mapURLAbsolute:this.ii.mapURLAbsolute,buildingOptions:this.ii.buildingOptions,navigationData:this.ii.navigationData,merge:this.ii.merge,loadNavi:!0,justDecode:!0,preLoad:!0,isPreview:"undefined"!=typeof KjWzhWlSj&&!!KjWzhWlSj.ProtoDef,tile:this.ii.tile,type:this.ii.type,bid:this.ii.buildingID||null,gate:null,isOutdoor:!0,uuid:this.rr};this.kc||(this.kc=this.ii.mapID),this.Oc.set(t.bid,t),this.ii.bidToMid=new Map,this.ii.bidToName=new Map,this.ii.bidToMid.set(t.bid,this.kc),this.Ec=new Yr,this.hr(t)},hr:function(n){var r=this;setTimeout(function(){n.tile?r.Jn.load(n,function(t,i){r.ar(t,i,n)}):r.Jn.loadWhole(n,function(t,i){r.ar(t,i,n)})},0)},ar:function(t,i,n){if("error"===t)this.nr&&this.nr({data:i});else if("scene"===t&&"undefined"!==i.naviType&&0!==i.naviType){var r=i.naviType;1===r&&this.nr&&this.nr({RoadNetworkResult:c.NONE_RODE_NETWORK}),8<=r&&(r-=8),4<=r?r-=4:this.Ht===mn.Drive&&this.nr&&this.nr({RoadNetworkResult:c.NONE_DRIVE_RODE_NETWORK}),r<=0&&this.Ht===mn.Walk&&this.nr&&this.nr({RoadNetworkResult:c.NONE_WALK_RODE_NETWORK})}else if("decode"===t){t=new Ir(n);if(t.er(i),this.Ec.ec.set(n.bid,t),this.Ec.mh.set(n.bid,t.mh),i.scene.mid===this.kc){i.gate&&(this.Ic=i.gate.floors);for(var e=0;e<(null===(o=i.scene.buildings)||void 0===o?void 0:o.length);e++){var s=i.scene.buildings[e],o={mapID:this.ii.mapID,buildingID:null,appName:this.ii.appName,key:this.ii.key,license:this.ii.license,mapURL:this.ii.mapURL,mapURLAbsolute:this.ii.mapURLAbsolute,merge:this.ii.merge,loadNavi:!0,justDecode:!0,preLoad:!0,isPreview:"undefined"!=typeof KjWzhWlSj,tile:this.ii.tile,type:this.ii.type,bid:null,isOutdoor:!1,uuid:this.rr};o.buildingID=s.mid,o.bid=s.bid;o=Object.assign({},o);this.Oc.set(s.bid,o),this.ii.bidToMid.set(s.bid,s.mid),this.ii.bidToName.set(s.bid,s.name),this.hr(o)}}this.Ec.ec.size===this.ii.bidToMid.size&&(null!=this.Ic&&this.Ec.er(Object.assign(this.ii,{gate:this.Ic})),this.ir&&this.ir())}},dr:function(){for(var t={buildings:[],distance:0,subs:[],viapoints:[]},i=0;i<this.Mc.length;i++){var n={buildingID:null,buildingName:null,levels:[],distance:0},r=this.Mc[i].bid,e=this.Mc[i].buildingName,s=this.Zc(this.Mc[i],this.jc[i],r);n.levels=s.levels,n.distance=s.distance,t.distance+=n.distance,i!==this.Mc.length-1&&(s.subs[s.subs.length-1].transfer=f.TRANSFER_ENTRANCE),n.buildingID=r==this.kc?null:r,n.buildingName=e||null;var o,u=Xr(s.subs);try{for(u.s();!(o=u.n()).done;){var h=o.value;h.buildingID=n.buildingID,h.buildingName=n.buildingName}}catch(t){u.e(t)}finally{u.f()}t.buildings.push(n),(e=t.subs).push.apply(e,zr(s.subs)),(e=t.viapoints).push.apply(e,zr(s.viapoints))}return t},Zc:function(t,i,n){if(0==t.length||0==i.length)return null;for(var r={viapoints:[],subs:[],levels:[],distance:0},e=i[0].descriptions,s=i[0].descriptionsData,o=i[1].descriptions,u=i[1].descriptionsData,h=i[0].ladderList,a=0,f=[],c=!1,l=[],v=0;v<s.length;v++){var d={instruction:{zh:null,en:null},waypoint:{points:null,direction:{zh:null,en:null}},viapoint:null,levels:null,distance:Number.NaN,buildingID:null},y=e[v],b=s[v],p=o[v],x=u[v];d.instruction.zh=y,d.instruction.en=p,d.levels=[b.startLevel,b.endLevel],d.distance=void 0!==b.distance?b.distance:0,d.waypoint.direction.zh=[b.startDirection,b.endDirection||b.startDirection],d.waypoint.direction.en=[x.startDirection,x.endDirection||x.startDirection],d.levels[0]!=d.levels[1]?(c=!0,f.push.apply(f,zr(d.levels)),d.transfer=h[a],a+=1):0!=f.length&&f.slice(-1)[0]==d.levels[0]&&!c||(f.push(d.levels[0]),c=!1);b=this.Wc([b.startIndex,b.endIndex],f,t.result);d.waypoint.points=b.points,b.via&&-1==l.indexOf(b.via)&&(d.viapoint=Object.assign({},b.via.zt),r.viapoints.push(Object.assign({},b.via.zt)),l.push(b.via)),r.distance+=d.distance,d.buildingID=n,r.subs.push(d)}for(var m=0;m<f.length;m++)0!=r.levels.length&&r.levels.slice(-1)[0]==f[m]||r.levels.push(f[m]);return r.door=this.Rc,r},Wc:function(t,i,n){for(var r=[],e=[],s=null,o=[],u=0;u<n.length;u++){var h=n[u];if(o.push(h.br),i.toString()==o.toString()){var a=i[i.length-1],f=h.xr,c=h.mr;if(void 0===t[0]||void 0===t[1])r.push(Object.assign({level:h.br},h.xr[0])),e.push(h.mr[0]),h=n[u-1],r.unshift(Object.assign({level:h.br},h.xr[0])),e.unshift(h.mr[0]);else for(var l=t[0];l<=t[1];l++)r.push({x:f[l].x,y:f[l].y,level:a}),e.push(c[l]);break}}return e.forEach(function(t){t.uh&&(s=t)}),{points:r,via:s}},Lc:function(t,i){var n=t.bid,r=t.result;if(!this.Vc(r))return{};i=new Dr(Object.assign({data:this.Jn.getDecode(this.Oc.get(n)),directionAssert:10,combineAsset:10,language:a.ZH},i)).vf(t),t=this.Yc(i[5]);return{descriptions:i[0],descriptionsData:i[1],distance:i[2],distances:i[3],ladderList:t}},Yc:function(t){for(var i=0;i<t.length;i++)switch(t[i]){case 1:t[i]=f.TRANSFER_LIFT;break;case 2:t[i]=f.TRANSFER_STAIR;break;case 3:t[i]=f.TRANSFER_ESCALATOR;break;case 4:t[i]=f.TRANSFER_ACCESSIBLE;break;case 5:t[i]=f.TRANSFER_RAMP;break;default:return}return t},Xc:function(t){this.Ec.qh=t,this.Ec.Fo(),this.Mc=this.zc.va()},qc:function(){return this.Mc},Hc:function(t){var i=t||this.Mc;if(!this.Vc(i))return[];var n,r=[];for(n in i)i[n].getPointList&&r.push.apply(r,zr(i[n].pointList));return r},Qc:function(t){var i=t||this.Mc;if(!this.Vc(i))return[];for(var n=[],r=0;r<i.length;r++){for(var e=i[r],s=e.level,o=e.pointList,u={level:s,points:[]},h=0;h<o.length;h++)u.points.push(Object.assign({},o[h]));n.push(u)}return n},Kc:function(t){t=t||this.Mc;if(!this.Vc(t))return 0;var i=this.Hc(t);if(0==i.length)return 0;for(var n=0,r=0;r<i.length-1;r++)n+=pt.vt(i[r],i[r+1]);return n},Jc:function(t){var i=t||this.Mc;if(!this.Vc(i))return[];var n,r=[];for(n in i)!i[n].level||0!=n&&r[r.length-1]==i[n].level||r.push(i[n].level);return r},$c:function(t,i){var n=i||this.Mc;if(!this.Vc(n))return[];var r,e=[];for(r in n)n[r].level&&n[r].level==t&&(e=e.concat(n[r].pointList));return e},Vc:function(t){if(!Array.isArray(t))return!1;if(0==t.length)return!1;for(var i=0;i<t.length;i++)if(!(t[i]instanceof gn))return!1;return!0},tl:function(s,t){if(0===s.length)return null;null==t&&(t={});var i=Object.assign({},this.Ac);t&&t.style&&Object.assign(i,t.style),1===this.Ht&&(i.color="#002FA7");for(var o=t&&t.height?t.height:this.Tc,u=[],n=0;n<s.length;n++)(function(t){var i=s[t],n=i.pointList;if(0===n.length)return;var t=i.level,r=[];n.forEach(function(t){var i=parseFloat(""+t.x),t=parseFloat(""+t.y);r.push({x:i,y:t,z:o})}),n.length<2&&(i=parseFloat(""+n[0].x),e=parseFloat(""+n[0].y),r.push({x:i,y:e,z:o}));var e=new fengmap.FMSegment;e.level=t,e.points=r,u.push(e)})(n);i=new fengmap.FMLineMarker(Object.assign({segments:u},i));return i.passed=t&&t.passed?t.passed:this.il,i},Pc:function(t){return{id:t.Vi,startPoint:{x:t.Qr.x,y:t.Qr.y},endPoint:{x:t.Jr.x,y:t.Jr.y},startId:t.Hr,endId:t.Kr,roadRank:t.re,roadEntry:t.ee}},Cc:function(t,i,n){if(0!=t.length&&null!=t.get(i)&&null!=t.get(n)){var r,e=new Map,s=Xr(t.keys());try{for(s.s();!(r=s.n()).done;){var o=r.value;e.set(o,!1)}}catch(t){s.e(t)}finally{s.f()}for(var u=[i];0<u.length;){var h=u[0];if(e.get(h))u.splice(0,1);else{if(e.set(h,!0),u.splice(0,1),h==n)return!1;var a=t.get(h);if(!a||a&&2<a.length)return!0;for(var f=0;f<a.length;f++)u.push(a[f].us)}}return!0}},Gc:function(t,i){var n=[];if(t.coords.buildingID!=i.coords.buildingID)return n;if(t.coords.level!=i.coords.level)return n;var r=null==t.buildingID?null:t.buildingID,e=t.coords.level,s=this.Sc.get(r).get(e),o=t.road.id,u=i.road.id,h=s.get(o),a=s.get(u),s=t.coords.buildingID,f=null==this.Nc.get(s)?[]:this.Nc.get(s),r=s?this.Ec.ec.get(r):this.Ec.ec.get(null);if(h!=a){for(var c=null,l=0;l<f.length;l++)if(f[l].level==e&&null!=f[l].ptsBwteenRoad){c=f[l].ptsBwteenRoad;break}null!=c&&null!=c.get(o)&&null!=c.get(o).get(u)?n=c.get(o).get(u):(r=r.Oh.get(e),n=this.nl(t,i,r),null==c?((c=new Map).set(o,new Map),c.get(o).set(u,n),f.push({level:e,ptsBwteenRoad:c}),this.Nc.set(s,f)):(null==c.get(o)&&c.set(o,new Map),c.get(o).set(u,n)))}return n},Bc:function(t,i,n,r){var e,s=new Map;if(null==(e=(n=null==n?null:n)?this.Ec.ec.get(n):this.Ec.ec.get(null)))return null;for(var o=r?e.wh:e.mh,u=0;u<t.length;u++){var h=t[u],a=o.get(h).Te,f=[],c=void 0;if(null!=i&&0<i.length){for(var l=0;l<i.length;l++)for(var v=0;v<a.length;v++)a[v].re===i[l]&&f.push(a[v]);c=this.rl(f)}else c=this.rl(a);s.set(h,c)}this.Sc.set(n,s)},rl:function(t){for(var i=new Map,n=new Map,r=0,e=0;e<t.length;e++)n.set(t[e].Vi,[0,0]),i.set(t[e].Vi,-1);for(var s=0;s<t.length;s++){var o=t[s];if(1!=n.get(o.Vi)[0]||1!=n.get(o.Vi)[1]){for(var u=o.Hr,h=o.Kr,a=new Tn(o.Jr.x-o.Qr.x,o.Jr.y-o.Qr.y),f=s+1;f<t.length;f++){var c=t[f];if(o.re==c.re){var l=c.Hr,v=c.Kr,d=i.get(o.Vi),y=i.get(c.Vi),b=void 0,b=-1==d&&-1==y?-1:-1!=d&&(-1==y||d<=y)?d:y,p=new Tn(c.Jr.x-c.Qr.x,c.Jr.y-c.Qr.y);if(0==n.get(o.Vi)[0]){if(u==l){if(a.multiplyScalar(-1).angleTo(p)*pt.it()<=5){if(b=-1==b?++r:b,i.set(o.Vi,b),i.set(c.Vi,b),n.get(o.Vi)[0]=1,-(n.get(c.Vi)[0]=1)!=y&&y!=b){var x,m=Xr(i.keys());try{for(m.s();!(x=m.n()).done;){var w=x.value;i.get(w)==y&&i.set(w,b)}}catch(t){m.e(t)}finally{m.f()}}if(-1!=d&&d!=b){var _,g=Xr(i.keys());try{for(g.s();!(_=g.n()).done;){var O=_.value;i.get(O)==d&&i.set(O,b)}}catch(t){g.e(t)}finally{g.f()}}}continue}if(u==v){if(a.multiplyScalar(-1).angleTo(p.multiplyScalar(-1))*pt.it()<=5){if(b=-1==b?++r:b,i.set(o.Vi,b),i.set(c.Vi,b),n.get(o.Vi)[0]=1,-(n.get(c.Vi)[1]=1)!=y&&y!=b){var E,M=Xr(i.keys());try{for(M.s();!(E=M.n()).done;){var j=E.value;i.get(j)==y&&i.set(j,b)}}catch(t){M.e(t)}finally{M.f()}}if(-1!=d&&d!=b){var k,R=Xr(i.keys());try{for(R.s();!(k=R.n()).done;){var T=k.value;i.get(T)==d&&i.set(T,b)}}catch(t){R.e(t)}finally{R.f()}}}continue}}if(0==n.get(o.Vi)[1])if(h!=l){if(h==v)if(a.angleTo(p.multiplyScalar(-1))*pt.it()<=5){if(b=-1==b?++r:b,i.set(o.Vi,b),i.set(c.Vi,b),n.get(o.Vi)[1]=1,-(n.get(c.Vi)[1]=1)!=y&&y!=b){var A,S=Xr(i.keys());try{for(S.s();!(A=S.n()).done;){var N=A.value;i.get(N)==y&&i.set(N,b)}}catch(t){S.e(t)}finally{S.f()}}if(-1!=d&&d!=b){var I,L=Xr(i.keys());try{for(L.s();!(I=L.n()).done;){var D=I.value;i.get(D)==d&&i.set(D,b)}}catch(t){L.e(t)}finally{L.f()}}}}else if(a.angleTo(p)*pt.it()<=5){if(b=-1==b?++r:b,i.set(o.Vi,b),i.set(c.Vi,b),n.get(o.Vi)[1]=1,-(n.get(c.Vi)[0]=1)!=y&&y!=b){var P,F=Xr(i.keys());try{for(F.s();!(P=F.n()).done;){var C=P.value;i.get(C)==y&&i.set(C,b)}}catch(t){F.e(t)}finally{F.f()}}if(-1!=d&&d!=b){var U,B=Xr(i.keys());try{for(B.s();!(U=B.n()).done;){var G=U.value;i.get(G)==d&&i.set(G,b)}}catch(t){B.e(t)}finally{B.f()}}}}}-1==i.get(o.Vi)&&i.set(o.Vi,++r)}}return i},nl:function(t,i,n){var r=[],e=t.coords,s=t.road,o=i.coords,u=i.road,h=n.No(e),t=n.No(o);if(null==s||null==u||e.level!=o.level||s.extentID!=u.extentID)return r;if(h!=t)return r;var i=null,t=null,a=e.level,t=pt.dt(o,s.startPoint)<=pt.dt(o,s.endPoint)?(i=s.startPoint,s.startID):(i=s.endPoint,s.endID),s=null,s=pt.dt(i,u.startPoint)<=pt.dt(i,u.endPoint)?(u.startPoint,u.startID):(u.endPoint,u.endID),i=n.ks,u=i.hs(t),t=[i.hs(s)],h=n.uu(h);if(!h.Bs(i,u,t))return r;h.Ss=s;for(var f=h.Zs(),c=f.length-1;0<=c;c--)r.push(Object.assign({level:a},f[c].zt));return r}});var Kr=r;function Jr(t){return(Jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function $r(t,i){return($r=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function te(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=ie(n);return t=r?(t=ie(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==Jr(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function ie(t){return(ie=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}s=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&$r(t,i)}(e,Kr);var r=te(e);function e(t,i,n){return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,e),r.call(this,t,i,n)}return e}();function ne(t){return function(t){if(Array.isArray(t))return re(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,i){if(t){if("string"==typeof t)return re(t,i);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?re(t,i):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function re(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=new Array(i);n<i;n++)r[n]=t[n];return r}E=function t(i){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),this.hc=void 0!==i.buildingID?i.buildingID:null,this.br=void 0!==i.level?i.level:null,this.fe=void 0!==i.points?i.points:null,this.el=-1,this.Zt=null,this.er()};Object.assign(E.prototype,{er:function(){this.fe&&(this.Zt=new mt,this.Zt.Ft(this.fe),this.el=this.fe.length,this.fe[0].x==this.fe[this.el-1].x&&this.fe[0].y==this.fe[this.el-1].y?this.el--:this.fe.push(this.fe[0]))},Au:function(t,i){return pt.ot(t,i,this.Zt.Lt,this.Zt.Dt)},vs:function(t){return!!pt.ht(t,this.Zt.min,this.Zt.max)&&pt.ct(t,this.fe,this.el)},sl:function(t){var i=0;return this.vs(t[0])&&(i|=1),this.vs(t[1])&&(i|=2),i},ls:function(t,i){if(!pt.ot(t[0],t[1],this.Zt.min,this.Zt.max))return!1;for(var n,r,e=!1,s=0;s<this.el;s++){var o={};1==pt.wt(t[0],t[1],this.fe[s],this.fe[s+1],o)&&(e=!0,i.push(o))}return t[0].x!=t[1].x?(n=t[0].x<t[1].x,i.sort(function(t,i){return n?t.x-i.x:i.x-t.x})):(r=t[0].y<t[1].y,i.sort(function(t,i){return r?t.y-i.y:i.y-t.y})),e},ol:function(t,i){var n=[];n.push(t[0]),n.push.apply(n,ne(i)),n.push(t[1]);for(var r=[],e=0;e<n.length-1;e++)r.push([n[e],n[e+1]]);return r},Ba:function(t){if(this.hc!=t.bid||this.br!=t.br)return!1;if(this.fe.length!=t.fe.length)return!1;for(var i=0;i<this.fe.length;i++){if(this.fe[i].x!=t.fe[i].x)return!1;if(this.fe[i].y!=t.fe[i].y)return!1}return!0}});A=E;function ee(t){return(ee="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function se(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function oe(t,i){return(oe=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function ue(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=he(n);return t=r?(t=he(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==ee(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function he(t){return(he=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}r=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&oe(t,i)}(e,Kr);var t,i,n,r=ue(e);function e(t,i,n){return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,e),t.type=mn.Drive,r.call(this,t,i,n)}return t=e,(i=[{key:"route",value:function(t,i,n){this.Ec.obstruct=void 0===t.obstruct||t.obstruct;var r=S.ROUTE_FAILED_CANNOT_ARRIVE;if((r=t.viapoints&&0<t.viapoints.length?(this.Ec.Jh=t.viapoints,this.Ec.na(t.start,t.dest)):this.Ec.ua(t.start,t.dest,t.mode||g.MODULE_SHORTEST,t.priority||m.PRIORITY_DEFAULT))===S.ROUTE_SUCCESS){this.Mc=this.Ec.pc();var e=null,s=null;this.jc=[];for(var o=0;o<this.Mc.length;o++)0<o&&(e=this.ii.bidToName.get(this.Mc[o-1].bid)?this.ii.bidToName.get(this.Mc[o-1].bid):"outdoor"),o<this.Mc.length-1&&(s=this.ii.bidToName.get(this.Mc[o+1].bid)?this.ii.bidToName.get(this.Mc[o+1].bid):"outdoor"),this.Mc[o].df=e,this.Mc[o].yf=s,s=e=null,this.Mc[o].buildingName=this.ii.bidToName.get(this.Mc[o].bid),this.jc.push([this.Lc(this.Mc[o],{language:a.ZH,type:mn.Drive}),this.Lc(this.Mc[o],{language:a.EN,type:mn.Drive})]);this.Rc=this.Ec.Yh,i&&i(this.dr())}else n&&n(r)}}])&&se(t.prototype,i),n&&se(t,n),e}();function ae(t){return(ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fe(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function ce(t,i){return(ce=Object.setPrototypeOf||function(t,i){return t.__proto__=i,t})(t,i)}function le(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var t,i=ve(n);return t=r?(t=ve(this).constructor,Reflect.construct(i,arguments,t)):i.apply(this,arguments),i=this,!(t=t)||"object"!==ae(t)&&"function"!=typeof t?function(t){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(i):t}}function ve(t){return(ve=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var de,E=function(){!function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(i&&i.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),i&&ce(t,i)}(e,Kr);var t,i,n,r=le(e);function e(t,i,n){return function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,e),t.type=mn.Walk,r.call(this,t,i,n)}return t=e,(i=[{key:"route",value:function(t,i,n){this.Ec.toDoors=void 0!==t.toDoors&&t.toDoors,this.Ec.obstruct=void 0===t.obstruct||t.obstruct,this.Ec.barrierFree=void 0!==t.barrierFree&&t.barrierFree;var r=S.ROUTE_FAILED_CANNOT_ARRIVE;if((r=t.viapoints&&0<t.viapoints.length?(this.Ec.Jh=t.viapoints,this.Ec.na(t.start,t.dest,t.mode||g.MODULE_SHORTEST,t.priority||m.PRIORITY_DEFAULT)):this.Ec.ua(t.start,t.dest,t.mode||g.MODULE_SHORTEST,t.priority||m.PRIORITY_DEFAULT))===S.ROUTE_SUCCESS){this.Mc=this.Ec.pc();var e=null,s=null;this.jc=[];for(var o=0;o<this.Mc.length;o++)0<o&&(e=this.ii.bidToName.get(this.Mc[o-1].bid)?this.ii.bidToName.get(this.Mc[o-1].bid):"outdoor"),o<this.Mc.length-1&&(s=this.ii.bidToName.get(this.Mc[o+1].bid)?this.ii.bidToName.get(this.Mc[o+1].bid):"outdoor"),this.Mc[o].df=e,this.Mc[o].yf=s,s=e=null,this.Mc[o].buildingName=this.ii.bidToName.get(this.Mc[o].bid),this.jc.push([this.Lc(this.Mc[o],{language:a.ZH}),this.Lc(this.Mc[o],{language:a.EN})]);this.Rc=this.Ec.Yh,i&&i(this.dr())}else n&&n(r)}}])&&fe(t.prototype,i),n&&fe(t,n),e}(),n=n(28),ye={VERSION:u,BUILD:h,XMLHttpRequest:n.a,FMType:e,FMNaviMode:g,FMNaviPriority:m,FMRouteResult:S,FMLanguageType:a,FMNaviTransfer:f,FMRoadNetworkResult:c,FMSearchRequest:b,FMSearchAnalyser:F,FMNaviAnalyser:s,FMNaviObstruction:A,FMNaviDriveAnalyser:r,FMNaviWalkAnalyser:E},E=Xt.a.global(),be=void 0!==E?E.fengmap:{};for(de in void 0===be&&(be={}),ye)be[de]=ye[de];void 0!==E&&(E.fengmap=be);i.default=be}],f={},g.m=d,g.c=f,g.d=function(t,i,n){g.o(t,i)||Object.defineProperty(t,i,{enumerable:!0,get:n})},g.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"ul",{value:!0})},g.t=function(i,t){if(1&t&&(i=g(i)),8&t)return i;if(4&t&&"object"==typeof i&&i&&i.ul)return i;var n=Object.create(null);if(g.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:i}),2&t&&"string"!=typeof i)for(var r in i)g.d(n,r,function(t){return i[t]}.bind(null,r));return n},g.n=function(t){var i=t&&t.ul?function(){return t.default}:function(){return t};return g.d(i,"a",i),i},g.o=function(t,i){return Object.prototype.hasOwnProperty.call(t,i)},g.p="",g(g.s=50).default;function g(t){if(f[t])return f[t].exports;var i=f[t]={i:t,l:!1,exports:{}};return d[t].call(i.exports,i,i.exports,g),i.l=!0,i.exports}var d,f});