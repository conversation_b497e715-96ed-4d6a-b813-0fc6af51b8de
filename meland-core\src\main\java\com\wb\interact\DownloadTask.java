package com.wb.interact;

import com.wb.common.Base;
import com.wb.util.LogUtil;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 下载任务执行类
 * 负责异步执行下载任务，处理文件内容，并记录任务状态
 */
public class DownloadTask implements Runnable {
    private static final Logger LOGGER = LoggerFactory.getLogger(DownloadTask.class);
    
    private final String downloadId;
    private final String metaParamsStr;
    private final String targetUrl;
    private final String type;
    private final JSONObject sessionInfo;
    private final File tempFile;
    private final FileOutputStream fileOutputStream;
    private final AtomicBoolean taskCancelled;
    private final CompletableFuture<Void> taskFuture;

    /**
     * 构造下载任务
     *
     * @param downloadId 下载任务ID
     * @param metaParamsStr 元参数字符串
     * @param targetUrl 目标URL
     * @param type 下载类型
     * @param sessionInfo 会话信息
     * @param tempFile 临时文件
     * @param fileOutputStream 文件输出流
     * @param taskCancelled 任务取消标志
     * @param taskFuture 任务完成Future
     */
    public DownloadTask(String downloadId, String metaParamsStr, String targetUrl, String type,
            JSONObject sessionInfo, File tempFile, FileOutputStream fileOutputStream, AtomicBoolean taskCancelled,
            CompletableFuture<Void> taskFuture) {
        this.downloadId = downloadId;
        this.metaParamsStr = metaParamsStr;
        this.targetUrl = targetUrl;
        this.type = type;
        this.sessionInfo = sessionInfo;
        this.tempFile = tempFile;
        this.fileOutputStream = fileOutputStream;
        this.taskCancelled = taskCancelled;
        this.taskFuture = taskFuture;
    }

    @Override
    public void run() {
        String threadName = Thread.currentThread().getName();
        LOGGER.debug("下载线程启动: " + threadName + ", 任务ID: " + downloadId);

        try {
            // 增加活跃线程计数
            FilePush.incrementActiveThreadCount();

            // 记录处理线程
            FilePush.setProcessingThread(downloadId, Thread.currentThread());

            // 检查取消标志
            if (taskCancelled.get()) {
                LOGGER.debug("任务在启动前已被取消: " + downloadId);
                taskFuture.completeExceptionally(new InterruptedException("任务被取消"));
                return;
            }

            // 执行文件内容处理
            FilePush.processFileContent(downloadId, metaParamsStr, targetUrl, fileOutputStream, type, sessionInfo);

            // 保存下载任务信息
            FilePush.setDownloadTaskValue(downloadId, tempFile.getAbsolutePath());

            // 添加任务完成标记
            FilePush.setDownloadTaskValue(downloadId + "_completed", "true");
            
            // 从lastAccessTimes中移除记录，防止被超时监控误认为是长时间运行任务
            FilePush.removeLastAccessTime(downloadId);

            // 同时在Redis中存储文件路径和完成标记
            try {
                Base.map.setValue(DownloadTaskKeyManager.getDownloadTaskKey(downloadId), tempFile.getAbsolutePath(),
                        FilePush.getDownloadExpireHours(), TimeUnit.HOURS);
                Base.map.setValue(DownloadTaskKeyManager.getDownloadTaskKey(downloadId) + "_completed", "true",
                        FilePush.getDownloadExpireHours(), TimeUnit.HOURS);
                
                // 安排延迟清理文件（在指定过期时间后自动删除）
                DownloadTaskCleaner.scheduleFileCleanup(downloadId, tempFile);
                
            } catch (Exception e) {
                LogUtil.warn("Redis存储下载任务文件路径失败: " + e.getMessage());
            }

            // 标记任务成功完成
            taskFuture.complete(null);

            LOGGER.info("下载任务完成: " + downloadId);

        } catch (InterruptedException ie) {
            // 对于中断异常不需要记录错误信息，这是正常的任务取消流程
            taskFuture.completeExceptionally(ie);

            // 对于中断异常不需要记录错误信息，这是正常的任务取消流程
        } catch (Exception e) {
            LogUtil.error("下载任务执行异常: " + downloadId + ", " + e.getMessage());
            taskFuture.completeExceptionally(e);

            // 记录错误信息到Redis
            try {
                Base.map.setValue(DownloadTaskKeyManager.getDownloadErrorKey(downloadId), e.getMessage(),
                        FilePush.getDownloadExpireHours(), TimeUnit.HOURS);
            } catch (Exception ex) {
                LogUtil.warn("Redis存储下载任务错误信息失败: " + ex.getMessage());
            }
        } finally {
            // 确保关闭文件输出流
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    LogUtil.error("关闭文件输出流失败: " + downloadId, e);
                }
            }

            // 减少活跃线程计数
            FilePush.decrementActiveThreadCount();

            // 从处理线程映射中移除
            FilePush.removeProcessingThread(downloadId);

            // 释放信号量
            FilePush.releaseDownloadSemaphore(downloadId);

            LOGGER.info("下载线程完成: " + threadName + ", 任务ID: " + downloadId + ", 剩余活跃线程: " + FilePush.getActiveThreadCount());
        }
    }
} 