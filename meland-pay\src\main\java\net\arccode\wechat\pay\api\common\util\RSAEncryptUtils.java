package net.arccode.wechat.pay.api.common.util;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.PublicKey;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.security.cert.X509Certificate;

/**
 * 微信敏感信息加密
 * 
 * <AUTHOR>
 *
 */
public class RSAEncryptUtils {

	private static final String CIPHER_PROVIDER = "SunJCE";
	private static final String TRANSFORMATION_PKCS1Paddiing = "RSA/ECB/PKCS1Padding";

	private static final String CHAR_ENCODING = "UTF-8";//固定值，无须修改

	//数据加密方法
	private static byte[] encryptPkcs1padding(PublicKey publicKey, byte[] data) throws Exception {
		Cipher ci = Cipher.getInstance(TRANSFORMATION_PKCS1Paddiing, CIPHER_PROVIDER);
		ci.init(Cipher.ENCRYPT_MODE, publicKey);
		return ci.doFinal(data);
	}

	//加密后的秘文，使用base64编码方法
	private static String encodeBase64(byte[] bytes) throws Exception {
		return Base64.getEncoder().encodeToString(bytes);
	}
	
	/**
	 * 对敏感内容（入参Content）加密
	 * 其中keyPath为存放平台证书的路径，
	 * 平台证书文件存放明文平台证书内容，
	 * 且为pem格式的平台证书（
	 *   平台证书的获取方式参照平台证书及序列号获取接口，
	 *   通过此接口得到的参数certificates包含了加密的平台证书内容ciphertext，
	 *   然后根据接口文档中平台证书解密指引，最终得到明文平台证书内容
	 * ）
	 * 
	 * @param Content 待加密的内容
	 * @return 加密后的字符串
	 * @throws Exception
	 */
	public static String rsaEncrypt(String Content,String keyPath) throws Exception {
		final byte[] PublicKeyBytes = Files.readAllBytes(Paths.get(keyPath));
		X509Certificate certificate = X509Certificate.getInstance(PublicKeyBytes);
		PublicKey publicKey = certificate.getPublicKey();

		return encodeBase64(encryptPkcs1padding(publicKey, Content.getBytes(CHAR_ENCODING)));
	}
}
