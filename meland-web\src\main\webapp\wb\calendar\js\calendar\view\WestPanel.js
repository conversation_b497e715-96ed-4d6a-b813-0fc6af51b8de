/**
 * FeyaSoft MyCalendar Copyright(c) 2006-2012, FeyaSoft Inc. All right reserved.
 * <EMAIL> http://www.cubedrive.com
 * 
 * Please read license first before your use myCalendar, For more detail
 * information, please can visit our link: http://www.cubedrive.com/myCalendar
 * 
 * You need buy one of the Feyasoft's License if you want to use MyCalendar in
 * your commercial product. You must not remove, obscure or interfere with any
 * FeyaSoft copyright, acknowledgment, attribution, trademark, warning or
 * disclaimer statement affixed to, incorporated in or otherwise applied in
 * connection with the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY,FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO
 * EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES
 * OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.
 */

Ext.define('Ext.ux.calendar.WestPanel', {
  extend: 'Ext.Panel',
  header: false,
  collapsible: true,
  region: 'west',
  minWidth: 240,
  width: 240,
  layout: 'border',
  split: true,
  glyph: 0xf133,
  border: true,
  title: '日历列表',
  initComponent: function() {
    var eh = this.ehandler;
    eh.applyCalendarSetting(this);
    this.ds = eh.ds;
    var lan = Ext.ux.calendar.Mask.WestPanel;
    this.datePicker = Ext.create('Ext.util.DatePicker', {
      region: 'north',
      cls: 'x-west-datepicker',
      border: false,
      value: new Date(),
      startDay: this.startDay
    });
    this.showAllBtn = Ext.create('Ext.Button', {
      text: lan['myShowAllBtn.text'],
      handler: this.onShowAllFn,
      buttonStyle: "default",
      scale: "medium",
      scope: this
    });

    this.addBtn = Ext.create('Ext.Button', {
      text: lan['myAddBtn.text'],
      glyph: 0xf271,
      handler: this.onAddFn,
      buttonStyle: "primary",
      scale: "medium",
      scope: this
    });
    var bbar = [this.showAllBtn, '->'];
    if (!this.readOnly) {
      bbar.push(this.addBtn);
    }
    this.myCalendarPanel = Ext.create('Ext.Panel', {
      border: false,
      region: 'center',
      glyph: 0xf073,
      title: lan['myCalendarPanel.title'],
      style: 'padding-top:5px;',
      bodyStyle: 'padding:2px;background-color:white;overflow-x:hidden;position:relative;',
      autoScroll: true,
      bbar: bbar
    });
    this.myCalendarPanel.on('render', this.onMyCalendarRenderFn, this);

    this.items = [this.datePicker, this.myCalendarPanel];

    this.callParent(arguments);

    this.addEvents('changedate');
    this.datePicker.on('aferinitevent', this.afterDatePickerInitFn, this);
    this.datePicker.on('select', this.onSelectFn, this);
    this.on('changedate', this.changeDateLabel, this);
  },

  onOtherCalendarRenderFn: function(p) {
    var eh = this.ehandler;
    eh.renderSharedCalendar(p.body);
  },
  onShowAllFn: function() {
    this.ehandler.onShowAllFn();
  },

  onAddFn: function() {
    this.ehandler.ceditor.popup({
      action: 'add'
    });
  },

  onSelectFn: function(dp, date) {
    var calendarContainer = this.ownerCt.calendarContainer;
    calendarContainer.showDay(date);
  },

  onMyCalendarRenderFn: function(p) {
    var eh = this.ehandler;
    eh.renderOwnedCalendar(p.body);
  },

  changeDateLabel: function(fromDate, toDate) {
    this.updateDatePicker(fromDate, toDate);
  },

  updateDatePicker: function(fromDate, toDate) {
    if (fromDate && toDate) {
      var vDate = Ext.Date;
      var from = vDate.format(fromDate, this.fromtoFormat);
      this.datePicker.setRange(fromDate, toDate);
      var dnum = Ext.ux.calendar.Mask.getDayOffset(fromDate, toDate);
      if (7 < dnum) {
        var fd = vDate.getFirstDateOfMonth(fromDate);
        var fday = vDate.format(fd, 'Y-m-d');
        from = vDate.format(fromDate, 'Y-m-d');
        if (from != fday) {
          fd = vDate.add(vDate.getLastDateOfMonth(fromDate),
            Ext.Date.DAY, 1);
        }
        this.datePicker.setValue(fd);
      } else {
        this.datePicker.setValue(fromDate);
      }
    }
  },

  afterDatePickerInitFn: function() {
    var cview = this.ownerCt.calendarContainer.currentView;
    this.updateDatePicker(cview.daySet[0], cview.daySet[cview.daySet.length - 1]);
  }
});