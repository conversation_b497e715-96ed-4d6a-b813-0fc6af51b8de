package com.wb.common;

import com.wb.cache.RedisCache;
import com.wb.util.DateUtil;
import com.wb.util.SpringContextUtils;
import com.wb.util.StringUtil;
import com.wb.util.WebUtil;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.session.FindByIndexNameSessionRepository;
import org.springframework.session.Session;
import org.springframework.data.redis.core.ZSetOperations.TypedTuple;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.Map.Entry;

/**
 * 在线用户列表
 */
public class UserList {

    /**
     * 获取Spring Bean "FindByIndexNameSessionRepository" 的实例， 它是Spring
     * Session中用于管理Session对象的接口
     *
     * @return 返回Spring Bean "FindByIndexNameSessionRepository" 的实例
     */
    @SuppressWarnings("unchecked")
    public static FindByIndexNameSessionRepository<Session> getSessionRepository() {
        return SpringContextUtils.getBean(FindByIndexNameSessionRepository.class);
    }

    /**
     * 移除指定用户的指定会话
     *
     * @param userId  用户ID
     * @param session HttpSession对象
     */
    public static void remove(String userId, HttpSession session) {
        doRemove(userId, session.getId());
    }

    /**
     * 移除指定用户的指定会话
     *
     * @param userId    用户ID
     * @param sessionId 会话ID
     */
    public static void remove(String userId, String sessionId) {
        doRemove(userId, sessionId);
    }

    /**
     * 移除指定用户的所有会话
     *
     * @param userId 用户ID
     */
    public static void remove(String userId) {
        doRemove(userId, null);
        Base.onlineUserManager.userLogout(userId);
    }

    /**
     * 根据用户ID数组批量使对应的所有会话失效
     *
     * @param userIds 用户ID数组
     */
    public static void invalidate(String[] userIds) {
        doInvalidate(userIds);
    }

    /**
     * 根据用户ID数组批量移除对应的所有会话
     *
     * @param userIds 用户ID数组
     */
    private static void doInvalidate(String[] userIds) {
        int j = userIds.length;
        for (String userId : userIds) {
            remove(userId);
        }
    }

    /**
     * 移除指定用户的指定会话或所有会话
     *
     * @param userId    用户ID
     * @param sessionId 会话ID，如果为null，则移除该用户的所有会话
     */
    private static void doRemove(String userId, String sessionId) {
        FindByIndexNameSessionRepository<Session> sessionRepository = getSessionRepository();
        // 如果指定了session可以直接移除，如果没有则根据用户名移除用户所有会话
        if (StringUtil.isEmpty(sessionId)) {
            Map<String, ? extends Session> sessionsByUsername = sessionRepository.findByIndexNameAndIndexValue(FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME, userId);

            for (Session session : sessionsByUsername.values()) {
                sessionRepository.deleteById(session.getId());
            }
        } else sessionRepository.deleteById(sessionId);
    }

    /**
     * 更新指定用户的角色信息
     *
     * @param userId 用户ID
     * @param roles  角色数组
     * @param status 状态，true表示更新，false表示移除所有会话
     */
    public static void update(String userId, String[] roles, boolean status) {
        doUpdate(userId, roles, status);
    }

    /**
     * 更新指定用户的角色信息
     *
     * @param userId 用户ID
     * @param roles  角色数组
     * @param status 状态，true表示更新，false表示移除所有会话
     */
    private static void doUpdate(String userId, String[] roles, boolean status) {
        FindByIndexNameSessionRepository<Session> sessionRepository = getSessionRepository();
        Map<String, ? extends Session> sessionsByUsername = sessionRepository.findByIndexNameAndIndexValue(FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME, userId);
        if (status) {
            // 如果更新权限，遍历用户所有的会话，更新角色信息
            for (Session session : sessionsByUsername.values()) {
                session.setAttribute("sys.roles", roles);
                session.setAttribute("sys.roleList", StringUtil.join(roles, ","));
                sessionRepository.save(session);
            }
        } else {
            // 如果不更新权限，使对应用户的所有会话失效
            String[] userIds = {userId};
            invalidate(userIds);
        }

    }

    /**
     * 获取指定用户的所有会话
     *
     * @param userId 用户ID
     * @return Session数组
     */
    public static Session[] getSessions(String userId) {
        FindByIndexNameSessionRepository<Session> sessionRepository = getSessionRepository();
        Map<String, Session> sessionsByUsername = sessionRepository.findByIndexNameAndIndexValue(FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME, userId);
        Session[] sessions = sessionsByUsername.values().toArray(new Session[0]);
        return sessions;
    }

    /**
     * 获取所有在线用户的用户ID数组
     *
     * @return 在线用户的用户ID数组
     */
    public static String[] getUsers() {
        if (Base.onlineUserManager != null) {
            List<Object> onlineUsers = Base.onlineUserManager.getAllOnlineUsers();
            String[] userArray = new String[onlineUsers.size()];
            for (int i = 0; i < onlineUsers.size(); i++) {
                userArray[i] = onlineUsers.get(i).toString();
            }
            return userArray;
        } else {
            // 降级处理，如果OnlineUserManager不可用，则使用原来的方法
            HashMap<String, HashSet<JSONObject>> users = getUserMap(null);
            return (String[]) users.keySet().toArray(new String[0]);
        }
    }

    /**
     * 获取用户的所有会话，用于前端页面展示
     *
     * @param request  HttpServletRequest对象
     * @param response HttpServletResponse对象
     */
    public static void getSessionList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        doGetSessionList(request, response);
    }

    /**
     * 获取用户的所有会话，用于前端页面展示
     *
     * @param request  HttpServletRequest对象
     * @param response HttpServletResponse对象
     */
    private static void doGetSessionList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (StringUtil.isEmpty(WebUtil.fetch(request, "start")) || StringUtil.isEmpty(WebUtil.fetch(request, "limit")))
            throw new Exception("参数错误");
        String user = WebUtil.fetch(request, "user");
        int start = Integer.parseInt(Objects.requireNonNull(WebUtil.fetch(request, "start")));
        int limit = Integer.parseInt(Objects.requireNonNull(WebUtil.fetch(request, "limit")));
        int index = -1;
        JSONArray rows = new JSONArray();

        Session[] sessions = getSessions(user);

        if (limit > Var.limitRecords) limit = Var.limitRecords;
        int end = start + limit;
        for (Session session : sessions) {
            index++;
            if (index >= start) {
                if (index >= end) break;
                JSONObject row = new JSONObject();
                try {
                    row.put("ip", session.getAttribute("sys.ip").toString());
                    row.put("userAgent", session.getAttribute("sysx.userAgent").toString());
                    row.put("sessionId", session.getId());

                    // 使用毫秒值创建Date
                    long creationTime = session.getCreationTime().toEpochMilli();
                    long lastAccessedTime = session.getLastAccessedTime().toEpochMilli();
                    row.put("createDate", DateUtil.format(new Date(creationTime)));
                    row.put("lastAccessDate", DateUtil.format(new Date(lastAccessedTime)));

                    rows.put(row);
                } catch (Exception e) {
                    // 忽略属性获取过程中的异常，继续处理其它会话
                }
            }
        }

        JSONObject result = new JSONObject();
        result.put("total", sessions.length);
        result.put("rows", rows);
        WebUtil.send(response, result);
    }

    public static void getUserList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        doGetUserList(request, response);
    }

    /**
     * 获取所有在线用户的信息的实现方法，返回一个HashMap对象。
     * HashMap的key为用户ID，value为一个HashSet对象，包含了该用户的所有会话信息。
     *
     * @param searchUserId 搜索用户ID
     * @param start        开始位置
     * @param end          结束位置
     * @return 包含所有在线用户信息的HashMap对象
     */
    private static HashMap<String, HashSet<JSONObject>> getUserMap(String searchUserId, long start, long end) {
        HashMap<String, HashSet<JSONObject>> users = new HashMap<>();
        Set<Object> userIds = new HashSet<>();

        // 使用OnlineUserManager获取在线用户
        if (Base.onlineUserManager != null) {
            if (!StringUtil.isEmpty(searchUserId)) {
                // 如果searchUserId不为空，那么只返回该用户的信息
                // 检查用户是否在线
                String status = Base.onlineUserManager.getUserStatus(searchUserId);
                if (status != null) {
                    userIds.add(searchUserId);
                }
            } else {
                // 获取指定范围的在线用户，直接使用Redis的范围查询功能
                List<Object> rangeUsers = Base.onlineUserManager.getOnlineUsersByRange(start, end);
                if (!rangeUsers.isEmpty()) {
                    userIds.addAll(rangeUsers);
                }
            }
        } else {
            // 降级方案：使用原来的方法获取在线用户
            if (!StringUtil.isEmpty(searchUserId)) {
                userIds.add(searchUserId);
            } else {
                // 使用zsetReverseRangeWithScores保持与OnlineUserManager一致的排序行为
                Set<TypedTuple<Object>> usersWithScores = Base.map.zsetReverseRangeWithScores(RedisCache.ONLINE_USERS, start, end);
                if (usersWithScores != null) {
                    for (TypedTuple<Object> tuple : usersWithScores) {
                        if (tuple.getValue() != null) {
                            userIds.add(tuple.getValue());
                        }
                    }
                }
            }
        }

        if (userIds.isEmpty()) {
            return users;
        }

        // 批量获取所有用户的会话
        FindByIndexNameSessionRepository<Session> sessionRepository = getSessionRepository();
        Map<String, Map<String, ? extends Session>> userSessions = new HashMap<>();
        
        for (Object userId : userIds) {
            Map<String, ? extends Session> sessions = sessionRepository.findByIndexNameAndIndexValue(
                FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME, userId.toString());
            if (sessions != null && !sessions.isEmpty()) {
                userSessions.put(userId.toString(), sessions);
            }
            // 即使没有会话，也要初始化用户的HashSet
            users.put(userId.toString(), new HashSet<>());
        }

        // 处理每个用户的会话信息
        for (Object userId : userIds) {
            String userIdStr = userId.toString();
            Map<String, ? extends Session> sessions = userSessions.get(userIdStr);
            
            if ((sessions == null || sessions.isEmpty()) && Base.onlineUserManager != null 
                && Base.onlineUserManager.getUserStatus(userIdStr) != null) {
                // 创建一个虚拟会话对象，包含基本信息
                JSONObject virtualSession = new JSONObject();
                virtualSession.put("username", userId); // 使用userId作为默认用户名
                virtualSession.put("dispname", userId); // 使用userId作为默认显示名
                virtualSession.put("ip", "");
                virtualSession.put("userAgent", "");
                users.get(userIdStr).add(virtualSession);
            } else if (sessions != null && !sessions.isEmpty()) {
                for (Session session : sessions.values()) {
                    try {
                        JSONObject object = new JSONObject();
                        object.put("username", session.getAttribute("sys.username").toString());
                        object.put("dispname", session.getAttribute("sys.dispname").toString());
                        object.put("ip", session.getAttribute("sys.ip").toString());
                        object.put("userAgent", session.getAttribute("sysx.userAgent").toString());
                        users.get(userIdStr).add(object);
                    } catch (Exception e) {
                        // 处理会话属性访问异常
                    }
                }
            }
        }
        return users;
    }

    private static HashMap<String, HashSet<JSONObject>> getUserMap(String searchUserId) {
        return getUserMap(searchUserId, 0, Integer.MAX_VALUE);
    }


    /**
     * 获取所有在线用户的信息，用于前端页面展示
     *
     * @param request  HttpServletRequest对象
     * @param response HttpServletResponse对象
     */
    private static void doGetUserList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (StringUtil.isEmpty(WebUtil.fetch(request, "start")) || StringUtil.isEmpty(WebUtil.fetch(request, "limit")))
            throw new Exception("参数错误");
        int start = Integer.parseInt(Objects.requireNonNull(WebUtil.fetch(request, "start")));
        int limit = Integer.parseInt(Objects.requireNonNull(WebUtil.fetch(request, "limit")));
        if (limit > Var.limitRecords) limit = Var.limitRecords;
        int end = start + limit;

        HashMap<String, HashSet<JSONObject>> users = getUserMap(WebUtil.fetch(request, "searchUser"), start, end);
        JSONArray rows = new JSONArray();
        for (Entry<String, HashSet<JSONObject>> e : users.entrySet()) {
            HashSet<JSONObject> sessions = e.getValue();
            int sessionCount = sessions.size();
            JSONObject session = null;
            for (JSONObject sess : sessions) {
                if (session == null) session = sess;
            }
            if (session != null) {
                session.put("sessionCount", sessionCount);
                session.put("user", e.getKey());

                // 添加用户状态信息
                if (Base.onlineUserManager != null) {
                    String status = Base.onlineUserManager.getUserStatus(e.getKey());
                    if (status != null) {
                        session.put("status", status);
                    }

                    // 添加最后活跃时间
                    Date lastActiveTime = Base.onlineUserManager.getUserLastActiveTime(e.getKey());
                    if (lastActiveTime != null) {
                        session.put("lastActiveTime", DateUtil.format(lastActiveTime));
                    }
                }

                rows.put(session);
            }
        }

        JSONObject result = new JSONObject();
        // 使用OnlineUserManager获取在线用户总数
        if (Base.onlineUserManager != null) {
            result.put("total", Base.onlineUserManager.getOnlineUserCount());
        } else {
            // 降级方案：使用ZSet的zCard方法来获取总的在线用户数
            result.put("total", Base.map.zsetSize(RedisCache.ONLINE_USERS));
        }
        result.put("rows", rows);
        WebUtil.send(response, result);
    }

    /**
     * 根据sessionId查找对应的session
     *
     * @param sessionId 会话ID
     * @return HttpSession对象
     */
    public static Session getSessionBySessionId(String sessionId) {
        FindByIndexNameSessionRepository<Session> sessionRepository = getSessionRepository();
        return sessionRepository.findById(sessionId);
    }
}