package com.wb.tool;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

import com.wb.util.DbUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FontUnderline;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.wb.common.Var;
import com.wb.util.DateUtil;
import com.wb.util.JsonUtil;
import com.wb.util.StringUtil;

/**
 * 用于Excel的工具方法。
 */
public class ExcelObject {
	/**
	 * 把包含Excel数据的输入流内容转换为JSON格式的字符串。每行数据以JSONObject存储，
	 * 不同行之间以换行符分隔。首行为字段名称，字段名称允许加备注，"()"内的内容将被忽略。
	 *
	 * @param inputStream 输入流。
	 * @param xlsxFormat  Excel格式，true为xlsx，false为xls。
	 * @return 转换为JSON格式的字符串。
	 * @throws Exception 转换过程发生异常。
	 */
	public static String excelToJson(InputStream inputStream, boolean xlsxFormat, boolean jsonFormat) throws Exception {
		int rowIndex = 0;
		int colLength = 0;

		JSONArray fieldList = new JSONArray();
		StringBuilder text = new StringBuilder("");
		Workbook book;
		if (xlsxFormat)
			book = new XSSFWorkbook(inputStream);
		else
			book = new HSSFWorkbook(inputStream);
		Iterator<Row> rows;
		Sheet sheet;
		try {
			sheet = book.getSheetAt(0);
			rows = sheet.rowIterator();
			if (jsonFormat)
				text.append("{rows:[");
			while (rows.hasNext()) {
				if (rowIndex > 1) {
					if (jsonFormat)
						text.append(',');
					else
						text.append("\n");
				}
				if (rowIndex > 0)
					text.append('{');
				Row row = (Row) rows.next();
				Iterator<Cell> cells = row.cellIterator();
				boolean isFirst = true;
				while (cells.hasNext()) {
					Cell cell = (Cell) cells.next();
					int colIndex = cell.getColumnIndex();
					Object value = getCellValue(cell);
					if (rowIndex == 0) {
						if (value == null)
							throw new NullPointerException("Field name has null value.");
						String valueStr = value.toString();
						int pos = valueStr.indexOf('(');
						int pos1 = valueStr.indexOf("（");
						if ((pos1 != -1) && ((pos == -1) || (pos1 < pos)))
							pos = pos1;
						if (pos == -1)
							fieldList.add(colIndex, valueStr);
						else
							fieldList.add(colIndex, valueStr.substring(0, pos));
					} else {
						if (isFirst)
							isFirst = false;
						else
							text.append(',');
						if (colIndex >= colLength)
							throw new RuntimeException(
									"Row " + (rowIndex + 1) + " column " + (colIndex + 1) + " is out of bounds.");
						text.append(StringUtil.quote(fieldList.getString(colIndex)));
						text.append(':');
						text.append(StringUtil.encode(value));
					}
				}
				if (rowIndex == 0)
					colLength = fieldList.length();
				else
					text.append('}');
				rowIndex++;
			}
			if (jsonFormat)
				text.append("]}");
		} finally {
			book.close();
		}
		return text.toString();
	}

	public static String excelToJson(File file) throws Exception {
		InputStream is = new FileInputStream(file);
		try {
			return excelToJson(is, true, true);
		} finally {
			is.close();
		}
	}

	public static String excelToJson(InputStream inputStream, boolean xlsxFormat, int beginRow, String tableName)
			throws Exception {
		Workbook book;
		Sheet sheet;
		Row row;
		Cell cell;
		Iterator<Row> rows;
		Object value;
		int rowIndex = 0;
		ArrayList<String> fieldList = new ArrayList<String>();
		JSONArray out = new JSONArray();
		JSONObject object;
		ResultSet rs = null;
		PreparedStatement st = null;
		Connection conn = DbUtil.getConnection();
		// 获取导入配置
		try {
			st = conn.prepareStatement(StringUtil.concat("select * from base_impsetting where table_name='",
					tableName, "' order by col_index"));
			rs = st.executeQuery();
			while (rs.next()) {
				fieldList.add(rs.getString("field_name"));
			}
		} finally {
			DbUtil.close(rs);
			DbUtil.close(st);
			DbUtil.close(conn);
		}

		if (xlsxFormat)
			book = new XSSFWorkbook(inputStream);
		else
			book = new HSSFWorkbook(inputStream);
		try {
			sheet = book.getSheetAt(0);
			rows = sheet.rowIterator();
			while (rows.hasNext()) {
				if (rowIndex <= beginRow - 1) {
					rows.next();
					rowIndex++;
					continue;
				}
				object = new JSONObject();
				row = rows.next();
				for (int i = 0; i < fieldList.size(); i++) {
					cell = row.getCell(i);
					value = (cell != null && getCellValue(cell) != null) ? getCellValue(cell) : "";
					object.put(fieldList.get(i), value);
				}
				out.put(object);
				rowIndex++;
			}
		} finally {
			book.close();
		}
		return out.toString();
	}


	/**
	 * 获取单元格用于显示的文本。
	 *
	 * @param cell 单元格。
	 * @return 单元格值。如果null返回空串。
	 */
	public static String getDisplayText(Cell cell) {
		Object object = getCellValue(cell);
		if (object == null) {
			return "";
		}
		if ((object instanceof Boolean))
			return object.toString();
		if ((object instanceof Double)) {
			double doubleVal = ((Double) object).doubleValue();
			String format = cell.getCellStyle().getDataFormatString();
			String value = StringUtil.formatNumber(doubleVal, convertNumFormat(format));
			// 多节格式，负数显示为红色
			if ((doubleVal < 0.0D) && (format != null) && (format.indexOf(';') != -1)
					&& (format.indexOf("[Red]") != -1))
				value = "<span style=\"color:red;\">" + value + "</span>";
			return value;
		}
		if ((object instanceof Date)) {
			return DateUtil.format((Date) object, "yyyy/M/d");
		}
		return object.toString();
	}

	/**
	 * 转换Excel数字格式为Java可用的数字格式。
	 *
	 * @param format excel数字格式。
	 * @return java数字格式。
	 */
	public static String convertNumFormat(String format) {
		if (format == null)
			return "0";
		if ("General".equals(format))
			return "0.##";
		String[] keys = {"\"", "_", "(", ")", "*", " ", "\\", "/"};
		int pos = format.indexOf(';');
		if (pos != -1)
			format = format.substring(0, pos);
		for (String key : keys) {
			format = StringUtil.replaceAll(format, key, "");
		}
		return format;
	}

	/**
	 * 根据对齐方法字符串返回对应Excel对齐值。
	 *
	 * @param align 对齐字符串。
	 * @return 对齐值。
	 */
	public static short getAlignment(String align, short defaultAlign) {
		if ("right".equals(align)) {
			return HorizontalAlignment.RIGHT.getCode();
		} else if ("center".equals(align)) {
			return HorizontalAlignment.CENTER.getCode();
		} else if ("left".equals(align)) {
			return HorizontalAlignment.LEFT.getCode();
		} else {
			return defaultAlign;
		}
	}

	/**
	 * 根据对齐方法字符串返回对应Excel对齐值。
	 *
	 * @param align 对齐字符串。
	 * @return 对齐值。
	 */
	public static HorizontalAlignment getAlignment(String align, HorizontalAlignment defaultAlign) {
		if ("right".equals(align)) {
			return HorizontalAlignment.RIGHT;
		} else if ("center".equals(align)) {
			return HorizontalAlignment.CENTER;
		} else if ("left".equals(align)) {
			return HorizontalAlignment.LEFT;
		} else {
			return defaultAlign;
		}
	}

	/**
	 * 根据Excel单元格的水平对齐值返回对应的HTML水平对齐字符串。
	 *
	 * @param align Excel单元格的对齐值。
	 * @return HTML对齐字符串。
	 */
	/**
	 * 根据Excel单元格的水平对齐值返回对应的HTML水平对齐字符串。
	 *
	 * @param align Excel单元格的对齐值。
	 * @return HTML对齐字符串。
	 */
	public static String toHtmlAlign(short align, String defaultAlign) {
		switch (HorizontalAlignment.forInt(align)) {
			case LEFT:
				return "left";
			case CENTER:
				return "center";
			case RIGHT:
				return "right";
			case JUSTIFY:
				return "justify";
			default:
				return defaultAlign;
		}
	}

	/**
	 * 根据Excel单元格的垂直对齐值返回对应的HTML垂直对齐字符串。
	 *
	 * @param align Excel单元格的对齐值。
	 * @return HTML对齐字符串。
	 */
	public static String toHtmlVerticalAlign(short align, String defaultAlign) {
		switch (VerticalAlignment.forInt(align)) {
			case TOP:
				return "top";
			case CENTER:
				return "middle";
			case BOTTOM:
				return "bottom";
			default:
				return defaultAlign;
		}
	}

	/**
	 * 根据设置的变量，创建并获取Excel指定版本的workbook对象。
	 *
	 * @return 新创建的workbook对象。
	 */
	public static Workbook getBook() throws IOException {
		if (Var.getBool("sys.service.excel.xlsx")) {
			return new XSSFWorkbook();
		}
		return new HSSFWorkbook();
	}

	/**
	 * 根据设置的变量，获取Excel文件的扩展名.xls或.xlsx。
	 *
	 * @return 指定版本的Excel文件扩展名。
	 */
	public static String getExtName() {
		if (Var.getBool("sys.service.excel.xlsx")) {
			return ".xlsx";
		}
		return ".xls";
	}

	/**
	 * 获取前端日期时间格式对应到Excel的日期时间格式。
	 *
	 * @param format        日期时间格式。
	 * @param returnDefault 如果格式不支持是否返回默认格式，true默认格式，false返回null。
	 * @return 转换后的Excel日期时间格式。
	 */
	public static String toExcelDateFormat(String format, boolean returnDefault) {
		String[] unSupportFormats = {"N", "S", "w", "z", "W", "t", "L", "o", "u", "O", "P", "T", "Z", "c", "U", "MS",
				"time", "timestamp"};
		String[][] supportFormats = {{"d", "dd"}, {"D", "aaa"}, {"j", "d"}, {"l", "aaaa"}, {"F", "mmmm"},
				{"m", "mm"}, {"M", "mmm"}, {"n", "m"}, {"Y", "yyyy"}, {"y", "yy"}, {"a", "am/pm"},
				{"A", "AM/PM"}, {"g", "h"}, {"G", "hh"}, {"h", "h"}, {"H", "hh"}, {"i", "mm"},
				{"s", "ss"}};

		for (String s : unSupportFormats) {
			if (format.indexOf(s) != -1)
				return returnDefault ? "yyyy-mm-dd" : null;
		}
		for (String[] s : supportFormats) {
			format = StringUtil.replaceAll(format, s[0], s[1]);
		}
		return format;
	}

	/**
	 * 获取前端日期时间格式对应到Java的日期时间格式。
	 *
	 * @param format        日期时间格式。
	 * @param returnDefault 如果格式不支持是否返回默认格式，true默认格式，false返回null。
	 * @return 转换后的Java日期时间格式。
	 */
	public static String toJavaDateFormat(String format, boolean returnDefault) {
		String[] unSupportFormats = {"N", "S", "D", "w", "z", "W", "t", "L", "o", "O", "P", "T", "Z", "c", "U", "F",
				"MS", "l", "M", "time", "timestamp"};
		String[][] supportFormats = {{"y", "yy"}, {"Y", "yyyy"}, {"m", "MM"}, {"n", "M"}, {"d", "dd"},
				{"j", "d"}, {"H", "HH"}, {"h", "hh"}, {"G", "H"}, {"g", "h"}, {"i", "mm"}, {"s", "ss"},
				{"u", "SSS"}, {"a", "'_x'"}, {"A", "'_X'"}};

		for (String s : unSupportFormats) {
			if (format.indexOf(s) != -1)
				return returnDefault ? "yyyy-MM-dd" : null;
		}
		for (String[] s : supportFormats) {
			format = StringUtil.replaceAll(format, s[0], s[1]);
		}
		return format;
	}

	/**
	 * 把Excel的单元格样式转换为HTML的样式。
	 *
	 * @param style    样式。
	 * @param isNumber 布尔值。
	 * @return 转换后的文本。
	 */
	public static String getCellStyle(XSSFCellStyle style, boolean isNumber) {
		StringBuilder buf = new StringBuilder();
		XSSFFont font = style.getFont();

		// 设置对齐方式
		buf.append("text-align:");
		buf.append(toHtmlAlign(style.getAlignment().getCode(), isNumber ? "right" : "left"));
		buf.append(";vertical-align:");
		buf.append(toHtmlVerticalAlign(style.getVerticalAlignment().getCode(), "middle"));
		// 设置字体
		buf.append(";font-family:");
		buf.append(font.getFontName());
		buf.append(";font-size:");
		buf.append(font.getFontHeightInPoints());
		buf.append("pt;font-weight:");
		buf.append(font.getBold());
		String rgb = getRGBColor(font.getXSSFColor());
		if (rgb != null) {
			buf.append(";color:");
			buf.append(rgb);
		}
		XSSFColor color = style.getFillForegroundXSSFColor();
		if (color != null) {
			rgb = getRGBColor(color);
			if (rgb != null) {
				buf.append(";background-color:");
				buf.append(rgb);
			}
		}
		if (font.getItalic())
			buf.append(";font-style:italic;");
		if (font.getStrikeout())
			buf.append(";text-decoration:line-through;");
		else if (FontUnderline.valueOf(font.getUnderline()) != FontUnderline.NONE) {
			buf.append(";text-decoration:underline;");
		}
		// 设置边框线
		buf.append(";border-top:");
		buf.append(getBorderStyle(style.getBorderTop().getCode(), style.getTopBorderXSSFColor()));
		buf.append(";border-right:");
		buf.append(getBorderStyle(style.getBorderRight().getCode(), style.getRightBorderXSSFColor()));
		buf.append(";border-bottom:");
		buf.append(getBorderStyle(style.getBorderBottom().getCode(), style.getBottomBorderXSSFColor()));
		buf.append(";border-left:");
		buf.append(getBorderStyle(style.getBorderLeft().getCode(), style.getLeftBorderXSSFColor()));
		return buf.toString();
	}

	/**
	 * 获取边框css样式。
	 *
	 * @param border excel边框样式。
	 * @param color  边框颜色。
	 * @return 边框css样式。
	 */
	private static String getBorderStyle(short border, XSSFColor color) {
		String width, style, rgb;

		switch (BorderStyle.valueOf(border)) {
			case DOTTED:
			case HAIR:
				width = "thin";
				style = "dotted";
				break;
			case DASH_DOT:
			case DASH_DOT_DOT:
			case SLANTED_DASH_DOT:
			case DASHED:
				width = "thin";
				style = "dashed";
				break;
			case DOUBLE:
				width = "thin";
				style = "double";
				break;
			case MEDIUM:
				width = "medium";
				style = "solid";
				break;
			case MEDIUM_DASH_DOT:
			case MEDIUM_DASH_DOT_DOT:
			case MEDIUM_DASHED:
				width = "medium";
				style = "dashed";
				break;
			case NONE:
				return "none";
			case THICK:
				width = "thick";
				style = "solid";
				break;
			default:
				width = "thin";
				style = "solid";
		}
		rgb = getRGBColor(color);
		if (rgb == null)
			rgb = "black";
		return String.format("%s %s %s", width, style, rgb);
	}

	/**
	 * 把Excel颜色转换为RGB格式的颜色字符串。
	 *
	 * @param color Excel颜色。
	 * @return RGB格式的颜色字符串。如果颜色无效或未设置返回null。
	 */
	public static String getRGBColor(XSSFColor color) {
		if (color == null)
			return null;
		int red, green, blue;
		byte[] xrgb = color.getRGBWithTint();

		if (xrgb == null)
			return null;
		red = (xrgb[0] < 0) ? (xrgb[0] + 256) : xrgb[0];
		green = (xrgb[1] < 0) ? (xrgb[1] + 256) : xrgb[1];
		blue = (xrgb[2] < 0) ? (xrgb[2] + 256) : xrgb[2];
		return String.format("#%02x%02x%02x", red, green, blue);
	}

	/**
	 * 获取单元格的值。
	 *
	 * @param cell 单元格。
	 * @return 单元格值。如果为空返回null。
	 */
	@SuppressWarnings("deprecation")
	public static Object getCellValue(Cell cell) {
		if (null == cell)
			return null;
		switch (cell.getCellType()) {
			case FORMULA:
			case NUMERIC:
				if (isDateFormatted(cell))
					return cell.getDateCellValue();
				else if (cell.getCellStyle().getDataFormatString().equals("@"))
					return ((Double) cell.getNumericCellValue()).intValue() + "";
				else
					return cell.getNumericCellValue();
			case STRING:
				return cell.getStringCellValue();
			case BOOLEAN:
				return cell.getBooleanCellValue();
			default:
				return null;
		}
	}

	/**
	 * 检查单元格是否格式化为日期。
	 *
	 * @param cell 需要判断的单元格对象。
	 * @return true格式化为日期，false没有格式化为日期。
	 */
	public static boolean isDateFormatted(Cell cell) {
		// 该方法区别于DateUtil.isCellDateFormatted方法，采用格式是否包括指定字符来判断。
		String format = cell.getCellStyle().getDataFormatString();
		if (format == null)
			return true;
		if (format.toLowerCase().equals("general"))
			return false;
		if (format.startsWith("reserved"))
			return true;
		int pos = format.lastIndexOf(']');
		if (pos != -1)
			format = format.substring(pos + 1);
		return (format.indexOf('0') == -1) && (format.indexOf('#') == -1);
	}

	/**
	 * 判断指定单元格是否为数字或公式单元格。日期类型单元格使用数字存储，因此也属于数字单元格。
	 *
	 * @param cell 单元格。
	 * @return true数字或公式单元格，false其他类型单元格。
	 */
	@SuppressWarnings("deprecation")
	public static boolean isNumericCell(Cell cell) {
		CellType cellType = cell.getCellType();
		return cellType == CellType.FORMULA || cellType == CellType.NUMERIC;
	}

	/**
	 * 填充表格数据到指定开始位置的单元格。新生成的单元格样式同起始单元格。
	 *
	 * @param sheet  填充的sheet对象。
	 * @param fill   填充的指令数据数组，每一项为一个对象。对象中name为参数名称，x为起始单元格x坐标，y为起始单元y坐标,
	 *               mergeCols是合并列，mergeRows是合并行。
	 * @param params 参数对象。
	 */
	public static void fillRows(Sheet sheet, JSONArray fill, JSONObject params) {
		if (fill == null)
			return;
		int i, j = fill.length(), k, l, m, n, x, y;
		String fields[];
		JSONObject obj, mergeConfig;
		JSONArray mergeInfo, mergeRows, mergeCols, mergeItem, subItem, data;

		for (i = 0; i < j; i++) {
			obj = fill.optJSONObject(i);
			// 添加数据
			x = obj.optInt("x");
			y = obj.optInt("y");
			data = JsonUtil.getArray(params.opt(obj.optString("name")));
			fields = createRows(sheet, x, y, data);
			if (data == null)
				continue;
			// 生成单元格合并的配置信息
			try {
				mergeConfig = new JSONObject();
				mergeRows = obj.optJSONArray("mergeRows");
				mergeCols = obj.optJSONArray("mergeCols");
				mergeInfo = new JSONArray();
				mergeConfig.put("mergeInfo", mergeInfo);
				if (mergeRows != null) {
					mergeConfig.put("mergeRows", true);
				}
				for (String field : fields) {
					mergeItem = new JSONArray();
					if (mergeRows != null)
						mergeItem.put(mergeRows.indexOf(field) != -1);
					else
						mergeItem.put(false);
					mergeItem.put(JSONObject.NULL);
					mergeInfo.put(mergeItem);
				}
				if (mergeCols != null) {
					mergeConfig.put("mergeCols", true);
					l = mergeCols.length();
					for (k = 0; k < l; k++) {
						subItem = mergeCols.getJSONArray(k);
						n = subItem.length();
						for (m = 0; m < n; m++) {
							mergeInfo.getJSONArray(StringUtil.indexOf(fields, subItem.getString(m))).put(1, "g" + k);
						}
					}
				}
			} catch (Throwable e) {
				throw new IllegalArgumentException("Invalid merge config " + obj.toString());
			}
			// 合并单元格
			ExcelObject.mergeCells(sheet, mergeConfig, y, y + data.length());
		}
	}

	public static void fillColumns(Sheet sheet, int x, int y, JSONArray data) {
		// 获取包含列标题的行
		Row headerRow = sheet.getRow(y);
		if (headerRow == null) {
			throw new IllegalArgumentException("Header row is missing at y=" + y);
		}
		// 读取并存储有效的键（即列标题）及其样式
		Map<String, CellStyle> validKeysWithStyle = new HashMap<>();
		for (int i = x; i < headerRow.getLastCellNum(); i++) {
			Cell headerCell = headerRow.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
			String key = headerCell.getStringCellValue().trim();
			validKeysWithStyle.put(key, headerCell.getCellStyle());
		}
		// 准备填充数据的行
		Row dataRow = sheet.getRow(y);
		if (dataRow == null) {
			dataRow = sheet.createRow(y);
		}
		// 横向填充数据
		int currentColumnIndex = x;
		for (int i = 0; i < data.length(); i++) {
			JSONObject obj = data.getJSONObject(i);
			for (String key : validKeysWithStyle.keySet()) {
				if (!obj.has(key)) {
					continue; // 如果JSONObject中不存在此键，则跳过
				}
				// 创建或获取单元格
				Cell cell = dataRow.getCell(currentColumnIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
				// 应用模板单元格的样式
				cell.setCellStyle(validKeysWithStyle.get(key));
				// 设置单元格的值
				Object value = obj.get(key);
				setCellValue(cell, value);
				// 移动到下一列
				currentColumnIndex++;
			}
		}
	}


	/**
	 * 把JSONArray对象中的数据直接输出至Sheet中由x,y指定的开始单元格。该方法将在起始单元格之后插入新的行。
	 *
	 * @param sheet sheet对象。
	 * @param x     单元格x坐标。
	 * @param y     单元格y坐标。
	 * @param recs  要插入的数据。
	 * @return 填充的字段名称组成的列表。
	 */
	public static String[] createRows(Sheet sheet, int x, int y, JSONArray recs) {
		Row originalRow = sheet.getRow(y);
		if (originalRow == null) return new String[0]; // 如果原始行不存在，则直接返回空字段列表
		CellStyle originalRowStyle = originalRow.getRowStyle();
		short originalHeight = originalRow.getHeight();
		int numberOfCells = originalRow.getLastCellNum();
		String[] fields = new String[numberOfCells];
		CellStyle[] styles = new CellStyle[numberOfCells];
		// 复制原始行的单元格内容和样式
		for (int k = 0; k < numberOfCells; k++) {
			Cell cell = originalRow.getCell(k, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
			fields[k] = cell.toString(); // 使用单元格的toString方法获取表示形式
			styles[k] = cell.getCellStyle();
		}
		// 如果没有要插入的数据，则删除原始行并返回
		if (recs == null || recs.length() == 0) {
			removeRow(sheet, y);
			return fields;
		}
		// 插入新的行
		int numberOfRows = recs.length();
		if (numberOfRows != 1) {
			sheet.shiftRows(y, sheet.getLastRowNum(), numberOfRows - 1, true, false);
		}
		for (int i = 0; i < numberOfRows; i++) {
			Row newRow = sheet.createRow(i + y);
			newRow.setRowStyle(originalRowStyle);
			newRow.setHeight(originalHeight);
			// 先复制x坐标之前的单元格
			for (int k = 0; k < x; k++) {
				Cell newCell = newRow.createCell(k);
				newCell.setCellStyle(styles[k]);
				Object cellValue = getCellValue(originalRow.getCell(k));
				setCellValue(newCell, cellValue); // 使用已有的setCellValue方法设置单元格值
			}
			// 填充新的数据到x坐标开始的单元格
			JSONObject rec = recs.optJSONObject(i);
			if (rec != null) {
				for (int k = x; k < numberOfCells; k++) {
					Cell newCell = newRow.createCell(k);
					newCell.setCellStyle(styles[k]);
					Object value = rec.has(fields[k]) ? rec.get(fields[k]) : null;
					setCellValue(newCell, value); // 为新单元格设置值
				}
			}
		}
		return fields;
	}

	/**
	 * 设置Excel单元格的值。该方法能把值依据其不同的类型填充到单元格中。 如果值为标准字符串日期格式，在设置值前系统将自动转换为日期格式。
	 *
	 * @param cell  单元格对象。
	 * @param value 需要设置的值。
	 */
	public static void setCellValue(Cell cell, Object value) {
		if (value == null) {
			cell.setCellValue("");
		} else if ((value instanceof String)) {
			String strVal = (String) value;
			if (DateUtil.isDate(strVal)) {
				cell.setCellValue(DateUtil.strToDate(strVal));
			} else {
				// 多行文本
				if (strVal.indexOf('\n') != -1)
					cell.getCellStyle().setWrapText(true);
					
				// 检查字符串是否类似于可能被Excel误认为日期的格式（如00-1-0）
				if (strVal.matches("\\d{2}-\\d{1,2}-\\d{1,2}") || strVal.matches("\\d{1,2}-\\d{1,2}-\\d{1,2}")) {
					// 设置单元格格式为文本格式，防止被识别为日期
					CellStyle textStyle = cell.getSheet().getWorkbook().createCellStyle();
					textStyle.cloneStyleFrom(cell.getCellStyle());
					DataFormat format = cell.getSheet().getWorkbook().createDataFormat();
					textStyle.setDataFormat(format.getFormat("@"));
					cell.setCellStyle(textStyle);
				}
				
				cell.setCellValue(strVal);
			}
		} else if ((value instanceof Number)) {
			double doubleValue = ((Number) value).doubleValue();
			cell.setCellValue(doubleValue);
			
			// 设置数字格式，防止被自动识别为日期
			String stringValue = value.toString();
			if (stringValue.contains("-") || stringValue.matches("\\d{1,2}\\.\\d{1,2}")) {
				CellStyle numberStyle = cell.getSheet().getWorkbook().createCellStyle();
				numberStyle.cloneStyleFrom(cell.getCellStyle());
				DataFormat format = cell.getSheet().getWorkbook().createDataFormat();
				numberStyle.setDataFormat(format.getFormat("0.00"));
				cell.setCellStyle(numberStyle);
			}
		} else if ((value instanceof Date)) {
			cell.setCellValue((Date) value);
		} else if ((value instanceof Boolean)) {
			cell.setCellValue(((Boolean) value).booleanValue());
		} else {
			String strVal = value.toString();
			// 多行文本
			if (strVal.indexOf('\n') != -1)
				cell.getCellStyle().setWrapText(true);
				
			// 检查字符串是否类似于可能被Excel误认为日期的格式
			if (strVal.matches("\\d{2}-\\d{1,2}-\\d{1,2}") || strVal.matches("\\d{1,2}-\\d{1,2}-\\d{1,2}")) {
				// 设置单元格格式为文本格式，防止被识别为日期
				CellStyle textStyle = cell.getSheet().getWorkbook().createCellStyle();
				textStyle.cloneStyleFrom(cell.getCellStyle());
				DataFormat format = cell.getSheet().getWorkbook().createDataFormat();
				textStyle.setDataFormat(format.getFormat("@"));
				cell.setCellStyle(textStyle);
			}
			
			cell.setCellValue(value.toString());
		}
	}

	/**
	 * 删除Sheet指定索引的行。该方法区别于sheet.removeRow，使用该方法删除行后下方行将上移。
	 *
	 * @param sheet    Sheet对象。
	 * @param rowIndex 删除的行索引号。
	 */
	public static void removeRow(Sheet sheet, int rowIndex) {
		int lastRowNum = sheet.getLastRowNum();

		// 如果包含单元格的合并信息则予以删除
		int mergeRegions = sheet.getNumMergedRegions();
		for (int i = mergeRegions - 1; i >= 0; i--) {
			CellRangeAddress range = sheet.getMergedRegion(i);
			if ((range.getFirstRow() <= rowIndex) && (range.getLastRow() >= rowIndex)) {
				sheet.removeMergedRegion(i);
			}
		}
		// 移动单元格
		if ((rowIndex >= 0) && (rowIndex < lastRowNum)) {
			sheet.shiftRows(rowIndex + 1, lastRowNum, -1);
		}
		// 如果为最后一行执行删除
		if (rowIndex == lastRowNum) {
			Row removingRow = sheet.getRow(rowIndex);
			if (removingRow != null)
				sheet.removeRow(removingRow);
		}
	}

	/**
	 * 执行Excel表格中的指令，指令位于最后一行并由"!!"开始。指令包括填充批量数据和合并单元格等。
	 *
	 * @param sheet  Sheet对象。
	 * @param params 参数对象。
	 */
	public static void executeInstruction(Sheet sheet, JSONObject params) {
		Cell cell;
		Object value;
		String instruction;
		int lastRowIndex;
		Row lastRow;
		lastRowIndex = sheet.getLastRowNum();
		lastRow = sheet.getRow(lastRowIndex);
		if (lastRow == null) return;
		cell = lastRow.getCell(0);
		if (cell == null) return;
		value = ExcelObject.getCellValue(cell);
		if (value == null) return;
		instruction = value.toString();
		instruction = StringUtil.replaceParams(params, instruction);
		if (instruction.startsWith("!!"))
			instruction = instruction.substring(2);
		else
			return;
		// 删除最后一行
		ExcelObject.removeRow(sheet, lastRowIndex);
		try {
			JSONArray instructions = null;
			try {
				// 首先尝试将instruction解析为JSONArray
				instructions = new JSONArray(instruction);
			} catch (JSONException e) {
				// 如果解析失败，则尝试解析为JSONObject
				try {
					JSONObject obj = new JSONObject(instruction);
					instructions = new JSONArray();
					instructions.put(obj); // 将JSONObject包装为JSONArray
				} catch (JSONException ex) {
					throw new IllegalArgumentException("Invalid instruction format: " + instruction);
				}
			}
			for (int i = 0; i < instructions.length(); i++) {
				JSONObject obj = instructions.getJSONObject(i);
				String direction = obj.optString("direction", "vertical"); // 默认为纵向填充
				if ("horizontal".equalsIgnoreCase(direction)) {
					// 横向填充
					JSONArray data = JsonUtil.getArray(params.opt(obj.optString("name")));
					fillColumns(sheet, obj.optInt("x"), obj.optInt("y"), data);
				} else {
					// 纵向填充
					JSONArray singleInstructionArray = new JSONArray();
					singleInstructionArray.put(obj); // 将单个指令包装成数组
					fillRows(sheet, singleInstructionArray, params);
				}
			}
		} catch (Throwable e) {
			throw new IllegalArgumentException("Invalid instruction format: " + instruction, e);
		}
	}


	/**
	 * 合并表格中需要合并的单元格。
	 *
	 * @param sheet    sheet对象。
	 * @param config   合并的配置信息以象。
	 * @param startRow 开始合并的行索引号。
	 * @param endRow   结束合并的行索引号。
	 */
	public static void mergeCells(Sheet sheet, JSONObject config, int startRow, int endRow) {
		JSONArray mergeInfo = config.optJSONArray("mergeInfo");
		int j = mergeInfo.length();
		int lastRowNum = sheet.getLastRowNum();
		int lastColNum = j - 1;

		// 合并行
		if (config.optBoolean("mergeRows")) {
			for (int i = 0; i < j; i++) {
				if (mergeInfo.getJSONArray(i).getBoolean(0)) {
					Iterator<Row> rows = sheet.rowIterator();
					int span = 0;
					int rowIndex = 0;
					String prevValue = null;
					while (rows.hasNext()) {
						Row row = (Row) rows.next();
						if (rowIndex < startRow) {
							rowIndex++;
						} else {
							if (rowIndex > endRow)
								break;
							Cell cell = row.getCell(i);
							Object object = getCellValue(cell);
							String value;
							if (object == null)
								value = "";
							else
								value = object.toString();
							boolean isLast = rowIndex == lastRowNum;
							if ((prevValue != null) && ((!value.equals(prevValue)) || (isLast))) {
								if (isLast) {
									if (value.equals(prevValue))
										span++; // 如果最后一行值相等合并单元格数+1
									else
										isLast = false; // 否则不执行最后一行合并的处理
								}
								if (span > 1) {
									if (isLast)
										sheet.addMergedRegion(
												new CellRangeAddress(rowIndex - span + 1, rowIndex - 1, i, i));
									else
										sheet.addMergedRegion(
												new CellRangeAddress(rowIndex - span, rowIndex - 1, i, i));
								}
								span = 0;
							}
							prevValue = value;
							span++;
							rowIndex++;
						}
					}
				}
			}
		}
		// 合并列
		int rowIndex = 0;
		if (config.optBoolean("mergeCols")) {
			Iterator<Row> rows = sheet.rowIterator();

			String[] colGroup = new String[j];
			for (int i = 0; i < j; i++) {
				colGroup[i] = mergeInfo.getJSONArray(i).optString(1);
			}
			while (rows.hasNext()) {
				Row row = (Row) rows.next();
				if (rowIndex < startRow) {
					rowIndex++;
				} else {
					if (rowIndex > endRow)
						break;
					Iterator<Cell> cells = row.cellIterator();
					int span = 0;
					int colIndex = 0;
					String prevValue = null;
					String prevGroup = null;
					while (cells.hasNext()) {
						Cell cell = (Cell) cells.next();
						Object object = getCellValue(cell);
						String group = colGroup[colIndex];
						String value;
						if (object == null)
							value = "";
						else
							value = object.toString();
						boolean isLast = colIndex == lastColNum;
						if ((prevValue != null)
								&& ((!value.equals(prevValue)) || (!group.equals(prevGroup)) || (isLast))) {
							if (isLast) {
								if (value.equals(prevValue))
									span++;
								else
									isLast = false;
							}
							if (span > 1) {
								if ((isLast) && (!group.isEmpty())
										&& (notInMergeRegion(sheet, rowIndex, colIndex - span + 1, colIndex)))
									sheet.addMergedRegion(
											new CellRangeAddress(rowIndex, rowIndex, colIndex - span + 1, colIndex));
								else if ((!prevGroup.isEmpty())
										&& (notInMergeRegion(sheet, rowIndex, colIndex - span, colIndex - 1))) {
									sheet.addMergedRegion(
											new CellRangeAddress(rowIndex, rowIndex, colIndex - span, colIndex - 1));
								}
							}
							span = 0;
						}
						prevValue = value;
						prevGroup = group;
						span++;
						colIndex++;
					}
					rowIndex++;
				}
			}
		}
	}

	private static boolean notInMergeRegion(Sheet sheet, int rowIndex, int beginCol, int endCol) {
		int j = sheet.getNumMergedRegions();

		for (int i = 0; i < j; i++) {
			CellRangeAddress range = sheet.getMergedRegion(i);
			int col = range.getFirstColumn();
			if ((range.getFirstRow() <= rowIndex) && (range.getLastRow() >= rowIndex) && (col >= beginCol)
					&& (col <= endCol))
				return false;
		}
		return true;
	}
}