package aliyun.sls;

import java.util.Random;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import com.aliyun.openservices.aliyun.log.producer.Callback;
import com.aliyun.openservices.aliyun.log.producer.LogProducer;
import com.aliyun.openservices.aliyun.log.producer.Producer;
import com.aliyun.openservices.aliyun.log.producer.ProducerConfig;
import com.aliyun.openservices.aliyun.log.producer.ProjectConfig;
import com.aliyun.openservices.aliyun.log.producer.Result;
import com.aliyun.openservices.aliyun.log.producer.errors.ProducerException;
import com.aliyun.openservices.log.common.LogItem;
import com.wb.util.StringUtil;

public class PerformanceTest {

	private static final Random random = new Random();

	public static void main(String[] args) throws InterruptedException {
		final String project = "meland-log";
		final String logStore = "sys-log";
		final String endpoint = "cn-shenzhen.log.aliyuncs.com";
		final String accessKeyId = "LTAI5tLWZkxLBJm7GLU9CcmV";
		final String accessKeySecret = "******************************";
		// 并发数
		int sendThreadCount = 2000;
		// 单个线程发送日志次数
		final int times = 1;
		// 默认处理器个数
		int ioThreadCount = Math.max(Runtime.getRuntime().availableProcessors(), 1);
		// 默认100M
		int totalSizeInBytes = 100 * 1024 * 1024;
		System.out.println(StringUtil.format(
				"project={0}, logStore={1}, endpoint={2}, sendThreadCount={3}, times={4}, ioThreadCount={5}, totalSizeInBytes={6}",
				project, logStore, endpoint, sendThreadCount, times, ioThreadCount, totalSizeInBytes));
		ExecutorService executorService = Executors.newFixedThreadPool(10);
		ProducerConfig producerConfig = new ProducerConfig();
		producerConfig.setBatchSizeThresholdInBytes(3 * 1024 * 1024);
		producerConfig.setBatchCountThreshold(40960);
		producerConfig.setIoThreadCount(ioThreadCount);
		producerConfig.setTotalSizeInBytes(totalSizeInBytes);
		producerConfig.setLingerMs(2000);
		
		final Producer producer = new LogProducer(producerConfig);
		producer.putProjectConfig(new ProjectConfig(project, endpoint, accessKeyId, accessKeySecret));

		final AtomicInteger successCount = new AtomicInteger(0);
		final CountDownLatch latch = new CountDownLatch(sendThreadCount);
		System.out.println("Test started.");
		long t1 = System.currentTimeMillis();
		for (int i = 0; i < sendThreadCount; ++i) {
			executorService.submit(new Runnable() {
				@Override
				public void run() {
					try {
						for (int i = 0; i < times; ++i) {
							int r = random.nextInt(times);
							producer.send(project, logStore, generateTopic(r), generateSource(r), generateLogItem(r),
									new Callback() {
										@Override
										public void onCompletion(Result result) {
											if (result.isSuccessful()) {
												successCount.incrementAndGet();
											}
										}
									});
						}
					} catch (Exception e) {
						e.printStackTrace();
					} finally {
						latch.countDown();
					}
				}
			});
		}
		latch.await();
		while (true) {
			if (successCount.get() == sendThreadCount * times) {
				break;
			}
			// Thread.sleep(100);
		}
		long t2 = System.currentTimeMillis();
		int totalCount = sendThreadCount * times;
		long timeCost = t2 - t1;
		int averageTimeCost = (int) (timeCost / totalCount);

		System.out.println("Test end.");
		System.out.println("======Summary======");
		System.out.println("并发线程数：" + sendThreadCount);
		System.out.println("每个线程发送日志次数：" + times);
		System.out.println("发送日志总条数：" + totalCount);
		System.out.println("总时长：" + timeCost + " millis");
		System.out.println("平均时长：" + averageTimeCost + " millis");
		try {
			producer.close();
		} catch (ProducerException e) {
			e.printStackTrace();
		}
		executorService.shutdown();
	}

	private static LogItem generateLogItem(int r) {
		LogItem logItem = new LogItem();
		logItem.PushBack("content_key_1", "1abcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_" + r);
		logItem.PushBack("content_key_2", "2abcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_" + r);
		logItem.PushBack("content_key_3", "3abcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_" + r);
		logItem.PushBack("content_key_4", "4abcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_" + r);
		logItem.PushBack("content_key_5", "5abcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_" + r);
		logItem.PushBack("content_key_6", "6abcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_" + r);
		logItem.PushBack("content_key_7", "7abcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_" + r);
		logItem.PushBack("content_key_8", "8abcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_" + r);
		return logItem;
	}

	private static String generateTopic(int r) {
		return "topic-" + r % 5;
	}

	private static String generateSource(int r) {
		return "source-" + r % 10;
	}

}
