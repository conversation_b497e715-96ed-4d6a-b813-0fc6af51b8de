/**
 * FeyaSoft MyCalendar Copyright(c) 2006-2012, FeyaSoft Inc. All right reserved. <EMAIL>
 * http://www.cubedrive.com Please read license first before your use myCalendar, For more detail information, please
 * can visit our link: http://www.cubedrive.com/myCalendar You need buy one of the Feyasoft's License if you want to use
 * MyCalendar in your commercial product. You must not remove, obscure or interfere with any FeyaSoft copyright,
 * acknowledgment, attribution, trademark, warning or disclaimer statement affixed to, incorporated in or otherwise
 * applied in connection with the Software. THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
 * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
Ext.define('Ext.ux.calendar.view.ResultView', {

  extend: 'Ext.ux.calendar.view.BasicView',

  initComponent: function() {
    this.ehandler.applyCalendarSetting(this);
    this.pageSize = 20;
    var lan = Ext.ux.calendar.Mask.ResultView;
    var store = Ext.ux.calendar.Mask.getEventStore(Ext.ux.calendar.CONST.searchURL, this.pageSize);
    var columns = [{
      header: '',
      sortable: true,
      menuDisabled: true,
      width: 20,
      dataIndex: 'calendarId',
      renderer: Ext.bind(this.hiddenRenderFn, this)
    }, {
      header: '',
      sortable: true,
      menuDisabled: true,
      width: 20,
      dataIndex: 'locked',
      renderer: Ext.bind(this.lockedRenderFn, this)
    }, {
      header: lan['cm.time'],
      dataIndex: 'ymd',
      sortable: true,
      width: 150,
      renderer: Ext.bind(this.fromtoRenderFn, this)
    }, {
      header: lan['cm.calendar'],
      sortable: true,
      menuDisabled: true,
      width: 100,
      dataIndex: 'calendarId',
      renderer: Ext.bind(this.calendarRenderFn, this)
    }, {
      header: lan['cm.subject'],
      sortable: true,
      width: 120,
      dataIndex: 'subject',
      renderer: this.subjectRenderFn
    }, {
      header: lan['cm.content'],
      sortable: true,
      width: 120,
      dataIndex: 'description',
      renderer: this.contentRenderFn
    }, {
      header: lan['cm.expire'],
      sortable: true,
      menuDisabled: true,
      renderer: Ext.bind(this.expireRenderFn, this)
    }];
    this.groupBtn = Ext.create('Ext.button.Button', {
      glyph: 0xf247,
      buttonStyle: 'default',
      text: lan['groupBtn.group.text'],
      handler: this.onGroupFn,
      scope: this
    });
    this.returnBtn = Ext.create('Ext.button.Button', {
      buttonStyle: 'primary',
      text: lan['returnBtn.text'],
      handler: this.onReturnFn,
      scope: this
    });
    var pbar = Ext.create('Ext.PagingToolbar', {
      pageSize: this.pageSize,
      store: store
    });
    pbar.on('render', this.onPageToolbarRenderFn, this);

    this.groupingFeature = Ext.create('Ext.grid.feature.Grouping', {
      groupHeaderTpl: '{rows.length} 个事件'
    });
    this.list = Ext.create('Ext.grid.Panel', {
      pagingBar: false,
      forceFit: true,
      store: store,
      features: [this.groupingFeature],
      columns: columns
    });
    Ext.apply(this, {
      layout: 'fit',
      items: [this.list],
      bbar: pbar
    });
    this.on('render', this.onRenderFn, this);
    this.list.on('itemdblclick', this.onRowDblClickFn, this);
    this.list.on('itemclick', this.onRowClickFn, this);
    store.on('load', this.onLoadFn, this);
    this.callParent(arguments);
  },

  onLoadFn: function(store, rds, options) {
    for (var i = 0, len = rds.length; i < len; i++) {
      var rd = rds[i];
      var rt = rd.data.repeatType;
      if ('no' != rt && 'exception' != rt) {
        rd.data.repeatType = Ext.decode(rt);
      }
    }
    this.groupingFeature.disable();
    var lan = Ext.ux.calendar.Mask.ResultView;
    this.groupBtn.setText(lan['groupBtn.group.text']);
  },

  onGroupFn: function(btn) {
    var lan = Ext.ux.calendar.Mask.ResultView;
    var store = this.list.getStore();
    if (lan['groupBtn.group.text'] == btn.getText()) {
      btn.setText(lan['groupBtn.unGroup.text']);
      btn.setGlyph(0xf248);
      this.groupingFeature.enable();
    } else {
      btn.setText(lan['groupBtn.group.text']);
      btn.setGlyph(0xf247);
      this.groupingFeature.disable();
    }
  },

  onReturnFn: function() {
    var calendarContainer = this.ownerCt;
    calendarContainer.getLayout().setActiveItem(calendarContainer.currentIdx);
    calendarContainer.currentView.checkLayout(true);
  },

  onRenderFn: function(p) {
    p.port = p.body;
  },

  onPageToolbarRenderFn: function(t) {
    t.add('->', this.groupBtn, '-', this.returnBtn);
  },
  onRowClickFn: function(grid, record, item, rowIndex, e) {
    var target = Ext.get(e.getTarget());
    if (target.hasCls("x-result-fromto")) {
      var store = this.list.getStore();
      var rd = store.getAt(rowIndex);
      var ymd = rd.data.ymd;
      this.fireEvent("selectymd", ymd, this);
    }
  },

  onRowDblClickFn: function(grid, record, item, rowIndex, e) {
    var cc = this.ownerCt;
    var cview = cc.currentView;
    var eh = this.ehandler;
    var store = grid.getStore();
    var rd = store.getAt(rowIndex);
    if (!rd.data.locked) {
      var rowEls = this.list.body.select("tr[class*=x-grid-row]");
      var rowEl = rowEls.elements[rowIndex];
      if (rowEl) {
        rowEl.bindEvent = {
          eventId: rd.data.id,
          calendarId: rd.data.calendarId,
          startRow: parseInt(rd.data.startRow),
          endRow: parseInt(rd.data.endRow),
          subject: rd.data.subject,
          content: rd.data.description,
          day: rd.data.ymd,
          eday: rd.data.eymd,
          isShared: rd.data.isShared,
          alertFlag: rd.data.alertFlag,
          locked: rd.data.locked,
          repeatType: rd.data.repeatType
        };
        rowEl.cview = cview;
        var obj = {
          bindEl: rowEl,
          cview: this,
          onLayout: function(event) {
            rd.set('calendarId', event.calendarId);
            rd.set('subject', event.subject);
            rd.set('description', event.content);
            rd.set('ymd', event.day);
            rd.set('eymd', event.eday);
            rd.set('isShared', event.isShared);
            rd.set('alertFlag', event.alertFlag);
            rd.set('locked', event.locked);
            rd.set('repeatType', event.repeatType);
          },
          action: 'update'
        };
        eh.editor.fireEvent('showdetailsetting', obj);
      }
    }
  },

  calendarRenderFn: function(value, meta, rd, row, col, store) {
    var legendStyle = "height:9px;";
    var calendarId = rd.data.calendarId;
    var eh = this.ehandler;
    var calendar = eh.calendarSet[calendarId];
    var html = eh.calendarTpl.apply({
      calendarId: calendar.id,
      'legend-style': legendStyle,
      title: calendar.name,
      color: calendar.color,
      _color: '#' + Ext.ux.calendar.Mask.getColorByIndex(calendar.color)
    });
    return html;
  },

  fromtoRenderFn: function(value, meta, rd, row, col, store) {
    if (rd && rd.data) {
      var data = rd.data;
      var getRowFromHM = Ext.ux.calendar.Mask.getRowFromHM;
      data.startRow = getRowFromHM(rd.data.startTime, this.intervalSlot);
      data.endRow = getRowFromHM(rd.data.endTime, this.intervalSlot);
      data.day = data.ymd;
      data.eday = data.eymd;
    }
    var html = "<span class=\"x-result-fromto\">" + this.ehandler.generateInfo(data) + "</span>";
    return html;
  },

  subjectRenderFn: function(value, meta, rd, row, col, store) {
    var html = value || '';
    if ('' == html.trim()) {
      html = Ext.ux.calendar.Mask.ResultView['noSubject'];
    }
    return html;
  },

  contentRenderFn: function(value, meta, rd, row, col, store) {
    var html = value || '';
    if ('' == html.trim()) {
      html = Ext.ux.calendar.Mask.ResultView['noContent'];
    }
    return html;
  },

  generateExpireHTML: function(data) {
    var lan = Ext.ux.calendar.Mask.ResultView;
    var hour = data.hour;
    var html;
    if (-1 == hour) {
      html = '<div style="border:1px solid silver;height:20px;line-height:20px;background:rgb(231,231,231);text-align:center;">' + '<b>已过时</b>' + '</div>';
    } else if (0 <= hour && hour <= 24) {
      html = '<div style="border:1px solid silver;height:20px;line-height:20px;background:rgb(249,66,51);text-align:center;">' + '<b>' + hour + ' ' + lan['hour'] + '</b>' + '</div>';
    } else if (24 < hour && hour <= 72) {
      html = '<div style="border:1px solid silver;height:20px;line-height:20px;background:rgb(255,255,164);text-align:center;">' + '<b>' + hour + ' ' + lan['hour'] + '</b>' + '</div>';
    } else if (72 < hour) {
      html = '<div style="border:1px solid silver;height:20px;line-height:20px;background:rgb(147,250,97);text-align:center;">' + '<b>' + hour + ' ' + lan['hour'] + '</b>' + '</div>';
    }
    return html;
  },

  expireRenderFn: function(value, meta, rd, row, col, store) {
    var vDate = Ext.Date;
    var endRow = Ext.ux.calendar.Mask.getRowFromHM(rd.data.endTime, this.intervalSlot);
    var str = rd.data.ymd + ' ' + rd.data.endTime;
    var day = vDate.parseDate(str, 'Y-m-d H:i');
    if (this.rowCount == endRow) {
      day = vDate.add(day, Date.DAY, 1);
      str = vDate.format(day, 'Y-m-d H:i');
    }
    var offset = vDate.getElapsed(day);
    if (vDate.format((new Date()), 'Y-m-d H:i') >= str) {
      return this.generateExpireHTML({
        hour: -1
      });
    } else {
      var hour = Math.round(offset / 3600000);
      return this.generateExpireHTML({
        hour: hour
      });
    }
  },

  hiddenRenderFn: function(value, meta, rd, row, col, store) {
    var html;
    var cs = this.ehandler.calendarSet;
    if (cs[rd.data.calendarId].hide) {
      html = "<span class=\"wb_glyph\" style=\"line-height:0.75em;font-size:1.2em;\">&#xf070</span>";
    } else {
      html = "<span class=\"wb_glyph\" style=\"line-height:0.75em;font-size:1.2em;\">&#xf06e</span>";
    }
    return html;
  },

  lockedRenderFn: function(value, meta, rd, row, col, store) {
    var html;
    if (rd.data.locked) {
      html = "<span class=\"wb_glyph\" style=\"line-height:0.75em;font-size:1.2em;\">&#xf023</span>";
    } else {
      html = "<span class=\"wb_glyph\" style=\"line-height:0.75em;font-size:1.2em;\">&#xf09c</span>";
    }
    return html;
  },

  loadEvents: function(text) {
    var store = this.list.getStore();
    this.matchText = text;
    store.load({
      params: {
        text: text,
        userId: this.ehandler.mainPanel.userId,
        start: 0,
        limit: this.pageSize
      }
    });
  },

  checkLayout: function() {
    var store = this.list.getStore();
    store.load();
  }
});