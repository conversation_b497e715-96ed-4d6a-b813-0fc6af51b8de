/**
 * FeyaSoft MyCalendar Copyright(c) 2006-2012, FeyaSoft Inc. All right reserved. <EMAIL>
 * http://www.cubedrive.com Please read license first before your use myCalendar, For more detail information, please
 * can visit our link: http://www.cubedrive.com/myCalendar You need buy one of the Feyasoft's License if you want to use
 * MyCalendar in your commercial product. You must not remove, obscure or interfere with any FeyaSoft copyright,
 * acknowledgment, attribution, trademark, warning or disclaimer statement affixed to, incorporated in or otherwise
 * applied in connection with the Software. THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
 * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
Ext.ns("Ext.ux.calendar");

Ext.ux.calendar.CONST = {
  /*
   * The version number of myCalendar
   */
  VERSION: '2.6.1',
  /*
   *true to show the language submenu in myCalendar, or not
   */
  SHOW_LANGUAGE_MENU: true,

  /*
   *define the main path of myCalendar
   */
  MAIN_PATH: 'wb/calendar/js/calendar/',
  /*
   *define the multi-language path of myCalendar
   */
  CALENDAR_LANGUAGE_PATH: 'wb/calendar/js/calendar/multi-language/',
  /*
   *define the multi-language path of EXT
   */
  EXT_LANGUAGE_PATH: 'wb/libs/ext/locale/',
  /*
   * define the some url here for datasource
   */
  searchURL: 'm?xwl=my/oa/calendar/search',

  showAllCalendarURL: 'm?xwl=my/oa/calendar/showAllCalendar', // 显示全部类型

  showOnlyCalendarURL: 'm?xwl=my/oa/calendar/showOnlyCalendar', // 显示指定类型

  createUpdateCalendarURL: 'm?xwl=my/oa/calendar/createUpdateCalendar', // 新增或更新类型

  deleteEventsByCalendarURL: 'm?xwl=my/oa/calendar/deleteEventsByCalendar', // 删除指定类型事件

  deleteCalendarURL: 'm?xwl=my/oa/calendar/deleteCalendar', // 删除日程类型

  loadCalendarURL: 'm?xwl=my/oa/calendar/loadCalendar', // 加日程类型

  loadEventURL: 'm?xwl=my/oa/calendar/loadEvent', // 加载日程事件

  loadRepeatEventURL: 'm?xwl=my/oa/calendar/loadRepeatEvent',

  createEventURL: 'm?xwl=my/oa/calendar/createEvent',

  updateEventURL: 'm?xwl=my/oa/calendar/updateEvent',

  deleteEventURL: 'm?xwl=my/oa/calendar/deleteEvent',

  deleteRepeatEventURL: 'm?xwl=my/oa/calendar/deleteRepeatEvent',

  changeDayURL: 'm?xwl=my/oa/calendar/changeDay',

  deleteDayURL: 'm?xwl=my/oa/calendar/deleteDay',

  loadSettingURL: 'm?xwl=my/oa/calendar/loadSetting',

  updateSettingURL: 'm?xwl=my/oa/calendar/updateSetting',

  createUpdateRepeatEventURL: 'm?xwl=my/oa/calendar/createUpdateRepeatEvent',

  initialLoadURL: 'm?xwl=my/oa/calendar/initialLoad' // 初始化日程
};