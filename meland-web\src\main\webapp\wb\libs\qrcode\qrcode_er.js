var QRCode;(function(){function d(y){this.mode=c.MODE_8BIT_BYTE;this.data=y;this.parsedData=[];for(var w=0,v=this.data.length;w<v;w++){var u=[];var x=this.data.charCodeAt(w);if(x>65536){u[0]=240|((x&1835008)>>>18);u[1]=128|((x&258048)>>>12);u[2]=128|((x&4032)>>>6);u[3]=128|(x&63)}else{if(x>2048){u[0]=224|((x&61440)>>>12);u[1]=128|((x&4032)>>>6);u[2]=128|(x&63)}else{if(x>128){u[0]=192|((x&1984)>>>6);u[1]=128|(x&63)}else{u[0]=x}}}this.parsedData.push(u)}this.parsedData=Array.prototype.concat.apply([],this.parsedData);if(this.parsedData.length!=this.data.length){this.parsedData.unshift(191);this.parsedData.unshift(187);this.parsedData.unshift(239)}}d.prototype={getLength:function(i){return this.parsedData.length},write:function(v){for(var w=0,u=this.parsedData.length;w<u;w++){v.put(this.parsedData[w],8)}}};function r(u,i){this.typeNumber=u;this.errorCorrectLevel=i;this.modules=null;this.moduleCount=0;this.dataCache=null;this.dataList=[]}r.prototype={addData:function(u){var i=new d(u);this.dataList.push(i);this.dataCache=null},isDark:function(u,i){if(u<0||this.moduleCount<=u||i<0||this.moduleCount<=i){throw new Error(u+","+i)}return this.modules[u][i]},getModuleCount:function(){return this.moduleCount},make:function(){this.makeImpl(false,this.getBestMaskPattern())},makeImpl:function(w,v){this.moduleCount=this.typeNumber*4+17;this.modules=new Array(this.moduleCount);for(var u=0;u<this.moduleCount;u++){this.modules[u]=new Array(this.moduleCount);for(var i=0;i<this.moduleCount;i++){this.modules[u][i]=null}}this.setupPositionProbePattern(0,0);this.setupPositionProbePattern(this.moduleCount-7,0);this.setupPositionProbePattern(0,this.moduleCount-7);this.setupPositionAdjustPattern();this.setupTimingPattern();this.setupTypeInfo(w,v);if(this.typeNumber>=7){this.setupTypeNumber(w)}if(this.dataCache==null){this.dataCache=r.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)}this.mapData(this.dataCache,v)},setupPositionProbePattern:function(v,i){for(var u=-1;u<=7;u++){if(v+u<=-1||this.moduleCount<=v+u){continue}for(var w=-1;w<=7;w++){if(i+w<=-1||this.moduleCount<=i+w){continue}if((0<=u&&u<=6&&(w==0||w==6))||(0<=w&&w<=6&&(u==0||u==6))||(2<=u&&u<=4&&2<=w&&w<=4)){this.modules[v+u][i+w]=true}else{this.modules[v+u][i+w]=false}}}},getBestMaskPattern:function(){var x=0;var w=0;for(var v=0;v<8;v++){this.makeImpl(true,v);var u=k.getLostPoint(this);if(v==0||x>u){x=u;w=v}}return w},createMovieClip:function(z,i,v){var D=z.createEmptyMovieClip(i,v);var w=1;this.make();for(var E=0;E<this.modules.length;E++){var B=E*w;for(var u=0;u<this.modules[E].length;u++){var C=u*w;var A=this.modules[E][u];if(A){D.beginFill(0,100);D.moveTo(C,B);D.lineTo(C+w,B);D.lineTo(C+w,B+w);D.lineTo(C,B+w);D.endFill()}}}return D},setupTimingPattern:function(){for(var i=8;i<this.moduleCount-8;i++){if(this.modules[i][6]!=null){continue}this.modules[i][6]=(i%2==0)}for(var u=8;u<this.moduleCount-8;u++){if(this.modules[6][u]!=null){continue}this.modules[6][u]=(u%2==0)}},setupPositionAdjustPattern:function(){var A=k.getPatternPosition(this.typeNumber);for(var w=0;w<A.length;w++){for(var v=0;v<A.length;v++){var y=A[w];var u=A[v];if(this.modules[y][u]!=null){continue}for(var x=-2;x<=2;x++){for(var z=-2;z<=2;z++){if(x==-2||x==2||z==-2||z==2||(x==0&&z==0)){this.modules[y+x][u+z]=true}else{this.modules[y+x][u+z]=false}}}}}},setupTypeNumber:function(x){var w=k.getBCHTypeNumber(this.typeNumber);for(var v=0;v<18;v++){var u=(!x&&((w>>v)&1)==1);this.modules[Math.floor(v/3)][v%3+this.moduleCount-8-3]=u}for(var v=0;v<18;v++){var u=(!x&&((w>>v)&1)==1);this.modules[v%3+this.moduleCount-8-3][Math.floor(v/3)]=u}},setupTypeInfo:function(z,y){var x=(this.errorCorrectLevel<<3)|y;var w=k.getBCHTypeInfo(x);for(var v=0;v<15;v++){var u=(!z&&((w>>v)&1)==1);if(v<6){this.modules[v][8]=u}else{if(v<8){this.modules[v+1][8]=u}else{this.modules[this.moduleCount-15+v][8]=u}}}for(var v=0;v<15;v++){var u=(!z&&((w>>v)&1)==1);if(v<8){this.modules[8][this.moduleCount-v-1]=u}else{if(v<9){this.modules[8][15-v-1+1]=u}else{this.modules[8][15-v-1]=u}}}this.modules[this.moduleCount-8][8]=(!z)},mapData:function(y,u){var w=-1;var C=this.moduleCount-1;var x=7;var i=0;for(var v=this.moduleCount-1;v>0;v-=2){if(v==6){v--}while(true){for(var A=0;A<2;A++){if(this.modules[C][v-A]==null){var z=false;if(i<y.length){z=(((y[i]>>>x)&1)==1)}var B=k.getMask(u,C,v-A);if(B){z=!z}this.modules[C][v-A]=z;x--;if(x==-1){i++;x=7}}}C+=w;if(C<0||this.moduleCount<=C){C-=w;w=-w;break}}}}};r.PAD0=236;r.PAD1=17;r.createData=function(B,A,x){var v=q.getRSBlocks(B,A);var u=new l();for(var w=0;w<x.length;w++){var z=x[w];u.put(z.mode,4);u.put(z.getLength(),k.getLengthInBits(z.mode,B));z.write(u)}var y=0;for(var w=0;w<v.length;w++){y+=v[w].dataCount}if(u.getLengthInBits()>y*8){throw new Error("code length overflow. ("+u.getLengthInBits()+">"+y*8+")")}if(u.getLengthInBits()+4<=y*8){u.put(0,4)}while(u.getLengthInBits()%8!=0){u.putBit(false)}while(true){if(u.getLengthInBits()>=y*8){break}u.put(r.PAD0,8);if(u.getLengthInBits()>=y*8){break}u.put(r.PAD1,8)}return r.createBytes(u,v)};r.createBytes=function(E,H){var w=0;var K=0;var I=0;var v=new Array(H.length);var z=new Array(H.length);for(var C=0;C<H.length;C++){var D=H[C].dataCount;var u=H[C].totalCount-D;K=Math.max(K,D);I=Math.max(I,u);v[C]=new Array(D);for(var F=0;F<v[C].length;F++){v[C][F]=255&E.buffer[F+w]}w+=D;var A=k.getErrorCorrectPolynomial(u);var J=new p(v[C],A.getLength()-1);var x=J.mod(A);z[C]=new Array(A.getLength()-1);for(var F=0;F<z[C].length;F++){var B=F+x.getLength()-z[C].length;z[C][F]=(B>=0)?x.get(B):0}}var G=0;for(var F=0;F<H.length;F++){G+=H[F].totalCount}var L=new Array(G);var y=0;for(var F=0;F<K;F++){for(var C=0;C<H.length;C++){if(F<v[C].length){L[y++]=v[C][F]}}}for(var F=0;F<I;F++){for(var C=0;C<H.length;C++){if(F<z[C].length){L[y++]=z[C][F]}}}return L};var c={MODE_NUMBER:1<<0,MODE_ALPHA_NUM:1<<1,MODE_8BIT_BYTE:1<<2,MODE_KANJI:1<<3};var f={L:1,M:0,Q:3,H:2};var t={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};var k={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:(1<<10)|(1<<8)|(1<<5)|(1<<4)|(1<<2)|(1<<1)|(1<<0),G18:(1<<12)|(1<<11)|(1<<10)|(1<<9)|(1<<8)|(1<<5)|(1<<2)|(1<<0),G15_MASK:(1<<14)|(1<<12)|(1<<10)|(1<<4)|(1<<1),getBCHTypeInfo:function(i){var u=i<<10;while(k.getBCHDigit(u)-k.getBCHDigit(k.G15)>=0){u^=(k.G15<<(k.getBCHDigit(u)-k.getBCHDigit(k.G15)))}return((i<<10)|u)^k.G15_MASK},getBCHTypeNumber:function(i){var u=i<<12;while(k.getBCHDigit(u)-k.getBCHDigit(k.G18)>=0){u^=(k.G18<<(k.getBCHDigit(u)-k.getBCHDigit(k.G18)))}return(i<<12)|u},getBCHDigit:function(i){var u=0;while(i!=0){u++;i>>>=1}return u},getPatternPosition:function(i){return k.PATTERN_POSITION_TABLE[i-1]},getMask:function(w,v,u){switch(w){case t.PATTERN000:return(v+u)%2==0;case t.PATTERN001:return v%2==0;case t.PATTERN010:return u%3==0;case t.PATTERN011:return(v+u)%3==0;case t.PATTERN100:return(Math.floor(v/2)+Math.floor(u/3))%2==0;case t.PATTERN101:return(v*u)%2+(v*u)%3==0;case t.PATTERN110:return((v*u)%2+(v*u)%3)%2==0;case t.PATTERN111:return((v*u)%3+(v+u)%2)%2==0;default:throw new Error("bad maskPattern:"+w)}},getErrorCorrectPolynomial:function(v){var u=new p([1],0);for(var w=0;w<v;w++){u=u.multiply(new p([1,o.gexp(w)],0))}return u},getLengthInBits:function(u,i){if(1<=i&&i<10){switch(u){case c.MODE_NUMBER:return 10;case c.MODE_ALPHA_NUM:return 9;case c.MODE_8BIT_BYTE:return 8;case c.MODE_KANJI:return 8;default:throw new Error("mode:"+u)}}else{if(i<27){switch(u){case c.MODE_NUMBER:return 12;case c.MODE_ALPHA_NUM:return 11;case c.MODE_8BIT_BYTE:return 16;case c.MODE_KANJI:return 10;default:throw new Error("mode:"+u)}}else{if(i<41){switch(u){case c.MODE_NUMBER:return 14;case c.MODE_ALPHA_NUM:return 13;case c.MODE_8BIT_BYTE:return 16;case c.MODE_KANJI:return 12;default:throw new Error("mode:"+u)}}else{throw new Error("type:"+i)}}}},getLostPoint:function(u){var w=u.getModuleCount();var x=0;for(var E=0;E<w;E++){for(var v=0;v<w;v++){var C=0;var B=u.isDark(E,v);for(var i=-1;i<=1;i++){if(E+i<0||w<=E+i){continue}for(var A=-1;A<=1;A++){if(v+A<0||w<=v+A){continue}if(i==0&&A==0){continue}if(B==u.isDark(E+i,v+A)){C++}}}if(C>5){x+=(3+C-5)}}}for(var E=0;E<w-1;E++){for(var v=0;v<w-1;v++){var y=0;if(u.isDark(E,v)){y++}if(u.isDark(E+1,v)){y++}if(u.isDark(E,v+1)){y++}if(u.isDark(E+1,v+1)){y++}if(y==0||y==4){x+=3}}}for(var E=0;E<w;E++){for(var v=0;v<w-6;v++){if(u.isDark(E,v)&&!u.isDark(E,v+1)&&u.isDark(E,v+2)&&u.isDark(E,v+3)&&u.isDark(E,v+4)&&!u.isDark(E,v+5)&&u.isDark(E,v+6)){x+=40}}}for(var v=0;v<w;v++){for(var E=0;E<w-6;E++){if(u.isDark(E,v)&&!u.isDark(E+1,v)&&u.isDark(E+2,v)&&u.isDark(E+3,v)&&u.isDark(E+4,v)&&!u.isDark(E+5,v)&&u.isDark(E+6,v)){x+=40}}}var D=0;for(var v=0;v<w;v++){for(var E=0;E<w;E++){if(u.isDark(E,v)){D++}}}var z=Math.abs(100*D/w/w-50)/5;x+=z*10;return x}};var o={glog:function(i){if(i<1){throw new Error("glog("+i+")")}return o.LOG_TABLE[i]},gexp:function(i){while(i<0){i+=255}while(i>=256){i-=255}return o.EXP_TABLE[i]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)};for(var n=0;n<8;n++){o.EXP_TABLE[n]=1<<n}for(var n=8;n<256;n++){o.EXP_TABLE[n]=o.EXP_TABLE[n-4]^o.EXP_TABLE[n-5]^o.EXP_TABLE[n-6]^o.EXP_TABLE[n-8]}for(var n=0;n<255;n++){o.LOG_TABLE[o.EXP_TABLE[n]]=n}function p(v,u){if(v.length==undefined){throw new Error(v.length+"/"+u)}var x=0;while(x<v.length&&v[x]==0){x++}this.num=new Array(v.length-x+u);for(var w=0;w<v.length-x;w++){this.num[w]=v[w+x]}}p.prototype={get:function(i){return this.num[i]},getLength:function(){return this.num.length},multiply:function(x){var v=new Array(this.getLength()+x.getLength()-1);for(var w=0;w<this.getLength();w++){for(var u=0;u<x.getLength();u++){v[w+u]^=o.gexp(o.glog(this.get(w))+o.glog(x.get(u)))}}return new p(v,0)},mod:function(x){if(this.getLength()-x.getLength()<0){return this}var w=o.glog(this.get(0))-o.glog(x.get(0));var u=new Array(this.getLength());for(var v=0;v<this.getLength();v++){u[v]=this.get(v)}for(var v=0;v<x.getLength();v++){u[v]^=o.gexp(o.glog(x.get(v))+w)}return new p(u,0).mod(x)}};function q(i,u){this.totalCount=i;this.dataCount=u}q.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];q.getRSBlocks=function(w,C){var v=q.getRsBlockTable(w,C);if(v==undefined){throw new Error("bad rs block @ typeNumber:"+w+"/errorCorrectLevel:"+C)}var u=v.length/3;var A=[];for(var y=0;y<u;y++){var z=v[y*3+0];var D=v[y*3+1];var B=v[y*3+2];for(var x=0;x<z;x++){A.push(new q(D,B))}}return A};q.getRsBlockTable=function(u,i){switch(i){case f.L:return q.RS_BLOCK_TABLE[(u-1)*4+0];case f.M:return q.RS_BLOCK_TABLE[(u-1)*4+1];case f.Q:return q.RS_BLOCK_TABLE[(u-1)*4+2];case f.H:return q.RS_BLOCK_TABLE[(u-1)*4+3];default:return undefined}};function l(){this.buffer=[];this.length=0}l.prototype={get:function(i){var u=Math.floor(i/8);return((this.buffer[u]>>>(7-i%8))&1)==1},put:function(u,w){for(var v=0;v<w;v++){this.putBit(((u>>>(w-v-1))&1)==1)}},getLengthInBits:function(){return this.length},putBit:function(u){var i=Math.floor(this.length/8);if(this.buffer.length<=i){this.buffer.push(0)}if(u){this.buffer[i]|=(128>>>(this.length%8))}this.length++}};var m=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];function s(){return typeof CanvasRenderingContext2D!="undefined"}function g(){var i=false;var v=navigator.userAgent;if(/android/i.test(v)){i=true;var u=v.toString().match(/android ([0-9]\.[0-9])/i);if(u&&u[1]){i=parseFloat(u[1])}}return i}var e=(function(){var i=function(u,v){this._el=u;this._htOption=v};i.prototype.draw=function(x){var E=this._htOption;var C=this._el;var u=x.getModuleCount();var A=Math.floor(E.width/u);var B=Math.floor(E.height/u);this.clear();function y(F,H){var I=document.createElementNS("http://www.w3.org/2000/svg",F);for(var G in H){if(H.hasOwnProperty(G)){I.setAttribute(G,H[G])}}return I}var z=y("svg",{viewBox:"0 0 "+String(u)+" "+String(u),width:"100%",height:"100%",fill:E.colorLight});z.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:xlink","http://www.w3.org/1999/xlink");C.appendChild(z);z.appendChild(y("rect",{fill:E.colorLight,width:"100%",height:"100%"}));z.appendChild(y("rect",{fill:E.colorDark,width:"1",height:"1",id:"template"}));for(var D=0;D<u;D++){for(var w=0;w<u;w++){if(x.isDark(D,w)){var v=y("use",{x:String(w),y:String(D)});v.setAttributeNS("http://www.w3.org/1999/xlink","href","#template");z.appendChild(v)}}}};i.prototype.clear=function(){while(this._el.hasChildNodes()){this._el.removeChild(this._el.lastChild)}};return i})();var b=document.documentElement.tagName.toLowerCase()==="svg";var h=b?e:!s()?(function(){var i=function(u,v){this._el=u;this._htOption=v};i.prototype.draw=function(y){var F=this._htOption;var C=this._el;var u=y.getModuleCount();var z=Math.floor(F.width/u);var B=Math.floor(F.height/u);var D=['<table style="border:0;border-collapse:collapse;">'];for(var E=0;E<u;E++){D.push("<tr>");for(var w=0;w<u;w++){D.push('<td style="border:0;border-collapse:collapse;padding:0;margin:0;width:'+z+"px;height:"+B+"px;background-color:"+(y.isDark(E,w)?F.colorDark:F.colorLight)+';"></td>')}D.push("</tr>")}D.push("</table>");C.innerHTML=D.join("");var A=C.childNodes[0];var x=(F.width-A.offsetWidth)/2;var v=(F.height-A.offsetHeight)/2;if(x>0&&v>0){A.style.margin=v+"px "+x+"px"}};i.prototype.clear=function(){this._el.innerHTML=""};return i})():(function(){function i(){this._elImage.src=this._elCanvas.toDataURL("image/png");this._elImage.style.display="block";this._elCanvas.style.display="none"}if(this._android&&this._android<=2.1){var v=1/window.devicePixelRatio;var w=CanvasRenderingContext2D.prototype.drawImage;CanvasRenderingContext2D.prototype.drawImage=function(z,E,D,F,B,H,G,y,C){if(("nodeName" in z)&&/img/i.test(z.nodeName)){for(var A=arguments.length-1;A>=1;A--){arguments[A]=arguments[A]*v}}else{if(typeof y=="undefined"){arguments[1]*=v;arguments[2]*=v;arguments[3]*=v;arguments[4]*=v}}w.apply(this,arguments)}}function x(y,C){var z=this;z._fFail=C;z._fSuccess=y;if(z._bSupportDataURI===null){var B=document.createElement("img");var D=function(){z._bSupportDataURI=false;if(z._fFail){z._fFail.call(z)}};var A=function(){z._bSupportDataURI=true;if(z._fSuccess){z._fSuccess.call(z)}};B.onabort=D;B.onerror=D;B.onload=A;B.src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==";return}else{if(z._bSupportDataURI===true&&z._fSuccess){z._fSuccess.call(z)}else{if(z._bSupportDataURI===false&&z._fFail){z._fFail.call(z)}}}}var u=function(y,z){this._bIsPainted=false;this._android=g();this._htOption=z;this._elCanvas=document.createElement("canvas");this._elCanvas.width=z.width;this._elCanvas.height=z.height;y.appendChild(this._elCanvas);this._el=y;this._oContext=this._elCanvas.getContext("2d");this._bIsPainted=false;this._elImage=document.createElement("img");this._elImage.alt="Scan me!";this._elImage.style.display="none";this._el.appendChild(this._elImage);this._bSupportDataURI=null};u.prototype.draw=function(D){var L=this._elImage;var I=this._oContext;var K=this._htOption;var A=D.getModuleCount();var F=K.width/A;var G=K.height/A;var B=Math.round(F);var H=Math.round(G);L.style.display="none";this.clear();for(var J=0;J<A;J++){for(var C=0;C<A;C++){var E=D.isDark(J,C);var y=C*F;var z=J*G;I.strokeStyle=E?K.colorDark:K.colorLight;I.lineWidth=1;I.fillStyle=E?K.colorDark:K.colorLight;I.fillRect(y,z,F,G);I.strokeRect(Math.floor(y)+0.5,Math.floor(z)+0.5,B,H);I.strokeRect(Math.ceil(y)-0.5,Math.ceil(z)-0.5,B,H)}}this._bIsPainted=true};u.prototype.makeImage=function(){if(this._bIsPainted){x.call(this,i)}};u.prototype.isPainted=function(){return this._bIsPainted};u.prototype.clear=function(){this._oContext.clearRect(0,0,this._elCanvas.width,this._elCanvas.height);this._bIsPainted=false};u.prototype.round=function(y){if(!y){return y}return Math.floor(y*1000)/1000};return u})();function a(w,y){var v=1;var z=j(w);for(var x=0,u=m.length;x<=u;x++){var A=0;switch(y){case f.L:A=m[x][0];break;case f.M:A=m[x][1];break;case f.Q:A=m[x][2];break;case f.H:A=m[x][3];break}if(z<=A){break}else{v++}}if(v>m.length){throw new Error("Too long data")}return v}function j(u){var i=encodeURI(u).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return i.length+(i.length!=u?3:0)}QRCode=function(v,w){this._htOption={width:256,height:256,typeNumber:4,colorDark:"#000000",colorLight:"#ffffff",correctLevel:f.H};if(typeof w==="string"){w={text:w}}if(w){for(var u in w){this._htOption[u]=w[u]}}if(typeof v=="string"){v=document.getElementById(v)}if(this._htOption.useSVG){h=e}this._android=g();this._el=v;this._oQRCode=null;this._oDrawing=new h(this._el,this._htOption);if(this._htOption.text){this.makeCode(this._htOption.text)}};QRCode.prototype.makeCode=function(i){this._oQRCode=new r(a(i,this._htOption.correctLevel),this._htOption.correctLevel);this._oQRCode.addData(i);this._oQRCode.make();this._el.title=i;this._oDrawing.draw(this._oQRCode);this.makeImage()};QRCode.prototype.makeImage=function(){if(typeof this._oDrawing.makeImage=="function"&&(!this._android||this._android>=3)){this._oDrawing.makeImage()}};QRCode.prototype.clear=function(){this._oDrawing.clear()};QRCode.CorrectLevel=f})();