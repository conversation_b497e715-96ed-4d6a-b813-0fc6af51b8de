package com.wb.tool;

import java.io.Serializable;

/** 字典记录实体类。 */
public class DictRecord implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	/** 链接到字段id */
	public String linkTo;
	/** 显示名称，为空表示根据字段属性 */
	public String dispText;
	/** 显示宽度，-1表示根据字段属性 */
	public int dispWidth;
	/** 显示格式 */
	public String dispFormat;
	/** 是否显示在列表中 */
	public boolean noList;
	/** 是否可编辑 */
	public boolean noEdit;
	/** 自动换行 */
	public boolean autoWrap;
	/** 是否允许为空，null表示根据字段属性 */
	public boolean noBlank;
	/** 是否只读，null表示根据字段属性  */
	public boolean readOnly;
	/** 键值名称 */
	public String keyName;
	/** 字段长度，-1表示根据字段属性 */
	public int fieldSize;
	/** 小数位数，-1表示根据字段属性 */
	public int decimalPrecision;
	/** 编辑时合法性验证函数 */
	public String validator;
	/** 自定义显示函数 */
	public String renderer;
	/** 上级ID */
	public String parentId;
	/** 数据结构中定义的字段类型 */
	public String dataType;
	/** 数据结构中的排序序号 */
	public int orderIndex;
	/** 编辑框中的默认值 */
	public String defaultValue;
	/** 是否主键 */
	public boolean isKey;
	/** 是否锁定列 */
	public boolean isLocked;
	/** 后端校验脚本(用于导入) */
	public String serverScript;
	/** 自动计算脚本(后端) */
	public String calFormula;
}