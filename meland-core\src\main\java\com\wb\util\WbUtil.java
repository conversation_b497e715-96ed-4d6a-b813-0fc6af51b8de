package com.wb.util;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.zip.GZIPInputStream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wb.common.Base;
import com.wb.common.Parser;
import com.wb.common.Session;
import com.wb.common.Str;
import com.wb.common.Var;
import com.wb.common.XwlBuffer;
import com.wb.fit.CustomRequest;
import com.wb.fit.CustomResponse;

public class WbUtil {

	private static final Logger LOGGER = LoggerFactory.getLogger(WbUtil.class);

	// 添加一个专用线程池用于执行模块角色预热任务
	private static final ExecutorService warmupExecutor = Executors.newSingleThreadExecutor(r -> {
		Thread thread = new Thread(r, "ModuleRolesWarmupThread");
		thread.setDaemon(true);
		return thread;
	});

	// 添加关闭钩子，在JVM退出时关闭线程池
	static {
		Runtime.getRuntime().addShutdownHook(new Thread(() -> {
			shutdownWarmupExecutor();
		}));
	}

	// 关闭线程池的方法
	private static void shutdownWarmupExecutor() {
		if (warmupExecutor != null && !warmupExecutor.isShutdown()) {
			try {
				warmupExecutor.shutdown();
				if (!warmupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
					warmupExecutor.shutdownNow();
				}
				if (Var.debug)
					LogUtil.info("模块角色预热线程池已关闭");
			} catch (InterruptedException e) {
				warmupExecutor.shutdownNow();
				Thread.currentThread().interrupt();
				LogUtil.error("关闭模块角色预热线程池时被中断", e);
			}
		}
	}

	/**
	 * 运行指定的模块文件，并解析其内容。
	 *
	 * @param url      模块文件的路径。
	 * @param params   需要传递给模块的参数，封装为JSON对象。
	 * @param isInvoke 是否作为调用模式运行该模块。
	 * @return 模块运行后返回的结果字符串。
	 * @throws Exception 如果在解析模块或运行过程中出现错误。
	 */
	public static String run(String url, JSONObject params, boolean isInvoke) throws Exception {
		CustomResponse response = new CustomResponse(null);
		CustomRequest request = new CustomRequest();

		request.setParams(params);
		String xwl = FileUtil.getModulePath(url);
		if (Var.getBool("sys.config.sys.enableCircuit")) {
			// 添加XWL模块限流检查
			if (!com.wb.circuit.XwlRateLimiterService.getInstance().checkRateLimit(xwl)) {
				// 超过限流阈值，返回请求过多
				return "{\"success\":false,\"errMsg\":\"请重试。\",\"errCode\":\"TOO_MANY_REQUESTS\"}";
			}

			// 添加XWL模块熔断检查
			if (!com.wb.circuit.XwlCircuitBreakerService.getInstance().allowRequest(xwl)) {
				// 熔断状态，返回服务不可用
				return "{\"success\":false,\"errMsg\":\"服务暂时不可用，请稍后再试\",\"errCode\":\"SERVICE_UNAVAILABLE\"}";
			}
		}
		// 记录开始时间
		long startTime = System.currentTimeMillis();
		boolean hasException = false;
		try {
			Parser parser = new Parser(request, response);
			parser.parse(xwl, true, isInvoke);
		} catch (Exception e) {
			hasException = true;
			if (Var.getBool("sys.config.sys.enableCircuit")) {
				// 记录异常，用于熔断计数
				com.wb.circuit.XwlCircuitBreakerService.getInstance().recordFailure(xwl);
			}
			throw e;
		} finally {
			// 记录执行结果
			if (!hasException) {
				// 执行时间
				long executionTime = System.currentTimeMillis() - startTime;
				LOGGER.debug("xwl1:" + xwl + " 执行时间: " + executionTime + "ms");
				if (Var.getBool("sys.config.sys.enableCircuit")) {
					// 记录成功，同时提供执行时间以便监控性能问题
					com.wb.circuit.XwlCircuitBreakerService.getInstance().recordSuccess(xwl, executionTime);
				}
			}
		}
		return getResponseString(response);
	}

	/**
	 * 执行指定的业务逻辑操作并返回响应结果。
	 *
	 * @param url      指定模块的路径，用于定位具体处理逻辑。
	 * @param params   请求参数，包含需要传递的业务数据，可为 null。
	 * @param request  HttpServletRequest 对象，包含当前 HTTP 请求的上下文信息。
	 * @param isInvoke 标志是否为直接调用，true 表示直接调用，false 表示常规处理。
	 * @return 返回处理后的响应字符串，表示执行结果。
	 * @throws Exception 当执行过程中发生错误时抛出异常。
	 */
	public static String run(String url, JSONObject params, HttpServletRequest request, boolean isInvoke)
			throws Exception {
		CustomResponse response = new CustomResponse(null);
		if (params != null)
			WebUtil.applyAttributes(request, params);
		String xwl = FileUtil.getModulePath(url);
		if (Var.getBool("sys.config.sys.enableCircuit")) {
			// 添加XWL模块限流检查
			if (!com.wb.circuit.XwlRateLimiterService.getInstance().checkRateLimit(xwl)) {
				// 超过限流阈值，返回请求过多
				return "{\"success\":false,\"errMsg\":\"请重试。\",\"errCode\":\"TOO_MANY_REQUESTS\"}";
			}
			// 添加XWL模块熔断检查
			if (!com.wb.circuit.XwlCircuitBreakerService.getInstance().allowRequest(xwl)) {
				// 熔断状态，返回服务不可用
				return "{\"success\":false,\"errMsg\":\"服务暂时不可用，请稍后再试\",\"errCode\":\"SERVICE_UNAVAILABLE\"}";
			}
		}
		// 记录开始时间
		long startTime = System.currentTimeMillis();
		boolean hasException = false;
		try {
			Parser parser = new Parser(request, response);
			parser.execute(xwl, isInvoke ? Parser.RunMode.INNER_INVOKE : Parser.RunMode.INNER, null, null);
		} catch (Exception e) {
			hasException = true;
			if (Var.getBool("sys.config.sys.enableCircuit")) {
				// 记录异常，用于熔断计数
				com.wb.circuit.XwlCircuitBreakerService.getInstance().recordFailure(xwl);
			}
			throw e;
		} finally {
			// 记录执行结果
			if (!hasException) {
				// 执行时间
				long executionTime = System.currentTimeMillis() - startTime;
				LOGGER.debug("xwl2:" + xwl + " 执行时间: " + executionTime + "ms");
				if (Var.getBool("sys.config.sys.enableCircuit")) {
					// 记录成功，同时提供执行时间以便监控性能问题
					com.wb.circuit.XwlCircuitBreakerService.getInstance().recordSuccess(xwl, executionTime);
				}
			}
		}
		return getResponseString(response);
	}

	/**
	 * 运行指定的解析逻辑。
	 *
	 * @param url      指定的模块路径，表示需要解析的目标资源。
	 * @param request  HTTP请求对象，包含请求的相关信息。
	 * @param response HTTP响应对象，用于发送响应数据。
	 * @throws Exception 在解析过程中可能抛出的异常，表示操作失败或其他问题。
	 */
	public static void run(String url, HttpServletRequest request, HttpServletResponse response) throws Exception {
		String xwl = FileUtil.getModulePath(url);
		if (Var.getBool("sys.config.sys.enableCircuit")) {
			// 添加XWL模块限流检查
			if (!com.wb.circuit.XwlRateLimiterService.getInstance().checkRateLimit(xwl)) {
				// 超过限流阈值，返回请求过多
				WebUtil.send(response,
						"{\"success\":false,\"errMsg\":\"请重试。\",\"errCode\":\"TOO_MANY_REQUESTS\"}");
				return;
			}
			// 添加XWL模块熔断检查
			if (!com.wb.circuit.XwlCircuitBreakerService.getInstance().allowRequest(xwl)) {
				// 熔断状态，返回服务不可用
				WebUtil.send(response,
						"{\"success\":false,\"errMsg\":\"服务暂时不可用，请稍后再试\",\"errCode\":\"SERVICE_UNAVAILABLE\"}");
				return;
			}
		}
		// 记录开始时间
		long startTime = System.currentTimeMillis();
		boolean hasException = false;
		try {
			Parser parser = new Parser(request, response);
			parser.execute(xwl, Parser.RunMode.MODULE, null, null);
		} catch (Exception e) {
			hasException = true;
			if (Var.getBool("sys.config.sys.enableCircuit")) {
				// 记录异常，用于熔断计数
				com.wb.circuit.XwlCircuitBreakerService.getInstance().recordFailure(xwl);
			}
			throw e;
		} finally {
			// 记录执行结果
			if (!hasException) {
				// 执行时间
				long executionTime = System.currentTimeMillis() - startTime;
				LOGGER.debug("xwl3:" + xwl + " 执行时间: " + executionTime + "ms");
				if (Var.getBool("sys.config.sys.enableCircuit")) {
					// 记录成功，同时提供执行时间以便监控性能问题
					com.wb.circuit.XwlCircuitBreakerService.getInstance().recordSuccess(xwl, executionTime);
				}
			}
		}
	}

	/**
	 * 获取应用程序信息。
	 *
	 * 该方法根据指定的URL路径，读取对应模块文件的信息，提取标题、图标等基本信息， 并生成包含标题、图标类名以及URL的JSON对象。 如果标题是以
	 * "Str." 开头的国际化字符串，则根据请求进行格式化。 如果标题为空，则使用文件名作为标题。
	 *
	 * @param url     模块文件的URL路径。
	 * @param request 当前的HttpServletRequest对象，用于格式化国际化字符串。
	 * @return 包含应用程序基本信息的JSON对象，包括 "title"（标题）、"iconCls"（图标类名）、"url"（模块路径）。
	 *         如果路径无效或未找到对应的模块信息，则返回null。
	 * @throws Exception 如果读取模块文件或解析数据时发生错误。
	 */
	public static JSONObject getAppInfo(String url, HttpServletRequest request) throws Exception {
		String path = FileUtil.getModulePath(url, true);
		if (path == null)
			return null;
		JSONObject obj = XwlBuffer.get(path, true);
		if (obj == null)
			return null;
		String[] names = { "title", "iconCls" };
		JSONObject info = JsonUtil.copy(obj, names);
		String title = info.optString("title");
		if (title.startsWith("Str."))
			info.put("title", Str.format(request, title.substring(4)));
		else if (title.isEmpty())
			info.put("title", FileUtil.getFilename(path));
		info.put("url", path);
		return info;
	}

	/**
	 * 获取系统中所有模块的角色权限配置。
	 *
	 * 该方法通过扫描系统模块文件路径，提取所有模块的角色权限配置， 并将结果存储于一个 JSON 对象中返回。
	 *
	 * @return 包含模块角色权限配置的 JSON 对象。JSON 对象的键为模块路径， 值为对应的角色权限信息。如果扫描过程中出现错误，会抛出异常。
	 * @throws Exception 如果在模块扫描或权限解析过程中发生错误。
	 */
	public static JSONObject getModuleRoles() throws Exception {
		JSONObject perms = new JSONObject();
		scanModulePerm(Base.modulePath, perms);
		return perms;
	}

	/**
	 * 预热模块角色映射缓存。
	 * 
	 * 该方法用于系统启动时预热模块角色映射关系,以提升后续访问性能。 使用分布式锁确保集群环境下只有一个节点执行预热操作。
	 * 预热完成后会在Redis中设置标记,有效期1小时,避免重复预热。
	 * 
	 * 预热过程包括: 1. 检查是否已被其他节点预热 2. 获取分布式锁 3. 执行getModuleRoles()预热 4. 设置预热完成标记
	 * 
	 * 该方法会异步执行预热操作，不会阻塞系统启动流程。 如果预热失败会记录错误日志,但不会影响系统正常运行。
	 */
	public static void warmModuleRoles() {
		if (Var.debug)
			LogUtil.info("异步启动模块角色映射预热...");
		// 使用CompletableFuture异步执行预热操作，指定使用专用线程池
		CompletableFuture.runAsync(() -> {
			try {
				if (Var.debug)
					LogUtil.info("开始预热模块角色映射...");
				long startTime = System.currentTimeMillis();
				// 执行预热操作
				getModuleRoles();
				long endTime = System.currentTimeMillis();
				if (Var.debug)
					LogUtil.info("模块角色映射预热完成，耗时: " + (endTime - startTime) + "ms");
			} catch (Exception e) {
				LogUtil.error("模块角色映射预热失败: " + e.getMessage());
			}
		}, warmupExecutor);
	}

	/**
	 * 根据提供的自定义响应对象，解析并返回响应字符串。 如果数据为 GZIP 格式，会先解压缩再转换为字符串，否则直接按照 UTF-8 编码解码。
	 *
	 * @param response 自定义响应对象，包含需要解析的字节数据。
	 * @return 解析后的响应字符串。
	 * @throws Exception 如果在数据解析过程中出现错误。
	 */
	public static String getResponseString(CustomResponse response) throws Exception {
		byte[] data = response.getBytes();
		String result;
		if ((data.length > 2) && (data[0] == 31) && (data[1] == -117)) {
			try (InputStream is = new GZIPInputStream(new ByteArrayInputStream(data))) {
				result = StringUtil.getString(is);
			}
		} else {
			result = new String(data, StandardCharsets.UTF_8);
		}
		return result;
	}

	/**
	 * 扫描指定路径下的模块文件，提取模块权限配置并存储到指定的 JSON 对象中。
	 *
	 * 遍历路径下的所有文件和子目录，如果文件名以 ".xwl" 结尾，则读取对应模块的权限配置 (角色映射信息)，并将结果添加到传入的 JSON
	 * 对象中。此方法通过递归方式处理子目录。
	 *
	 * @param path  要扫描的文件路径，表示模块文件所在的目录或文件。
	 * @param perms 用于存储模块权限配置的 JSON 对象，键为模块路径，值为对应的角色配置。
	 * @throws Exception 如果在读取文件或解析模块权限配置时发生错误。
	 */
	private static void scanModulePerm(File path, JSONObject perms) throws Exception {
		File[] files = FileUtil.listFiles(path);

		for (File file : files)
			if (file.isDirectory()) {
				scanModulePerm(file, perms);
			} else {
				String filename = file.getName();
				if (filename.endsWith(".xwl")) {
					String filePath = FileUtil.getModulePath(file);
					JSONObject jo = XwlBuffer.get(filePath);
					JSONObject roles = (JSONObject) jo.opt("roles");
					perms.put(filePath, roles);
				}
			}
	}

	/**
	 * 判断指定的 HTTP 请求用户是否有权限访问给定的模块路径。
	 *
	 * @param request HttpServletRequest对象，包含当前HTTP请求的上下文信息。
	 * @param path    模块的路径，用于验证访问权限。
	 * @return 如果用户有权限访问指定路径，则返回true；否则返回false。
	 * @throws Exception 如果获取模块配置信息或权限验证过程中发生错误。
	 */
	public static boolean canAccess(HttpServletRequest request, String path) throws Exception {
		if ((request == null) || (path == null))
			return false;
		JSONObject module = XwlBuffer.get(FileUtil.getModulePath(path));
		String[] roles = Session.getRoles(request);
		return canAccess(module, roles);
	}

	/**
	 * 判断指定的会话用户是否具有访问给定模块路径的权限。
	 *
	 * @param session HTTP 会话对象，用于获取用户的角色信息。
	 * @param path    模块文件的路径，用于验证访问权限。
	 * @return 如果用户具有访问权限，则返回 true；否则返回 false。
	 * @throws Exception 如果获取模块配置信息或验证权限的过程中发生错误。
	 */
	public static boolean canAccess(HttpSession session, String path) throws Exception {
		if ((session == null) || (path == null))
			return false;
		JSONObject module = XwlBuffer.get(FileUtil.getModulePath(path));
		String[] roles = (String[]) session.getAttribute("sys.roles");
		return canAccess(module, roles);
	}

	/**
	 * 判断指定的会话用户是否具有访问给定模块路径的权限。
	 *
	 * @param session Spring Session 对象，用于获取用户的角色信息。
	 * @param path    模块文件的路径，用于验证访问权限。
	 * @return 如果用户具有访问权限，则返回 true；否则返回 false。
	 * @throws Exception 如果获取模块配置信息或验证权限的过程中发生错误。
	 */
	public static boolean canAccess(org.springframework.session.Session session, String path) throws Exception {
		if ((session == null) || (path == null))
			return false;
		JSONObject module = XwlBuffer.get(FileUtil.getModulePath(path));
		String[] roles = (String[]) session.getAttribute("sys.roles");
		return canAccess(module, roles);
	}

	/**
	 * 判断用户是否有权限访问指定模块。
	 *
	 * @param module 表示模块信息的 JSONObject，其中包含是否需要登录和角色的相关信息。
	 * @param roles  用户拥有的角色列表，使用字符串数组表示。
	 * @return 如果用户有权限访问模块，返回 true；否则返回 false。
	 */
	public static boolean canAccess(JSONObject module, String[] roles) {
		boolean noLoginRequired = Boolean.FALSE.equals(module.opt("loginRequired"));

		if (noLoginRequired)
			return true;
		if (roles == null)
			return false;
		JSONObject setRoles = (JSONObject) module.opt("roles");
		for (String role : roles) {
			if (setRoles.has(role))
				return true;
		}
		return false;
	}

	/**
	 * 检查熔断器是否允许请求通过
	 * 
	 * @param serviceId 服务标识
	 * @return 是否允许请求通过
	 */
	public static boolean circuitBreakerAllowRequest(String serviceId) {
		return com.wb.circuit.XwlCircuitBreakerService.getInstance().allowRequest(serviceId);
	}

	/**
	 * 记录服务成功请求
	 * 
	 * @param serviceId     服务标识
	 * @param executionTime 执行时间(毫秒)
	 */
	public static void circuitBreakerRecordSuccess(String serviceId, long executionTime) {
		com.wb.circuit.XwlCircuitBreakerService.getInstance().recordSuccess(serviceId, executionTime);
	}

	/**
	 * 记录服务失败请求
	 * 
	 * @param serviceId 服务标识
	 */
	public static void circuitBreakerRecordFailure(String serviceId) {
		com.wb.circuit.XwlCircuitBreakerService.getInstance().recordFailure(serviceId);
	}

	/**
	 * 检查限流器是否允许请求通过
	 * 
	 * @param serviceId 服务标识
	 * @return 是否允许请求通过
	 */
	public static boolean rateLimitCheck(String serviceId) {
		return com.wb.circuit.XwlRateLimiterService.getInstance().checkRateLimit(serviceId);
	}

	/**
	 * 设置服务的QPS限制
	 * 
	 * @param serviceId 服务标识
	 * @param qpsLimit  QPS限制值
	 */
	public static void setRateLimit(String serviceId, int qpsLimit) {
		com.wb.circuit.XwlRateLimiterService.getInstance().saveRateLimitConfiguration(serviceId, qpsLimit);
	}
}