package com.wb.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.wb.common.*;
import com.wb.common.ws.SessionBridge;
import com.wb.exception.AccessDeniedException;
import com.wb.exception.NotFoundException;
import com.wb.tool.Console;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.ProgressListener;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.IOUtils;
import org.apache.commons.net.util.SubnetUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.redisson.api.RLock;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.springframework.session.Session;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import com.wb.common.ws.WebSocketSessionManager;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.zip.GZIPOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.time.Duration;

/**
 * Web工具方法类。
 */
public class WebUtil {
    /**
     * 向指定url发起GET方式的web请求。
     *
     * @param url 请求的url地址。
     * @return 请求返回的结果，结果以utf-8编码的字符串表示。
     * @throws IOException 请求过程中发生异常。
     */
    public static String submit(String url) throws IOException {
        return submit(url, "GET", null);
    }

    /**
     * 向指定url发起POST方式的web请求，并提交参数。
     *
     * @param url    请求的url地址。
     * @param params 提交的参数。
     * @return 请求返回的结果，结果以utf-8编码的字符串表示。
     * @throws IOException 请求过程中发生异常。
     */
    public static String submit(String url, JSONObject params) throws IOException {
        return submit(url, "POST", params);
    }

    /**
     * 向指定url发起指定方式的web请求，并提交参数。
     *
     * @param url    请求的url地址。
     * @param method 请求使用的方式，如POST, GET。
     * @param params 提交的参数。
     * @return 请求返回的结果，结果以utf-8编码的字符串表示。
     * @throws IOException 请求过程中发生异常。
     */
    public static String submit(String url, String method, JSONObject params) throws IOException {
        return new String(submitBytes(url, method, params, Var.getInt("sys.session.submitTimeout")), "utf-8");
    }

    public static byte[] submitBytes(String url, String method, JSONObject params, int timeout) throws IOException {
        return submitBytes(url, method, params, timeout, "application/x-www-form-urlencoded; charset=utf-8");
    }

    public static byte[] submitBytes(String url, String method, byte[] data, int timeout, String contentType)
            throws IOException {
        HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
        try {
            conn.setConnectTimeout(timeout);
            conn.setReadTimeout(timeout);
            conn.setUseCaches(false);
            conn.setRequestMethod(method);
            if (data != null) {
                conn.setDoOutput(true);
                conn.setRequestProperty("Content-Type", contentType);
                conn.setRequestProperty("Content-Length", Integer.toString(data.length));
                OutputStream os = conn.getOutputStream();
                try {
                    os.write(data);
                } finally {
                }
            }
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            InputStream is = conn.getInputStream();
            try {
                IOUtils.copy(is, bos);
            } finally {
            }
            return bos.toByteArray();
        } finally {
            conn.disconnect();
        }
    }

    /**
     * 向指定url发起指定方式的web请求，并提交参数。
     *
     * @param url     请求的url地址。
     * @param method  请求使用的方式，如POST, GET。
     * @param params  提交的参数。
     * @param timeout 连接和读时的超时时间，单位为毫秒。
     * @return 请求返回的结果，结果以字节数组表示。
     * @throws IOException 请求过程中发生异常。
     */
    public static byte[] submitBytes(String url, String method, JSONObject params, int timeout, String contentType)
            throws IOException {
        byte[] data;
        if (params == null)
            data = null;
        else
            data = getParamsText(params).getBytes("utf-8");
        return submitBytes(url, method, data, timeout, contentType);
    }

    /**
     * 把指定JSONObject中的参数转换成以web形式表示的参数字符串。
     *
     * @param jo 参数对象。
     * @return 以web形式表示的参数字符串。
     * @throws IOException 转换过程发生异常。
     */
    private static String getParamsText(JSONObject jo) throws IOException {
        StringBuilder sb = new StringBuilder();
        Set<Entry<String, Object>> es = jo.entrySet();
        boolean isFirst = true;

        for (Entry<String, Object> e : es) {
            if (isFirst)
                isFirst = false;
            else
                sb.append("&");
            sb.append(e.getKey());
            sb.append("=");
            sb.append(URLEncoder.encode(e.getValue().toString(), "utf-8"));
        }
        return sb.toString();
    }

    /**
     * 根据指定名称获取所有WebSocketSession对象的列表，并返回
     *
     * @param name String类型，需要获取WebSocketSession的名称
     * @return ArrayList<WebSocketSession>类型，包含指定名称的所有WebSocketSession对象的列表
     * @throws IOException
     */
    public static ArrayList<WebSocketSession> getSocketSessions(String name) throws IOException {
        String[] users = UserList.getUsers();
        ArrayList<WebSocketSession> allSessions = new ArrayList<WebSocketSession>();

        for (String user : users) {
            ArrayList<WebSocketSession> val = getSocketSessions(user, name);
            if (val != null)
                allSessions.addAll(val);
        }
        return allSessions;
    }

    /**
     * 根据指定用户ID和名称获取所有WebSocketSession对象的列表，并返回
     *
     * @param userId String类型，需要获取WebSocketSession的用户ID
     * @param name   String类型，需要获取WebSocketSession的名称
     * @return ArrayList<WebSocketSession>类型，包含指定用户ID和名称的所有WebSocketSession对象的列表
     */
    public static ArrayList<WebSocketSession> getSocketSessions(String userId, String name) throws IOException {
        org.springframework.session.Session[] httpSessions = UserList.getSessions(userId);
        ArrayList<WebSocketSession> allSessions = new ArrayList<WebSocketSession>();

        for (org.springframework.session.Session httpSession : httpSessions) {
            List<WebSocketSession> sessions = SessionBridge.getSessions(httpSession, name);
            if (sessions != null) {
                allSessions.addAll(sessions);
            }
        }
        return allSessions.size() > 0 ? allSessions : null;
    }

    /**
     * 向指定的WebSocketSession对象发送包含数据内容、是否发送成功和是否需要反馈信息的消息
     *
     * @param session    WebSocketSession类型，要发送消息的WebSocketSession对象
     * @param data       String类型，要发送的数据内容
     * @param successful boolean类型，是否发送成功
     * @param feedback   boolean类型，是否需要反馈信息
     * @throws IOException
     */
    public static void sendSocketText(WebSocketSession session, String data, boolean successful, boolean feedback)
            throws IOException {
        StringBuilder buf = new StringBuilder(data == null ? 44 : 44 + data.length());

        buf.append("{\"success\":");
        buf.append(successful ? "true" : "false");
        buf.append(",\"feedback\":");
        buf.append(feedback ? "true" : "false");
        buf.append(",\"data\":");
        buf.append(StringUtil.quote(data));
        buf.append("}");
        String result = buf.toString();

        // 更新最后活动时间
        WebSocketSessionManager.getInstance().updateSessionLastActive(session.getId());

        // 同步发送消息
        synchronized (session) {
            session.sendMessage(new TextMessage(result));
        }
    }

    /**
     * 向指定用户ID的所有会话发送消息
     * 
     * @param userId     用户ID
     * @param data       消息内容
     * @param successful 是否成功标志
     * @param feedback   是否需要反馈
     */
    public static void sendSocketTextToUser(String userId, String data, boolean successful, boolean feedback) {
        StringBuilder buf = new StringBuilder(data == null ? 44 : 44 + data.length());
        buf.append("{\"success\":");
        buf.append(successful ? "true" : "false");
        buf.append(",\"feedback\":");
        buf.append(feedback ? "true" : "false");
        buf.append(",\"data\":");
        buf.append(StringUtil.quote(data));
        buf.append("}");
        String result = buf.toString();

        // 使用WebSocketMessageSender发送给用户
        com.wb.common.ws.WebSocketMessageSender.sendMessageToUser(userId, result, true, null);
    }

    /**
     * 向指定的WebSocketSession对象发送包含数据内容的消息，并设置是否发送成功和是否需要反馈信息为默认值
     *
     * @param session WebSocketSession类型，要发送消息的WebSocketSession对象
     * @param data    String类型，要发送的数据内容
     */
    public static void sendSocketText(WebSocketSession session, String data) throws IOException {
        sendSocketText(session, data, true, false);
    }

    /**
     * 根据指定名称获取所有WebSocketSession对象的列表，并向所有WebSocketSession对象发送指定的数据内容
     *
     * @param name   String类型，需要发送消息的WebSocketSession名称
     * @param object Object类型，要发送的数据对象
     */
    public static void send(String name, Object object) throws IOException {
        ArrayList<WebSocketSession> socketSessions = getSocketSessions(name);
        String text = object == null ? "" : object.toString();
        for (WebSocketSession session : socketSessions)
            if (session.isOpen())
                sendSocketText(session, text);
    }

    /**
     * 根据指定名称获取所有WebSocketSession对象的列表，筛选出指定url的WebSocketSession对象，并向它们发送指定的数据内容
     *
     * @param name   String类型，需要发送消息的WebSocketSession名称
     * @param url    String类型，发送消息的WebSocketSession对象的url
     * @param object Object类型，要发送的数据对象
     */
    public static void sendWithUrl(String name, String url, Object object) throws IOException {
        ArrayList<WebSocketSession> socketSessions = getSocketSessions(name);
        String text = object == null ? "" : object.toString();
        if (url.startsWith("m?xwl="))
            url = url.substring(6) + ".xwl";
        for (WebSocketSession session : socketSessions)
            if ((session.isOpen()) && (url.equals(session.getAttributes().get("xwl"))))
                sendSocketText(session, text);
    }

    /**
     * 根据指定用户ID和名称获取所有WebSocketSession对象的列表，并向所有WebSocketSession对象发送指定的数据内容
     *
     * @param userId String类型，需要发送消息的WebSocketSession的用户ID
     * @param name   String类型，需要发送消息的WebSocketSession名称
     * @param object Object类型，要发送的数据对象
     */
    public static void send(String userId, String name, Object object) throws IOException {
        ArrayList<WebSocketSession> socketSessions = getSocketSessions(userId, name);
        if (socketSessions == null)
            return;
        String text = object == null ? "" : object.toString();
        for (WebSocketSession session : socketSessions)
            if (session.isOpen())
                sendSocketText(session, text);
    }

    private static void send(List<WebSocketSession> sessions, String name, Object object) throws IOException {
        if (sessions == null)
            return;
        String text = object == null ? "" : object.toString();
        for (WebSocketSession session : sessions)
            if (session.isOpen())
                sendSocketText(session, text);
    }

    /**
     * 根据指定HttpSession和名称获取所有WebSocketSession对象的列表，并向所有WebSocketSession对象发送指定的数据内容
     *
     * @param httpSession HttpSession类型，需要发送消息的WebSocketSession的HttpSession对象
     * @param name        String类型，需要发送消息的WebSocketSession名称
     * @param object      Object类型，要发送的数据对象
     */
    public static void send(HttpSession httpSession, String name, Object object) throws IOException {
        List<WebSocketSession> sessions = SessionBridge.getSessions(httpSession, name);
        send(sessions, name, object);
    }

    public static void send(Session httpSession, String name, Object object) throws IOException {
        List<WebSocketSession> sessions = SessionBridge.getSessions(httpSession, name);
        send(sessions, name, object);
    }

    /**
     * 获取指定请求对象中的排序信息参数。
     *
     * @param request 请求对象。
     * @return 排序信息数组，0项property，1项direction。
     */
    public static String[] getSortInfo(HttpServletRequest request) {
        String sort = request.getParameter("sort");

        if (StringUtil.isEmpty(sort))
            return null;
        JSONObject jo = new JSONArray(sort).getJSONObject(0);
        String[] result = new String[2];
        result[0] = jo.getString("property");
        result[1] = jo.optString("direction");
        return result;
    }

    /**
     * 把文件名称转换成特定浏览器可识别的字符串，字符串采用浏览器相关的特定编码方式。
     *
     * @param request  包含浏览器信息的请求对象。
     * @param filename 文件名称。
     * @return 转码后的文件名称。
     * @throws IOException 转码过程发生异常。
     */
    public static String encodeFilename(HttpServletRequest request, String filename) throws IOException {
        String agent = StringUtil.opt(request.getHeader("user-agent")).toLowerCase();
        if (agent.contains("opera"))
            return StringUtil.concat("filename*=\"utf-8''", encode(filename), "\"");
        else if (agent.contains("trident") || agent.contains("msie") || agent.contains("edge"))
            return StringUtil.concat("filename=\"", encode(filename), "\"");
        else
            return StringUtil.concat("filename=\"",
                    new String(filename.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1), "\"");
    }

    /**
     * 把指定字符串使用application/x-www-form-urlencoded方式编码。
     *
     * @param string 需要编码的字符串。
     * @return 编码后的字符串。
     * @throws IOException 编码过程发生异常。
     */
    public static String encode(String string) throws IOException {
        return StringUtil.replaceAll(URLEncoder.encode(string, "utf-8"), "+", "%20");
    }

    /**
     * 把按指定编码方式编码的字符串解码后重新使用utf-8编码。
     *
     * @param string 需要重新编码的字符串。
     * @return 重新编码后的字符串。
     * @throws IOException 重新编码过程发生异常。
     */
    public static String decode(String string) throws IOException {
        if (Var.urlEncoding.isEmpty() || StringUtil.isEmpty(string))
            return string;
        return new String(string.getBytes(Var.urlEncoding), "utf-8");
    }

    /**
     * 清除本次请求使用文件上传功能产生的临时文件和会话中存储的值。
     *
     * @param request 请求对象。
     * @param list    上传的值列表。
     */
    @SuppressWarnings("deprecation")
    public static void clearUpload(HttpServletRequest request, List<FileItem> list) {
        for (FileItem item : list) {
            if (!item.isFormField())
                IOUtils.closeQuietly((InputStream) request.getAttribute(item.getFieldName()));
            item.delete();
        }
        String uploadId = (String) request.getAttribute("sys.uploadId");
        if (uploadId != null) {
            HttpSession session = request.getSession(true);
            session.removeAttribute("sys.upread." + uploadId);
            session.removeAttribute("sys.uplen." + uploadId);
        }
    }

    /**
     * 给指定编号添加上用户id号前缀，格式为userId@id。
     *
     * @param request 包含用户id号的请求对象。
     * @param id      需要添加前缀的id号。
     * @return 添加前缀后的id号。
     */
    public static String getIdWithUser(HttpServletRequest request, String id) {
        String user = fetch(request, "sys.user");
        return StringUtil.concat(StringUtil.opt(user), "@", id);
    }

    /**
     * 设置request对象关联session的属性值。如果session不存在将抛出异常。
     *
     * @param request 请求对象。
     * @param name    属性名称。
     * @param value   属性值。
     * @throws RuntimeException session不存在。
     */
    public static void setSessionValue(HttpServletRequest request, String name, Object value) {
        HttpSession session = request.getSession(false);

        if (session == null)
            throw new RuntimeException("Session does not exist.");
        session.setAttribute(name, value);
    }

    /**
     * 获取HttpServletRequest和其关联的HttpSession对象中指定名称的属性或参数的原始值。
     * 如果相同名称的属性或参数都存在则返回优先级最高的值。优先级从高到低依次为：
     * HttpSession的attribute，HttpServletRequest的attribute和
     * HttpServletRequest的parameter。如果都不存在则返回null。
     *
     * @param request 请求对象。
     * @param name    属性或参数名称。
     * @return 指定名称的属性或参数值。
     */
    public static Object fetchObject(HttpServletRequest request, String name) {
        HttpSession session = request.getSession(false);
        Object value;

        if (session == null || (value = session.getAttribute(name)) == null) {
            value = request.getAttribute(name);
            if (value == null)
                return request.getParameter(name);
            else
                return value;
        } else
            return value;
    }

    /**
     * 获取HttpServletRequest和其关联的HttpSession对象中指定名称的属性或参数转换的字符串值。
     * 如果相同名称的属性或参数都存在则返回优先级最高的值。优先级从高到低依次为：
     * HttpSession的attribute，HttpServletRequest的attribute和
     * HttpServletRequest的parameter。如果都不存在则返回null。
     *
     * @param request 请求对象。
     * @param name    属性或参数名称。
     * @return 指定名称的属性或参数值。
     */
    public static String fetch(HttpServletRequest request, String name) {
        Object object = fetchObject(request, name);
        if (object == null)
            return null;
        else
            return object.toString();
    }

    /**
     * 把HttpServletRequest和其关联的HttpSession对象中属性和参数取值到JSONObject中。
     * 如果相同名称的属性或参数都存在则返回优先级最高的值。优先级从高到低依次为：
     * HttpSession的attribute，HttpServletRequest的attribute和
     * HttpServletRequest的parameter。如果都不存在则返回null。
     *
     * @param request 请求对象。
     * @return 取到所有值组成的JSONObject对象。
     */
    public static JSONObject fetch(HttpServletRequest request) {
        JSONObject json = new JSONObject();
        Entry<?, ?> entry;
        String name;
        HttpSession session;
        Iterator<?> requestParams = request.getParameterMap().entrySet().iterator();
        Enumeration<?> requestAttrs = request.getAttributeNames();

        // 获取request parameters
        while (requestParams.hasNext()) {
            entry = (Entry<?, ?>) requestParams.next();
            json.put((String) entry.getKey(), ((String[]) entry.getValue())[0]);
        }
        // 重复概率较小，因此采用覆盖策略效率较高
        // 获取request attributes，如果同名的值已经存在，则覆盖。
        while (requestAttrs.hasMoreElements()) {
            name = requestAttrs.nextElement().toString();
            if (!name.startsWith("sysx."))
                json.put(name, request.getAttribute(name));
        }
        // 获取session attributes，如果同名的值已经存在，则覆盖。
        session = request.getSession(false);
        if (session != null) {
            Enumeration<?> sessionAttrs = session.getAttributeNames();
            while (sessionAttrs.hasMoreElements()) {
                name = sessionAttrs.nextElement().toString();
                if (!name.startsWith("sysx."))
                    json.put(name, session.getAttribute(name));
            }
        }
        return json;
    }

    /**
     * 替换指定文本中使用{#name#}语法引用的值。值取自HttpServletRequest中的属性和参数、
     * HttpSession属性、Var变量值、Str字符串值。如果名称重复，优先级从高到低依次为：
     * Var变量，Str字符串值，session属性，request属性，request参数。
     *
     * @param request 请求对象。如果为null，直接返回text。
     * @param text    需要进行替换的文本。
     * @return 替换值后的文本。
     */
    public static String replaceParams(HttpServletRequest request, String text) {
        if ((request == null) || (StringUtil.isEmpty(text)))
            return text;
        int start = 0;
        int startPos = text.indexOf("{#", start);
        int endPos = text.indexOf("#}", startPos + 2);

        if ((startPos != -1) && (endPos != -1)) {
            StringBuilder buf = new StringBuilder(text.length());

            while ((startPos != -1) && (endPos != -1)) {
                String paramName = text.substring(startPos + 2, endPos);
                String paramValue;
                if (paramName.startsWith("Var.")) {
                    paramValue = Var.getString(paramName.substring(4));
                } else {
                    if (paramName.startsWith("Str."))
                        paramValue = Str.format(request, paramName.substring(4), new Object[0]);
                    else
                        paramValue = fetch(request, paramName);
                }
                buf.append(text, start, startPos);
                if (paramValue != null)
                    buf.append(paramValue);
                start = endPos + 2;
                startPos = text.indexOf("{#", start);
                endPos = text.indexOf("#}", startPos + 2);
            }
            buf.append(text.substring(start));
            return buf.toString();
        }
        return text;
    }

    /**
     * 发送指定对象数据至客户端，并立即提交。如果缓冲区数据已经提交则该方法没有任何效果。
     * 发送的对象根据类型分为InputStream类型数据和非InputStream类型数据。前者根据系统变量
     * sys.sendStreamGzip决定是否使用gzip压缩输出；后者如果是非字节数组对象先转换成字符串，
     * 然后再转换成utf-8编码的字节数组，并根据系统变量sys.sendGzipMinSize决定是否使用gzip
     * 压缩输出。非InputStream类型数据如果没有使用gzip压缩，设置response的contentLength为
     * 字节长度，如果为字符串类数据设置头信息内容为"text/html;charset=utf-8"。如果启用压缩
     * 不能设置response对象的contentLength。
     *
     * @param response 响应对象，数据将发送到该对象输出流中。
     * @param object   发送的对象。
     * @throws IOException 输出过程中发生异常。
     */
    public static void send(HttpServletResponse response, Object object) throws IOException {
        InputStream inputStream = (object instanceof InputStream) ? (InputStream) object : null;
        try {
            if (response.isCommitted())
                return;
            OutputStream outputStream = response.getOutputStream();
            if (inputStream == null) {
                byte[] bytes;
                if ((object instanceof byte[])) {
                    bytes = (byte[]) object;
                } else {
                    String text;
                    if (object == null)
                        text = "";
                    else
                        text = object.toString();
                    bytes = text.getBytes(StandardCharsets.UTF_8);
                    if (StringUtil.isEmpty(response.getContentType()))
                        response.setContentType("text/html;charset=utf-8");
                }
                int len = bytes.length;
                if ((len >= Var.sendGzipMinSize) && (Var.sendGzipMinSize != -1)) {
                    response.setHeader("Content-Encoding", "gzip");
                    try (GZIPOutputStream gos = new GZIPOutputStream(outputStream)) {
                        gos.write(bytes);
                    }
                } else {
                    response.setContentLength(len);
                    outputStream.write(bytes);
                }

            } else if (Var.sendStreamGzip) {
                response.setHeader("Content-Encoding", "gzip");
                GZIPOutputStream gos = new GZIPOutputStream(outputStream);
                try {
                    IOUtils.copy(inputStream, gos);
                } finally {
                    gos.close();
                }
            } else {
                IOUtils.copy(inputStream, outputStream);
            }

            response.flushBuffer();
        } finally {
            if (inputStream != null)
                inputStream.close();
        }
        if (inputStream != null)
            inputStream.close();
    }

    public static void send(HttpServletResponse response, String text, boolean successful) throws IOException {
        WebUtil.send(response, StringUtil.textareaQuote(
                StringUtil.concat("{success:", Boolean.toString(successful), ",value:", StringUtil.quote(text), "}")));
    }

    public static void send(HttpServletResponse response, Object object, boolean successful) throws IOException {
        String text;
        if (object == null)
            text = null;
        else
            text = object.toString();
        send(response, text, successful);
    }

    /**
     * 判断指定请求是否由Ajax发出。
     *
     * @param request 请求对象。
     * @return true表示由Ajax发出，false其他。
     */
    public static boolean fromAjax(HttpServletRequest request) {
        try {
            return "XMLHttpRequest".equalsIgnoreCase(request.getHeader("X-Requested-With"));
        } catch (Throwable e) {
        }
        return false;
    }

    /**
     * 显示异常信息。
     *
     * @param exception 异常信息对象。
     * @param request   请求对象。
     * @param response  响应对象。
     * @throws ServletException 如果异常未处理，则抛出异常交由服务器处理。
     */
    public static void showException(Throwable exception, HttpServletRequest request, HttpServletResponse response)
            throws ServletException {
        boolean isAjax = fromAjax(request);
        boolean jsonResp = jsonResponse(request);
        boolean directOutput = isAjax || jsonResp;

        StringWriter writer = new StringWriter();
        PrintWriter pwriter = new PrintWriter(writer, true);

        // 处理请求输入流
        discardInputStream(request);
        exception.printStackTrace(pwriter);
        String errorMessage = writer.toString();
        Console.printToClient(request, errorMessage, "error", false);

        JSONObject params = fetch(request);
        int errorStatus = determineErrorStatus(exception);

        // 记录错误日志
        if (errorStatus == HttpServletResponse.SC_INTERNAL_SERVER_ERROR && !params.has("password")) {
            LogUtil.error(request, "URL=" + request.getServletPath() + ";XWL=" + WebUtil.fetch(request, "sys.xwl")
                    + "\r\n错误信息：\r\n" + errorMessage + "\r\n\r\n请求参数：\r\n" + params.toString());
        }

        String rootError = SysUtil.getRootError(exception);
        Throwable rootExcept = SysUtil.getRootExcept(exception);

        if (Var.printError && !directOutput) {
            handleNonDirectOutput(response, errorStatus, rootError, rootExcept);
        } else {
            handleDirectOutput(response, errorStatus, rootError, jsonResp, errorMessage);
        }
    }

    /**
     * 确定错误状态码。
     *
     * @param exception 异常信息对象。
     * @return 错误状态码。
     */
    private static int determineErrorStatus(Throwable exception) {
        if (exception instanceof AccessDeniedException
                || SysUtil.getRootExcept(exception) instanceof AccessDeniedException) {
            return HttpServletResponse.SC_FORBIDDEN;
        }
        if (exception instanceof NotFoundException || SysUtil.getRootExcept(exception) instanceof NotFoundException) {
            return HttpServletResponse.SC_NOT_FOUND;
        }
        return HttpServletResponse.SC_INTERNAL_SERVER_ERROR;
    }

    /**
     * 处理非直接输出的情况。
     *
     * @param response    响应对象。
     * @param errorStatus 错误状态码。
     * @param rootError   根错误信息。
     * @param rootExcept  根异常。
     * @throws ServletException 如果处理失败，则抛出ServletException。
     */
    private static void handleNonDirectOutput(HttpServletResponse response, int errorStatus, String rootError,
            Throwable rootExcept) throws ServletException {
        try {
            if (errorStatus != HttpServletResponse.SC_INTERNAL_SERVER_ERROR) {
                response.sendError(errorStatus, rootError);
            } else if (rootExcept instanceof ServletException) {
                throw (ServletException) rootExcept;
            } else {
                throw new ServletException(rootExcept);
            }
        } catch (IOException e) {
            throw new ServletException(rootExcept);
        }
    }

    /**
     * 处理直接输出的情况。
     *
     * @param response     响应对象。
     * @param errorStatus  错误状态码。
     * @param rootError    根错误信息。
     * @param jsonResp     是否为JSON响应。
     * @param errorMessage 错误信息。
     */
    private static void handleDirectOutput(HttpServletResponse response, int errorStatus, String rootError,
            boolean jsonResp, String errorMessage) throws ServletException {
        try {
            if (Var.printError)
                System.err.println(errorMessage);
            if (!response.isCommitted()) {
                response.reset();
                if (jsonResp) {
                    send(response, rootError, false);
                } else {
                    response.setStatus(errorStatus);
                    WebUtil.send(response, rootError);
                }
            }
        } catch (Throwable throwable) {
            throw new ServletException(throwable);
        }
    }

    /**
     * 检查当前请求的登录状态，如果请求需要登录则转到登录页面，否则发送401状态码。
     *
     * @param request  请求对象。
     * @param response 响应对象。
     * @return true已经成功登录，false没有登录。
     * @throws Exception 检查登录过程发生异常。
     */
    public static boolean checkLogin(HttpServletRequest request, HttpServletResponse response) throws Exception {
        HttpSession session = request.getSession(false);

        if ((session == null) || (session.getAttribute("sys.logined") == null)) {
            boolean isAjax = fromAjax(request);
            boolean jsonResp = jsonResponse(request);
            boolean directOutput = (isAjax) || (jsonResp);

            if (directOutput) {
                if (jsonResp) {
                    WebUtil.send(response, "$WBE201: 需要登录", false);
                } else {
                    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                    WebUtil.send(response, "需要登录");
                }
            } else {
                Parser parser = new Parser(request, response);
                if (isTouchModule(request))
                    parser.parse("sys/session/tlogin.xwl");
                else
                    parser.parse("sys/session/login.xwl");
            }
            discardInputStream(request);
            return false;
        }
        return true;
    }

    /**
     * 判断是否请求xwl移动应用模块。
     *
     * @param request 请求对象。
     * @return 如果是返回true，否则返回false.
     * @throws IOException
     */
    private static boolean isTouchModule(HttpServletRequest request) throws Exception {
        String url;
        if (Var.useServletPath)
            url = request.getServletPath();
        else
            url = request.getRequestURI().substring(Base.rootPathLen);
        String xwl = UrlBuffer.get(url);
        if (StringUtil.isEmpty(xwl)) {
            xwl = request.getParameter("xwl");
            if (StringUtil.isEmpty(xwl))
                return false;
            xwl = StringUtil.concat(new String[] { xwl, ".xwl" });
        }
        JSONObject module = XwlBuffer.get(xwl, true);
        if (module == null)
            return false;
        return module.has("hasTouch");
    }

    /**
     * 判断当前请求的用户是否包含指定的角色。
     *
     * @param request  请求对象
     * @param roleName 角色名称
     * @return true包含角色，false未包含角色。
     */
    public static boolean hasRole(HttpServletRequest request, String roleName) {
        String[] roles = (String[]) fetchObject(request, "sys.roles");
        return StringUtil.indexOf(roles, roleName) != -1;
    }

    /**
     * 处理文件上传模式提交的参数，把上传的文件和参数存储到HttpServletRequest对象
     * 的attribute属性中，其中上传的文件以输入流对象的形式存储在attribute中。
     *
     * @param request 请求对象
     * @return 上传的所有参数和文件列表。
     * @throws Exception 处理过程发生异常。
     */
    public static List<FileItem> setUploadFile(HttpServletRequest request) throws Exception {
        DiskFileItemFactory factory = new DiskFileItemFactory();
        ServletFileUpload upload = new ServletFileUpload(factory);
        List<FileItem> list;
        String fieldName, fileName, fileIndexText;
        int maxSize = Var.getInt("sys.service.upload.maxSize");
        int totalMaxSize = Var.getInt("sys.service.upload.totalMaxSize");

        final String uploadId = request.getParameter("uploadId");
        Integer fileIndex;
        HashMap<String, Integer> multiFilesMap = null;

        if (totalMaxSize != -1)
            upload.setSizeMax(totalMaxSize * 1024L);
        long maxByteSize;
        if (maxSize == -1)
            maxByteSize = -1;
        else
            maxByteSize = maxSize * 1024L;
        factory.setSizeThreshold(Var.getInt("sys.service.upload.bufferSize"));
        if (uploadId != null && uploadId.indexOf('.') == -1) {
            request.setAttribute("sys.uploadId", uploadId);
            final HttpSession session = request.getSession(true);
            if (session != null) {
                upload.setProgressListener(new ProgressListener() {
                    @Override
                    public void update(long read, long length, int id) {
                        session.setAttribute("sys.upread." + uploadId, read);
                        session.setAttribute("sys.uplen." + uploadId, length);
                    }
                });
            }
        }
        list = upload.parseRequest(request);
        if (list == null || list.size() == 0)
            return null;
        try {
            for (FileItem item : list) {
                fieldName = item.getFieldName();
                // 外部request属性名称不允许带"."和"@"，防止注入系统变量
                if (fieldName.indexOf('.') != -1 || fieldName.indexOf('@') != -1)
                    continue;
                if (item.isFormField()) {
                    if (request.getAttribute(fieldName) != null)
                        throw new RuntimeException("Duplicate parameters \"" + fieldName + "\" found.");
                    request.setAttribute(fieldName, item.getString("utf-8"));
                } else {
                    /*
                     * 没有上传文件值为""，上传文件值为InputStream类型， 可以使用WebUtil.hasFile方法判断文件域是否非空。
                     */
                    fileName = FileUtil.getFilename(item.getName());
                    long fileSize = item.getSize();
                    if (maxByteSize != -1 && fileSize > maxByteSize)
                        throw new IllegalArgumentException("服务器设置最大允许上传：" + maxSize + "KB");
                    if (request.getAttribute(fieldName) != null) {
                        // 单文件控件上传多个文件
                        // 属性名称为itemId@index
                        if (multiFilesMap == null)
                            multiFilesMap = new HashMap<String, Integer>();
                        fileIndex = multiFilesMap.get(fieldName);
                        if (fileIndex == null)
                            fileIndex = 0;
                        fileIndex++;
                        multiFilesMap.put(fieldName, fileIndex);
                        fileIndexText = "@" + Integer.toString(fileIndex);
                        if (StringUtil.isEmpty(fileName) && fileSize == 0)
                            request.setAttribute(fieldName + fileIndexText, "");
                        else
                            // 已经上传文件，包括空文件
                            request.setAttribute(fieldName + fileIndexText, item.getInputStream());
                        request.setAttribute(StringUtil.concat(fieldName, fileIndexText, "__name"), fileName);
                        request.setAttribute(StringUtil.concat(fieldName, fileIndexText, "__size"), fileSize);
                    } else {
                        if (StringUtil.isEmpty(fileName) && fileSize == 0)
                            request.setAttribute(fieldName, "");
                        else
                            // 已经上传文件，包括空文件
                            request.setAttribute(fieldName, item.getInputStream());
                        request.setAttribute(fieldName + "__name", fileName);
                        request.setAttribute(fieldName + "__size", fileSize);
                    }
                }
            }
        } catch (Throwable e) {
            WebUtil.clearUploadFile(request, list);
            throw new Exception(e);
        }
        return list;
    }

    /**
     * 判断上传的文件域是否为非空。
     *
     * @param request 请求对象。
     * @param name    文件域名称。
     * @return 如果存在上传的文件包括空文件则返回true，否则返回false。
     */
    public static boolean hasFile(HttpServletRequest request, String name) {
        return request.getAttribute(name) instanceof InputStream;
    }

    public static void discardInputStream(HttpServletRequest request) {
        try {
            InputStream stream = request.getInputStream();
            byte[] skipBuffer = new byte[2048];

            while (stream.read(skipBuffer) != -1)
                ;
        } catch (Throwable localThrowable) {
        }
    }

    /**
     * 清除上传文件的资源，包括临时文件和标识上传进度的session属性。
     *
     * @param request 请求对象。
     * @param list    上传的所有参数和文件列表。
     */
    @SuppressWarnings("deprecation")
    public static void clearUploadFile(HttpServletRequest request, List<FileItem> list) {
        Object object;
        for (FileItem item : list) {
            if (!item.isFormField() && !item.isInMemory()) {
                // 文件且非缓存项需要清除临时文件
                object = request.getAttribute(item.getFieldName());
                if (object instanceof InputStream)
                    IOUtils.closeQuietly((InputStream) object);
                item.delete();
            }
        }
        String uploadId = (String) request.getAttribute("sys.uploadId");
        if (uploadId != null) {
            HttpSession session = request.getSession(true);
            session.removeAttribute("sys.upread." + uploadId);
            session.removeAttribute("sys.uplen." + uploadId);
        }
    }

    /**
     * 判断请求是否需要采用json格式返回响应数据。
     *
     * @param request 请求对象。
     * @return true json格式，false其他。
     */
    public static boolean jsonResponse(HttpServletRequest request) {
        return exists(request, "_jsonresp");
    }

    /**
     * 判断请求对象中是否存在指定属性且其值为1或"1"。取值的优先顺序为session,attribute,
     * 和parameter。如果参数名称以"sys."为前缀，只判断request的attribute属性。
     *
     * @param request 请求对象。
     * @param name    属性名称。
     * @return true存在，false不存在。
     */
    public static boolean exists(HttpServletRequest request, String name) {
        Object value;
        if (name.startsWith("sys."))
            value = request.getAttribute(name);// 防止读取外部参数，避免被注入内部参数
        else
            value = WebUtil.fetch(request, name);
        if (value == null)
            return false;
        else
            return "1".equals(value.toString());
    }

    /**
     * 获取存储于特定Map中，通过setObject设置的对象。如果值不存在返回null。
     *
     * @param request 请求对象。
     * @param name    存储的名称。
     * @return request 指定名称的值或null。
     */
    public static Object getObject(HttpServletRequest request, String name) {
        Object object = request.getAttribute("sysx.varMap");
        if (object != null) {
            ConcurrentHashMap<String, Object> map = JSONObject.toConHashMap(object);
            return map.get(name);
        }
        return null;
    }

    public static void applyAttributes(HttpServletRequest request, JSONObject params) {
        if (params == null)
            return;
        Set<Entry<String, Object>> es = params.entrySet();
        for (Entry<String, Object> e : es)
            request.setAttribute(e.getKey(), e.getValue());
    }

    /**
     * 引用指定的请求。如果地址为xwl模块立即执行该xwl模块，其他请求地址使用 include进行引用。
     *
     * @param request  请求对象。
     * @param response 响应对象。
     * @param path     请求路径，路径不能含有url参数。如果带url参数可使用Wrapper对request进行包装。
     *                 调用xwl模块可使用捷径或xwl文件路径，如"dbe"或"admin/dbe.xwl"。
     * @throws Exception 调用过程发生异常。
     */
    public static void include(HttpServletRequest request, HttpServletResponse response, String path) throws Exception {
        doInclude(request, response, path, false);
    }

    /**
     * 转发指定的请求。如果地址为xwl模块清空response缓冲区并立即执行该xwl模块， 其他请求地址使用include进行引用。
     *
     * @param request  请求对象。
     * @param response 响应对象。
     * @param path     请求路径，路径不能含有url参数。如果带url参数可使用Wrapper对request进行包装。
     *                 调用xwl模块可使用捷径或xwl文件路径，如"dbe"或"admin/dbe.xwl"。
     * @throws Exception 调用过程发生异常。
     */
    public static void forward(HttpServletRequest request, HttpServletResponse response, String path) throws Exception {
        doInclude(request, response, path, true);
    }

    /**
     * 引用或转发指定的请求。如果地址为xwl模块立即执行该xwl模块，其他请求地址使用 forward或include进行引用或转发。
     *
     * @param request   请求对象。
     * @param response  响应对象。
     * @param path      请求路径，路径不能含有url参数。如果带url参数可使用Wrapper对request进行包装。
     * @param isForward 指定是否使用include或forward。对于xwl模块两者之间的区别在于forward
     *                  前执行resetBuffer操作。
     * @throws Exception 调用过程发生异常。
     */
    private static void doInclude(HttpServletRequest request, HttpServletResponse response, String path,
            boolean isForward) throws Exception {
        String xwl = FileUtil.getModulePath(path, true);

        if (xwl == null) {
            if (isForward)
                request.getRequestDispatcher(path).forward(request, response);
            else
                request.getRequestDispatcher(path).include(request, response);
        } else {
            if (isForward)
                response.resetBuffer();
            if (xwl.isEmpty()) {
                xwl = request.getParameter("xwl");
                if (xwl == null) {
                    response.sendError(HttpServletResponse.SC_BAD_REQUEST, "null xwl");
                    return;
                }
                xwl = StringUtil.concat(xwl, ".xwl");
            }
            Parser parser = new Parser(request, response);
            parser.parse(xwl);
        }
    }

    public static void sendMessage(String fromUser, JSONArray toUsers, String msg) throws Exception {
        Connection conn = null;
        PreparedStatement st = null;
        ResultSet rs = null;

        Timestamp now = DateUtil.now();
        String homeText;
        String dispName;
        String userName;
        JSONObject jo;
        int j;
        int i;
        try {
            conn = DbUtil.getConnection();
            st = conn.prepareStatement("select USER_NAME,DISPLAY_NAME from WB_USER where USER_ID=?");
            st.setString(1, fromUser);
            rs = st.executeQuery();
            rs.next();
            userName = rs.getString(1);
            dispName = rs.getString(2);
            rs.close();
            st.close();
            jo = new JSONObject();
            jo.put("module", "m?xwl=my/im");
            jo.put("count", 1);
            jo.put("title", dispName);
            jo.put("msg", msg);
            homeText = jo.toString();
            jo = new JSONObject();
            jo.put("FROM_SYS", true);
            jo.put("FROM_USER", fromUser);
            jo.put("USER_NAME", userName);
            jo.put("DISPLAY_NAME", dispName);
            jo.put("USER_ID", fromUser);
            jo.put("NOT_READ", 1);
            jo.put("SEND_DATE", now);
            jo.put("TEXT_CONTENT", msg);
            jo.put("IM_ID", SysUtil.getId());
            jo.put("SEND_DATE", now);
            jo.put("msgId", SysUtil.getId());
            st = conn.prepareStatement("insert into WB_IM values (?,?,?,?,1,null,null,?,null)");
            j = toUsers.length();
            for (i = 0; i < j; i++) {
                String userId = toUsers.getString(i);
                st.setString(1, SysUtil.getId());
                if (i == 0) {
                    st.setTimestamp(2, now);
                    st.setString(3, fromUser);
                }
                st.setString(4, userId);
                if (i == 0)
                    DbUtil.setText(st, 5, msg);
                st.executeUpdate();
                jo.put("TO_USER", userId);
                send(userId, "sys.im", jo.toString());
                send(userId, "sys.home", homeText);
            }
        } finally {
            DbUtil.close(rs);
            DbUtil.close(st);
            DbUtil.close(conn);
        }
    }

    /**
     * 把对象存储于特定Map中，待请求周期结束，系统将自动关闭或释放该变量。该列表对象存储
     * 于request的attribute对象。如果相同的名称已经存在将抛出异常。
     *
     * @param request 请求对象。
     * @param name    存储的名称。
     * @param value   存储的值。值允许重复。
     * @throws IllegalArgumentException 参数名称已经存在。
     */
    public static void setObject(HttpServletRequest request, String name, Object value) {
        Object object = request.getAttribute("sysx.varMap");
        ConcurrentHashMap<String, Object> map = JSONObject.toConHashMap(object);
        if (map.containsKey(name)) {
            throw new IllegalArgumentException("Key \"" + name + "\" already exists.");
        }
        map.put(name, value);
    }

    /**
     * 获取真实的IP地址 通过nginx反向代理之后，IP地址会发生改变，所以需要通过特定方法虎丘
     *
     * @param request 请求对象
     * @return IP地址
     */
    public static String getRemoteAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
            ip = request.getHeader("Proxy-Client-IP");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
            ip = request.getHeader("WL-Proxy-Client-IP");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
            ip = request.getRemoteAddr();
        // 如果使用CDN加速，获取到的IP可能是多个，取第一个IP
        return ip.split(",")[0];
    }

    /**
     * 生成字符串二维码
     *
     * @param text   二维码内容
     * @param width  宽度
     * @param height 高度
     * @return 二维码
     * @throws WriterException
     * @throws IOException
     */
    public static byte[] getQRCodeImage(String text, int width, int height) throws WriterException, IOException {
        // 设置二维码纠错级别ＭＡＰ
        Hashtable<EncodeHintType, ErrorCorrectionLevel> hintMap = new Hashtable<EncodeHintType, ErrorCorrectionLevel>();
        hintMap.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H); // 矫错级别
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        // 创建比特矩阵(位矩阵)的QR码编码的字符串
        BitMatrix bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height, hintMap);

        ByteArrayOutputStream pngOutputStream = new ByteArrayOutputStream();
        MatrixToImageWriter.writeToStream(bitMatrix, "PNG", pngOutputStream);
        byte[] pngData = pngOutputStream.toByteArray();
        return pngData;
    }

    /**
     * 检查客户端IP地址是否在白名单中。
     *
     * @param ipList          字符串数组，包含用CIDR表示法表示的白名单IP地址或地址范围。
     * @param clientIpAddress 客户端IP地址，可以是IPv4或IPv6地址。
     * @return 如果客户端IP地址在白名单中，则返回true，否则返回false。
     * @throws UnknownHostException 如果解析IP地址时发生错误。
     */
    public static boolean checkIP(String[] ipList, String clientIpAddress) throws UnknownHostException {
        List<SubnetUtils> ipv4Whitelist = new ArrayList<>();
        List<IPv6Range> ipv6Whitelist = new ArrayList<>();
        for (String ip : ipList) {
            if (ip.contains(":")) {
                IPv6Range range = new IPv6Range(ip.trim());
                ipv6Whitelist.add(range);
            } else {
                SubnetUtils subnet = new SubnetUtils(ip.trim());
                subnet.setInclusiveHostCount(true);
                ipv4Whitelist.add(subnet);
            }
        }
        boolean isAllowed = false;
        if (clientIpAddress.contains(":")) {
            InetAddress clientInetAddress = Inet6Address.getByName(clientIpAddress);
            for (IPv6Range range : ipv6Whitelist) {
                if (range.isInRange(clientInetAddress)) {
                    isAllowed = true;
                    break;
                }
            }
        } else {
            for (SubnetUtils subnet : ipv4Whitelist) {
                if (subnet.getInfo().isInRange(clientIpAddress)) {
                    isAllowed = true;
                    break;
                }
            }
        }
        return isAllowed;
    }

    /**
     * 根据会话指定Key进行速率限制方法
     *
     * @param request      当前的HttpServletRequest对象
     * @param key          限流的KEY
     * @param rate         速率（每秒允许的请求数）
     * @param rateInterval 速率的时间间隔（秒）
     * @return 如果未超过速率限制，则返回true，否则返回false
     */
    public static boolean limitRateSessionKey(HttpServletRequest request, String key, long rate, int rateInterval) {
        HttpSession session = request.getSession(false);
        String sessionId = null == session ? null : session.getId();
        if (sessionId == null || sessionId.isEmpty()) {
            return true; // 对没有sessionId的请求不进行限流，直接返回true
        }
        // 使用session:前缀区分会话限流
        return limitRateKey("session:" + key + ":" + sessionId, rate, rateInterval);
    }

    /**
     * 会话级别的速率限制方法
     *
     * @param request      当前的HttpServletRequest对象
     * @param rate         速率（每秒允许的请求数）
     * @param rateInterval 速率的时间间隔（秒）
     * @return 如果未超过速率限制，则返回true，否则返回false
     */
    public static boolean limitRateSession(HttpServletRequest request, long rate, int rateInterval) {
        HttpSession session = request.getSession(false);
        String sessionId = null == session ? null : session.getId();
        if (sessionId == null || sessionId.isEmpty()) {
            return true; // 对没有sessionId的请求不进行限流，直接返回true
        }
        // 使用session:前缀区分会话限流
        return limitRateKey("session:" + sessionId, rate, rateInterval);
    }

    /**
     * 根据Key进行速率限制方法
     *
     * @param key          限流的KEY
     * @param rate         速率（每秒允许的请求数）
     * @param rateInterval 速率的时间间隔（秒）
     * @return 如果未超过速率限制，则返回true，否则返回false
     */
    public static boolean limitRateKey(String key, long rate, int rateInterval) {
        // 添加前缀以便识别和管理
        String rateLimiterKey = "ratelimiter:" + key;
        RRateLimiter rateLimiter = Base.map.getRedissonClient().getRateLimiter(rateLimiterKey);

        // 设置过期时间（秒）- 增加一些余量确保所有内部键能在处理完成前保持有效
        int expireSeconds = Math.max(1800, rateInterval + 300); // 至少30分钟或间隔时间+5分钟

        boolean justInitialized = false; // 标志位：跟踪此线程是否刚刚初始化了限流器

        // 1. 初始化限流器（如果需要）
        if (!rateLimiter.isExists()) {
            // 2. 加锁，保证只有一个线程/节点执行初始化
            // 使用更长的锁等待时间（例如5秒）来减少竞争导致的超时
            RLock initLock = Base.map.getRedissonClient().getLock("rateLimiterInitLock:" + rateLimiterKey);
            try {
                // 尝试获取锁，最多等待5秒
                if (initLock.tryLock(5, TimeUnit.SECONDS)) {
                    try {
                        // 二次检查：在锁内再次确认，防止其他线程已初始化
                        if (!rateLimiter.isExists()) {
                            // RateType.OVERALL表示集群内所有节点共享配额
                            rateLimiter.trySetRate(RateType.OVERALL, rate, rateInterval, RateIntervalUnit.SECONDS);

                            // 设置过期时间，确保新创建的限流器有 TTL
                            rateLimiter.expire(Duration.ofSeconds(expireSeconds));
                            justInitialized = true; // 标记此线程完成了初始化
                            LogUtil.debug("速率限制器已初始化，键为: " + rateLimiterKey + "，速率为 " + rate + "/" + rateInterval
                                    + "秒，过期时间为 " + expireSeconds + "秒");
                        }
                    } finally {
                        // 确保释放锁
                        if (initLock.isHeldByCurrentThread()) {
                            initLock.unlock();
                        }
                    }
                } else {
                    // 获取锁超时 - 可能有其他节点正在初始化或已经完成
                    if (Var.debug)
                        LogUtil.warn("获取锁超时: " + rateLimiterKey + ". 可能有其他节点正在初始化或已经完成.");
                    // 继续执行，假设限流器现在可能存在或将被 tryAcquire 创建
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                // 在初始化尝试期间被中断，记录错误并认为限流失败（fail closed）
                LogUtil.error("在键 " + rateLimiterKey + " 的速率限制器初始化期间发生错误: ", e);
                return false;
            } catch (Exception e) {
                // 其他初始化期间的未知错误，记录并认为限流失败（fail closed）
                LogUtil.error("在键 " + rateLimiterKey + " 的速率限制器初始化期间发生错误: ", e);
                return false;
            }
        }

        // 3. 尝试获取令牌
        boolean acquired = false;
        try {
            acquired = rateLimiter.tryAcquire(1);
        } catch (Exception e) {
            // 获取令牌时发生错误（例如 Redis 连接问题）
            LogUtil.error("在键 " + rateLimiterKey + " 的速率限制器获取令牌时发生错误: ", e);
            // 在限流器检查失败时，选择 fail open 策略，允许请求通过，避免因 Redis 问题导致服务完全不可用
            return true;
        }

        // 4. 确保设置/刷新过期时间，特别是在获取锁超时后
        // 避免在本线程刚刚初始化并设置了过期时间后再次调用 expire
        if (!justInitialized) {
            try {
                // 设置或刷新过期时间。这处理了锁超时且 tryAcquire 可能创建了 key 的情况。
                // 对于已存在的限流器，这也会在使用时刷新其 TTL。
                rateLimiter.expire(Duration.ofSeconds(expireSeconds));
            } catch (Exception e) {
                // 设置/刷新过期失败通常不影响本次获取令牌的结果，记录错误但继续
                LogUtil.error("键 " + rateLimiterKey + " 的速率限制器设置/刷新过期时发生错误: ", e);
            }
        }

        // 记录限流结果（可选）
        if (!acquired && Var.debug) {
            LogUtil.info("键 " + rateLimiterKey + " 的速率限制器超过限制.");
        }

        return acquired;
    }

}