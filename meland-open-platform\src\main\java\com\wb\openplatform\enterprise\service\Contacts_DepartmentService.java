package com.wb.openplatform.enterprise.service;

import javax.servlet.http.HttpServletRequest;

import org.json.JSONObject;

import com.wb.openplatform.enterprise.util.WeiXinParamesUtil;
import com.wb.openplatform.enterprise.util.WeiXinUtil;
import com.wb.util.LogUtil;

/**
 * 操作部门
 * 
 * <AUTHOR>
 *
 */
public class Contacts_DepartmentService {
	private static String createDepartment_url = "https://qyapi.weixin.qq.com/cgi-bin/department/create?access_token=ACCESS_TOKEN";
	private static String updateDepartment_url = "https://qyapi.weixin.qq.com/cgi-bin/department/update?access_token=ACCESS_TOKEN";
	private static String deleteDepartment_url = "https://qyapi.weixin.qq.com/cgi-bin/department/delete?access_token=ACCESS_TOKEN&id=ID";
	@SuppressWarnings("unused")
	private static String getDepartmentList_url = "https://qyapi.weixin.qq.com/cgi-bin/department/list?access_token=ACCESS_TOKEN&id=ID";

	// 1.创建部门
	public static JSONObject createDepartment(String department, HttpServletRequest request) {
		// 重新加载全局变量
		WeiXinParamesUtil.init();
		String accessToken = WeiXinUtil
				.getAccessToken(WeiXinParamesUtil.corpId, WeiXinParamesUtil.contactsSecret, "TXL").getToken();
		// 2.拼接请求的url
		String Department_url = createDepartment_url.replace("ACCESS_TOKEN", accessToken);
		// 3.调用接口，发送请求，创建部门
		JSONObject jsonObject = WeiXinUtil.httpRequest(Department_url, "POST", department);

		if (null != jsonObject) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	// 2.更新部门
	public static JSONObject updateDepartment(String department, HttpServletRequest request) {
		// 重新加载全局变量
		WeiXinParamesUtil.init();
		String accessToken = WeiXinUtil
				.getAccessToken(WeiXinParamesUtil.corpId, WeiXinParamesUtil.contactsSecret, "TXL").getToken();
		// 2.拼接请求的url
		String Department_url = updateDepartment_url.replace("ACCESS_TOKEN", accessToken);
		// 3.调用接口，发送请求，更新部门
		JSONObject jsonObject = WeiXinUtil.httpRequest(Department_url, "POST", department);

		if (null != jsonObject) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	// 3.删除部门
	public static JSONObject deleteDepartment(String departmentId, HttpServletRequest request) {
		// 重新加载全局变量
		WeiXinParamesUtil.init();
		String accessToken = WeiXinUtil
				.getAccessToken(WeiXinParamesUtil.corpId, WeiXinParamesUtil.contactsSecret, "TXL").getToken();
		// 1.获取请求的url
		String Department_url = deleteDepartment_url.replace("ACCESS_TOKEN", accessToken).replace("ID", departmentId);
		// 2.调用接口，发送请求，删除部门
		JSONObject jsonObject = WeiXinUtil.httpRequest(Department_url, "GET", null);

		// 3.错误消息处理
		if (null != jsonObject) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	// 4.获取部门列表
	public static JSONObject getDepartmentList(String departmentId, HttpServletRequest request) {
		// 重新加载全局变量
		WeiXinParamesUtil.init();
		String accessToken = WeiXinUtil
				.getAccessToken(WeiXinParamesUtil.corpId, WeiXinParamesUtil.contactsSecret, "TXL").getToken();
		// 1.获取请求的url
		// String Department_url=getDepartmentList_url.replace("ACCESS_TOKEN",
		// accessToken).replace("ID", departmentId);
		String Department_url = "https://qyapi.weixin.qq.com/cgi-bin/department/list?access_token=" + accessToken
				+ "&id=" + departmentId;

		// 2.调用接口，发送请求，获取部门
		JSONObject jsonObject = WeiXinUtil.httpRequest(Department_url, "GET", null);

		// 3.错误消息处理
		if (null != jsonObject) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}
}
