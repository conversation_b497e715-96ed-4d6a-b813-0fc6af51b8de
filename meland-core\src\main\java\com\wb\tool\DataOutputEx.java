package com.wb.tool;

import java.io.OutputStream;
import java.sql.Timestamp;
import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wb.common.KVBuffer;
import com.wb.common.Var;
import com.wb.util.DateUtil;
import com.wb.util.JsonUtil;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import com.wb.tool.ExcelObject;

/**
 * 优化的数据输出工具类，提供流式Excel导出功能，适用于大数据量导出场景。
 * 使用POI的SXSSF API实现，避免内存溢出问题。
 */
public class DataOutputEx {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataOutputEx.class);

    /**
     * 流式导出Excel数据，适用于大数据量场景，避免内存溢出。
     * 使用SXSSF API实现，只在内存中保持有限的行数，其余写入临时文件。
     * 支持自动创建多个sheet以避免行数限制。
     * 
     * @param outputStream 输出流。输出数据后该流不关闭，如果有必要需要手动关闭。
     * @param headers 标题列。标题列允许嵌套，并根据设置的嵌套自动合并单元格。
     * @param records 记录数据。如果数据量大建议传入null，通过后续appendRecords方法追加数据。
     * @param title 标题。标题将显示在首行并合并所在行所有单元格且居中显示。如果为null，将不生成标题。
     * @param reportInfo 报表信息，包括合并单元格等配置。
     * @param dateFormat 当未指定格式时使用的默认日期格式。
     * @param timeFormat 当未指定格式时使用的默认时间格式。
     * @param neptune 客户端是否为海王星主题，海王星主题列较宽映射到表格时需要按比缩小。
     * @param rowAccessWindowSize 内存中保留的行数，推荐值100-500，根据每行数据量大小调整。
     * @return StreamingExcelContext 流式Excel上下文，用于后续追加数据和完成导出。
     * @throws Exception 当导出过程中发生错误时抛出。
     */
    public static StreamingExcelContext streamingExcel(OutputStream outputStream, JSONArray headers, JSONArray records,
                                                     String title, JSONObject reportInfo, String dateFormat, 
                                                     String timeFormat, boolean neptune, int rowAccessWindowSize) 
                                                     throws Exception {
        if (rowAccessWindowSize <= 0) {
            rowAccessWindowSize = 100; // 默认值
        }
        
        if (reportInfo == null) {
            reportInfo = new JSONObject();
            reportInfo.put("mergeInfo", new JSONArray());
        }
        
        // 检查headers是否为null或空
        if (headers == null || headers.length() == 0) {
            headers = new JSONArray();
            // 创建一个默认的header
            JSONObject defaultHeader = new JSONObject();
            defaultHeader.put("text", "数据");
            defaultHeader.put("name", "column1");
            defaultHeader.put("field", "column1"); // 添加field属性兼容DataOutput
            defaultHeader.put("width", 100);
            headers.put(defaultHeader);
            LogUtil.warn("导出Excel时headers为空，将使用默认标题");
        } else {
            // 记录headers的内容，用于调试
            LOGGER.debug("导出Excel的headers: " + headers.toString().substring(0, Math.min(200, headers.toString().length())));
            
            // 递归处理所有headers，确保每个header都有field属性
            for (int i = 0; i < headers.length(); i++) {
                try {
                    JSONObject header = headers.getJSONObject(i);
                    ensureHeadersHaveField(header);
                } catch (Exception e) {
                    // 如果不是JSONObject，尝试转换
                    Object item = headers.get(i);
                    JSONObject header = new JSONObject();
                    if (item instanceof String) {
                        header.put("text", item);
                        header.put("name", "column" + (i + 1));
                        header.put("field", "column" + (i + 1));
                    } else {
                        header.put("text", "列" + (i + 1));
                        header.put("name", "column" + (i + 1));
                        header.put("field", "column" + (i + 1));
                    }
                    header.put("width", 100);
                    headers.put(i, header); // 替换原有的非JSONObject元素
                }
            }
        }
        
        int startRow = 0;
        JSONArray topHtml = reportInfo.optJSONArray("topHtml");
        JSONArray bottomHtml = reportInfo.optJSONArray("bottomHtml");
        
        // 创建SXSSF工作簿，设置内存中保留的行数
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowAccessWindowSize);
        // 启用自动刷新写入磁盘的临时文件并进行压缩
        workbook.setCompressTempFiles(true);
        
        JSONArray fields = null;
        int headerRows = 0;
        int headerCols = 0;
        SXSSFSheet sheet;
        
        try {
            sheet = workbook.createSheet("数据导出");
            
            if (topHtml != null) {
                // 使用DataOutput的createHtml方法
                DataOutput.createHtml(sheet, topHtml);
                startRow = topHtml.length();
                title = null;
            }
            
            if (title != null) {
                startRow = 1;
            }
            
            // 使用DataOutput的createHeaders方法
            Object[] result = DataOutput.createHeaders(sheet, headers, startRow, neptune);
            headerCols = (Integer) result[0];
            headerRows = (Integer) result[1];
            fields = (JSONArray) result[2];
            
            if (title != null) {
                // 使用DataOutput的createTitle方法
                DataOutput.createTitle(sheet, title, headerCols);
            }
            
            startRow += headerRows;
            
            if (Var.getBool("sys.service.excel.freezePane")) {
                sheet.createFreezePane(0, startRow);
            }
            
            // 创建流式Excel上下文，传递所有必要参数
            StreamingExcelContext context = new StreamingExcelContext(workbook, outputStream, sheet, fields, startRow, 
                                                                   dateFormat, timeFormat, headers, title, reportInfo, neptune);
            
            // 如果提供了初始记录，追加到Excel
            if (records != null && records.length() > 0) {
                appendRecords(context, records);
            }
            
            // 可以合并单元格，这通常不会占用太多内存
            if (reportInfo.has("mergeInfo")) {
                // 使用ExcelObject的mergeCells方法处理合并单元格
                ExcelObject.mergeCells(sheet, reportInfo, startRow, Integer.MAX_VALUE);
            }
            
            if (bottomHtml != null) {
                // 使用DataOutput的createHtml方法
                DataOutput.createHtml(sheet, bottomHtml);
            }
            
            return context;
        } catch (Exception e) {
            // 出现异常时，确保释放临时文件
            if (workbook != null) {
                try {
                    workbook.dispose();
                } catch (Exception ex) {
                    // 忽略关闭时的异常
                }
                try {
                    workbook.close();
                } catch (Exception ex) {
                    // 忽略关闭时的异常
                }
            }
            throw e;
        }
    }
    
    /**
     * 向流式Excel中追加记录数据。
     * 
     * @param context 流式Excel上下文。
     * @param records 当前批次的记录数据。
     * @throws Exception 当追加过程中发生错误时抛出。
     */
    public static void appendRecords(StreamingExcelContext context, JSONArray records) throws Exception {
        if (records == null || records.length() == 0) {
            return;
        }
        
        LOGGER.debug("准备追加" + records.length() + "条记录到Sheet " + (context.getSheetIndex() + 1) 
            + "，当前行号: " + context.getCurrentRow());
        
        int startRow = context.getCurrentRow();
        Sheet sheet = context.getSheet();
        JSONArray fields = context.getFields();
        String dateFormat = context.getDateFormat();
        String timeFormat = context.getTimeFormat();
        int j = fields.length();
        
        // 获取字段名数组
        String[] fieldNames = context.getFieldNames();
        if (fieldNames == null) {
            fieldNames = new String[j];
            for (int k = 0; k < j; k++) {
                try {
                    JSONObject field = fields.getJSONObject(k);
                    // 优先使用field属性，与DataOutput保持一致
                    String fieldName = field.optString("field", "");
                    if (StringUtil.isEmpty(fieldName)) {
                        fieldName = field.optString("name", "column" + (k + 1));
                    }
                    fieldNames[k] = fieldName;
                } catch (Exception e) {
                    fieldNames[k] = "column" + (k + 1);
                }
            }
            context.setFieldNames(fieldNames);
        }
        
        // 预先获取基础样式
        CellStyle baseStyle = getCellStyle(context, "text");
        
        // 布尔值显示配置
        String boolString = Var.getString("sys.service.excel.boolText");
        boolean useBoolString = !boolString.isEmpty();
        String trueText = null;
        String falseText = null;
        if (useBoolString) {
            String[] boolStrings = boolString.split(",");
            trueText = boolStrings[0];
            falseText = boolStrings[1];
        }
        
        for (int i = 0; i < records.length(); i++) {
            // 检查是否需要创建新sheet
            if (context.checkAndCreateNewSheetIfNeeded()) {
                // 如果创建了新sheet，更新引用
                sheet = context.getSheet();
                startRow = context.getCurrentRow();
                LOGGER.debug("创建新Sheet后继续处理记录，剩余记录数: " + (records.length() - i));
            }
            
            Object recordObj = records.get(i);
            JSONObject record;
            
            // 确保record是JSONObject
            if (recordObj instanceof JSONObject) {
                record = (JSONObject) recordObj;
            } else if (recordObj instanceof String) {
                // 如果是字符串，创建一个只有一个值的对象
                record = new JSONObject();
                record.put(fieldNames[0], recordObj);
            } else if (recordObj instanceof JSONArray) {
                // 如果是数组，将数组值映射到字段
                JSONArray arrayRecord = (JSONArray) recordObj;
                record = new JSONObject();
                for (int k = 0; k < Math.min(j, arrayRecord.length()); k++) {
                    record.put(fieldNames[k], arrayRecord.get(k));
                }
            } else {
                // 其他类型，创建一个默认对象
                record = new JSONObject();
                record.put(fieldNames[0], String.valueOf(recordObj));
            }
            
            Row row = sheet.createRow(startRow++);
            
            for (int m = 0; m < j; m++) {
                JSONObject field;
                try {
                    field = fields.getJSONObject(m);
                } catch (Exception e) {
                    // 如果获取字段失败，跳过此列
                    LogUtil.warn("获取字段定义失败，跳过第" + (m+1) + "列: " + e.getMessage());
                    continue;
                }
                
                Cell cell = row.createCell(m);
                
                // 获取字段值 - 使用DataOutput逻辑
                Object value = null;
                String fieldName = fieldNames[m];
                
                // 使用JsonUtil.opt方法获取值，与DataOutput保持一致
                if (!StringUtil.isEmpty(fieldName)) {
                    value = JsonUtil.opt(record, fieldName);
                }
                
                // 如果没有值，尝试使用备用名称
                if (value == null) {
                    String name = field.optString("name", "column" + (m + 1));
                    if (record.has(name)) {
                        value = record.opt(name);
                    } else if (record.has("col" + (m + 1))) {
                        value = record.opt("col" + (m + 1));
                    } else if (record.has("column" + (m + 1))) {
                        value = record.opt("column" + (m + 1));
                    } else if (record.has("f" + (m + 1))) {
                        value = record.opt("f" + (m + 1));
                    } else if (record.has("field" + (m + 1))) {
                        value = record.opt("field" + (m + 1));
                    }
                }
                
                // 如果值为空，跳过
                if (value == null || "null".equals(value) || value == JSONObject.NULL) {
                    continue;
                }
                
                // 处理键值映射
                String keyName = field.optString("keyName");
                if (!keyName.isEmpty()) {
                    Object keyMap = KVBuffer.buffer.get(keyName);
                    if (keyMap != null && keyMap instanceof ConcurrentHashMap && value != null) {
                        value = KVBuffer.getValue((ConcurrentHashMap<?, ?>) keyMap, value);
                    }
                }
                
                // 获取字段类型和格式
                String dataTypeStr = keyName.isEmpty() ? field.optString("type", "").toLowerCase() : "string";
                String format = field.optString("format");
                String align = field.optString("align", "");
                
                // 确定数据类型
                int dataType;
                if (dataTypeStr.equals("string")) {
                    dataType = 1; // 字符串类型
                } else if ((dataTypeStr.startsWith("int")) || (dataTypeStr.equals("float")) || (dataTypeStr.equals("number"))) {
                    dataType = 2; // 数字类型
                } else if (dataTypeStr.equals("date")) {
                    dataType = 3; // 日期类型
                } else if (dataTypeStr.startsWith("bool")) {
                    dataType = 4; // 布尔类型
                } else {
                    // 自动类型，根据值判断
                    if (value instanceof Number) {
                        dataType = 2;
                    } else if (value instanceof Date) {
                        dataType = 3;
                    } else if (value instanceof Boolean) {
                        dataType = 4;
                    } else {
                        dataType = 1; // 默认按字符串处理
                    }
                }
                
                // 定义用于缓存最终使用的样式
                CellStyle finalStyle = null;
                
                // 根据数据类型和格式创建样式
                if (dataType == 2) { // 数字类型
                    String styleKey = "number" + (StringUtil.isEmpty(format) ? "" : "_" + format) + 
                                       (!align.isEmpty() ? "_" + align : "");
                    
                    finalStyle = context.getStyleCache().get(styleKey);
                    if (finalStyle == null) {
                        finalStyle = context.getWorkbook().createCellStyle();
                        finalStyle.cloneStyleFrom(baseStyle);
                        
                        // 设置数字格式
                        if (!StringUtil.isEmpty(format)) {
                            DataFormat dataFormat = context.getWorkbook().createDataFormat();
                            finalStyle.setDataFormat(dataFormat.getFormat(format));
                        } else {
                            // 默认使用数字格式而非通用格式，避免被识别为日期
                            if (dataTypeStr.startsWith("int")) {
                                DataFormat dataFormat = context.getWorkbook().createDataFormat();
                                finalStyle.setDataFormat(dataFormat.getFormat("0")); // 整数格式
                            } else if (dataTypeStr.equals("float")) {
                                DataFormat dataFormat = context.getWorkbook().createDataFormat();
                                finalStyle.setDataFormat(dataFormat.getFormat("0.00")); // 浮点数格式
                            }
                        }
                        
                        // 设置对齐方式
                        if (!align.isEmpty()) {
                            finalStyle.setAlignment(ExcelObject.getAlignment(align, HorizontalAlignment.LEFT));
                        }
                        
                        context.getStyleCache().put(styleKey, finalStyle);
                    }
                } else if (dataType == 3) { // 日期类型
                    String formatPattern;
                    if (StringUtil.isEmpty(format)) {
                        // 根据值判断是否有时间部分
                        boolean hasTime = false;
                        if (value instanceof Date) {
                            hasTime = !DateUtil.dateToStr((Date)value).endsWith("00:00:00.0");
                        } else if (value instanceof String) {
                            String dateTimeStr = value.toString();
                            hasTime = (!dateTimeStr.endsWith("00:00:00.0")) && 
                                      (!(dateTimeStr.endsWith("00:00:00") || dateTimeStr.endsWith("00:00:00.000")));
                        }
                        
                        // 选择合适的格式
                        if (hasTime) {
                            formatPattern = ExcelObject.toExcelDateFormat(dateFormat + " " + timeFormat, true);
                        } else {
                            formatPattern = ExcelObject.toExcelDateFormat(dateFormat, true);
                        }
                    } else {
                        formatPattern = ExcelObject.toExcelDateFormat(format, false);
                        if (formatPattern == null) {
                            formatPattern = ExcelObject.toExcelDateFormat(dateFormat, true);
                        }
                    }
                    
                    String styleKey = "date_" + formatPattern + (!align.isEmpty() ? "_" + align : "");
                    finalStyle = context.getStyleCache().get(styleKey);
                    
                    if (finalStyle == null) {
                        finalStyle = context.getWorkbook().createCellStyle();
                        finalStyle.cloneStyleFrom(baseStyle);
                        
                        // 设置日期格式
                        DataFormat dataFormat = context.getWorkbook().createDataFormat();
                        finalStyle.setDataFormat(dataFormat.getFormat(formatPattern));
                        
                        // 设置对齐方式
                        if (!align.isEmpty()) {
                            finalStyle.setAlignment(ExcelObject.getAlignment(align, HorizontalAlignment.LEFT));
                        }
                        
                        context.getStyleCache().put(styleKey, finalStyle);
                    }
                } else { // 字符串或布尔类型
                    // 检查是否需要文本格式(@)来防止Excel自动转换
                    boolean needTextFormat = false;
                    String strVal = null;
                    
                    if (value instanceof String) {
                        strVal = (String)value;
                        // 检查是否是特殊格式的字符串，可能被Excel误识别
                        if (strVal.matches("\\d{2}-\\d{1,2}-\\d{1,2}") || strVal.matches("\\d{1,2}-\\d{1,2}-\\d{1,2}")) {
                            needTextFormat = true;
                        }
                    }
                    
                    String styleKey = (needTextFormat ? "text_format_@" : "text") + 
                                      (!align.isEmpty() ? "_" + align : "");
                    
                    finalStyle = context.getStyleCache().get(styleKey);
                    if (finalStyle == null) {
                        finalStyle = context.getWorkbook().createCellStyle();
                        finalStyle.cloneStyleFrom(baseStyle);
                        
                        // 设置文本格式
                        if (needTextFormat) {
                            DataFormat dataFormat = context.getWorkbook().createDataFormat();
                            finalStyle.setDataFormat(dataFormat.getFormat("@"));
                        }
                        
                        // 设置对齐方式
                        if (!align.isEmpty()) {
                            finalStyle.setAlignment(ExcelObject.getAlignment(align, HorizontalAlignment.LEFT));
                        }
                        
                        context.getStyleCache().put(styleKey, finalStyle);
                    }
                }
                
                // 应用样式
                if (finalStyle != null) {
                    cell.setCellStyle(finalStyle);
                } else {
                    cell.setCellStyle(baseStyle); // 默认基础样式
                }
                
                // 设置单元格值
                if (dataType == 2) { // 数字类型
                    double number;
                    if (value instanceof Number) {
                        number = ((Number) value).doubleValue();
                    } else {
                        try {
                            number = Double.parseDouble(value.toString());
                        } catch (NumberFormatException e) {
                            // 如果无法解析为数字，按文本处理
                            cell.setCellValue(value.toString());
                            continue;
                        }
                    }
                    cell.setCellValue(number);
                } else if (dataType == 3) { // 日期类型
                    Date date;
                    if (value instanceof Date) {
                        date = (Date) value;
                    } else {
                        try {
                            date = Timestamp.valueOf(DateUtil.fixTimestamp(value.toString(), false));
                        } catch (Exception e) {
                            // 如果无法解析为日期，按文本处理
                            cell.setCellValue(value.toString());
                            continue;
                        }
                    }
                    cell.setCellValue(date);
                } else if (dataType == 4) { // 布尔类型
                    boolean boolValue = StringUtil.getBool(value.toString());
                    if (useBoolString) {
                        cell.setCellValue(boolValue ? trueText : falseText);
                    } else {
                        cell.setCellValue(boolValue);
                    }
                } else { // 字符串类型或其他类型
                    String strVal = value.toString();
                    // 如果是特殊格式的字符串，添加单引号前缀防止Excel自动转换
                    if (strVal.matches("\\d{2}-\\d{1,2}-\\d{1,2}") || strVal.matches("\\d{1,2}-\\d{1,2}-\\d{1,2}")) {
                        if (!strVal.startsWith("'")) {
                            strVal = "'" + strVal;
                        }
                    }
                    cell.setCellValue(strVal);
                }
            }
        }
        
        context.setCurrentRow(startRow);
        LOGGER.debug("已完成" + records.length() + "条记录追加，更新行号为: " + startRow);
    }
    
    /**
     * 完成流式Excel导出，将数据写入输出流并释放资源。
     * 
     * @param context 流式Excel上下文。
     * @throws Exception 当写入过程中发生错误时抛出。
     */
    public static void finishExcel(StreamingExcelContext context) throws Exception {
        if (context == null) {
            return;
        }
        
        Workbook workbook = context.getWorkbook();
        OutputStream outputStream = context.getOutputStream();
        
        try {
            // 记录导出统计信息
            int totalSheets = context.getSheetIndex() + 1;
            int totalRows = context.getCurrentRow();
            if (totalSheets > 1) {
                LOGGER.debug("Excel导出完成，共创建" + totalSheets + "个Sheet，最后Sheet当前行号: " + 
                          totalRows + "，总数据量超过" + totalSheets * StreamingExcelContext.MAX_ROWS_PER_SHEET + "行");
            } else {
                LOGGER.debug("Excel导出完成，共写入" + totalRows + "行数据");
            }
            
            // 写入输出流
            workbook.write(outputStream);
        } finally {
            // 释放临时文件
            if (workbook instanceof SXSSFWorkbook) {
                try {
                    ((SXSSFWorkbook) workbook).dispose();
                } catch (Exception ex) {
                    LogUtil.warn("释放临时文件失败: " + ex.getMessage());
                }
            }
            
            // 关闭工作簿
            try {
                workbook.close();
            } catch (Exception ex) {
                LogUtil.warn("关闭工作簿失败: " + ex.getMessage());
            }
        }
    }
    
    /**
     * 流式Excel上下文类，用于保存流式导出的状态和资源。
     */
    public static class StreamingExcelContext {
        private final Workbook workbook;
        private final OutputStream outputStream;
        private Sheet sheet;
        private final JSONArray fields;
        private final int startRow;
        private final String dateFormat;
        private final String timeFormat;
        private int currentRowNum = 0;
        
        // 多sheet支持
        private int sheetIndex = 0;
        private final String sheetNamePrefix;
        private final JSONArray headers;
        private final String title;
        private final JSONObject reportInfo;
        private final boolean neptune;
        
        // 行数限制
        public static final int MAX_ROWS_PER_SHEET = 1000000; // Excel最大支持1048576行，留一定余量
        
        // 样式和类型信息
        private CellStyle[] colStyles;
        private CellStyle[][] dateTimeStyles;
        private short rowHeight;
        private String trueText;
        private String falseText;
        private boolean useBoolString;
        private int[] dataTypes;
        private Object[] keyMaps;
        private String[] fieldNames;
        
        private Map<String, CellStyle> styleCache = new ConcurrentHashMap<>();
        
        /**
         * 构造函数
         */
        public StreamingExcelContext(Workbook workbook, OutputStream outputStream, Sheet sheet, 
                                   JSONArray fields, int startRow, String dateFormat, String timeFormat,
                                   JSONArray headers, String title, JSONObject reportInfo, boolean neptune) {
            this.workbook = workbook;
            this.outputStream = outputStream;
            this.sheet = sheet;
            this.fields = fields;
            this.startRow = startRow;
            this.dateFormat = dateFormat;
            this.timeFormat = timeFormat;
            this.currentRowNum = startRow;
            this.headers = headers;
            this.title = title;
            this.reportInfo = reportInfo;
            this.neptune = neptune;
            this.sheetNamePrefix = "数据导出";
            
            // 初始化字段名数组
            if (fields != null && fields.length() > 0) {
                int l = fields.length();
                this.fieldNames = new String[l];
                for (int i = 0; i < l; i++) {
                    try {
                        JSONObject field = fields.getJSONObject(i);
                        // 优先使用field属性，与DataOutput保持一致，如果没有则使用name属性
                        String fieldName = field.optString("field", "");
                        if (StringUtil.isEmpty(fieldName)) {
                            fieldName = field.optString("name", "column" + (i + 1));
                        }
                        this.fieldNames[i] = fieldName;
                    } catch (Exception e) {
                        this.fieldNames[i] = "column" + (i + 1);
                        LogUtil.warn("获取字段名失败，使用默认字段名: " + e.getMessage());
                    }
                }
            }
        }
        
        /**
         * 检查是否需要创建新的sheet，并在必要时创建
         * 
         * @return 是否创建了新sheet
         * @throws Exception 创建sheet时可能发生的异常
         */
        public boolean checkAndCreateNewSheetIfNeeded() throws Exception {
            // 如果当前行数接近最大行数，创建新sheet
            if (currentRowNum >= MAX_ROWS_PER_SHEET) {
                // 创建新的sheet
                sheetIndex++;
                String newSheetName = sheetNamePrefix + (sheetIndex > 0 ? " (" + (sheetIndex + 1) + ")" : "");
                SXSSFSheet newSheet = (SXSSFSheet) workbook.createSheet(newSheetName);
                
                // 配置新sheet的表头
                int newStartRow = 0;
                JSONArray topHtml = reportInfo.optJSONArray("topHtml");
                
                if (topHtml != null) {
                    // 使用DataOutput的createHtml方法
                    DataOutput.createHtml(newSheet, topHtml);
                    newStartRow = topHtml.length();
                }
                
                if (title != null) {
                    newStartRow = 1;
                }
                
                // 使用DataOutput的createHeaders方法创建表头
                Object[] result = DataOutput.createHeaders(newSheet, headers, newStartRow, neptune);
                int headerCols = (Integer) result[0];
                int headerRows = (Integer) result[1];
                
                if (title != null) {
                    // 使用DataOutput的createTitle方法
                    DataOutput.createTitle(newSheet, title, headerCols);
                }
                
                newStartRow += headerRows;
                
                if (Var.getBool("sys.service.excel.freezePane")) {
                    newSheet.createFreezePane(0, newStartRow);
                }
                
                // 更新当前sheet和行号
                this.sheet = newSheet;
                this.currentRowNum = newStartRow;
                
                LOGGER.debug("已创建新的Sheet: " + newSheetName + "，当前行号: " + currentRowNum);
                return true;
            }
            return false;
        }
        
        public Workbook getWorkbook() {
            return workbook;
        }
        
        public OutputStream getOutputStream() {
            return outputStream;
        }
        
        public Sheet getSheet() {
            return sheet;
        }
        
        public JSONArray getFields() {
            return fields;
        }
        
        public int getStartRow() {
            return startRow;
        }
        
        public String getDateFormat() {
            return dateFormat;
        }
        
        public String getTimeFormat() {
            return timeFormat;
        }
        
        public int getCurrentRowNum() {
            return currentRowNum;
        }
        
        public void setCurrentRowNum(int currentRowNum) {
            this.currentRowNum = currentRowNum;
        }
        
        public CellStyle[] getColStyles() {
            return colStyles;
        }
        
        public void setColStyles(CellStyle[] colStyles) {
            this.colStyles = colStyles;
        }
        
        public CellStyle[][] getDateTimeStyles() {
            return dateTimeStyles;
        }
        
        public void setDateTimeStyles(CellStyle[][] dateTimeStyles) {
            this.dateTimeStyles = dateTimeStyles;
        }
        
        public short getRowHeight() {
            return rowHeight;
        }
        
        public void setRowHeight(short rowHeight) {
            this.rowHeight = rowHeight;
        }
        
        public String getTrueText() {
            return trueText;
        }
        
        public void setTrueText(String trueText) {
            this.trueText = trueText;
        }
        
        public String getFalseText() {
            return falseText;
        }
        
        public void setFalseText(String falseText) {
            this.falseText = falseText;
        }
        
        public boolean isUseBoolString() {
            return useBoolString;
        }
        
        public void setUseBoolString(boolean useBoolString) {
            this.useBoolString = useBoolString;
        }
        
        public int[] getDataTypes() {
            return dataTypes;
        }
        
        public void setDataTypes(int[] dataTypes) {
            this.dataTypes = dataTypes;
        }
        
        public Object[] getKeyMaps() {
            return keyMaps;
        }
        
        public void setKeyMaps(Object[] keyMaps) {
            this.keyMaps = keyMaps;
        }
        
        public String[] getFieldNames() {
            return fieldNames;
        }
        
        public void setFieldNames(String[] fieldNames) {
            this.fieldNames = fieldNames;
        }
        
        public Map<String, CellStyle> getStyleCache() {
            return styleCache;
        }
        
        public int getCurrentRow() {
            return currentRowNum;
        }
        
        public void setCurrentRow(int currentRow) {
            this.currentRowNum = currentRow;
        }
        
        public int getSheetIndex() {
            return sheetIndex;
        }
    }

    /**
     * 根据单元格类型获取样式。
     * 这里直接调用DataOutput的createCellStyle方法
     * @param context 流式Excel上下文
     * @param type 单元格类型，如"text"
     * @return 单元格样式
     */
    public static CellStyle getCellStyle(StreamingExcelContext context, String type) {
        Map<String, CellStyle> styleCache = context.getStyleCache();
        CellStyle cellStyle = styleCache.get(type);
        if (cellStyle == null) {
            Object[] styles = DataOutput.createCellStyle(context.getWorkbook(), type);
            cellStyle = (CellStyle) styles[0];
            styleCache.put(type, cellStyle);
        }
        return cellStyle;
    }

    /**
     * 递归处理嵌套的headers，确保每个header都有field属性
     * 
     * @param header 需要处理的header对象
     */
    private static void ensureHeadersHaveField(JSONObject header) {
        if (header == null) {
            return;
        }
        
        try {
            // 确保text字段存在
            if (!header.has("text")) {
                header.put("text", "未命名列");
                LogUtil.warn("发现header缺少text属性，已自动添加默认值");
            }
            
            // 确保当前header有field属性
            if (!header.has("field") && header.has("name")) {
                header.put("field", header.getString("name"));
                LOGGER.debug("根据name属性(" + header.getString("name") + ")补充了field属性");
            } else if (!header.has("field") && !header.has("name")) {
                header.put("field", "column_" + System.currentTimeMillis() % 1000);
                LogUtil.warn("发现header既无field又无name属性，已自动添加默认field值");
            }
            
            // 处理嵌套header
            if (header.has("items") && header.optJSONArray("items") != null) {
                JSONArray items = header.getJSONArray("items");
                for (int i = 0; i < items.length(); i++) {
                    try {
                        JSONObject subHeader = items.getJSONObject(i);
                        // 递归处理子header
                        ensureHeadersHaveField(subHeader);
                    } catch (Exception e) {
                        // 忽略处理失败的子header
                        LogUtil.warn("处理嵌套header失败: " + e.getMessage());
                    }
                }
                
                // 确保有width属性，以便Excel正确显示列宽
                if (!header.has("width")) {
                    header.put("width", 100);
                }
            }
        } catch (Exception e) {
            LogUtil.error("处理header失败: " + e.getMessage());
        }
    }
} 