package com.wb.cache;

import com.wb.util.LogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * Redis健康检查服务
 * 定期检查Redis连接状态，并在发现问题时尝试恢复
 */
@Service
public class RedisHealthCheckService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RedisHealthCheckService.class);

    /**
     * Redis连接工厂
     */
    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    /**
     * 检查间隔（毫秒）
     */
    private static final long CHECK_INTERVAL = 60000; // 1分钟

    /**
     * 上次连接状态 - 默认认为连接正常
     */
    private boolean lastConnectionStatus = true;

    /**
     * 连续正常次数计数器
     */
    private int successiveSuccessCount = 0;

    /**
     * 连续错误次数计数器
     */
    private int successiveErrorCount = 0;

    /**
     * 执行健康检查
     * 每分钟执行一次健康检查
     */
    @Scheduled(fixedRate = CHECK_INTERVAL, initialDelay = 30 * 1000)
    public void checkRedisHealth() {
        RedisConnection connection = null;
        try {
            // 获取Redis连接
            connection = redisConnectionFactory.getConnection();

            // 执行PING命令检查连接状态
            String pong = new String(connection.ping());

            if ("PONG".equalsIgnoreCase(pong)) {
                // 连接正常
                successiveSuccessCount++;

                if (!lastConnectionStatus) {
                    // 从异常状态恢复，记录日志
                    LOGGER.info("Redis连接已恢复正常（之前出现异常）");
                    lastConnectionStatus = true;
                    successiveErrorCount = 0;
                } else if (successiveSuccessCount == 1 || successiveSuccessCount % 60 == 0) {
                    // 第一次成功或每60次成功记录一次日志（相当于每小时记录一次）
                    LOGGER.info("Redis连接状态正常，持续" + successiveSuccessCount + "次检查");
                }
            } else {
                // 连接异常但能返回响应
                successiveErrorCount++;
                successiveSuccessCount = 0;

                if (lastConnectionStatus || successiveErrorCount % 5 == 1) {
                    // 从正常变为异常，或每5次异常记录一次
                    LogUtil.warn("Redis连接异常，响应: " + pong + "，连续异常次数: " + successiveErrorCount);
                    lastConnectionStatus = false;
                }
                // 尝试重置连接
                resetConnection();
            }
        } catch (Exception e) {
            // 连接失败
            successiveErrorCount++;
            successiveSuccessCount = 0;

            if (lastConnectionStatus || successiveErrorCount % 5 == 1) {
                // 从正常变为异常，或每5次异常记录一次
                LogUtil.error("Redis连接失败 - " + e.getMessage() + "，连续失败次数: " + successiveErrorCount, e);
                lastConnectionStatus = false;
            }
            // 尝试重置连接
            resetConnection();
        } finally {
            // 关闭连接
            if (connection != null) {
                try {
                    connection.close();
                } catch (Exception e) {
                    if (lastConnectionStatus) {
                        // 只在状态变化时记录
                        LogUtil.warn("关闭Redis连接异常: " + e.getMessage());
                        lastConnectionStatus = false;
                    }
                }
            }
        }
    }

    /**
     * 重置Redis连接
     * 尝试重新初始化Redis连接
     */
    private void resetConnection() {
        try {
            // 只在连续出错3次后尝试重置连接
            if (successiveErrorCount >= 3) {
                // 在这里可以执行一些连接重置逻辑
                // 例如重新初始化连接池或重新获取连接
                LOGGER.info("正在尝试重置Redis连接（连续" + successiveErrorCount + "次异常）...");

                // 关闭所有连接并重新初始化连接池
                // 注意：不同Redis客户端实现可能需要不同的重置方法
                if (redisConnectionFactory instanceof org.springframework.data.redis.connection.jedis.JedisConnectionFactory) {
                    ((org.springframework.data.redis.connection.jedis.JedisConnectionFactory) redisConnectionFactory).destroy();
                    ((org.springframework.data.redis.connection.jedis.JedisConnectionFactory) redisConnectionFactory).afterPropertiesSet();
                    LOGGER.info("Redis连接重置完成");
                } else {
                    LogUtil.warn("无法重置当前类型的Redis连接工厂: " + redisConnectionFactory.getClass().getName());
                }
            }
        } catch (Exception e) {
            LogUtil.error("重置Redis连接失败: " + e.getMessage(), e);
        }
    }
} 