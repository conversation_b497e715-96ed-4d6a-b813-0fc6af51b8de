package com.wb.rocketmq.expense;

import com.wb.rocketmq.config.MqConfig;
import com.wb.rocketmq.config.RocketMQConfiguration;
import com.wb.rocketmq.util.MessageUtil;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.consumer.FilterExpression;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.apache.rocketmq.client.apis.consumer.MessageListener;
import org.apache.rocketmq.client.apis.consumer.PushConsumer;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;

/**
 * RocketMQ消费者管理类
 * 集中管理消费者实例
 */
public class RocketMQConsumerManager {
    private static volatile RocketMQConsumerManager instance;
    private MqConfig config;
    private final Map<String, PushConsumer> consumerMap = new ConcurrentHashMap<>();
    private final Map<String, ConsumerRunnable> consumerRunnables = new ConcurrentHashMap<>();
    
    // 线程池管理消费者线程
    private final ExecutorService consumerExecutor;
    
    // 重试策略相关参数
    private static final int MAX_RETRIES = 5; // 最大重试次数
    private static final int INITIAL_BACKOFF_MS = 1000; // 初始退避时间（毫秒）
    private static final int MAX_BACKOFF_MS = 60000; // 最大退避时间（毫秒）
    
    // 线程池配置参数
    private static final int CORE_POOL_SIZE = 5;
    private static final int MAX_POOL_SIZE = 20;
    private static final long KEEP_ALIVE_TIME = 60L;

    /**
     * 私有构造函数，防止外部实例化
     */
    private RocketMQConsumerManager() {
        try {
            // 确保RocketMQ配置初始化
            // RocketMQConfiguration.init(); // 在Web应用中不需要设置守护线程
            
            this.config = new MqConfig();
            
            // 初始化线程池
            this.consumerExecutor = new ThreadPoolExecutor(
                CORE_POOL_SIZE,
                MAX_POOL_SIZE,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(),
                r -> {
                    Thread t = new Thread(r);
                    t.setDaemon(true);
                    return t;
                },
                new ThreadPoolExecutor.CallerRunsPolicy()
            );
            
            // 配置MessageUtil
            MessageUtil.linker();
            
            // 注册关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown));
            
            LogUtil.info("RocketMQ消费者管理器初始化成功");
        } catch (Exception e) {
            LogUtil.error(StringUtil.format("RocketMQ消费者管理器初始化失败：{0}", e.getMessage()), e);
            throw new RuntimeException("RocketMQ消费者管理器初始化失败", e);
        }
    }

    /**
     * 使用双重检查锁定单例模式获取实例
     * 
     * @return 实例对象
     */
    public static RocketMQConsumerManager getInstance() {
        if (instance == null) {
            synchronized (RocketMQConsumerManager.class) {
                if (instance == null) {
                    instance = new RocketMQConsumerManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 启动消费者
     * 
     * @param threadName 线程名称
     * @param listener 消息监听器
     * @param groupId 消费者组ID
     * @param topicId 主题ID
     * @return 是否成功启动
     */
    public boolean startConsumer(String threadName, MessageListener listener, String groupId, String topicId) {
        String key = groupId + ":" + topicId;
        
        // 使用同步块确保线程安全
        synchronized (consumerRunnables) {
            // 检查是否已存在此消费者
            if (consumerRunnables.containsKey(key)) {
                LogUtil.info(StringUtil.format("消费者[{0}]已存在，无需重复启动", key));
                return true;
            }
            
            try {
                ConsumerRunnable runnable = new ConsumerRunnable(threadName, listener, groupId, topicId);
                consumerRunnables.put(key, runnable);
                
                // 使用线程池提交任务（异步方式启动）
                consumerExecutor.submit(runnable);
                
                // 不再同步等待启动完成，避免TimeoutException
                LogUtil.info(StringUtil.format("消息消费线程[{0}]已提交启动，消费者组：{1}，主题：{2}", 
                    threadName, groupId, topicId));
                return true;
            } catch (Exception e) {
                // 启动失败时移除记录
                consumerRunnables.remove(key);
                LogUtil.error(StringUtil.format("提交启动消费者[{0}]失败：{1}", key, e.getMessage()), e);
                return false;
            }
        }
    }
    
    /**
     * 消费者线程运行类
     */
    private class ConsumerRunnable implements Runnable {
        private final String threadName;
        private final MessageListener listener;
        private final String groupId;
        private final String topicId;
        private volatile boolean running = true;
        private PushConsumer consumer;
        private final CountDownLatch shutdownLatch = new CountDownLatch(1);
        private volatile boolean startupSuccess = false;
        
        public ConsumerRunnable(String threadName, MessageListener listener, String groupId, String topicId) {
            this.threadName = threadName;
            this.listener = listener;
            this.groupId = groupId;
            this.topicId = topicId;
        }
        
        @Override
        public void run() {
            Thread.currentThread().setName(threadName);
            try {
                runWithRetry(0, INITIAL_BACKOFF_MS);
            } catch (Throwable t) {
                // 捕获所有异常，确保线程不会意外终止
                LogUtil.error(StringUtil.format("消费者线程[{0}]运行异常：{1}", threadName, t.getMessage()), t);
                String key = groupId + ":" + topicId;
                synchronized (consumerRunnables) {
                    consumerRunnables.remove(key);
                }
                consumerMap.remove(key);
            } finally {
                shutdownLatch.countDown();
            }
        }
        
        private void runWithRetry(int retryCount, int backoffMs) {
            if (!running || retryCount >= MAX_RETRIES) {
                if (retryCount >= MAX_RETRIES) {
                    LogUtil.error(StringUtil.format("重试次数已达到上限，消息消费服务启动失败：{0}", this.listener.getClass()));
                    // 启动失败，从映射中移除
                    String key = groupId + ":" + topicId;
                    synchronized (consumerRunnables) {
                        consumerRunnables.remove(key);
                    }
                }
                return;
            }
            
            try {
                // 初始化消费者
                ClientServiceProvider provider = ClientServiceProvider.loadService();
                FilterExpression filterExpression = new FilterExpression("*", FilterExpressionType.TAG);
                
                // 创建消费者
                consumer = provider.newPushConsumerBuilder()
                    .setClientConfiguration(MessageUtil.configuration)
                    .setConsumerGroup(groupId)
                    .setSubscriptionExpressions(Collections.singletonMap(topicId, filterExpression))
                    .setMessageListener(this.listener)
                    .build();
                
                // 记录消费者实例
                String key = groupId + ":" + topicId;
                consumerMap.put(key, consumer);
                
                // 标记启动成功
                startupSuccess = true;
                
                LogUtil.info(StringUtil.format("消息消费服务[{0}]启动成功，消费者组：{1}，主题：{2}", 
                    this.listener.getClass(), groupId, topicId));
                
                // 等待关闭信号
                try {
                    synchronized (this) {
                        while (running) {
                            try {
                                this.wait();
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                                break;
                            }
                        }
                    }
                } finally {
                    closeConsumer();
                }
            } catch (Exception e) {
                LogUtil.error(StringUtil.format("消息消费服务[{0}]启动失败，准备重试（{1}/{2}）：{3}", 
                    this.listener.getClass(), retryCount + 1, MAX_RETRIES, e.getMessage()), e);
                
                // 使用指数退避策略计算下次重试的退避时间
                int nextBackoffMs = Math.min(backoffMs * 2, MAX_BACKOFF_MS);
                
                try {
                    // 使用带中断检查的休眠
                    long startTime = System.currentTimeMillis();
                    while (System.currentTimeMillis() - startTime < backoffMs) {
                        if (!running) {
                            return;
                        }
                        Thread.sleep(Math.min(100, backoffMs)); // 分段休眠以便响应中断
                    }
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                    LogUtil.error(StringUtil.format("重试启动消息消费服务等待期间线程被中断：{0}", ex.getMessage()), ex);
                    return;
                }
                
                if (running) {
                    runWithRetry(retryCount + 1, nextBackoffMs);
                }
            }
        }
        
        private void closeConsumer() {
            if (consumer != null) {
                try {
                    consumer.close();
                    String key = groupId + ":" + topicId;
                    consumerMap.remove(key);
                    LogUtil.info(StringUtil.format("消息消费服务[{0}]已关闭", this.listener.getClass()));
                } catch (Exception e) {
                    LogUtil.error(StringUtil.format("关闭消费者失败：{0}", e.getMessage()), e);
                } finally {
                    shutdownLatch.countDown();
                }
            } else {
                shutdownLatch.countDown();
            }
        }
        
        public void shutdown() {
            synchronized (this) {
                running = false;
                this.notify();
            }
            
            try {
                // 等待消费者关闭完成，设置超时时间
                if (!shutdownLatch.await(10, TimeUnit.SECONDS)) {
                    LogUtil.warn(StringUtil.format("等待消费者[{0}:{1}]关闭超时", groupId, topicId));
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                LogUtil.error(StringUtil.format("等待消费者关闭时被中断：{0}", e.getMessage()));
            }
        }
        
        /**
         * 检查消费者是否成功启动
         * @return 是否成功启动
         */
        public boolean isStartupSuccess() {
            return startupSuccess;
        }
    }
    
    /**
     * 关闭资源
     */
    public void shutdown() {
        try {
            LogUtil.info("开始关闭RocketMQ消费者资源...");
            
            // 关闭所有消费者线程
            for (Map.Entry<String, ConsumerRunnable> entry : consumerRunnables.entrySet()) {
                try {
                    String key = entry.getKey();
                    ConsumerRunnable runnable = entry.getValue();
                    
                    if (runnable != null) {
                        LogUtil.info(StringUtil.format("正在关闭消费者线程：{0}", key));
                        runnable.shutdown();
                    }
                } catch (Exception e) {
                    LogUtil.error(StringUtil.format("关闭消费者线程失败：{0}", e.getMessage()), e);
                }
            }
            
            // 关闭线程池
            consumerExecutor.shutdown();
            try {
                // 等待所有任务完成
                if (!consumerExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    // 强制关闭未完成的任务
                    consumerExecutor.shutdownNow();
                    // 再次等待所有任务响应中断
                    if (!consumerExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                        LogUtil.error("线程池未能完全关闭");
                    }
                }
            } catch (InterruptedException e) {
                // 重新设置中断标志
                Thread.currentThread().interrupt();
                // 强制关闭
                consumerExecutor.shutdownNow();
                LogUtil.error(StringUtil.format("关闭线程池时被中断：{0}", e.getMessage()), e);
            }
            
            // 关闭所有消费者实例（安全检查）
            for (Map.Entry<String, PushConsumer> entry : consumerMap.entrySet()) {
                try {
                    String key = entry.getKey();
                    PushConsumer consumer = entry.getValue();
                    
                    if (consumer != null) {
                        consumer.close();
                        LogUtil.info(StringUtil.format("已关闭消费者：{0}", key));
                    }
                } catch (Exception e) {
                    LogUtil.error(StringUtil.format("关闭消费者失败：{0}", e.getMessage()), e);
                }
            }
            
            // 清空集合
            consumerRunnables.clear();
            consumerMap.clear();
            
            LogUtil.info("RocketMQ消费者资源已全部关闭");
        } catch (Exception e) {
            LogUtil.error(StringUtil.format("关闭RocketMQ消费者资源失败：{0}", e.getMessage()), e);
        }
    }
    
    /**
     * 检查消费者健康状态
     * 
     * @return 是否健康
     */
    public boolean isHealthy() {
        // 检查线程池和消费者状态
        if (consumerExecutor.isShutdown() || consumerMap.isEmpty()) {
            return false;
        }
        
        // 检查是否有任何消费者成功启动
        for (ConsumerRunnable runnable : consumerRunnables.values()) {
            if (runnable.isStartupSuccess()) {
                return true;
            }
        }
        
        // 如果有消费者但都未成功启动，则返回false
        return consumerRunnables.isEmpty();
    }
    
    /**
     * 获取消费者详情
     * 
     * @return 消费者详情
     */
    public Map<String, Object> getConsumerDetails() {
        Map<String, Object> details = new HashMap<>();
        details.put("activeConsumers", consumerMap.size());
        details.put("threadPoolActiveCount", ((ThreadPoolExecutor) consumerExecutor).getActiveCount());
        details.put("threadPoolQueueSize", ((ThreadPoolExecutor) consumerExecutor).getQueue().size());
        return details;
    }
} 