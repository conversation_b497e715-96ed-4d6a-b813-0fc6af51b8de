package com.wb.interact;

import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map.Entry;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.json.JSONArray;
import org.json.JSONObject;

import com.wb.common.Base;
import com.wb.util.DbUtil;
import com.wb.util.JsonUtil;
import com.wb.util.SortUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;
import com.wb.util.WebUtil;

public class DBE {
	/**
	 * 获取数据库表树数据。
	 */
	public static void getTree(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String type = request.getParameter("type");
		String jndi = request.getParameter("jndi");
		String schema = request.getParameter("schema");
		String result;
		if ("db".equals(type)) {
			result = getSchemaList(jndi, null);
			//如果没有Schema直接返回表列表
			if (result == null)
				result = getTableList(jndi, null, null);
		} else {
			if ("schema".equals(type))
				result = getTableList(jndi, schema, null);
			else
				result = getDbList();
		}
		WebUtil.send(response, result);
	}

	/**
	 * 如果用户包含演示角色且非管理员，SQL语句仅允许执行select * from table，否则抛出异常。
	 */
	public static void checkSelectSql(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String sql = request.getParameter("sql").toUpperCase();
		String[] roles = (String[]) WebUtil.fetchObject(request, "sys.roles");
		if ((StringUtil.indexOf(roles, "demo") != -1) && (StringUtil.indexOf(roles, "admin") == -1)
				&& ((!sql.startsWith("SELECT * FROM "))
						|| (!StringUtil.checkName(StringUtil.replaceAll(sql.substring(14), ".", "a")))
						|| (sql.endsWith("WB_USER")) || (sql.endsWith("WB_SN")) || (sql.endsWith("WB_SYS1"))
						|| (sql.endsWith("WB_SYS2"))))
			SysUtil.accessDenied(request);
	}

	/**
	 * 从变量sys.jndi节点获得所有配置的jndi，并生成树组件脚本。
	 */
	public static String getDbList() throws Exception {
		JSONObject config = JsonUtil.readObject(new File(Base.path, "wb/system/var.json"));
		HashMap<String, Object> map = new HashMap<String, Object>();

		config = config.optJSONObject("sys").optJSONObject("jndi");
		Set<Entry<String, Object>> es = config.entrySet();

		JSONArray ja = new JSONArray();

		// 默认jndi，插入到首行
		config.remove("default");
		for (Entry<String, Object> e : es) {
			String key = (String) e.getKey();
			map.put(key, ((JSONArray) e.getValue()).optString(0));
		}
		ArrayList<Entry<String, Object>> sortedItems = SortUtil.sortKey(map);
		JSONObject jo = new JSONObject();
		jo.put("text", "default");
		jo.put("jndi", "default");
		jo.put("type", "db");
		jo.put("glyph", "f1c0");
		ja.put(jo);

		for (Entry<String, Object> e : sortedItems) {
			jo = new JSONObject();
			jo.put("text", e.getKey());
			jo.put("jndi", e.getKey());
			jo.put("type", "db");
			jo.put("glyph", "f1c0");
			ja.put(jo);
		}
		return ja.toString();
	}

	/**
	 * 获取指定jndi所有数据库表JSON脚本。
	 * @param jndi jndi名称。
	 * @param schema 表Schema。
	 * @param tables 已经配置的表定义。
	 * @return 表列表JSON脚本。 
	 */
	public static String getTableList(String jndi, String schema, HashSet<String> tables) throws Exception {
		Connection conn = null;
		ResultSet rs = null;
		boolean isFirst = true;
		boolean hasTableDefine = tables != null;
		String[] types = { "TABLE" };
		String jndiText = StringUtil.quote(jndi);
		StringBuilder buf = new StringBuilder();

		HashMap<String, String> tableMap = new HashMap<String, String>();
		try {
			conn = DbUtil.getConnection(jndi);
			rs = conn.getMetaData().getTables(null, schema, null, types);
			while (rs.next()) {
				String tableSchema = StringUtil.opt(rs.getString(2));
				String tableName = rs.getString(3);
				tableMap.put(tableName, tableSchema);
			}
			ArrayList<Entry<String, String>> sortedEntries = SortUtil.sortKey(tableMap);
			buf.append('[');
			for (Entry<String, String> entry : sortedEntries) {
				if (isFirst)
					isFirst = false;
				else
					buf.append(',');
				String tableName = (String) entry.getKey();
				String tableText = StringUtil.quote(tableName);
				String tableSchema = StringUtil.quote((String) entry.getValue());
				buf.append("{\"text\":");
				buf.append(tableText);
				buf.append(",\"type\":\"table\",\"table\":");
				buf.append(tableText);
				buf.append(",\"schema\":");
				buf.append(tableSchema);
				buf.append(",\"jndi\":");
				buf.append(jndiText);
				buf.append(",\"leaf\":true,\"iconCls\":\"");
				String upperTableName = tableName.toUpperCase();
				if ((hasTableDefine) && (tables.contains(upperTableName))) {
					tables.remove(upperTableName);
					buf.append("f00c\"}");
				} else {
					buf.append("f0ce\"}");
				}
			}

			if ((hasTableDefine) && (schema == null)) {
				for (String fullName : tables) {
					fullName = StringUtil.quote(fullName);
					buf.append(",{\"text\":");
					buf.append(fullName);
					buf.append(",\"type\":\"table\",\"table\":");
					buf.append(fullName);
					buf.append(",\"schema\":\"\",\"jndi\":");
					buf.append(jndiText);
					buf.append(",\"leaf\":true,\"glyph\":\"f0ce\"}");
				}
			}
			buf.append(']');
			return buf.toString();
		} finally {
			DbUtil.close(rs);
			DbUtil.close(conn);
		}
	}

	/**
	 * 获取指定jndi所有Schema列表。
	 * @param jndi jndi名称。
	 * @param tables 已经配置的表定义。
	 * @return Schema列表JSON脚本。 如果没有Schema返回null。
	 */
	public static String getSchemaList(String jndi, HashSet<String> tables) throws Exception {
		Connection conn = null;
		ResultSet rs = null;
		String[] types = { "TABLE" };
		String jndiText = StringUtil.quote(jndi);
		StringBuilder buf = new StringBuilder();
		HashMap<String, Boolean> schemaMap = new HashMap<String, Boolean>();

		boolean isFirst = true;
		boolean hasTableDefine = tables != null;
		try {
			conn = DbUtil.getConnection(jndi);
			rs = conn.getMetaData().getTables(null, null, null, types);
			while (rs.next()) {
				String schema = StringUtil.opt(rs.getString(2));
				String tableName = rs.getString(3);
				String upperTableName = tableName.toUpperCase();
				schemaMap.put(schema, Boolean.valueOf(true));
				if ((hasTableDefine) && (tables.contains(upperTableName))) {
					tables.remove(upperTableName);
				}
			}
			//如果不包含Schema返回null
			if ((schemaMap.isEmpty()) || ((schemaMap.size() == 1) && (schemaMap.containsKey(""))))
				return null;
			ArrayList<Entry<String, Boolean>> entryList = SortUtil.sortKey(schemaMap);
			buf.append('[');
			for (Entry<String, Boolean> entry : entryList) {
				String schema = StringUtil.quote((String) entry.getKey());
				if (isFirst)
					isFirst = false;
				else
					buf.append(',');
				buf.append("{\"text\":");
				buf.append(schema);
				buf.append(",\"jndi\":");
				buf.append(jndiText);
				buf.append(",\"schema\":");
				buf.append(schema);
				buf.append(",\"type\":\"schema\",\"iconCls\":\"db_form_icon\"}");
			}
			// 不匹配的表添加到最后
			if (hasTableDefine) {
				for (String fullName : tables) {
					fullName = StringUtil.quote(fullName);
					buf.append(",{\"text\":");
					buf.append(fullName);
					buf.append(",\"type\":\"table\",\"table\":");
					buf.append(fullName);
					buf.append(",\"schema\":\"\",\"jndi\":");
					buf.append(jndiText);
					buf.append(",\"leaf\":true,\"iconCls\":\"table_add_icon\"}");
				}
			}
			buf.append(']');
			return buf.toString();
		} finally {
			DbUtil.close(rs);
			DbUtil.close(conn);
		}
	}

	/**
	 * 从指定表下载二进制字段内容。
	 */
	public static void downloadBlob(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String jndi = request.getParameter("__jndi");
		String tableName = request.getParameter("__tableName");
		String fieldName = request.getParameter("__fieldName");
		String selectSql = DbUtil.buildSQLs(jndi, tableName, false, 1, null, new JSONObject().put(fieldName, 1), null,
				null)[3];
		ResultSet rs = (ResultSet) DbUtil.run(request, selectSql, jndi);
		DbUtil.outputBlob(rs, request, response, "download");
	}

	/**
	 * 上传文件数据至指定数据库表二进制字段。
	 */
	public static void uploadBlob(HttpServletRequest request, HttpServletResponse response) throws Exception {
		setBlob(request, false);
	}

	/**
	 * 上传文件数据至指定数据库表二进制字段。
	 */
	public static void clearBlob(HttpServletRequest request, HttpServletResponse response) throws Exception {
		setBlob(request, true);
	}

	/**
	 * 上传文件数据至指定数据库表二进制字段或清除该字段。
	 * @param isClear 是否清除二进制字段，true清除，false更新。
	 */
	private static void setBlob(HttpServletRequest request, boolean isClear) throws Exception {
		String jndi = WebUtil.fetch(request, "__jndi");
		String tableName = WebUtil.fetch(request, "__tableName");
		String fieldName = WebUtil.fetch(request, "__fieldName");

		if (isClear)
			request.setAttribute(fieldName, "");
		else
			request.setAttribute(fieldName, request.getAttribute("file"));
		String updateSql = DbUtil.buildSQLs(jndi, tableName, false, 1, null, new JSONObject().put(fieldName, 1), null,
				null)[1];
		DbUtil.run(request, updateSql, jndi);
	}
}