<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/wb/libs/layui/css/layui.css">
</head>
<body>
<div class="  ">
    <input type="hidden" id="data_field" value=""> 
    <table class="layui-hide" id="demo"></table>
</div>
<script src="/wb/libs/layui/layui.js"></script>
    <script>
layui.use(['table','jquery'], function(){
  var table = layui.table;
  var $ = layui.jquery;
  var data = JSON.parse($('#data_field').val()) ;
  //展示已知数据
  table.render({
    elem: '#demo'
    ,skin: 'line' //行边框风格
    ,cols: [[ //标题栏
      {field: 'prize_name', title: '奖项名称', width: 120}
      ,{field: 'record_date', title: '抽奖时间', minWidth: 150}
    ]]
    ,data:data
    ,even: true
  });
});

    </script>
</body>
</html>