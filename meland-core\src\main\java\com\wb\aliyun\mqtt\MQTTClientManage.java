package com.wb.aliyun.mqtt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.mqtt.server.ServerConsumer;
import com.alibaba.mqtt.server.callback.StatusListener;
import com.alibaba.mqtt.server.config.ChannelConfig;
import com.alibaba.mqtt.server.config.ConsumerConfig;
import com.alibaba.mqtt.server.model.StatusNotice;
import com.alibaba.mqtt.server.model.StatusType;
import com.aliyun.onsmqtt20200420.Client;
import com.aliyun.onsmqtt20200420.models.QuerySessionByClientIdRequest;
import com.aliyun.onsmqtt20200420.models.QuerySessionByClientIdResponse;
import com.aliyun.onsmqtt20200420.models.QuerySessionByClientIdResponseBody;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;
import com.google.gson.JsonObject;
import com.wb.cache.RedisCache;
import com.wb.common.Base;
import com.wb.common.Str;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.IOException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @date 2024/6/14-14:55
 */
public class MQTTClientManage {
    private static Client client;
    private static ServerConsumer serverConsumer;

    private String accessKeyId;
    private String accessKeySecret;
    private String endPoint;//消息公网
    private String endPointCredential;//凭证公网
    private String instanceId;

    public MQTTClientManage(String AccessKeyId, String AccessKeySecret, String endPoint, String endPointCredential, String instanceId) {
        this.accessKeyId = AccessKeyId;
        this.accessKeySecret = AccessKeySecret;
        this.endPoint = endPoint;
        this.endPointCredential = endPointCredential;
        this.instanceId = instanceId;
    }

    public Client createClientIdManager() throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        config.endpoint = endPointCredential;
        if (client == null) {
            client = new Client(config);
        }
        return client;
    }

    /**
     * 根据 clientId 查询客户端的是否在线
     *
     * @param clientId
     * @return
     * @throws Exception
     */
    public Boolean querySessionByClientId(String clientId) {
        QuerySessionByClientIdRequest querySessionByClientIdRequest = new QuerySessionByClientIdRequest()
                .setClientId(clientId)
                .setInstanceId(instanceId);
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            QuerySessionByClientIdResponse response = client.querySessionByClientIdWithOptions(querySessionByClientIdRequest, runtime);
            QuerySessionByClientIdResponseBody body = response.getBody();
            Boolean onlineStatus = body.getOnlineStatus();
            return onlineStatus;

        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            Common.assertAsString(error.message);
        }
        return false;
    }


    /**
     * 创建客户端状态监听器
     */
    public void createClientListener() {
        ChannelConfig channelConfig = new ChannelConfig();
        channelConfig.setDomain(endPoint);
        channelConfig.setPort(5672);
        channelConfig.setInstanceId(instanceId);
        channelConfig.setAccessKey(accessKeyId);
        channelConfig.setSecretKey(accessKeySecret);
        if (serverConsumer == null) {
            serverConsumer = new ServerConsumer(channelConfig, new ConsumerConfig());
        }
        System.out.println("serverConsumer创建: " + serverConsumer);

    }

    /**
     * 客户端状态开始监听
     *
     * @param mqttGroupId
     * @throws IOException
     * @throws TimeoutException
     */
    public void subscribeStatus(String mqttGroupId) throws IOException, TimeoutException {
        serverConsumer.start();
        serverConsumer.subscribeStatus(mqttGroupId, new StatusListener() {
            @Override
            public void process(StatusNotice statusNotice) {
                Long time = statusNotice.getTime();
                String key = statusNotice.getClientId() + "-status";
                String value = statusNotice.getStatusType().toString() + "-" + time;
                String channelId = statusNotice.getChannelId();
                String clientIp = statusNotice.getClientIp();
//                System.out.println("key=" + key +
//                        " value=" + value +
//                        " channelId=" + channelId +
//                        " clientIp=" + clientIp +
//                        " time=" + time);

                RedisCache redisCache = Base.map;
                RedissonClient redissonClient = redisCache.getRedissonClient();
                StringRedisTemplate stringRedisTemplate = redisCache.getStringRedisTemplate();
                //添加分布式锁
                RLock lock = redissonClient.getLock(key);
                try {
                    //判断key是否存在
                    Boolean hasKey = stringRedisTemplate.hasKey(key);
                    if (hasKey) {
                        //如果已经存在key,则需要对比时间戳, 将最新的状态更新到redis里面
                        String s = stringRedisTemplate.opsForValue().get(key);
                            //获取value里的的时间戳
                            JSONObject json = JSON.parseObject(s);
                            String value1 = json.get("value").toString();
                            Long redisTime = Long.parseLong(value1.split("-")[1]);

                            //如果当前消息时间>redis中消息的时间, 则更新redis中消息状态
                            if (time > redisTime) {
                                //将数据添加到redis中, 保存10个小时
                                redisCache.put(key, value, 36000000L);
                            }

                    } else {
                        //将数据添加到redis中, 保存10个小时
                        redisCache.put(key, value, 36000000L);
                    }
                } catch (Exception e) {
                    System.out.println(e);
                } finally {
                    lock.unlock();
                }

            }
        });
    }

    /**
     * 客户端状态监听器停止监听
     *
     * @throws IOException
     */
    public void stop() throws IOException {
        System.out.println("serverConsumer停止: " + serverConsumer);
        serverConsumer.stop();
    }
}
