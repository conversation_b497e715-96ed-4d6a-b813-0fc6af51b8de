package com.wb.message.handler;

import java.util.Date;
import java.util.List;

import org.json.JSONArray;
import org.json.JSONObject;

import com.wb.common.Var;
import com.wb.message.Handler;
import com.wb.message.util.ParamUtil;
import com.wb.openplatform.enterprise.util.WeiXinParamesUtil;
import com.wb.openplatform.enterprise.util.WeiXinUtil;
import com.wb.util.DateUtil;
import com.wb.util.DbUtil;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;

public class HandlerSmall extends Handler {

	public HandlerSmall(String title, String content, String users, String data, JSONObject arg) {
		super(title, content, users, data, arg);
	}

	// 接收者的用户列长度
	private static final Integer PAGE_SIZE = 1000;
	private static final String url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=ACCESS_TOKEN";

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public void handlerMessage() {
		// 存放所有用户
		String user = StringUtil.joinQuote(this.users);
		if (user != "") {
			// 查询用户的钉钉ID
			String sql = "select ifnull(qy_id,qysmall_id) as qy_id from  wb_user_weixin where user_id in (" + user
					+ ")  and (qy_id <> '' or qysmall_id <> '') ";

			JSONArray array = DbUtil.query(sql);

			// 总记录数
			Integer totalCount = array.length();
			if (totalCount <= 0) {
				LogUtil.warn(StringUtil.format("消息[{0}] 微信小程序----没有用户", title));
				System.err.println("微信小程序----没有用户");
				return;
			}
			// 分多少次处理
			Integer requestCount = totalCount / PAGE_SIZE;
			for (int i = 0; i <= requestCount; i++) {
				Integer fromIndex = i * PAGE_SIZE;
				// 如果总数少于PAGE_SIZE,为了防止数组越界,toIndex直接使用totalCount即可
				int toIndex = Math.min(totalCount, (i + 1) * PAGE_SIZE);
				List subList = array.toList().subList(fromIndex, toIndex);
				String userList = String.join("|", subList);
				sendMessage(userList);
				// 总数不到一页或者刚好等于一页的时候,只需要处理一次就可以退出for循环了
				if (toIndex == totalCount) {
					break;
				}
			}
		} else {
			LogUtil.warn(StringUtil.format("消息[{0}] 微信小程序----没有用户", title));
			System.err.println("微信小程序----没有用户");
		}
	}

	/**
	 * 发送消息
	 * 
	 * @param users
	 */
	public void sendMessage(String users) {
		try {
			// 获取accessToken
			String accessToken = WeiXinUtil
					.getAccessToken(WeiXinParamesUtil.corpId, Var.getString("sys.config.smallProgram.Secret"), "Small")
					.getToken();

			// 重写参数
			JSONObject Arg_ = new JSONObject(this.arg.toString());
			JSONObject param = new JSONObject(this.data);
			System.err.println(Arg_);
			JSONArray array = ParamUtil.getSmallParams(param, Arg_, title);
			if (array == null) {
				LogUtil.warn(StringUtil.format("消息[{0}] [content_item]为空，无法发送小程序通知", title));
				return;
			}

			// 加粗第一条数据
			boolean emphasis_first = false;
			// 小程序參數
			if (param.has("bold"))
				emphasis_first = param.getBoolean("bold");
			// 判断重写参数是否存在 不存在使用默认参数 存在使用重写参数
			if (StringUtil.isEmpty(param.optString("path"))) {
				param.put("path", StringUtil.isEmpty(param.optString("path")) ? Arg_.getString("path") : "");
			}
			// 创建JSON参数
			JSONObject obj = new JSONObject();
			obj.put("touser", users);
			obj.put("toparty", "");
			obj.put("totag", "");
			obj.put("msgtype", "miniprogram_notice");
			JSONObject not = new JSONObject();
			not.put("appid", Var.getString("sys.config.smallProgram.AppID"));
			not.put("page", param.get("path"));
			not.put("title", this.title);
			not.put("description", DateUtil.format(new Date(), "MM月dd日 HH:mm"));
			not.put("emphasis_first_item", emphasis_first);
			not.put("content_item", array);
			obj.put("miniprogram_notice", not);

			System.err.println(obj);
			// 判断消息长度是否合法
			if (Strlength(this.title) < 4) {
				LogUtil.warn(StringUtil.format("消息[{0}] [title]长度不合法 --> 规定长度【4~12】，无法发送小程序通知", title));
				return;
			}
			if (Strlength(this.title) > 12) {
				LogUtil.warn(StringUtil.format("消息[{0}] [title]长度不合法 --> 规定长度【4~12】，已自动截取", title));
				this.title = this.title.replace(this.title.substring(9, this.title.length() - 1), "...");
			}
			if (array.length() > 10 || array.length() < 1) {
				LogUtil.warn(StringUtil.format("消息[{0}] 消息内容键值对 长度不合法 --> 规定长度【1~10】，无法发送小程序通知", title));
				return;
			}
			for (int i = 0; i < array.length(); i++) {
				JSONObject ar = array.getJSONObject(i);
				if (Strlength(ar.getString("key")) > 10 || Strlength(ar.getString("key")) < 1) {
					LogUtil.warn(StringUtil.format("消息[{0}] 消息内容键值对 key[{1}] 长度不合法 --> 规定长度【1~10】，已自动截取", title,
							ar.getString("key")));
					array.getJSONObject(i).put("key", ar.getString("key").substring(0, 10));
				} else if (Strlength(ar.getString("value")) > 30 || Strlength(ar.getString("value")) < 1) {
					LogUtil.warn(StringUtil.format("消息[{0}] 消息内容键值对 value[{1}] 长度不合法 --> 规定长度【1~30】，已自动截取", title,
							ar.getString("value")));
					array.getJSONObject(i).put("value", ar.getString("value").substring(0, 30));
				}
			}
			// 发送地址
			String msgUrl = url.replace("ACCESS_TOKEN", accessToken);
			JSONObject jsonObject = WeiXinUtil.httpRequest(msgUrl, "POST", obj.toString());
			if (jsonObject.has("errcode")) {
				if (0 != jsonObject.getInt("errcode")) {
					LogUtil.warn(StringUtil.format("消息[{0}] 微信小程序----发送失败,错误代码【{1}】", title,
							jsonObject.getInt("errcode")));
					System.err.println("微信小程序----发送失败,错误代码【" + jsonObject.getInt("errcode") + "】");
				} else {
					LogUtil.warn(StringUtil.format("消息[{0}] 微信小程序----发送成功", title));
					System.out.println("微信小程序----发送成功");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static int Strlength(String value) {
		Double valueLength = 0.0;
		String chinese = "[\u4e00-\u9fa5]";
		String sz = "^[0-9]*$";
		for (int i = 0; i < value.length(); i++) {
			String temp = value.substring(i, i + 1);
			if (temp.matches(chinese)) {
				valueLength += 1;
			} else if (temp.matches(sz)) {
				valueLength += 1;
			} else {
				valueLength += 0.5;
			}
		}
		return (int) Math.ceil(valueLength);
	}
}
