package com.wb.interact;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wb.common.Base;
import com.wb.util.LogUtil;

/**
 * 下载任务管理器
 * 负责管理下载任务的生命周期，包括取消、清理资源等
 */
public class DownloadTaskManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(DownloadTaskManager.class);
    
    // 引用FilePush中的常量和变量
    private static final int DOWNLOAD_EXPIRE_HOURS = FilePush.getDownloadExpireHours();
    
    /**
     * 取消下载任务
     *
     * @param downloadId 下载任务ID
     */
    public static void cancelTask(String downloadId) {
        try {
            // 设置取消标志
            FilePush.setCancelledTask(downloadId, true);
            try {
                Base.map.setValue(DownloadTaskKeyManager.getDownloadCancelledKey(downloadId), "true",
                        DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);
            } catch (Exception e) {
                LogUtil.warn("Redis保存取消标志失败: " + e.getMessage());
            }

            // 取消Future任务
            Future<?> future = FilePush.getTaskFuture(downloadId);
            if (future != null && !future.isDone() && !future.isCancelled()) {
                future.cancel(true);
                LOGGER.info("Future任务已取消：" + downloadId);
            }

            // 添加强制停止机制
            Thread thread = FilePush.getProcessingThread(downloadId);
            if (thread != null && thread.isAlive()) {
                thread.interrupt(); // 先尝试正常中断

                // 使用异步方式监控线程是否响应中断
                CompletableFuture.runAsync(() -> {
                    try {
                        // 等待5秒让线程正常响应中断
                        for (int i = 0; i < 50; i++) {
                            if (!thread.isAlive()) {
                                return; // 线程已正常退出
                            }
                            Thread.sleep(100);
                        }

                        // 5秒后线程仍然运行，尝试强制停止
                        if (thread.isAlive()) {
                            LogUtil.warn("线程未能响应中断信号，记录为僵尸线程: " + downloadId);

                            // 不再使用Thread.stop()方法，而是使用标记和记录
                            // 将线程标记为异常状态，但不强制终止
                            // Base.map.setValue(DownloadTaskKeyManager.getDownloadTaskKey(downloadId) + "_zombie",
                            //         "true", DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);

                            // 从处理线程集合中移除，避免持续引用
                            FilePush.removeProcessingThread(downloadId);

                            // 确保信号量被释放
                            try {
                                FilePush.releaseDownloadSemaphore(downloadId);
                            } catch (Exception e) {
                                // 忽略异常，确保流程继续
                            }

                            // 添加任务到死锁检测器中
                            FilePush.addThreadToDeadlockMonitor(downloadId, thread);
                        }
                    } catch (Exception e) {
                        LogUtil.error("监控线程中断状态时出错: " + e.getMessage());
                    }
                }, FilePush.getTaskExecutor());
            }

            // 清理任务资源
            cleanupTaskResources(downloadId, true);
        } catch (Exception e) {
            LogUtil.error("取消任务时出错: " + e.getMessage());
        }
    }

    /**
     * 清理下载任务资源 (Refactored to delegate to FilePush)
     *
     * @param downloadId 下载任务ID
     * @param removeCancelledFlag 是否移除取消标志
     */
    public static void cleanupTaskResources(String downloadId, boolean removeCancelledFlag) {
        // Delegate the actual cleanup to FilePush.cleanupDownloadTask
        // which now handles local cache, Redis keys, Set/ZSet entries, and potentially files.
        FilePush.cleanupDownloadTask(downloadId, removeCancelledFlag);
        // The LOGGER.info previously here is now effectively inside FilePush.cleanupDownloadTask (or should be if needed)
    }
} 