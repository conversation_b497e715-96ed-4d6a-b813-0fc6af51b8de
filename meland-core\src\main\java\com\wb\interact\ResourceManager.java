package com.wb.interact;

import com.wb.common.Base;
import com.wb.common.Session;
import com.wb.common.Str;
import com.wb.common.Var;
import com.wb.util.DbUtil;
import com.wb.util.FileUtil;
import com.wb.util.JsonUtil;
import com.wb.util.SortUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;
import com.wb.util.WebUtil;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.json.JSONArray;
import org.json.JSONObject;

/**
 * 管理系统资源的类，提供了一些方法来下载、显示、保存资源文件。
 */
public class ResourceManager {
    /**
     * 基础路径，存放系统资源的根目录。
     */
    public static final File basePath = new File(Base.path, "wb/system/resource");
    /**
     * 资源文件的扩展名列表。
     */
    private static final String[] resourceExt = Var.getString("sys.file.resourceFiles").split(",");

    /**
     * 下载指定路径的文件或目录。
     *
     * @param request  请求对象
     * @param response 响应对象
     * @throws Exception 下载受限制的文件或读取文件发生异常。
     */
    public static void getFileList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        File path = new File(request.getParameter("path"));
        int type = Integer.parseInt(request.getParameter("type"));
        boolean isTree = type == 1;

        if (!FileUtil.isAncestor(basePath, path))
            SysUtil.accessDenied(request);
        File[] files = FileUtil.listFiles(path);
        if (isTree) {
            SortUtil.sort(files);
        } else {
            String[] sortInfo = WebUtil.getSortInfo(request);
            String[] fields = new String[]{"text", "size", "type", "date"};
            SortUtil.sort(files, StringUtil.indexOf(fields, sortInfo[0]), sortInfo[1].equalsIgnoreCase("desc"));
        }
        JSONArray rows = new JSONArray();
        for (File file : files) {
            boolean isDir = file.isDirectory();
            if (((!isTree) || (isDir)) && (canDisplay(request, file))) {
                String dirPath = FileUtil.getPath(file);
                JSONObject item = new JSONObject();
                item.put("text", StringUtil.select(new String[]{file.getName(), dirPath}));
                item.put("path", dirPath);
                item.put("leaf", !isDir);
                if (isTree) {
                    if (isLeafFolder(request, file))
                        item.put("children", new JSONArray());
                } else {
                    item.put("icon", WebUtil.encode(dirPath));
                    item.put("date", new Date(file.lastModified()));
                    item.put("size", file.length());
                    item.put("type", isDir ? Str.format(request, "folder", new Object[0]) : FileUtil.getFileType(file));
                }
                rows.put(item);
            }
        }
        WebUtil.send(response, new JSONObject().put(isTree ? "children" : "rows", rows));
    }

    /**
     * 判断指定文件是否可以显示。
     *
     * @param request  请求对象
     * @param response 响应对象
     * @throws Exception 下载受限制的文件或读取文件发生异常。
     */
    public static void getPerm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        File file = new File(basePath, request.getParameter("fileName"));
        if (!FileUtil.isAncestor(basePath, file)) {
            SysUtil.accessDenied(request);
        }
        String[] types = {"get", "read", "write", "execute"};

        JSONObject content = JsonUtil.readObject(file);
        JSONObject rolePerms = (JSONObject) content.opt("roles");
        JSONObject result;
        if (rolePerms == null) {
            result = new JSONObject();
        } else {
            checkPerm(request, content, "get");
            HashMap<String, String> roleMap = DbUtil.getMap("select ROLE_ID,ROLE_NAME from WB_ROLE");
            for (String type : types) {
                JSONArray roles = rolePerms.optJSONArray(type);
                if (roles != null) {
                    JSONArray roleNames = new JSONArray();
                    int j = roles.length();
                    for (int i = 0; i < j; i++) {
                        String roleName = (String) roleMap.get(roles.getString(i));
                        if (roleName != null)
                            roleNames.put(roleName);
                    }
                    rolePerms.put(type + "RoleCombo", roleNames);
                }
            }
            result = rolePerms;
        }
        WebUtil.send(response, result);
    }

    /**
     * 判断用户是否有执行指定类型权限的权限。
     *
     * @param request 请求对象
     * @param content 文件内容
     * @param type    权限类型
     * @throws Exception 下载受限制的文件或读取文件发生异常。
     */
    private static boolean hasPerm(HttpServletRequest request, JSONObject content, String type) throws Exception {
        JSONObject permRoles = content.optJSONObject("roles");
        if (permRoles == null)
            return true;
        JSONArray roles = permRoles.optJSONArray(type);
        if (roles == null)
            return true;
        boolean hasPerm = false;

        String[] userRoles = Session.getRoles(request);

        if (userRoles == null)
            return false;
        if (StringUtil.indexOf(userRoles, "admin") != -1)
            return true;
        HashSet<String> hs = JsonUtil.toHashSet(roles);
        for (String role : userRoles) {
            if (hs.contains(role)) {
                hasPerm = true;
                break;
            }
        }
        return hasPerm;
    }

    /**
     * 检查用户是否有执行指定类型权限的权限，如果没有则拒绝访问。
     *
     * @param request 请求对象
     * @param content 文件内容
     * @param type    权限类型
     * @throws Exception 下载受限制的文件或读取文件发生异常。
     */
    private static void checkPerm(HttpServletRequest request, JSONObject content, String type) throws Exception {
        if (!hasPerm(request, content, type))
            SysUtil.accessDenied(request);
    }

    /**
     * 保存资源文件。
     *
     * @param request  请求对象
     * @param response 响应对象
     * @throws Exception 下载受限制的文件或读取文件发生异常。
     */
    public static void saveResource(HttpServletRequest request, HttpServletResponse response) throws Exception {
        JSONArray array = new JSONArray(request.getParameter("data"));

        int j = array.length();

        for (int i = 0; i < j; i++) {
            JSONObject object = array.getJSONObject(i);
            saveFile(request, new File(object.getString("file")), object.getJSONObject("data"), true);
        }
    }

    /**
     * 打开指定的资源文件。
     *
     * @param request  请求对象
     * @param response 响应对象
     * @throws Exception 下载受限制的文件或读取文件发生异常。
     */
    public static void openResource(HttpServletRequest request, HttpServletResponse response) throws Exception {
        JSONArray result = new JSONArray();
        JSONArray items = new JSONArray(request.getParameter("filenames"));

        int j = items.length();

        for (int i = 0; i < j; i++) {
            String filename = items.getString(i);
            File file = new File(filename);
            if (!file.exists())
                String.format("文件 “%s” 不存在。", new Object[]{file.getName()});
            if (!FileUtil.isAncestor(basePath, file))
                SysUtil.accessDenied(request);
            String fileData = FileUtil.readString(file);
            checkPerm(request, new JSONObject(fileData), "read");
            JSONObject content = new JSONObject();
            content.put("file", FileUtil.getPath(file));
            content.put("data", fileData);
            result.put(content);
        }
        WebUtil.send(response, result);
    }

    /**
     * 设置指定文件的权限信息。
     *
     * @param request  请求对象
     * @param response 响应对象
     * @throws Exception 下载受限制的文件或读取文件发生异常。
     */
    public static void setPerm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        JSONArray fileNames = new JSONArray(request.getParameter("fileNames"));
        JSONObject perms = new JSONObject(request.getParameter("perms"));
        int j = fileNames.length();

        for (int i = 0; i < j; i++) {
            File file = new File(basePath, fileNames.getString(i));
            doSetPerm(request, file, perms);
        }
    }

    /**
     * 递归设置指定文件及其子目录中所有文件的权限信息。
     *
     * @param request 请求对象
     * @param file    文件或目录
     * @param perms   权限信息
     * @throws Exception 下载受限制的文件或读取文件发生异常。
     */
    private static void doSetPerm(HttpServletRequest request, File file, JSONObject perms) throws Exception {
        if (file.isDirectory()) {
            File[] fs = file.listFiles();
            for (File f : fs)
                doSetPerm(request, f, perms);
        } else {
            JSONObject content = JsonUtil.readObject(file);
            content.put("roles", perms);
            saveFile(request, file, content, false);
        }
    }

    /**
     * 获取执行指定资源文件的对象，并检查用户是否有执行权限。
     *
     * @param request 请求对象
     * @param file    资源文件
     * @return JSON对象
     * @throws Exception 下载受限制的文件或读取文件发生异常。
     */
    public static JSONObject getExecuteObject(HttpServletRequest request, File file) throws Exception {
        if (!FileUtil.isAncestor(basePath, file))
            SysUtil.accessDenied(request);
        JSONObject content = JsonUtil.readObject(file);
        checkPerm(request, content, "execute");
        return content;
    }

    /**
     * 保存指定资源文件，并检查用户是否有写入权限。
     *
     * @param request     请求对象
     * @param file        文件
     * @param content     文件内容
     * @param reservePerm 是否保留原有权限信息
     * @throws Exception 下载受限制的文件或读取文件发生异常。
     */
    private static void saveFile(HttpServletRequest request, File file, JSONObject content, boolean reservePerm)
            throws Exception {
        if (!FileUtil.isAncestor(basePath, file))
            SysUtil.accessDenied(request);
        if (file.exists()) {
            JSONObject oldContent = JsonUtil.readObject(file);
            checkPerm(request, oldContent, "write");
            if (reservePerm)
                content.put("roles", oldContent.opt("roles"));
        }
        FileUtil.syncSave(file, content.toString());
    }

    /**
     * 判断指定目录是否为叶子目录，即目录中是否含有子目录。
     *
     * @param request 请求对象
     * @param file    目录
     * @return 如果是叶子目录则返回true，否则返回false
     * @throws Exception 下载受限制的文件或读取文件发生异常。
     */
    private static boolean isLeafFolder(HttpServletRequest request, File file) throws Exception {
        File[] files = FileUtil.listFiles(file);
        for (File subFile : files) {
            if ((subFile.isDirectory()) && (canDisplay(request, subFile)))
                return false;
        }
        return true;
    }

    /**
     * 判断指定文件是否可以在资源管理器中显示。
     *
     * @param request 请求对象
     * @param file    文件或目录
     * @return 如果可以显示则返回true，否则返回false
     * @throws Exception 下载受限制的文件或读取文件发生异常。
     */
    private static boolean canDisplay(HttpServletRequest request, File file) throws Exception {
        if (file.isDirectory()) {
            File[] files = FileUtil.listFiles(file);
            for (File subFile : files)
                if (canDisplay(request, subFile))
                    return true;
        } else {
            String ext = FileUtil.getFileExt(file);
            if (StringUtil.indexOf(resourceExt, ext) == -1) {
                return true;
            }
            JSONObject content = JsonUtil.readObject(file);
            if (hasPerm(request, content, "get")) {
                return true;
            }
        }
        return false;
    }
}