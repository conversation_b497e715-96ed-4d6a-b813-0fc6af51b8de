package com.wb.openplatform.login;

import java.util.HashMap;

import org.json.JSONObject;

/**
 * 登录池
 * <AUTHOR>
 *
 */
public class LoginV {

	//存放
	private static HashMap<String, JSONObject> loginUserMap = new HashMap<String, JSONObject>();
	private static LoginV loginV;
	public static LoginV getVo(){
		if(loginV == null){
			loginV = new LoginV();
		}
		return loginV;
	}
	public static HashMap<String, JSONObject> getLoginUserMap() {
		return loginUserMap;
	}
}
