package com.wb.interact;

import com.wb.common.Base;
import com.wb.common.Var;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;

import java.io.File;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 下载任务清理器，负责清理过期的下载文件
 */
public class DownloadTaskCleaner {
    // 常量和配置
    // 文件保留时间（物理文件在任务完成后多久被删除）
    private static final int FILE_RETENTION_HOURS = 2;
    private static final int MAX_DELETE_RETRIES = 3;
    private static final long RETRY_DELAY_MS = 1000;

    // 不再需要独立的 lastAccessTimes
    // private static final ConcurrentHashMap<String, Long> lastAccessTimes = new ConcurrentHashMap<>();

    // 移除 set/getLastAccessTime 方法

    /**
     * 调度物理文件的延迟清理
     * 在指定时间后尝试删除生成的文件。
     *
     * @param downloadId 下载任务ID (用于日志和清理完成标记)
     * @param file 要清理的文件对象
     */
    public static void scheduleFileCleanup(final String downloadId, final File file) {
        if (downloadId == null || file == null) {
            LogUtil.warn(StringUtil.format("无效的文件清理任务参数: downloadId={0}, file={1}", downloadId, file));
            return;
        }

        String filename = file.getAbsolutePath();
        long delayMillis = FILE_RETENTION_HOURS * 60 * 60 * 1000L;

        // 使用 DownloadTaskExecutor 调度延迟任务
        DownloadTaskExecutor.schedule(() -> {
            try {
                if (Var.debug) {
                    LogUtil.info(StringUtil.format("执行延迟文件清理: downloadId={0}, file={1}", downloadId, filename));
                }

                // 检查文件是否存在并尝试删除
                if (file.exists()) {
                    boolean deleted = deleteFileWithRetry(file);
                    if (deleted) {
                        if (Var.debug) {
                            LogUtil.info(StringUtil.format("成功清理下载文件: downloadId={0}, file={1}", downloadId, filename));
                        }
                        // 文件成功删除后，清理 Redis 中的 _completed 标记 (REMOVED)
                        // try {
                        //     Base.map.delKey(DownloadTaskKeyManager.getDownloadCompletedKey(downloadId));
                        //     if (Var.debug) {
                        //         LogUtil.info(StringUtil.format("已清理 Redis _completed 标记: downloadId={0}", downloadId));
                        //     }
                        // } catch (Exception e) {
                        //     LogUtil.warn(StringUtil.format("清理 Redis _completed 标记失败: downloadId={0}, error={1}",
                        //             downloadId, e.getMessage()));
                        // }
                    } else {
                        LogUtil.warn(StringUtil.format("无法清理下载文件（可能是文件仍被占用）: downloadId={0}, file={1}", downloadId, filename));
                        // 可选：如果删除失败，可以考虑重新安排一次？或者依赖后续的系统级清理
                    }
                } else {
                    if (Var.debug) {
                        LogUtil.info(StringUtil.format("文件已不存在，无需清理: downloadId={0}, file={1}", downloadId, filename));
                    }
                    // 文件不存在，仍然尝试清理 _completed 标记，以防万一 (REMOVED)
                    // try {
                    //      Base.map.delKey(DownloadTaskKeyManager.getDownloadCompletedKey(downloadId));
                    //      if (Var.debug) {
                    //             LogUtil.info(StringUtil.format("文件不存在，尝试清理 Redis _completed 标记: downloadId={0}", downloadId));
                    //         }
                    // } catch (Exception e) {
                    //      LogUtil.warn(StringUtil.format("文件不存在时清理 Redis _completed 标记失败: downloadId={0}, error={1}",
                    //                 downloadId, e.getMessage()));
                    // }
                }

                // 不再需要清理本地 lastAccessTimes 或调用 cleanupRedisKeys

            } catch (Exception e) {
                // 这里捕获调度任务本身的异常，而不是文件删除逻辑中的（已在内部处理）
                LogUtil.error(StringUtil.format("文件清理调度任务执行失败: downloadId={0}, file={1}, error={2}",
                        downloadId, filename, e.getMessage()), e);
            }
        }, delayMillis, TimeUnit.MILLISECONDS); // 使用 DownloadTaskExecutor 的 schedule 方法
    }

    // 移除 cleanupRedisKeys 方法

    /**
     * 尝试删除指定文件，支持多次重试机制。
     * ... (方法实现保持不变)
     */
    public static boolean deleteFileWithRetry(File file) {
        if (!file.exists()) {
            return true;
        }
        for (int i = 0; i < MAX_DELETE_RETRIES; i++) {
            if (file.delete()) {
                return true;
            }
            try {
                Thread.sleep(RETRY_DELAY_MS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        return false;
    }

    // 移除 startCleanupTask 方法

} // End of class 