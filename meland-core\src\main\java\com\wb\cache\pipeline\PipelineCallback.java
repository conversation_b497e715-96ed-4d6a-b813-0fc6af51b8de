package com.wb.cache.pipeline;

import java.util.concurrent.TimeUnit;

/**
 * Redis管道回调接口
 * 用于在管道中执行多个Redis操作，减少网络往返次数
 */
public interface PipelineCallback {
    /**
     * 在Pipeline中设置键值
     * 
     * @param key 键
     * @param value 值
     */
    void set(String key, Object value);
    
    /**
     * 在Pipeline中设置键值并指定过期时间
     * 
     * @param key 键
     * @param value 值
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    void set(String key, Object value, long timeout, TimeUnit unit);
    
    /**
     * 在Pipeline中删除键
     * 
     * @param key 键
     */
    void delete(String key);
    
    /**
     * 在Pipeline中设置Hash值
     * 
     * @param key 键
     * @param hashKey 哈希键
     * @param value 值
     */
    void hashPut(String key, Object hashKey, Object value);
    
    /**
     * 在Pipeline中删除Hash值
     * 
     * @param key 键
     * @param hashKey 哈希键
     */
    void hashDelete(String key, Object hashKey);
    
    /**
     * 在Pipeline中添加列表元素
     * 
     * @param key 键
     * @param value 值
     */
    void listPush(String key, Object value);
    
    /**
     * 在Pipeline中添加集合元素
     * 
     * @param key 键
     * @param value 值
     */
    void setAdd(String key, Object value);
    
    /**
     * 在Pipeline中设置键的过期时间
     * 
     * @param key 键
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    void expire(String key, long timeout, TimeUnit unit);
} 