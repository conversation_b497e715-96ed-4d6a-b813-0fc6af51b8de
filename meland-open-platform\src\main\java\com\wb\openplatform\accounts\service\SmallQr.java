package com.wb.openplatform.accounts.service;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;

import org.json.JSONObject;

public class SmallQr {
	/**
	 * 获取 二维码图片
	 * 
	 * @param data
	 * @return
	 */
	public static String getminiqrQr(String data) {
		JSONObject obj = new JSONObject(data);
		String url = obj.getString("url");
		String file = obj.getString("file");
		String scene = obj.getString("scene");
		String page = obj.getString("page");
		String token = obj.getString("token");

		String twoCodeUrl = scene + ".png";
		try {
			URL httpUrl = new URL(url + "?access_token=" + token);
			HttpURLConnection httpURLConnection = (HttpURLConnection) httpUrl.openConnection();
			httpURLConnection.setRequestMethod("POST");// 提交模式
			// conn.setConnectTimeout(10000);//连接超时 单位毫秒
			// conn.setReadTimeout(2000);//读取超时 单位毫秒
			// 发送POST请求必须设置如下两行
			httpURLConnection.setDoOutput(true);
			httpURLConnection.setDoInput(true);
			// 获取URLConnection对象对应的输出流
			PrintWriter printWriter = new PrintWriter(httpURLConnection.getOutputStream());
			// 发送请求参数
			JSONObject paramJson = new JSONObject();
			paramJson.put("scene", scene);
			paramJson.put("path", page);
			paramJson.put("width", 430);
			paramJson.put("is_hyaline", true);
			paramJson.put("auto_color", true);
			/**
			 * line_color生效 paramJson.put("auto_color", false); JSONObject lineColor = new
			 * JSONObject(); lineColor.put("r", 0); lineColor.put("g", 0);
			 * lineColor.put("b", 0); paramJson.put("line_color", lineColor);
			 */

			printWriter.write(paramJson.toString());
			// flush输出流的缓冲
			printWriter.flush();
			// 开始获取数据
			BufferedInputStream bis = new BufferedInputStream(httpURLConnection.getInputStream());
			File fileUrl = new File(file, twoCodeUrl);
			OutputStream os = new FileOutputStream(fileUrl);
			int len;
			byte[] arr = new byte[1024];
			while ((len = bis.read(arr)) != -1) {
				os.write(arr, 0, len);
				os.flush();
			}
			os.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return twoCodeUrl;
	}

	/**
	 * 获取网络图片
	 * 
	 * @param url
	 * @param fileName
	 * @return
	 */
	public static String getHeadImg(String url, String fileName, String path) {
		byte[] btImg = getImageFromNetByUrl(url);
		if (null != btImg && btImg.length > 0) {
			System.out.println("读取到：" + btImg.length + " 字节");
			writeImageToDisk(btImg, fileName, path);
			return fileName;
		} else {
			System.out.println("没有从该连接获得内容");
		}
		return null;
	}

	/**
	 * 将图片写入到磁盘
	 * 
	 * @param img      图片数据流
	 * @param fileName 文件保存时的名称
	 */
	public static void writeImageToDisk(byte[] img, String fileName, String path) {
		try {
			File file = new File(path, fileName);
			FileOutputStream fops = new FileOutputStream(file);
			fops.write(img);
			fops.flush();
			fops.close();
			System.out.println("图片已经写入到C盘");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 根据地址获得数据的字节流
	 * 
	 * @param strUrl 网络连接地址
	 * @return
	 */
	public static byte[] getImageFromNetByUrl(String strUrl) {
		try {
			URL url = new URL(strUrl);
			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			conn.setRequestMethod("GET");
			conn.setConnectTimeout(5 * 1000);
			InputStream inStream = conn.getInputStream();// 通过输入流获取图片数据
			byte[] btImg = readInputStream(inStream);// 得到图片的二进制数据
			return btImg;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 从输入流中获取数据
	 * 
	 * @param inStream 输入流
	 * @return
	 * @throws Exception
	 */
	public static byte[] readInputStream(InputStream inStream) throws Exception {
		ByteArrayOutputStream outStream = new ByteArrayOutputStream();
		byte[] buffer = new byte[1024];
		int len = 0;
		while ((len = inStream.read(buffer)) != -1) {
			outStream.write(buffer, 0, len);
		}
		inStream.close();
		return outStream.toByteArray();
	}

}
