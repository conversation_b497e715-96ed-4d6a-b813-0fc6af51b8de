package com.wb.interact;

import com.wb.common.Base;
import com.wb.common.UserList;
import com.wb.common.Var;
import com.wb.fit.CustomRequest;
import com.wb.fit.CustomResponse;
import com.wb.fit.CustomSession;
import com.wb.tool.DataOutput;
import com.wb.tool.DataOutputEx;
import com.wb.tool.DataOutputEx.StreamingExcelContext;
import com.wb.tool.ExcelObject;
import com.wb.util.*;
import org.json.JSONArray;
import org.json.JSONObject;
// Added Redisson imports
import org.redisson.api.RAtomicLong;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RSet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.session.Session;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.zip.GZIPOutputStream;

/**
 * 文件推送处理类 主要功能： 1. 处理客户端文件下载请求 2. 支持同步和异步文件生成 3. 支持多种文件格式：原始数据、Excel、文本、HTML 4.
 * 提供文件下载状态查询 5. 自动清理过期文件
 */
public class FilePush {

    private static final Logger LOGGER = LoggerFactory.getLogger(FilePush.class);
    /**
     * 用于存储下载任务信息的映射表。
     * <p>
     * downloadTasks 是一个线程安全的 HashMap，其中的 key 表示下载任务的唯一标识（downloadId）， value
     * 表示对应的文件路径。
     * <p>
     * 该变量可以用于管理异步下载任务的状态及相关文件的存储路径，例如记录生成文件的下载信息， 以供后续下载或清理操作使用。
     * <p>
     * 注意：使用该变量时需确保对任务状态的更新、查询和删除操作的正确性，以避免因并发问题导致数据不一致。
     */
    private static final ConcurrentHashMap<String, String> downloadTasks = new ConcurrentHashMap<>();

    /**
     * 用于控制下载任务的并发执行的信号量。 此信号量允许最多5个线程同时执行下载操作，从而限制并发下载任务的数量，避免过多任务占用系统资源。
     */
    // 下载并发控制，最多允许5个并发下载任务
    private static final Semaphore downloadSemaphore = new Semaphore(10);

    /**
     * 定义下载文件的基础目录路径。
     * <p>
     * 此变量为常量，用于指定文件下载相关操作的根目录。 所有下载文件将在该目录下根据不同需求进行存储。 可结合文件生成、异步传输等功能模块使用。
     * <p>
     * 值为 "attach/download"。
     */
    private static final String DOWNLOAD_BASE_DIR = "attach/download";

    /**
     * 定义下载文件的过期时间（以小时为单位）。 在指定时间限制内，允许用户下载文件。当超过该时间后，文件可能会被自动清理，以释放存储空间。
     */
    private static final int DOWNLOAD_EXPIRE_HOURS = 24;

    /**
     * 添加新的Map来存储最后访问时间和任务状态
     */
    private static final ConcurrentHashMap<String, Long> lastAccessTimes = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Boolean> cancelledTasks = new ConcurrentHashMap<>();

    /**
     * 定义任务超时时间（30秒） 如果用户在指定时间内没有请求，则判定为超时
     */
    private static final long TASK_TIMEOUT_MS = 30 * 1000;

    // 添加任务Future管理
    private static final ConcurrentHashMap<String, Future<?>> taskFutures = new ConcurrentHashMap<>();
    // 添加处理线程管理
    private static final ConcurrentHashMap<String, Thread> processingThreads = new ConcurrentHashMap<>();

    // 系统并发处理能力配置，用于统一管理下载并发数和线程池大小
    private static final int SYSTEM_CONCURRENT_CAPACITY = 10;

    // 使用公共配置常量定义最大并发下载数
    private static final int MAX_CONCURRENT_DOWNLOADS = SYSTEM_CONCURRENT_CAPACITY;

    // 使用公共配置常量定义最大线程数
    private static final int MAX_CONCURRENT_THREADS = SYSTEM_CONCURRENT_CAPACITY; // 最大并发线程数

    // 在类开始处添加活跃线程计数器和相关常量
    private static final AtomicInteger activeThreadCount = new AtomicInteger(0);

    /**
     * 基于Redis的分布式信号量实现 - 获取信号量 使用Redis原子计数器控制全局并发数量
     *
     * @return 获取成功返回true，失败返回false
     */
    private static boolean acquireDownloadSemaphore() {
        try {
            // 获取当前信号量计数
            long currentCount = Base.map.getRedissonClient()
                    .getAtomicLong(DownloadTaskKeyManager.getDownloadSemaphoreKey()).incrementAndGet();

            // 如果超过最大并发数，回滚计数并拒绝
            if (currentCount > MAX_CONCURRENT_DOWNLOADS) {
                // 回滚+1操作
                Base.map.getRedissonClient().getAtomicLong(DownloadTaskKeyManager.getDownloadSemaphoreKey())
                        .decrementAndGet();
                return false;
            }

            return downloadSemaphore.tryAcquire(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            LogUtil.error("获取下载信号量失败: " + e.getMessage());

            // 确保回滚计数
            try {
                Base.map.getRedissonClient().getAtomicLong(DownloadTaskKeyManager.getDownloadSemaphoreKey())
                        .decrementAndGet();
            } catch (Exception ex) {
                // 忽略回滚异常
            }

            return false;
        }
    }

    /**
     * 基于Redis的分布式信号量实现 - 释放信号量
     *
     * @param downloadId 下载任务ID，用于日志跟踪
     */
    public static void releaseDownloadSemaphore(String downloadId) {
        try {
            // 先释放本地信号量
            downloadSemaphore.release();

            // 同时减少Redis信号量计数
            RAtomicLong semaphoreCounter = Base.map.getRedissonClient()
                    .getAtomicLong(DownloadTaskKeyManager.getDownloadSemaphoreKey());
            long currentCount = semaphoreCounter.get();
            if (currentCount > 0) {
                semaphoreCounter.decrementAndGet();
            }

            LOGGER.debug("释放下载信号量: " + downloadId);
        } catch (Exception e) {
            LogUtil.error("释放下载信号量失败: " + e.getMessage());
        }
    }

    /**
     * 更新任务心跳时间戳 用于检测任务是否仍在运行
     *
     * @param downloadId 下载任务ID
     */
    private static void updateTaskHeartbeat(String downloadId) {
        try {
            Base.map.setValue(DownloadTaskKeyManager.getDownloadHeartbeatKey(downloadId),
                    String.valueOf(System.currentTimeMillis()), 1, TimeUnit.MINUTES);
        } catch (Exception e) {
            LogUtil.warn("更新任务心跳失败: " + e.getMessage());
        }
    }

    /**
     * 静态初始化块，注册JVM关闭钩子，确保应用程序关闭时线程池能够正确关闭
     */
    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                waitForThreadsToComplete(30);
                if (!waitForThreadsToComplete(30)) {
                    LogUtil.warn("FilePush下载线程池在30秒内未能完全关闭，将强制关闭");
                    waitForThreadsToComplete(30);
                    if (!waitForThreadsToComplete(30)) {
                        LogUtil.error("FilePush下载线程池强制关闭失败");
                    }
                }
            } catch (Exception e) {
                Thread.currentThread().interrupt();
                LogUtil.error("关闭FilePush下载线程池时被中断");
                waitForThreadsToComplete(30);
            }
        }));

        // 启动校准任务，定期检查并校准计数器
        DownloadTaskExecutor.runAsync(() -> {
            try {
                // 首次启动等待30秒，避免所有节点同时启动时的竞争
                Thread.sleep(30 * 1000);

                // 首次执行校准（使用分布式锁）
                calibrateWithLock();

                while (!Thread.currentThread().isInterrupted() && !waitForThreadsToComplete(0)) {
                    try {
                        // 每10分钟校准一次计数器
                        Thread.sleep(10 * 60 * 1000);
                        calibrateWithLock();
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    } catch (Exception e) {
                        LogUtil.warn("校准下载信号量计数器时出错: " + e.getMessage());
                    }
                }
            } catch (Exception e) {
                LogUtil.error("下载信号量计数器校准任务初始化失败: " + e.getMessage());
            }
        });

        // 在静态初始化块中添加定期检查任务 (信号量修复)
        DownloadTaskExecutor.runAsync(() -> {
            try {
                while (!Thread.currentThread().isInterrupted()) {
                    try {
                        Thread.sleep(60 * 1000); // 每分钟检查一次

                        // 获取当前可用的信号量数量
                        int availablePermits = downloadSemaphore.availablePermits();
                        int activeTaskCount = processingThreads.size();

                        // 如果可用信号量数量异常（小于应有值），进行修复
                        if (availablePermits + activeTaskCount < MAX_CONCURRENT_DOWNLOADS) {
                            LogUtil.warn("检测到信号量异常，进行修复");
                            int toRelease = MAX_CONCURRENT_DOWNLOADS - (availablePermits + activeTaskCount);
                            for (int i = 0; i < toRelease; i++) {
                                downloadSemaphore.release();
                            }
                        }

                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    } catch (Exception e) {
                        LogUtil.warn("检查信号量状态时出错: " + e.getMessage());
                    }
                }
            } catch (Exception e) {
                LogUtil.error("信号量监控任务异常: " + e.getMessage());
            }
        });
    }

    /**
     * 使用分布式锁执行计数器校准任务 确保集群环境下只有一个节点执行校准
     */
    private static void calibrateWithLock() {
        String lockKey = "DOWNLOAD_CALIBRATE_LOCK";
        boolean lockAcquired = false;

        try {
            // 尝试获取锁，等待最多5秒，持有锁2分钟（校准任务预计不会超过这个时间）
            lockAcquired = Base.map.tryLock(lockKey, 5, 2 * 60);

            if (lockAcquired) {
                calibrateDownloadSemaphoreCounter();
            }
        } catch (Exception e) {
            LogUtil.error("获取下载计数器校准锁失败: " + e.getMessage());
        } finally {
            // 释放锁（只有成功获取了锁的节点才需要释放）
            if (lockAcquired) {
                try {
                    Base.map.unLock(lockKey);
                } catch (Exception e) {
                    LogUtil.error("释放下载计数器校准锁失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 校准分布式信号量计数器 通过检查活跃任务Set的大小来确定实际运行任务数，并校准计数器 (优化后) 适用于集群环境，不会简单重置计数器
     */
    private static void calibrateDownloadSemaphoreCounter() {
        try {
            // 获取当前信号量计数
            RAtomicLong semaphoreCounter = Base.map.getRedissonClient()
                    .getAtomicLong(DownloadTaskKeyManager.getDownloadSemaphoreKey());
            long currentCount = semaphoreCounter.get();

            // 获取活跃任务Set的大小 (优化点：使用SCARD替代SCAN)
            RSet<String> activeTasksSet = Base.map.getRedissonClient()
                    .getSet(DownloadTaskKeyManager.getActiveTasksSetKey());
            long activeTaskCount = activeTasksSet.size(); // SCARD is O(1)

            // 周期性清理 Set 中的无效任务 (可选，但推荐)
            // 可以添加一个逻辑，例如每隔几次校准，就遍历 Set 中的 ID，
            // 检查对应的 heartbeat key 是否仍然存在，如果不存在则从 Set 中移除。
            // 这里暂时省略这个清理逻辑，依赖于 cleanupDownloadTask 的 SREM。

            if (currentCount != activeTaskCount) {
                if (Var.debug) {
                    LogUtil.info(StringUtil.format("校准下载信号量计数: 从 {0} 到 {1}", currentCount, activeTaskCount));
                }
                // 使用 set 而不是 getAndSet，避免竞态条件下的短暂错误值
                semaphoreCounter.set(activeTaskCount);

                // 详细记录调整情况（调试用）
                if (activeTaskCount > 0 && Var.debug) {
                    // 获取部分活跃任务ID用于日志记录，避免获取整个集合
                    StringBuilder taskIds = new StringBuilder();
                    int count = 0;
                    for (String taskId : activeTasksSet.readAll()) { // readAll() 即 SMEMBERS
                        taskIds.append(taskId).append(", ");
                        count++;
                        if (count >= 10) { // 只记录前10个
                            taskIds.append("...");
                            break;
                        }
                    }
                    LogUtil.info("当前活跃任务示例: " + taskIds.toString());
                }
            }
        } catch (Exception e) {
            LogUtil.error("校准下载信号量计数器失败: " + e.getMessage(), e); // 添加异常堆栈跟踪
        }
    }

    /**
     * 根据客户端推送的数据生成原始数据文件，并发送给客户端
     *
     * @param request  HTTP请求对象，包含文件名、数据内容和压缩选项
     * @param response HTTP响应对象，用于返回生成的文件
     * @throws Exception 当文件处理过程中发生错误时抛出
     */
    public static void getFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String filename = WebUtil.fetch(request, "filename");
        String data = WebUtil.fetch(request, "data");
        boolean gzip = Boolean.parseBoolean(WebUtil.fetch(request, "gzip"));

        if (filename == null) {
            if (gzip)
                filename = "data.gz";
            else
                filename = "data";
        }
        response.setHeader("content-type", "application/force-download");
        response.setHeader("content-disposition", "attachment;" + WebUtil.encodeFilename(request, filename));
        if (gzip) {
            GZIPOutputStream gos = new GZIPOutputStream(response.getOutputStream());
            try {
                gos.write(data.getBytes("utf-8"));
            } finally {
                if (gos != null) {
                    try {
                        gos.close();
                    } catch (IOException e) {
                        // 忽略关闭异常
                    }
                }
            }
            response.flushBuffer();
        } else
            WebUtil.send(response, data);
    }

    /**
     * 根据客户端推送的数据生成原始文本，并发送给客户端
     *
     * @param request  HTTP请求对象，包含要发送的文本内容
     * @param response HTTP响应对象，用于返回生成的文本
     * @throws Exception 当文本处理过程中发生错误时抛出
     */
    public static void getText(HttpServletRequest request, HttpServletResponse response) throws Exception {
        WebUtil.send(response, request.getParameter("text"));
    }

    /**
     * 根据客户端推送的数据生成Excel文件，并发送给客户端 支持自定义标题、日期时间格式等
     *
     * @param request  HTTP请求对象，包含Excel相关参数
     * @param response HTTP响应对象，用于返回生成的Excel文件
     * @throws Exception 当Excel生成过程中发生错误时抛出
     */
    public static void getExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String ext = ExcelObject.getExtName();
        String filename = WebUtil.fetch(request, "filename");
        String title = WebUtil.fetch(request, "title");
        if (StringUtil.isEmpty(filename))
            filename = "data" + ext;
        else
            filename += ext;
        response.setHeader("content-type", "application/force-download");
        response.setHeader("content-disposition", "attachment;" + WebUtil.encodeFilename(request, filename));
        DataOutput.outputExcel(response.getOutputStream(), new JSONArray(WebUtil.fetch(request, "headers")),
                new JSONArray(WebUtil.fetch(request, "rows")), StringUtil.isEmpty(title) ? null : title,
                new JSONObject("{mergeInfo:[]}"), StringUtil.select(WebUtil.fetch(request, "dateFormat"), "Y-m-d"),
                StringUtil.select(WebUtil.fetch(request, "timeFormat"), "H:i:s"), false);
        response.flushBuffer();
    }

    /**
     * 根据客户端推送的数据在服务器端指定目录生成原始数据文件 支持普通文本和GZIP压缩格式
     *
     * @param request  HTTP请求对象，包含文件名、数据内容和压缩选项
     * @param response HTTP响应对象
     * @throws Exception 当文件已存在或写入失败时抛出
     */
    public static void writeFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String filename = WebUtil.fetch(request, "filename");
        String data = WebUtil.fetch(request, "data");
        boolean gzip = Boolean.parseBoolean(WebUtil.fetch(request, "gzip"));
        File file = new File(filename);
        if (file.exists())
            throw new Exception("文件 \"" + filename + "\" 已经存在。");
        if (gzip) {
            FileOutputStream fos = null;
            GZIPOutputStream gos = null;
            try {
                fos = new FileOutputStream(file);
                gos = new GZIPOutputStream(fos);
                gos.write(data.getBytes("utf-8"));
            } finally {
                if (gos != null) {
                    try {
                        gos.close();
                    } catch (IOException e) {
                        // 忽略关闭异常
                    }
                }
                if (fos != null) {
                    try {
                        fos.close();
                    } catch (IOException e) {
                        // 忽略关闭异常
                    }
                }
            }
        } else
            FileUtil.writeString(file, data);
    }

    /**
     * 对指定url使用include方法发起请求，并把请求获取的数据按指定格式进行输出 支持Excel、文本和HTML格式的导出
     *
     * @param request  HTTP请求对象，包含导出相关参数
     * @param response HTTP响应对象，用于返回导出的文件
     * @throws Exception 当数据处理或导出过程中发生错误时抛出
     */
    public static void transfer(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String data, filename, fileExtName, respText;
        String type, title, dateFormat, timeFormat;
        String decimalSeparator, thousandSeparator;
        JSONObject responseObject, reportInfo, metaParams = new JSONObject(request.getParameter("__metaParams"));
        JSONArray headers, records;
        boolean isHtml = false, neptune;
        CustomResponse resp;

        type = metaParams.optString("type");
        if ("excel".equals(type)) {
            fileExtName = ExcelObject.getExtName();
        } else if ("text".equals(type)) {
            fileExtName = ".txt";
        } else if ("html".equals(type)) {
            fileExtName = ".html";
            isHtml = true;
        } else
            throw new IllegalArgumentException("Invalid request file type.");
        data = metaParams.optString("data", null);// 获取客户端数据
        if (data == null) {
            // 如果客户端无数据（输出全部页时）重新获取服务端数据
            resp = new CustomResponse(response);
            request.setAttribute("sys.rowOnly", 1);// 标记仅生成rows数据
            request.setAttribute("sys.fromExport", 1);// 标记操作来自导出
            String url = StringUtil.isEmpty(metaParams.optString("url")) ? request.getParameter("xwl")
                    : metaParams.optString("url");
            WebUtil.include(request, resp, StringUtil.format("m?xwl={0}", url));
            respText = WbUtil.getResponseString(resp);
            if (respText.startsWith("<textarea>")) {
                // json response格式
                data = respText.substring(10, respText.length() - 11);
                responseObject = new JSONObject(data);
                data = (String) responseObject.opt("value");
                if (!responseObject.optBoolean("success")) {
                    WebUtil.send(response, data, false);
                    return;
                }
            } else {
                // Ajax格式
                respText = respText.trim();
                if (!respText.startsWith("{") || !respText.endsWith("}")) {
                    WebUtil.send(response, respText, false);
                    return;
                }
                data = respText;
            }
            records = new JSONObject(data).getJSONArray("rows");
        } else
            records = new JSONArray(data);
        headers = metaParams.optJSONArray("headers");
        title = metaParams.optString("title", null);// 标题如果为null不输出
        reportInfo = metaParams.optJSONObject("reportInfo");
        // 确保reportInfo不为null
        if (reportInfo == null) {
            reportInfo = new JSONObject();
        }
        dateFormat = metaParams.optString("dateFormat");// 默认日期格式
        timeFormat = metaParams.optString("timeFormat");// 默认时间格式
        neptune = metaParams.optBoolean("neptune");
        decimalSeparator = metaParams.optString("decimalSeparator", ".");
        thousandSeparator = metaParams.optString("thousandSeparator", ",");
        filename = metaParams.optString("filename");
        if (StringUtil.isEmpty(filename))
            filename = "data";
        filename += fileExtName;
        if (!isHtml) {
            // 非html下载文件
            response.setHeader("content-type", "application/force-download");
            response.setHeader("content-disposition", "attachment;" + WebUtil.encodeFilename(request, filename));
        }
        if ("excel".equals(type)) {
            DataOutput.outputExcel(response.getOutputStream(), headers, records, title, reportInfo, dateFormat,
                    timeFormat, neptune);
        } else if ("text".equals(type)) {
            DataOutput.outputText(response.getOutputStream(), headers, records, dateFormat, timeFormat,
                    decimalSeparator, thousandSeparator);

        } else {// html预览
            DataOutput.outputHtml(response.getOutputStream(), headers, records, title, dateFormat, timeFormat, neptune,
                    metaParams.getInt("rowNumberWidth"), metaParams.optString("rowNumberTitle", "#"), decimalSeparator,
                    thousandSeparator);
        }
        response.flushBuffer();
    }

    /**
     * 根据客户端推送的数据异步生成文件，并返回下载ID 支持大文件异步处理，避免请求超时
     *
     * @param request  HTTP请求对象，包含文件生成相关参数
     * @param response HTTP响应对象，返回下载ID
     * @throws Exception 当参数处理或文件生成过程中发生错误时抛出
     */
    public static void asyncTransfer(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 获取请求参数
        String type = request.getParameter("type");
        if (StringUtil.isEmpty(type)) {
            type = "excel"; // 默认类型
        }

        // 获取原始文件名
        String filename = request.getParameter("filename");
        // 默认名称
        if (StringUtil.isEmpty(filename)) {
            filename = "download";
        }

        // 生成文件名基础部分
        String filenameBase = filename;

        // 处理元参数
        JSONObject metaParams = null;
        try {
            final String metaParamsStr = request.getParameter("__metaParams");
            if (!StringUtil.isEmpty(metaParamsStr)) {
                metaParams = new JSONObject(metaParamsStr);
            } else {
                metaParams = new JSONObject();
            }
        } catch (Exception e) {
            metaParams = new JSONObject();
        }

        // 生成下载ID
        final String downloadId = filenameBase + "_" + System.currentTimeMillis();

        // 保存所有原始请求参数
        final Map<String, String[]> originalParams = new HashMap<>(request.getParameterMap());

        // 创建包含所有参数的JSONObject，以保证所有参数都能被传递
        final JSONObject allParams = new JSONObject();

        // 将所有原始参数放入allParams中
        for (Map.Entry<String, String[]> entry : originalParams.entrySet()) {
            String paramName = entry.getKey();
            String[] values = entry.getValue();

            if (values != null && values.length > 0) {
                if (values.length == 1) {
                    // 单值参数
                    allParams.put(paramName, values[0]);
                } else {
                    // 多值参数
                    JSONArray valuesArray = new JSONArray();
                    for (String value : values) {
                        valuesArray.put(value);
                    }
                    allParams.put(paramName, valuesArray);
                }
            }
        }

        // 如果已有__metaParams，保留它
        if (metaParams != null && metaParams.length() > 0) {
            allParams.put("__metaParams", request.getParameter("__metaParams"));
        }

        // 将完整参数对象转为字符串
        final String completeParamsStr = allParams.toString();

        // 从__metaParams中获取目标url，与transfer方法保持一致
        final String targetUrl = StringUtil.isEmpty(metaParams.optString("url")) ? request.getParameter("xwl")
                : metaParams.optString("url");

        // 保存session相关信息
        final JSONObject sessionInfo = new JSONObject();
        javax.servlet.http.HttpSession session = request.getSession(false);
        if (session != null) {
            // 保存基本的 session 属性
            sessionInfo.put("JSESSIONID", session.getId());
            sessionInfo.put("creationTime", session.getCreationTime());
            sessionInfo.put("lastAccessedTime", session.getLastAccessedTime());
            sessionInfo.put("maxInactiveInterval", session.getMaxInactiveInterval());

            // 特别保存登录状态
            Object loginStatus = session.getAttribute("sys.logined");
            if (loginStatus != null) {
                sessionInfo.put("sys.logined", loginStatus);
            }

            // 保存其他 session 属性
            Enumeration<String> attrNames = session.getAttributeNames();
            while (attrNames.hasMoreElements()) {
                String name = attrNames.nextElement();
                if (!"sys.logined".equals(name)) { // 登录状态已经单独处理
                    Object value = session.getAttribute(name);
                    if (value != null) {
                        sessionInfo.put(name, value);
                    }
                }
            }
        }

        // 打印状态信息
        LOGGER.info("准备提交下载任务: " + downloadId + ", 当前活跃线程数: " + activeThreadCount.get());

        // 检查活跃线程数是否超过限制
        if (activeThreadCount.get() >= MAX_CONCURRENT_THREADS) {
            LogUtil.error("当前活跃线程数已达上限，拒绝任务: " + downloadId + ", 活跃线程: " + activeThreadCount.get());
            WebUtil.send(response, "{\"success\":false,\"message\":\"系统繁忙，请稍后重试\"}", true);
            return;
        }

        // 创建年月日目录结构
        String datePath = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
        File downloadDir = new File(Base.path + "/" + DOWNLOAD_BASE_DIR, datePath);
        if (!downloadDir.exists()) {
            downloadDir.mkdirs();
        }

        // 确定文件扩展名
        String fileExt = ".dat";
        if ("excel".equals(type)) {
            fileExt = ExcelObject.getExtName();
        } else if ("text".equals(type)) {
            fileExt = ".txt";
        } else if ("html".equals(type)) {
            fileExt = ".html";
        }

        // 创建临时文件
        final File tempFile = new File(downloadDir, filenameBase + "_" + System.currentTimeMillis() + fileExt);
        final FileOutputStream fileOutputStream = new FileOutputStream(tempFile);

        // 创建一个取消标志
        final AtomicBoolean taskCancelled = new AtomicBoolean(false);

        // 创建一个任务Future
        final CompletableFuture<Void> taskFuture = new CompletableFuture<>();

        // 添加metaParamsStr变量
        final String metaParamsStr = request.getParameter("__metaParams");

        // 创建并启动任务
        DownloadTask task = new DownloadTask(downloadId, completeParamsStr, // 使用completeParamsStr
                targetUrl, type, sessionInfo, tempFile, fileOutputStream, taskCancelled, taskFuture);

        // 使用 DownloadTaskExecutor 提交任务，替代手动创建和启动线程
        DownloadTaskExecutor.runAsync(task);

        // 存储 CompletableFuture 用于后续管理（例如状态检查或取消）
        // 注意：taskFuture 是传递给 DownloadTask 的，任务内部会 complete 或 completeExceptionally
        taskFutures.put(downloadId, taskFuture);

        // 优化任务提交后的前端反馈
        JSONObject responseResult = new JSONObject();
        responseResult.put("success", true);
        responseResult.put("downloadId", downloadId);
        responseResult.put("timestamp", System.currentTimeMillis()); // 添加时间戳
        WebUtil.send(response, responseResult.toString(), true);

        // 日志记录任务已提交
        LOGGER.info("下载任务已提交到执行器: " + downloadId + ", 当前活跃线程数: " + activeThreadCount.get());

        // 移除独立的取消监控任务
    }

    /**
     * 检查异步下载任务的状态 返回任务的当前状态：处理中、完成、错误等
     *
     * @param request  HTTP请求对象，包含下载ID
     * @param response HTTP响应对象，返回任务状态
     * @throws Exception 当状态检查过程中发生错误时抛出
     */
    public static void checkDownloadStatus(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String downloadId = WebUtil.fetch(request, "downloadId");
        if (StringUtil.isEmpty(downloadId)) {
            WebUtil.send(response, "{\"success\":false,\"message\":\"下载ID不能为空\"}", true);
            return;
        }

        long currentTime = System.currentTimeMillis();

        // 更新最后访问时间 (本地和 Redis ZSet)
        lastAccessTimes.put(downloadId, currentTime); // 本地缓存仍然可以保留，用于快速检查超时
        try {
            // 更新 Redis ZSet (优化点)
            RScoredSortedSet<String> accessTimesZSet = Base.map.getRedissonClient()
                    .getScoredSortedSet(DownloadTaskKeyManager.getAccessTimesSortedSetKey());
            accessTimesZSet.add(currentTime, downloadId);
            // 设置 ZSet 中成员的过期时间可能比较复杂，通常依赖于定期清理任务
            // 或者在添加时移除旧的 score (如果需要精确控制过期)
            // accessTimesZSet.addAndGetRank(currentTime, downloadId); // addAndGetRank 也可以

            // 更新独立的访问时间 key (可选，如果其他地方依赖这个 key)
            // Base.map.setValue(DownloadTaskKeyManager.getDownloadAccessKey(downloadId),
            // String.valueOf(currentTime), DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);
        } catch (Exception e) {
            LogUtil.warn("更新 Redis 下载任务访问时间 ZSet 失败: " + e.getMessage());
        }

        // 检查任务是否已取消
        if (Boolean.TRUE.equals(cancelledTasks.get(downloadId))) {
            WebUtil.send(response, "{\"success\":false,\"status\":\"cancelled\",\"message\":\"下载已取消\"}", true);
            return;
        }

        // 检查Redis中任务是否已取消
        try {
            String cancelledValue = (String) Base.map
                    .getValue(DownloadTaskKeyManager.getDownloadCancelledKey(downloadId));
            if ("true".equals(cancelledValue)) {
                WebUtil.send(response, "{\"success\":false,\"status\":\"cancelled\",\"message\":\"下载已取消\"}", true);
                return;
            }
        } catch (Exception e) {
            LogUtil.warn("检查Redis下载任务取消状态失败: " + e.getMessage());
        }

        // 检查任务是否超时 (使用本地缓存)
        Long lastAccessTime = lastAccessTimes.get(downloadId);
        if (lastAccessTime != null && currentTime - lastAccessTime > TASK_TIMEOUT_MS) {
            // 清理超时任务
            cleanupDownloadTask(downloadId);
            WebUtil.send(response, "{\"success\":false,\"status\":\"timeout\",\"message\":\"下载任务已超时(本地检测)\"}", true);
            return;
        }

        // 如果本地缓存没有超时，可以再次检查 ZSet (可选，作为冗余检查)
        // try {
        // RScoredSortedSet<String> accessTimesZSet =
        // Base.map.getRedissonClient().getSortedSet(getAccessTimesSortedSetKey());
        // Double score = accessTimesZSet.getScore(downloadId);
        // if (score != null && currentTime - score.longValue() > TASK_TIMEOUT_MS) {
        // cleanupDownloadTask(downloadId);
        // WebUtil.send(response,
        // "{\"success\":false,\"status\":\"timeout\",\"message\":\"下载任务已超时(Redis检测)\"}",
        // true);
        // return;
        // }
        // } catch (Exception e) {
        // LogUtil.warn("检查Redis下载任务超时状态失败: " + e.getMessage());
        // }

        // 先检查是否有错误
        String errorMessage = downloadTasks.get(downloadId + "_error");
        if (errorMessage == null) {
            // 从Redis中检查错误信息
            try {
                errorMessage = (String) Base.map.getValue(DownloadTaskKeyManager.getDownloadErrorKey(downloadId));
            } catch (Exception e) {
                LogUtil.warn("从Redis获取下载任务错误信息失败: " + e.getMessage());
            }
        }

        if (errorMessage != null) {
            // 记录日志以便追踪
            LOGGER.info("找到下载任务错误信息: " + downloadId + ", " + errorMessage);

            // 延迟清理错误状态
            CompletableFuture.runAsync(() -> {
                try {
                    // 延迟5秒后再清理错误状态
                    Thread.sleep(5000);

                    downloadTasks.remove(downloadId + "_error");
                    try {
                        Base.map.delKey(DownloadTaskKeyManager.getDownloadErrorKey(downloadId));
                    } catch (Exception e) {
                        LogUtil.warn("删除Redis下载任务错误信息失败: " + e.getMessage());
                    }

                    cleanupDownloadTask(downloadId);
                } catch (Exception e) {
                    LogUtil.error("延迟清理错误状态失败: " + e.getMessage());
                }
            });

            // 构建错误响应
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("status", "error");
            // 将换行符转换为<br>标签
            errorMessage = errorMessage.replace("\n", "<br>");
            errorResponse.put("message", errorMessage);

            // 返回错误信息
            WebUtil.send(response, errorResponse.toString(), true);
            return;
        }

        String filePath = downloadTasks.get(downloadId);
        // 如果本地没有找到，从Redis中获取
        if (filePath == null) {
            try {
                filePath = (String) Base.map.getValue(DownloadTaskKeyManager.getDownloadTaskKey(downloadId));
            } catch (Exception e) {
                LogUtil.warn("从Redis获取下载任务文件路径失败: " + e.getMessage());
            }
        }

        // 添加特殊处理逻辑：如果找不到文件路径但有任务完成标记，尝试恢复状态
        if (filePath == null) {
            // 检查是否有完成标记
            String completedMark = downloadTasks.get(downloadId + "_completed");
            if (completedMark == null) {
                // 从Redis检查完成标记
                try {
                    completedMark = (String) Base.map
                            .getValue(DownloadTaskKeyManager.getDownloadTaskKey(downloadId) + "_completed");
                } catch (Exception e) {
                    LogUtil.warn("从Redis获取下载任务完成标记失败: " + e.getMessage());
                }
            }

            if ("true".equals(completedMark)) {
                // 任务已完成但路径丢失，尝试查找实际文件
                String datePath = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
                File downloadDir = new File(Base.path + "/" + DOWNLOAD_BASE_DIR, datePath);

                if (downloadDir.exists()) {
                    // 查找与downloadId匹配的文件
                    File[] files = downloadDir.listFiles((dir, name) -> name.contains(downloadId.split("_")[0]));
                    if (files != null && files.length > 0) {
                        // 使用最近修改的文件
                        File latestFile = files[0];
                        for (File file : files) {
                            if (file.lastModified() > latestFile.lastModified()) {
                                latestFile = file;
                            }
                        }

                        // 恢复文件路径
                        filePath = latestFile.getAbsolutePath();

                        // 重新保存路径信息
                        downloadTasks.put(downloadId, filePath);
                        try {
                            Base.map.setValue(DownloadTaskKeyManager.getDownloadTaskKey(downloadId), filePath,
                                    DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);
                        } catch (Exception e) {
                            LogUtil.warn("Redis恢复下载任务文件路径失败: " + e.getMessage());
                        }

                        LogUtil.warn("已恢复丢失的下载文件路径: " + downloadId + " -> " + filePath);
                    }
                }
            }
        }

        if (filePath == null) {
            // 获取任务开始时间
            String startTimeStr = downloadTasks.get(downloadId + "_start_time");
            if (startTimeStr == null) {
                // 从Redis中获取开始时间
                try {
                    startTimeStr = (String) Base.map
                            .getValue(DownloadTaskKeyManager.getDownloadStartTimeKey(downloadId));
                } catch (Exception e) {
                    LogUtil.warn("从Redis获取下载任务开始时间失败: " + e.getMessage());
                }
            }

            // 获取保存的进度百分比和进度文本
            String progressPercentageStr = null;
            String progressText = null;
            try {
                progressPercentageStr = (String) Base.map
                        .getValue(DownloadTaskKeyManager.getDownloadProgressPercentageKey(downloadId));
                progressText = (String) Base.map.getValue(DownloadTaskKeyManager.getDownloadProgressKey(downloadId));
            } catch (Exception e) {
                LogUtil.warn("从Redis获取下载任务进度信息失败: " + e.getMessage());
            }

            double progress = 0;

            // 如果有保存的进度百分比，优先使用
            if (progressPercentageStr != null) {
                try {
                    progress = Double.parseDouble(progressPercentageStr);
                } catch (NumberFormatException e) {
                    LogUtil.warn("解析进度百分比失败: " + progressPercentageStr);
                }
            } else {
                // 如果没有保存的进度，使用基于时间的估算进度
                long startTime;
                if (startTimeStr == null) {
                    // 如果没有开始时间，说明任务刚刚开始
                    startTime = System.currentTimeMillis();
                    downloadTasks.put(downloadId + "_start_time", String.valueOf(startTime));
                    // 同时存储到Redis
                    try {
                        Base.map.setValue(DownloadTaskKeyManager.getDownloadStartTimeKey(downloadId),
                                String.valueOf(startTime), DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);
                    } catch (Exception e) {
                        LogUtil.warn("Redis存储下载任务开始时间失败: " + e.getMessage());
                    }
                } else {
                    startTime = Long.parseLong(startTimeStr);
                }

                // 计算已经过去的时间（毫秒）
                long elapsedTime = System.currentTimeMillis() - startTime;

                // 估算进度（假设一般任务在30秒内完成）
                progress = Math.min(0.95, elapsedTime / 30000.0);
            }

            // 构建进度响应
            JSONObject progressResponse = new JSONObject();
            progressResponse.put("success", true);
            progressResponse.put("status", "processing");
            progressResponse.put("progress", progress);

            // 根据进度设置不同的提示文本
            if (progressText == null) {
                if (progress < 0.3) {
                    progressText = "正在准备数据...";
                } else if (progress < 0.6) {
                    progressText = "正在处理数据...";
                } else if (progress < 0.8) {
                    progressText = "正在生成文件...";
                } else {
                    progressText = "即将完成...";
                }
            }
            progressResponse.put("progressText", progressText);

            WebUtil.send(response, progressResponse.toString(), true);
            return;
        }

        // 检查文件是否真实存在
        File file = new File(filePath);
        if (!file.exists()) {
            WebUtil.send(response, "{\"success\":false,\"status\":\"error\",\"message\":\"文件不存在或已被删除\"}", true);
            return;
        }

        // 清理开始时间记录
        downloadTasks.remove(downloadId + "_start_time");
        WebUtil.send(response, "{\"success\":true,\"status\":\"ready\"}", true);
    }

    /**
     * 下载已异步生成的文件 根据下载ID获取并发送文件给客户端
     *
     * @param request  HTTP请求对象，包含下载ID
     * @param response HTTP响应对象，用于返回文件内容
     * @throws Exception 当文件不存在或下载过程中发生错误时抛出
     */
    public static void downloadFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String downloadId = WebUtil.fetch(request, "downloadId");
        if (StringUtil.isEmpty(downloadId)) {
            WebUtil.send(response, "下载ID不能为空", false);
            return;
        }

        String filePath = downloadTasks.get(downloadId);
        // 从Redis中获取文件路径
        if (filePath == null) {
            try {
                filePath = (String) Base.map.getValue(DownloadTaskKeyManager.getDownloadTaskKey(downloadId));
            } catch (Exception e) {
                LogUtil.warn("从Redis获取下载任务文件路径失败: " + e.getMessage());
            }
        }

        if (filePath == null) {
            WebUtil.send(response, "下载文件不存在或已过期", false);
            return;
        }

        File file = new File(filePath);
        if (!file.exists()) {
            WebUtil.send(response, "文件不存在", false);
            return;
        }

        // 设置响应头
        response.setHeader("content-type", "application/force-download");
        response.setHeader("content-disposition", "attachment;" + WebUtil.encodeFilename(request, file.getName()));

        // 使用缓冲流输出文件
        BufferedInputStream bis = null;
        BufferedOutputStream bos = null;
        try {
            bis = new BufferedInputStream(new FileInputStream(file));
            bos = new BufferedOutputStream(response.getOutputStream());

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = bis.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
            bos.flush();
        } finally {
            // 确保关闭所有资源
            closeQuietly(bis);
            closeQuietly(bos);
        }
    }

    /**
     * 安全关闭可关闭资源
     * 
     * @param closeable 可关闭的资源
     */
    private static void closeQuietly(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                // 记录但不抛出异常
                LOGGER.debug("关闭资源时出错: " + e.getMessage());
            }
        }
    }

    /**
     * 处理文件内容生成 支持多种数据源和输出格式，在异步线程中执行 改造为流式处理，分批次获取和处理数据，避免一次性加载全部数据导致内存溢出
     *
     * @param downloadId    下载ID
     * @param metaParamsStr 元数据参数JSON字符串
     * @param xwlParam      XWL参数
     * @param outputStream  输出流
     * @param type          输出类型（excel）
     * @param sessionInfo   会话信息
     * @throws Exception 当文件处理过程中发生错误时抛出
     */
    public static void processFileContent(String downloadId, String metaParamsStr, String xwlParam,
            OutputStream outputStream, String type, JSONObject sessionInfo) throws Exception {
        boolean taskCompleted = false;
        boolean semaphoreAcquired = false;
        boolean addedToActiveSet = false; // 标记是否已添加到活跃集合

        try {
            // 尝试获取信号量
            semaphoreAcquired = acquireDownloadSemaphore();
            if (!semaphoreAcquired) {
                String errorMsg = "系统繁忙，无法获取下载资源，请稍后重试";
                downloadTasks.put(downloadId + "_error", errorMsg);
                try {
                    Base.map.setValue(DownloadTaskKeyManager.getDownloadErrorKey(downloadId), errorMsg,
                            DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);
                } catch (Exception ex) {
                    LogUtil.warn("Redis存储下载错误信息失败: " + ex.getMessage());
                }
                return;
            }

            // 添加到活跃任务 Set (优化点)
            try {
                RSet<String> activeTasksSet = Base.map.getRedissonClient()
                        .getSet(DownloadTaskKeyManager.getActiveTasksSetKey());
                activeTasksSet.add(downloadId);
                addedToActiveSet = true;
                LogUtil.debug("任务 " + downloadId + " 已添加到活跃任务Set");
            } catch (Exception e) {
                LogUtil.error("将任务添加到活跃Set失败: " + downloadId + ", " + e.getMessage(), e);
                // 如果添加失败，最好释放信号量并返回错误，避免计数不准
                if (semaphoreAcquired) {
                    releaseDownloadSemaphore(downloadId);
                }
                // 记录错误信息到Redis
                String errorMsg = "系统内部错误，无法跟踪任务状态";
                downloadTasks.put(downloadId + "_error", errorMsg);
                try {
                    Base.map.setValue(DownloadTaskKeyManager.getDownloadErrorKey(downloadId), errorMsg,
                            DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);
                } catch (Exception ex) {
                    LogUtil.warn("Redis存储下载错误信息失败: " + ex.getMessage());
                }
                return; // 提前返回
            }

            // 记录处理线程
            Thread currentThread = Thread.currentThread();
            processingThreads.put(downloadId, currentThread);

            // 更新任务心跳
            updateTaskHeartbeat(downloadId); // 心跳仍然需要，用于检测单个任务的活性

            // 检查任务是否被取消
            checkTaskCancellation(downloadId);

            JSONObject allParams = new JSONObject(metaParamsStr);
            JSONObject metaParams;

            // 从allParams中提取__metaParams
            if (allParams.has("__metaParams")) {
                String metaParamsValue = allParams.getString("__metaParams");

                // 解析__metaParams字符串
                metaParams = new JSONObject(metaParamsValue);

                // 如果存在嵌套的__metaParams，使用内部的参数
                if (metaParams.has("__metaParams")) {
                    JSONArray metaParamsArray = metaParams.getJSONArray("__metaParams");
                    if (metaParamsArray.length() > 0) {
                        String innerMetaParams = metaParamsArray.getString(0);
                        metaParams = new JSONObject(innerMetaParams);
                    }
                }
            } else {
                // 如果没有__metaParams，将allParams作为metaParams使用
                metaParams = allParams;
            }

            // 获取必要的参数
            String data = metaParams.optString("data", null);
            String title = metaParams.optString("title", "数据导出");
            String dateFormat = metaParams.optString("dateFormat", "yyyy-MM-dd");
            String timeFormat = metaParams.optString("timeFormat", "HH:mm:ss");
            boolean neptune = metaParams.optBoolean("neptune", false);
            String decimalSeparator = metaParams.optString("decimalSeparator", ".");
            String thousandSeparator = metaParams.optString("thousandSeparator", ",");

            // 从metaParams中获取目标URL
            String targetUrl = StringUtil.isEmpty(metaParams.optString("url")) ? xwlParam : metaParams.optString("url");

            // 处理reportInfo，确保包含必要的结构
            JSONObject reportInfo;
            if (metaParams.has("reportInfo")) {
                reportInfo = metaParams.getJSONObject("reportInfo");
                if (!reportInfo.has("mergeInfo")) {
                    reportInfo.put("mergeInfo", new JSONArray());
                }
            } else {
                reportInfo = new JSONObject();
                reportInfo.put("mergeInfo", new JSONArray());
            }

            // 获取数据
            JSONArray headers = metaParams.optJSONArray("headers");
            if (headers == null) {
                headers = new JSONArray();
                if (Var.debug) {
                    LogUtil.warn("导出Excel时headers为空，将使用默认标题");
                }
            } else {
                // 记录headers的内容，用于调试
                LOGGER.debug("导出Excel的headers: "
                        + headers.toString().substring(0, Math.min(200, headers.toString().length())));

                // 检查headers是否包含text和field字段
                boolean foundIssue = false;
                for (int i = 0; i < headers.length(); i++) {
                    try {
                        JSONObject header = headers.getJSONObject(i);
                        if (!header.has("text")) {
                            header.put("text", "列" + (i + 1));
                            foundIssue = true;
                        }
                        if (!header.has("field") && !header.has("name")) {
                            header.put("field", "column" + (i + 1));
                            foundIssue = true;
                        }
                    } catch (Exception e) {
                        if (Var.debug) {
                            LogUtil.warn("处理header[" + i + "]异常: " + e.getMessage());
                        }
                    }
                }

                if (foundIssue && Var.debug) {
                    LogUtil.warn("导出Excel时修复了headers的问题");
                }
            }

            // 确保使用Excel格式导出
            if (!"excel".equals(type)) {
                if (Var.debug) {
                    LogUtil.warn("不支持的导出类型: " + type + "，已自动转换为Excel格式");
                }
                type = "excel";
            }

            // 直接使用从客户端传来的数据
            if (data != null) {
                // 使用客户端提供的数据
                JSONArray records;
                try {
                    if (data.trim().startsWith("[")) {
                        records = new JSONArray(data);
                    } else {
                        JSONObject dataObj = new JSONObject(data);
                        if (dataObj.has("rows")) {
                            records = dataObj.getJSONArray("rows");
                        } else {
                            records = new JSONArray();
                        }
                    }

                    // 数据量小时，直接使用一次性导出
                    if (records.length() <= 500) {
                        DataOutput.outputExcel(outputStream, headers, records, title, reportInfo, dateFormat,
                                timeFormat, neptune);
                    } else {
                        // 数据量大时使用流式导出
                        StreamingExcelContext context = null;
                        try {
                            // 初始化流式Excel
                            context = DataOutputEx.streamingExcel(outputStream, headers, null, title, reportInfo,
                                    dateFormat, timeFormat, neptune, 100);

                            // 分批追加数据
                            int batchSize = 100;
                            int totalRecords = records.length();

                            for (int i = 0; i < totalRecords; i += batchSize) {
                                // 检查任务是否被取消
                                checkTaskCancellation(downloadId);

                                // 创建批次数据
                                JSONArray batchRecords = new JSONArray();
                                int end = Math.min(i + batchSize, totalRecords);

                                for (int j = i; j < end; j++) {
                                    batchRecords.put(records.get(j));
                                }

                                // 追加批次数据
                                DataOutputEx.appendRecords(context, batchRecords);

                                // 更新进度
                                updateProgress(downloadId, 1, end, totalRecords);
                            }

                            // 完成Excel写入
                            DataOutputEx.finishExcel(context);
                        } catch (Exception e) {
                            // 确保资源释放
                            if (context != null) {
                                try {
                                    DataOutputEx.finishExcel(context);
                                } catch (Exception ex) {
                                    // 忽略关闭时的异常
                                }
                            }
                            throw e;
                        }
                    }
                } catch (Exception e) {
                    LogUtil.error("解析客户端数据失败，任务ID: " + downloadId + ", 错误: " + e.getMessage());
                    throw new Exception("解析数据失败：" + e.getMessage());
                }
            } else {
                // 服务端获取数据 - 改为流式处理模式

                // 分页参数初始化
                int pageSize = Var.limitRecords; // 每页记录数，按照最大单页输出数据量
                int currentPage = 1;
                int start = 0;
                boolean hasMoreData = true;
                int finalTotalRecords = 0; // 用于跟踪最终的总记录数

                // 创建流式Excel上下文
                StreamingExcelContext context = null;

                try {
                    // 初始化流式Excel
                    context = DataOutputEx.streamingExcel(outputStream, headers, null, title, reportInfo, dateFormat,
                            timeFormat, neptune, 100);

                    // 分页循环获取数据
                    while (hasMoreData) {
                        // 检查任务是否被取消
                        checkTaskCancellation(downloadId);

                        // 创建当前页请求参数的副本
                        JSONObject pageParams = new JSONObject(allParams.toString());

                        // 更新分页参数到请求中
                        pageParams.put("page", currentPage);
                        pageParams.put("start", start);
                        pageParams.put("limit", pageSize);

                        // 创建请求对象
                        CustomRequest customReq = new CustomRequest();

                        // 从 Spring Session 仓库获取 session
                        if (sessionInfo != null && sessionInfo.has("JSESSIONID")) {
                            String sessionId = sessionInfo.getString("JSESSIONID");
                            Session springSession = UserList.getSessionRepository().findById(sessionId);

                            if (springSession != null) {
                                CustomSession customSession = new CustomSession();
                                customSession.setId(springSession.getId());
                                customSession.setCreationTime(springSession.getCreationTime().toEpochMilli());
                                customSession.setLastAccessedTime(springSession.getLastAccessedTime().toEpochMilli());
                                customSession.setMaxInactiveInterval(
                                        (int) springSession.getMaxInactiveInterval().getSeconds());
                                customSession.setNew(
                                        springSession.getCreationTime().equals(springSession.getLastAccessedTime()));
                                customSession.setValid(true);

                                // 复制所有属性
                                for (String attrName : springSession.getAttributeNames()) {
                                    customSession.setAttribute(attrName, springSession.getAttribute(attrName));
                                }

                                customReq.setSession(customSession);
                            }
                        }

                        // 复制原始请求中的所有参数到 CustomRequest
                        Map<String, String[]> paramMap = customReq.getParameterMap();
                        if (paramMap instanceof HashMap) {
                            @SuppressWarnings("unchecked")
                            HashMap<String, String[]> params = (HashMap<String, String[]>) paramMap;

                            // 使用pageParams中解析出的所有参数
                            for (String key : pageParams.keySet()) {
                                if (!"__metaParams".equals(key)) { // 排除__metaParams参数，稍后单独处理
                                    Object value = pageParams.get(key);
                                    String[] paramValues;

                                    if (value instanceof JSONArray) {
                                        JSONArray array = (JSONArray) value;
                                        if (array.length() > 0) {
                                            Object firstElement = array.get(0);
                                            if (firstElement instanceof JSONArray) {
                                                // 处理嵌套数组 [["value"]]
                                                JSONArray innerArray = (JSONArray) firstElement;
                                                paramValues = new String[innerArray.length()];
                                                for (int i = 0; i < innerArray.length(); i++) {
                                                    paramValues[i] = String.valueOf(innerArray.get(i));
                                                }
                                            } else {
                                                // 处理普通数组 ["value"]
                                                paramValues = new String[array.length()];
                                                for (int i = 0; i < array.length(); i++) {
                                                    paramValues[i] = String.valueOf(array.get(i));
                                                }
                                            }
                                        } else {
                                            paramValues = new String[0];
                                        }
                                    } else if (value instanceof String) {
                                        paramValues = new String[] { (String) value };
                                    } else if (value instanceof Number || value instanceof Boolean) {
                                        paramValues = new String[] { value.toString() };
                                    } else if (value instanceof JSONObject) {
                                        paramValues = new String[] { value.toString() };
                                    } else {
                                        paramValues = new String[] { String.valueOf(value) };
                                    }

                                    params.put(key, paramValues);

                                    // 设置属性
                                    if (paramValues.length > 0) {
                                        customReq.setAttribute(key, paramValues[0]);
                                    }
                                }
                            }
                        }

                        // __metaParams特殊处理，转为string[]类型
                        if (pageParams.has("__metaParams")) {
                            customReq.setAttribute("__metaParams", pageParams.get("__metaParams").toString());
                        }

                        // 请求后端数据
                        try {
                            CustomResponse customResp = new CustomResponse(null);

                            LOGGER.debug("准备调用XWL，targetUrl: " + targetUrl);
                            // 构造URL为m?xwl=格式
                            String url = StringUtil.format("m?xwl={0}", targetUrl);
                            // 使用WebUtil.include调用XWL
                            WebUtil.include(customReq, customResp, url);
                            LOGGER.debug("完成调用XWL，获取返回内容");

                            // 使用WbUtil.getResponseString处理响应
                            String respText = WbUtil.getResponseString(customResp);
                            LOGGER.debug("响应内容长度: " + (respText != null ? respText.length() : "null"));

                            // 记录返回内容的前200个字符以帮助诊断
                            if (respText != null && respText.length() > 0) {
                                LOGGER.debug("响应内容前200字符: " + respText.substring(0, Math.min(respText.length(), 200)));
                            } else {
                                if (Var.debug) {
                                    LogUtil.warn("响应内容为空");
                                }
                                throw new Exception("XWL响应内容为空");
                            }

                            String jsonData = null;

                            // 解析返回的数据格式，同时支持<textarea>和/*{JSON}*/格式
                            if (respText.startsWith("<textarea>") && respText.endsWith("</textarea>")) {
                                // 传统json response格式
                                LOGGER.debug("识别为<textarea>格式");
                                jsonData = respText.substring(10, respText.length() - 11);
                                JSONObject responseObject = new JSONObject(jsonData);
                                jsonData = responseObject.optString("value");
                                if (!responseObject.optBoolean("success")) {
                                    if (Var.debug) {
                                        LogUtil.error("JSON response标记失败: " + jsonData);
                                    }
                                    throw new Exception(jsonData);
                                }
                            } else if (respText.startsWith("/*{JSON}*/") && respText.endsWith("/*{/JSON}*/")) {
                                // 新json response格式
                                LOGGER.debug("识别为/*{JSON}*/格式");
                                jsonData = respText.substring(10, respText.length() - 11);
                                JSONObject responseObject = new JSONObject(jsonData);
                                jsonData = responseObject.optString("value");
                                if (!responseObject.optBoolean("success")) {
                                    if (Var.debug) {
                                        LogUtil.error("JSON response标记失败: " + jsonData);
                                    }
                                    throw new Exception(jsonData);
                                }
                            } else {
                                // Ajax格式
                                respText = respText.trim();
                                if (!respText.startsWith("{") || !respText.endsWith("}")) {
                                    if (Var.debug) {
                                        LogUtil.error("响应内容不是有效的JSON格式: " + respText);
                                    }
                                    throw new Exception(respText);
                                }
                                LOGGER.debug("识别为Ajax格式");
                                jsonData = respText;
                            }

                            // 检查任务是否被取消
                            checkTaskCancellation(downloadId);

                            // 解析数据获取当前页记录
                            try {
                                JSONObject responseObject = new JSONObject(jsonData);
                                LOGGER.debug("成功将响应解析为JSONObject");

                                // 检查响应是否成功，优先将缺失 success 字段视为失败
                                if (!responseObject.optBoolean("success", false)) { // Default to false for safety
                                    LOGGER.error("导出[{}-{}]数据问题：{}", downloadId, targetUrl, responseObject.toString());
                                    // 优先尝试获取 "errMsg"，然后是 "message"，最后使用默认错误信息
                                    String errorMessage = responseObject.optString("errMsg", // Check errMsg first
                                            responseObject.optString("message", "未知错误")); // Then check message
                                    throw new Exception(errorMessage);
                                }

                                LOGGER.debug("开始提取记录数据");
                                JSONArray currentPageRecords = getRowsFromResponse(responseObject);
                                LOGGER.debug("成功提取记录数据，记录数: "
                                        + (currentPageRecords != null ? currentPageRecords.length() : "null"));

                                int totalRecords = getTotalFromResponse(responseObject);
                                LOGGER.debug("获取总记录数: " + totalRecords);

                                if (totalRecords > 0) {
                                    finalTotalRecords = totalRecords; // 保存总记录数用于最后的进度更新
                                }

                                // 如果当前页有数据，处理这一页
                                if (currentPageRecords != null && currentPageRecords.length() > 0) {
                                    // 追加批次数据到Excel
                                    LOGGER.debug("准备追加数据到Excel，记录数: " + currentPageRecords.length());

                                    try {
                                        // 记录第一条记录的结构，帮助诊断
                                        if (currentPageRecords.length() > 0) {
                                            Object firstRecord = currentPageRecords.get(0);
                                            LOGGER.debug("第一条记录类型: " + firstRecord.getClass().getName());
                                            if (firstRecord instanceof JSONObject) {
                                                JSONObject record = (JSONObject) firstRecord;
                                                StringBuilder keys = new StringBuilder("第一条记录字段: ");
                                                for (String key : record.keySet()) {
                                                    keys.append(key).append(", ");
                                                }
                                                LOGGER.debug(keys.toString());
                                            } else if (firstRecord instanceof JSONArray) {
                                                LOGGER.debug(
                                                        "第一条记录是JSONArray，长度: " + ((JSONArray) firstRecord).length());
                                            }
                                        }

                                        DataOutputEx.appendRecords(context, currentPageRecords);
                                        LOGGER.debug("成功追加数据到Excel");
                                    } catch (Exception e) {
                                        LogUtil.error("追加数据到Excel失败: " + e.getMessage(), e);
                                        throw e;
                                    }

                                    // 更新处理进度
                                    updateProgress(downloadId, currentPage, start + currentPageRecords.length(),
                                            totalRecords);
                                } else {
                                    if (Var.debug) {
                                        LogUtil.warn("当前页没有记录数据");
                                    }
                                }

                                // 判断是否还有更多数据
                                if (currentPageRecords == null || currentPageRecords.length() < pageSize
                                        || (totalRecords > 0 && start + currentPageRecords.length() >= totalRecords)) {
                                    hasMoreData = false;
                                    LOGGER.debug("没有更多数据，分页结束");
                                } else {
                                    // 更新下一页的参数
                                    currentPage++;
                                    start += currentPageRecords.length();
                                    LOGGER.debug("准备获取下一页，页码: " + currentPage + ", 起始位置: " + start);
                                }
                            } catch (Exception e) {
                                LogUtil.error("解析JSONObject失败: " + e.getMessage(), e);
                                throw e;
                            }

                        } catch (Exception e) {
                            LogUtil.error(
                                    "获取分页数据失败，任务ID: " + downloadId + ", 页码: " + currentPage + ", 错误: " + e.getMessage(),
                                    e);
                            throw e;
                        }
                    }

                    // 完成数据写入
                    DataOutputEx.finishExcel(context);

                    // 更新进度为100%
                    updateFinalProgress(downloadId, finalTotalRecords);

                } catch (Exception e) {
                    // 确保释放资源
                    if (context != null) {
                        try {
                            DataOutputEx.finishExcel(context);
                        } catch (Exception ex) {
                            // 忽略关闭时的异常
                        }
                    }
                    throw e;
                }
            }

            // 标记任务完成
            taskCompleted = true;

        } catch (InterruptedException ie) {
            if (Var.debug) {
                LogUtil.warn("下载任务被中断: " + downloadId);
            }
            Thread.currentThread().interrupt();
            // 对于中断异常不需要记录错误信息，这是正常的任务取消流程
        } catch (Exception e) {
            LogUtil.error("下载任务执行异常: " + downloadId + ", " + e.getMessage(), e);

            // 保存错误信息到本地缓存和Redis
            String errorMsg = "下载处理失败: " + e.getMessage();
            downloadTasks.put(downloadId + "_error", errorMsg);
            try {
                Base.map.setValue(DownloadTaskKeyManager.getDownloadErrorKey(downloadId), errorMsg,
                        DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);

                // 确保错误信息能被前端获取到，移除完成标记
                downloadTasks.remove(downloadId + "_completed");
                Base.map.delKey(DownloadTaskKeyManager.getDownloadTaskKey(downloadId) + "_completed");

                // 延迟删除错误信息，确保前端能读取到
                final String finalDownloadId = downloadId;
                CompletableFuture.runAsync(() -> {
                    try {
                        // 等待30秒，给前端足够时间获取错误信息
                        Thread.sleep(30000);
                        // 再次检查是否已被清理
                        if (downloadTasks.containsKey(finalDownloadId + "_error")) {
                            LOGGER.debug("延迟清理错误信息: " + finalDownloadId);
                        }
                    } catch (Exception ignore) {
                    }
                });
            } catch (Exception ex) {
                LogUtil.warn("Redis存储下载错误信息失败: " + ex.getMessage());
            }
            throw e; // 重新抛出异常，让调用者知道任务失败
        } finally {
            // 统一资源清理
            try {
                // 关闭输出流
                if (outputStream != null) {
                    try {
                        outputStream.flush();
                        outputStream.close();
                    } catch (Exception e) {
                        LogUtil.error("关闭输出流失败: " + e.getMessage());
                    }
                }

                // 从活跃任务 Set 中移除 (优化点)
                if (addedToActiveSet) { // 只有成功添加了才需要移除
                    try {
                        RSet<String> activeTasksSet = Base.map.getRedissonClient()
                                .getSet(DownloadTaskKeyManager.getActiveTasksSetKey());
                        activeTasksSet.remove(downloadId);
                        LogUtil.debug("任务 " + downloadId + " 已从活跃任务Set移除");
                    } catch (Exception e) {
                        LogUtil.error("从活跃Set移除任务失败: " + downloadId + ", " + e.getMessage(), e);
                        // 即使移除失败，也应继续后续清理
                    }
                }

                // 从处理线程集合中移除
                Thread currentThread = processingThreads.remove(downloadId);
                if (currentThread == null) {
                    if (Var.debug) {
                        LogUtil.warn("处理线程已不在集合中: " + downloadId);
                    }
                }

                // 释放信号量
                if (semaphoreAcquired) {
                    try {
                        releaseDownloadSemaphore(downloadId); // release 内部会减少 Redis 计数器
                        semaphoreAcquired = false; // 避免重复释放
                    } catch (Exception e) {
                        LogUtil.error("释放信号量失败: " + e.getMessage());
                    }
                }

                // 如果任务未完成且发生了异常，才执行清理
                if (!taskCompleted) {
                    // 检查是否有异常发生
                    boolean hasError = downloadTasks.containsKey(downloadId + "_error") || Base.map.getRedissonClient()
                            .getKeys().countExists(DownloadTaskKeyManager.getDownloadErrorKey(downloadId)) > 0; // Check
                                                                                                                // Redis
                                                                                                                // too
                    if (hasError) {
                        LogUtil.error("任务未完成且存在错误，执行资源清理: " + downloadId);
                        try {
                            // 确保清理函数会移除活跃 Set 中的任务
                            cleanupDownloadTask(downloadId, true); // 确保这个函数会移除 active_tasks set 中的 ID
                        } catch (Exception e) {
                            LogUtil.error("清理资源失败: " + downloadId + ", " + e.getMessage());
                        }
                    } else {
                        LogUtil.warn("任务未标记完成但无错误，保留资源: " + downloadId);
                    }
                } else {
                    // 任务成功完成，执行标准清理
                    try {
                        // cleanupDownloadTask 会处理 Redis key 和 Set/ZSet
                        cleanupDownloadTask(downloadId, true);
                    } catch (Exception e) {
                        LogUtil.error("标准清理资源失败: " + downloadId + ", " + e.getMessage());
                    }
                }

                // 完全移除对monitorThreadPool的调用
                // 仅记录当前任务完成状态
                LOGGER.info(String.format("任务[%s]完成 - 当前活跃线程数: %d", downloadId, activeThreadCount.get()));

            } catch (Exception e) {
                LogUtil.error("清理下载任务资源时出错: " + e.getMessage());
                // 最后尝试释放信号量
                if (semaphoreAcquired) {
                    try {
                        releaseDownloadSemaphore(downloadId);
                    } catch (Exception ex) {
                        // 忽略异常
                    }
                }
                // 尝试从活跃Set移除，以防万一
                if (addedToActiveSet) {
                    try {
                        RSet<String> activeTasksSet = Base.map.getRedissonClient()
                                .getSet(DownloadTaskKeyManager.getActiveTasksSetKey());
                        activeTasksSet.removeAsync(downloadId);
                    } catch (Exception remEx) {
                    }
                }

            }
        }
    }

    /**
     * 检查任务是否已被取消，如被取消则抛出中断异常
     *
     * @param downloadId 下载任务ID
     * @throws InterruptedException 如果任务被取消或线程被中断
     */
    private static void checkTaskCancellation(String downloadId) throws InterruptedException {
        // 首先检查线程中断状态
        if (Thread.currentThread().isInterrupted()) {
            throw new InterruptedException("任务处理线程被中断");
        }

        // 检查本地取消标记
        Boolean cancelled = cancelledTasks.get(downloadId);
        if (Boolean.TRUE.equals(cancelled)) {
            throw new InterruptedException("任务已被取消");
        }

        // 检查Redis中的取消标记
        try {
            String cancelledFromRedis = (String) Base.map
                    .getValue(DownloadTaskKeyManager.getDownloadCancelledKey(downloadId));
            if ("true".equals(cancelledFromRedis)) {
                throw new InterruptedException("任务已被取消");
            }
        } catch (Exception e) {
            // 如果获取Redis值失败，忽略错误继续执行
            LogUtil.warn("检查任务取消状态失败，但继续执行: " + e.getMessage());
        }

        // 正常情况下，更新心跳
        updateTaskHeartbeat(downloadId);
    }

    /**
     * 清理下载任务相关的资源
     *
     * @param downloadId 下载任务ID
     */
    private static void cleanupDownloadTask(String downloadId) {
        cleanupDownloadTask(downloadId, true);
    }

    /**
     * 清理下载任务相关的资源 (已修改，包含 Set/ZSet 清理)
     *
     * @param downloadId          下载任务ID
     * @param removeCancelledFlag 是否删除取消标记，通常为true，但取消任务时保留标记可便于诊断
     */
    public static void cleanupDownloadTask(String downloadId, boolean removeCancelledFlag) {
        if (StringUtil.isEmpty(downloadId)) {
            LogUtil.warn("尝试清理空的下载ID");
            return;
        }

        try {
            // 移除本地缓存
            lastAccessTimes.remove(downloadId);
            if (removeCancelledFlag) {
                cancelledTasks.remove(downloadId);
            }
            taskFutures.remove(downloadId);
            processingThreads.remove(downloadId);
            downloadTasks.remove(downloadId);
            downloadTasks.remove(downloadId + "_error");
            downloadTasks.remove(downloadId + "_start_time");
            downloadTasks.remove(downloadId + "_completed"); // 确保移除完成标记

            // 移除 Redis 中的相关 Key (使用 Redisson 客户端)
            String taskKey = DownloadTaskKeyManager.getDownloadTaskKey(downloadId);
            String heartbeatKey = DownloadTaskKeyManager.getDownloadHeartbeatKey(downloadId);
            String progressKey = DownloadTaskKeyManager.getDownloadProgressKey(downloadId);
            String progressPercentKey = DownloadTaskKeyManager.getDownloadProgressPercentageKey(downloadId);
            String progressTimestampKey = DownloadTaskKeyManager.getDownloadProgressTimestampKey(downloadId);
            String errorKey = DownloadTaskKeyManager.getDownloadErrorKey(downloadId);
            String cancelledKey = DownloadTaskKeyManager.getDownloadCancelledKey(downloadId);
            String accessKey = DownloadTaskKeyManager.getDownloadAccessKey(downloadId); // 旧的访问key
            String completedKey = taskKey + "_completed"; // 完成标记Key
            String startTimeKey = DownloadTaskKeyManager.getDownloadStartTimeKey(downloadId);
            // String zombieKey =
            // DownloadTaskKeyManager.getDownloadZombieThreadKey(downloadId); // 旧的僵尸 key
            // (REMOVED)

            // 批量删除 Redis Keys
            Base.map.getRedissonClient().getKeys().deleteAsync(taskKey, heartbeatKey, progressKey, progressPercentKey,
                    progressTimestampKey, errorKey, cancelledKey, accessKey, completedKey, startTimeKey // Removed
                                                                                                        // zombieKey
            );

            // 从 Set 和 SortedSet 中移除 (优化点)
            Base.map.getRedissonClient().getSet(DownloadTaskKeyManager.getActiveTasksSetKey()).removeAsync(downloadId);
            Base.map.getRedissonClient().getScoredSortedSet(DownloadTaskKeyManager.getAccessTimesSortedSetKey())
                    .removeAsync(downloadId);
            // 如果任务也被标记为僵尸，也从僵尸Set中移除
            Base.map.getRedissonClient().getSet(DownloadTaskKeyManager.getZombieTasksSetKey()).removeAsync(downloadId);

            // 清理本地文件 (如果文件路径存在)
            // 找到文件路径，可能在本地 downloadTasks 或 Redis 中
            String filePath = downloadTasks.get(downloadId);
            if (filePath == null) {
                try {
                    filePath = (String) Base.map.getValue(taskKey);
                } catch (Exception e) {
                    // 忽略获取路径失败
                }
            }
            if (filePath != null) {
                try {
                    File file = new File(filePath);
                    if (file.exists()) {
                        if (file.delete()) {
                            LogUtil.debug("已删除下载文件: " + filePath);
                        } else {
                            LogUtil.warn("删除下载文件失败: " + filePath);
                        }
                    }
                } catch (Exception e) {
                    LogUtil.warn("清理下载文件时出错: " + filePath + ", " + e.getMessage());
                }
            }

        } catch (Exception e) {
            LogUtil.error(StringUtil.format("清理下载任务 {0} 资源时出错: {1}", downloadId, e.getMessage()), e);
        }
    }

    /**
     * 取消下载任务 当用户主动取消下载时调用此方法
     *
     * @param request  HTTP请求对象，包含下载ID
     * @param response HTTP响应对象
     * @throws Exception 当取消过程中发生错误时抛出
     */
    public static void cancelDownload(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String downloadId = request.getParameter("downloadId");
        if (StringUtil.isEmpty(downloadId)) {
            WebUtil.send(response, "{\"success\":false,\"message\":\"下载ID不能为空\"}", true);
            return;
        }

        LOGGER.debug("收到取消下载请求: " + downloadId);

        // 使用任务管理器取消任务
        DownloadTaskManager.cancelTask(downloadId);

        // 返回取消成功响应
        JSONObject result = new JSONObject();
        result.put("success", true);
        WebUtil.send(response, result.toString(), true);
    }

    /**
     * 等待所有下载线程完成
     *
     * @param waitTimeSeconds 等待任务完成的最大时间（秒）
     * @return 是否成功等待完成
     */
    public static boolean waitForThreadsToComplete(int waitTimeSeconds) {
        // 如果没有活跃线程，立即返回成功，无需等待
        if (activeThreadCount.get() == 0) {
            return true;
        }

        LOGGER.debug("等待所有下载线程完成...");
        long startTime = System.currentTimeMillis();

        while (activeThreadCount.get() > 0) {
            try {
                // 检查超时
                if ((System.currentTimeMillis() - startTime) > waitTimeSeconds * 1000) {
                    LogUtil.warn("等待下载线程完成超时，仍有活跃线程: " + activeThreadCount.get());
                    return false;
                }

                // 减少等待间隔，加快响应速度
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                LogUtil.warn("等待下载线程完成被中断");
                return false;
            }
        }

        LOGGER.debug("所有下载线程已完成");
        return true;
    }

    /**
     * 重新初始化下载线程池 此方法可在线程池被关闭后调用，以重新创建线程池
     *
     * @param threadCount 线程池中的线程数量
     */
    public static synchronized void reinitializeThreadPool(int threadCount) {
        if (threadCount <= 0) {
            threadCount = 10; // 默认线程数
        }

        // 检查当前线程池状态
        if (waitForThreadsToComplete(60)) {
            waitForThreadsToComplete(60);
        } else {
            LogUtil.warn("FilePush下载线程池当前仍在运行，无需重新初始化");
        }
    }

    /**
     * 获取下载任务的状态信息 此方法返回当前各集合的大小和线程池状态，便于监控
     *
     * @return 包含状态信息的JSONObject
     */
    public static JSONObject getStatusInfo() {
        JSONObject status = new JSONObject();

        // 收集各集合的大小
        status.put("downloadTasksSize", downloadTasks.size());
        status.put("lastAccessTimesSize", lastAccessTimes.size());
        status.put("cancelledTasksSize", cancelledTasks.size());
        status.put("taskFuturesSize", taskFutures.size());
        status.put("processingThreadsSize", processingThreads.size());

        // 收集信号量信息
        status.put("availablePermits", downloadSemaphore.availablePermits());
        status.put("queueLength", downloadSemaphore.getQueueLength());

        // 线程池状态（无法直接获取线程池详细信息，只能判断是否关闭）
        status.put("threadPoolShutdown", waitForThreadsToComplete(60));
        status.put("threadPoolTerminated", waitForThreadsToComplete(60));

        // 添加一些任务ID示例，最多10个
        JSONArray taskIds = new JSONArray();
        int count = 0;
        for (String id : downloadTasks.keySet()) {
            if (!id.contains("_error") && !id.contains("_start_time")) {
                taskIds.put(id);
                count++;
                if (count >= 10)
                    break;
            }
        }
        status.put("sampleTaskIds", taskIds);

        return status;
    }

    /**
     * 清理所有过期的下载任务 此方法用于清理长时间未访问的下载任务，防止资源泄漏
     *
     * @param expireTimeMs 过期时间（毫秒），超过此时间未访问的任务将被清理
     * @return 清理的任务数量
     */
    public static int cleanupExpiredTasks(long expireTimeMs) {
        if (expireTimeMs <= 0) {
            expireTimeMs = TASK_TIMEOUT_MS * 2; // 默认使用超时时间的两倍
        }

        int cleanedCount = 0;
        long currentTime = System.currentTimeMillis();

        // 创建一个要清理的任务ID列表，避免在遍历过程中修改集合
        List<String> tasksToClean = new ArrayList<>();

        // 查找过期的任务
        for (Map.Entry<String, Long> entry : lastAccessTimes.entrySet()) {
            String downloadId = entry.getKey();
            Long lastAccessTime = entry.getValue();

            if (lastAccessTime != null && (currentTime - lastAccessTime) > expireTimeMs) {
                tasksToClean.add(downloadId);
            }
        }

        // 清理过期任务
        for (String downloadId : tasksToClean) {
            try {
                cleanupDownloadTask(downloadId);
                cleanedCount++;
            } catch (Exception e) {
                LogUtil.warn(StringUtil.format("清理过期下载任务 {0} 时出错: {1}", downloadId, e.getMessage()));
            }
        }

        return cleanedCount;
    }

    /**
     * 获取下载任务和信号量的诊断信息 可用于检查当前下载状态和信号量计数是否一致
     *
     * @return 包含诊断信息的JSON对象
     */
    public static JSONObject getDownloadDiagnosticInfo() {
        JSONObject info = new JSONObject();
        try {
            // 获取当前信号量计数
            long semaphoreCount = Base.map.getRedissonClient()
                    .getAtomicLong(DownloadTaskKeyManager.getDownloadSemaphoreKey()).get();
            info.put("semaphoreCount", semaphoreCount);

            // 获取本地正在进行的任务数量
            info.put("localTaskCount", processingThreads.size());
            info.put("localTaskIds", new JSONArray(processingThreads.keySet()));

            // 获取 Redis 中的任务信息 (使用 Set/Scan)
            RSet<String> activeTasksSet = Base.map.getRedissonClient()
                    .getSet(DownloadTaskKeyManager.getActiveTasksSetKey());
            long activeTaskCount = activeTasksSet.size();
            info.put("redisActiveTaskCount (Set)", activeTaskCount);

            // Scan heartbeat keys (as a secondary check or if Set is not fully trusted yet
            // - CAN BE REMOVED if Set is trusted)
            List<String> heartbeatKeys = Base.map.scan(DownloadTaskKeyManager.getDownloadHeartbeatKey("*"));
            int heartbeatCount = heartbeatKeys != null ? heartbeatKeys.size() : 0;
            info.put("activeHeartbeatCount (Scan - for reference)", heartbeatCount);

            List<String> cancelledKeys = Base.map.scan(DownloadTaskKeyManager.getDownloadCancelledKey("*")); // Scan
                                                                                                             // cancelled
                                                                                                             // keys
                                                                                                             // (can be
                                                                                                             // removed
                                                                                                             // if not
                                                                                                             // needed
                                                                                                             // for
                                                                                                             // diagnostics)
            RSet<String> zombieSet = Base.map.getRedissonClient().getSet(DownloadTaskKeyManager.getZombieTasksSetKey()); // Read
                                                                                                                         // zombie
                                                                                                                         // set

            info.put("cancelledTaskCount (Scan - for reference)", cancelledKeys != null ? cancelledKeys.size() : 0);
            info.put("zombieTaskCount (Set)", zombieSet.size());

            // 收集活跃任务ID (from Set)
            JSONArray activeTaskIds = new JSONArray();
            int count = 0;
            // Use iterator for potentially large sets
            Iterator<String> iterator = activeTasksSet.iterator();
            while (iterator.hasNext() && count < 50) { // Limit sample size
                activeTaskIds.put(iterator.next());
                count++;
            }
            info.put("sampleActiveTaskIds (Set)", activeTaskIds);

            // 收集已取消任务ID (from Scan)
            JSONArray cancelledTaskIds = new JSONArray();
            if (cancelledKeys != null) {
                count = 0;
                for (String key : cancelledKeys) {
                    if (count >= 50)
                        break; // Limit sample size
                    String taskId = key.substring(DownloadTaskKeyManager.getDownloadCancelledKey("").length());
                    cancelledTaskIds.put(taskId);
                    count++;
                }
            }
            info.put("sampleCancelledTaskIds (Scan)", cancelledTaskIds);

            // 收集僵尸任务ID (from Set)
            JSONArray zombieTaskIds = new JSONArray();
            count = 0;
            Iterator<String> zombieIterator = zombieSet.iterator();
            while (zombieIterator.hasNext() && count < 50) { // Limit sample size
                zombieTaskIds.put(zombieIterator.next());
                count++;
            }
            info.put("sampleZombieTaskIds (Set)", zombieTaskIds);

            // 检查是否有未正常释放信号量的已取消任务
            int anomalyCount = 0;
            // Check if cancelled tasks (from scan) are still in the active set
            if (cancelledKeys != null) {
                for (String cancelledKey : cancelledKeys) {
                    String taskId = cancelledKey.substring(DownloadTaskKeyManager.getDownloadCancelledKey("").length());
                    if (activeTasksSet.contains(taskId)) {
                        anomalyCount++;
                    }
                }
            }
            info.put("cancelledButActiveCount (Set Check)", anomalyCount);

            // 状态评估 (compare semaphore count with active set count)
            boolean countsMatch = semaphoreCount == activeTaskCount;
            info.put("countsMatch (Semaphore vs Set)", countsMatch);
            info.put("diagnosisTime", System.currentTimeMillis());

            if (anomalyCount > 0) {
                info.put("status", "存在已取消但仍标记为活跃的任务(Set)");
            } else if (!countsMatch) {
                info.put("status", "信号量计数与活跃任务Set大小不匹配");
            } else {
                info.put("status", "正常");
            }

        } catch (Exception e) {
            info.put("error", "获取诊断信息失败: " + e.getMessage());
            LogUtil.error("获取诊断信息失败", e); // Log stack trace
        }
        return info;
    }

    /**
     * 获取下载任务状态和信号量诊断信息
     *
     * @param request  HTTP请求对象
     * @param response HTTP响应对象
     * @throws Exception 处理过程中可能抛出的异常
     */
    public static void getDownloadStatus(HttpServletRequest request, HttpServletResponse response) throws Exception {
        JSONObject result = new JSONObject();

        try {
            // 获取诊断信息
            JSONObject diagnosticInfo = getDownloadDiagnosticInfo();
            result.put("success", true);
            result.put("diagnosticInfo", diagnosticInfo);

            // 检查是否需要修复异常任务
            String fixAnomalies = WebUtil.fetch(request, "fixAnomalies");
            if ("true".equals(fixAnomalies)) {
                int fixedCount = fixAnomalyCancelledTasks();
                result.put("anomalyFixPerformed", true);
                result.put("anomalyFixedCount", fixedCount);

                // 重新获取诊断信息
                JSONObject updatedInfo = getDownloadDiagnosticInfo();
                result.put("updatedDiagnosticInfo", updatedInfo);
            }

            // 检查是否需要重置计数器
            String reset = WebUtil.fetch(request, "reset");
            if ("true".equals(reset)) {
                boolean resetSuccess = resetDownloadSemaphoreCounter();
                result.put("resetPerformed", true);
                result.put("resetSuccess", resetSuccess);

                // 重新获取诊断信息
                JSONObject updatedInfo = getDownloadDiagnosticInfo();
                result.put("updatedDiagnosticInfo", updatedInfo);
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取下载状态信息失败: " + e.getMessage());
        }

        WebUtil.send(response, result.toString(), true);
    }

    /**
     * 重置分布式信号量计数器 用于手动重置计数器，修复可能的计数错误
     *
     * @return 重置是否成功
     */
    public static boolean resetDownloadSemaphoreCounter() {
        try {
            long oldValue = Base.map.getRedissonClient().getAtomicLong(DownloadTaskKeyManager.getDownloadSemaphoreKey())
                    .getAndSet(0);
            if (oldValue > 0 && Var.debug) {
                LogUtil.info("重置分布式下载信号量计数器，旧值为: " + oldValue);
            }
            return true;
        } catch (Exception e) {
            LogUtil.error("重置分布式下载信号量计数器失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 定时清理过期的下载任务和文件 此方法可以由系统定时任务调用，例如每小时执行一次
     *
     * @return 清理的任务数量
     */
    public static int cleanupExpiredDownloadTasks() {
        int cleanedCount = 0;
        long expireTimeMs = DOWNLOAD_EXPIRE_HOURS * 60 * 60 * 1000L;
        long cutoffTime = System.currentTimeMillis() - expireTimeMs;

        try {
            RScoredSortedSet<String> accessTimesZSet = Base.map.getRedissonClient()
                    .getScoredSortedSet(DownloadTaskKeyManager.getAccessTimesSortedSetKey());

            // 获取所有 score 小于 cutoffTime 的任务 ID (优化点：使用 ZRANGEBYSCORE)
            // ZRANGEBYSCORE key -inf cutoffTime
            // 使用 valueRangeByScore 而不是 valueRange
            Collection<String> expiredTaskIds = accessTimesZSet.valueRange(Double.NEGATIVE_INFINITY, true,
                    (double) cutoffTime, true);

            if (expiredTaskIds == null || expiredTaskIds.isEmpty()) {
                return 0;
            }

            if (Var.debug) {
                LogUtil.info("发现 " + expiredTaskIds.size() + " 个可能过期的任务 (基于访问时间)");
            }

            // 遍历可能过期的任务并执行清理
            for (String downloadId : expiredTaskIds) {
                try {
                    // 在清理前可以再做一次检查，例如检查任务心跳，防止误删活跃但长时间未被checkStatus的任务
                    boolean isActive = false;
                    try {
                        String heartbeatTimeStr = (String) Base.map
                                .getValue(DownloadTaskKeyManager.getDownloadHeartbeatKey(downloadId));
                        if (heartbeatTimeStr != null) {
                            long heartbeatTime = Long.parseLong(heartbeatTimeStr);
                            // 如果心跳在过期时间内，则认为它还活跃，不清理
                            if (System.currentTimeMillis() - heartbeatTime <= expireTimeMs) { // 使用 expireTimeMs
                                                                                              // 作为心跳活跃阈值
                                isActive = true;
                                if (Var.debug) {
                                    LogUtil.info(StringUtil.format("任务 {0} 访问超时但心跳活跃，暂不清理", downloadId));
                                }
                            }
                        }
                    } catch (Exception e) {
                        LogUtil.warn(StringUtil.format("检查任务 {0} 心跳失败: {1}", downloadId, e.getMessage()));
                        // 获取心跳失败，为安全起见，暂时不清理
                        isActive = true;
                    }

                    if (!isActive) {
                        if (Var.debug) {
                            LogUtil.info(StringUtil.format("清理过期任务: {0} (最后访问时间早于 {1})", downloadId, cutoffTime));
                        }
                        // 统一调用 cleanupDownloadTask 进行清理
                        // cleanupDownloadTask 内部需要确保也从 ZSet 中移除
                        cleanupDownloadTask(downloadId, true);
                        cleanedCount++;
                    }

                } catch (Exception e) {
                    // 处理单个任务清理时发生异常，记录日志并继续处理下一个任务
                    LogUtil.warn(StringUtil.format("清理过期下载任务 {0} 时出错: {1}", downloadId, e.getMessage()), e);
                }
            }

            // 清理 ZSet 中已被移除的任务（可选，作为维护步骤）
            // accessTimesZSet.removeRangeByScoreAsync(Double.NEGATIVE_INFINITY, true,
            // cutoffTime, true);

        } catch (Exception e) {
            LogUtil.error("清理过期下载任务时发生未预料的错误: " + e.getMessage(), e);
        }

        return cleanedCount;
    }

    /**
     * 修复异常的已取消任务 检查所有已标记为取消但仍有心跳的任务，释放它们的信号量并清理资源
     *
     * @return 修复的任务数量
     */
    public static int fixAnomalyCancelledTasks() {
        int fixedCount = 0;

        // 获取当前所有任务
        Set<String> activeTaskIds = new HashSet<>(taskFutures.keySet());
        Set<String> cancelledTaskIds = new HashSet<>(cancelledTasks.keySet());

        // 处理已取消但未清理的任务
        for (String downloadId : cancelledTaskIds) {
            if (activeTaskIds.contains(downloadId)) {
                LogUtil.warn("发现异常任务: " + downloadId + ", 已标记为取消但仍在活跃任务列表中");

                try {
                    // 强制取消并清理
                    DownloadTaskManager.cancelTask(downloadId);
                    fixedCount++;
                } catch (Exception e) {
                    LogUtil.error("修复异常任务失败: " + downloadId + ", " + e.getMessage());
                }
            }
        }

        // 检查processingThreads中的僵尸线程
        for (Map.Entry<String, Thread> entry : processingThreads.entrySet()) {
            String downloadId = entry.getKey();
            Thread thread = entry.getValue();

            if (!activeTaskIds.contains(downloadId) || !thread.isAlive()) {
                LogUtil.warn("发现僵尸处理线程: " + downloadId);

                try {
                    // 从处理线程集合中移除
                    processingThreads.remove(downloadId);
                    fixedCount++;
                } catch (Exception e) {
                    LogUtil.error("移除僵尸处理线程失败: " + downloadId + ", " + e.getMessage());
                }
            }
        }

        // 检查任务心跳超时的任务
        long now = System.currentTimeMillis();
        for (String downloadId : activeTaskIds) {
            try {
                String heartbeatKey = DownloadTaskKeyManager.getDownloadHeartbeatKey(downloadId);
                Object heartbeatObj = Base.map.getValue(heartbeatKey);
                String heartbeatStr = heartbeatObj != null ? heartbeatObj.toString() : null;

                if (heartbeatStr == null) {
                    LogUtil.warn("发现无心跳任务: " + downloadId);

                    // 取消并清理
                    DownloadTaskManager.cancelTask(downloadId);
                    fixedCount++;
                    continue;
                }

                long heartbeat = Long.parseLong(heartbeatStr);
                if (now - heartbeat > 30 * 1000) { // 30秒无心跳
                    LogUtil.warn("发现心跳超时任务: " + downloadId + ", 最后心跳时间: "
                            + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(heartbeat)));

                    // 取消并清理
                    DownloadTaskManager.cancelTask(downloadId);
                    fixedCount++;
                }
            } catch (Exception e) {
                LogUtil.error("检查任务心跳失败: " + downloadId + ", " + e.getMessage());
            }
        }

        return fixedCount;
    }

    /**
     * 从响应中提取记录数组
     *
     * @param responseObject 响应JSON对象
     * @return 数据记录数组
     */
    private static JSONArray getRowsFromResponse(JSONObject responseObject) {
        if (responseObject == null) {
            if (Var.debug) {
                LogUtil.warn("getRowsFromResponse: responseObject is null");
            }
            return new JSONArray();
        }

        if (LOGGER.isDebugEnabled()) {
            // 记录响应结构，帮助诊断
            LOGGER.debug(
                    "响应结构: " + responseObject.toString().substring(0, Math.min(500, responseObject.toString().length()))
                            + "...");
        }

        // 标准响应格式中使用rows字段
        if (responseObject.has("rows")) {
            Object rows = responseObject.get("rows");
            if (rows instanceof JSONArray) {
                return responseObject.getJSONArray("rows");
            } else {
                if (Var.debug) {
                    LogUtil.warn("rows字段不是JSONArray类型: " + rows.getClass().getName());
                }
                // 尝试转换
                if (rows instanceof String) {
                    try {
                        return new JSONArray((String) rows);
                    } catch (Exception e) {
                        LogUtil.error("无法将rows字符串转换为JSONArray: " + e.getMessage(), e);
                    }
                }
            }
        }
        // 兼容其他可能的格式
        else if (responseObject.has("data")) {
            Object data = responseObject.get("data");
            if (data instanceof JSONArray) {
                return (JSONArray) data;
            } else if (data instanceof JSONObject && ((JSONObject) data).has("rows")) {
                Object rows = ((JSONObject) data).get("rows");
                if (rows instanceof JSONArray) {
                    return ((JSONObject) data).getJSONArray("rows");
                } else {
                    if (Var.debug) {
                        LogUtil.warn("data.rows字段不是JSONArray类型: " + rows.getClass().getName());
                    }
                }
            } else {
                if (Var.debug) {
                    LogUtil.warn("data字段既不是JSONArray也不包含rows字段: " + data.getClass().getName());
                }
            }
        } else if (responseObject.has("records")) {
            Object records = responseObject.get("records");
            if (records instanceof JSONArray) {
                return responseObject.getJSONArray("records");
            } else {
                if (Var.debug) {
                    LogUtil.warn("records字段不是JSONArray类型: " + records.getClass().getName());
                }
            }
        } else {
            // 记录所有可用的字段，帮助诊断
            StringBuilder fields = new StringBuilder("响应中没有找到rows/data/records字段，可用字段: ");
            for (String key : responseObject.keySet()) {
                fields.append(key).append(", ");
            }
            if (Var.debug) {
                LogUtil.warn(fields.toString());
            }
        }

        // 如果找不到任何有效数据，返回空数组
        return new JSONArray();
    }

    /**
     * 从响应中提取总记录数
     *
     * @param responseObject 响应JSON对象
     * @return 总记录数
     */
    private static int getTotalFromResponse(JSONObject responseObject) {
        // 标准响应格式中使用total字段
        if (responseObject.has("total")) {
            return responseObject.getInt("total");
        }
        // 兼容其他可能的格式
        else if (responseObject.has("data") && responseObject.get("data") instanceof JSONObject) {
            JSONObject data = responseObject.getJSONObject("data");
            if (data.has("total")) {
                return data.getInt("total");
            }
        }
        return 0;
    }

    /**
     * 更新任务进度
     *
     * @param downloadId       下载ID
     * @param currentPage      当前页码
     * @param processedRecords 已处理记录数
     * @param totalRecords     总记录数
     */
    private static void updateProgress(String downloadId, int currentPage, int processedRecords, int totalRecords) {
        String progressInfo;
        double progressPercentage = 0;

        if (totalRecords > 0) {
            progressPercentage = processedRecords * 100.0 / totalRecords;
            int percentage = (int) progressPercentage;
            progressInfo = String.format("第%d页，已处理%d/%d条 (%d%%)", currentPage, processedRecords, totalRecords,
                    percentage);
        } else {
            // 如果不知道总数，则基于页码估算进度
            // 假设大多数导出不超过50页
            progressPercentage = Math.min(95, currentPage * 2); // 最高显示到95%
            progressInfo = String.format("第%d页，已处理%d条", currentPage, processedRecords);
        }

        try {
            // 保存详细的进度文本
            Base.map.setValue(DownloadTaskKeyManager.getDownloadProgressKey(downloadId), progressInfo,
                    DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);

            // 保存进度百分比（0-1之间的小数）
            Base.map.setValue(DownloadTaskKeyManager.getDownloadProgressPercentageKey(downloadId),
                    String.valueOf(progressPercentage / 100), DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);

            // 保存最后更新时间
            Base.map.setValue(DownloadTaskKeyManager.getDownloadProgressTimestampKey(downloadId),
                    String.valueOf(System.currentTimeMillis()), DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);
        } catch (Exception e) {
            // 忽略进度更新失败
            LOGGER.warn("更新任务进度失败: " + e.getMessage());
        }
    }

    // 添加进度更新方法
    private static void updateFinalProgress(String downloadId, int totalRecords) {
        try {
            // 使用100%表示任务已完成
            double progressPercentage = 100.0;
            String progressInfo = "已处理 " + totalRecords + " 条记录，处理完成(100%)";

            // 保存进度信息
            Base.map.setValue(DownloadTaskKeyManager.getDownloadProgressKey(downloadId), progressInfo,
                    DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);

            // 保存进度百分比（0-1之间的小数）
            Base.map.setValue(DownloadTaskKeyManager.getDownloadProgressPercentageKey(downloadId),
                    String.valueOf(progressPercentage / 100), DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);

            // 保存最后更新时间
            Base.map.setValue(DownloadTaskKeyManager.getDownloadProgressTimestampKey(downloadId),
                    String.valueOf(System.currentTimeMillis()), DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);

            // 从lastAccessTimes中移除记录，防止被超时监控误认为是长时间运行任务
            lastAccessTimes.remove(downloadId);
        } catch (Exception e) {
            // 忽略进度更新失败
            LOGGER.warn("更新任务进度失败: " + e.getMessage());
        }
    }

    // 添加线程池定义，替换ForkJoinPool.commonPool()
    private static final ThreadPoolExecutor taskExecutor = new ThreadPoolExecutor(5, MAX_CONCURRENT_THREADS, 60,
            TimeUnit.SECONDS, new LinkedBlockingQueue<>(100), new ThreadFactory() {
                private final AtomicInteger counter = new AtomicInteger(1);

                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r, "file-push-worker-" + SysUtil.getId() + counter.getAndIncrement());
                    thread.setDaemon(true);
                    return thread;
                }
            }, new ThreadPoolExecutor.CallerRunsPolicy());

    private static final ScheduledExecutorService monitorExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread thread = new Thread(r, "file-push-monitor");
        thread.setDaemon(true);
        return thread;
    });

    static {
        // 替换无限循环的线程监控
        monitorExecutor.scheduleAtFixedRate(() -> {
            try {
                // 获取当前可用的信号量数量
                int availablePermits = downloadSemaphore.availablePermits();
                int activeTaskCount = processingThreads.size();

                // 如果可用信号量数量异常（小于应有值），进行修复
                if (availablePermits + activeTaskCount < 10) {
                    LogUtil.warn("检测到信号量异常，进行修复");
                    int toRelease = 10 - (availablePermits + activeTaskCount);
                    for (int i = 0; i < toRelease; i++) {
                        downloadSemaphore.release();
                    }
                }

                // 记录线程池状态
                LOGGER.debug(String.format("线程池状态 - 活跃:%d, 池大小:%d, 核心:%d, 最大:%d, 队列:%d, 任务总数:%d",
                        taskExecutor.getActiveCount(), taskExecutor.getPoolSize(), taskExecutor.getCorePoolSize(),
                        taskExecutor.getMaximumPoolSize(), taskExecutor.getQueue().size(), taskFutures.size()));
            } catch (Exception e) {
                LogUtil.warn("检查信号量状态时出错: " + e.getMessage());
            }
        }, 30, 30, TimeUnit.SECONDS);

        // 添加定期清理过期下载任务
        monitorExecutor.scheduleAtFixedRate(() -> {
            try {
                // 执行清理逻辑
                int cleanedUp = cleanupExpiredDownloadTasks();
                if (cleanedUp > 0) {
                    LOGGER.info("清理了 " + cleanedUp + " 个过期下载任务");
                }

                // 清理僵尸线程
                int zombiesCleaned = cleanupZombieThreads();
                if (zombiesCleaned > 0) {
                    LOGGER.info("清理了 " + zombiesCleaned + " 个僵尸线程");
                }
            } catch (Exception e) {
                LogUtil.warn("定期清理任务执行失败: " + e.getMessage());
            }
        }, 1, 10, TimeUnit.MINUTES);

        // 添加线程池监控
        monitorExecutor.scheduleAtFixedRate(() -> {
            try {
                // 检查处理时间过长的任务
                long now = System.currentTimeMillis();
                // 用于跟踪已处理的基础任务ID，避免重复报告
                Set<String> processedBaseIds = new HashSet<>();

                // 创建一个新的集合来存储要移除的key，避免并发修改异常
                Set<String> keysToRemove = new HashSet<>();

                for (Map.Entry<String, Long> entry : lastAccessTimes.entrySet()) {
                    String downloadId = entry.getKey();
                    long lastAccess = entry.getValue();

                    // 检查是否有后缀，如果有则移除
                    String baseId = downloadId;
                    if (downloadId.endsWith("_start_time")) {
                        baseId = downloadId.substring(0, downloadId.length() - "_start_time".length());
                        // 标记原始key等待移除
                        keysToRemove.add(downloadId);
                        // 如果需要，更新访问时间到不带后缀的ID
                        if (!lastAccessTimes.containsKey(baseId)) {
                            lastAccessTimes.put(baseId, lastAccess);
                        }
                    }

                    // 检查基础ID是否已经处理过，避免重复警告
                    if (processedBaseIds.contains(baseId)) {
                        continue;
                    }

                    // 检查任务是否已完成
                    boolean isCompleted = false;
                    try {
                        String completedMark = (String) Base.map
                                .getValue(DownloadTaskKeyManager.getDownloadTaskKey(baseId) + "_completed");
                        isCompleted = "true".equals(completedMark);
                    } catch (Exception e) {
                        // 忽略异常
                    }

                    // 如果任务已完成，移除访问时间记录
                    if (isCompleted) {
                        keysToRemove.add(downloadId);
                        continue;
                    }

                    // 记录本次已处理的基础ID
                    processedBaseIds.add(baseId);

                    // 如果任务超过30分钟未完成，记录警告
                    if (now - lastAccess > 30 * 60 * 1000) {
                        LogUtil.warn("检测到长时间运行任务: " + baseId + ", 已运行: " + ((now - lastAccess) / 1000) + "秒");

                        // 如果超过2小时，尝试强制取消
                        if (now - lastAccess > 2 * 60 * 60 * 1000) {
                            LogUtil.error("强制取消长时间运行任务: " + baseId);
                            DownloadTaskManager.cancelTask(baseId);
                            // 移除访问时间记录
                            keysToRemove.add(downloadId);
                        }
                    }
                }

                // 批量移除需要清理的记录
                for (String key : keysToRemove) {
                    lastAccessTimes.remove(key);
                }

            } catch (Exception e) {
                LogUtil.error("线程池监控异常: " + e.getMessage());
            }
        }, 1, 30, TimeUnit.MINUTES);

        // 删除重复的健康检查初始化
        LOGGER.debug("下载任务健康检查机制已启动");

        // 添加JVM关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                LOGGER.info("正在关闭FilePush线程池...");
                taskExecutor.shutdown();
                if (!taskExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    LOGGER.warn("FilePush线程池未能在30秒内关闭，执行强制关闭");
                    taskExecutor.shutdownNow();
                }
                monitorExecutor.shutdown();
                if (!monitorExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    monitorExecutor.shutdownNow();
                }
                LOGGER.info("FilePush线程池已关闭");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                LOGGER.error("关闭FilePush线程池时被中断");
                taskExecutor.shutdownNow();
                monitorExecutor.shutdownNow();
            }
        }));
    }

    // 添加线程池关闭方法，确保资源释放
    public static void shutdown() {
        try {
            LOGGER.info("正在关闭下载任务处理器...");

            // 使用DownloadTaskExecutor关闭线程池
            DownloadTaskExecutor.cancelAllTasks(taskFutures);
            DownloadTaskExecutor.shutdown(true);

            LOGGER.info("下载任务处理器已关闭");
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            LOGGER.error("关闭下载任务处理器时被中断: " + e.getMessage());
        }
    }

    /**
     * 动态调整系统并发处理能力 同时调整信号量和线程池配置，确保一致性
     * 
     * @param newCapacity 新的并发处理能力
     * @return 是否调整成功
     */
    public static synchronized boolean adjustConcurrencyCapacity(int newCapacity) {
        if (newCapacity <= 0) {
            return false;
        }

        try {
            // 重置信号量
            resetDownloadSemaphoreCounter();
            // 重新初始化线程池
            reinitializeThreadPool(newCapacity);
            LOGGER.info("系统并发处理能力已调整: " + newCapacity);
            return true;
        } catch (Exception e) {
            LOGGER.error("调整系统并发处理能力失败: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 添加死锁监控方法 (优化后)
     *
     * @param downloadId 下载ID
     * @param thread     疑似僵尸线程
     */
    public static void addThreadToDeadlockMonitor(String downloadId, Thread thread) {
        // 将问题线程记录到特殊集合中，用于后续系统定期检查和处理
        try {
            // 记录线程信息，但不保留强引用
            String threadInfo = String.format("线程ID:%d, 名称:%s, 状态:%s", thread.getId(), thread.getName(),
                    thread.getState());
            // 旧的逻辑：写入单独的 key (REMOVED)
            // Base.map.setValue(DownloadTaskKeyManager.getDownloadZombieThreadKey(downloadId),
            // threadInfo,
            // DOWNLOAD_EXPIRE_HOURS, TimeUnit.HOURS);

            // 添加到僵尸任务 Set (优化点)
            RSet<String> zombieSet = Base.map.getRedissonClient().getSet(DownloadTaskKeyManager.getZombieTasksSetKey());
            zombieSet.addAsync(downloadId); // 使用 addAsync 异步添加

            // 发送告警，通知管理员
            LogUtil.error("检测到僵尸线程，已添加到待处理集合: " + downloadId + ", Info: " + threadInfo);
        } catch (Exception e) {
            LogUtil.error("记录僵尸线程信息或添加到Set时出错: " + e.getMessage());
        }
    }

    /**
     * 系统维护方法，清理检测到的僵尸线程 (优化后)
     *
     * @return 清理的僵尸线程数量
     */
    public static int cleanupZombieThreads() {
        int cleanedCount = 0;
        RSet<String> zombieSet = null;

        try {
            zombieSet = Base.map.getRedissonClient().getSet(DownloadTaskKeyManager.getZombieTasksSetKey());
            // 获取所有僵尸任务 ID (优化点：使用 SMEMBERS 或 SSCAN)
            // 如果集合可能很大，应使用 scanIterator 代替 readAll()
            Set<String> zombieTaskIds = zombieSet.readAll(); // readAll() 即 SMEMBERS

            if (zombieTaskIds == null || zombieTaskIds.isEmpty()) {
                return 0;
            }

            if (Var.debug) {
                LogUtil.info("发现 " + zombieTaskIds.size() + " 个待处理的僵尸任务记录");
            }

            for (String downloadId : zombieTaskIds) {
                boolean removedFromSet = false; // 标记是否已从 Set 中移除
                try {
                    // 检查线程是否还存在于处理线程集合中
                    Thread zombieThread = processingThreads.get(downloadId);
                    if (zombieThread != null) {
                        if (Var.debug) {
                            LogUtil.info("清理僵尸任务（线程仍在集合中）: " + downloadId);
                        }
                        // 尝试中断线程（如果还在运行）
                        if (zombieThread.isAlive()) {
                            zombieThread.interrupt();
                        }
                        // 从处理线程集合中移除
                        processingThreads.remove(downloadId);
                    } else {
                        if (Var.debug) {
                            LogUtil.info("清理僵尸任务（线程已不在集合中）: " + downloadId);
                        }
                    }

                    // 确保信号量已释放（即使线程不在集合中，也可能占用了信号量）
                    try {
                        // releaseDownloadSemaphore 内部会检查并减少 Redis 计数器
                        releaseDownloadSemaphore(downloadId);
                    } catch (Exception e) {
                        LogUtil.warn("释放僵尸任务信号量失败（可能已被释放）: " + downloadId + ", " + e.getMessage());
                    }

                    // 清理任务资源（包括 Redis keys 和文件）
                    // cleanupDownloadTask 会处理大部分清理，包括从 ZSet/Set 移除
                    cleanupDownloadTask(downloadId, true);

                    // 从僵尸 Set 中移除 (移到这里确保成功清理后再移除)
                    zombieSet.remove(downloadId);
                    removedFromSet = true;
                    cleanedCount++;

                } catch (Exception e) {
                    LogUtil.error("清理僵尸任务 " + downloadId + " 时出错: " + e.getMessage(), e);
                } finally {
                    // 如果清理过程中发生异常且未从 Set 中移除，尝试再次移除
                    if (!removedFromSet && zombieSet != null) {
                        try {
                            zombieSet.removeAsync(downloadId);
                            LogUtil.warn("在异常处理后尝试从僵尸 Set 移除任务: " + downloadId);
                        } catch (Exception remEx) {
                            LogUtil.error("从僵尸 Set 移除任务 " + downloadId + " 失败: " + remEx.getMessage());
                        }
                    }
                }
            }
        } catch (Exception e) {
            LogUtil.error("扫描或处理僵尸任务记录时出错: " + e.getMessage(), e);
        }

        return cleanedCount;
    }

    // 添加getter和setter方法供DownloadTaskManager使用
    public static int getDownloadExpireHours() {
        return DOWNLOAD_EXPIRE_HOURS;
    }

    public static void setCancelledTask(String downloadId, boolean value) {
        cancelledTasks.put(downloadId, value);
    }

    public static void removeCancelledTask(String downloadId) {
        cancelledTasks.remove(downloadId);
    }

    public static Future<?> getTaskFuture(String downloadId) {
        return taskFutures.get(downloadId);
    }

    public static void removeTaskFuture(String downloadId) {
        taskFutures.remove(downloadId);
    }

    public static Thread getProcessingThread(String downloadId) {
        return processingThreads.get(downloadId);
    }

    public static void removeProcessingThread(String downloadId) {
        processingThreads.remove(downloadId);
    }

    public static String getDownloadTaskValue(String key) {
        return downloadTasks.get(key);
    }

    public static void setDownloadTaskValue(String key, String value) {
        downloadTasks.put(key, value);
    }

    public static void removeDownloadTask(String key) {
        downloadTasks.remove(key);
    }

    public static void removeLastAccessTime(String key) {
        lastAccessTimes.remove(key);
    }

    public static ThreadPoolExecutor getTaskExecutor() {
        return taskExecutor;
    }

    /**
     * 增加活跃线程计数
     * 
     * @return 增加后的线程数
     */
    public static int incrementActiveThreadCount() {
        return activeThreadCount.incrementAndGet();
    }

    /**
     * 减少活跃线程计数
     * 
     * @return 减少后的线程数
     */
    public static int decrementActiveThreadCount() {
        return activeThreadCount.decrementAndGet();
    }

    /**
     * 获取当前活跃线程数
     * 
     * @return 当前活跃线程数
     */
    public static int getActiveThreadCount() {
        return activeThreadCount.get();
    }

    /**
     * 设置处理线程
     * 
     * @param downloadId 下载任务ID
     * @param thread     处理线程
     */
    public static void setProcessingThread(String downloadId, Thread thread) {
        processingThreads.put(downloadId, thread);
    }
}