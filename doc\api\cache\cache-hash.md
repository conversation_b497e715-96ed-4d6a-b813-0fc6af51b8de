# Redis Hash操作使用文档

本文档介绍Redis缓存系统提供的Hash数据结构操作功能，用于存储对象字段或映射关系。

## 功能概览

Redis Hash操作功能提供以下能力：

- 添加/更新Hash字段
- 获取Hash字段值
- 删除Hash字段
- 获取所有Hash字段和值
- 批量获取/删除Hash字段
- 序列化/反序列化List对象到Hash字段

## Java 用法

在Java代码中，可以直接使用`Base.map`访问Hash操作功能：

```java
// 设置Hash字段
Base.map.hashPut("user:profile:1001", "name", "张三");
Base.map.hashPut("user:profile:1001", "age", 30);
Base.map.hashPut("user:profile:1001", "email", "<EMAIL>");

// 检查Hash字段是否存在
boolean exists = Base.map.hasHashKey("user:profile:1001", "mobile");

// 获取Hash字段值
String name = (String) Base.map.hashGet("user:profile:1001", "name");
Integer age = (Integer) Base.map.hashGet("user:profile:1001", "age");

// 删除Hash字段
Base.map.hashDelete("user:profile:1001", "email");

// 批量设置Hash字段
Map<Object, Object> fields = new HashMap<>();
fields.put("mobile", "13800138000");
fields.put("address", "北京市海淀区");
fields.put("company", "示例科技有限公司");
Base.map.hashPutAll("user:profile:1001", fields);

// 获取所有Hash字段和值
Map<Object, Object> allFields = Base.map.hashGetAll("user:profile:1001");

// 批量获取Hash字段值
List<String> fieldNames = Arrays.asList("name", "age", "mobile");
List<Object> values = Base.map.hashMultiGet("user:profile:1001", fieldNames);

// 批量删除Hash字段
List<String> fieldsToDelete = Arrays.asList("address", "company");
Base.map.hashDeleteKeys("user:profile:1001", fieldsToDelete);

// 存储List到Hash字段
List<Product> products = getProductList();
Base.map.hashSetList("shop:1001", "products", products);

// 从Hash字段获取List
List<Product> productList = Base.map.hashGetList(
    "shop:1001", "products", Product.class);
```

## XWL 脚本用法

在XWL脚本中，直接使用`Base.map`访问Hash操作功能：

```javascript
// 设置Hash字段
Base.map.hashPut("user:profile:1001", "name", "张三");
Base.map.hashPut("user:profile:1001", "age", 30);
Base.map.hashPut("user:profile:1001", "email", "<EMAIL>");

// 检查Hash字段是否存在
var exists = Base.map.hasHashKey("user:profile:1001", "mobile");

// 获取Hash字段值
var name = Base.map.hashGet("user:profile:1001", "name");
var age = Base.map.hashGet("user:profile:1001", "age");

// 删除Hash字段
Base.map.hashDelete("user:profile:1001", "email");

// 批量设置Hash字段
var fields = {
    "mobile": "13800138000",
    "address": "北京市海淀区",
    "company": "示例科技有限公司"
};
Base.map.hashPutAll("user:profile:1001", fields);

// 获取所有Hash字段和值
var allFields = Base.map.hashGetAll("user:profile:1001");

// 批量获取Hash字段值
var fieldNames = ["name", "age", "mobile"];
var values = Base.map.hashMultiGet("user:profile:1001", fieldNames);

// 批量删除Hash字段
var fieldsToDelete = ["address", "company"];
Base.map.hashDeleteKeys("user:profile:1001", fieldsToDelete);

// 存储List到Hash字段
var products = getProductList();
Base.map.hashSetList("shop:1001", "products", products);

// 从Hash字段获取List
var productList = Base.map.hashGetList("shop:1001", "products", "com.wb.model.Product");
```

## 国际化处理

按照系统国际化规则，错误信息和提示可以使用：

```javascript
// 在XWL脚本中
var errorMsg = Str.format(request, "cache_hashKeyNotFound");
Wb.warn(Wb.format("Str.cache_hashPutFailed"));

// 带参数的消息
var msg = Str.format(request, "cache_hashSetSuccess", key, field);
```

## 示例：用户配置管理

### Java 示例

```java
/**
 * 用户配置管理服务，使用Redis Hash存储用户配置
 */
@Service
public class UserConfigService {
    
    private static final String CONFIG_PREFIX = "user:config:";
    
    /**
     * 保存用户配置项
     */
    public void saveConfig(String userId, String configKey, Object configValue) {
        String key = CONFIG_PREFIX + userId;
        Base.map.hashPut(key, configKey, configValue);
    }
    
    /**
     * 批量保存用户配置
     */
    public void saveConfigs(String userId, Map<String, Object> configs) {
        String key = CONFIG_PREFIX + userId;
        Map<Object, Object> configMap = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : configs.entrySet()) {
            configMap.put(entry.getKey(), entry.getValue());
        }
        
        Base.map.hashPutAll(key, configMap);
    }
    
    /**
     * 获取用户配置项
     */
    public <T> T getConfig(String userId, String configKey, Class<T> type) {
        String key = CONFIG_PREFIX + userId;
        Object value = Base.map.hashGet(key, configKey);
        
        if (value == null) {
            return null;
        }
        
        // 简单类型直接转换
        if (value.getClass().equals(type)) {
            return (T) value;
        }
        
        // JSON转换复杂类型
        if (value instanceof String) {
            try {
                return JSONObject.parseObject((String) value, type);
            } catch (Exception e) {
                return null;
            }
        }
        
        return null;
    }
    
    /**
     * 获取所有用户配置
     */
    public Map<String, Object> getAllConfigs(String userId) {
        String key = CONFIG_PREFIX + userId;
        Map<Object, Object> allConfigs = Base.map.hashGetAll(key);
        
        // 转换成String作为Key的Map
        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<Object, Object> entry : allConfigs.entrySet()) {
            result.put(entry.getKey().toString(), entry.getValue());
        }
        
        return result;
    }
    
    /**
     * 删除用户配置项
     */
    public void deleteConfig(String userId, String configKey) {
        String key = CONFIG_PREFIX + userId;
        Base.map.hashDelete(key, configKey);
    }
    
    /**
     * 清空用户所有配置
     */
    public void clearAllConfigs(String userId) {
        String key = CONFIG_PREFIX + userId;
        Base.map.delKey(key);
    }
}
```

### XWL 脚本示例

```javascript
/**
 * 用户配置管理功能，使用Redis Hash存储用户配置
 */

// 配置前缀
var CONFIG_PREFIX = "user:config:";

/**
 * 保存用户配置项
 */
function saveConfig(userId, configKey, configValue) {
    var key = CONFIG_PREFIX + userId;
    Base.map.hashPut(key, configKey, configValue);
}

/**
 * 批量保存用户配置
 */
function saveConfigs(userId, configs) {
    var key = CONFIG_PREFIX + userId;
    Base.map.hashPutAll(key, configs);
}

/**
 * 获取用户配置项
 */
function getConfig(userId, configKey) {
    var key = CONFIG_PREFIX + userId;
    return Base.map.hashGet(key, configKey);
}

/**
 * 获取所有用户配置
 */
function getAllConfigs(userId) {
    var key = CONFIG_PREFIX + userId;
    return Base.map.hashGetAll(key);
}

/**
 * 删除用户配置项
 */
function deleteConfig(userId, configKey) {
    var key = CONFIG_PREFIX + userId;
    Base.map.hashDelete(key, configKey);
}

/**
 * 清空用户所有配置
 */
function clearAllConfigs(userId) {
    var key = CONFIG_PREFIX + userId;
    Base.map.delKey(key);
}
```

## 注意事项

1. Hash结构适合存储对象的多个字段，可以减少键的数量，提高内存使用效率
2. Redis Hash最大可以存储2^32-1个字段，但建议单个Hash不要存储过多字段（一般不超过1000个）
3. 从Hash中获取数据时注意类型转换，系统会尝试保留原始数据类型
4. 对于需要序列化/反序列化的复杂对象，可以使用`hashSetList`和`hashGetList`方法
5. Hash操作是原子性的，不需要担心并发问题
6. 对于大型Hash结构，批量操作比单个操作更高效
7. 使用Hash存储JSON或复杂对象时，注意序列化和反序列化的性能开销 