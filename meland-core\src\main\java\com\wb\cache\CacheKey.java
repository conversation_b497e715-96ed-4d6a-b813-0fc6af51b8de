package com.wb.cache;

/**
 * Redis缓存KEY
 */
public class CacheKey {

    /**
     * 门店缓存KEY
     */
    public final static String STORE_KEY = "STORE:";

    /**
     * 隐私协议缓存KEY
     */
    public final static String PRIVACY_AGREEMENT_KEY = "PRIVACY_AGREEMENT:";

    /**
     * 通用隐私协议缓存KEY
     */
    public final static String GENERAL_PRIVACY_AGREEMENT_KEY = "GENERAL_PRIVACY_AGREEMENT:";

    /**
     * 会员隐私协议缓存KEY
     */
    public final static String MEMBER_PRIVACY_AGREEMENT_KEY = "MEMBER_PRIVACY_AGREEMENT:";

    /**
     * 入园须知缓存KEY
     */
    public final static String PARK_INSTRUCTIONS_KEY = "PARK_INSTRUCTIONS:";

    /**
     * 卡头接口中卡头信息缓存KEY
     */
    public final static String AUTO_CARD_INFO_KEY = "AUTO_CARD_INFO_KEY:";
}
