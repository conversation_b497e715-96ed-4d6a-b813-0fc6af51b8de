package com.wb.controls;

import java.util.Map.Entry;
import java.util.Set;

import com.wb.util.StringUtil;

/**
 * 用于存储大段的文本。控件将把第一个字符串属性存储到以name为名称的request attribute中。
 */
public class Text extends Control {
	public void create() throws Exception {
		String key, text;

		Set<Entry<String, Object>> es = configs.entrySet();
		for (Entry<String, Object> entry : es) {
			key = entry.getKey();
			if (key.equals("itemId") || key.equals("quoted"))
				continue;
			text = gs(key);
			if (gb("quoted"))
				text = StringUtil.text(text);
			request.setAttribute(gs("itemId"), text);
			return;
		}
	}
}