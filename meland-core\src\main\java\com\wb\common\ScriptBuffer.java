package com.wb.common;

import com.wb.util.FileUtil;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import org.json.JSONObject;
import org.quartz.JobExecutionContext;

import javax.script.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * JavaScript脚本执行和缓存器。缓存首次访问并编译后的脚本对象，
 * 便于下次对脚本访问时无需编译而直接运行。
 * 
 * 注意：此实现依赖于线程安全的脚本引擎（如Nashorn）。
 * CompiledScript实例在多线程环境下共享使用，请确保使用线程安全的引擎。
 */
public class ScriptBuffer {
	private static final Logger LOGGER = Logger.getLogger(ScriptBuffer.class.getName());

	/**
	 * JavaScript全局成员列表。
	 */
	public static ArrayList<String> globalMembers;

	/**
	 * JavaScript引擎管理器对象。
	 */
	private static ScriptEngineManager manager;
	
	/**
	 * JavaScript引擎对象。
	 */
	private static ScriptEngine engine;
	
	/**
	 * JavaScript可编译的引擎对象。
	 */
	private static Compilable compilable;
	
	/**
	 * 缓存编译后的JavaScript脚本的HashMap。
	 */
	private static ConcurrentHashMap<String, CompiledScript> buffer;
	
	/**
	 * 脚本绑定上下文，支持嵌套调用
	 */
	private static class ScriptContext {
		// 绑定对象
		private final Bindings bindings;
		
		// 嵌套深度计数器
		private final AtomicInteger nestedCounter = new AtomicInteger(0);
		
		// 当前作用域变量集合
		private final Deque<Set<String>> scopeVariables = new LinkedList<>();
		
		/**
		 * 构造函数
		 */
		public ScriptContext() {
			this.bindings = createSafeBindings();
		}
		
		/**
		 * 安全地创建Bindings对象
		 */
		private static Bindings createSafeBindings() {
			try {
				return engine != null ? engine.createBindings() : null;
			} catch (Exception e) {
				LOGGER.log(Level.WARNING, "Failed to create bindings", e);
				return null;
			}
		}
		
		/**
		 * 获取绑定对象
		 */
		public Bindings getBindings() {
			return bindings;
		}
		
		/**
		 * 进入新的调用作用域
		 */
		public void enterScope() {
			nestedCounter.incrementAndGet();
			scopeVariables.push(new HashSet<>());
		}
		
		/**
		 * 在当前作用域添加变量
		 */
		public void addVariable(String key, Object value) {
			if (bindings != null && key != null) {
				bindings.put(key, value);
				
				// 记录当前作用域的变量
				if (!scopeVariables.isEmpty()) {
					scopeVariables.peek().add(key);
				}
			}
		}
		
		/**
		 * 离开当前作用域并清理变量
		 */
		public void exitScope() {
			int count = nestedCounter.decrementAndGet();
			
			// 清理当前作用域的变量
			if (!scopeVariables.isEmpty()) {
				Set<String> currentScope = scopeVariables.pop();
				if (bindings != null) {
					for (String key : currentScope) {
						bindings.remove(key);
					}
				}
			}
			
			// 如果嵌套计数为0，可以完全清理
			if (count <= 0) {
				if (bindings != null) {
					bindings.clear();
				}
				nestedCounter.set(0);
				scopeVariables.clear();
			}
		}
		
		/**
		 * 强制清理所有数据
		 */
		public void clear() {
			if (bindings != null) {
				bindings.clear();
			}
			nestedCounter.set(0);
			scopeVariables.clear();
		}
	}
	
	/**
	 * 使用ThreadLocal为每个线程维护独立的ScriptContext
	 */
	private static final ThreadLocal<ScriptContext> SCRIPT_CONTEXT = new ThreadLocal<ScriptContext>() {
		@Override
		protected ScriptContext initialValue() {
			return new ScriptContext();
		}
	};
	
	/**
	 * 脚本池条目，包含脚本及其使用时间戳
	 */
	private static class ScriptPoolEntry {
		private final CompiledScript script;
		private long lastAccessTime;
		
		public ScriptPoolEntry(CompiledScript script) {
			this.script = script;
			this.lastAccessTime = System.currentTimeMillis();
		}
		
		public CompiledScript getScript() {
			this.lastAccessTime = System.currentTimeMillis();
			return script;
		}
		
		public long getLastAccessTime() {
			return lastAccessTime;
		}
	}
	
	/**
	 * 脚本池，用于重用CompiledScript对象，包含访问时间以支持LRU缓存策略
	 */
	private static final ConcurrentHashMap<String, ConcurrentLinkedQueue<ScriptPoolEntry>> scriptPool = new ConcurrentHashMap<>();
	
	/**
	 * 脚本池最大条目数（每个键）
	 */
	private static final int MAX_POOL_ENTRIES_PER_KEY = 20;
	
	/**
	 * 脚本池条目过期时间（毫秒）
	 */
	private static final long POOL_ENTRY_EXPIRATION_MS = 30 * 60 * 1000; // 30分钟
	
	/**
	 * 标记已预编译的公共脚本
	 */
	private static boolean commonScriptsPrecompiled = false;
	
	/**
	 * 公共脚本列表
	 */
	private static final String[] COMMON_SCRIPTS = {
		"common.js", 
		"utils.js", 
		"validation.js"
	};
	
	static {
		// 添加JVM关闭钩子来清理资源
		Runtime.getRuntime().addShutdownHook(new Thread(() -> {
			if (buffer != null) {
				buffer.clear();
			}
			if (scriptPool != null) {
				scriptPool.clear();
			}
		}));
		
		// 启动脚本池清理定时任务
		startPoolCleanupTask();
	}
	
	/**
	 * 启动脚本池清理定时任务，定期清理未使用的脚本
	 */
	private static void startPoolCleanupTask() {
		Thread cleanupThread = new Thread(() -> {
			while (!Thread.currentThread().isInterrupted()) {
				try {
					Thread.sleep(5 * 60 * 1000); // 5分钟运行一次
					cleanupScriptPool();
				} catch (InterruptedException e) {
					Thread.currentThread().interrupt();
					break;
				} catch (Exception e) {
					LOGGER.log(Level.WARNING, "Error cleaning up script pool", e);
				}
			}
		});
		cleanupThread.setDaemon(true);
		cleanupThread.setName("ScriptBuffer-Pool-Cleanup");
		cleanupThread.start();
	}
	
	/**
	 * 清理过期或未使用的脚本池条目
	 */
	private static void cleanupScriptPool() {
		if (scriptPool == null) {
			return;
		}
		
		long currentTime = System.currentTimeMillis();
		int removedEntries = 0;
		
		for (Map.Entry<String, ConcurrentLinkedQueue<ScriptPoolEntry>> entry : scriptPool.entrySet()) {
			ConcurrentLinkedQueue<ScriptPoolEntry> queue = entry.getValue();
			if (queue == null || queue.isEmpty()) {
				continue;
			}
			
			// 移除过期条目
			List<ScriptPoolEntry> validEntries = new ArrayList<>();
			for (ScriptPoolEntry poolEntry : queue) {
				if (currentTime - poolEntry.getLastAccessTime() < POOL_ENTRY_EXPIRATION_MS) {
					validEntries.add(poolEntry);
				} else {
					removedEntries++;
				}
			}
			
			// 重置队列
			queue.clear();
			// 限制每个键的条目数量
			int limit = Math.min(validEntries.size(), MAX_POOL_ENTRIES_PER_KEY);
			for (int i = 0; i < limit; i++) {
				queue.offer(validEntries.get(i));
			}
			
			removedEntries += validEntries.size() - limit;
		}
		
		if (removedEntries > 0) {
			LOGGER.log(Level.INFO, "Cleaned up " + removedEntries + " expired or excess script pool entries");
		}
	}
	
	/**
	 * 获取当前线程的Bindings对象
	 * @return 当前线程的Bindings对象或null
	 */
	private static Bindings getThreadBindings() {
		ScriptContext context = SCRIPT_CONTEXT.get();
		return context != null ? context.getBindings() : null;
	}
	
	/**
	 * 在当前作用域中添加变量
	 * @param key 变量名
	 * @param value 变量值
	 */
	private static void addScopeVariable(String key, Object value) {
		ScriptContext context = SCRIPT_CONTEXT.get();
		if (context != null) {
			context.addVariable(key, value);
		}
	}
	
	/**
	 * 批量添加变量到当前作用域
	 * @param params 参数映射
	 */
	private static void addScopeVariables(Map<String, Object> params) {
		if (params != null && !params.isEmpty()) {
			ScriptContext context = SCRIPT_CONTEXT.get();
			if (context != null) {
				for (Map.Entry<String, Object> entry : params.entrySet()) {
					context.addVariable(entry.getKey(), entry.getValue());
				}
			}
		}
	}
	
	/**
	 * 进入新的执行作用域
	 */
	private static void enterScope() {
		ScriptContext context = SCRIPT_CONTEXT.get();
		if (context != null) {
			context.enterScope();
		}
	}
	
	/**
	 * 离开当前执行作用域
	 */
	private static void exitScope() {
		ScriptContext context = SCRIPT_CONTEXT.get();
		if (context != null) {
			context.exitScope();
		}
	}
	
	/**
	 * 完全清理当前线程的ScriptContext数据
	 * 应该在线程任务完成或重新利用前调用
	 */
	public static void clearThreadLocalData() {
		try {
			ScriptContext context = SCRIPT_CONTEXT.get();
			if (context != null) {
				context.clear();
			}
		} finally {
			SCRIPT_CONTEXT.remove(); // 彻底移除ThreadLocal引用
		}
	}
	
	/**
	 * 计算脚本内容的SHA-256哈希值，用作缓存键
	 * 
	 * @param scriptText 脚本文本
	 * @return 哈希值，如果计算失败则返回基于hashCode的备用键
	 */
	private static String calculateScriptHash(String scriptText) {
		if (scriptText == null) {
			return "null";
		}
		
		try {
			MessageDigest digest = MessageDigest.getInstance("SHA-256");
			byte[] hashBytes = digest.digest(scriptText.getBytes(StandardCharsets.UTF_8));
			
			// 转换为十六进制字符串
			StringBuilder hexString = new StringBuilder();
			for (byte b : hashBytes) {
				String hex = Integer.toHexString(0xff & b);
				if (hex.length() == 1) {
					hexString.append('0');
				}
				hexString.append(hex);
			}
			
			// 添加长度信息，进一步降低碰撞可能性
			return hexString.toString() + "_" + scriptText.length();
		} catch (NoSuchAlgorithmException e) {
			// 回退到旧方法，但添加长度信息
			LOGGER.log(Level.WARNING, "SHA-256 hash calculation failed, falling back to hashCode", e);
			return String.valueOf(scriptText.hashCode()) + "_" + scriptText.length();
		}
	}
	
	/**
	 * 在作用域中执行代码块
	 * @param action 要执行的代码
	 * @param <T> 返回值类型
	 * @return 执行结果
	 * @throws Exception 执行过程中的异常
	 */
	private static <T> T executeInScope(Function<Bindings, T> action) throws Exception {
		if (action == null) {
			return null;
		}
		
		enterScope();
		try {
			Bindings bindings = getThreadBindings();
			if (bindings != null) {
				return action.apply(bindings);
			}
			return null;
		} finally {
			exitScope();
		}
	}

	/**
	 * 在Web请求上下文中运行指定编号的脚本对象。首次运行脚本将被编译并缓存编译后的实例。
	 * 
	 * @param id 脚本编号
	 * @param scriptText 脚本内容
	 * @param request Web上下文HttpServletRequest对象参数
	 * @param response Web上下文HttpServletResponse对象参数
	 * @param sourceURL 调试脚本时标识代码的文件路径
	 * @throws Exception 执行脚本发生错误
	 */
	public static void run(String id, String scriptText, HttpServletRequest request, HttpServletResponse response,
			String sourceURL) throws Exception {
		if (engine == null || compilable == null) {
			throw new IllegalStateException("ScriptEngine not initialized");
		}
		
		executeInScope(bindings -> {
			try {
				CompiledScript script = buffer.get(id);
		
				if (script == null) {
					script = compilable.compile(encScript(scriptText, sourceURL, true));
					buffer.put(id, script);
				}
				
				// 添加Web请求变量
				addScopeVariable("request", request);
				addScopeVariable("response", response);
				
				script.eval(bindings);
				return null;
			} catch (Exception e) {
				LOGGER.log(Level.SEVERE, "Error executing script: " + id, e);
				throw new RuntimeException(e);
			}
		});
	}

	/**
	 * 在Web请求上下文中运行指定编号的脚本对象。首次运行脚本将被编译并缓存编译后的实例。
	 * 本方法可以传入更多的参数，并返回执行结果
	 * 
	 * @param id 脚本编号
	 * @param scriptText 脚本内容
	 * @param request Web上下文HttpServletRequest对象参数
	 * @param response Web上下文HttpServletResponse对象参数
	 * @param sourceURL 调试脚本时标识代码的文件路径
	 * @param params 传入的参数
	 * @throws Exception 执行脚本发生错误
	 */
	public static Object run(String id, String scriptText, HttpServletRequest request, HttpServletResponse response,
			String sourceURL, JSONObject params) throws Exception {
		if (engine == null || compilable == null) {
			throw new IllegalStateException("ScriptEngine not initialized");
		}
		
		return executeInScope(bindings -> {
			try {
				CompiledScript script = buffer.get(id);
		
				if (script == null) {
					script = compilable.compile(encScript(scriptText, sourceURL, true));
					buffer.put(id, script);
				}
				
				// 添加Web请求变量
				addScopeVariable("request", request);
				addScopeVariable("response", response);
				
				// 添加额外参数
				if (params != null) {
					// 转换JSONObject为标准Map
					Map<String, Object> paramMap = jsonObjectToMap(params);
					addScopeVariables(paramMap);
				}
				
				return script.eval(bindings);
			} catch (Exception e) {
				LOGGER.log(Level.SEVERE, "Error executing script with params: " + id, e);
				throw new RuntimeException(e);
			}
		});
	}

	/**
	 * 将JSONObject转换为标准Map
	 */
	private static Map<String, Object> jsonObjectToMap(JSONObject json) {
		if (json == null) {
			return Collections.emptyMap();
		}
		
		Map<String, Object> map = new HashMap<>();
		for (String key : json.keySet()) {
			map.put(key, json.opt(key));
		}
		return map;
	}

	/**
	 * 包装执行的脚本，添加sourceURL和app对象。如果在调试模块下添加调试信息。
	 * @param scriptText 脚本内容。
	 * @param sourceURL 脚本对应的源URL。
	 * @param addApp 是否添加app对象。
	 * @return 包装script。
	 */
	private static String encScript(String scriptText, String sourceURL, boolean addApp) {
		StringBuilder buf = new StringBuilder(scriptText.length() + 100);

		// 修改函数签名，确保request和response变量在作用域内可见
		buf.append("(function(request, response){");
		if (addApp)
			buf.append("var app=Wb.getApp(request,response);");
		buf.append(scriptText);
		// 修改函数调用，添加请求和响应参数
		buf.append("\n})(request, response);");
		if (!StringUtil.isEmpty(sourceURL)) {
			buf.append("\n//# sourceURL=");
			buf.append(sourceURL);
		}
		return buf.toString();
	}

	/**
	 * 包装执行的无参数脚本，添加sourceURL。该方法用于不需要request/response的脚本。
	 * @param scriptText 脚本内容。
	 * @param sourceURL 脚本对应的源URL。
	 * @return 包装script。
	 */
	private static String encScriptNoParams(String scriptText, String sourceURL) {
		StringBuilder buf = new StringBuilder(scriptText.length() + 100);

		buf.append("(function(){");
		buf.append(scriptText);
		buf.append("\n})();");
		if (!StringUtil.isEmpty(sourceURL)) {
			buf.append("\n//# sourceURL=");
			buf.append(sourceURL);
		}
		return buf.toString();
	}

	public static Object run(String scriptText, JSONObject params) throws Exception {
		if (engine == null || compilable == null) {
			throw new IllegalStateException("ScriptEngine not initialized");
		}
		
		return executeInScope(bindings -> {
			try {
				// 尝试从脚本池获取已编译的脚本
				String scriptHash = calculateScriptHash(scriptText);
				CompiledScript script = getScriptFromPool(scriptHash);
				boolean fromPool = script != null;
				
				if (script == null) {
					// 使用无参数脚本包装，因为这里不需要request/response
					script = compilable.compile(encScriptNoParams(scriptText, null));
				}
				
				// 添加参数
				if (params != null) {
					Map<String, Object> paramMap = jsonObjectToMap(params);
					addScopeVariables(paramMap);
				}
				
				Object result = script.eval(bindings);
				
				// 返回脚本到池中
				if (fromPool) {
					returnScriptToPool(scriptHash, script);
				}
				
				return result;
			} catch (Exception e) {
				LOGGER.log(Level.SEVERE, "Error executing script with JSON params", e);
				throw new RuntimeException(e);
			}
		});
	}

	public static boolean evalCondition(String condition, JSONObject params) throws Exception {
		if (condition == null || condition.isEmpty()) {
			return false;
		}
		
		Object value = run(condition, params);
		if (value == null)
			return false;
		if ((value instanceof Boolean))
			return ((Boolean) value).booleanValue();
		if ((value instanceof Number)) {
			return ((Number) value).intValue() != 0;
		}
		String str = value.toString();
		if (str.isEmpty()) {
			return false;
		}
		return true;
	}

	/**
	 * 运行服务器端脚本。运行后的脚本不会进行缓存。
	 * @param scriptText 脚本内容
	 * @param sourceURL 脚本对应的源URL。
	 * @throws Exception 执行脚本发生错误
	 */
	public static Object run(String scriptText, String sourceURL) throws Exception {
		if (engine == null || compilable == null) {
			throw new IllegalStateException("ScriptEngine not initialized");
		}
		
		return executeInScope(bindings -> {
			try {
				// 尝试从脚本池获取已编译的脚本
				String scriptHash = calculateScriptHash(scriptText);
				CompiledScript script = getScriptFromPool(scriptHash);
				boolean fromPool = script != null;
				
				if (script == null) {
					// 使用无参数脚本包装，因为这里不需要request/response
					script = compilable.compile(encScriptNoParams(scriptText, sourceURL));
				}
				
				Object result = script.eval(bindings);
				
				// 返回脚本到池中
				if (fromPool) {
					returnScriptToPool(scriptHash, script);
				}
				
				return result;
			} catch (Exception e) {
				LOGGER.log(Level.SEVERE, "Error executing script: " + sourceURL, e);
				throw new RuntimeException(e);
			}
		});
	}

	/**
	 * 包装执行计划任务的脚本，传入jobContext参数。
	 * @param scriptText 脚本内容。
	 * @param sourceURL 脚本对应的源URL。
	 * @return 包装script。
	 */
	private static String encScriptForJob(String scriptText, String sourceURL) {
		StringBuilder buf = new StringBuilder(scriptText.length() + 100);

		buf.append("(function(jobContext){");
		buf.append(scriptText);
		buf.append("\n})(jobContext);");
		if (!StringUtil.isEmpty(sourceURL)) {
			buf.append("\n//# sourceURL=");
			buf.append(sourceURL);
		}
		return buf.toString();
	}

	/**
	 * 在计划任务中运行指定编号的脚本对象。首次运行脚本将被编译并缓存编译后的实例。
	 * 
	 * @param taskId 计划任务编号
	 * @param scriptText 脚本内容
	 * @param jobContext 任务上下文对象
	 * @throws Exception 执行脚本发生错误
	 */
	public static void run(String id, String scriptText, JobExecutionContext jobContext) throws Exception {
		if (engine == null || compilable == null) {
			throw new IllegalStateException("ScriptEngine not initialized");
		}
		
		try {
			// 使用try-finally确保即使发生异常也清理ThreadLocal
			executeInScope(bindings -> {
				try {
					CompiledScript script = buffer.get(id);
			
					if (script == null) {
						// 使用适合任务的封装方式，不需要request和response参数
						script = compilable.compile(encScriptForJob(scriptText, id));
						buffer.put(id, script);
					}
					
					// 添加任务上下文变量
					addScopeVariable("jobContext", jobContext);
					
					script.eval(bindings);
					return null;
				} catch (Exception e) {
					LOGGER.log(Level.SEVERE, "Error executing job script: " + id, e);
					throw new RuntimeException(e);
				}
			});
		} finally {
			// 对于计划任务，确保任务执行完成后完全清理ThreadLocal数据
			// 因为这些线程可能会长时间存在于线程池中
			clearThreadLocalData();
		}
	}

	/**
	* 加载和初始化。
	*/
	public static synchronized void load() {
		try {
			manager = new ScriptEngineManager();
			engine = manager.getEngineByName("javascript");
			compilable = (Compilable) engine;
			buffer = new ConcurrentHashMap<String, CompiledScript>();
			globalMembers = new ArrayList<String>();
			
			loadUtils();
			
			// 预编译公共脚本
			// precompileCommonScripts();
			
			LOGGER.info("ScriptBuffer initialized successfully");
		} catch (Throwable e) {
			LogUtil.error("Failed to initialize ScriptBuffer", e);
			throw new RuntimeException(e);
		}
	}

	/**
	 * 删除缓存中指定编码前缀的所有脚本实例对象。
	 * @param id 脚本id编号的前缀
	 */
	public static void remove(String id) {
		if (buffer == null || id == null) {
			return;
		}
		
		Set<Entry<String, CompiledScript>> es = buffer.entrySet();
		List<String> toRemove = new ArrayList<>();

		for (Entry<String, CompiledScript> e : es) {
			String k = e.getKey();
			if (k.startsWith(id)) {
				toRemove.add(k);
			}
		}
		
		// 避免在迭代中修改集合
		for (String key : toRemove) {
			buffer.remove(key);
		}
	}

	/**
	 * 加载全局工具类方法。
	 * @throws Exception 加载过程发生异常。
	 */
	private static void loadUtils() throws Exception {
		if (engine == null || manager == null) {
			throw new IllegalStateException("ScriptEngine not initialized");
		}
		
		File serverJsFile = new File(Base.path, "wb/system/server.js");
		if (!serverJsFile.exists()) {
			LogUtil.error("server.js not found at: " + serverJsFile.getAbsolutePath());
			return;
		}
		
		String text = FileUtil.readString(serverJsFile);

		text = text + "\n//# sourceURL=server.js";
		CompiledScript script = compilable.compile(text);
		Bindings bindings = engine.createBindings();
		script.eval(bindings);
		
		if (globalMembers == null) {
			globalMembers = new ArrayList<>();
		}
		
		Set<Entry<String, Object>> es = bindings.entrySet();
		for (Entry<String, Object> e : es) {
			String key = e.getKey();
			manager.put(key, e.getValue());
			globalMembers.add(key);
		}
	}
	
	/**
	 * 预编译公共脚本，提高系统启动后的脚本执行效率
	 */
	public static void precompileCommonScripts() {
		if (commonScriptsPrecompiled || engine == null || compilable == null) {
			return;
		}
		
		for (String scriptName : COMMON_SCRIPTS) {
			try {
				String scriptPath = "wb/system/" + scriptName;
				File scriptFile = new File(Base.path, scriptPath);
				if (scriptFile.exists()) {
					String content = FileUtil.readString(scriptFile);
					String key = "precompiled:" + scriptName;
					
					// 使用encScriptNoParams包装脚本内容，确保变量正确封装
					String wrappedContent = encScriptNoParams(content, scriptName);
					CompiledScript script = compilable.compile(wrappedContent);
					
					buffer.put(key, script);
					LogUtil.info("Precompiled script: " + scriptName);
				} else {
					LogUtil.warn("Common script not found: " + scriptPath);
				}
			} catch (Exception e) {
				LogUtil.error("Failed to precompile script: " + scriptName, e);
			}
		}
		
		commonScriptsPrecompiled = true;
	}
	
	/**
	 * 从脚本池中获取已编译的脚本
	 * 
	 * @param key 脚本的唯一标识符
	 * @return 编译后的脚本，如果池中不存在则返回null
	 */
	public static CompiledScript getScriptFromPool(String key) {
		if (key == null || scriptPool == null) {
			return null;
		}
		
		ConcurrentLinkedQueue<ScriptPoolEntry> queue = scriptPool.computeIfAbsent(key, 
				k -> new ConcurrentLinkedQueue<>());
		
		ScriptPoolEntry entry = queue.poll();
		return entry != null ? entry.getScript() : null;
	}
	
	/**
	 * 将编译后的脚本返回到脚本池中
	 * 
	 * @param key 脚本的唯一标识符
	 * @param script 编译后的脚本
	 */
	public static void returnScriptToPool(String key, CompiledScript script) {
		if (key == null || script == null || scriptPool == null) {
			return;
		}
		
		ConcurrentLinkedQueue<ScriptPoolEntry> queue = scriptPool.get(key);
		if (queue != null) {
			// 检查队列大小，避免过度增长
			if (queue.size() < MAX_POOL_ENTRIES_PER_KEY) {
				queue.offer(new ScriptPoolEntry(script));
			}
		}
	}
	
	/**
	 * 创建并编译新脚本
	 * 
	 * @param scriptText 脚本文本
	 * @param sourceURL 源代码URL，用于调试
	 * @return 编译后的脚本
	 * @throws ScriptException 编译失败时抛出
	 */
	public static CompiledScript createAndCompileScript(String scriptText, String sourceURL) throws ScriptException {
		if (engine == null || compilable == null) {
			throw new IllegalStateException("ScriptEngine not initialized");
		}
		
		return compilable.compile(encScriptNoParams(scriptText, sourceURL));
	}
	
	/**
	 * 清理脚本池
	 */
	public static void clearScriptPool() {
		if (scriptPool != null) {
			scriptPool.clear();
		}
	}
	
	/**
	 * 获取已缓存脚本数量
	 * 
	 * @return 缓存中的脚本数量
	 */
	public static int getCachedScriptCount() {
		return buffer != null ? buffer.size() : 0;
	}
	
	/**
	 * 获取脚本池大小
	 * 
	 * @return 脚本池中的脚本总数
	 */
	public static int getScriptPoolSize() {
		if (scriptPool == null) {
			return 0;
		}
		
		int total = 0;
		for (ConcurrentLinkedQueue<ScriptPoolEntry> queue : scriptPool.values()) {
			total += queue.size();
		}
		return total;
	}
	
	/**
	 * 完全重置ScriptBuffer状态，用于单元测试或特殊场景
	 * 注意：生产环境谨慎使用
	 */
	public static synchronized void reset() {
		if (buffer != null) {
			buffer.clear();
		}
		
		if (scriptPool != null) {
			scriptPool.clear();
		}
		
		commonScriptsPrecompiled = false;
	}
	
	/**
	 * 注册Servlet容器关闭监听器，确保在Web应用关闭时清理资源
	 *
	 * @param servletContextEvent Servlet上下文事件
	 */
	public static void registerShutdownHook(Object servletContextEvent) {
		try {
			// 使用反射调用避免直接依赖Servlet API
			Class<?> eventClass = servletContextEvent.getClass();
			Object servletContext = eventClass.getMethod("getServletContext").invoke(servletContextEvent);
			
			Class<?> contextClass = servletContext.getClass();
			contextClass.getMethod("addListener", String.class).invoke(servletContext, 
					"com.wb.common.ScriptBufferCleanupListener");
			
			LOGGER.info("ScriptBuffer shutdown hook registered");
		} catch (Exception e) {
			LogUtil.error("Failed to register ScriptBuffer shutdown hook", e);
		}
	}
}