---
description: 
globs: 
alwaysApply: false
---
---
description: 
alwaysApply: false
---
---
description: 你是一个专注于ExtJS 4.2前端开发的AI助手，熟练掌握ExtJS组件与架构。
globs: *.xwl,*.js
alwaysApply: false
---
    ExtJS资源结构：
      - 资源目录: meland-web\src\main\webapp\wb\libs\ext
      - 主题文件: meland-web\src\main\webapp\wb\libs\ext\resources\ext-theme-neptune
      - 扩展主题: meland-web\src\main\webapp\wb\css\style-debug.css
    
    技术规范：
      - 基于ExtJS 4.2框架
      - 使用MVC架构模式
      - 适配Neptune主题
      - 兼容自定义样式扩展
    
    编码标准：
      - 组件创建：
        - 使用Ext.define定义新组件
        - 遵循命名空间规范
        - 正确继承基础组件
        - 合理使用mixins功能
      
      - 布局管理：
        - 优先使用border、vbox、hbox等布局
        - 避免绝对定位
        - 合理设置组件大小和比例
        - 使用响应式设计原则
      
      - 数据处理：
        - 使用Model和Store管理数据
        - RESTful API交互
        - 正确配置代理和读写器
        - 实现适当的数据验证
      
      - 事件处理：
        - 使用控制器统一管理事件
        - 遵循事件委托原则
        - 避免DOM直接操作
        - 合理处理组件生命周期事件
      
      - 性能优化：
        - 延迟渲染大型组件
        - 使用缓冲视图处理大数据集
        - 避免过度嵌套组件
        - 优化选择器使用
      
      - 主题定制：
        - 使用SASS/SCSS进行主题定制
        - 遵循Neptune主题的设计规范
        - 样式覆盖遵循特异性原则
        - 合理使用自定义CSS扩展
    
    集成指南：
      - 与Java后端的交互规范
      - XWL文件集成方式
      - ExtJS组件与其他框架的互操作
      - 国际化实现方案
    
    质量控制：
      - 跨浏览器兼容性测试
      - 性能监控与优化
      - 代码审查与重构
      - 组件复用与维护