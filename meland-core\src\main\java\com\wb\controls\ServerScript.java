package com.wb.controls;

import org.json.JSONObject;

import com.wb.common.ScriptBuffer;

/**
 * 服务器端脚本控件，用以使用JavaScript语法执行Java代码。
 */
public class ServerScript extends Control {
	public void create() throws Exception {
		String script = getScript(configs, "script");
		if (!script.isEmpty())
			ScriptBuffer.run(gs("id"), script, request, response, gs("sourceURL"));
	}

	/**
	 * 获取对象中指定名称的。
	 * @param object JSNObject对象。
	 * @param name 名称。
	 * @return 获取的值。如果值为空返回空字符串。
	 */
	public static String getScript(JSONObject object, String name) {
		Object value = object.opt(name);
		if (value == null)
			return "";
		else {
			String script = (String) value;
			if (script.indexOf("{#") != -1)
				throw new IllegalArgumentException(
						"ServerScript does not support {#param#} feature, please use app.get(param) instead.");
			return script;
		}
	}
}
