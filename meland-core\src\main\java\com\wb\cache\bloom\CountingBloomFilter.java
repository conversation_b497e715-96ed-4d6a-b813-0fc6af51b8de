package com.wb.cache.bloom;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;

/**
 * Redis计数布隆过滤器实现
 * 使用计数器替代位数组，支持元素删除操作
 */
public class CountingBloomFilter {
    private static final Logger logger = LoggerFactory.getLogger(CountingBloomFilter.class);
    
    private final String filterName; // 过滤器名称，用作Redis键前缀
    private final int numHashFunctions; // 哈希函数数量
    private final int bitSize; // 计数数组大小
    private final double expectedFpp; // 期望的误判率
    private final int maxCount; // 每个计数器的最大值
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final StringRedisTemplate stringRedisTemplate;
    
    /**
     * 创建计数布隆过滤器
     * 
     * @param filterName 过滤器名称
     * @param expectedElements 预期元素数量
     * @param expectedFpp 期望的误判率（0-1之间，越小越精确但空间占用越大）
     * @param maxCount 计数器最大值，防止溢出（默认为15）
     * @param redisTemplate Redis模板
     * @param stringRedisTemplate 字符串Redis模板
     */
    public CountingBloomFilter(String filterName, long expectedElements, double expectedFpp, 
                               int maxCount, 
                               RedisTemplate<String, Object> redisTemplate, 
                               StringRedisTemplate stringRedisTemplate) {
        if (isEmpty(filterName)) {
            throw new IllegalArgumentException("过滤器名称不能为空");
        }
        if (expectedElements <= 0) {
            throw new IllegalArgumentException("预期元素数量必须大于0");
        }
        if (expectedFpp <= 0 || expectedFpp >= 1) {
            throw new IllegalArgumentException("期望误判率必须在0-1之间");
        }
        if (maxCount <= 0) {
            throw new IllegalArgumentException("计数器最大值必须大于0");
        }
        
        this.filterName = "cbf:" + filterName;
        this.expectedFpp = expectedFpp;
        this.maxCount = maxCount;
        this.redisTemplate = redisTemplate;
        this.stringRedisTemplate = stringRedisTemplate;
        
        // 根据预期元素数量和误判率计算最佳的位数组大小和哈希函数数量
        this.bitSize = optimalBitSize(expectedElements, expectedFpp);
        this.numHashFunctions = optimalNumHashFunctions(expectedElements, bitSize);
        
        logger.info("初始化计数布隆过滤器 [name=" + filterName + ", expectedElements=" + expectedElements 
                + ", fpp=" + expectedFpp + ", bitSize=" + bitSize 
                + ", hashFunctions=" + numHashFunctions + ", maxCount=" + maxCount + "]");
    }
    
    /**
     * 创建计数布隆过滤器（使用默认的最大计数值15）
     */
    public CountingBloomFilter(String filterName, long expectedElements, double expectedFpp,
                               RedisTemplate<String, Object> redisTemplate, 
                               StringRedisTemplate stringRedisTemplate) {
        this(filterName, expectedElements, expectedFpp, 15, redisTemplate, stringRedisTemplate);
    }
    
    /**
     * 判断字符串是否为空
     */
    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 计算最优的位数组大小
     */
    private int optimalBitSize(long expectedElements, double fpp) {
        return (int) Math.ceil(-1 * expectedElements * Math.log(fpp) / (Math.log(2) * Math.log(2)));
    }
    
    /**
     * 计算最优的哈希函数数量
     */
    private int optimalNumHashFunctions(long expectedElements, int bitSize) {
        return Math.max(1, (int) Math.round((double) bitSize / expectedElements * Math.log(2)));
    }
    
    /**
     * 计算元素的位置
     * 
     * @param element 元素
     * @return 位置数组
     */
    private int[] getBitIndices(String element) {
        int[] indices = new int[numHashFunctions];
        
        // 使用Murmur3哈希函数的两个种子作为基础
        long hash1 = MurmurHash3.hash64(element.getBytes(), 0);
        long hash2 = MurmurHash3.hash64(element.getBytes(), hash1);
        
        // 使用双重哈希法生成多个哈希函数
        for (int i = 0; i < numHashFunctions; i++) {
            // 使用双重哈希法计算位置: (hash1 + i * hash2) % bitSize
            // 这种方法可以产生numHashFunctions个不同的哈希值
            indices[i] = (int) ((hash1 + i * hash2) % bitSize);
        }
        
        return indices;
    }
    
    /**
     * 增加元素到计数布隆过滤器
     * 
     * @param element 要添加的元素
     * @return 是否成功
     */
    public boolean add(String element) {
        if (isEmpty(element)) {
            return false;
        }
        
        try {
            int[] bitIndices = getBitIndices(element);
            return executeWithExceptionHandling("CBF_ADD", filterName, () -> {
                // 使用Pipeline批量设置位
                List<Object> results = redisTemplate.executePipelined(new SessionCallback<Object>() {
                    @Override
                    @SuppressWarnings("unchecked")
                    public Object execute(RedisOperations operations) throws DataAccessException {
                        for (int bitIndex : bitIndices) {
                            // 获取当前计数
                            String counterKey = getCounterKey(bitIndex);
                            operations.opsForValue().get(counterKey);
                        }
                        return null;
                    }
                });
                
                // 处理结果，将计数加1但不超过最大值
                List<Object> updateResults = redisTemplate.executePipelined(new SessionCallback<Object>() {
                    @Override
                    @SuppressWarnings("unchecked")
                    public Object execute(RedisOperations operations) throws DataAccessException {
                        for (int i = 0; i < bitIndices.length; i++) {
                            String counterKey = getCounterKey(bitIndices[i]);
                            int currentCount = 0;
                            if (i < results.size() && results.get(i) != null) {
                                try {
                                    currentCount = Integer.parseInt(results.get(i).toString());
                                } catch (NumberFormatException e) {
                                    logger.warn("计数转换失败: " + results.get(i));
                                }
                            }
                            
                            // 如果计数未达到最大值，则加1
                            if (currentCount < maxCount) {
                                operations.opsForValue().set(counterKey, String.valueOf(currentCount + 1));
                            }
                        }
                        return null;
                    }
                });
                
                return updateResults != null;
            });
        } catch (Exception e) {
            logger.error("添加元素到计数布隆过滤器失败 [name=" + filterName + ", element=" + element + "]: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 批量添加元素到计数布隆过滤器
     * 
     * @param elements 要添加的元素集合
     * @return 添加成功的元素数量
     */
    public int addAll(Collection<String> elements) {
        if (elements == null || elements.isEmpty()) {
            return 0;
        }
        
        int successCount = 0;
        for (String element : elements) {
            if (add(element)) {
                successCount++;
            }
        }
        return successCount;
    }
    
    /**
     * 从计数布隆过滤器中删除元素
     * 
     * @param element 要删除的元素
     * @return 是否成功
     */
    public boolean remove(String element) {
        if (isEmpty(element)) {
            return false;
        }
        
        try {
            int[] bitIndices = getBitIndices(element);
            return executeWithExceptionHandling("CBF_REMOVE", filterName, () -> {
                // 使用Pipeline批量获取当前计数
                List<Object> results = redisTemplate.executePipelined(new SessionCallback<Object>() {
                    @Override
                    @SuppressWarnings("unchecked")
                    public Object execute(RedisOperations operations) throws DataAccessException {
                        for (int bitIndex : bitIndices) {
                            String counterKey = getCounterKey(bitIndex);
                            operations.opsForValue().get(counterKey);
                        }
                        return null;
                    }
                });
                
                // 处理结果，将计数减1但不小于0
                List<Object> updateResults = redisTemplate.executePipelined(new SessionCallback<Object>() {
                    @Override
                    @SuppressWarnings("unchecked")
                    public Object execute(RedisOperations operations) throws DataAccessException {
                        for (int i = 0; i < bitIndices.length; i++) {
                            String counterKey = getCounterKey(bitIndices[i]);
                            int currentCount = 0;
                            if (i < results.size() && results.get(i) != null) {
                                try {
                                    currentCount = Integer.parseInt(results.get(i).toString());
                                } catch (NumberFormatException e) {
                                    logger.warn("计数转换失败: " + results.get(i));
                                }
                            }
                            
                            // 如果计数大于0，则减1
                            if (currentCount > 0) {
                                operations.opsForValue().set(counterKey, String.valueOf(currentCount - 1));
                            }
                        }
                        return null;
                    }
                });
                
                return updateResults != null;
            });
        } catch (Exception e) {
            logger.error("从计数布隆过滤器中删除元素失败 [name=" + filterName + ", element=" + element + "]: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 批量从计数布隆过滤器中删除元素
     * 
     * @param elements 要删除的元素集合
     * @return 删除成功的元素数量
     */
    public int removeAll(Collection<String> elements) {
        if (elements == null || elements.isEmpty()) {
            return 0;
        }
        
        int successCount = 0;
        for (String element : elements) {
            if (remove(element)) {
                successCount++;
            }
        }
        return successCount;
    }
    
    /**
     * 判断元素是否可能存在于过滤器中
     * 
     * @param element 要检查的元素
     * @return 如果返回false则一定不存在，如果返回true则可能存在（有误判的可能）
     */
    public boolean mightContain(String element) {
        if (isEmpty(element)) {
            return false;
        }
        
        try {
            int[] bitIndices = getBitIndices(element);
            return executeWithExceptionHandling("CBF_CONTAINS", filterName, () -> {
                // 使用Pipeline批量检查计数
                List<Object> results = redisTemplate.executePipelined(new SessionCallback<Object>() {
                    @Override
                    @SuppressWarnings("unchecked")
                    public Object execute(RedisOperations operations) throws DataAccessException {
                        for (int bitIndex : bitIndices) {
                            String counterKey = getCounterKey(bitIndex);
                            operations.opsForValue().get(counterKey);
                        }
                        return null;
                    }
                });
                
                // 所有位的计数都大于0，则可能存在
                if (results != null) {
                    for (Object result : results) {
                        if (result == null) {
                            return false;
                        }
                        try {
                            int count = Integer.parseInt(result.toString());
                            if (count <= 0) {
                                return false;
                            }
                        } catch (NumberFormatException e) {
                            logger.warn("计数转换失败: " + result);
                            return false;
                        }
                    }
                    return true;
                }
                return false;
            });
        } catch (Exception e) {
            logger.error("检查元素是否存在于计数布隆过滤器失败 [name=" + filterName + ", element=" + element + "]: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 批量检查元素是否存在于过滤器中
     * 
     * @param elements 要检查的元素集合
     * @return 存在的元素映射（元素 -> 是否可能存在）
     */
    public Map<String, Boolean> multiMightContain(Collection<String> elements) {
        if (elements == null || elements.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<String, Boolean> result = new HashMap<>();
        for (String element : elements) {
            if (!isEmpty(element)) {
                result.put(element, mightContain(element));
            }
        }
        
        return result;
    }
    
    /**
     * 获取计数器在Redis中的键
     * 
     * @param bitIndex 位索引
     * @return Redis键
     */
    private String getCounterKey(int bitIndex) {
        return filterName + ":" + bitIndex;
    }
    
    /**
     * 获取过滤器中的元素数量（估计值）
     * 
     * @return 估计的元素数量
     */
    public long approximateElementCount() {
        try {
            // 获取所有计数器的值
            Map<String, Integer> counters = getAllCounters();
            if (counters.isEmpty()) {
                return 0;
            }
            
            // 使用布隆过滤器的数学公式估算元素数量
            double n = -bitSize * Math.log(1.0 - (double) counters.size() / bitSize) / numHashFunctions;
            return Math.round(n);
        } catch (Exception e) {
            logger.error("获取元素数量失败 [name=" + filterName + "]: " + e.getMessage());
            return 0;
        }
    }
    
    /**
     * 获取所有计数器的值
     * 
     * @return 计数器映射（索引 -> 计数）
     */
    public Map<String, Integer> getAllCounters() {
        try {
            return executeWithExceptionHandling("CBF_GET_ALL_COUNTERS", filterName, () -> {
                Map<String, Integer> counters = new HashMap<>();
                
                // 使用Redis的scan命令获取所有计数器键
                List<String> keys = scanKeysWithPrefix(filterName + ":");
                if (keys.isEmpty()) {
                    return counters;
                }
                
                // 使用Pipeline批量获取计数值
                List<Object> results = redisTemplate.executePipelined(new SessionCallback<Object>() {
                    @Override
                    @SuppressWarnings("unchecked")
                    public Object execute(RedisOperations operations) throws DataAccessException {
                        for (String key : keys) {
                            operations.opsForValue().get(key);
                        }
                        return null;
                    }
                });
                
                // 处理结果
                for (int i = 0; i < keys.size(); i++) {
                    if (i < results.size() && results.get(i) != null) {
                        try {
                            int count = Integer.parseInt(results.get(i).toString());
                            if (count > 0) {
                                counters.put(keys.get(i), count);
                            }
                        } catch (NumberFormatException e) {
                            logger.warn("计数转换失败: " + results.get(i));
                        }
                    }
                }
                
                return counters;
            });
        } catch (Exception e) {
            logger.error("获取所有计数器失败 [name=" + filterName + "]: " + e.getMessage());
            return new HashMap<>();
        }
    }
    
    /**
     * 扫描指定前缀的Redis键
     * 
     * @param prefix 键前缀
     * @return 匹配的键列表
     */
    private List<String> scanKeysWithPrefix(String prefix) {
        return executeWithExceptionHandling("SCAN_KEYS", prefix, () -> {
            List<String> keys = new ArrayList<>();
            
            // 使用Redis的keys命令查找匹配的键
            // 注意：在大型生产环境中，应该避免使用keys命令，而应该使用scan命令
            try {
                Set<String> keySet = stringRedisTemplate.keys(prefix + "*");
                if (keySet != null) {
                    keys.addAll(keySet);
                }
            } catch (Exception e) {
                logger.warn("扫描键失败，尝试使用替代方法: " + e.getMessage());
                
                // 备选方法：通过连接工厂获取连接并执行scan
                try {
                    org.springframework.data.redis.core.Cursor<byte[]> cursor = 
                        stringRedisTemplate.getConnectionFactory().getConnection()
                            .scan(org.springframework.data.redis.core.ScanOptions.scanOptions().match(prefix + "*").build());
                    
                    while (cursor.hasNext()) {
                        keys.add(new String(cursor.next()));
                    }
                    
                    try {
                        cursor.close();
                    } catch (Exception ex) {
                        logger.warn("关闭游标失败: " + ex.getMessage());
                    }
                } catch (Exception ex) {
                    logger.error("备选扫描方法也失败: " + ex.getMessage());
                }
            }
            
            return keys;
        });
    }
    
    /**
     * 清空过滤器
     */
    public void clear() {
        try {
            executeWithExceptionHandling("CBF_CLEAR", filterName, () -> {
                // 获取所有计数器键
                List<String> keys = scanKeysWithPrefix(filterName + ":");
                if (!keys.isEmpty()) {
                    // 批量删除
                    redisTemplate.delete(keys);
                }
            });
        } catch (Exception e) {
            logger.error("清空计数布隆过滤器失败 [name=" + filterName + "]: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前参数信息
     * 
     * @return 信息Map
     */
    public Map<String, Object> getInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("name", filterName);
        info.put("bitSize", bitSize);
        info.put("hashFunctions", numHashFunctions);
        info.put("expectedFpp", expectedFpp);
        info.put("maxCount", maxCount);
        info.put("approximateElementCount", approximateElementCount());
        
        // 统计计数器数量
        Map<String, Integer> counters = getAllCounters();
        info.put("nonZeroCounters", counters.size());
        
        // 计算当前的误判率
        double p = Math.pow(1.0 - Math.exp(-numHashFunctions * (double) counters.size() / bitSize), numHashFunctions);
        info.put("currentFpp", p);
        
        return info;
    }
    
    /**
     * 通用的异常处理执行方法
     */
    private <T> T executeWithExceptionHandling(String operation, String key, Supplier<T> supplier) {
        try {
            T result = supplier.get();
            return result;
        } catch (Exception e) {
            logger.error("计数布隆过滤器操作失败 [op=" + operation + ", key=" + key + "]: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 通用的异常处理执行方法（无返回值）
     */
    private void executeWithExceptionHandling(String operation, String key, Runnable runnable) {
        try {
            runnable.run();
        } catch (Exception e) {
            logger.error("计数布隆过滤器操作失败 [op=" + operation + ", key=" + key + "]: " + e.getMessage());
            throw e;
        }
    }
} 