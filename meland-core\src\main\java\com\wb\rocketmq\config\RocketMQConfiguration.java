package com.wb.rocketmq.config;

import com.wb.util.LogUtil;

/**
 * RocketMQ配置类
 * 负责设置RocketMQ客户端线程守护状态，确保系统可以正常退出
 */
public class RocketMQConfiguration {
    
    /** 是否已初始化标志 */
    private static boolean initialized = false;
    
    /** RocketMQ客户端守护线程系统属性名 */
    private static final String ROCKETMQ_DAEMON_PROPERTY = "rocketmq.client.daemon";
    
    /**
     * 初始化RocketMQ配置
     * 设置RocketMQ客户端线程为守护线程，确保JVM能够正常退出
     * 该方法是线程安全的，可以被多次调用，但只会执行一次初始化
     */
    public static synchronized void init() {
        // 如果已经初始化，直接返回
        if (initialized) {
            return;
        }
        
        try {
            // 设置RocketMQ客户端线程为守护线程
            System.setProperty(ROCKETMQ_DAEMON_PROPERTY, "true");
            LogUtil.info("RocketMQ客户端守护线程配置已设置");
            
            // 标记为已初始化
            initialized = true;
        } catch (Exception e) {
            LogUtil.error("设置RocketMQ客户端守护线程配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    public static boolean isInitialized() {
        return initialized;
    }
    
    /**
     * 重置初始化状态（主要用于测试）
     */
    public static synchronized void reset() {
        initialized = false;
    }
} 