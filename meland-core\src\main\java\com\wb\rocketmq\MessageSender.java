package com.wb.rocketmq;

import com.wb.rocketmq.util.MessageType;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import org.json.JSONArray;
import org.json.JSONObject;

public class MessageSender {

    /**
     * 同步发送
     *
     * @param topic   自定义topic
     * @param tag     消息标签
     * @param message 发送的消息内容
     * @return 发送结果
     */
    public static String sendSync(String topic, String tag, final JSONObject message) {
        try {
            if (StringUtil.isEmpty(tag)) {
                LogUtil.error("同步消息tag未明确，调用消息发送失败");
            }
            
            // 使用RocketMQProducerManager
            RocketMQProducerManager manager = RocketMQProducerManager.getInstance();
            return manager.sendMessage(topic, tag, message, MessageType.SYNC);
        } catch (Exception exception) {
            LogUtil.error(StringUtil.format("同步消息[{0}]发送异常：{1}", message.toString(), exception));
        }
        return "";
    }

    /**
     * 同步发送
     *
     * @param tag     消息标签
     * @param message 发送的消息内容
     * @return 发送结果
     */
    public static String sendSync(String tag, final JSONObject message) {
        return sendSync(null, tag, message);
    }

    /**
     * 异步发送
     *
     * @param topic   自定义topic
     * @param tag     消息标签
     * @param message 发送的消息内容
     */
    public static void sendAsync(String topic, String tag, final JSONObject message) {
        try {
            if (StringUtil.isEmpty(tag)) {
                LogUtil.error("异步消息tag未明确，调用消息发送失败");
            }
            
            // 使用RocketMQProducerManager
            RocketMQProducerManager manager = RocketMQProducerManager.getInstance();
            manager.sendMessage(topic, tag, message, MessageType.ASYNC);
        } catch (Exception exception) {
            LogUtil.error(StringUtil.format("异步消息[{0}]发送异常：{1}", message.toString(), exception));
        }
    }

    /**
     * 异步发送
     *
     * @param tag     消息标签
     * @param message 发送的消息内容
     */
    public static void sendAsync(String tag, final JSONObject message) {
        sendAsync(null, tag, message);
    }

    /**
     * 异步发送(批量)
     *
     * @param topic   自定义topic
     * @param tag     消息标签
     * @param message 发送的消息内容数组
     */
    public static void sendAsyncBatch(String topic, String tag, final JSONArray message) {
        try {
            if (StringUtil.isEmpty(tag)) {
                LogUtil.error("异步消息tag未明确，调用消息发送失败");
            }
            
            // 使用RocketMQProducerManager
            RocketMQProducerManager manager = RocketMQProducerManager.getInstance();
            manager.sendBatchMessage(topic, tag, message);
        } catch (Exception exception) {
            LogUtil.error(StringUtil.format("批量异步消息[{0}]发送异常：{1}", message.toString(), exception));
        }
    }

    /**
     * 异步发送(批量)
     *
     * @param tag     消息标签
     * @param message 发送的消息内容数组
     */
    public static void sendAsyncBatch(String tag, final JSONArray message) {
        sendAsyncBatch(null, tag, message);
    }

    /**
     * 顺序消息
     *
     * @param topic   自定义topic
     * @param tag     消息标签
     * @param message 发送的消息内容
     * @return 发送结果
     */
    public static String sendOrder(String topic, String tag, final JSONObject message) {
        try {
            if (StringUtil.isEmpty(tag)) {
                LogUtil.error("顺序消息tag未明确，调用消息发送失败");
            }
            
            // 使用RocketMQProducerManager
            RocketMQProducerManager manager = RocketMQProducerManager.getInstance();
            return manager.sendMessage(topic, tag, message, MessageType.SEQUENTIAL);
        } catch (Exception exception) {
            LogUtil.error(StringUtil.format("顺序消息[{0}]发送异常：{1}", message.toString(), exception));
        }
        return "";
    }

    /**
     * 顺序消息
     *
     * @param tag     消息标签
     * @param message 发送的消息内容
     * @return 发送结果
     */
    public static String sendOrder(String tag, final JSONObject message) {
        return sendOrder(null, tag, message);
    }

    /**
     * 定时/延迟消息
     *
     * @param topic   自定义topic
     * @param tag     消息标签
     * @param time    定时时间戳
     * @param message 发送的消息内容
     * @return 发送结果
     */
    public static String sendTime(String topic, String tag, Long time, final JSONObject message) {
        try {
            if (StringUtil.isEmpty(tag)) {
                LogUtil.error("定时/延迟消息tag未明确，调用消息发送失败");
            }
            if (time == null || time == 0) {
                LogUtil.error("定时/延迟消息时间未明确，调用消息发送失败");
            }
            
            // 使用RocketMQProducerManager
            RocketMQProducerManager manager = RocketMQProducerManager.getInstance();
            return manager.sendMessage(topic, tag, message, MessageType.TIME_DELAY, time);
        } catch (Exception exception) {
            LogUtil.error(StringUtil.format("定时/延迟消息[{0}]发送异常：{1}", message.toString(), exception));
        }
        return "";
    }

    /**
     * 定时/延迟消息
     *
     * @param tag     消息标签
     * @param time    定时时间戳
     * @param message 发送的消息内容
     * @return 发送结果
     */
    public static String sendTime(String tag, Long time, final JSONObject message) {
        return sendTime(null, tag, time, message);
    }
}