package com.wb.openplatform.dingtalk.util;

import javax.servlet.http.HttpServletRequest;

import org.json.JSONObject;

import com.wb.openplatform.enterprise.util.WeiXinUtil;
import com.wb.util.LogUtil;

/**
 * 由Mr. Huang创建于2019-04-04
 * 
 * <AUTHOR>
 */
public class DingTalkServer {

	/********************* 考勤管理 ******************/
	/**
	 * 企业考勤排班详情
	 * 在钉钉考勤应用中，设置考勤组规则后，会生成每天的排班信息，包括工作日、周末、节假日等。如果企业想查询某天的排班情况，可使用此接口查询某天的考勤排班全量信息。
	 * 注：固定班制只能查到未来15天的排班信息
	 */
	public final static String scheduling_url = "https://oapi.dingtalk.com/topapi/attendance/listschedule?access_token=ACCESS_TOKEN";
	/**
	 * 企业考勤组详情
	 * 在钉钉考勤应用中，考勤组是一类具有相同的班次、考勤位置等考勤规则的人或部门的组合，企业可根据实际业务设置多个考勤组。如果企业想获取企业的考勤组与企业业务系统对接，
	 * 可使用此接口查询所有的考勤组详情信息。
	 */
	public final static String Group_check_url = "https://oapi.dingtalk.com/topapi/attendance/getsimplegroups?access_token=ACCESS_TOKEN";

	/**
	 * 获取打卡详情
	 * 该接口用于返回企业内员工的实际打卡记录。比如，企业给一个员工设定的排班是上午9点和下午6点各打一次卡，但是员工在这期间打了多次，该接口会把所有的打卡记录都返回。
	 * 如果只要获取打卡结果数据，不需要详情数据，可使用获取打卡结果接口。
	 */
	public final static String Clock_details_url = "https://oapi.dingtalk.com/attendance/listRecord?access_token=ACCESS_TOKEN";

	/**
	 * 获取打卡结果
	 * 该接口用于返回企业内员工的实际打卡结果。比如，企业给一个员工设定的排班是上午9点和下午6点各打一次卡，即使员工在这期间打了多次，该接口也只会返回两条记录，
	 * 包括上午的打卡结果和下午的打卡结果。如果要获取打卡详细数据，比如打卡位置，可使用获取打卡详情接口。
	 */
	public final static String Clock_the_url = "https://oapi.dingtalk.com/attendance/list?access_token=ACCESS_TOKEN";

	/**
	 * 获取请假时长
	 * 该接口可以自动根据排班规则统计出每个员工的请假时长，进而与企业自有的请假／财务系统对接，进行工资统计，如果您的企业使用了钉钉考勤并希望依赖考勤系统自动计算员工请假时长，
	 * 可选择使用此接口
	 */
	public final static String Leave_time_url = "https://oapi.dingtalk.com/topapi/attendance/getleaveapproveduration?access_token=ACCESS_TOKEN";

	/**
	 * 查询请假状态 该接口用于查询指定企业下的指定用户在指定时间段内的请假状态
	 */
	public final static String Leave_state_url = "https://oapi.dingtalk.com/topapi/attendance/getleavestatus?access_token=ACCESS_TOKEN";

	/**
	 * 获取用户考勤组
	 * 在钉钉考勤应用中，考勤组是一类具有相同的班次、考勤位置等考勤规则的人或部门的组合，一个企业中的一个人只能属于一个考勤组。如果您的企业使用了钉钉考勤并希望获取员工的考勤组信息，可选择使用此接口。
	 */
	public final static String get_Group_check_url = "https://oapi.dingtalk.com/topapi/attendance/getusergroup?access_token=ACCESS_TOKEN";

	/********************* 部门用户管理 ******************/
	/**
	 * 获取用户详情 如果您想调用通讯录接口并同时获取员工手机号，请先参考通讯录权限说明，设置下通讯录接口权限和手机号等敏感字段权限
	 */
	public final static String User_details_url = "https://oapi.dingtalk.com/user/get?access_token=ACCESS_TOKEN&userid=USER_ID";

	/**
	 * 获取部门用户userid列表 【推荐使用】通过该接口，可以获取当前部门下的userid列表
	 */
	public final static String Dept_user_url = "https://oapi.dingtalk.com/user/getDeptMember?access_token=ACCESS_TOKEN&deptId=DEPTID";

	/**
	 * 获取部门用户List
	 */
	public final static String Dept_user_list_url = "https://oapi.dingtalk.com/user/simplelist?access_token=ACCESS_TOKEN&department_id=DEPTID";

	/**
	 * 获取部门用户详情 如果您想调用通讯录接口并同时获取员工手机号，请先参考通讯录权限说明，设置下通讯录接口权限和手机号等敏感字段权限
	 */
	public final static String Dept_user_details_url = "https://oapi.dingtalk.com/user/listbypage?access_token=ACCESS_TOKEN&department_id=DEPTID";

	/********************* 部门管理 ******************/
	/**
	 * 获取子部门ID列表 【注意】该接口能获取到企业部门下的所有直属子部门列表，不受授权范围限制，ISV可以根据该接口完成企业部门的遍历
	 */
	public final static String Dept_child_url = "https://oapi.dingtalk.com/department/list_ids?access_token=ACCESS_TOKEN&id=DEPTID";

	/**
	 * 获取部门列表
	 */
	public final static String Dept_url = "https://oapi.dingtalk.com/department/list?access_token=ACCESS_TOKEN";

	/**
	 * 获取部门详情
	 */
	public final static String Dept_details_url = "https://oapi.dingtalk.com/department/get?access_token=ACCESS_TOKEN&id=DEPTID";

	/**
	 * 获取企业考勤排班详情
	 * 
	 * @param         params{ workDate date offset number size number }
	 * @param request
	 * @return
	 */
	public static JSONObject getScheduling(String params, HttpServletRequest request) {
		// 重新加载全局变量
		DingtalkParamesUtil.init();
		String accessToken = DingTalkUtil
				.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret, DingtalkParamesUtil.tokenDD)
				.getToken();
		// 2.获取请求的url
		String mat_url = scheduling_url.replace("ACCESS_TOKEN", accessToken);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "POST", params);

		// 4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	/**
	 * 获取企业考勤组详情
	 * 
	 * @param         params{ offset number size number }
	 * @param request
	 * @return
	 */
	public static JSONObject getGroup_check(String params, HttpServletRequest request) {
		// 重新加载全局变量
		DingtalkParamesUtil.init();
		String accessToken = DingTalkUtil
				.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret, DingtalkParamesUtil.tokenDD)
				.getToken();
		// 2.获取请求的url
		String mat_url = Group_check_url.replace("ACCESS_TOKEN", accessToken);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "POST", params);

		// 4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	/**
	 * 获取打卡详情
	 * 
	 * @param         params{
	 *  "userIds": ["001","002"],
	 *  "checkDateFrom": "yyyy-MM-dd hh:mm:ss",
	 *  "checkDateTo": "yyyy-MM-dd hh:mm:ss",//起始与结束工作日最多相隔7天 
	 *  "isI18n":"false" }
	 * @param request
	 * @return
	 */
	public static JSONObject getClock_details(String params, HttpServletRequest request) {
		// 重新加载全局变量
		DingtalkParamesUtil.init();
		String accessToken = DingTalkUtil
				.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret, DingtalkParamesUtil.tokenDD)
				.getToken();
		// 2.获取请求的url
		String mat_url = Clock_details_url.replace("ACCESS_TOKEN", accessToken);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "POST", params);

		// 4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	/**
	 * 获取打卡结果
	 * 
	 * @param         params{ "workDateFrom": "yyyy-MM-dd HH:mm:ss", "workDateTo":
	 *                "yyyy-MM-dd HH:mm:ss", "userIdList":["员工UserId列表"], //
	 *                必填，与offset和limit配合使用 "offset":0, //
	 *                必填，第一次传0，如果还有多余数据，下次传之前的offset加上limit的值 "limit":1, //
	 *                必填，表示数据条数，最大不能超过50条 }
	 * @param request
	 * @return
	 */
	public static JSONObject getClock_the(String params, HttpServletRequest request) {
		// 重新加载全局变量
		DingtalkParamesUtil.init();
		String accessToken = DingTalkUtil
				.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret, DingtalkParamesUtil.tokenDD)
				.getToken();
		// 2.获取请求的url
		String mat_url = Clock_the_url.replace("ACCESS_TOKEN", accessToken);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "POST", params);

		// 4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	/**
	 * 获取请假时长
	 * 
	 * @param         params{ "userid": "", "from_date": "yyyy-MM-dd HH:mm:ss",
	 *                "to_date":"yyyy-MM-dd HH:mm:ss" }
	 * @param request
	 * @return
	 */
	public static JSONObject getLeave_time(String params, HttpServletRequest request) {
		// 重新加载全局变量
		DingtalkParamesUtil.init();
		String accessToken = DingTalkUtil
				.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret, DingtalkParamesUtil.tokenDD)
				.getToken();
		// 2.获取请求的url
		String mat_url = Leave_time_url.replace("ACCESS_TOKEN", accessToken);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "POST", params);

		// 4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	/**
	 * 查询请假状态
	 * 
	 * @param         params{ "userid_list": "33,44", "start_time": 1538323200000,
	 *                // 开始时间 ，UNIX时间戳，支持最多180天的查询 "end_time": 1538323200000, //结束时间
	 *                ，UNIX时间戳，支持最多180天的查询时间 "offset": 0, //分页偏移，非负整数 "size": 20
	 *                //分页大小，正整数，最大20 }
	 * @param request
	 * @return
	 */
	public static JSONObject getLeave_state(String params, HttpServletRequest request) {
		// 重新加载全局变量
		DingtalkParamesUtil.init();
		String accessToken = DingTalkUtil
				.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret, DingtalkParamesUtil.tokenDD)
				.getToken();
		// 2.获取请求的url
		String mat_url = Leave_state_url.replace("ACCESS_TOKEN", accessToken);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "POST", params);

		// 4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	/**
	 * 获取用户考勤组
	 * 
	 * @param         params{ "userid": "3344" //员工在企业内的UserID }
	 * @param request
	 * @return
	 */
	public static JSONObject getUser_group(String params, HttpServletRequest request) {
		// 重新加载全局变量
		DingtalkParamesUtil.init();
		String accessToken = DingTalkUtil
				.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret, DingtalkParamesUtil.tokenDD)
				.getToken();
		// 2.获取请求的url
		String mat_url = get_Group_check_url.replace("ACCESS_TOKEN", accessToken);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "POST", params);

		// 4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	/**
	 * 获取用户详情
	 * 
	 * @param params  员工ID
	 * @param request
	 * @return
	 */
	public static JSONObject getUser_details(String params, HttpServletRequest request) {
		// 重新加载全局变量
		DingtalkParamesUtil.init();
		String accessToken = DingTalkUtil
				.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret, DingtalkParamesUtil.tokenDD)
				.getToken();
		// 2.获取请求的url
		String mat_url = User_details_url.replace("ACCESS_TOKEN", accessToken).replace("USER_ID", params);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "GET", null);

		// 4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	/**
	 * 获取部门用户userid列表
	 * 
	 * @param params  部门ID
	 * @param request
	 * @return
	 */
	public static JSONObject getDept_user(String params, HttpServletRequest request) {
		// 重新加载全局变量
		DingtalkParamesUtil.init();
		String accessToken = DingTalkUtil
				.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret, DingtalkParamesUtil.tokenDD)
				.getToken();
		// 2.获取请求的url
		String mat_url = Dept_user_url.replace("ACCESS_TOKEN", accessToken).replace("DEPTID", params);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "GET", null);

		// 4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	/**
	 * 获取部门用户List
	 * 
	 * @param params  { deptId 获取的部门id offset 支持分页查询，与size参数同时设置时才生效，此参数代表偏移量 size
	 *                支持分页查询，与offset参数同时设置时才生效，此参数代表分页大小，最大100 }
	 * @param request
	 * @return
	 */
	public static JSONObject getDept_user_list(String params, HttpServletRequest request) {
		// 重新加载全局变量
		DingtalkParamesUtil.init();
		String accessToken = DingTalkUtil
				.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret, DingtalkParamesUtil.tokenDD)
				.getToken();
		// 2.获取请求的url
		// 参数
		JSONObject parm = new JSONObject(params);
		String deptId = parm.get("deptId").toString(), offset = parm.get("offset").toString(),
				size = parm.get("size").toString();
		String mat_url = Dept_user_list_url.replace("ACCESS_TOKEN", accessToken).replace("DEPTID", deptId)
				.concat("&offset=OFFSET&size=SIZE".replace("OFFSET", offset).replace("SIZE", size));
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "GET", null);
		// 4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	/**
	 * 获取部门用户详情
	 * 
	 * @param params  { deptId 获取的部门id offset 支持分页查询，与size参数同时设置时才生效，此参数代表偏移量 size
	 *                支持分页查询，与offset参数同时设置时才生效，此参数代表分页大小，最大100 }
	 * @param request
	 * @return
	 */
	public static JSONObject getDept_user_details(String params, HttpServletRequest request) {
		// 重新加载全局变量
		DingtalkParamesUtil.init();
		String accessToken = DingTalkUtil
				.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret, DingtalkParamesUtil.tokenDD)
				.getToken();
		// 2.获取请求的url
		// 参数
		JSONObject parm = new JSONObject(params);
		String deptId = parm.get("deptId").toString(), offset = parm.get("offset").toString(),
				size = parm.get("size").toString();
		String mat_url = Dept_user_details_url.replace("ACCESS_TOKEN", accessToken).replace("DEPTID", deptId)
				.concat("&offset=OFFSET&size=SIZE".replace("OFFSET", offset).replace("SIZE", size));
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "GET", null);
		// 4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	/**
	 * 获取子部门ID列表
	 * 
	 * @param params  获取的部门id
	 * @param request
	 * @return
	 */
	public static JSONObject getDept_child(String params, HttpServletRequest request) {
		// 重新加载全局变量
		DingtalkParamesUtil.init();
		String accessToken = DingTalkUtil
				.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret, DingtalkParamesUtil.tokenDD)
				.getToken();
		// 2.获取请求的url
		String mat_url = Dept_child_url.replace("ACCESS_TOKEN", accessToken).replace("DEPTID", params);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "GET", null);
		// 4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	/**
	 * 获取部门列表
	 * 
	 * @param params  { child 是否递归部门的全部子部门(true,false) deptId 父部门id(根部门ID为1) }
	 * @param request
	 * @return
	 */
	public static JSONObject getDept(String params, HttpServletRequest request) {
		// 重新加载全局变量
		DingtalkParamesUtil.init();
		String accessToken = DingTalkUtil
				.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret, DingtalkParamesUtil.tokenDD)
				.getToken();
		// 2.获取请求的url
		// 参数
		JSONObject parm = new JSONObject(params);
		String child = parm.get("child").toString(), deptId = parm.get("deptId").toString();
		String mat_url = Dept_url.replace("ACCESS_TOKEN", accessToken)
				.concat("&fetch_child=CHILD&id=DEPTID".replace("CHILD", child).replace("DEPTID", deptId));
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "GET", null);
		// 4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	/**
	 * 获取部门列表
	 * 
	 * @param params  部门id
	 * @param request
	 * @return
	 */
	public static JSONObject getDept_details(String params, HttpServletRequest request) {
		// 重新加载全局变量
		DingtalkParamesUtil.init();
		String accessToken = DingTalkUtil
				.getAccessToken(DingtalkParamesUtil.AppKey, DingtalkParamesUtil.AppSecret, DingtalkParamesUtil.tokenDD)
				.getToken();
		// 2.获取请求的url
		String mat_url = Dept_details_url.replace("ACCESS_TOKEN", accessToken).replace("DEPTID", params);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "GET", null);
		// 4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

}
