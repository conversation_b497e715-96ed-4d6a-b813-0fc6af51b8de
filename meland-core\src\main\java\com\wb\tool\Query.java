package com.wb.tool;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;

import javax.servlet.http.HttpServletRequest;

import org.json.JSONArray;
import org.json.JSONObject;

import com.wb.common.Str;
import com.wb.common.Var;
import com.wb.util.DbUtil;
import com.wb.util.JsonUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;
import com.wb.util.WebUtil;

/**
 * 执行数据库SQL语句的工具类。
 */
public class Query {
	/** 请求对象，用于读取参数和存储数据。在该请求结束后系统自动关闭和释放资源。 */
	public HttpServletRequest request;
	/** SQL语句。 */
	public String sql;
	/** 数据库连接jndi。 */
	public String jndi;
	/** 执行批处理时，指定数据源来自request中存储的该变量。 */
	public String arrayName;
	/** 执行批处理时，指定数据源来自JSONArray对象。  */
	public JSONArray arrayData;
	/** 是否允许批处理操作。 */
	public boolean batchUpdate;
	/** 执行何种SQL操作，可为"query","update","execute","call"，默认为自动。 */
	public String type;
	/** 是否自动加载输出参数，可为"auto","load","none"，默认为自动。 */
	public String loadParams;
	public boolean returnStatement;
	/** 执行何种数据库事务操作，可为"start","commit","none"。 */
	public String transaction;
	/** 数据库事务隔离级别，可为"readCommitted","readUncommitted","repeatableRead","serializable"。 */
	public String isolation;
	/** 指定插入、更改或删除记录操作是否有且只有1条。 */
	public boolean uniqueUpdate;
	/** 当该值不为空且查询结果集不为空，系统将抛出该信息的异常。 */
	public String errorText;
	/** 用于调试的SQL语句。 */
	private String debugSql;
	/** 经过格式化后可直接运行的SQL语句。 */
	private String formattedSql;
	/** 参数列表，0项名称，1项类型，2项标识（true输出参数，false输出参数）。 */
	private ArrayList<Object[]> paramList;
	/** 参数值列表 */
	private ArrayList<String> paramValList;
	/** 参数数量 */
	private int paramCount;
	/** 用于执行SQL的statement对象 */
	private PreparedStatement statement;

	/**
	 * 运行SQL，并返回运行结果。如果SQL为空或disabled为true将直接返回null。
	 * @return 运行的结果。可能值为结果集，影响记录数或输出参数结果Map。
	 */
	public Object run() throws Exception {
		checkProperties();
		boolean hasArray = (this.arrayData != null) || (!StringUtil.isEmpty(this.arrayName));

		Object result = null;

		this.sql = this.sql.trim();
		replaceMacros();
		Connection connection = DbUtil.getConnection(this.request, this.jndi);
		boolean isCommit = "commit".equals(this.transaction);
		if (isCommit) {
			if (connection.getAutoCommit())
				this.transaction = "start";
		} else if ((StringUtil.isEmpty(this.transaction)) && ((this.uniqueUpdate) || (hasArray))
				&& (connection.getAutoCommit()))
			this.transaction = "start";
		if ("start".equals(this.transaction))
			DbUtil.startTransaction(connection, this.isolation);
		if (StringUtil.isEmpty(this.type)) {
			if (this.sql.startsWith("{"))
				this.type = "call";
			else
				this.type = "execute";
		}
		boolean isCall = "call".equals(this.type);
		if (isCall)
			this.statement = connection.prepareCall(this.formattedSql);
		else
			this.statement = connection.prepareStatement(this.formattedSql);
		if (Var.fetchSize != -1)
			this.statement.setFetchSize(Var.fetchSize);
			
		// JDBC级别行数限制 - 仅对查询操作限制结果集大小
		boolean isQueryOperation = "query".equals(this.type) || 
								  (StringUtil.isEmpty(this.type) && 
								  (this.sql.toLowerCase().startsWith("select") || 
								   this.sql.toLowerCase().startsWith("with")));
								   
		if (isQueryOperation) {
			// 获取适当的行数限制
			int maxRows = -1; // 默认不限制
			
			// 导出场景使用limitExportRecords，否则使用limitRecords
			if (WebUtil.exists(this.request, "sys.fromExport")) {
				Object exportLimit = WebUtil.fetchObject(this.request, "limitExportRecords");
				if (exportLimit != null && StringUtil.isInteger(exportLimit.toString())) {
					maxRows = Integer.parseInt(exportLimit.toString());
				} else {
					// 使用全局配置的导出限制
					maxRows = Var.limitExportRecords; 
				}
			} else {
				Object normalLimit = WebUtil.fetchObject(this.request, "limitRecords");
				if (normalLimit != null && StringUtil.isInteger(normalLimit.toString())) {
					maxRows = Integer.parseInt(normalLimit.toString());
				} else {
					// 使用全局配置的查询限制
					maxRows = Var.limitRecords;
				}
			}
			
			// 应用JDBC行数限制（如果maxRows为正值）
			if (maxRows > 0) {
				this.statement.setMaxRows(maxRows);
				// 可选：记录日志
				if (Var.debug) {
					Console.log(this.request, "已设置查询结果最大行数: " + maxRows);
				}
			}
		}
		
		WebUtil.setObject(this.request, SysUtil.getId(), this.statement);
		regParameters();
		if (hasArray) {
			executeBatch();
		} else {
			if (Var.debug)
				printSql();
			if ("query".equals(this.type)) {
				result = this.statement.executeQuery();
				// 如果返回多个resultset,可通过result获得statement进而获得更多resultset
				WebUtil.setObject(this.request, SysUtil.getId(), result);
			} else if ("update".equals(this.type)) {
				int affectedRows = this.statement.executeUpdate();
				result = Integer.valueOf(affectedRows);
				if ((this.uniqueUpdate) && (affectedRows != 1))
					notUnique();
			} else {
				boolean autoLoadParams = (StringUtil.isEmpty(this.loadParams)) || ("auto".equals(this.loadParams));
				if (this.statement.execute()) {
					if (autoLoadParams)
						this.loadParams = "none";
					result = this.statement.getResultSet();
					WebUtil.setObject(this.request, SysUtil.getId(), result);
				} else {
					if (autoLoadParams)
						this.loadParams = "load";
					int affectedRows = this.statement.getUpdateCount();
					result = Integer.valueOf(affectedRows);
					if ((this.uniqueUpdate) && (affectedRows != 1))
						notUnique();
				}
				if ((isCall) && ((this.paramCount > 0) || (this.returnStatement))) {
					HashMap<String, Object> map = getOutParameter("load".equals(this.loadParams));
					if (map.size() > 0) {
						if (map.containsKey("return"))
							throw new IllegalArgumentException("Invalid output parameter name \"return\"");
						map.put("return", result);
						result = map;
					}
				}
			}
		}
		if (isCommit) {
			connection.commit();
			connection.setAutoCommit(true);
		}
		checkError(result);
		return result;
	}

	/**
	 * 检查参数的合法性，如果非法将抛出异常。
	 */
	private void checkProperties() {
		if (!StringUtil.isEmpty(this.transaction)) {
			String[] trans = { "start", "commit", "none" };
			if (StringUtil.indexOf(trans, this.transaction) == -1)
				throw new IllegalArgumentException("Invalid transaction \"" + this.transaction + "\".");
		}
		if (!StringUtil.isEmpty(this.loadParams)) {
			String[] params = { "auto", "load", "none" };
			if (StringUtil.indexOf(params, this.loadParams) == -1)
				throw new IllegalArgumentException("Invalid loadParams \"" + this.loadParams + "\".");
		}
		if (!StringUtil.isEmpty(this.type)) {
			String[] types = { "query", "update", "execute", "call" };
			if (StringUtil.indexOf(types, this.type) == -1)
				throw new IllegalArgumentException("Invalid type \"" + this.type + "\".");
		}
		if (!StringUtil.isEmpty(this.isolation)) {
			String[] isolations = { "readCommitted", "readUncommitted", "repeatableRead", "serializable" };
			if (StringUtil.indexOf(isolations, this.isolation) == -1)
				throw new IllegalArgumentException("Invalid isolation \"" + this.isolation + "\".");
		}
	}

	/**
	 * 根据arrayName指定的数组参数名称，执行批处理操作。
	 * 批处理中的记录如果未指定参数值将引用最后一次设置的值。
	 * @param arrayName 在parameters或attribute中的数据参数名称。
	 * @throws Exception 执行过程发生异常。
	 */
	private void executeBatch() throws Exception {
		JSONArray ja;
		if (this.arrayData == null) {
			Object obj = WebUtil.fetchObject(this.request, this.arrayName);
			if ((obj instanceof JSONArray)) {
				ja = (JSONArray) obj;
			} else {
				if (obj == null)
					return;
				String val = obj.toString();
				if (val.isEmpty())
					return;
				ja = new JSONArray(val);
			}
		} else {
			ja = this.arrayData;
		}
		int j = ja.length();
		if (j == 0)
			return;
		for (int i = 0; i < j; i++) {
			JSONObject jo = ja.getJSONObject(i);
			for (int k = 0; k < this.paramCount; k++) {
				Object[] param = (Object[]) this.paramList.get(k);
				String name = (String) param[0];
				if ((!((Boolean) param[2]).booleanValue()) && (jo.has(name))) {
					Object valObj = JsonUtil.opt(jo, name);
					DbUtil.setObject(this.statement, k + 1, ((Integer) param[1]).intValue(), valObj);
					if (Var.debug)
						this.paramValList.set(k, StringUtil.toString(valObj));
				}
			}
			if (Var.debug)
				printSql();
			if (this.batchUpdate) {
				this.statement.addBatch();
			} else {
				int affectedRows = this.statement.executeUpdate();
				if ((this.uniqueUpdate) && (affectedRows != 1))
					notUnique();
			}
		}
		if (this.batchUpdate)
			this.statement.executeBatch();
		// 批处理时不判断uniqueUpdate，因为不是所有数据库都支持
		// 批处理如果要使用uniqueUpdate必须设置batchUpdate为false
	}

	/**
	 * 抛出更新不唯一异常。
	 */
	private void notUnique() {
		throw new RuntimeException(Str.format(request, "updateNotUnique"));
	}

	/**
	 * 替换SQL中的宏参数为SQL参数。
	 */
	private void replaceMacros() {
		StringBuilder buf = new StringBuilder();
		int startPos = 0;
		int endPos = 0;
		int lastPos = 0;

		while (((startPos = this.sql.indexOf("{?", startPos)) > -1)
				&& ((endPos = this.sql.indexOf("?}", endPos)) > -1)) {
			buf.append(this.sql.substring(lastPos, startPos));
			startPos += 2;
			endPos += 2;
			buf.append("'{?");
			buf.append(this.sql.substring(endPos - 1, endPos));
			buf.append('\'');
			lastPos = endPos;
		}
		buf.append(this.sql.substring(lastPos));
		this.debugSql = buf.toString();
		this.formattedSql = StringUtil.replaceAll(this.debugSql, "'{?}'", "?");
	}

	/**
	 * 注册输入和输出参数。
	 * @throws Exception 注册参数发生异常。
	 */
	private void regParameters() throws Exception {
		int index = 1;
		int startPos = 0;
		int endPos = 0;

		this.paramList = new ArrayList<Object[]>();
		if (Var.debug)
			this.paramValList = new ArrayList<String>();
		CallableStatement callStatement;
		if ((this.statement instanceof CallableStatement))
			callStatement = (CallableStatement) this.statement;
		else
			callStatement = null;
		boolean isCall = callStatement != null;
		while (((startPos = this.sql.indexOf("{?", startPos)) > -1)
				&& ((endPos = this.sql.indexOf("?}", endPos)) > -1)) {
			startPos += 2;
			String param = this.sql.substring(startPos, endPos);
			endPos += 2;
			String orgParam = param;
			boolean isOutParam = (isCall) && (param.startsWith("@"));
			int type;
			String paraName;
			if (isOutParam) {
				param = param.substring(1);
				int dotPos = param.indexOf('.');
				String typeText;
				if (dotPos == -1) {
					typeText = "varchar";
					paraName = param;
				} else {
					typeText = param.substring(0, dotPos);
					paraName = param.substring(dotPos + 1);
				}
				boolean hasSub = typeText.indexOf('=') != -1;
				if (hasSub) {
					Integer typeObj = DbUtil.getFieldType(StringUtil.getNamePart(typeText));
					if (typeObj == null)
						throw new Exception("Invalid type " + typeText);
					type = typeObj.intValue();
					int subType = Integer.parseInt(StringUtil.getValuePart(typeText));
					callStatement.registerOutParameter(index, type, subType);
				} else {
					Integer typeObj = DbUtil.getFieldType(typeText);
					if (typeObj == null)
						throw new Exception("Invalid type " + typeText);
					type = typeObj.intValue();
					callStatement.registerOutParameter(index, type);
				}
				if (Var.debug) {
					// 输出参数直接输出参数类型和名称
					this.paramValList.add(orgParam);
				}
			} else {
				int dotPos = param.indexOf('.');
				if (dotPos == -1) {
					type = 12;
					paraName = param;
				} else {
					Integer typeObj = DbUtil.getFieldType(param.substring(0, dotPos));
					if (typeObj == null) {
						type = 12;
						paraName = param;
					} else {
						type = typeObj.intValue();
						paraName = param.substring(dotPos + 1);
					}
				}
				Object obj = WebUtil.fetchObject(this.request, paraName);
				DbUtil.setObject(this.statement, index, type, obj);
				if (Var.debug) {
					// 输入参数输出替换后的值
					this.paramValList.add(StringUtil.toString(obj));
				}
			}
			Object[] paramObjects = new Object[3];
			paramObjects[0] = paraName;
			paramObjects[1] = Integer.valueOf(type);
			paramObjects[2] = Boolean.valueOf(isOutParam);
			this.paramList.add(paramObjects);
			index++;
		}
		this.paramCount = this.paramList.size();
	}

	/**
	 * 获得输出参数值。
	 * @param loadOutParams 是否自动加载输出参数到Map中。true加载所有参数，false返回statement本身。
	 * @return 输出参数组成的map。
	 */
	private HashMap<String, Object> getOutParameter(boolean loadOutParams) throws Exception {
		CallableStatement st = (CallableStatement) this.statement;
		HashMap<String, Object> map = new HashMap<String, Object>();

		map.put("sys.statement", st);
		if (!loadOutParams)
			return map;
		for (int i = 0; i < this.paramCount; i++) {
			Object[] param = (Object[]) this.paramList.get(i);
			if (((Boolean) param[2]).booleanValue()) {
				Object object = DbUtil.getObject(st, i + 1, ((Integer) param[1]).intValue());
				if ((object instanceof ResultSet))
					WebUtil.setObject(this.request, SysUtil.getId(), object);
				map.put((String) param[0], object);
			}
		}
		return map;
	}

	/**
	 * 如果结果集不为空且指定errorText属性，将抛出该信息的异常。
	 */
	private void checkError(Object object) throws Exception {
		if ((!StringUtil.isEmpty(this.errorText)) && ((object instanceof ResultSet))) {
			ResultSet rs = (ResultSet) object;
			if (rs.next())
				throw new RuntimeException(this.errorText);
		}
	}

	/**
	 * 打印用于调试的SQL语句。
	 */
	private void printSql() {
		String sql = this.debugSql;

		for (String s : this.paramValList) {
			sql = StringUtil.replaceFirst(sql, "{?}", s);
		}
		Console.log(this.request, sql);
	}
}