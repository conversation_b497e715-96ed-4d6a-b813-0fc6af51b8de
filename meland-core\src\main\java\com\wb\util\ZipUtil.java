package com.wb.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.io.IOUtils;

/**
 * 压缩工具方法类。
 */
public class ZipUtil {
	/**
	 * 压缩文件列表到输入流中。
	 *
	 * @param source       需要压缩的文件列表。
	 * @param outputStream 压缩的文件输出到该流。
	 * @throws IOException 压缩过程中发生异常。
	 */
	public static void zip(File source[], OutputStream outputStream) throws IOException {
		ZipArchiveOutputStream zipStream = new ZipArchiveOutputStream(outputStream);
		try {
			for (File file : source)
				zip(file, zipStream, file.getName());
		} finally {
			if (zipStream != null) {
				zipStream.closeArchiveEntry();
				zipStream.close();
			}
		}
	}

	/**
	 * 压缩文件列表到指定文件中。
	 *
	 * @param source  需要压缩的文件列表。
	 * @param zipFile 压缩的文件输出到该文件。
	 * @throws IOException 压缩过程中发生异常。
	 */
	public static void zip(File source[], File zipFile) throws Exception {
		zip(source, new FileOutputStream(zipFile));
	}

	/**
	 * 压缩文件或目录指输出流。
	 *
	 * @param source    被压缩的文件。
	 * @param zipStream 输出的文件。
	 * @param base      文件地址。
	 * @throws IOException 压缩过程发生异常。
	 */
	private static void zip(File source, ZipArchiveOutputStream zipStream, String base) throws IOException {
		ZipArchiveEntry entry;

		if (source.isDirectory()) {
			entry = new ZipArchiveEntry(base + '/');
			entry.setTime(source.lastModified());
			zipStream.putArchiveEntry(entry);
			if (!StringUtil.isEmpty(base))
				base += '/';
			File[] fileList = FileUtil.listFiles(source);
			for (File file : fileList)
				zip(file, zipStream, base + file.getName());
		} else {
			entry = new ZipArchiveEntry(base);
			entry.setTime(source.lastModified());
			zipStream.putArchiveEntry(entry);
			FileInputStream in = new FileInputStream(source);
			try {
				IOUtils.copy(in, zipStream);
			} finally {
				in.close();
			}
		}
	}

	/**
	 * 解压缩流中的文件至指定目录。
	 *
	 * @param inputStream 需要解压缩的流。
	 * @param dest        流中的文件解压缩到该目录。
	 * @throws IOException 解压缩过程发生异常。
	 */
	public static void unzip(InputStream inputStream, File dest) throws IOException {
		ZipArchiveInputStream zipStream = new ZipArchiveInputStream(inputStream);
		ArchiveEntry z;
		try {
			while ((z = zipStream.getNextEntry()) != null) {
				String name = z.getName();
				File f;
				if (z.isDirectory()) {
					name = name.substring(0, name.length() - 1);
					f = new File(dest, name);
					if (!f.exists()) {
						f.mkdir();
					}
				} else {
					f = new File(dest, name);
					if (!f.exists()) {
						f.createNewFile();
					}
					FileOutputStream out = new FileOutputStream(f);
					try {
						IOUtils.copy(zipStream, out);
					} finally {
						out.close();
					}
				}
				f.setLastModified(z.getLastModifiedDate().getTime());
			}

		} finally {
			zipStream.close();
		}
	}

	/**
	 * 解压缩压缩文件中的文件至指定目录。
	 *
	 * @param zipFile 需要解压缩的文件。
	 * @param dest    文件解压缩到该目录。
	 * @throws IOException 解压缩过程发生异常。
	 */
	public static void unzip(File zipFile, File dest) throws IOException {
		unzip(new FileInputStream(zipFile), dest);
	}
}