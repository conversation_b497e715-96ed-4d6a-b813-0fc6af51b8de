package com.wb.rocketmq.expense;

import com.wb.common.Var;
import com.wb.rocketmq.config.MqConfig;
import com.wb.rocketmq.config.RocketMQConfiguration;
import com.wb.rocketmq.listener.AsyncMessageListener;
import com.wb.rocketmq.listener.SequentialMessageListener;
import com.wb.rocketmq.listener.SyncMessageListener;
import com.wb.rocketmq.listener.TimeDelayMessageListener;
import com.wb.rocketmq.util.MessageUtil;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;

public class MessageConsume {

    /**
     * 消息消费服务管理器
     */
    private static RocketMQConsumerManager consumerManager;

    /**
     * 消费处理加载
     */
    public static void load() {
        // 确保RocketMQ配置初始化
        // RocketMQConfiguration.init(); // 在Web应用中不需要设置守护线程
        
        MqConfig config = new MqConfig();
        MessageUtil.linker();
        
        // 获取消费者管理器实例
        consumerManager = RocketMQConsumerManager.getInstance();
        
        // 同步消息接收
        if (!StringUtil.isEmpty(config.getSyncGroupId()) && !StringUtil.isEmpty(config.getSyncTopic())) {
            consumerManager.startConsumer("syncConsumer", new SyncMessageListener(), 
                config.getSyncGroupId(), config.getSyncTopic());
            LogUtil.info(StringUtil.format("同步消息消费者已启动，消费者组：{0}，主题：{1}", 
                config.getSyncGroupId(), config.getSyncTopic()));
        }
        
        // 异步消息接收
        if (!StringUtil.isEmpty(config.getAsyncGroupId()) && !StringUtil.isEmpty(config.getAsyncTopic())) {
            consumerManager.startConsumer("asyncConsumer", new AsyncMessageListener(), 
                config.getAsyncGroupId(), config.getAsyncTopic());
            LogUtil.info(StringUtil.format("异步消息消费者已启动，消费者组：{0}，主题：{1}", 
                config.getAsyncGroupId(), config.getAsyncTopic()));
        }
        
        // 顺序消息接收，开启了顺序消息接收才启动线程
        if (Var.getBool("sys.config.aliyun.cusOrderMsg") && !StringUtil.isEmpty(config.getSequentialGroupId()) && !StringUtil.isEmpty(config.getSequentialTopic())) {
            consumerManager.startConsumer("seqConsumer", new SequentialMessageListener(),
                config.getSequentialGroupId(), config.getSequentialTopic());
            LogUtil.info(StringUtil.format("顺序消息消费者已启动，消费者组：{0}，主题：{1}", 
                config.getSequentialGroupId(), config.getSequentialTopic()));
        }
        
        // 定时/延时消息接收
        if (!StringUtil.isEmpty(config.getTimeDelayGroupId()) && !StringUtil.isEmpty(config.getTimeDelayTopic())) {
            consumerManager.startConsumer("timeConsumer", new TimeDelayMessageListener(),
                config.getTimeDelayGroupId(), config.getTimeDelayTopic());
            LogUtil.info(StringUtil.format("定时/延时消息消费者已启动，消费者组：{0}，主题：{1}", 
                config.getTimeDelayGroupId(), config.getTimeDelayTopic()));
        }
    }

    /**
     * 关闭消息消费服务线程
     */
    public static void shutdown() {
        if (consumerManager != null) {
            consumerManager.shutdown();
            LogUtil.info("所有消息消费服务已关闭");
        }
    }
}
