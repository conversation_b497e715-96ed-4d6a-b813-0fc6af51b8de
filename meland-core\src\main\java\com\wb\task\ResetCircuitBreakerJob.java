package com.wb.task;

import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.wb.circuit.XwlCircuitBreakerService;
import com.wb.common.Var;
import com.wb.util.StringUtil;

/**
 * Quartz Job 实现类，用于每日定时重置所有 XWL 熔断器的状态。 使用 @DisallowConcurrentExecution
 * 注解确保同一时间只有一个实例在运行。
 */
@DisallowConcurrentExecution
public class ResetCircuitBreakerJob implements Job {

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        String jobDesc = "每日自动重置所有熔断器状态任务";
        if (context != null && context.getJobDetail() != null && context.getJobDetail().getDescription() != null) {
            jobDesc = context.getJobDetail().getDescription();
        }
        try {
            XwlCircuitBreakerService circuitBreakerService = XwlCircuitBreakerService.getInstance();
            if (circuitBreakerService != null) {
                // 调用核心服务的重置所有熔断器方法
                circuitBreakerService.resetAllCircuitBreakers();
            } else {
                // 抛出异常让 Quartz 知道任务失败
                throw new JobExecutionException("无法获取 XwlCircuitBreakerService 实例");
            }
        } catch (Throwable e) {
            if (Var.printError)
                // 将原始异常包装在 JobExecutionException 中，通知 Quartz 任务执行失败
                throw new JobExecutionException(StringUtil.concat("执行任务失败: ", jobDesc), e, false); // false 表示 Quartz
        }
    }
}