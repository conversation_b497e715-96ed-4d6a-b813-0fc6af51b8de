!function(e,t,i,s,n){function l(e){return i.translate(e)||e}function o(e){e.id=e.attr("id"),e.html('<div class="plupload_wrapper"><div class="ui-widget-content plupload_container"><div class="ui-state-default ui-widget-header plupload_header"><div class="plupload_header_content"><div class="plupload_logo"> </div><div class="plupload_header_title">'+l("Select files")+"</div>"+'<div class="plupload_header_text">'+l("Add files to the upload queue and click the start button.")+"</div>"+'<div class="plupload_view_switch">'+'<input type="radio" id="'+e.id+'_view_list" name="view_mode_'+e.id+'" checked="checked" /><label class="plupload_button" for="'+e.id+'_view_list" data-view="list">'+l("List")+"</label>"+'<input type="radio" id="'+e.id+'_view_thumbs" name="view_mode_'+e.id+'" /><label class="plupload_button"  for="'+e.id+'_view_thumbs" data-view="thumbs">'+l("Thumbnails")+"</label>"+"</div>"+"</div>"+"</div>"+'<table class="plupload_filelist plupload_filelist_header ui-widget-header">'+"<tr>"+'<td class="plupload_cell plupload_file_name">'+l("Filename")+"</td>"+'<td class="plupload_cell plupload_file_status">'+l("Status")+"</td>"+'<td class="plupload_cell plupload_file_size">'+l("Size")+"</td>"+'<td class="plupload_cell plupload_file_action">&nbsp;</td>'+"</tr>"+"</table>"+'<div class="plupload_content">'+'<div class="plupload_droptext">'+l("Drag files here.")+"</div>"+'<ul class="plupload_filelist_content"> </ul>'+'<div class="plupload_clearer">&nbsp;</div>'+"</div>"+'<table class="plupload_filelist plupload_filelist_footer ui-widget-header">'+"<tr>"+'<td class="plupload_cell plupload_file_name">'+'<div class="plupload_buttons"><!-- Visible -->'+'<a class="plupload_button plupload_add">'+l("Add Files")+"</a>&nbsp;"+'<a class="plupload_button plupload_start">'+l("Start Upload")+"</a>&nbsp;"+'<a class="plupload_button plupload_stop plupload_hidden">'+l("Stop Upload")+"</a>&nbsp;"+"</div>"+'<div class="plupload_started plupload_hidden"><!-- Hidden -->'+'<div class="plupload_progress plupload_right">'+'<div class="plupload_progress_container"></div>'+"</div>"+'<div class="plupload_cell plupload_upload_status"></div>'+'<div class="plupload_clearer">&nbsp;</div>'+"</div>"+"</td>"+'<td class="plupload_file_status"><span class="plupload_total_status">0%</span></td>'+'<td class="plupload_file_size"><span class="plupload_total_file_size">0 kb</span></td>'+'<td class="plupload_file_action"></td>'+"</tr>"+"</table>"+"</div>"+'<input class="plupload_count" value="0" type="hidden">'+"</div>")}var a={};n.widget("ui.plupload",{widgetEventPrefix:"",contents_bak:"",options:{browse_button_hover:"ui-state-hover",browse_button_active:"ui-state-active",filters:{},buttons:{browse:!0,start:!0,stop:!0},views:{list:!0,thumbs:!1,active:"list",remember:!0},thumb_width:100,thumb_height:60,multiple_queues:!0,dragdrop:!0,autostart:!1,sortable:!1,rename:!1},FILE_COUNT_ERROR:-9001,_create:function(){var e=this.element.attr("id");e||(e=i.guid(),this.element.attr("id",e)),this.id=e,this.contents_bak=this.element.html(),o(this.element),this.container=n(".plupload_container",this.element).attr("id",e+"_container"),this.content=n(".plupload_content",this.element),n.fn.resizable&&this.container.resizable({handles:"s",minHeight:300}),this.filelist=n(".plupload_filelist_content",this.container).attr({id:e+"_filelist",unselectable:"on"}),this.browse_button=n(".plupload_add",this.container).attr("id",e+"_browse"),this.start_button=n(".plupload_start",this.container).attr("id",e+"_start"),this.stop_button=n(".plupload_stop",this.container).attr("id",e+"_stop"),this.thumbs_switcher=n("#"+e+"_view_thumbs"),this.list_switcher=n("#"+e+"_view_list"),n.ui.button&&(this.browse_button.button({icons:{primary:"ui-icon-circle-plus"},disabled:!0}),this.start_button.button({icons:{primary:"ui-icon-circle-arrow-e"},disabled:!0}),this.stop_button.button({icons:{primary:"ui-icon-circle-close"}}),this.list_switcher.button({text:!1,icons:{secondary:"ui-icon-grip-dotted-horizontal"}}),this.thumbs_switcher.button({text:!1,icons:{secondary:"ui-icon-image"}})),this.progressbar=n(".plupload_progress_container",this.container),n.ui.progressbar&&this.progressbar.progressbar(),this.counter=n(".plupload_count",this.element).attr({id:e+"_count",name:e+"_count"}),this._initUploader()},_initUploader:function(){var e,t=this,s=this.id,o={container:s+"_buttons",browse_button:s+"_browse"};n(".plupload_buttons",this.element).attr("id",s+"_buttons"),t.options.dragdrop&&(this.filelist.parent().attr("id",this.id+"_dropbox"),o.drop_element=this.id+"_dropbox"),this.filelist.on("click",function(e){var i,s=n(e.target);s.hasClass("plupload_action_icon")&&(i=s.closest(".plupload_file"),i.hasClass("plupload_delete")&&(t.removeFile(i.attr("id")),e.preventDefault()))}),e=this.uploader=a[s]=new i.Uploader(n.extend(this.options,o)),this.options=e.getOption(),t.options.views.thumbs&&(e.settings.required_features.display_media=!0),t.options.max_file_count&&i.extend(e.getOption("filters"),{max_file_count:t.options.max_file_count}),i.addFileFilter("max_file_count",function(e,i,s){e<=this.files.length-(this.total.uploaded+this.total.failed)?(t.browse_button.button("disable"),this.disableBrowse(),this.trigger("Error",{code:t.FILE_COUNT_ERROR,message:l("File count error."),file:i}),s(!1)):s(!0)}),e.bind("Error",function(e,s){var n,o="";switch(n="<strong>"+s.message+"</strong>",s.code){case i.FILE_EXTENSION_ERROR:o=i.sprintf(l("File: %s"),s.file.name);break;case i.FILE_SIZE_ERROR:o=i.sprintf(l("File: %s, size: %d, max file size: %d"),s.file.name,i.formatSize(s.file.size),i.formatSize(i.parseSize(e.getOption("filters").max_file_size)));break;case i.FILE_DUPLICATE_ERROR:o=i.sprintf(l("%s already present in the queue."),s.file.name);break;case t.FILE_COUNT_ERROR:o=i.sprintf(l("Upload element accepts only %d file(s) at a time. Extra files were stripped."),e.getOption("filters").max_file_count||0);break;case i.IMAGE_FORMAT_ERROR:o=l("Image format either wrong or not supported.");break;case i.IMAGE_MEMORY_ERROR:o=l("Runtime ran out of available memory.");break;case i.HTTP_ERROR:o=l("Upload URL might be wrong or doesn't exist.")}n+=" <br /><i>"+i.xmlEncode(details)+"</i>",t._trigger("error",null,{up:e,error:s}),s.code===i.INIT_ERROR?setTimeout(function(){t.destroy()},1):t.notify("error",n)}),e.bind("PostInit",function(e){t.options.buttons.browse?t.browse_button.button("enable"):(t.browse_button.button("disable").hide(),e.disableBrowse(!0)),t.options.buttons.start||t.start_button.button("disable").hide(),t.options.buttons.stop||t.stop_button.button("disable").hide(),!t.options.unique_names&&t.options.rename&&t._enableRenaming(),t.options.dragdrop&&e.features.dragdrop&&t.filelist.parent().addClass("plupload_dropbox"),t._enableViewSwitcher(),t.start_button.click(function(e){n(this).button("option","disabled")||t.start(),e.preventDefault()}),t.stop_button.click(function(e){t.stop(),e.preventDefault()}),t._trigger("ready",null,{up:e})}),e.init(),e.bind("FileFiltered",function(e,i){t._addFiles(i)}),e.bind("FilesAdded",function(e,i){t._trigger("selected",null,{up:e,files:i}),t.options.sortable&&n.ui.sortable&&t._enableSortingList(),t._trigger("updatelist",null,{filelist:t.filelist}),t.options.autostart&&setTimeout(function(){t.start()},10)}),e.bind("FilesRemoved",function(e,i){n.ui.sortable&&t.options.sortable&&n("tbody",t.filelist).sortable("destroy"),n.each(i,function(e,t){n("#"+t.id).toggle("highlight",function(){n(this).remove()})}),e.files.length&&t.options.sortable&&n.ui.sortable&&t._enableSortingList(),t._trigger("updatelist",null,{filelist:t.filelist}),t._trigger("removed",null,{up:e,files:i})}),e.bind("QueueChanged",function(){t._handleState()}),e.bind("StateChanged",function(e){t._handleState(),i.STARTED===e.state?t._trigger("started",null,{up:this.uploader}):i.STOPPED===e.state&&t._trigger("stopped",null,{up:this.uploader})}),e.bind("UploadFile",function(e,i){t._handleFileStatus(i)}),e.bind("FileUploaded",function(e,i,s){t._handleFileStatus(i),t._trigger("uploaded",null,{up:e,file:i,result:s})}),e.bind("UploadProgress",function(e,i){t._handleFileStatus(i),t._updateTotalProgress(),t._trigger("progress",null,{up:e,file:i})}),e.bind("UploadComplete",function(e,i){t._addFormFields(),t._trigger("complete",null,{up:e,files:i})})},_setOption:function(e,t){var i=this;"buttons"==e&&"object"==typeof t&&(t=n.extend(i.options.buttons,t),t.browse?(i.browse_button.button("enable").show(),i.uploader.disableBrowse(!1)):(i.browse_button.button("disable").hide(),i.uploader.disableBrowse(!0)),t.start?i.start_button.button("enable").show():i.start_button.button("disable").hide(),t.stop?i.start_button.button("enable").show():i.stop_button.button("disable").hide()),i.uploader.setOption(e,t)},start:function(){this.uploader.start()},stop:function(){this.uploader.stop()},enable:function(){this.browse_button.button("enable"),this.uploader.disableBrowse(!1)},disable:function(){this.browse_button.button("disable"),this.uploader.disableBrowse(!0)},getFile:function(e){var t;return t="number"==typeof e?this.uploader.files[e]:this.uploader.getFile(e)},getFiles:function(){return this.uploader.files},removeFile:function(e){"string"===i.typeOf(e)&&(e=this.getFile(e)),this.uploader.removeFile(e)},clearQueue:function(){this.uploader.splice()},getUploader:function(){return this.uploader},refresh:function(){this.uploader.refresh()},notify:function(e,t){var i=n('<div class="plupload_message"><span class="plupload_message_close ui-icon ui-icon-circle-close" title="'+l("Close")+'"></span>'+'<p><span class="ui-icon"></span>'+t+"</p>"+"</div>");i.addClass("ui-state-"+("error"===e?"error":"highlight")).find("p .ui-icon").addClass("ui-icon-"+("error"===e?"alert":"info")).end().find(".plupload_message_close").click(function(){i.remove()}).end(),n(".plupload_header",this.container).append(i)},destroy:function(){this.uploader.destroy(),n(".plupload_button",this.element).unbind(),n.ui.button&&n(".plupload_add, .plupload_start, .plupload_stop",this.container).button("destroy"),n.ui.progressbar&&this.progressbar.progressbar("destroy"),n.ui.sortable&&this.options.sortable&&n("tbody",this.filelist).sortable("destroy"),this.element.empty().html(this.contents_bak),this.contents_bak="",n.Widget.prototype.destroy.apply(this)},_handleState:function(){var e=this.uploader,t=e.files.length-(e.total.uploaded+e.total.failed),s=e.getOption("filters").max_file_count||0;i.STARTED===e.state?(n([]).add(this.stop_button).add(".plupload_started").removeClass("plupload_hidden"),this.start_button.button("disable"),this.options.multiple_queues||(this.browse_button.button("disable"),e.disableBrowse()),n(".plupload_upload_status",this.element).html(i.sprintf(l("Uploaded %d/%d files"),e.total.uploaded,e.files.length)),n(".plupload_header_content",this.element).addClass("plupload_header_content_bw")):i.STOPPED===e.state&&(n([]).add(this.stop_button).add(".plupload_started").addClass("plupload_hidden"),t?this.start_button.button("enable"):this.start_button.button("disable"),this.options.multiple_queues&&n(".plupload_header_content",this.element).removeClass("plupload_header_content_bw"),this.options.multiple_queues&&s&&s>t&&(this.browse_button.button("enable"),e.disableBrowse(!1)),this._updateTotalProgress()),0===e.total.queued?n(".ui-button-text",this.browse_button).html(l("Add Files")):n(".ui-button-text",this.browse_button).html(i.sprintf(l("%d files queued"),e.total.queued)),this.container.toggleClass("plupload_files_queued",e.files.length),e.refresh()},_handleFileStatus:function(e){var t,s,l=n("#"+e.id);if(l.length){switch(e.status){case i.DONE:t="plupload_done",s="plupload_action_icon ui-icon ui-icon-circle-check";break;case i.FAILED:t="ui-state-error plupload_failed",s="plupload_action_icon ui-icon ui-icon-alert";break;case i.QUEUED:t="plupload_delete",s="plupload_action_icon ui-icon ui-icon-circle-minus";break;case i.UPLOADING:t="ui-state-highlight plupload_uploading",s="plupload_action_icon ui-icon ui-icon-circle-arrow-w";var o=n(".plupload_scroll",this.container),a=o.scrollTop(),r=o.height(),d=l.position().top+l.height();d>r&&o.scrollTop(a+d-r),l.find(".plupload_file_percent").html(e.percent+"%").end().find(".plupload_file_progress").css("width",e.percent+"%").end().find(".plupload_file_size").html(i.formatSize(e.size))}t+=" ui-state-default plupload_file",l.attr("class",t).find(".plupload_action_icon").attr("class",s)}},_updateTotalProgress:function(){var e=this.uploader;this.filelist[0].scrollTop=this.filelist[0].scrollHeight,this.progressbar.progressbar("value",e.total.percent),this.element.find(".plupload_total_status").html(e.total.percent+"%").end().find(".plupload_total_file_size").html(i.formatSize(e.total.size)).end().find(".plupload_upload_status").html(i.sprintf(l("Uploaded %d/%d files"),e.total.uploaded,e.files.length))},_displayThumbs:function(){function e(e,t,i){var s;e.on(t,function(){clearTimeout(s),s=setTimeout(function(){clearTimeout(s),i()},300)})}function t(){if(!r||!d){var e=n(".plupload_file:eq(0)",p.filelist);r=e.outerWidth(!0),d=e.outerHeight(!0)}var t=p.content.width(),i=p.content.height();u=Math.floor(t/r),c=u*(Math.ceil(i/d)+1)}function i(){var e=Math.floor(p.content.scrollTop()/d)*u;_=n(".plupload_file .plupload_file_thumb",p.filelist).slice(e,e+c).filter(".plupload_thumb_toload").get()}function l(){function s(){"thumbs"===p.view_mode&&(t(),i(),a())}n.fn.resizable&&e(p.container,"resize",s),e(p.window,"resize",s),e(p.content,"scroll",s),p.element.on("viewchanged selected",s),s()}function o(e,t){var i=new s.image.Image,l=s.core.utils.Url.resolveUrl;i.onload=function(){var t=n("#"+e.id+" .plupload_file_thumb",p.filelist);this.embed(t[0],{width:p.options.thumb_width,height:p.options.thumb_height,crop:!0,fit:!0,preserveHeaders:!1,swf_url:l(p.options.flash_swf_url),xap_url:l(p.options.silverlight_xap_url)})},i.bind("embedded error",function(i){n("#"+e.id,p.filelist).find(".plupload_file_thumb").removeClass("plupload_thumb_loading").addClass("plupload_thumb_"+i.type),this.destroy(),setTimeout(t,1)}),n("#"+e.id,p.filelist).find(".plupload_file_thumb").removeClass("plupload_thumb_toload").addClass("plupload_thumb_loading"),i.load(e.getSource())}function a(){"thumbs"!==p.view_mode||h||(i(),_.length&&(h=!0,o(p.getFile(n(_.shift()).closest(".plupload_file").attr("id")),function(){h=!1,a()})))}var r,d,u,p=this,c=0,_=[],h=!1;this.options.views.thumbs&&this.element.on("selected",function f(){p.element.off("selected",f),l()})},_addFiles:function(e){var t,l=this,o="";t='<li class="plupload_file ui-state-default plupload_delete" id="{id}" style="width:{thumb_width}px;"><div class="plupload_file_thumb plupload_thumb_toload" style="width: {thumb_width}px; height: {thumb_height}px;"><div class="plupload_file_dummy ui-widget-content" style="line-height: {thumb_height}px;"><span class="ui-state-disabled">{ext} </span></div></div><div class="plupload_file_status"><div class="plupload_file_progress ui-widget-header" style="width: 0%"> </div><span class="plupload_file_percent">{percent} </span></div><div class="plupload_file_name" title="{name}"><span class="plupload_file_name_wrapper">{name} </span></div><div class="plupload_file_action"><div class="plupload_action_icon ui-icon ui-icon-circle-minus"> </div></div><div class="plupload_file_size">{size} </div><div class="plupload_file_fields"> </div></li>',"array"!==i.typeOf(e)&&(e=[e]),n.each(e,function(e,n){var a=s.core.utils.Mime.getFileExtension(n.name)||"none";o+=t.replace(/\{(\w+)\}/g,function(e,t){switch(t){case"thumb_width":case"thumb_height":return l.options[t];case"size":return i.formatSize(n.size);case"ext":return a;default:return i.xmlEncode(n[t]||"")}})}),l.filelist.append(o)},_addFormFields:function(){var e=this;n(".plupload_file_fields",this.filelist).html(""),i.each(this.uploader.files,function(t,s){var l="",o=e.id+"_"+s;t.target_name&&(l+='<input type="hidden" name="'+o+'_tmpname" value="'+i.xmlEncode(t.target_name)+'" />'),l+='<input type="hidden" name="'+o+'_name" value="'+i.xmlEncode(t.name)+'" />',l+='<input type="hidden" name="'+o+'_status" value="'+(t.status===i.DONE?"done":"failed")+'" />',n("#"+t.id).find(".plupload_file_fields").html(l)}),this.counter.val(this.uploader.files.length)},_viewChanged:function(e){this.options.views.remember&&n.cookie&&n.cookie("plupload_ui_view",e,{expires:7,path:"/"}),"IE"===i.ua.browser&&i.ua.version<7&&this.content.attr("style",'height:expression(document.getElementById("'+this.id+"_container"+'").clientHeight - '+("list"===e?132:102)+")"),this.container.removeClass("plupload_view_list plupload_view_thumbs").addClass("plupload_view_"+e),this.view_mode=e,this._trigger("viewchanged",null,{view:e})},_enableViewSwitcher:function(){var e,t,s,l=this,o=n(".plupload_view_switch",this.container);i.each(["list","thumbs"],function(e){l.options.views[e]||o.find('[for="'+l.id+"_view_"+e+'"], #'+l.id+"_view_"+e).remove()}),t=o.find(".plupload_button"),1===t.length?(o.hide(),e=t.eq(0).data("view"),this._viewChanged(e)):n.ui.button&&t.length>1?(this.options.views.remember&&n.cookie&&(e=n.cookie("plupload_ui_view")),~i.inArray(e,["list","thumbs"])||(e=this.options.views.active),o.show().buttonset().find(".ui-button").click(function(t){e=n(this).data("view"),l._viewChanged(e),t.preventDefault()}),s=o.find('[for="'+l.id+"_view_"+e+'"]'),s.length&&s.trigger("click")):(o.show(),this._viewChanged(this.options.views.active)),this.options.views.thumbs&&this._displayThumbs()},_enableRenaming:function(){var e=this;this.filelist.dblclick(function(t){var i,s,l,o,a,r="",d=n(t.target);d.hasClass("plupload_file_name_wrapper")&&(s=d.closest(".plupload_file"),s.hasClass("plupload_delete")&&(l=e.uploader.getFile(s[0].id),a=l.name,o=/^(.+)(\.[^.]+)$/.exec(a),o&&(a=o[1],r=o[2]),i=n('<input class="plupload_file_rename" type="text" />').width(d.width()).insertAfter(d.hide()),i.val(a).blur(function(){d.show().parent().scrollLeft(0).end().next().remove()}).keydown(function(e){var t=n(this);-1!==n.inArray(e.keyCode,[13,27])&&(e.preventDefault(),13===e.keyCode&&(l.name=t.val()+r,d.text(l.name)),t.blur())})[0].focus()))})},_enableSortingList:function(){var e=this;n(".plupload_file",this.filelist).length<2||(n("tbody",this.filelist).sortable("destroy"),this.filelist.sortable({items:".plupload_delete",cancel:"object, .plupload_clearer",stop:function(){var t=[];n.each(n(this).sortable("toArray"),function(i,s){t[t.length]=e.uploader.getFile(s)}),t.unshift(t.length),t.unshift(0),Array.prototype.splice.apply(e.uploader.files,t)}}))}})}(window,document,plupload,moxie,jQuery);