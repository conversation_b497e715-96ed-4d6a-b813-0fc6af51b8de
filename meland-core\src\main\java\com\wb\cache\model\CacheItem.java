package com.wb.cache.model;

/**
 * 缓存项类
 * 用于在本地缓存中存储带有过期时间的值
 */
public class CacheItem {
    private final Object value;
    private final long expireTime;
    
    /**
     * 创建缓存项
     * 
     * @param value 缓存的值
     * @param expireTimeMillis 过期时间（毫秒）
     */
    public CacheItem(Object value, long expireTimeMillis) {
        this.value = value;
        this.expireTime = System.currentTimeMillis() + expireTimeMillis;
    }
    
    /**
     * 获取缓存值
     * 
     * @return 值
     */
    public Object getValue() {
        return value;
    }
    
    /**
     * 检查缓存项是否已过期
     * 
     * @return 是否过期
     */
    public boolean isExpired() {
        return System.currentTimeMillis() > expireTime;
    }
} 