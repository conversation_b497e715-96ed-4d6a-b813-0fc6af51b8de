package com.wb.aliyun.mqtt;

import com.alibaba.mqtt.server.util.Tools;
import com.aliyun.onsmqtt20200420.models.RegisterDeviceCredentialResponseBody;

import com.wb.aliyun.mqtt.util.ConnectionOptionWrapper;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 消息发送
 *
 * <AUTHOR>
 * @date 2023/12/15-12:00
 */
public class MessageClient {
    private String instanceId;
    private String endPoint;
    //凭证的 endPoint
    private String endPointCredential;

    private String accessKey;
    private String secretKey;
    private Boolean isSignature;
    private String clientId;
    private Boolean isProducer;

    private static MessageClient messageClient;
    MqttClient mqttClient;
    MqttMessage message;
    ConnectionOptionWrapper connectionOptionWrapper;

    /**
     * @param instanceId
     * @param endPoint
     * @param accessKey
     * @param secretKey
     * @param isSignature
     * @param clientId
     * @param isProducer
     * @throws Exception
     */
    private MessageClient(String instanceId, String endPoint, String endPointCredential, String accessKey, String secretKey, Boolean isSignature, String clientId, Boolean isProducer) throws Exception {
        this.instanceId = instanceId;
        this.endPoint = endPoint;
        this.endPointCredential = endPointCredential;

        this.accessKey = accessKey;
        this.secretKey = secretKey;
        this.isSignature = isSignature;
        this.clientId = clientId;
        this.isProducer = isProducer;
        createConnect();
    }

    //获取消息客户端(单例)
    public static MessageClient getMessageClient(String instanceId, String endPoint, String endPointCredential, String accessKey, String secretKey, Boolean isSignature, String clientId, Boolean isProducer) throws Exception {
        if (messageClient == null) {
            messageClient = new MessageClient(instanceId, endPoint, endPointCredential, accessKey, secretKey, isSignature, clientId, isProducer);
        }
        return messageClient;
    }

    /**
     * 创建客户端并连接
     *
     * @return
     * @throws InvalidKeyException
     * @throws NoSuchAlgorithmException
     * @throws MqttException
     */
    public MqttClient createConnect() throws Exception {
        connectionOptionWrapper = new ConnectionOptionWrapper(instanceId, accessKey, secretKey, clientId);

        if (!isSignature) {//不是签名模式则使用一机以密模式
            setOneDeviceOneSecret();
        }

        final MemoryPersistence memoryPersistence = new MemoryPersistence();
        /**
         * 客户端使用的协议和端口必须匹配，具体参考文档 https://help.aliyun.com/document_detail/44866.html?spm=a2c4g.11186623.6.552.25302386RcuYFB
         * 如果是 SSL 加密则设置ssl://endpoint:8883
         */
        mqttClient = new MqttClient("tcp://" + endPoint + ":1883", clientId, memoryPersistence);

        //客户端设置好发送超时时间，防止无限阻塞
        mqttClient.setTimeToWait(5000);
        final ExecutorService executorService = new ThreadPoolExecutor(1, 1, 0, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>());

        //是消息生产者就去设置回调
        if (isProducer) {
            setCallback();
        }
        mqttClient.connect(connectionOptionWrapper.getMqttConnectOptions());

        return mqttClient;
    }

    /**
     * 设置一机一密验证
     *
     * @return
     * @throws Exception
     */
    public ConnectionOptionWrapper setOneDeviceOneSecret() throws Exception {
        CredentialInfo credentialInfo = new CredentialInfo(accessKey, secretKey,
                endPointCredential,
                instanceId, clientId);
        //注册凭证
        RegisterDeviceCredentialResponseBody.RegisterDeviceCredentialResponseBodyDeviceCredential credential = credentialInfo.registerCredential();
        connectionOptionWrapper.getMqttConnectOptions().setUserName("DeviceCredential|" + credential.getDeviceAccessKeyId() + "|" + instanceId);
        connectionOptionWrapper.getMqttConnectOptions().setPassword(Tools.macSignature(clientId, credential.getDeviceAccessKeySecret()).toCharArray());
        return connectionOptionWrapper;
    }

    /**
     * 消息发送回调
     */
    public void setCallback() {
        mqttClient.setManualAcks(true);
        mqttClient.setCallback(new MqttCallbackExtended() {
            @Override
            public void connectComplete(boolean reconnect, String serverURI) {
                //客户端连接成功后就需要尽快订阅需要的 topic
                System.out.println("连接成功");
            }

            @Override
            public void connectionLost(Throwable throwable) {
                System.out.println("连接丢失");
//                throwable.printStackTrace();
            }

            @Override
            public void messageArrived(String s, MqttMessage mqttMessage) throws Exception {
                /**
                 * 消费消息的回调接口，需要确保该接口不抛异常，该接口运行返回即代表消息消费成功。
                 * 消费消息需要保证在规定时间内完成，如果消费耗时超过服务端约定的超时时间，对于可靠传输的模式，服务端可能会重试推送，业务需要做好幂等去重处理。超时时间约定参考限制
                 * https://help.aliyun.com/document_detail/63620.html?spm=a2c4g.11186623.6.546.229f1f6ago55Fj
                 */
                System.out.println("messageArrived消息消费成功 topic : " + s + " , body : " + new String(mqttMessage.getPayload()));
            }

            @Override
            public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
                System.out.println("deliveryComplete消息发送成功topic : " + iMqttDeliveryToken.getTopics()[0]);
            }
        });
    }

    /**
     * 订阅主题
     *
     * @param topicFilter
     * @param qos
     * @throws MqttException
     */
    public void subscribeTopic(String topicFilter, int qos) throws MqttException {
        mqttClient.subscribe(topicFilter, qos, new IMqttMessageListener() {
            public void messageArrived(String topicFilter, MqttMessage message) {
                System.out.println("subscribeTopic消息被消费: " + topicFilter + " " + message);
            }
        });
    }

    /**
     * 取消订阅主题
     *
     * @param topicFilter
     * @throws MqttException
     */
    public void unSubscribeTopic(String topicFilter) throws MqttException {
        mqttClient.unsubscribe(topicFilter);
    }

    /**
     * 关闭客户端连接
     *
     * @throws MqttException
     */
    public void disconnect() throws MqttException {
        mqttClient.disconnect();
    }

    /**
     * 客户端是否连接
     *
     * @throws MqttException
     */
    public boolean isConnected() throws MqttException {
        return mqttClient.isConnected();
    }

    /**
     * 发送消息
     *
     * @param mq4IotTopic
     * @param qosLevel
     * @param messageTextArr
     * @throws MqttException
     * @throws InvalidKeyException
     * @throws NoSuchAlgorithmException
     */
    public void sendMessage(String mq4IotTopic, int qosLevel, String[] messageTextArr) throws Exception {
        for (int i = 0; i < messageTextArr.length; i++) {
            message = new MqttMessage(messageTextArr[i].getBytes());

            //QoS参数代表传输质量，可选0，1，2，根据实际需求合理设置，具体参考 https://help.aliyun.com/document_detail/42420.html?spm=a2c4g.11186623.6.544.1ea529cfAO5zV3
            message.setQos(qosLevel);

            //发送普通消息时，topic 必须和接收方订阅的 topic 一致，或者符合通配符匹配规则
            mqttClient.publish(mq4IotTopic, message);
        }
    }

    /**
     * 送消息p2p模式
     *
     * @param parentTopic
     * @param clientId
     * @param qosLevel
     * @param messageTextArr
     * @throws MqttException
     * @throws InvalidKeyException
     * @throws NoSuchAlgorithmException
     */
    public void sendMessageP2P(String parentTopic, String clientId, int qosLevel, String[] messageTextArr) throws Exception {
        for (int i = 0; i < messageTextArr.length; i++) {
            message = new MqttMessage(messageTextArr[i].getBytes());
            /**
             * MQ4IoT支持点对点消息，即如果发送方明确知道该消息只需要给特定的一个设备接收，且知道对端的 clientId，
             * 则可以直接发送点对点消息。
             * 点对点消息不需要经过订阅关系匹配，可以简化订阅方的逻辑。
             * 点对点消息的 topic 格式规范是  {{parentTopic}}/p2p/{{targetClientId}}
             */
            final String p2pSendTopic = parentTopic + "/p2p/" + clientId;
            message = new MqttMessage(messageTextArr[i].getBytes());
            message.setQos(qosLevel);
            mqttClient.publish(p2pSendTopic, message);
        }
    }
}
