---
description: 
globs: 
alwaysApply: false
---
# 项目结构指南

这是一个基于 Maven 的多模块 Java 项目。

## 主要模块

*   **[meland-core](mdc:meland-core):** 可能包含核心业务逻辑。
*   **[meland-dependencies](mdc:meland-dependencies):** 可能管理项目依赖。
*   **[meland-pay](mdc:meland-pay):** 处理支付相关功能。
*   **[meland-open-platform](mdc:meland-open-platform):** 开放平台相关接口或服务。
*   **[meland-message](mdc:meland-message):** 消息处理或通知系统。
*   **[meland-json](mdc:meland-json):** JSON 处理相关的工具或模块。
*   **[meland-web](mdc:meland-web):** Web 应用或 API 接口。

## 配置文件

*   **[pom.xml](mdc:pom.xml):** Maven 项目配置文件，定义了项目结构和依赖。

## 启动脚本

*   **[run.bat](mdc:run.bat):** Windows 环境下的启动脚本。
*   **[run.sh](mdc:run.sh):** Linux/macOS 环境下的启动脚本。

## 其他目录

*   **[doc](mdc:doc):** 项目文档。
*   **[.cursor](mdc:.cursor):** Cursor 相关配置和规则。

