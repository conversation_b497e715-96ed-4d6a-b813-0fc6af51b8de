package com.wb.common.ws;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wb.util.LogUtil;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Collection;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * WebSocket消息监听器，用于处理Redis通道中的消息
 */
public class WebSocketMessageListener implements MessageListener {
    
    private RedisMessageListenerContainer webSocketRedisContainer;
    
    private String nodeId;
    
    /**
     * 连续错误计数
     */
    private final AtomicInteger errorCounter = new AtomicInteger(0);
    
    /**
     * 最后一次错误时间
     */
    private long lastErrorTime = 0;
    
    public void setWebSocketRedisContainer(RedisMessageListenerContainer container) {
        this.webSocketRedisContainer = container;
    }
    
    /**
     * 设置节点ID
     * @param nodeId 节点唯一标识
     */
    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }
    
    @PostConstruct
    public void init() {
        // 如果节点ID未设置，则从WebSocketSessionManager获取
        if (this.nodeId == null) {
            this.nodeId = WebSocketSessionManager.getInstance().getNodeId();
        }
        
        // 注意：移除此处的监听器添加逻辑，由WebSocketRedisConfig统一添加
    }
    
    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            // 反序列化消息
            String channel = new String(message.getChannel());
            String jsonString = new String(message.getBody());
            JSONObject msgData = JSON.parseObject(jsonString);
            
            // 忽略自己发出的消息
            if (nodeId.equals(msgData.getString("sourceNodeId"))) {
                return;
            }
            
            // 处理广播消息
            if (channel.equals(WebSocketRedisConfig.WS_BROADCAST_CHANNEL)) {
                handleBroadcastMessage(msgData);
                return;
            }
            
            // 处理用户消息
            if (channel.startsWith(WebSocketRedisConfig.WS_USER_CHANNEL_PREFIX)) {
                String userId = channel.substring(WebSocketRedisConfig.WS_USER_CHANNEL_PREFIX.length());
                handleUserMessage(userId, msgData);
                return;
            }
            
            // 处理节点消息
            if (channel.equals(WebSocketRedisConfig.WS_NODE_CHANNEL_PREFIX + nodeId)) {
                handleNodeMessage(msgData);
                return;
            }
            
            // 成功处理消息后重置错误计数
            resetErrorStats();
        } catch (RedisConnectionFailureException e) {
            // 连接异常处理
            handleConnectionError(e, "处理WebSocket Redis消息时连接异常");
        } catch (Exception e) {
            LogUtil.error("处理WebSocket消息异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 处理广播消息
     */
    private void handleBroadcastMessage(JSONObject msgData) {
        String message = msgData.getString("message");
        boolean isJson = msgData.getBooleanValue("isJson");
        String targetName = msgData.getString("targetName");
        
        // 广播到所有本地会话
        Collection<WebSocketSession> sessions = WebSocketSessionManager.getInstance().getAllLocalSessions();
        for (WebSocketSession session : sessions) {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(message));
                }
            } catch (IOException e) {
                LogUtil.error("发送WebSocket广播消息异常: " + e.getMessage());
            }
        }
    }
    
    /**
     * 处理用户消息
     */
    private void handleUserMessage(String userId, JSONObject msgData) {
        String message = msgData.getString("message");
        
        // 获取用户的所有本地会话
        Set<String> sessionIds = WebSocketSessionManager.getInstance().getSessionIdsByUserId(userId);
        for (String sessionId : sessionIds) {
            WebSocketSession session = WebSocketSessionManager.getInstance().getSession(sessionId);
            try {
                if (session != null && session.isOpen()) {
                    session.sendMessage(new TextMessage(message));
                }
            } catch (IOException e) {
                LogUtil.error("发送WebSocket用户消息异常: " + e.getMessage());
            }
        }
    }
    
    /**
     * 处理节点消息
     */
    private void handleNodeMessage(JSONObject msgData) {
        String action = msgData.getString("action");
        
        if ("sessionUpdate".equals(action)) {
            // 处理会话更新
            LogUtil.info("收到会话更新通知");
            // 简单记录日志，实际更新由WebSocketSessionManager自行处理
        } else if ("ping".equals(action)) {
            // 心跳检测响应
            LogUtil.info("收到WebSocket节点心跳检测");
        }
    }
    
    /**
     * 处理连接错误
     * 
     * @param e 异常对象
     * @param message 错误消息
     */
    private void handleConnectionError(Exception e, String message) {
        long now = System.currentTimeMillis();
        
        // 特别处理"Unexpected end of stream"错误
        boolean isStreamError = e.getMessage() != null && e.getMessage().contains("Unexpected end of stream");
        
        // 如果距离上次错误超过30秒，重置计数
        if (now - lastErrorTime > 30000) {
            errorCounter.set(0);
        }
        
        // 更新错误时间和计数
        lastErrorTime = now;
        int count = errorCounter.incrementAndGet();
        
        // 根据错误类型和次数记录不同级别的日志
        if (count <= 3) {
            LogUtil.warn(message + ": " + e.getMessage() + " (第" + count + "次)" + 
                    (isStreamError ? " - 可能是Redis连接超时导致" : ""));
        } else if (count % 10 == 0) {
            // 每10次记录一次详细错误
            LogUtil.error(message + ": " + e.getMessage() + " (连续" + count + "次)" + 
                     (isStreamError ? " - 建议检查Redis连接池配置" : ""), e);
        }
        
        // 如果是连接流错误且错误次数较多，尝试重连
        if (isStreamError && count > 3 && count % 3 == 0) {
            attemptReconnection();
        }
    }
    
    /**
     * 重置错误统计
     */
    private void resetErrorStats() {
        if (errorCounter.get() > 0) {
            errorCounter.set(0);
            lastErrorTime = 0;
        }
    }
    
    /**
     * 尝试重新建立Redis连接
     */
    private void attemptReconnection() {
        try {
            LogUtil.info("尝试刷新WebSocket Redis连接...");
            
            if (webSocketRedisContainer != null) {
                // 重新添加监听器
                webSocketRedisContainer.removeMessageListener(this);
                
                // 等待短暂时间
                Thread.sleep(500);
                
                // 重新添加监听
                MessageListenerAdapter adapter = new MessageListenerAdapter(this);
                webSocketRedisContainer.addMessageListener(adapter, new ChannelTopic(WebSocketRedisConfig.WS_BROADCAST_CHANNEL));
                webSocketRedisContainer.addMessageListener(adapter, new ChannelTopic(WebSocketRedisConfig.WS_NODE_CHANNEL_PREFIX + nodeId));
                
                LogUtil.info("WebSocket Redis连接已刷新");
            } else {
                LogUtil.warn("无法获取WebSocketRedisContainer，无法刷新连接");
            }
        } catch (Exception ex) {
            LogUtil.error("尝试刷新WebSocket Redis连接失败: " + ex.getMessage(), ex);
        }
    }
} 