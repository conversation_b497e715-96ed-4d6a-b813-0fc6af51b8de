package com.wb.cache;

import java.nio.charset.Charset;

import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wb.util.JsonUtil;

/**
 * @description 自定义GenericFastJson2JsonRedisSerializer序列化器，其中用到了FastJsonWraper包装类
 */
public class GenericFastJson2JsonRedisSerializer<T> implements RedisSerializer<T> {

	public static final Charset DEFAULT_CHARSET = Charset.forName("UTF-8");

	/**
	 * 添加autotype白名单 解决redis反序列化对象时报错 ：com.alibaba.fastjson.JSONException: autoType
	 * is not support
	 */
	static {
		ParserConfig.getGlobalInstance().setAutoTypeSupport(true);
		ParserConfig.getGlobalInstance().addAccept("org.json.JSONObject");
	}

	public GenericFastJson2JsonRedisSerializer() {
		super();
	}

	public GenericFastJson2JsonRedisSerializer(Object value) {
		super();
	}

	@Override
	@SuppressWarnings("unchecked")
	public byte[] serialize(T t) {
		if (t == null) {
			return new byte[0];
		}
		FastJsonWraper<T> wraperSet = new FastJsonWraper<>(t);
		if (t instanceof JSONObject || t instanceof JSONArray) {
			Object obj = JSON.parse(t.toString());
			wraperSet.setValue((T) obj);
			wraperSet.setJson(true);
		}
		return JSON.toJSONString(wraperSet, SerializerFeature.WriteClassName).getBytes(DEFAULT_CHARSET);
	}

	/**
	 * 当deserializeStr不符合Fastjson格式时
	 * 首先尝试使用ObjectMapper进行解码
	 * 如果抛出JsonParseException异常
	 * 则尝试使用JdkSerializationRedisSerializer进行反序列化
	 * 这样可以确保对JdkSerializationRedisSerializer的解码支持
	 */
	@Override
	@SuppressWarnings("unchecked")
	public T deserialize(byte[] bytes) {
		if (bytes == null || bytes.length <= 0) {
			return null;
		}
		String deserializeStr = new String(bytes, DEFAULT_CHARSET);
		ObjectMapper objectMapper = new ObjectMapper();
		JdkSerializationRedisSerializer jdkSerializer = new JdkSerializationRedisSerializer();
		try {
			if (deserializeStr.startsWith("{\"@type")) {
				FastJsonWraper<T> wraperGet = JSON.parseObject(deserializeStr, FastJsonWraper.class);
				if (wraperGet.isJson()) {
					if (wraperGet.getValue() instanceof com.alibaba.fastjson.JSONObject) {
						return (T) JsonUtil.getObject(wraperGet.getValue().toString());
					}
					if (wraperGet.getValue() instanceof com.alibaba.fastjson.JSONArray) {
						return (T) JsonUtil.getArray(wraperGet.getValue().toString());
					}
				}
				return wraperGet.getValue();
			} else {
				try {
					JavaType javaType = objectMapper.getTypeFactory().constructType(this.getClass());
					return (T) objectMapper.readValue(deserializeStr, javaType);
				} catch (JsonParseException e) {
					// Fallback to JdkSerializationRedisSerializer
					return (T) jdkSerializer.deserialize(bytes);
				}
			}
		} catch (Exception ex) {
			throw new SerializationException("Could not deserialize: " + ex.getMessage(), ex);
		}
	}

}
