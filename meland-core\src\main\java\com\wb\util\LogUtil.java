package com.wb.util;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wb.aliyun.sls.SLSLogUtil;
import com.wb.common.Var;
import com.wb.tool.Console;

/**
 * 使用阿里云SLS的日志工具方法类。
 */
public class LogUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogUtil.class);

    /**
     * 提示信息
     */
    public static final int INFO = 1;
    /**
     * 警告信息
     */
    public static final int WARN = 2;
    /**
     * 错误信息
     */
    public static final int ERROR = 3;
    /**
     * 调试信息
     */
    public static final int DEBUG = 0;

    /**
     * 把日志信息记入数据库日志表中。
     *
     * @param userName 用户名称。
     * @param ip       ip地址。
     * @param type     日志类别，1信息，2警告，3错误。
     * @param object   要记录日志的对象。
     */
    private static void record(String userName, String ip, int type, Object object) {
        long milliSec = System.currentTimeMillis();
        int len;
        Connection conn = null;
        PreparedStatement st = null;
        String text;

        try {
            conn = DbUtil.getConnection();
            st = conn.prepareStatement("insert into WB_LOG values(?,?,?,?,?)");
            if (StringUtil.isEmpty(ip))
                ip = "-";
            if (StringUtil.isEmpty(userName))
                userName = "-";
            if (object == null)
                text = "-";
            else {
                text = object.toString();
                if (StringUtil.isEmpty(text))
                    text = "-";
            }
            st.setTimestamp(1, new Timestamp(milliSec));
            st.setString(2, userName);
            st.setString(3, ip);
            st.setInt(4, type);
            // 确保最多只有255个字节被写入数据库
            len = Math.min(text.length(), 256);
            while (text.getBytes().length > 255) {
                len--;
                text = text.substring(0, len);
            }
            st.setString(5, text);
            st.executeUpdate();
        } catch (Throwable ignored) {
        } finally {
            DbUtil.close(st);
            DbUtil.close(conn);
        }
    }

    /**
     * 把日志信息记入SLS日志中。
     *
     * @param userName 用户名称。
     * @param ip       ip地址。
     * @param type     日志类别，1信息，2警告，3错误。
     * @param object   要记录日志的对象。
     */
    private static void sendLog(String userName, String ip, int type, Object object) {
        if (Var.getBool("sys.config.sls.useSLS")) {
            JSONObject json = new JSONObject();
            json.put("log_date", DateUtil.format(new Date()));
            json.put("user_name", StringUtil.isEmpty(userName) ? "-" : userName);
            json.put("ip", StringUtil.format("({0}){1}", SysUtil.getServerId(), StringUtil.isEmpty(ip) ? "-" : ip));
            json.put("log_type", type);
            json.put("msg", object == null ? "-" : (StringUtil.isEmpty(object.toString()) ? "-" : object.toString()));
            SLSLogUtil.sendLogAsync(json, Var.getString("sys.config.sls.sysLog"));
        } else {
            record(userName, ip, type, object);
        }
    }

    /**
     * 把指定类型的日志信息记入数据库日志表中。
     *
     * @param type 日志类别，1信息，2警告，3错误。
     * @param msg  日志信息。
     */
    private static void recordMsg(int type, Object msg) {
        if (Var.log) {
            sendLog(null, null, type, msg);
        }
    }

    /**
     * 把当前用户指定类型的日志信息记入数据库日志表中。
     *
     * @param request 请求对象，该对象包含有当前用户和IP信息。
     * @param type    日志类别，0日志，1信息，2警告，3错误。
     * @param msg     日志信息。
     */
    private static void recordUserMsg(HttpServletRequest request, int type, Object msg) {
        if (Var.log) {
            sendLog(WebUtil.fetch(request, "sys.username"), WebUtil.getRemoteAddr(request), type, msg);
        }
    }

    /**
     * 把指定用户，IP，类型的日志信息记入数据库日志表中。
     *
     * @param userName 用户名称。
     * @param ip       ip地址。
     * @param type     日志类别，1信息，2警告，3错误。
     * @param msg      日志信息。
     */
    public static void log(String userName, String ip, int type, Object msg) {
        if (Var.log) {
            sendLog(userName, ip, type, msg);
        }
    }

    /**
     * 把当前用户信息类日志信息记入数据库日志表中。
     *
     * @param request 请求对象，该对象包含有当前用户和IP信息。
     * @param msg     日志信息。
     */
    public static void info(HttpServletRequest request, Object msg) {
        recordUserMsg(request, INFO, msg);
    }

    /**
     * 把信息类日志信息记入数据库日志表中。
     *
     * @param msg 日志信息。
     */
    public static void info(Object msg) {
        recordMsg(INFO, msg);
    }

    /**
     * 把信息类日志信息记入数据库日志表中，支持占位符。
     *
     * @param format 带占位符的日志信息格式。
     * @param args   占位符参数列表。
     */
    public static void info(String format, Object... args) {
        recordMsg(INFO, StringUtil.format(format, args));
    }

    /**
     * 把当前用户信息类日志信息记入数据库日志表中，支持占位符。
     *
     * @param request 请求对象，该对象包含有当前用户和IP信息。
     * @param format  带占位符的日志信息格式。
     * @param args    占位符参数列表。
     */
    public static void info(HttpServletRequest request, String format, Object... args) {
        recordUserMsg(request, INFO, StringUtil.format(format, args));
    }

    /**
     * 把警告类日志信息记入数据库日志表中，支持占位符。
     *
     * @param format 带占位符的日志信息格式。
     * @param args   占位符参数列表。
     */
    public static void warn(String format, Object... args) {
        recordMsg(WARN, StringUtil.format(format, args));
    }

    /**
     * 把当前用户警告类日志信息记入数据库日志表中，支持占位符。
     *
     * @param request 请求对象，该对象包含有当前用户和IP信息。
     * @param format  带占位符的日志信息格式。
     * @param args    占位符参数列表。
     */
    public static void warn(HttpServletRequest request, String format, Object... args) {
        recordUserMsg(request, WARN, StringUtil.format(format, args));
    }

    /**
     * 把当前用户警告类日志信息记入数据库日志表中。
     *
     * @param request 请求对象，该对象包含有当前用户和IP信息。
     * @param s       日志信息。
     */
    public static void warn(HttpServletRequest request, Object s) {
        recordUserMsg(request, WARN, s);
    }

    /**
     * 把警告类日志信息记入数据库日志表中。
     *
     * @param s 日志信息。
     */
    public static void warn(Object s) {
        recordMsg(WARN, s);
    }

    /**
     * 把当前用户错误类日志信息记入数据库日志表中。
     *
     * @param request 请求对象，该对象包含有当前用户和IP信息。
     * @param s       日志信息。
     */
    public static void error(HttpServletRequest request, Object s) {
        recordUserMsg(request, ERROR, s);
    }

    /**
     * 把错误类日志信息记入数据库日志表中。
     *
     * @param s 日志信息。
     */
    public static void error(Object s) {
        recordMsg(ERROR, s);
    }

    /**
     * 把错误类日志信息和异常堆栈记入数据库日志表中。
     *
     * @param s 日志信息。
     * @param e 异常对象。
     */
    public static void error(String s, Throwable e) {
        recordMsg(ERROR, s);
        // 使用Log4j记录完整的异常堆栈
        if (LOGGER.isErrorEnabled()) {
            LOGGER.error(s.toString(), e);
        }
    }

    /**
     * 把当前用户错误类日志信息和异常堆栈记入数据库日志表中，支持占位符。
     *
     * @param s    日志信息。
     * @param e    异常对象。
     * @param args 占位符参数列表。
     */
    public static void error(String s, Throwable e, Object... args) {
        String message = StringUtil.format(s, args);
        recordMsg(ERROR, message);
        // 使用Log4j记录完整的异常堆栈
        if (LOGGER.isErrorEnabled()) {
            LOGGER.error(message, e);
        }
    }

    /**
     * 把当前用户错误类日志信息和异常堆栈记入数据库日志表中，支持占位符。 /** 把当前用户错误类日志信息和异常堆栈记入数据库日志表中。
     *
     * @param request 请求对象，该对象包含有当前用户和IP信息。
     * @param s       日志信息。
     * @param e       异常对象。
     */
    public static void error(HttpServletRequest request, String s, Throwable e) {
        recordUserMsg(request, ERROR, s);
        // 使用Log4j记录完整的异常堆栈
        if (LOGGER.isErrorEnabled()) {
            LOGGER.error(s.toString(), e);
        }
    }

    /**
     * 把当前用户错误类日志信息和异常堆栈记入数据库日志表中，支持占位符。
     *
     * @param request 请求对象，该对象包含有当前用户和IP信息。
     * @param s       日志信息。
     * @param e       异常对象。
     * @param args    占位符参数列表。
     */
    public static void error(HttpServletRequest request, String s, Throwable e, Object... args) {
        String message = StringUtil.format(s, args);
        recordUserMsg(request, ERROR, message);
        // 使用Log4j记录完整的异常堆栈
        if (LOGGER.isErrorEnabled()) {
            LOGGER.error(message, e);
        }
    }

    /**
     * 把调试信息记录到日志文件并通过浏览器控制台输出。
     *
     * @param object 调试信息对象。
     */
    public static void debug(Object object) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(object == null ? "null" : object.toString());
        }
    }

    /**
     * 把调试信息记录到日志文件并通过浏览器控制台输出。
     *
     * @param request 请求对象，该对象用于将调试信息传递到客户端。
     * @param object  调试信息对象。
     */
    public static void debug(HttpServletRequest request, Object object) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(object == null ? "null" : object.toString());
            if (request != null) {
                try {
                    Console.print(request, object, "log", false);
                } catch (Exception ignored) {
                    // 忽略输出到控制台时的异常
                }
            }
        }
    }

    /**
     * 把调试信息记录到日志文件并通过浏览器控制台输出，支持占位符。
     *
     * @param format 带占位符的调试信息格式。
     * @param args   占位符参数列表。
     */
    public static void debug(String format, Object... args) {
        String message = StringUtil.format(format, args);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(message);
        }
    }

    /**
     * 把调试信息记录到日志文件并通过浏览器控制台输出，支持占位符。
     *
     * @param request 请求对象，该对象用于将调试信息传递到客户端。
     * @param format  带占位符的调试信息格式。
     * @param args    占位符参数列表。
     */
    public static void debug(HttpServletRequest request, String format, Object... args) {
        String message = StringUtil.format(format, args);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(message);
            if (request != null) {
                try {
                    Console.print(request, message, "log", false);
                } catch (Exception ignored) {
                    // 忽略输出到控制台时的异常
                }
            }
        }
    }
}
