package com.wb.rocketmq;

import com.wb.common.Var;
import com.wb.rocketmq.config.MqConfig;
import com.wb.rocketmq.util.MessageType;
import com.wb.rocketmq.util.MetricsCollector;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;
import org.apache.rocketmq.client.apis.*;
import org.apache.rocketmq.client.apis.message.Message;
import org.apache.rocketmq.client.apis.producer.Producer;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.json.JSONArray;
import org.json.JSONObject;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.ArrayList;

/**
 * RocketMQ生产者管理类 单例模式实现，统一管理生产者实例
 */
public class RocketMQProducerManager {
    private static volatile RocketMQProducerManager instance;
    private Producer producer;
    private ClientServiceProvider provider;
    private MqConfig config;
    private MetricsCollector metricsCollector;
    // 添加线程池用于处理异步发送消息任务
    private ExecutorService asyncSendExecutor;
    // 批量消息的最大大小（字节）
    private static final int MAX_BATCH_SIZE_BYTES = 4 * 1024 * 1024; // 4MB
    // 批量消息最大条数
    private static final int MAX_BATCH_COUNT = 50;

    /**
     * 私有构造函数，防止外部实例化
     */
    private RocketMQProducerManager() {
        try {
            // 确保RocketMQ配置初始化
            // RocketMQConfiguration.init(); // 在Web应用中不需要设置守护线程

            this.config = new MqConfig();
            this.provider = ClientServiceProvider.loadService();
            this.metricsCollector = new MetricsCollector();

            // 初始化异步发送线程池
            this.asyncSendExecutor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2,
                    new ThreadFactory() {
                        private final AtomicInteger threadIndex = new AtomicInteger(0);

                        @Override
                        public Thread newThread(Runnable r) {
                            Thread thread = new Thread(r,
                                    "AsyncMessageSenderThread_" + this.threadIndex.incrementAndGet());
                            thread.setDaemon(true);
                            return thread;
                        }
                    });

            // 初始化生产者
            initProducer();

            // 注册关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown));

            LogUtil.info("RocketMQ生产者管理器初始化成功");
        } catch (Exception e) {
            LogUtil.error(StringUtil.format("RocketMQ生产者管理器初始化失败：{0}", e.getMessage()), e);
        }
    }

    /**
     * 初始化生产者
     */
    private void initProducer() throws ClientException {
        // 收集所有可能的主题
        Set<String> topics = new HashSet<>();
        if (!StringUtil.isEmpty(config.getSyncTopic()))
            topics.add(config.getSyncTopic());
        if (!StringUtil.isEmpty(config.getAsyncTopic()))
            topics.add(config.getAsyncTopic());
        if (!StringUtil.isEmpty(config.getSequentialTopic()))
            topics.add(config.getSequentialTopic());
        if (!StringUtil.isEmpty(config.getTimeDelayTopic()))
            topics.add(config.getTimeDelayTopic());

        // 创建客户端配置
        ClientConfigurationBuilder builder = ClientConfiguration.newBuilder().setEndpoints(config.getServerAddress());

        if (config.getIsPublic()) {
            builder.setCredentialProvider(
                    new StaticSessionCredentialsProvider(config.getAccessKey(), config.getSecretKey()));
        }

        ClientConfiguration clientConfig = builder.build();

        // 创建生产者
        producer = provider.newProducerBuilder().setTopics(topics.toArray(new String[0]))
                .setClientConfiguration(clientConfig).build();

        LogUtil.info(StringUtil.format("RocketMQ生产者初始化成功，主题：{0}", String.join(",", topics)));
    }

    /**
     * 使用双重检查锁定单例模式获取实例
     *
     * @return 实例对象
     */
    public static RocketMQProducerManager getInstance() {
        if (instance == null) {
            synchronized (RocketMQProducerManager.class) {
                if (instance == null) {
                    instance = new RocketMQProducerManager();
                }
            }
        }
        return instance;
    }

    /**
     * 关闭资源
     */
    public void shutdown() {
        try {
            // 关闭异步发送线程池
            if (asyncSendExecutor != null && !asyncSendExecutor.isShutdown()) {
                asyncSendExecutor.shutdown();
                try {
                    if (!asyncSendExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                        asyncSendExecutor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    asyncSendExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
                LogUtil.info("RocketMQ异步发送线程池已关闭");
            }

            // 关闭生产者
            if (producer != null) {
                producer.close();
                LogUtil.info("RocketMQ生产者已关闭");
            }
        } catch (Exception e) {
            LogUtil.error(StringUtil.format("关闭RocketMQ资源失败：{0}", e.getMessage()), e);
        }
    }

    /**
     * 构建消息对象
     *
     * @param topic   主题
     * @param tag     标签
     * @param message 消息内容
     * @return 消息对象
     */
    private Message buildMessage(String topic, String tag, JSONObject message) {
        return provider.newMessageBuilder().setTopic(topic).setTag(tag).setKeys(SysUtil.getId())
                .setBody(message.toString().getBytes(StandardCharsets.UTF_8)).build();
    }

    /**
     * 构建定时消息对象
     *
     * @param topic             主题
     * @param tag               标签
     * @param message           消息内容
     * @param deliveryTimestamp 定时时间戳
     * @return 消息对象
     */
    private Message buildTimedMessage(String topic, String tag, JSONObject message, long deliveryTimestamp) {
        return provider.newMessageBuilder().setTopic(topic).setTag(tag).setKeys(SysUtil.getId())
                .setBody(message.toString().getBytes(StandardCharsets.UTF_8)).setDeliveryTimestamp(deliveryTimestamp)
                .build();
    }

    /**
     * 构建顺序消息对象
     *
     * @param topic   主题
     * @param tag     标签
     * @param message 消息内容
     * @return 消息对象
     */
    private Message buildSequentialMessage(String topic, String tag, JSONObject message) {
        // 从消息中获取或生成消息ID作为消息键，确保相同键的消息能够发送到相同的队列
        String messageId = message.has("id") ? message.getString("id") : SysUtil.getId();

        return provider.newMessageBuilder().setTopic(topic).setTag(tag).setKeys(messageId) // 使用消息ID作为键，确保相同ID的消息按顺序发送
                .setMessageGroup(messageId) // 设置消息组，确保同一组的消息按顺序处理
                .setBody(message.toString().getBytes(StandardCharsets.UTF_8)).build();
    }

    /**
     * 发送同步消息
     *
     * @param topic   主题，为空时使用配置中的主题
     * @param tag     标签
     * @param message 消息内容
     * @return 发送结果
     */
    public String sendSyncMessage(String topic, String tag, JSONObject message) {
        // 如果主题为空，使用配置中的主题
        String actualTopic = StringUtil.isEmpty(topic) ? config.getSyncTopic() : topic;

        try {
            // 开始计时
            long startTime = System.currentTimeMillis();

            // 构建消息
            Message msg = buildMessage(actualTopic, tag, message);

            // 同步发送
            SendReceipt receipt = producer.send(msg);

            // 记录指标
            long endTime = System.currentTimeMillis();
            metricsCollector.recordMessageSent(actualTopic, tag);
            metricsCollector.recordProcessingTime(actualTopic, tag, endTime - startTime);

            // 记录日志
            if (Var.getBool("sys.config.aliyun.logDebug")) {
                LogUtil.info(StringUtil.format("同步消息发送成功, messageId={0}", receipt.getMessageId()));
            }

            return receipt.getMessageId().toString();
        } catch (Exception e) {
            metricsCollector.recordSendFailed(actualTopic, tag);
            LogUtil.error(StringUtil.format("同步消息[{0}]发送异常：{1}", message.toString(), e.getMessage()), e);
            return "";
        }
    }

    /**
     * 发送异步消息
     *
     * @param topic   主题，为空时使用配置中的主题
     * @param tag     标签
     * @param message 消息内容
     */
    public void sendAsyncMessage(String topic, String tag, JSONObject message) {
        // 如果主题为空，使用配置中的主题
        String actualTopic = StringUtil.isEmpty(topic) ? config.getAsyncTopic() : topic;

        try {
            // 构建消息
            Message msg = buildMessage(actualTopic, tag, message);
            
            // 开始计时
            long startTime = System.currentTimeMillis();

            // 直接使用RocketMQ客户端的异步发送功能，避免线程池嵌套
            CompletableFuture<SendReceipt> future = producer.sendAsync(msg);
            future.whenComplete((receipt, throwable) -> {
                if (throwable == null) {
                    // 成功
                    long endTime = System.currentTimeMillis();
                    metricsCollector.recordMessageSent(actualTopic, tag);
                    metricsCollector.recordProcessingTime(actualTopic, tag, endTime - startTime);

                    if (Var.getBool("sys.config.aliyun.logDebug")) {
                        LogUtil.info(StringUtil.format("异步消息发送成功, messageId={0}", receipt.getMessageId()));
                    }
                } else {
                    // 失败处理
                    metricsCollector.recordSendFailed(actualTopic, tag);
                    LogUtil.error(StringUtil.format("异步消息发送失败: {0}", throwable.getMessage()), throwable);
                }
            });
        } catch (Exception e) {
            metricsCollector.recordSendFailed(actualTopic, tag);
            LogUtil.error(StringUtil.format("异步消息[{0}]提交失败: {1}", message.toString(), e.getMessage()), e);
        }
    }

    /**
     * 批量发送异步消息
     *
     * @param topic    主题，为空时使用配置中的主题
     * @param tag      标签
     * @param messages 消息内容数组
     */
    public void sendAsyncBatchMessage(String topic, String tag, JSONArray messages) {
        // 如果主题为空，使用配置中的主题
        String actualTopic = StringUtil.isEmpty(topic) ? config.getAsyncTopic() : topic;

        try {
            // 分批发送，确保每个批次不超过最大大小限制
            int totalMessages = messages.length();

            // 创建消息列表
            List<List<Message>> messageBatches = splitMessageBatches(actualTopic, tag, messages);

            // 用于跟踪所有批次的成功/失败状态
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failCount = new AtomicInteger(0);
            
            // 存储所有批次的CompletableFuture
            List<CompletableFuture<Void>> batchFutures = new ArrayList<>();

            // 对每个批次使用线程池异步发送
            for (List<Message> batch : messageBatches) {
                CompletableFuture<Void> batchFuture = CompletableFuture.runAsync(() -> {
                    // 发送这一批次的消息
                    long batchStartTime = System.currentTimeMillis();

                    try {
                        // 如果批次只有一条消息，直接发送
                        if (batch.size() == 1) {
                            CompletableFuture<SendReceipt> future = producer.sendAsync(batch.get(0));
                            future.whenComplete((receipt, throwable) -> processAsyncSendResult(actualTopic, tag,
                                    throwable, batchStartTime, successCount, failCount));
                        } else {
                            // 批量发送多条消息
                            // RocketMQ 5.x不支持直接发送List<Message>，需要单独处理每条消息
                            // 收集批次内所有消息的CompletableFuture
                            List<CompletableFuture<SendReceipt>> messageFutures = new ArrayList<>(batch.size());
                            
                            for (Message batchMsg : batch) {
                                messageFutures.add(producer.sendAsync(batchMsg));
                            }
                            
                            // 使用CompletableFuture.allOf等待批次内所有消息完成
                            CompletableFuture<Void> allMessagesFuture = CompletableFuture.allOf(
                                    messageFutures.toArray(new CompletableFuture[0])
                            );
                            
                            // 处理批次内所有消息的结果
                            allMessagesFuture.whenComplete((v, batchThrowable) -> {
                                int batchSuccessCount = 0;
                                int batchFailCount = 0;
                                
                                // 统计成功和失败的消息数量
                                for (CompletableFuture<SendReceipt> future : messageFutures) {
                                    try {
                                        // 如果future已完成且无异常，则成功
                                        if (future.isDone() && !future.isCompletedExceptionally()) {
                                            batchSuccessCount++;
                                        } else {
                                            batchFailCount++;
                                            try {
                                                // 尝试获取异常信息
                                                future.join();
                                            } catch (Exception e) {
                                                LogUtil.error(StringUtil.format("批内单条消息发送失败: {0}", e.getMessage()), e);
                                            }
                                        }
                                    } catch (Exception e) {
                                        batchFailCount++;
                                        LogUtil.error(StringUtil.format("处理消息结果时发生异常: {0}", e.getMessage()), e);
                                    }
                                }
                                
                                // 处理整个批次的结果
                                if (batchFailCount > 0) {
                                    // 只要有一条失败，整个批次就算部分失败
                                    failCount.addAndGet(batchFailCount);
                                    successCount.addAndGet(batchSuccessCount);
                                    LogUtil.warn(StringUtil.format("批次消息部分失败: 成功={0}, 失败={1}, 总计={2}",
                                            batchSuccessCount, batchFailCount, batch.size()));
                                } else {
                                    // 整个批次成功
                                    successCount.addAndGet(batch.size());
                                    metricsCollector.recordMessageSent(actualTopic, tag);
                                    metricsCollector.recordProcessingTime(actualTopic, tag,
                                            System.currentTimeMillis() - batchStartTime);
                                    if (Var.getBool("sys.config.aliyun.logDebug")) {
                                        LogUtil.info(StringUtil.format("批次消息发送成功, 数量={0}", batch.size()));
                                    }
                                }
                            });
                            
                            // 等待批次完成，但不阻塞线程
                            try {
                                allMessagesFuture.get(30, TimeUnit.SECONDS);
                            } catch (Exception e) {
                                // 超时或其他异常，记录失败
                                failCount.addAndGet(batch.size());
                                metricsCollector.recordSendFailed(actualTopic, tag);
                                LogUtil.error(StringUtil.format("批量异步消息发送失败: {0}", e.getMessage()), e);
                            }
                        }
                    } catch (Exception e) {
                        metricsCollector.recordSendFailed(actualTopic, tag);
                        failCount.addAndGet(batch.size());
                        LogUtil.error(StringUtil.format("批量异步消息发送失败: {0}", e.getMessage()), e);
                    }
                }, asyncSendExecutor);
                
                batchFutures.add(batchFuture);
            }

            // 使用CompletableFuture.allOf等待所有批次完成
            CompletableFuture<Void> allBatchesFuture = CompletableFuture.allOf(
                    batchFutures.toArray(new CompletableFuture[0])
            );
            
            // 处理所有批次完成的情况
            allBatchesFuture.whenComplete((v, throwable) -> {
                if (throwable == null) {
                    if (Var.debug) {
                        LogUtil.info(StringUtil.format("批量消息发送完成，成功: {0}, 失败: {1}, 总计: {2}", 
                                successCount.get(), failCount.get(), totalMessages));
                    }
                } else {
                    LogUtil.warn(StringUtil.format("批量消息发送出现异常，成功: {0}, 失败: {1}, 总计: {2}, 错误: {3}", 
                            successCount.get(), failCount.get(), totalMessages, throwable.getMessage()));
                }
            });
            
            // 添加超时处理，确保在一定时间内完成（不阻塞当前线程）
            CompletableFuture.runAsync(() -> {
                try {
                    allBatchesFuture.get(60, TimeUnit.SECONDS);
                } catch (Exception e) {
                    LogUtil.warn(StringUtil.format("批量消息发送操作超时，已处理: {0}/{1}", 
                            successCount.get() + failCount.get(), totalMessages));
                }
            }, asyncSendExecutor);
        } catch (Exception e) {
            metricsCollector.recordSendFailed(actualTopic, tag);
            LogUtil.error(StringUtil.format("批量异步消息提交失败: {0}", e.getMessage()), e);
        }
    }

    /**
     * 将消息拆分为多个批次，确保每批不超过大小限制
     *
     * @param topic    主题
     * @param tag      标签
     * @param messages 要发送的消息数组
     * @return 分批后的消息列表
     */
    private List<List<Message>> splitMessageBatches(String topic, String tag, JSONArray messages) {
        List<List<Message>> batches = new ArrayList<>();
        List<Message> currentBatch = new ArrayList<>();
        int currentBatchSize = 0;

        for (int i = 0; i < messages.length(); i++) {
            JSONObject msgObj = messages.getJSONObject(i);
            Message msg = buildMessage(topic, tag, msgObj);

            // 估算消息大小（消息体 + 消息头 + 其他开销）
            // 获取消息体的字节数组长度
            byte[] bodyBytes = msgObj.toString().getBytes(StandardCharsets.UTF_8);
            int messageSize = bodyBytes.length + 512; // 512字节作为消息头和其他开销的估算值

            // 如果添加此消息会超过批次大小限制，或者达到最大数量限制，则创建新批次
            if (currentBatch.size() >= MAX_BATCH_COUNT
                    || (currentBatchSize > 0 && currentBatchSize + messageSize > MAX_BATCH_SIZE_BYTES)) {
                batches.add(new ArrayList<>(currentBatch));
                currentBatch.clear();
                currentBatchSize = 0;
            }

            // 添加到当前批次
            currentBatch.add(msg);
            currentBatchSize += messageSize;
        }

        // 添加最后一个批次
        if (!currentBatch.isEmpty()) {
            batches.add(currentBatch);
        }

        return batches;
    }

    /**
     * 处理异步发送结果
     * 
     * @param topic        主题
     * @param tag          标签
     * @param throwable    异常（如果有）
     * @param startTime    发送开始时间
     * @param successCount 成功计数器
     * @param failCount    失败计数器
     */
    private void processAsyncSendResult(String topic, String tag, Throwable throwable, long startTime,
            AtomicInteger successCount, AtomicInteger failCount) {
        if (throwable == null) {
            // 成功
            long endTime = System.currentTimeMillis();
            metricsCollector.recordMessageSent(topic, tag);
            metricsCollector.recordProcessingTime(topic, tag, endTime - startTime);
            successCount.incrementAndGet();

            if (Var.getBool("sys.config.aliyun.logDebug")) {
                LogUtil.info("批量异步消息发送成功");
            }
        } else {
            // 失败处理
            metricsCollector.recordSendFailed(topic, tag);
            failCount.incrementAndGet();
            LogUtil.error(StringUtil.format("批量异步消息发送失败: {0}", throwable.getMessage()), throwable);
        }
    }

    /**
     * 发送顺序消息
     *
     * @param topic   主题，为空时使用配置中的主题
     * @param tag     标签
     * @param message 消息内容
     * @return 发送结果
     */
    public String sendSequentialMessage(String topic, String tag, JSONObject message) {
        // 如果主题为空，使用配置中的主题
        String actualTopic = StringUtil.isEmpty(topic) ? config.getSequentialTopic() : topic;

        try {
            // 开始计时
            long startTime = System.currentTimeMillis();

            // 构建消息 - 使用顺序消息专用的构建方法
            Message msg = buildSequentialMessage(actualTopic, tag, message);

            // 发送消息
            SendReceipt receipt = producer.send(msg);

            // 记录指标
            long endTime = System.currentTimeMillis();
            metricsCollector.recordMessageSent(actualTopic, tag);
            metricsCollector.recordProcessingTime(actualTopic, tag, endTime - startTime);

            // 记录日志
            if (Var.getBool("sys.config.aliyun.logDebug")) {
                LogUtil.info(StringUtil.format("顺序消息发送成功, messageId={0}", receipt.getMessageId()));
            }

            return receipt.getMessageId().toString();
        } catch (Exception e) {
            metricsCollector.recordSendFailed(actualTopic, tag);
            LogUtil.error(StringUtil.format("顺序消息[{0}]发送异常：{1}", message.toString(), e.getMessage()), e);
            return "";
        }
    }

    /**
     * 发送定时/延迟消息
     *
     * @param topic             主题，为空时使用配置中的主题
     * @param tag               标签
     * @param message           消息内容
     * @param deliveryTimestamp 定时时间戳
     * @return 发送结果
     */
    public String sendTimeDelayMessage(String topic, String tag, JSONObject message, long deliveryTimestamp) {
        // 如果主题为空，使用配置中的主题
        String actualTopic = StringUtil.isEmpty(topic) ? config.getTimeDelayTopic() : topic;

        try {
            // 开始计时
            long startTime = System.currentTimeMillis();

            // 构建消息
            Message msg = buildTimedMessage(actualTopic, tag, message, deliveryTimestamp);

            // 发送消息
            SendReceipt receipt = producer.send(msg);

            // 记录指标
            long endTime = System.currentTimeMillis();
            metricsCollector.recordMessageSent(actualTopic, tag);
            metricsCollector.recordProcessingTime(actualTopic, tag, endTime - startTime);

            // 记录日志
            if (Var.getBool("sys.config.aliyun.logDebug")) {
                LogUtil.info(StringUtil.format("定时/延迟消息发送成功, messageId={0}, 定时时间={1}", receipt.getMessageId(),
                        new Date(deliveryTimestamp)));
            }

            return receipt.getMessageId().toString();
        } catch (Exception e) {
            metricsCollector.recordSendFailed(actualTopic, tag);
            LogUtil.error(StringUtil.format("定时/延迟消息[{0}]发送异常：{1}", message.toString(), e.getMessage()), e);
            return "";
        }
    }

    /**
     * 统一发送消息的方法
     *
     * @param topic   主题，为空时使用配置中的主题
     * @param tag     标签
     * @param message 消息内容
     * @param type    消息类型
     * @return 发送结果，异步消息返回空字符串
     */
    public String sendMessage(String topic, String tag, JSONObject message, MessageType type) {
        return sendMessage(topic, tag, message, type, 0);
    }

    /**
     * 统一发送消息的方法，支持定时消息
     *
     * @param topic             主题，为空时使用配置中的主题
     * @param tag               标签
     * @param message           消息内容
     * @param type              消息类型
     * @param deliveryTimestamp 定时时间戳，仅定时/延迟消息有效
     * @return 发送结果，异步消息返回空字符串
     */
    public String sendMessage(String topic, String tag, JSONObject message, MessageType type, long deliveryTimestamp) {
        switch (type) {
        case SYNC:
            return sendSyncMessage(topic, tag, message);
        case ASYNC:
            sendAsyncMessage(topic, tag, message);
            return "";
        case SEQUENTIAL:
            return sendSequentialMessage(topic, tag, message);
        case TIME_DELAY:
            return sendTimeDelayMessage(topic, tag, message, deliveryTimestamp);
        default:
            LogUtil.error("未知的消息类型: " + type);
            return "";
        }
    }

    /**
     * 批量发送异步消息的统一方法
     *
     * @param topic    主题，为空时使用配置中的主题
     * @param tag      标签
     * @param messages 消息内容数组
     */
    public void sendBatchMessage(String topic, String tag, JSONArray messages) {
        sendAsyncBatchMessage(topic, tag, messages);
    }
}