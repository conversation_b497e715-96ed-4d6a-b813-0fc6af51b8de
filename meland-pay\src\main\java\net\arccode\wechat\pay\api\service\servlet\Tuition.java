package net.arccode.wechat.pay.api.service.servlet;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Servlet implementation class Tuition
 */
public class Tuition extends HttpServlet {
	private static final long serialVersionUID = 1L;

	/**
	 * @see HttpServlet#HttpServlet()
	 */
	public Tuition() {
		super();
		// TODO Auto-generated constructor stub
	}

	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		doPost(request, response);
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		String stu_id = request.getParameter("stu_id");
		String class_id = request.getParameter("class_id");
		String s_charge_type = request.getParameter("s_charge_type");
		String type = request.getParameter("type");
		String code = request.getParameter("code");
		//存储在request里面
		request.setAttribute("stu_id", stu_id);
		request.setAttribute("class_id", class_id);
		request.setAttribute("s_charge_type", s_charge_type);
		request.setAttribute("code", code);
		request.setAttribute("type", type);
		//获取浏览设备
		// 获取 USER-AGENT
		String appStr = request.getHeader("user-agent");
		request.setAttribute("appStr", appStr);
		
		// 不知类型
		if(type == null && appStr.indexOf("AlipayClient") != -1) {
			response.sendRedirect("pay");
		}else if(type == null && appStr.indexOf("MicroMessenger") != -1) {
			response.sendRedirect("pay");
		// 微信
		}else if (type != null && type.equals("wechat")) {
			request.getRequestDispatcher("wxPay").forward(request, response);
		// 支付宝
		} else if (type != null && type.equals("ailpay")) {
			request.getRequestDispatcher("aliPay").forward(request, response);
		// 跳转错误页面
		}else {
			response.sendRedirect("error.html");
		}
	}

}
