package com.wb.task;

import org.quartz.*;

import com.wb.common.ScriptBuffer;
import com.wb.common.Var;
import com.wb.util.DateUtil;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;

/**
 * 用于执行服务器端脚本任务的代理。 DisallowConcurrentExecution 这个注解会告诉 Quartz Scheduler
 * 对该任务禁止并发执行，即如果上一个任务实例还未执行完毕，那么下一个任务实例就需要等待
 */
@DisallowConcurrentExecution
public class ScriptProxy implements Job {
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        long start = System.currentTimeMillis();
        String jobDesc = context.getJobDetail().getDescription();
        try {
            if (Var.taskLog)
                LogUtil.info("开始任务：" + jobDesc);
            JobDataMap dataMap = context.getJobDetail().getJobDataMap();
            String serverScript = dataMap.getString("job.serverScript");
            if (!StringUtil.isEmpty(serverScript))
                ScriptBuffer.run(dataMap.getString("job.id"), serverScript, context);
            if (Var.taskLog)
                LogUtil.info(StringUtil.concat("结束任务：", jobDesc, "；时间：",
                        DateUtil.format(System.currentTimeMillis() - start)));
        } catch (Throwable e) {
            LogUtil.error(StringUtil.concat("执行任务异常：", jobDesc, "；异常信息：", SysUtil.getRootError(e), "；时间：",
                    DateUtil.format(System.currentTimeMillis() - start)), e);
            // 使用 JobExecutionException 来通知 Quartz 任务执行失败，这样 Quartz 可以进行相应的错误处理，比如重试等
            if (Var.printError)
                throw new JobExecutionException(e);
        }
    }
}