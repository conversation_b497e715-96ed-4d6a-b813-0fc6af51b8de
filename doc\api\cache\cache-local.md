# 本地缓存与缓存防护机制

本文档介绍Redis缓存系统的本地缓存功能以及缓存防护机制，包括缓存穿透、缓存击穿和缓存雪崩的防护策略。

## 功能概览

系统提供的本地缓存与防护机制包括：

- 本地缓存功能（进程内缓存）
- 缓存穿透防护
- 缓存击穿防护（互斥锁）
- 缓存雪崩防护（随机过期时间）
- 缓存统计功能

## 本地缓存功能

本地缓存是在应用程序进程内存储的缓存，访问速度比Redis更快，可以减轻Redis的负载。

### Java 用法

```java
// 获取带本地缓存防护的值
Object value = Base.map.getWithProtection("user:1001");

// 设置带防雪崩功能的缓存（会自动随机化过期时间）
Base.map.setWithAntiAvalanche("user:1001", userObject, 30, TimeUnit.MINUTES);

// 删除缓存（同时删除本地缓存）
Base.map.deleteWithLocalCache("user:1001");

// 批量删除（同时删除本地缓存）
List<String> keys = Arrays.asList("user:1001", "user:1002");
Base.map.multiDeleteWithLocalCache(keys);

// 清空本地缓存
Base.map.clearLocalCache();

// 获取本地缓存大小
int size = Base.map.getLocalCacheSize();

// 手动清理过期的本地缓存项
Base.map.cleanupLocalCache();
```

### XWL 脚本用法

```javascript
// 直接使用Base.map
// 获取带本地缓存防护的值
var value = Base.map.getWithProtection("user:1001");

// 设置带防雪崩功能的缓存
Base.map.setWithAntiAvalanche("user:1001", userObject, 30, java.util.concurrent.TimeUnit.MINUTES);

// 删除缓存（同时删除本地缓存）
Base.map.deleteWithLocalCache("user:1001");

// 批量删除（同时删除本地缓存）
var keys = ["user:1001", "user:1002"];
Base.map.multiDeleteWithLocalCache(keys);

// 清空本地缓存
Base.map.clearLocalCache();

// 获取本地缓存大小
var size = Base.map.getLocalCacheSize();
```

## 缓存加载机制

系统提供了带加载器的缓存获取方法，可以在缓存不存在时自动加载并设置缓存。

### Java 用法

```java
// 使用加载器获取缓存，如缓存不存在则从数据库加载
User user = Base.map.getWithLoader(
    "user:1001",           // 缓存键
    key -> {               // 加载函数，缓存不存在时调用
        String userId = key.substring(5); // 从key中提取userId
        return userRepository.findById(userId);
    },
    30,                    // 过期时间
    TimeUnit.MINUTES       // 时间单位
);
```

### XWL 脚本用法

```javascript
// 使用加载器获取缓存
var user = Base.map.getWithLoader(
  "user:admin",
  function(key) {
    // 从数据库加载
    var result = app.getRecord("select * from wb_user where user_id='admin'");
    return Wb.reverse(result);
  },
  30,
  java.util.concurrent.TimeUnit.MINUTES
);
```

## 缓存击穿防护（互斥锁）

当热点数据过期时，可能会有大量请求同时查询数据库，这种情况称为缓存击穿。使用互斥锁可以防止这种情况。

### Java 用法

```java
// 使用互斥锁防止缓存击穿
User user = Base.map.getWithMutex(
    "user:1001",           // 缓存键
    () -> {                // 缓存不存在时的加载函数
        String userId = "1001";
        return userRepository.findById(userId);
    },
    30,                    // 过期时间
    TimeUnit.MINUTES       // 时间单位
);
```

### XWL 脚本用法

```javascript
// 使用互斥锁防止缓存击穿
var user = Base.map.getWithMutex(
    "user:1001",
    function() {
        // 从数据库加载
        var result = app.getRecord("select * from wb_user where user_id='admin'");
        return Wb.reverse(result);
    },
    30,
    java.util.concurrent.TimeUnit.MINUTES
);
```

## 缓存统计功能

系统提供了缓存统计功能，可以查看缓存的命中率、加载次数等信息。

### Java 用法

```java
// 获取缓存统计信息
Map<String, Object> stats = Base.map.getCacheStats();
long hits = (Long) stats.get("hits");
long misses = (Long) stats.get("misses");
double hitRate = (Double) stats.get("hitRate");
long loads = (Long) stats.get("loads");

// 重置统计信息
Base.map.resetCacheStats();
```

### XWL 脚本用法

```javascript
// 获取缓存统计信息
var stats = Base.map.getCacheStats();
var hits = stats.get("hits");
var misses = stats.get("misses");
var hitRate = stats.get("hitRate");

// 重置统计信息
Base.map.resetCacheStats();
```

## 示例：完整的缓存防护策略

### Java 示例

```java
public User getUserWithFullProtection(String userId) {
    String cacheKey = "user:" + userId;
    
    // 使用互斥锁和加载器，防止缓存击穿和穿透
    return Base.map.getWithMutex(
        cacheKey,
        () -> {
            // 从数据库加载
            User user = userRepository.findById(userId);
            if (user != null) {
                // 使用随机过期时间防止缓存雪崩
                Base.map.setWithAntiAvalanche(cacheKey, user, 30, TimeUnit.MINUTES);
            }
            return user;
        },
        30,
        TimeUnit.MINUTES
    );
}
```

### XWL 脚本示例

```javascript
function getUserWithFullProtection(userId) {
    var cacheKey = "user:" + userId;
    
    // 使用互斥锁和加载器，防止缓存击穿和穿透
    return Base.map.getWithMutex(
        cacheKey,
        function() {
            // 从数据库加载
            var user =  Wb.reverse(app.getRecord("select * from wb_user where user_id='admin'"));
            if (user != null) {
                // 使用随机过期时间防止缓存雪崩
                Base.map.setWithAntiAvalanche(cacheKey, user, 30, java.util.concurrent.TimeUnit.MINUTES);
            }
            return user;
        },
        30,
        java.util.concurrent.TimeUnit.MINUTES
    );
}
```

## 缓存防护概念解释

1. **缓存穿透**：指查询一个不存在的数据，导致请求直接打到数据库上
   - 解决方案：使用布隆过滤器或者缓存空值

2. **缓存击穿**：指一个热点key过期的瞬间，大量请求打到数据库
   - 解决方案：使用互斥锁（本文档中的`getWithMutex`方法）

3. **缓存雪崩**：指大量缓存同时过期，导致请求全部打到数据库
   - 解决方案：随机化过期时间（本文档中的`setWithAntiAvalanche`方法）

## 注意事项

1. 本地缓存默认最大容量为1000项，如需调整请联系系统管理员
2. 本地缓存默认过期时间为60秒，主要用于缓解热点数据的访问压力
3. 互斥锁的默认等待时间为3秒，可能会增加请求延迟
4. 统计功能会略微影响性能，但影响很小，可以放心使用
5. 本地缓存在应用重启后会清空，适合存放访问频率高且变化不频繁的数据 