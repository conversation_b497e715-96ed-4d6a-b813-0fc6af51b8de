(()=>{var t={95:(t,e,n)=>{!function(t){"use strict";var e,n,r=t.Pos;function i(t){var e=t.flags;return null!=e?e:(t.ignoreCase?"i":"")+(t.global?"g":"")+(t.multiline?"m":"")}function o(t,e){for(var n=i(t),r=n,o=0;o<e.length;o++)-1==r.indexOf(e.charAt(o))&&(r+=e.charAt(o));return n==r?t:new RegExp(t.source,r)}function s(t){return/\\s|\\n|\n|\\W|\\D|\[\^/.test(t.source)}function l(t,e,n){e=o(e,"g");for(var i=n.line,s=n.ch,l=t.lastLine();i<=l;i++,s=0){e.lastIndex=s;var a=t.getLine(i),c=e.exec(a);if(c)return{from:r(i,c.index),to:r(i,c.index+c[0].length),match:c}}}function a(t,e,n){if(!s(e))return l(t,e,n);e=o(e,"gm");for(var i,a=1,c=n.line,h=t.lastLine();c<=h;){for(var u=0;u<a&&!(c>h);u++){var d=t.getLine(c++);i=null==i?d:i+"\n"+d}a*=2,e.lastIndex=n.ch;var f=e.exec(i);if(f){var p=i.slice(0,f.index).split("\n"),g=f[0].split("\n"),m=n.line+p.length-1,v=p[p.length-1].length;return{from:r(m,v),to:r(m+g.length-1,1==g.length?v+g[0].length:g[g.length-1].length),match:f}}}}function c(t,e,n){for(var r,i=0;i<=t.length;){e.lastIndex=i;var o=e.exec(t);if(!o)break;var s=o.index+o[0].length;if(s>t.length-n)break;(!r||s>r.index+r[0].length)&&(r=o),i=o.index+1}return r}function h(t,e,n){e=o(e,"g");for(var i=n.line,s=n.ch,l=t.firstLine();i>=l;i--,s=-1){var a=t.getLine(i),h=c(a,e,s<0?0:a.length-s);if(h)return{from:r(i,h.index),to:r(i,h.index+h[0].length),match:h}}}function u(t,e,n){if(!s(e))return h(t,e,n);e=o(e,"gm");for(var i,l=1,a=t.getLine(n.line).length-n.ch,u=n.line,d=t.firstLine();u>=d;){for(var f=0;f<l&&u>=d;f++){var p=t.getLine(u--);i=null==i?p:p+"\n"+i}l*=2;var g=c(i,e,a);if(g){var m=i.slice(0,g.index).split("\n"),v=g[0].split("\n"),y=u+m.length,b=m[m.length-1].length;return{from:r(y,b),to:r(y+v.length-1,1==v.length?b+v[0].length:v[v.length-1].length),match:g}}}}function d(t,e,n,r){if(t.length==e.length)return n;for(var i=0,o=n+Math.max(0,t.length-e.length);;){if(i==o)return i;var s=i+o>>1,l=r(t.slice(0,s)).length;if(l==n)return s;l>n?o=s:i=s+1}}function f(t,i,o,s){if(!i.length)return null;var l=s?e:n,a=l(i).split(/\r|\n\r?/);t:for(var c=o.line,h=o.ch,u=t.lastLine()+1-a.length;c<=u;c++,h=0){var f=t.getLine(c).slice(h),p=l(f);if(1==a.length){var g=p.indexOf(a[0]);if(-1==g)continue t;return o=d(f,p,g,l)+h,{from:r(c,d(f,p,g,l)+h),to:r(c,d(f,p,g+a[0].length,l)+h)}}var m=p.length-a[0].length;if(p.slice(m)==a[0]){for(var v=1;v<a.length-1;v++)if(l(t.getLine(c+v))!=a[v])continue t;var y=t.getLine(c+a.length-1),b=l(y),w=a[a.length-1];if(b.slice(0,w.length)==w)return{from:r(c,d(f,p,m,l)+h),to:r(c+a.length-1,d(y,b,w.length,l))}}}}function p(t,i,o,s){if(!i.length)return null;var l=s?e:n,a=l(i).split(/\r|\n\r?/);t:for(var c=o.line,h=o.ch,u=t.firstLine()-1+a.length;c>=u;c--,h=-1){var f=t.getLine(c);h>-1&&(f=f.slice(0,h));var p=l(f);if(1==a.length){var g=p.lastIndexOf(a[0]);if(-1==g)continue t;return{from:r(c,d(f,p,g,l)),to:r(c,d(f,p,g+a[0].length,l))}}var m=a[a.length-1];if(p.slice(0,m.length)==m){var v=1;for(o=c-a.length+1;v<a.length-1;v++)if(l(t.getLine(o+v))!=a[v])continue t;var y=t.getLine(c+1-a.length),b=l(y);if(b.slice(b.length-a[0].length)==a[0])return{from:r(c+1-a.length,d(y,b,y.length-a[0].length,l)),to:r(c,d(f,p,m.length,l))}}}}function g(t,e,n,i){var s;this.atOccurrence=!1,this.afterEmptyMatch=!1,this.doc=t,n=n?t.clipPos(n):r(0,0),this.pos={from:n,to:n},"object"==typeof i?s=i.caseFold:(s=i,i=null),"string"==typeof e?(null==s&&(s=!1),this.matches=function(n,r){return(n?p:f)(t,e,r,s)}):(e=o(e,"gm"),i&&!1===i.multiline?this.matches=function(n,r){return(n?h:l)(t,e,r)}:this.matches=function(n,r){return(n?u:a)(t,e,r)})}String.prototype.normalize?(e=function(t){return t.normalize("NFD").toLowerCase()},n=function(t){return t.normalize("NFD")}):(e=function(t){return t.toLowerCase()},n=function(t){return t}),g.prototype={findNext:function(){return this.find(!1)},findPrevious:function(){return this.find(!0)},find:function(e){var n=this.doc.clipPos(e?this.pos.from:this.pos.to);if(this.afterEmptyMatch&&this.atOccurrence&&(n=r(n.line,n.ch),e?(n.ch--,n.ch<0&&(n.line--,n.ch=(this.doc.getLine(n.line)||"").length)):(n.ch++,n.ch>(this.doc.getLine(n.line)||"").length&&(n.ch=0,n.line++)),0!=t.cmpPos(n,this.doc.clipPos(n))))return this.atOccurrence=!1;var i=this.matches(e,n);if(this.afterEmptyMatch=i&&0==t.cmpPos(i.from,i.to),i)return this.pos=i,this.atOccurrence=!0,this.pos.match||!0;var o=r(e?this.doc.firstLine():this.doc.lastLine()+1,0);return this.pos={from:o,to:o},this.atOccurrence=!1},from:function(){if(this.atOccurrence)return this.pos.from},to:function(){if(this.atOccurrence)return this.pos.to},replace:function(e,n){if(this.atOccurrence){var i=t.splitLines(e);this.doc.replaceRange(i,this.pos.from,this.pos.to,n),this.pos.to=r(this.pos.from.line+i.length-1,i[i.length-1].length+(1==i.length?this.pos.from.ch:0))}}},t.defineExtension("getSearchCursor",(function(t,e,n){return new g(this.doc,t,e,n)})),t.defineDocExtension("getSearchCursor",(function(t,e,n){return new g(this,t,e,n)})),t.defineExtension("selectMatches",(function(e,n){for(var r=[],i=this.getSearchCursor(e,this.getCursor("from"),n);i.findNext()&&!(t.cmpPos(i.to(),this.getCursor("to"))>0);)r.push({anchor:i.from(),head:i.to()});r.length&&this.setSelections(r,0)}))}(n(631))},20:(t,e,n)=>{!function(t){"use strict";function e(t){t.state.markedSelection&&t.operation((function(){c(t)}))}function n(t){t.state.markedSelection&&t.state.markedSelection.length&&t.operation((function(){l(t)}))}t.defineOption("styleSelectedText",!1,(function(r,i,o){var s=o&&o!=t.Init;i&&!s?(r.state.markedSelection=[],r.state.markedSelectionStyle="string"==typeof i?i:"CodeMirror-selectedtext",a(r),r.on("cursorActivity",e),r.on("change",n)):!i&&s&&(r.off("cursorActivity",e),r.off("change",n),l(r),r.state.markedSelection=r.state.markedSelectionStyle=null)}));var r=8,i=t.Pos,o=t.cmpPos;function s(t,e,n,s){if(0!=o(e,n))for(var l=t.state.markedSelection,a=t.state.markedSelectionStyle,c=e.line;;){var h=c==e.line?e:i(c,0),u=c+r,d=u>=n.line,f=d?n:i(u,0),p=t.markText(h,f,{className:a});if(null==s?l.push(p):l.splice(s++,0,p),d)break;c=u}}function l(t){for(var e=t.state.markedSelection,n=0;n<e.length;++n)e[n].clear();e.length=0}function a(t){l(t);for(var e=t.listSelections(),n=0;n<e.length;n++)s(t,e[n].from(),e[n].to())}function c(t){if(!t.somethingSelected())return l(t);if(t.listSelections().length>1)return a(t);var e=t.getCursor("start"),n=t.getCursor("end"),i=t.state.markedSelection;if(!i.length)return s(t,e,n);var c=i[0].find(),h=i[i.length-1].find();if(!c||!h||n.line-e.line<=r||o(e,h.to)>=0||o(n,c.from)<=0)return a(t);for(;o(e,c.from)>0;)i.shift().clear(),c=i[0].find();for(o(e,c.from)<0&&(c.to.line-e.line<r?(i.shift().clear(),s(t,e,c.to,0)):s(t,e,c.from,0));o(n,h.to)<0;)i.pop().clear(),h=i[i.length-1].find();o(n,h.to)>0&&(n.line-h.from.line<r?(i.pop().clear(),s(t,h.from,n)):s(t,h.to,n))}}(n(631))},631:function(t){t.exports=function(){"use strict";var t=navigator.userAgent,e=navigator.platform,n=/gecko\/\d/i.test(t),r=/MSIE \d/.test(t),i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(t),o=/Edge\/(\d+)/.exec(t),s=r||i||o,l=s&&(r?document.documentMode||6:+(o||i)[1]),a=!o&&/WebKit\//.test(t),c=a&&/Qt\/\d+\.\d+/.test(t),h=!o&&/Chrome\/(\d+)/.exec(t),u=h&&+h[1],d=/Opera\//.test(t),f=/Apple Computer/.test(navigator.vendor),p=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(t),g=/PhantomJS/.test(t),m=f&&(/Mobile\/\w+/.test(t)||navigator.maxTouchPoints>2),v=/Android/.test(t),y=m||v||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(t),b=m||/Mac/.test(e),w=/\bCrOS\b/.test(t),x=/win/i.test(e),_=d&&t.match(/Version\/(\d*\.\d*)/);_&&(_=Number(_[1])),_&&_>=15&&(d=!1,a=!0);var C=b&&(c||d&&(null==_||_<12.11)),k=n||s&&l>=9;function S(t){return new RegExp("(^|\\s)"+t+"(?:$|\\s)\\s*")}var L,M=function(t,e){var n=t.className,r=S(e).exec(n);if(r){var i=n.slice(r.index+r[0].length);t.className=n.slice(0,r.index)+(i?r[1]+i:"")}};function T(t){for(var e=t.childNodes.length;e>0;--e)t.removeChild(t.firstChild);return t}function O(t,e){return T(t).appendChild(e)}function N(t,e,n,r){var i=document.createElement(t);if(n&&(i.className=n),r&&(i.style.cssText=r),"string"==typeof e)i.appendChild(document.createTextNode(e));else if(e)for(var o=0;o<e.length;++o)i.appendChild(e[o]);return i}function D(t,e,n,r){var i=N(t,e,n,r);return i.setAttribute("role","presentation"),i}function A(t,e){if(3==e.nodeType&&(e=e.parentNode),t.contains)return t.contains(e);do{if(11==e.nodeType&&(e=e.host),e==t)return!0}while(e=e.parentNode)}function E(t){var e;try{e=t.activeElement}catch(n){e=t.body||null}for(;e&&e.shadowRoot&&e.shadowRoot.activeElement;)e=e.shadowRoot.activeElement;return e}function W(t,e){var n=t.className;S(e).test(n)||(t.className+=(n?" ":"")+e)}function P(t,e){for(var n=t.split(" "),r=0;r<n.length;r++)n[r]&&!S(n[r]).test(e)&&(e+=" "+n[r]);return e}L=document.createRange?function(t,e,n,r){var i=document.createRange();return i.setEnd(r||t,n),i.setStart(t,e),i}:function(t,e,n){var r=document.body.createTextRange();try{r.moveToElementText(t.parentNode)}catch(t){return r}return r.collapse(!0),r.moveEnd("character",n),r.moveStart("character",e),r};var H=function(t){t.select()};function z(t){return t.display.wrapper.ownerDocument}function R(t){return z(t).defaultView}function F(t){var e=Array.prototype.slice.call(arguments,1);return function(){return t.apply(null,e)}}function I(t,e,n){for(var r in e||(e={}),t)!t.hasOwnProperty(r)||!1===n&&e.hasOwnProperty(r)||(e[r]=t[r]);return e}function B(t,e,n,r,i){null==e&&-1==(e=t.search(/[^\s\u00a0]/))&&(e=t.length);for(var o=r||0,s=i||0;;){var l=t.indexOf("\t",o);if(l<0||l>=e)return s+(e-o);s+=l-o,s+=n-s%n,o=l+1}}m?H=function(t){t.selectionStart=0,t.selectionEnd=t.value.length}:s&&(H=function(t){try{t.select()}catch(t){}});var V=function(){this.id=null,this.f=null,this.time=0,this.handler=F(this.onTimeout,this)};function $(t,e){for(var n=0;n<t.length;++n)if(t[n]==e)return n;return-1}V.prototype.onTimeout=function(t){t.id=0,t.time<=+new Date?t.f():setTimeout(t.handler,t.time-+new Date)},V.prototype.set=function(t,e){this.f=e;var n=+new Date+t;(!this.id||n<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,t),this.time=n)};var U=50,G={toString:function(){return"CodeMirror.Pass"}},j={scroll:!1},K={origin:"*mouse"},q={origin:"+move"};function X(t,e,n){for(var r=0,i=0;;){var o=t.indexOf("\t",r);-1==o&&(o=t.length);var s=o-r;if(o==t.length||i+s>=e)return r+Math.min(s,e-i);if(i+=o-r,r=o+1,(i+=n-i%n)>=e)return r}}var Y=[""];function Z(t){for(;Y.length<=t;)Y.push(J(Y)+" ");return Y[t]}function J(t){return t[t.length-1]}function Q(t,e){for(var n=[],r=0;r<t.length;r++)n[r]=e(t[r],r);return n}function tt(t,e,n){for(var r=0,i=n(e);r<t.length&&n(t[r])<=i;)r++;t.splice(r,0,e)}function et(){}function nt(t,e){var n;return Object.create?n=Object.create(t):(et.prototype=t,n=new et),e&&I(e,n),n}var rt=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function it(t){return/\w/.test(t)||t>""&&(t.toUpperCase()!=t.toLowerCase()||rt.test(t))}function ot(t,e){return e?!!(e.source.indexOf("\\w")>-1&&it(t))||e.test(t):it(t)}function st(t){for(var e in t)if(t.hasOwnProperty(e)&&t[e])return!1;return!0}var lt=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function at(t){return t.charCodeAt(0)>=768&&lt.test(t)}function ct(t,e,n){for(;(n<0?e>0:e<t.length)&&at(t.charAt(e));)e+=n;return e}function ht(t,e,n){for(var r=e>n?-1:1;;){if(e==n)return e;var i=(e+n)/2,o=r<0?Math.ceil(i):Math.floor(i);if(o==e)return t(o)?e:n;t(o)?n=o:e=o+r}}function ut(t,e,n,r){if(!t)return r(e,n,"ltr",0);for(var i=!1,o=0;o<t.length;++o){var s=t[o];(s.from<n&&s.to>e||e==n&&s.to==e)&&(r(Math.max(s.from,e),Math.min(s.to,n),1==s.level?"rtl":"ltr",o),i=!0)}i||r(e,n,"ltr")}var dt=null;function ft(t,e,n){var r;dt=null;for(var i=0;i<t.length;++i){var o=t[i];if(o.from<e&&o.to>e)return i;o.to==e&&(o.from!=o.to&&"before"==n?r=i:dt=i),o.from==e&&(o.from!=o.to&&"before"!=n?r=i:dt=i)}return null!=r?r:dt}var pt=function(){var t="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",e="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function n(n){return n<=247?t.charAt(n):1424<=n&&n<=1524?"R":1536<=n&&n<=1785?e.charAt(n-1536):1774<=n&&n<=2220?"r":8192<=n&&n<=8203?"w":8204==n?"b":"L"}var r=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,i=/[stwN]/,o=/[LRr]/,s=/[Lb1n]/,l=/[1n]/;function a(t,e,n){this.level=t,this.from=e,this.to=n}return function(t,e){var c="ltr"==e?"L":"R";if(0==t.length||"ltr"==e&&!r.test(t))return!1;for(var h=t.length,u=[],d=0;d<h;++d)u.push(n(t.charCodeAt(d)));for(var f=0,p=c;f<h;++f){var g=u[f];"m"==g?u[f]=p:p=g}for(var m=0,v=c;m<h;++m){var y=u[m];"1"==y&&"r"==v?u[m]="n":o.test(y)&&(v=y,"r"==y&&(u[m]="R"))}for(var b=1,w=u[0];b<h-1;++b){var x=u[b];"+"==x&&"1"==w&&"1"==u[b+1]?u[b]="1":","!=x||w!=u[b+1]||"1"!=w&&"n"!=w||(u[b]=w),w=x}for(var _=0;_<h;++_){var C=u[_];if(","==C)u[_]="N";else if("%"==C){var k=void 0;for(k=_+1;k<h&&"%"==u[k];++k);for(var S=_&&"!"==u[_-1]||k<h&&"1"==u[k]?"1":"N",L=_;L<k;++L)u[L]=S;_=k-1}}for(var M=0,T=c;M<h;++M){var O=u[M];"L"==T&&"1"==O?u[M]="L":o.test(O)&&(T=O)}for(var N=0;N<h;++N)if(i.test(u[N])){var D=void 0;for(D=N+1;D<h&&i.test(u[D]);++D);for(var A="L"==(N?u[N-1]:c),E=A==("L"==(D<h?u[D]:c))?A?"L":"R":c,W=N;W<D;++W)u[W]=E;N=D-1}for(var P,H=[],z=0;z<h;)if(s.test(u[z])){var R=z;for(++z;z<h&&s.test(u[z]);++z);H.push(new a(0,R,z))}else{var F=z,I=H.length,B="rtl"==e?1:0;for(++z;z<h&&"L"!=u[z];++z);for(var V=F;V<z;)if(l.test(u[V])){F<V&&(H.splice(I,0,new a(1,F,V)),I+=B);var $=V;for(++V;V<z&&l.test(u[V]);++V);H.splice(I,0,new a(2,$,V)),I+=B,F=V}else++V;F<z&&H.splice(I,0,new a(1,F,z))}return"ltr"==e&&(1==H[0].level&&(P=t.match(/^\s+/))&&(H[0].from=P[0].length,H.unshift(new a(0,0,P[0].length))),1==J(H).level&&(P=t.match(/\s+$/))&&(J(H).to-=P[0].length,H.push(new a(0,h-P[0].length,h)))),"rtl"==e?H.reverse():H}}();function gt(t,e){var n=t.order;return null==n&&(n=t.order=pt(t.text,e)),n}var mt=[],vt=function(t,e,n){if(t.addEventListener)t.addEventListener(e,n,!1);else if(t.attachEvent)t.attachEvent("on"+e,n);else{var r=t._handlers||(t._handlers={});r[e]=(r[e]||mt).concat(n)}};function yt(t,e){return t._handlers&&t._handlers[e]||mt}function bt(t,e,n){if(t.removeEventListener)t.removeEventListener(e,n,!1);else if(t.detachEvent)t.detachEvent("on"+e,n);else{var r=t._handlers,i=r&&r[e];if(i){var o=$(i,n);o>-1&&(r[e]=i.slice(0,o).concat(i.slice(o+1)))}}}function wt(t,e){var n=yt(t,e);if(n.length)for(var r=Array.prototype.slice.call(arguments,2),i=0;i<n.length;++i)n[i].apply(null,r)}function xt(t,e,n){return"string"==typeof e&&(e={type:e,preventDefault:function(){this.defaultPrevented=!0}}),wt(t,n||e.type,t,e),Mt(e)||e.codemirrorIgnore}function _t(t){var e=t._handlers&&t._handlers.cursorActivity;if(e)for(var n=t.curOp.cursorActivityHandlers||(t.curOp.cursorActivityHandlers=[]),r=0;r<e.length;++r)-1==$(n,e[r])&&n.push(e[r])}function Ct(t,e){return yt(t,e).length>0}function kt(t){t.prototype.on=function(t,e){vt(this,t,e)},t.prototype.off=function(t,e){bt(this,t,e)}}function St(t){t.preventDefault?t.preventDefault():t.returnValue=!1}function Lt(t){t.stopPropagation?t.stopPropagation():t.cancelBubble=!0}function Mt(t){return null!=t.defaultPrevented?t.defaultPrevented:0==t.returnValue}function Tt(t){St(t),Lt(t)}function Ot(t){return t.target||t.srcElement}function Nt(t){var e=t.which;return null==e&&(1&t.button?e=1:2&t.button?e=3:4&t.button&&(e=2)),b&&t.ctrlKey&&1==e&&(e=3),e}var Dt,At,Et=function(){if(s&&l<9)return!1;var t=N("div");return"draggable"in t||"dragDrop"in t}();function Wt(t){if(null==Dt){var e=N("span","​");O(t,N("span",[e,document.createTextNode("x")])),0!=t.firstChild.offsetHeight&&(Dt=e.offsetWidth<=1&&e.offsetHeight>2&&!(s&&l<8))}var n=Dt?N("span","​"):N("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return n.setAttribute("cm-text",""),n}function Pt(t){if(null!=At)return At;var e=O(t,document.createTextNode("AخA")),n=L(e,0,1).getBoundingClientRect(),r=L(e,1,2).getBoundingClientRect();return T(t),!(!n||n.left==n.right)&&(At=r.right-n.right<3)}var Ht,zt=3!="\n\nb".split(/\n/).length?function(t){for(var e=0,n=[],r=t.length;e<=r;){var i=t.indexOf("\n",e);-1==i&&(i=t.length);var o=t.slice(e,"\r"==t.charAt(i-1)?i-1:i),s=o.indexOf("\r");-1!=s?(n.push(o.slice(0,s)),e+=s+1):(n.push(o),e=i+1)}return n}:function(t){return t.split(/\r\n?|\n/)},Rt=window.getSelection?function(t){try{return t.selectionStart!=t.selectionEnd}catch(t){return!1}}:function(t){var e;try{e=t.ownerDocument.selection.createRange()}catch(t){}return!(!e||e.parentElement()!=t)&&0!=e.compareEndPoints("StartToEnd",e)},Ft="oncopy"in(Ht=N("div"))||(Ht.setAttribute("oncopy","return;"),"function"==typeof Ht.oncopy),It=null;function Bt(t){if(null!=It)return It;var e=O(t,N("span","x")),n=e.getBoundingClientRect(),r=L(e,0,1).getBoundingClientRect();return It=Math.abs(n.left-r.left)>1}var Vt={},$t={};function Ut(t,e){arguments.length>2&&(e.dependencies=Array.prototype.slice.call(arguments,2)),Vt[t]=e}function Gt(t,e){$t[t]=e}function jt(t){if("string"==typeof t&&$t.hasOwnProperty(t))t=$t[t];else if(t&&"string"==typeof t.name&&$t.hasOwnProperty(t.name)){var e=$t[t.name];"string"==typeof e&&(e={name:e}),(t=nt(e,t)).name=e.name}else{if("string"==typeof t&&/^[\w\-]+\/[\w\-]+\+xml$/.test(t))return jt("application/xml");if("string"==typeof t&&/^[\w\-]+\/[\w\-]+\+json$/.test(t))return jt("application/json")}return"string"==typeof t?{name:t}:t||{name:"null"}}function Kt(t,e){e=jt(e);var n=Vt[e.name];if(!n)return Kt(t,"text/plain");var r=n(t,e);if(qt.hasOwnProperty(e.name)){var i=qt[e.name];for(var o in i)i.hasOwnProperty(o)&&(r.hasOwnProperty(o)&&(r["_"+o]=r[o]),r[o]=i[o])}if(r.name=e.name,e.helperType&&(r.helperType=e.helperType),e.modeProps)for(var s in e.modeProps)r[s]=e.modeProps[s];return r}var qt={};function Xt(t,e){I(e,qt.hasOwnProperty(t)?qt[t]:qt[t]={})}function Yt(t,e){if(!0===e)return e;if(t.copyState)return t.copyState(e);var n={};for(var r in e){var i=e[r];i instanceof Array&&(i=i.concat([])),n[r]=i}return n}function Zt(t,e){for(var n;t.innerMode&&(n=t.innerMode(e))&&n.mode!=t;)e=n.state,t=n.mode;return n||{mode:t,state:e}}function Jt(t,e,n){return!t.startState||t.startState(e,n)}var Qt=function(t,e,n){this.pos=this.start=0,this.string=t,this.tabSize=e||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=n};function te(t,e){if((e-=t.first)<0||e>=t.size)throw new Error("There is no line "+(e+t.first)+" in the document.");for(var n=t;!n.lines;)for(var r=0;;++r){var i=n.children[r],o=i.chunkSize();if(e<o){n=i;break}e-=o}return n.lines[e]}function ee(t,e,n){var r=[],i=e.line;return t.iter(e.line,n.line+1,(function(t){var o=t.text;i==n.line&&(o=o.slice(0,n.ch)),i==e.line&&(o=o.slice(e.ch)),r.push(o),++i})),r}function ne(t,e,n){var r=[];return t.iter(e,n,(function(t){r.push(t.text)})),r}function re(t,e){var n=e-t.height;if(n)for(var r=t;r;r=r.parent)r.height+=n}function ie(t){if(null==t.parent)return null;for(var e=t.parent,n=$(e.lines,t),r=e.parent;r;e=r,r=r.parent)for(var i=0;r.children[i]!=e;++i)n+=r.children[i].chunkSize();return n+e.first}function oe(t,e){var n=t.first;t:do{for(var r=0;r<t.children.length;++r){var i=t.children[r],o=i.height;if(e<o){t=i;continue t}e-=o,n+=i.chunkSize()}return n}while(!t.lines);for(var s=0;s<t.lines.length;++s){var l=t.lines[s].height;if(e<l)break;e-=l}return n+s}function se(t,e){return e>=t.first&&e<t.first+t.size}function le(t,e){return String(t.lineNumberFormatter(e+t.firstLineNumber))}function ae(t,e,n){if(void 0===n&&(n=null),!(this instanceof ae))return new ae(t,e,n);this.line=t,this.ch=e,this.sticky=n}function ce(t,e){return t.line-e.line||t.ch-e.ch}function he(t,e){return t.sticky==e.sticky&&0==ce(t,e)}function ue(t){return ae(t.line,t.ch)}function de(t,e){return ce(t,e)<0?e:t}function fe(t,e){return ce(t,e)<0?t:e}function pe(t,e){return Math.max(t.first,Math.min(e,t.first+t.size-1))}function ge(t,e){if(e.line<t.first)return ae(t.first,0);var n=t.first+t.size-1;return e.line>n?ae(n,te(t,n).text.length):me(e,te(t,e.line).text.length)}function me(t,e){var n=t.ch;return null==n||n>e?ae(t.line,e):n<0?ae(t.line,0):t}function ve(t,e){for(var n=[],r=0;r<e.length;r++)n[r]=ge(t,e[r]);return n}Qt.prototype.eol=function(){return this.pos>=this.string.length},Qt.prototype.sol=function(){return this.pos==this.lineStart},Qt.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},Qt.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},Qt.prototype.eat=function(t){var e=this.string.charAt(this.pos);if("string"==typeof t?e==t:e&&(t.test?t.test(e):t(e)))return++this.pos,e},Qt.prototype.eatWhile=function(t){for(var e=this.pos;this.eat(t););return this.pos>e},Qt.prototype.eatSpace=function(){for(var t=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>t},Qt.prototype.skipToEnd=function(){this.pos=this.string.length},Qt.prototype.skipTo=function(t){var e=this.string.indexOf(t,this.pos);if(e>-1)return this.pos=e,!0},Qt.prototype.backUp=function(t){this.pos-=t},Qt.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=B(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?B(this.string,this.lineStart,this.tabSize):0)},Qt.prototype.indentation=function(){return B(this.string,null,this.tabSize)-(this.lineStart?B(this.string,this.lineStart,this.tabSize):0)},Qt.prototype.match=function(t,e,n){if("string"!=typeof t){var r=this.string.slice(this.pos).match(t);return r&&r.index>0?null:(r&&!1!==e&&(this.pos+=r[0].length),r)}var i=function(t){return n?t.toLowerCase():t};if(i(this.string.substr(this.pos,t.length))==i(t))return!1!==e&&(this.pos+=t.length),!0},Qt.prototype.current=function(){return this.string.slice(this.start,this.pos)},Qt.prototype.hideFirstChars=function(t,e){this.lineStart+=t;try{return e()}finally{this.lineStart-=t}},Qt.prototype.lookAhead=function(t){var e=this.lineOracle;return e&&e.lookAhead(t)},Qt.prototype.baseToken=function(){var t=this.lineOracle;return t&&t.baseToken(this.pos)};var ye=function(t,e){this.state=t,this.lookAhead=e},be=function(t,e,n,r){this.state=e,this.doc=t,this.line=n,this.maxLookAhead=r||0,this.baseTokens=null,this.baseTokenPos=1};function we(t,e,n,r){var i=[t.state.modeGen],o={};Oe(t,e.text,t.doc.mode,n,(function(t,e){return i.push(t,e)}),o,r);for(var s=n.state,l=function(r){n.baseTokens=i;var l=t.state.overlays[r],a=1,c=0;n.state=!0,Oe(t,e.text,l.mode,n,(function(t,e){for(var n=a;c<t;){var r=i[a];r>t&&i.splice(a,1,t,i[a+1],r),a+=2,c=Math.min(t,r)}if(e)if(l.opaque)i.splice(n,a-n,t,"overlay "+e),a=n+2;else for(;n<a;n+=2){var o=i[n+1];i[n+1]=(o?o+" ":"")+"overlay "+e}}),o),n.state=s,n.baseTokens=null,n.baseTokenPos=1},a=0;a<t.state.overlays.length;++a)l(a);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function xe(t,e,n){if(!e.styles||e.styles[0]!=t.state.modeGen){var r=_e(t,ie(e)),i=e.text.length>t.options.maxHighlightLength&&Yt(t.doc.mode,r.state),o=we(t,e,r);i&&(r.state=i),e.stateAfter=r.save(!i),e.styles=o.styles,o.classes?e.styleClasses=o.classes:e.styleClasses&&(e.styleClasses=null),n===t.doc.highlightFrontier&&(t.doc.modeFrontier=Math.max(t.doc.modeFrontier,++t.doc.highlightFrontier))}return e.styles}function _e(t,e,n){var r=t.doc,i=t.display;if(!r.mode.startState)return new be(r,!0,e);var o=Ne(t,e,n),s=o>r.first&&te(r,o-1).stateAfter,l=s?be.fromSaved(r,s,o):new be(r,Jt(r.mode),o);return r.iter(o,e,(function(n){Ce(t,n.text,l);var r=l.line;n.stateAfter=r==e-1||r%5==0||r>=i.viewFrom&&r<i.viewTo?l.save():null,l.nextLine()})),n&&(r.modeFrontier=l.line),l}function Ce(t,e,n,r){var i=t.doc.mode,o=new Qt(e,t.options.tabSize,n);for(o.start=o.pos=r||0,""==e&&ke(i,n.state);!o.eol();)Se(i,o,n.state),o.start=o.pos}function ke(t,e){if(t.blankLine)return t.blankLine(e);if(t.innerMode){var n=Zt(t,e);return n.mode.blankLine?n.mode.blankLine(n.state):void 0}}function Se(t,e,n,r){for(var i=0;i<10;i++){r&&(r[0]=Zt(t,n).mode);var o=t.token(e,n);if(e.pos>e.start)return o}throw new Error("Mode "+t.name+" failed to advance stream.")}be.prototype.lookAhead=function(t){var e=this.doc.getLine(this.line+t);return null!=e&&t>this.maxLookAhead&&(this.maxLookAhead=t),e},be.prototype.baseToken=function(t){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=t;)this.baseTokenPos+=2;var e=this.baseTokens[this.baseTokenPos+1];return{type:e&&e.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-t}},be.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},be.fromSaved=function(t,e,n){return e instanceof ye?new be(t,Yt(t.mode,e.state),n,e.lookAhead):new be(t,Yt(t.mode,e),n)},be.prototype.save=function(t){var e=!1!==t?Yt(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new ye(e,this.maxLookAhead):e};var Le=function(t,e,n){this.start=t.start,this.end=t.pos,this.string=t.current(),this.type=e||null,this.state=n};function Me(t,e,n,r){var i,o,s=t.doc,l=s.mode,a=te(s,(e=ge(s,e)).line),c=_e(t,e.line,n),h=new Qt(a.text,t.options.tabSize,c);for(r&&(o=[]);(r||h.pos<e.ch)&&!h.eol();)h.start=h.pos,i=Se(l,h,c.state),r&&o.push(new Le(h,i,Yt(s.mode,c.state)));return r?o:new Le(h,i,c.state)}function Te(t,e){if(t)for(;;){var n=t.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!n)break;t=t.slice(0,n.index)+t.slice(n.index+n[0].length);var r=n[1]?"bgClass":"textClass";null==e[r]?e[r]=n[2]:new RegExp("(?:^|\\s)"+n[2]+"(?:$|\\s)").test(e[r])||(e[r]+=" "+n[2])}return t}function Oe(t,e,n,r,i,o,s){var l=n.flattenSpans;null==l&&(l=t.options.flattenSpans);var a,c=0,h=null,u=new Qt(e,t.options.tabSize,r),d=t.options.addModeClass&&[null];for(""==e&&Te(ke(n,r.state),o);!u.eol();){if(u.pos>t.options.maxHighlightLength?(l=!1,s&&Ce(t,e,r,u.pos),u.pos=e.length,a=null):a=Te(Se(n,u,r.state,d),o),d){var f=d[0].name;f&&(a="m-"+(a?f+" "+a:f))}if(!l||h!=a){for(;c<u.start;)i(c=Math.min(u.start,c+5e3),h);h=a}u.start=u.pos}for(;c<u.pos;){var p=Math.min(u.pos,c+5e3);i(p,h),c=p}}function Ne(t,e,n){for(var r,i,o=t.doc,s=n?-1:e-(t.doc.mode.innerMode?1e3:100),l=e;l>s;--l){if(l<=o.first)return o.first;var a=te(o,l-1),c=a.stateAfter;if(c&&(!n||l+(c instanceof ye?c.lookAhead:0)<=o.modeFrontier))return l;var h=B(a.text,null,t.options.tabSize);(null==i||r>h)&&(i=l-1,r=h)}return i}function De(t,e){if(t.modeFrontier=Math.min(t.modeFrontier,e),!(t.highlightFrontier<e-10)){for(var n=t.first,r=e-1;r>n;r--){var i=te(t,r).stateAfter;if(i&&(!(i instanceof ye)||r+i.lookAhead<e)){n=r+1;break}}t.highlightFrontier=Math.min(t.highlightFrontier,n)}}var Ae=!1,Ee=!1;function We(){Ae=!0}function Pe(){Ee=!0}function He(t,e,n){this.marker=t,this.from=e,this.to=n}function ze(t,e){if(t)for(var n=0;n<t.length;++n){var r=t[n];if(r.marker==e)return r}}function Re(t,e){for(var n,r=0;r<t.length;++r)t[r]!=e&&(n||(n=[])).push(t[r]);return n}function Fe(t,e,n){var r=n&&window.WeakSet&&(n.markedSpans||(n.markedSpans=new WeakSet));r&&t.markedSpans&&r.has(t.markedSpans)?t.markedSpans.push(e):(t.markedSpans=t.markedSpans?t.markedSpans.concat([e]):[e],r&&r.add(t.markedSpans)),e.marker.attachLine(t)}function Ie(t,e,n){var r;if(t)for(var i=0;i<t.length;++i){var o=t[i],s=o.marker;if(null==o.from||(s.inclusiveLeft?o.from<=e:o.from<e)||o.from==e&&"bookmark"==s.type&&(!n||!o.marker.insertLeft)){var l=null==o.to||(s.inclusiveRight?o.to>=e:o.to>e);(r||(r=[])).push(new He(s,o.from,l?null:o.to))}}return r}function Be(t,e,n){var r;if(t)for(var i=0;i<t.length;++i){var o=t[i],s=o.marker;if(null==o.to||(s.inclusiveRight?o.to>=e:o.to>e)||o.from==e&&"bookmark"==s.type&&(!n||o.marker.insertLeft)){var l=null==o.from||(s.inclusiveLeft?o.from<=e:o.from<e);(r||(r=[])).push(new He(s,l?null:o.from-e,null==o.to?null:o.to-e))}}return r}function Ve(t,e){if(e.full)return null;var n=se(t,e.from.line)&&te(t,e.from.line).markedSpans,r=se(t,e.to.line)&&te(t,e.to.line).markedSpans;if(!n&&!r)return null;var i=e.from.ch,o=e.to.ch,s=0==ce(e.from,e.to),l=Ie(n,i,s),a=Be(r,o,s),c=1==e.text.length,h=J(e.text).length+(c?i:0);if(l)for(var u=0;u<l.length;++u){var d=l[u];if(null==d.to){var f=ze(a,d.marker);f?c&&(d.to=null==f.to?null:f.to+h):d.to=i}}if(a)for(var p=0;p<a.length;++p){var g=a[p];null!=g.to&&(g.to+=h),null==g.from?ze(l,g.marker)||(g.from=h,c&&(l||(l=[])).push(g)):(g.from+=h,c&&(l||(l=[])).push(g))}l&&(l=$e(l)),a&&a!=l&&(a=$e(a));var m=[l];if(!c){var v,y=e.text.length-2;if(y>0&&l)for(var b=0;b<l.length;++b)null==l[b].to&&(v||(v=[])).push(new He(l[b].marker,null,null));for(var w=0;w<y;++w)m.push(v);m.push(a)}return m}function $e(t){for(var e=0;e<t.length;++e){var n=t[e];null!=n.from&&n.from==n.to&&!1!==n.marker.clearWhenEmpty&&t.splice(e--,1)}return t.length?t:null}function Ue(t,e,n){var r=null;if(t.iter(e.line,n.line+1,(function(t){if(t.markedSpans)for(var e=0;e<t.markedSpans.length;++e){var n=t.markedSpans[e].marker;!n.readOnly||r&&-1!=$(r,n)||(r||(r=[])).push(n)}})),!r)return null;for(var i=[{from:e,to:n}],o=0;o<r.length;++o)for(var s=r[o],l=s.find(0),a=0;a<i.length;++a){var c=i[a];if(!(ce(c.to,l.from)<0||ce(c.from,l.to)>0)){var h=[a,1],u=ce(c.from,l.from),d=ce(c.to,l.to);(u<0||!s.inclusiveLeft&&!u)&&h.push({from:c.from,to:l.from}),(d>0||!s.inclusiveRight&&!d)&&h.push({from:l.to,to:c.to}),i.splice.apply(i,h),a+=h.length-3}}return i}function Ge(t){var e=t.markedSpans;if(e){for(var n=0;n<e.length;++n)e[n].marker.detachLine(t);t.markedSpans=null}}function je(t,e){if(e){for(var n=0;n<e.length;++n)e[n].marker.attachLine(t);t.markedSpans=e}}function Ke(t){return t.inclusiveLeft?-1:0}function qe(t){return t.inclusiveRight?1:0}function Xe(t,e){var n=t.lines.length-e.lines.length;if(0!=n)return n;var r=t.find(),i=e.find(),o=ce(r.from,i.from)||Ke(t)-Ke(e);if(o)return-o;var s=ce(r.to,i.to)||qe(t)-qe(e);return s||e.id-t.id}function Ye(t,e){var n,r=Ee&&t.markedSpans;if(r)for(var i=void 0,o=0;o<r.length;++o)(i=r[o]).marker.collapsed&&null==(e?i.from:i.to)&&(!n||Xe(n,i.marker)<0)&&(n=i.marker);return n}function Ze(t){return Ye(t,!0)}function Je(t){return Ye(t,!1)}function Qe(t,e){var n,r=Ee&&t.markedSpans;if(r)for(var i=0;i<r.length;++i){var o=r[i];o.marker.collapsed&&(null==o.from||o.from<e)&&(null==o.to||o.to>e)&&(!n||Xe(n,o.marker)<0)&&(n=o.marker)}return n}function tn(t,e,n,r,i){var o=te(t,e),s=Ee&&o.markedSpans;if(s)for(var l=0;l<s.length;++l){var a=s[l];if(a.marker.collapsed){var c=a.marker.find(0),h=ce(c.from,n)||Ke(a.marker)-Ke(i),u=ce(c.to,r)||qe(a.marker)-qe(i);if(!(h>=0&&u<=0||h<=0&&u>=0)&&(h<=0&&(a.marker.inclusiveRight&&i.inclusiveLeft?ce(c.to,n)>=0:ce(c.to,n)>0)||h>=0&&(a.marker.inclusiveRight&&i.inclusiveLeft?ce(c.from,r)<=0:ce(c.from,r)<0)))return!0}}}function en(t){for(var e;e=Ze(t);)t=e.find(-1,!0).line;return t}function nn(t){for(var e;e=Je(t);)t=e.find(1,!0).line;return t}function rn(t){for(var e,n;e=Je(t);)t=e.find(1,!0).line,(n||(n=[])).push(t);return n}function on(t,e){var n=te(t,e),r=en(n);return n==r?e:ie(r)}function sn(t,e){if(e>t.lastLine())return e;var n,r=te(t,e);if(!ln(t,r))return e;for(;n=Je(r);)r=n.find(1,!0).line;return ie(r)+1}function ln(t,e){var n=Ee&&e.markedSpans;if(n)for(var r=void 0,i=0;i<n.length;++i)if((r=n[i]).marker.collapsed){if(null==r.from)return!0;if(!r.marker.widgetNode&&0==r.from&&r.marker.inclusiveLeft&&an(t,e,r))return!0}}function an(t,e,n){if(null==n.to){var r=n.marker.find(1,!0);return an(t,r.line,ze(r.line.markedSpans,n.marker))}if(n.marker.inclusiveRight&&n.to==e.text.length)return!0;for(var i=void 0,o=0;o<e.markedSpans.length;++o)if((i=e.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==n.to&&(null==i.to||i.to!=n.from)&&(i.marker.inclusiveLeft||n.marker.inclusiveRight)&&an(t,e,i))return!0}function cn(t){for(var e=0,n=(t=en(t)).parent,r=0;r<n.lines.length;++r){var i=n.lines[r];if(i==t)break;e+=i.height}for(var o=n.parent;o;o=(n=o).parent)for(var s=0;s<o.children.length;++s){var l=o.children[s];if(l==n)break;e+=l.height}return e}function hn(t){if(0==t.height)return 0;for(var e,n=t.text.length,r=t;e=Ze(r);){var i=e.find(0,!0);r=i.from.line,n+=i.from.ch-i.to.ch}for(r=t;e=Je(r);){var o=e.find(0,!0);n-=r.text.length-o.from.ch,n+=(r=o.to.line).text.length-o.to.ch}return n}function un(t){var e=t.display,n=t.doc;e.maxLine=te(n,n.first),e.maxLineLength=hn(e.maxLine),e.maxLineChanged=!0,n.iter((function(t){var n=hn(t);n>e.maxLineLength&&(e.maxLineLength=n,e.maxLine=t)}))}var dn=function(t,e,n){this.text=t,je(this,e),this.height=n?n(this):1};function fn(t,e,n,r){t.text=e,t.stateAfter&&(t.stateAfter=null),t.styles&&(t.styles=null),null!=t.order&&(t.order=null),Ge(t),je(t,n);var i=r?r(t):1;i!=t.height&&re(t,i)}function pn(t){t.parent=null,Ge(t)}dn.prototype.lineNo=function(){return ie(this)},kt(dn);var gn={},mn={};function vn(t,e){if(!t||/^\s*$/.test(t))return null;var n=e.addModeClass?mn:gn;return n[t]||(n[t]=t.replace(/\S+/g,"cm-$&"))}function yn(t,e){var n=D("span",null,null,a?"padding-right: .1px":null),r={pre:D("pre",[n],"CodeMirror-line"),content:n,col:0,pos:0,cm:t,trailingSpace:!1,splitSpaces:t.getOption("lineWrapping")};e.measure={};for(var i=0;i<=(e.rest?e.rest.length:0);i++){var o=i?e.rest[i-1]:e.line,s=void 0;r.pos=0,r.addToken=wn,Pt(t.display.measure)&&(s=gt(o,t.doc.direction))&&(r.addToken=_n(r.addToken,s)),r.map=[],kn(o,r,xe(t,o,e!=t.display.externalMeasured&&ie(o))),o.styleClasses&&(o.styleClasses.bgClass&&(r.bgClass=P(o.styleClasses.bgClass,r.bgClass||"")),o.styleClasses.textClass&&(r.textClass=P(o.styleClasses.textClass,r.textClass||""))),0==r.map.length&&r.map.push(0,0,r.content.appendChild(Wt(t.display.measure))),0==i?(e.measure.map=r.map,e.measure.cache={}):((e.measure.maps||(e.measure.maps=[])).push(r.map),(e.measure.caches||(e.measure.caches=[])).push({}))}if(a){var l=r.content.lastChild;(/\bcm-tab\b/.test(l.className)||l.querySelector&&l.querySelector(".cm-tab"))&&(r.content.className="cm-tab-wrap-hack")}return wt(t,"renderLine",t,e.line,r.pre),r.pre.className&&(r.textClass=P(r.pre.className,r.textClass||"")),r}function bn(t){var e=N("span","•","cm-invalidchar");return e.title="\\u"+t.charCodeAt(0).toString(16),e.setAttribute("aria-label",e.title),e}function wn(t,e,n,r,i,o,a){if(e){var c,h=t.splitSpaces?xn(e,t.trailingSpace):e,u=t.cm.state.specialChars,d=!1;if(u.test(e)){c=document.createDocumentFragment();for(var f=0;;){u.lastIndex=f;var p=u.exec(e),g=p?p.index-f:e.length-f;if(g){var m=document.createTextNode(h.slice(f,f+g));s&&l<9?c.appendChild(N("span",[m])):c.appendChild(m),t.map.push(t.pos,t.pos+g,m),t.col+=g,t.pos+=g}if(!p)break;f+=g+1;var v=void 0;if("\t"==p[0]){var y=t.cm.options.tabSize,b=y-t.col%y;(v=c.appendChild(N("span",Z(b),"cm-tab"))).setAttribute("role","presentation"),v.setAttribute("cm-text","\t"),t.col+=b}else"\r"==p[0]||"\n"==p[0]?((v=c.appendChild(N("span","\r"==p[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",p[0]),t.col+=1):((v=t.cm.options.specialCharPlaceholder(p[0])).setAttribute("cm-text",p[0]),s&&l<9?c.appendChild(N("span",[v])):c.appendChild(v),t.col+=1);t.map.push(t.pos,t.pos+1,v),t.pos++}}else t.col+=e.length,c=document.createTextNode(h),t.map.push(t.pos,t.pos+e.length,c),s&&l<9&&(d=!0),t.pos+=e.length;if(t.trailingSpace=32==h.charCodeAt(e.length-1),n||r||i||d||o||a){var w=n||"";r&&(w+=r),i&&(w+=i);var x=N("span",[c],w,o);if(a)for(var _ in a)a.hasOwnProperty(_)&&"style"!=_&&"class"!=_&&x.setAttribute(_,a[_]);return t.content.appendChild(x)}t.content.appendChild(c)}}function xn(t,e){if(t.length>1&&!/  /.test(t))return t;for(var n=e,r="",i=0;i<t.length;i++){var o=t.charAt(i);" "!=o||!n||i!=t.length-1&&32!=t.charCodeAt(i+1)||(o=" "),r+=o,n=" "==o}return r}function _n(t,e){return function(n,r,i,o,s,l,a){i=i?i+" cm-force-border":"cm-force-border";for(var c=n.pos,h=c+r.length;;){for(var u=void 0,d=0;d<e.length&&!((u=e[d]).to>c&&u.from<=c);d++);if(u.to>=h)return t(n,r,i,o,s,l,a);t(n,r.slice(0,u.to-c),i,o,null,l,a),o=null,r=r.slice(u.to-c),c=u.to}}}function Cn(t,e,n,r){var i=!r&&n.widgetNode;i&&t.map.push(t.pos,t.pos+e,i),!r&&t.cm.display.input.needsContentAttribute&&(i||(i=t.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",n.id)),i&&(t.cm.display.input.setUneditable(i),t.content.appendChild(i)),t.pos+=e,t.trailingSpace=!1}function kn(t,e,n){var r=t.markedSpans,i=t.text,o=0;if(r)for(var s,l,a,c,h,u,d,f=i.length,p=0,g=1,m="",v=0;;){if(v==p){a=c=h=l="",d=null,u=null,v=1/0;for(var y=[],b=void 0,w=0;w<r.length;++w){var x=r[w],_=x.marker;if("bookmark"==_.type&&x.from==p&&_.widgetNode)y.push(_);else if(x.from<=p&&(null==x.to||x.to>p||_.collapsed&&x.to==p&&x.from==p)){if(null!=x.to&&x.to!=p&&v>x.to&&(v=x.to,c=""),_.className&&(a+=" "+_.className),_.css&&(l=(l?l+";":"")+_.css),_.startStyle&&x.from==p&&(h+=" "+_.startStyle),_.endStyle&&x.to==v&&(b||(b=[])).push(_.endStyle,x.to),_.title&&((d||(d={})).title=_.title),_.attributes)for(var C in _.attributes)(d||(d={}))[C]=_.attributes[C];_.collapsed&&(!u||Xe(u.marker,_)<0)&&(u=x)}else x.from>p&&v>x.from&&(v=x.from)}if(b)for(var k=0;k<b.length;k+=2)b[k+1]==v&&(c+=" "+b[k]);if(!u||u.from==p)for(var S=0;S<y.length;++S)Cn(e,0,y[S]);if(u&&(u.from||0)==p){if(Cn(e,(null==u.to?f+1:u.to)-p,u.marker,null==u.from),null==u.to)return;u.to==p&&(u=!1)}}if(p>=f)break;for(var L=Math.min(f,v);;){if(m){var M=p+m.length;if(!u){var T=M>L?m.slice(0,L-p):m;e.addToken(e,T,s?s+a:a,h,p+T.length==v?c:"",l,d)}if(M>=L){m=m.slice(L-p),p=L;break}p=M,h=""}m=i.slice(o,o=n[g++]),s=vn(n[g++],e.cm.options)}}else for(var O=1;O<n.length;O+=2)e.addToken(e,i.slice(o,o=n[O]),vn(n[O+1],e.cm.options))}function Sn(t,e,n){this.line=e,this.rest=rn(e),this.size=this.rest?ie(J(this.rest))-n+1:1,this.node=this.text=null,this.hidden=ln(t,e)}function Ln(t,e,n){for(var r,i=[],o=e;o<n;o=r){var s=new Sn(t.doc,te(t.doc,o),o);r=o+s.size,i.push(s)}return i}var Mn=null;function Tn(t){Mn?Mn.ops.push(t):t.ownsGroup=Mn={ops:[t],delayedCallbacks:[]}}function On(t){var e=t.delayedCallbacks,n=0;do{for(;n<e.length;n++)e[n].call(null);for(var r=0;r<t.ops.length;r++){var i=t.ops[r];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(n<e.length)}function Nn(t,e){var n=t.ownsGroup;if(n)try{On(n)}finally{Mn=null,e(n)}}var Dn=null;function An(t,e){var n=yt(t,e);if(n.length){var r,i=Array.prototype.slice.call(arguments,2);Mn?r=Mn.delayedCallbacks:Dn?r=Dn:(r=Dn=[],setTimeout(En,0));for(var o=function(t){r.push((function(){return n[t].apply(null,i)}))},s=0;s<n.length;++s)o(s)}}function En(){var t=Dn;Dn=null;for(var e=0;e<t.length;++e)t[e]()}function Wn(t,e,n,r){for(var i=0;i<e.changes.length;i++){var o=e.changes[i];"text"==o?Rn(t,e):"gutter"==o?In(t,e,n,r):"class"==o?Fn(t,e):"widget"==o&&Bn(t,e,r)}e.changes=null}function Pn(t){return t.node==t.text&&(t.node=N("div",null,null,"position: relative"),t.text.parentNode&&t.text.parentNode.replaceChild(t.node,t.text),t.node.appendChild(t.text),s&&l<8&&(t.node.style.zIndex=2)),t.node}function Hn(t,e){var n=e.bgClass?e.bgClass+" "+(e.line.bgClass||""):e.line.bgClass;if(n&&(n+=" CodeMirror-linebackground"),e.background)n?e.background.className=n:(e.background.parentNode.removeChild(e.background),e.background=null);else if(n){var r=Pn(e);e.background=r.insertBefore(N("div",null,n),r.firstChild),t.display.input.setUneditable(e.background)}}function zn(t,e){var n=t.display.externalMeasured;return n&&n.line==e.line?(t.display.externalMeasured=null,e.measure=n.measure,n.built):yn(t,e)}function Rn(t,e){var n=e.text.className,r=zn(t,e);e.text==e.node&&(e.node=r.pre),e.text.parentNode.replaceChild(r.pre,e.text),e.text=r.pre,r.bgClass!=e.bgClass||r.textClass!=e.textClass?(e.bgClass=r.bgClass,e.textClass=r.textClass,Fn(t,e)):n&&(e.text.className=n)}function Fn(t,e){Hn(t,e),e.line.wrapClass?Pn(e).className=e.line.wrapClass:e.node!=e.text&&(e.node.className="");var n=e.textClass?e.textClass+" "+(e.line.textClass||""):e.line.textClass;e.text.className=n||""}function In(t,e,n,r){if(e.gutter&&(e.node.removeChild(e.gutter),e.gutter=null),e.gutterBackground&&(e.node.removeChild(e.gutterBackground),e.gutterBackground=null),e.line.gutterClass){var i=Pn(e);e.gutterBackground=N("div",null,"CodeMirror-gutter-background "+e.line.gutterClass,"left: "+(t.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px; width: "+r.gutterTotalWidth+"px"),t.display.input.setUneditable(e.gutterBackground),i.insertBefore(e.gutterBackground,e.text)}var o=e.line.gutterMarkers;if(t.options.lineNumbers||o){var s=Pn(e),l=e.gutter=N("div",null,"CodeMirror-gutter-wrapper","left: "+(t.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px");if(l.setAttribute("aria-hidden","true"),t.display.input.setUneditable(l),s.insertBefore(l,e.text),e.line.gutterClass&&(l.className+=" "+e.line.gutterClass),!t.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(e.lineNumber=l.appendChild(N("div",le(t.options,n),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+r.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+t.display.lineNumInnerWidth+"px"))),o)for(var a=0;a<t.display.gutterSpecs.length;++a){var c=t.display.gutterSpecs[a].className,h=o.hasOwnProperty(c)&&o[c];h&&l.appendChild(N("div",[h],"CodeMirror-gutter-elt","left: "+r.gutterLeft[c]+"px; width: "+r.gutterWidth[c]+"px"))}}}function Bn(t,e,n){e.alignable&&(e.alignable=null);for(var r=S("CodeMirror-linewidget"),i=e.node.firstChild,o=void 0;i;i=o)o=i.nextSibling,r.test(i.className)&&e.node.removeChild(i);$n(t,e,n)}function Vn(t,e,n,r){var i=zn(t,e);return e.text=e.node=i.pre,i.bgClass&&(e.bgClass=i.bgClass),i.textClass&&(e.textClass=i.textClass),Fn(t,e),In(t,e,n,r),$n(t,e,r),e.node}function $n(t,e,n){if(Un(t,e.line,e,n,!0),e.rest)for(var r=0;r<e.rest.length;r++)Un(t,e.rest[r],e,n,!1)}function Un(t,e,n,r,i){if(e.widgets)for(var o=Pn(n),s=0,l=e.widgets;s<l.length;++s){var a=l[s],c=N("div",[a.node],"CodeMirror-linewidget"+(a.className?" "+a.className:""));a.handleMouseEvents||c.setAttribute("cm-ignore-events","true"),Gn(a,c,n,r),t.display.input.setUneditable(c),i&&a.above?o.insertBefore(c,n.gutter||n.text):o.appendChild(c),An(a,"redraw")}}function Gn(t,e,n,r){if(t.noHScroll){(n.alignable||(n.alignable=[])).push(e);var i=r.wrapperWidth;e.style.left=r.fixedPos+"px",t.coverGutter||(i-=r.gutterTotalWidth,e.style.paddingLeft=r.gutterTotalWidth+"px"),e.style.width=i+"px"}t.coverGutter&&(e.style.zIndex=5,e.style.position="relative",t.noHScroll||(e.style.marginLeft=-r.gutterTotalWidth+"px"))}function jn(t){if(null!=t.height)return t.height;var e=t.doc.cm;if(!e)return 0;if(!A(document.body,t.node)){var n="position: relative;";t.coverGutter&&(n+="margin-left: -"+e.display.gutters.offsetWidth+"px;"),t.noHScroll&&(n+="width: "+e.display.wrapper.clientWidth+"px;"),O(e.display.measure,N("div",[t.node],null,n))}return t.height=t.node.parentNode.offsetHeight}function Kn(t,e){for(var n=Ot(e);n!=t.wrapper;n=n.parentNode)if(!n||1==n.nodeType&&"true"==n.getAttribute("cm-ignore-events")||n.parentNode==t.sizer&&n!=t.mover)return!0}function qn(t){return t.lineSpace.offsetTop}function Xn(t){return t.mover.offsetHeight-t.lineSpace.offsetHeight}function Yn(t){if(t.cachedPaddingH)return t.cachedPaddingH;var e=O(t.measure,N("pre","x","CodeMirror-line-like")),n=window.getComputedStyle?window.getComputedStyle(e):e.currentStyle,r={left:parseInt(n.paddingLeft),right:parseInt(n.paddingRight)};return isNaN(r.left)||isNaN(r.right)||(t.cachedPaddingH=r),r}function Zn(t){return U-t.display.nativeBarWidth}function Jn(t){return t.display.scroller.clientWidth-Zn(t)-t.display.barWidth}function Qn(t){return t.display.scroller.clientHeight-Zn(t)-t.display.barHeight}function tr(t,e,n){var r=t.options.lineWrapping,i=r&&Jn(t);if(!e.measure.heights||r&&e.measure.width!=i){var o=e.measure.heights=[];if(r){e.measure.width=i;for(var s=e.text.firstChild.getClientRects(),l=0;l<s.length-1;l++){var a=s[l],c=s[l+1];Math.abs(a.bottom-c.bottom)>2&&o.push((a.bottom+c.top)/2-n.top)}}o.push(n.bottom-n.top)}}function er(t,e,n){if(t.line==e)return{map:t.measure.map,cache:t.measure.cache};if(t.rest){for(var r=0;r<t.rest.length;r++)if(t.rest[r]==e)return{map:t.measure.maps[r],cache:t.measure.caches[r]};for(var i=0;i<t.rest.length;i++)if(ie(t.rest[i])>n)return{map:t.measure.maps[i],cache:t.measure.caches[i],before:!0}}}function nr(t,e){var n=ie(e=en(e)),r=t.display.externalMeasured=new Sn(t.doc,e,n);r.lineN=n;var i=r.built=yn(t,r);return r.text=i.pre,O(t.display.lineMeasure,i.pre),r}function rr(t,e,n,r){return sr(t,or(t,e),n,r)}function ir(t,e){if(e>=t.display.viewFrom&&e<t.display.viewTo)return t.display.view[Fr(t,e)];var n=t.display.externalMeasured;return n&&e>=n.lineN&&e<n.lineN+n.size?n:void 0}function or(t,e){var n=ie(e),r=ir(t,n);r&&!r.text?r=null:r&&r.changes&&(Wn(t,r,n,Wr(t)),t.curOp.forceUpdate=!0),r||(r=nr(t,e));var i=er(r,e,n);return{line:e,view:r,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function sr(t,e,n,r,i){e.before&&(n=-1);var o,s=n+(r||"");return e.cache.hasOwnProperty(s)?o=e.cache[s]:(e.rect||(e.rect=e.view.text.getBoundingClientRect()),e.hasHeights||(tr(t,e.view,e.rect),e.hasHeights=!0),(o=ur(t,e,n,r)).bogus||(e.cache[s]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var lr,ar={left:0,right:0,top:0,bottom:0};function cr(t,e,n){for(var r,i,o,s,l,a,c=0;c<t.length;c+=3)if(l=t[c],a=t[c+1],e<l?(i=0,o=1,s="left"):e<a?o=1+(i=e-l):(c==t.length-3||e==a&&t[c+3]>e)&&(i=(o=a-l)-1,e>=a&&(s="right")),null!=i){if(r=t[c+2],l==a&&n==(r.insertLeft?"left":"right")&&(s=n),"left"==n&&0==i)for(;c&&t[c-2]==t[c-3]&&t[c-1].insertLeft;)r=t[2+(c-=3)],s="left";if("right"==n&&i==a-l)for(;c<t.length-3&&t[c+3]==t[c+4]&&!t[c+5].insertLeft;)r=t[(c+=3)+2],s="right";break}return{node:r,start:i,end:o,collapse:s,coverStart:l,coverEnd:a}}function hr(t,e){var n=ar;if("left"==e)for(var r=0;r<t.length&&(n=t[r]).left==n.right;r++);else for(var i=t.length-1;i>=0&&(n=t[i]).left==n.right;i--);return n}function ur(t,e,n,r){var i,o=cr(e.map,n,r),a=o.node,c=o.start,h=o.end,u=o.collapse;if(3==a.nodeType){for(var d=0;d<4;d++){for(;c&&at(e.line.text.charAt(o.coverStart+c));)--c;for(;o.coverStart+h<o.coverEnd&&at(e.line.text.charAt(o.coverStart+h));)++h;if((i=s&&l<9&&0==c&&h==o.coverEnd-o.coverStart?a.parentNode.getBoundingClientRect():hr(L(a,c,h).getClientRects(),r)).left||i.right||0==c)break;h=c,c-=1,u="right"}s&&l<11&&(i=dr(t.display.measure,i))}else{var f;c>0&&(u=r="right"),i=t.options.lineWrapping&&(f=a.getClientRects()).length>1?f["right"==r?f.length-1:0]:a.getBoundingClientRect()}if(s&&l<9&&!c&&(!i||!i.left&&!i.right)){var p=a.parentNode.getClientRects()[0];i=p?{left:p.left,right:p.left+Er(t.display),top:p.top,bottom:p.bottom}:ar}for(var g=i.top-e.rect.top,m=i.bottom-e.rect.top,v=(g+m)/2,y=e.view.measure.heights,b=0;b<y.length-1&&!(v<y[b]);b++);var w=b?y[b-1]:0,x=y[b],_={left:("right"==u?i.right:i.left)-e.rect.left,right:("left"==u?i.left:i.right)-e.rect.left,top:w,bottom:x};return i.left||i.right||(_.bogus=!0),t.options.singleCursorHeightPerLine||(_.rtop=g,_.rbottom=m),_}function dr(t,e){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!Bt(t))return e;var n=screen.logicalXDPI/screen.deviceXDPI,r=screen.logicalYDPI/screen.deviceYDPI;return{left:e.left*n,right:e.right*n,top:e.top*r,bottom:e.bottom*r}}function fr(t){if(t.measure&&(t.measure.cache={},t.measure.heights=null,t.rest))for(var e=0;e<t.rest.length;e++)t.measure.caches[e]={}}function pr(t){t.display.externalMeasure=null,T(t.display.lineMeasure);for(var e=0;e<t.display.view.length;e++)fr(t.display.view[e])}function gr(t){pr(t),t.display.cachedCharWidth=t.display.cachedTextHeight=t.display.cachedPaddingH=null,t.options.lineWrapping||(t.display.maxLineChanged=!0),t.display.lineNumChars=null}function mr(t){return h&&v?-(t.body.getBoundingClientRect().left-parseInt(getComputedStyle(t.body).marginLeft)):t.defaultView.pageXOffset||(t.documentElement||t.body).scrollLeft}function vr(t){return h&&v?-(t.body.getBoundingClientRect().top-parseInt(getComputedStyle(t.body).marginTop)):t.defaultView.pageYOffset||(t.documentElement||t.body).scrollTop}function yr(t){var e=en(t).widgets,n=0;if(e)for(var r=0;r<e.length;++r)e[r].above&&(n+=jn(e[r]));return n}function br(t,e,n,r,i){if(!i){var o=yr(e);n.top+=o,n.bottom+=o}if("line"==r)return n;r||(r="local");var s=cn(e);if("local"==r?s+=qn(t.display):s-=t.display.viewOffset,"page"==r||"window"==r){var l=t.display.lineSpace.getBoundingClientRect();s+=l.top+("window"==r?0:vr(z(t)));var a=l.left+("window"==r?0:mr(z(t)));n.left+=a,n.right+=a}return n.top+=s,n.bottom+=s,n}function wr(t,e,n){if("div"==n)return e;var r=e.left,i=e.top;if("page"==n)r-=mr(z(t)),i-=vr(z(t));else if("local"==n||!n){var o=t.display.sizer.getBoundingClientRect();r+=o.left,i+=o.top}var s=t.display.lineSpace.getBoundingClientRect();return{left:r-s.left,top:i-s.top}}function xr(t,e,n,r,i){return r||(r=te(t.doc,e.line)),br(t,r,rr(t,r,e.ch,i),n)}function _r(t,e,n,r,i,o){function s(e,s){var l=sr(t,i,e,s?"right":"left",o);return s?l.left=l.right:l.right=l.left,br(t,r,l,n)}r=r||te(t.doc,e.line),i||(i=or(t,r));var l=gt(r,t.doc.direction),a=e.ch,c=e.sticky;if(a>=r.text.length?(a=r.text.length,c="before"):a<=0&&(a=0,c="after"),!l)return s("before"==c?a-1:a,"before"==c);function h(t,e,n){return s(n?t-1:t,1==l[e].level!=n)}var u=ft(l,a,c),d=dt,f=h(a,u,"before"==c);return null!=d&&(f.other=h(a,d,"before"!=c)),f}function Cr(t,e){var n=0;e=ge(t.doc,e),t.options.lineWrapping||(n=Er(t.display)*e.ch);var r=te(t.doc,e.line),i=cn(r)+qn(t.display);return{left:n,right:n,top:i,bottom:i+r.height}}function kr(t,e,n,r,i){var o=ae(t,e,n);return o.xRel=i,r&&(o.outside=r),o}function Sr(t,e,n){var r=t.doc;if((n+=t.display.viewOffset)<0)return kr(r.first,0,null,-1,-1);var i=oe(r,n),o=r.first+r.size-1;if(i>o)return kr(r.first+r.size-1,te(r,o).text.length,null,1,1);e<0&&(e=0);for(var s=te(r,i);;){var l=Or(t,s,i,e,n),a=Qe(s,l.ch+(l.xRel>0||l.outside>0?1:0));if(!a)return l;var c=a.find(1);if(c.line==i)return c;s=te(r,i=c.line)}}function Lr(t,e,n,r){r-=yr(e);var i=e.text.length,o=ht((function(e){return sr(t,n,e-1).bottom<=r}),i,0);return{begin:o,end:i=ht((function(e){return sr(t,n,e).top>r}),o,i)}}function Mr(t,e,n,r){return n||(n=or(t,e)),Lr(t,e,n,br(t,e,sr(t,n,r),"line").top)}function Tr(t,e,n,r){return!(t.bottom<=n)&&(t.top>n||(r?t.left:t.right)>e)}function Or(t,e,n,r,i){i-=cn(e);var o=or(t,e),s=yr(e),l=0,a=e.text.length,c=!0,h=gt(e,t.doc.direction);if(h){var u=(t.options.lineWrapping?Dr:Nr)(t,e,n,o,h,r,i);l=(c=1!=u.level)?u.from:u.to-1,a=c?u.to:u.from-1}var d,f,p=null,g=null,m=ht((function(e){var n=sr(t,o,e);return n.top+=s,n.bottom+=s,!!Tr(n,r,i,!1)&&(n.top<=i&&n.left<=r&&(p=e,g=n),!0)}),l,a),v=!1;if(g){var y=r-g.left<g.right-r,b=y==c;m=p+(b?0:1),f=b?"after":"before",d=y?g.left:g.right}else{c||m!=a&&m!=l||m++,f=0==m?"after":m==e.text.length?"before":sr(t,o,m-(c?1:0)).bottom+s<=i==c?"after":"before";var w=_r(t,ae(n,m,f),"line",e,o);d=w.left,v=i<w.top?-1:i>=w.bottom?1:0}return kr(n,m=ct(e.text,m,1),f,v,r-d)}function Nr(t,e,n,r,i,o,s){var l=ht((function(l){var a=i[l],c=1!=a.level;return Tr(_r(t,ae(n,c?a.to:a.from,c?"before":"after"),"line",e,r),o,s,!0)}),0,i.length-1),a=i[l];if(l>0){var c=1!=a.level,h=_r(t,ae(n,c?a.from:a.to,c?"after":"before"),"line",e,r);Tr(h,o,s,!0)&&h.top>s&&(a=i[l-1])}return a}function Dr(t,e,n,r,i,o,s){var l=Lr(t,e,r,s),a=l.begin,c=l.end;/\s/.test(e.text.charAt(c-1))&&c--;for(var h=null,u=null,d=0;d<i.length;d++){var f=i[d];if(!(f.from>=c||f.to<=a)){var p=sr(t,r,1!=f.level?Math.min(c,f.to)-1:Math.max(a,f.from)).right,g=p<o?o-p+1e9:p-o;(!h||u>g)&&(h=f,u=g)}}return h||(h=i[i.length-1]),h.from<a&&(h={from:a,to:h.to,level:h.level}),h.to>c&&(h={from:h.from,to:c,level:h.level}),h}function Ar(t){if(null!=t.cachedTextHeight)return t.cachedTextHeight;if(null==lr){lr=N("pre",null,"CodeMirror-line-like");for(var e=0;e<49;++e)lr.appendChild(document.createTextNode("x")),lr.appendChild(N("br"));lr.appendChild(document.createTextNode("x"))}O(t.measure,lr);var n=lr.offsetHeight/50;return n>3&&(t.cachedTextHeight=n),T(t.measure),n||1}function Er(t){if(null!=t.cachedCharWidth)return t.cachedCharWidth;var e=N("span","xxxxxxxxxx"),n=N("pre",[e],"CodeMirror-line-like");O(t.measure,n);var r=e.getBoundingClientRect(),i=(r.right-r.left)/10;return i>2&&(t.cachedCharWidth=i),i||10}function Wr(t){for(var e=t.display,n={},r={},i=e.gutters.clientLeft,o=e.gutters.firstChild,s=0;o;o=o.nextSibling,++s){var l=t.display.gutterSpecs[s].className;n[l]=o.offsetLeft+o.clientLeft+i,r[l]=o.clientWidth}return{fixedPos:Pr(e),gutterTotalWidth:e.gutters.offsetWidth,gutterLeft:n,gutterWidth:r,wrapperWidth:e.wrapper.clientWidth}}function Pr(t){return t.scroller.getBoundingClientRect().left-t.sizer.getBoundingClientRect().left}function Hr(t){var e=Ar(t.display),n=t.options.lineWrapping,r=n&&Math.max(5,t.display.scroller.clientWidth/Er(t.display)-3);return function(i){if(ln(t.doc,i))return 0;var o=0;if(i.widgets)for(var s=0;s<i.widgets.length;s++)i.widgets[s].height&&(o+=i.widgets[s].height);return n?o+(Math.ceil(i.text.length/r)||1)*e:o+e}}function zr(t){var e=t.doc,n=Hr(t);e.iter((function(t){var e=n(t);e!=t.height&&re(t,e)}))}function Rr(t,e,n,r){var i=t.display;if(!n&&"true"==Ot(e).getAttribute("cm-not-content"))return null;var o,s,l=i.lineSpace.getBoundingClientRect();try{o=e.clientX-l.left,s=e.clientY-l.top}catch(t){return null}var a,c=Sr(t,o,s);if(r&&c.xRel>0&&(a=te(t.doc,c.line).text).length==c.ch){var h=B(a,a.length,t.options.tabSize)-a.length;c=ae(c.line,Math.max(0,Math.round((o-Yn(t.display).left)/Er(t.display))-h))}return c}function Fr(t,e){if(e>=t.display.viewTo)return null;if((e-=t.display.viewFrom)<0)return null;for(var n=t.display.view,r=0;r<n.length;r++)if((e-=n[r].size)<0)return r}function Ir(t,e,n,r){null==e&&(e=t.doc.first),null==n&&(n=t.doc.first+t.doc.size),r||(r=0);var i=t.display;if(r&&n<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>e)&&(i.updateLineNumbers=e),t.curOp.viewChanged=!0,e>=i.viewTo)Ee&&on(t.doc,e)<i.viewTo&&Vr(t);else if(n<=i.viewFrom)Ee&&sn(t.doc,n+r)>i.viewFrom?Vr(t):(i.viewFrom+=r,i.viewTo+=r);else if(e<=i.viewFrom&&n>=i.viewTo)Vr(t);else if(e<=i.viewFrom){var o=$r(t,n,n+r,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=r):Vr(t)}else if(n>=i.viewTo){var s=$r(t,e,e,-1);s?(i.view=i.view.slice(0,s.index),i.viewTo=s.lineN):Vr(t)}else{var l=$r(t,e,e,-1),a=$r(t,n,n+r,1);l&&a?(i.view=i.view.slice(0,l.index).concat(Ln(t,l.lineN,a.lineN)).concat(i.view.slice(a.index)),i.viewTo+=r):Vr(t)}var c=i.externalMeasured;c&&(n<c.lineN?c.lineN+=r:e<c.lineN+c.size&&(i.externalMeasured=null))}function Br(t,e,n){t.curOp.viewChanged=!0;var r=t.display,i=t.display.externalMeasured;if(i&&e>=i.lineN&&e<i.lineN+i.size&&(r.externalMeasured=null),!(e<r.viewFrom||e>=r.viewTo)){var o=r.view[Fr(t,e)];if(null!=o.node){var s=o.changes||(o.changes=[]);-1==$(s,n)&&s.push(n)}}}function Vr(t){t.display.viewFrom=t.display.viewTo=t.doc.first,t.display.view=[],t.display.viewOffset=0}function $r(t,e,n,r){var i,o=Fr(t,e),s=t.display.view;if(!Ee||n==t.doc.first+t.doc.size)return{index:o,lineN:n};for(var l=t.display.viewFrom,a=0;a<o;a++)l+=s[a].size;if(l!=e){if(r>0){if(o==s.length-1)return null;i=l+s[o].size-e,o++}else i=l-e;e+=i,n+=i}for(;on(t.doc,n)!=n;){if(o==(r<0?0:s.length-1))return null;n+=r*s[o-(r<0?1:0)].size,o+=r}return{index:o,lineN:n}}function Ur(t,e,n){var r=t.display;0==r.view.length||e>=r.viewTo||n<=r.viewFrom?(r.view=Ln(t,e,n),r.viewFrom=e):(r.viewFrom>e?r.view=Ln(t,e,r.viewFrom).concat(r.view):r.viewFrom<e&&(r.view=r.view.slice(Fr(t,e))),r.viewFrom=e,r.viewTo<n?r.view=r.view.concat(Ln(t,r.viewTo,n)):r.viewTo>n&&(r.view=r.view.slice(0,Fr(t,n)))),r.viewTo=n}function Gr(t){for(var e=t.display.view,n=0,r=0;r<e.length;r++){var i=e[r];i.hidden||i.node&&!i.changes||++n}return n}function jr(t){t.display.input.showSelection(t.display.input.prepareSelection())}function Kr(t,e){void 0===e&&(e=!0);var n=t.doc,r={},i=r.cursors=document.createDocumentFragment(),o=r.selection=document.createDocumentFragment(),s=t.options.$customCursor;s&&(e=!0);for(var l=0;l<n.sel.ranges.length;l++)if(e||l!=n.sel.primIndex){var a=n.sel.ranges[l];if(!(a.from().line>=t.display.viewTo||a.to().line<t.display.viewFrom)){var c=a.empty();if(s){var h=s(t,a);h&&qr(t,h,i)}else(c||t.options.showCursorWhenSelecting)&&qr(t,a.head,i);c||Yr(t,a,o)}}return r}function qr(t,e,n){var r=_r(t,e,"div",null,null,!t.options.singleCursorHeightPerLine),i=n.appendChild(N("div"," ","CodeMirror-cursor"));if(i.style.left=r.left+"px",i.style.top=r.top+"px",i.style.height=Math.max(0,r.bottom-r.top)*t.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(t.getWrapperElement().className)){var o=xr(t,e,"div",null,null),s=o.right-o.left;i.style.width=(s>0?s:t.defaultCharWidth())+"px"}if(r.other){var l=n.appendChild(N("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));l.style.display="",l.style.left=r.other.left+"px",l.style.top=r.other.top+"px",l.style.height=.85*(r.other.bottom-r.other.top)+"px"}}function Xr(t,e){return t.top-e.top||t.left-e.left}function Yr(t,e,n){var r=t.display,i=t.doc,o=document.createDocumentFragment(),s=Yn(t.display),l=s.left,a=Math.max(r.sizerWidth,Jn(t)-r.sizer.offsetLeft)-s.right,c="ltr"==i.direction;function h(t,e,n,r){e<0&&(e=0),e=Math.round(e),r=Math.round(r),o.appendChild(N("div",null,"CodeMirror-selected","position: absolute; left: "+t+"px;\n                             top: "+e+"px; width: "+(null==n?a-t:n)+"px;\n                             height: "+(r-e)+"px"))}function u(e,n,r){var o,s,u=te(i,e),d=u.text.length;function f(n,r){return xr(t,ae(e,n),"div",u,r)}function p(e,n,r){var i=Mr(t,u,null,e),o="ltr"==n==("after"==r)?"left":"right";return f("after"==r?i.begin:i.end-(/\s/.test(u.text.charAt(i.end-1))?2:1),o)[o]}var g=gt(u,i.direction);return ut(g,n||0,null==r?d:r,(function(t,e,i,u){var m="ltr"==i,v=f(t,m?"left":"right"),y=f(e-1,m?"right":"left"),b=null==n&&0==t,w=null==r&&e==d,x=0==u,_=!g||u==g.length-1;if(y.top-v.top<=3){var C=(c?w:b)&&_,k=(c?b:w)&&x?l:(m?v:y).left,S=C?a:(m?y:v).right;h(k,v.top,S-k,v.bottom)}else{var L,M,T,O;m?(L=c&&b&&x?l:v.left,M=c?a:p(t,i,"before"),T=c?l:p(e,i,"after"),O=c&&w&&_?a:y.right):(L=c?p(t,i,"before"):l,M=!c&&b&&x?a:v.right,T=!c&&w&&_?l:y.left,O=c?p(e,i,"after"):a),h(L,v.top,M-L,v.bottom),v.bottom<y.top&&h(l,v.bottom,null,y.top),h(T,y.top,O-T,y.bottom)}(!o||Xr(v,o)<0)&&(o=v),Xr(y,o)<0&&(o=y),(!s||Xr(v,s)<0)&&(s=v),Xr(y,s)<0&&(s=y)})),{start:o,end:s}}var d=e.from(),f=e.to();if(d.line==f.line)u(d.line,d.ch,f.ch);else{var p=te(i,d.line),g=te(i,f.line),m=en(p)==en(g),v=u(d.line,d.ch,m?p.text.length+1:null).end,y=u(f.line,m?0:null,f.ch).start;m&&(v.top<y.top-2?(h(v.right,v.top,null,v.bottom),h(l,y.top,y.left,y.bottom)):h(v.right,v.top,y.left-v.right,v.bottom)),v.bottom<y.top&&h(l,v.bottom,null,y.top)}n.appendChild(o)}function Zr(t){if(t.state.focused){var e=t.display;clearInterval(e.blinker);var n=!0;e.cursorDiv.style.visibility="",t.options.cursorBlinkRate>0?e.blinker=setInterval((function(){t.hasFocus()||ei(t),e.cursorDiv.style.visibility=(n=!n)?"":"hidden"}),t.options.cursorBlinkRate):t.options.cursorBlinkRate<0&&(e.cursorDiv.style.visibility="hidden")}}function Jr(t){t.hasFocus()||(t.display.input.focus(),t.state.focused||ti(t))}function Qr(t){t.state.delayingBlurEvent=!0,setTimeout((function(){t.state.delayingBlurEvent&&(t.state.delayingBlurEvent=!1,t.state.focused&&ei(t))}),100)}function ti(t,e){t.state.delayingBlurEvent&&!t.state.draggingText&&(t.state.delayingBlurEvent=!1),"nocursor"!=t.options.readOnly&&(t.state.focused||(wt(t,"focus",t,e),t.state.focused=!0,W(t.display.wrapper,"CodeMirror-focused"),t.curOp||t.display.selForContextMenu==t.doc.sel||(t.display.input.reset(),a&&setTimeout((function(){return t.display.input.reset(!0)}),20)),t.display.input.receivedFocus()),Zr(t))}function ei(t,e){t.state.delayingBlurEvent||(t.state.focused&&(wt(t,"blur",t,e),t.state.focused=!1,M(t.display.wrapper,"CodeMirror-focused")),clearInterval(t.display.blinker),setTimeout((function(){t.state.focused||(t.display.shift=!1)}),150))}function ni(t){for(var e=t.display,n=e.lineDiv.offsetTop,r=Math.max(0,e.scroller.getBoundingClientRect().top),i=e.lineDiv.getBoundingClientRect().top,o=0,a=0;a<e.view.length;a++){var c=e.view[a],h=t.options.lineWrapping,u=void 0,d=0;if(!c.hidden){if(i+=c.line.height,s&&l<8){var f=c.node.offsetTop+c.node.offsetHeight;u=f-n,n=f}else{var p=c.node.getBoundingClientRect();u=p.bottom-p.top,!h&&c.text.firstChild&&(d=c.text.firstChild.getBoundingClientRect().right-p.left-1)}var g=c.line.height-u;if((g>.005||g<-.005)&&(i<r&&(o-=g),re(c.line,u),ri(c.line),c.rest))for(var m=0;m<c.rest.length;m++)ri(c.rest[m]);if(d>t.display.sizerWidth){var v=Math.ceil(d/Er(t.display));v>t.display.maxLineLength&&(t.display.maxLineLength=v,t.display.maxLine=c.line,t.display.maxLineChanged=!0)}}}Math.abs(o)>2&&(e.scroller.scrollTop+=o)}function ri(t){if(t.widgets)for(var e=0;e<t.widgets.length;++e){var n=t.widgets[e],r=n.node.parentNode;r&&(n.height=r.offsetHeight)}}function ii(t,e,n){var r=n&&null!=n.top?Math.max(0,n.top):t.scroller.scrollTop;r=Math.floor(r-qn(t));var i=n&&null!=n.bottom?n.bottom:r+t.wrapper.clientHeight,o=oe(e,r),s=oe(e,i);if(n&&n.ensure){var l=n.ensure.from.line,a=n.ensure.to.line;l<o?(o=l,s=oe(e,cn(te(e,l))+t.wrapper.clientHeight)):Math.min(a,e.lastLine())>=s&&(o=oe(e,cn(te(e,a))-t.wrapper.clientHeight),s=a)}return{from:o,to:Math.max(s,o+1)}}function oi(t,e){if(!xt(t,"scrollCursorIntoView")){var n=t.display,r=n.sizer.getBoundingClientRect(),i=null,o=n.wrapper.ownerDocument;if(e.top+r.top<0?i=!0:e.bottom+r.top>(o.defaultView.innerHeight||o.documentElement.clientHeight)&&(i=!1),null!=i&&!g){var s=N("div","​",null,"position: absolute;\n                         top: "+(e.top-n.viewOffset-qn(t.display))+"px;\n                         height: "+(e.bottom-e.top+Zn(t)+n.barHeight)+"px;\n                         left: "+e.left+"px; width: "+Math.max(2,e.right-e.left)+"px;");t.display.lineSpace.appendChild(s),s.scrollIntoView(i),t.display.lineSpace.removeChild(s)}}}function si(t,e,n,r){var i;null==r&&(r=0),t.options.lineWrapping||e!=n||(n="before"==e.sticky?ae(e.line,e.ch+1,"before"):e,e=e.ch?ae(e.line,"before"==e.sticky?e.ch-1:e.ch,"after"):e);for(var o=0;o<5;o++){var s=!1,l=_r(t,e),a=n&&n!=e?_r(t,n):l,c=ai(t,i={left:Math.min(l.left,a.left),top:Math.min(l.top,a.top)-r,right:Math.max(l.left,a.left),bottom:Math.max(l.bottom,a.bottom)+r}),h=t.doc.scrollTop,u=t.doc.scrollLeft;if(null!=c.scrollTop&&(gi(t,c.scrollTop),Math.abs(t.doc.scrollTop-h)>1&&(s=!0)),null!=c.scrollLeft&&(vi(t,c.scrollLeft),Math.abs(t.doc.scrollLeft-u)>1&&(s=!0)),!s)break}return i}function li(t,e){var n=ai(t,e);null!=n.scrollTop&&gi(t,n.scrollTop),null!=n.scrollLeft&&vi(t,n.scrollLeft)}function ai(t,e){var n=t.display,r=Ar(t.display);e.top<0&&(e.top=0);var i=t.curOp&&null!=t.curOp.scrollTop?t.curOp.scrollTop:n.scroller.scrollTop,o=Qn(t),s={};e.bottom-e.top>o&&(e.bottom=e.top+o);var l=t.doc.height+Xn(n),a=e.top<r,c=e.bottom>l-r;if(e.top<i)s.scrollTop=a?0:e.top;else if(e.bottom>i+o){var h=Math.min(e.top,(c?l:e.bottom)-o);h!=i&&(s.scrollTop=h)}var u=t.options.fixedGutter?0:n.gutters.offsetWidth,d=t.curOp&&null!=t.curOp.scrollLeft?t.curOp.scrollLeft:n.scroller.scrollLeft-u,f=Jn(t)-n.gutters.offsetWidth,p=e.right-e.left>f;return p&&(e.right=e.left+f),e.left<10?s.scrollLeft=0:e.left<d?s.scrollLeft=Math.max(0,e.left+u-(p?0:10)):e.right>f+d-3&&(s.scrollLeft=e.right+(p?0:10)-f),s}function ci(t,e){null!=e&&(fi(t),t.curOp.scrollTop=(null==t.curOp.scrollTop?t.doc.scrollTop:t.curOp.scrollTop)+e)}function hi(t){fi(t);var e=t.getCursor();t.curOp.scrollToPos={from:e,to:e,margin:t.options.cursorScrollMargin}}function ui(t,e,n){null==e&&null==n||fi(t),null!=e&&(t.curOp.scrollLeft=e),null!=n&&(t.curOp.scrollTop=n)}function di(t,e){fi(t),t.curOp.scrollToPos=e}function fi(t){var e=t.curOp.scrollToPos;e&&(t.curOp.scrollToPos=null,pi(t,Cr(t,e.from),Cr(t,e.to),e.margin))}function pi(t,e,n,r){var i=ai(t,{left:Math.min(e.left,n.left),top:Math.min(e.top,n.top)-r,right:Math.max(e.right,n.right),bottom:Math.max(e.bottom,n.bottom)+r});ui(t,i.scrollLeft,i.scrollTop)}function gi(t,e){Math.abs(t.doc.scrollTop-e)<2||(n||ji(t,{top:e}),mi(t,e,!0),n&&ji(t),Ri(t,100))}function mi(t,e,n){e=Math.max(0,Math.min(t.display.scroller.scrollHeight-t.display.scroller.clientHeight,e)),(t.display.scroller.scrollTop!=e||n)&&(t.doc.scrollTop=e,t.display.scrollbars.setScrollTop(e),t.display.scroller.scrollTop!=e&&(t.display.scroller.scrollTop=e))}function vi(t,e,n,r){e=Math.max(0,Math.min(e,t.display.scroller.scrollWidth-t.display.scroller.clientWidth)),(n?e==t.doc.scrollLeft:Math.abs(t.doc.scrollLeft-e)<2)&&!r||(t.doc.scrollLeft=e,Yi(t),t.display.scroller.scrollLeft!=e&&(t.display.scroller.scrollLeft=e),t.display.scrollbars.setScrollLeft(e))}function yi(t){var e=t.display,n=e.gutters.offsetWidth,r=Math.round(t.doc.height+Xn(t.display));return{clientHeight:e.scroller.clientHeight,viewHeight:e.wrapper.clientHeight,scrollWidth:e.scroller.scrollWidth,clientWidth:e.scroller.clientWidth,viewWidth:e.wrapper.clientWidth,barLeft:t.options.fixedGutter?n:0,docHeight:r,scrollHeight:r+Zn(t)+e.barHeight,nativeBarWidth:e.nativeBarWidth,gutterWidth:n}}var bi=function(t,e,n){this.cm=n;var r=this.vert=N("div",[N("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=N("div",[N("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");r.tabIndex=i.tabIndex=-1,t(r),t(i),vt(r,"scroll",(function(){r.clientHeight&&e(r.scrollTop,"vertical")})),vt(i,"scroll",(function(){i.clientWidth&&e(i.scrollLeft,"horizontal")})),this.checkedZeroWidth=!1,s&&l<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};bi.prototype.update=function(t){var e=t.scrollWidth>t.clientWidth+1,n=t.scrollHeight>t.clientHeight+1,r=t.nativeBarWidth;if(n){this.vert.style.display="block",this.vert.style.bottom=e?r+"px":"0";var i=t.viewHeight-(e?r:0);this.vert.firstChild.style.height=Math.max(0,t.scrollHeight-t.clientHeight+i)+"px"}else this.vert.scrollTop=0,this.vert.style.display="",this.vert.firstChild.style.height="0";if(e){this.horiz.style.display="block",this.horiz.style.right=n?r+"px":"0",this.horiz.style.left=t.barLeft+"px";var o=t.viewWidth-t.barLeft-(n?r:0);this.horiz.firstChild.style.width=Math.max(0,t.scrollWidth-t.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&t.clientHeight>0&&(0==r&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:n?r:0,bottom:e?r:0}},bi.prototype.setScrollLeft=function(t){this.horiz.scrollLeft!=t&&(this.horiz.scrollLeft=t),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},bi.prototype.setScrollTop=function(t){this.vert.scrollTop!=t&&(this.vert.scrollTop=t),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},bi.prototype.zeroWidthHack=function(){var t=b&&!p?"12px":"18px";this.horiz.style.height=this.vert.style.width=t,this.horiz.style.visibility=this.vert.style.visibility="hidden",this.disableHoriz=new V,this.disableVert=new V},bi.prototype.enableZeroWidthBar=function(t,e,n){function r(){var i=t.getBoundingClientRect();("vert"==n?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1))!=t?t.style.visibility="hidden":e.set(1e3,r)}t.style.visibility="",e.set(1e3,r)},bi.prototype.clear=function(){var t=this.horiz.parentNode;t.removeChild(this.horiz),t.removeChild(this.vert)};var wi=function(){};function xi(t,e){e||(e=yi(t));var n=t.display.barWidth,r=t.display.barHeight;_i(t,e);for(var i=0;i<4&&n!=t.display.barWidth||r!=t.display.barHeight;i++)n!=t.display.barWidth&&t.options.lineWrapping&&ni(t),_i(t,yi(t)),n=t.display.barWidth,r=t.display.barHeight}function _i(t,e){var n=t.display,r=n.scrollbars.update(e);n.sizer.style.paddingRight=(n.barWidth=r.right)+"px",n.sizer.style.paddingBottom=(n.barHeight=r.bottom)+"px",n.heightForcer.style.borderBottom=r.bottom+"px solid transparent",r.right&&r.bottom?(n.scrollbarFiller.style.display="block",n.scrollbarFiller.style.height=r.bottom+"px",n.scrollbarFiller.style.width=r.right+"px"):n.scrollbarFiller.style.display="",r.bottom&&t.options.coverGutterNextToScrollbar&&t.options.fixedGutter?(n.gutterFiller.style.display="block",n.gutterFiller.style.height=r.bottom+"px",n.gutterFiller.style.width=e.gutterWidth+"px"):n.gutterFiller.style.display=""}wi.prototype.update=function(){return{bottom:0,right:0}},wi.prototype.setScrollLeft=function(){},wi.prototype.setScrollTop=function(){},wi.prototype.clear=function(){};var Ci={native:bi,null:wi};function ki(t){t.display.scrollbars&&(t.display.scrollbars.clear(),t.display.scrollbars.addClass&&M(t.display.wrapper,t.display.scrollbars.addClass)),t.display.scrollbars=new Ci[t.options.scrollbarStyle]((function(e){t.display.wrapper.insertBefore(e,t.display.scrollbarFiller),vt(e,"mousedown",(function(){t.state.focused&&setTimeout((function(){return t.display.input.focus()}),0)})),e.setAttribute("cm-not-content","true")}),(function(e,n){"horizontal"==n?vi(t,e):gi(t,e)}),t),t.display.scrollbars.addClass&&W(t.display.wrapper,t.display.scrollbars.addClass)}var Si=0;function Li(t){t.curOp={cm:t,viewChanged:!1,startHeight:t.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++Si,markArrays:null},Tn(t.curOp)}function Mi(t){var e=t.curOp;e&&Nn(e,(function(t){for(var e=0;e<t.ops.length;e++)t.ops[e].cm.curOp=null;Ti(t)}))}function Ti(t){for(var e=t.ops,n=0;n<e.length;n++)Oi(e[n]);for(var r=0;r<e.length;r++)Ni(e[r]);for(var i=0;i<e.length;i++)Di(e[i]);for(var o=0;o<e.length;o++)Ai(e[o]);for(var s=0;s<e.length;s++)Ei(e[s])}function Oi(t){var e=t.cm,n=e.display;Bi(e),t.updateMaxLine&&un(e),t.mustUpdate=t.viewChanged||t.forceUpdate||null!=t.scrollTop||t.scrollToPos&&(t.scrollToPos.from.line<n.viewFrom||t.scrollToPos.to.line>=n.viewTo)||n.maxLineChanged&&e.options.lineWrapping,t.update=t.mustUpdate&&new Ii(e,t.mustUpdate&&{top:t.scrollTop,ensure:t.scrollToPos},t.forceUpdate)}function Ni(t){t.updatedDisplay=t.mustUpdate&&Ui(t.cm,t.update)}function Di(t){var e=t.cm,n=e.display;t.updatedDisplay&&ni(e),t.barMeasure=yi(e),n.maxLineChanged&&!e.options.lineWrapping&&(t.adjustWidthTo=rr(e,n.maxLine,n.maxLine.text.length).left+3,e.display.sizerWidth=t.adjustWidthTo,t.barMeasure.scrollWidth=Math.max(n.scroller.clientWidth,n.sizer.offsetLeft+t.adjustWidthTo+Zn(e)+e.display.barWidth),t.maxScrollLeft=Math.max(0,n.sizer.offsetLeft+t.adjustWidthTo-Jn(e))),(t.updatedDisplay||t.selectionChanged)&&(t.preparedSelection=n.input.prepareSelection())}function Ai(t){var e=t.cm;null!=t.adjustWidthTo&&(e.display.sizer.style.minWidth=t.adjustWidthTo+"px",t.maxScrollLeft<e.doc.scrollLeft&&vi(e,Math.min(e.display.scroller.scrollLeft,t.maxScrollLeft),!0),e.display.maxLineChanged=!1);var n=t.focus&&t.focus==E(z(e));t.preparedSelection&&e.display.input.showSelection(t.preparedSelection,n),(t.updatedDisplay||t.startHeight!=e.doc.height)&&xi(e,t.barMeasure),t.updatedDisplay&&Xi(e,t.barMeasure),t.selectionChanged&&Zr(e),e.state.focused&&t.updateInput&&e.display.input.reset(t.typing),n&&Jr(t.cm)}function Ei(t){var e=t.cm,n=e.display,r=e.doc;t.updatedDisplay&&Gi(e,t.update),null==n.wheelStartX||null==t.scrollTop&&null==t.scrollLeft&&!t.scrollToPos||(n.wheelStartX=n.wheelStartY=null),null!=t.scrollTop&&mi(e,t.scrollTop,t.forceScroll),null!=t.scrollLeft&&vi(e,t.scrollLeft,!0,!0),t.scrollToPos&&oi(e,si(e,ge(r,t.scrollToPos.from),ge(r,t.scrollToPos.to),t.scrollToPos.margin));var i=t.maybeHiddenMarkers,o=t.maybeUnhiddenMarkers;if(i)for(var s=0;s<i.length;++s)i[s].lines.length||wt(i[s],"hide");if(o)for(var l=0;l<o.length;++l)o[l].lines.length&&wt(o[l],"unhide");n.wrapper.offsetHeight&&(r.scrollTop=e.display.scroller.scrollTop),t.changeObjs&&wt(e,"changes",e,t.changeObjs),t.update&&t.update.finish()}function Wi(t,e){if(t.curOp)return e();Li(t);try{return e()}finally{Mi(t)}}function Pi(t,e){return function(){if(t.curOp)return e.apply(t,arguments);Li(t);try{return e.apply(t,arguments)}finally{Mi(t)}}}function Hi(t){return function(){if(this.curOp)return t.apply(this,arguments);Li(this);try{return t.apply(this,arguments)}finally{Mi(this)}}}function zi(t){return function(){var e=this.cm;if(!e||e.curOp)return t.apply(this,arguments);Li(e);try{return t.apply(this,arguments)}finally{Mi(e)}}}function Ri(t,e){t.doc.highlightFrontier<t.display.viewTo&&t.state.highlight.set(e,F(Fi,t))}function Fi(t){var e=t.doc;if(!(e.highlightFrontier>=t.display.viewTo)){var n=+new Date+t.options.workTime,r=_e(t,e.highlightFrontier),i=[];e.iter(r.line,Math.min(e.first+e.size,t.display.viewTo+500),(function(o){if(r.line>=t.display.viewFrom){var s=o.styles,l=o.text.length>t.options.maxHighlightLength?Yt(e.mode,r.state):null,a=we(t,o,r,!0);l&&(r.state=l),o.styles=a.styles;var c=o.styleClasses,h=a.classes;h?o.styleClasses=h:c&&(o.styleClasses=null);for(var u=!s||s.length!=o.styles.length||c!=h&&(!c||!h||c.bgClass!=h.bgClass||c.textClass!=h.textClass),d=0;!u&&d<s.length;++d)u=s[d]!=o.styles[d];u&&i.push(r.line),o.stateAfter=r.save(),r.nextLine()}else o.text.length<=t.options.maxHighlightLength&&Ce(t,o.text,r),o.stateAfter=r.line%5==0?r.save():null,r.nextLine();if(+new Date>n)return Ri(t,t.options.workDelay),!0})),e.highlightFrontier=r.line,e.modeFrontier=Math.max(e.modeFrontier,r.line),i.length&&Wi(t,(function(){for(var e=0;e<i.length;e++)Br(t,i[e],"text")}))}}var Ii=function(t,e,n){var r=t.display;this.viewport=e,this.visible=ii(r,t.doc,e),this.editorIsHidden=!r.wrapper.offsetWidth,this.wrapperHeight=r.wrapper.clientHeight,this.wrapperWidth=r.wrapper.clientWidth,this.oldDisplayWidth=Jn(t),this.force=n,this.dims=Wr(t),this.events=[]};function Bi(t){var e=t.display;!e.scrollbarsClipped&&e.scroller.offsetWidth&&(e.nativeBarWidth=e.scroller.offsetWidth-e.scroller.clientWidth,e.heightForcer.style.height=Zn(t)+"px",e.sizer.style.marginBottom=-e.nativeBarWidth+"px",e.sizer.style.borderRightWidth=Zn(t)+"px",e.scrollbarsClipped=!0)}function Vi(t){if(t.hasFocus())return null;var e=E(z(t));if(!e||!A(t.display.lineDiv,e))return null;var n={activeElt:e};if(window.getSelection){var r=R(t).getSelection();r.anchorNode&&r.extend&&A(t.display.lineDiv,r.anchorNode)&&(n.anchorNode=r.anchorNode,n.anchorOffset=r.anchorOffset,n.focusNode=r.focusNode,n.focusOffset=r.focusOffset)}return n}function $i(t){if(t&&t.activeElt&&t.activeElt!=E(t.activeElt.ownerDocument)&&(t.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(t.activeElt.nodeName)&&t.anchorNode&&A(document.body,t.anchorNode)&&A(document.body,t.focusNode))){var e=t.activeElt.ownerDocument,n=e.defaultView.getSelection(),r=e.createRange();r.setEnd(t.anchorNode,t.anchorOffset),r.collapse(!1),n.removeAllRanges(),n.addRange(r),n.extend(t.focusNode,t.focusOffset)}}function Ui(t,e){var n=t.display,r=t.doc;if(e.editorIsHidden)return Vr(t),!1;if(!e.force&&e.visible.from>=n.viewFrom&&e.visible.to<=n.viewTo&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo)&&n.renderedView==n.view&&0==Gr(t))return!1;Zi(t)&&(Vr(t),e.dims=Wr(t));var i=r.first+r.size,o=Math.max(e.visible.from-t.options.viewportMargin,r.first),s=Math.min(i,e.visible.to+t.options.viewportMargin);n.viewFrom<o&&o-n.viewFrom<20&&(o=Math.max(r.first,n.viewFrom)),n.viewTo>s&&n.viewTo-s<20&&(s=Math.min(i,n.viewTo)),Ee&&(o=on(t.doc,o),s=sn(t.doc,s));var l=o!=n.viewFrom||s!=n.viewTo||n.lastWrapHeight!=e.wrapperHeight||n.lastWrapWidth!=e.wrapperWidth;Ur(t,o,s),n.viewOffset=cn(te(t.doc,n.viewFrom)),t.display.mover.style.top=n.viewOffset+"px";var a=Gr(t);if(!l&&0==a&&!e.force&&n.renderedView==n.view&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo))return!1;var c=Vi(t);return a>4&&(n.lineDiv.style.display="none"),Ki(t,n.updateLineNumbers,e.dims),a>4&&(n.lineDiv.style.display=""),n.renderedView=n.view,$i(c),T(n.cursorDiv),T(n.selectionDiv),n.gutters.style.height=n.sizer.style.minHeight=0,l&&(n.lastWrapHeight=e.wrapperHeight,n.lastWrapWidth=e.wrapperWidth,Ri(t,400)),n.updateLineNumbers=null,!0}function Gi(t,e){for(var n=e.viewport,r=!0;;r=!1){if(r&&t.options.lineWrapping&&e.oldDisplayWidth!=Jn(t))r&&(e.visible=ii(t.display,t.doc,n));else if(n&&null!=n.top&&(n={top:Math.min(t.doc.height+Xn(t.display)-Qn(t),n.top)}),e.visible=ii(t.display,t.doc,n),e.visible.from>=t.display.viewFrom&&e.visible.to<=t.display.viewTo)break;if(!Ui(t,e))break;ni(t);var i=yi(t);jr(t),xi(t,i),Xi(t,i),e.force=!1}e.signal(t,"update",t),t.display.viewFrom==t.display.reportedViewFrom&&t.display.viewTo==t.display.reportedViewTo||(e.signal(t,"viewportChange",t,t.display.viewFrom,t.display.viewTo),t.display.reportedViewFrom=t.display.viewFrom,t.display.reportedViewTo=t.display.viewTo)}function ji(t,e){var n=new Ii(t,e);if(Ui(t,n)){ni(t),Gi(t,n);var r=yi(t);jr(t),xi(t,r),Xi(t,r),n.finish()}}function Ki(t,e,n){var r=t.display,i=t.options.lineNumbers,o=r.lineDiv,s=o.firstChild;function l(e){var n=e.nextSibling;return a&&b&&t.display.currentWheelTarget==e?e.style.display="none":e.parentNode.removeChild(e),n}for(var c=r.view,h=r.viewFrom,u=0;u<c.length;u++){var d=c[u];if(d.hidden);else if(d.node&&d.node.parentNode==o){for(;s!=d.node;)s=l(s);var f=i&&null!=e&&e<=h&&d.lineNumber;d.changes&&($(d.changes,"gutter")>-1&&(f=!1),Wn(t,d,h,n)),f&&(T(d.lineNumber),d.lineNumber.appendChild(document.createTextNode(le(t.options,h)))),s=d.node.nextSibling}else{var p=Vn(t,d,h,n);o.insertBefore(p,s)}h+=d.size}for(;s;)s=l(s)}function qi(t){var e=t.gutters.offsetWidth;t.sizer.style.marginLeft=e+"px",An(t,"gutterChanged",t)}function Xi(t,e){t.display.sizer.style.minHeight=e.docHeight+"px",t.display.heightForcer.style.top=e.docHeight+"px",t.display.gutters.style.height=e.docHeight+t.display.barHeight+Zn(t)+"px"}function Yi(t){var e=t.display,n=e.view;if(e.alignWidgets||e.gutters.firstChild&&t.options.fixedGutter){for(var r=Pr(e)-e.scroller.scrollLeft+t.doc.scrollLeft,i=e.gutters.offsetWidth,o=r+"px",s=0;s<n.length;s++)if(!n[s].hidden){t.options.fixedGutter&&(n[s].gutter&&(n[s].gutter.style.left=o),n[s].gutterBackground&&(n[s].gutterBackground.style.left=o));var l=n[s].alignable;if(l)for(var a=0;a<l.length;a++)l[a].style.left=o}t.options.fixedGutter&&(e.gutters.style.left=r+i+"px")}}function Zi(t){if(!t.options.lineNumbers)return!1;var e=t.doc,n=le(t.options,e.first+e.size-1),r=t.display;if(n.length!=r.lineNumChars){var i=r.measure.appendChild(N("div",[N("div",n)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,s=i.offsetWidth-o;return r.lineGutter.style.width="",r.lineNumInnerWidth=Math.max(o,r.lineGutter.offsetWidth-s)+1,r.lineNumWidth=r.lineNumInnerWidth+s,r.lineNumChars=r.lineNumInnerWidth?n.length:-1,r.lineGutter.style.width=r.lineNumWidth+"px",qi(t.display),!0}return!1}function Ji(t,e){for(var n=[],r=!1,i=0;i<t.length;i++){var o=t[i],s=null;if("string"!=typeof o&&(s=o.style,o=o.className),"CodeMirror-linenumbers"==o){if(!e)continue;r=!0}n.push({className:o,style:s})}return e&&!r&&n.push({className:"CodeMirror-linenumbers",style:null}),n}function Qi(t){var e=t.gutters,n=t.gutterSpecs;T(e),t.lineGutter=null;for(var r=0;r<n.length;++r){var i=n[r],o=i.className,s=i.style,l=e.appendChild(N("div",null,"CodeMirror-gutter "+o));s&&(l.style.cssText=s),"CodeMirror-linenumbers"==o&&(t.lineGutter=l,l.style.width=(t.lineNumWidth||1)+"px")}e.style.display=n.length?"":"none",qi(t)}function to(t){Qi(t.display),Ir(t),Yi(t)}function eo(t,e,r,i){var o=this;this.input=r,o.scrollbarFiller=N("div",null,"CodeMirror-scrollbar-filler"),o.scrollbarFiller.setAttribute("cm-not-content","true"),o.gutterFiller=N("div",null,"CodeMirror-gutter-filler"),o.gutterFiller.setAttribute("cm-not-content","true"),o.lineDiv=D("div",null,"CodeMirror-code"),o.selectionDiv=N("div",null,null,"position: relative; z-index: 1"),o.cursorDiv=N("div",null,"CodeMirror-cursors"),o.measure=N("div",null,"CodeMirror-measure"),o.lineMeasure=N("div",null,"CodeMirror-measure"),o.lineSpace=D("div",[o.measure,o.lineMeasure,o.selectionDiv,o.cursorDiv,o.lineDiv],null,"position: relative; outline: none");var c=D("div",[o.lineSpace],"CodeMirror-lines");o.mover=N("div",[c],null,"position: relative"),o.sizer=N("div",[o.mover],"CodeMirror-sizer"),o.sizerWidth=null,o.heightForcer=N("div",null,null,"position: absolute; height: "+U+"px; width: 1px;"),o.gutters=N("div",null,"CodeMirror-gutters"),o.lineGutter=null,o.scroller=N("div",[o.sizer,o.heightForcer,o.gutters],"CodeMirror-scroll"),o.scroller.setAttribute("tabIndex","-1"),o.wrapper=N("div",[o.scrollbarFiller,o.gutterFiller,o.scroller],"CodeMirror"),h&&u>=105&&(o.wrapper.style.clipPath="inset(0px)"),o.wrapper.setAttribute("translate","no"),s&&l<8&&(o.gutters.style.zIndex=-1,o.scroller.style.paddingRight=0),a||n&&y||(o.scroller.draggable=!0),t&&(t.appendChild?t.appendChild(o.wrapper):t(o.wrapper)),o.viewFrom=o.viewTo=e.first,o.reportedViewFrom=o.reportedViewTo=e.first,o.view=[],o.renderedView=null,o.externalMeasured=null,o.viewOffset=0,o.lastWrapHeight=o.lastWrapWidth=0,o.updateLineNumbers=null,o.nativeBarWidth=o.barHeight=o.barWidth=0,o.scrollbarsClipped=!1,o.lineNumWidth=o.lineNumInnerWidth=o.lineNumChars=null,o.alignWidgets=!1,o.cachedCharWidth=o.cachedTextHeight=o.cachedPaddingH=null,o.maxLine=null,o.maxLineLength=0,o.maxLineChanged=!1,o.wheelDX=o.wheelDY=o.wheelStartX=o.wheelStartY=null,o.shift=!1,o.selForContextMenu=null,o.activeTouch=null,o.gutterSpecs=Ji(i.gutters,i.lineNumbers),Qi(o),r.init(o)}Ii.prototype.signal=function(t,e){Ct(t,e)&&this.events.push(arguments)},Ii.prototype.finish=function(){for(var t=0;t<this.events.length;t++)wt.apply(null,this.events[t])};var no=0,ro=null;function io(t){var e=t.wheelDeltaX,n=t.wheelDeltaY;return null==e&&t.detail&&t.axis==t.HORIZONTAL_AXIS&&(e=t.detail),null==n&&t.detail&&t.axis==t.VERTICAL_AXIS?n=t.detail:null==n&&(n=t.wheelDelta),{x:e,y:n}}function oo(t){var e=io(t);return e.x*=ro,e.y*=ro,e}function so(t,e){h&&102==u&&(null==t.display.chromeScrollHack?t.display.sizer.style.pointerEvents="none":clearTimeout(t.display.chromeScrollHack),t.display.chromeScrollHack=setTimeout((function(){t.display.chromeScrollHack=null,t.display.sizer.style.pointerEvents=""}),100));var r=io(e),i=r.x,o=r.y,s=ro;0===e.deltaMode&&(i=e.deltaX,o=e.deltaY,s=1);var l=t.display,c=l.scroller,f=c.scrollWidth>c.clientWidth,p=c.scrollHeight>c.clientHeight;if(i&&f||o&&p){if(o&&b&&a)t:for(var g=e.target,m=l.view;g!=c;g=g.parentNode)for(var v=0;v<m.length;v++)if(m[v].node==g){t.display.currentWheelTarget=g;break t}if(i&&!n&&!d&&null!=s)return o&&p&&gi(t,Math.max(0,c.scrollTop+o*s)),vi(t,Math.max(0,c.scrollLeft+i*s)),(!o||o&&p)&&St(e),void(l.wheelStartX=null);if(o&&null!=s){var y=o*s,w=t.doc.scrollTop,x=w+l.wrapper.clientHeight;y<0?w=Math.max(0,w+y-50):x=Math.min(t.doc.height,x+y+50),ji(t,{top:w,bottom:x})}no<20&&0!==e.deltaMode&&(null==l.wheelStartX?(l.wheelStartX=c.scrollLeft,l.wheelStartY=c.scrollTop,l.wheelDX=i,l.wheelDY=o,setTimeout((function(){if(null!=l.wheelStartX){var t=c.scrollLeft-l.wheelStartX,e=c.scrollTop-l.wheelStartY,n=e&&l.wheelDY&&e/l.wheelDY||t&&l.wheelDX&&t/l.wheelDX;l.wheelStartX=l.wheelStartY=null,n&&(ro=(ro*no+n)/(no+1),++no)}}),200)):(l.wheelDX+=i,l.wheelDY+=o))}}s?ro=-.53:n?ro=15:h?ro=-.7:f&&(ro=-1/3);var lo=function(t,e){this.ranges=t,this.primIndex=e};lo.prototype.primary=function(){return this.ranges[this.primIndex]},lo.prototype.equals=function(t){if(t==this)return!0;if(t.primIndex!=this.primIndex||t.ranges.length!=this.ranges.length)return!1;for(var e=0;e<this.ranges.length;e++){var n=this.ranges[e],r=t.ranges[e];if(!he(n.anchor,r.anchor)||!he(n.head,r.head))return!1}return!0},lo.prototype.deepCopy=function(){for(var t=[],e=0;e<this.ranges.length;e++)t[e]=new ao(ue(this.ranges[e].anchor),ue(this.ranges[e].head));return new lo(t,this.primIndex)},lo.prototype.somethingSelected=function(){for(var t=0;t<this.ranges.length;t++)if(!this.ranges[t].empty())return!0;return!1},lo.prototype.contains=function(t,e){e||(e=t);for(var n=0;n<this.ranges.length;n++){var r=this.ranges[n];if(ce(e,r.from())>=0&&ce(t,r.to())<=0)return n}return-1};var ao=function(t,e){this.anchor=t,this.head=e};function co(t,e,n){var r=t&&t.options.selectionsMayTouch,i=e[n];e.sort((function(t,e){return ce(t.from(),e.from())})),n=$(e,i);for(var o=1;o<e.length;o++){var s=e[o],l=e[o-1],a=ce(l.to(),s.from());if(r&&!s.empty()?a>0:a>=0){var c=fe(l.from(),s.from()),h=de(l.to(),s.to()),u=l.empty()?s.from()==s.head:l.from()==l.head;o<=n&&--n,e.splice(--o,2,new ao(u?h:c,u?c:h))}}return new lo(e,n)}function ho(t,e){return new lo([new ao(t,e||t)],0)}function uo(t){return t.text?ae(t.from.line+t.text.length-1,J(t.text).length+(1==t.text.length?t.from.ch:0)):t.to}function fo(t,e){if(ce(t,e.from)<0)return t;if(ce(t,e.to)<=0)return uo(e);var n=t.line+e.text.length-(e.to.line-e.from.line)-1,r=t.ch;return t.line==e.to.line&&(r+=uo(e).ch-e.to.ch),ae(n,r)}function po(t,e){for(var n=[],r=0;r<t.sel.ranges.length;r++){var i=t.sel.ranges[r];n.push(new ao(fo(i.anchor,e),fo(i.head,e)))}return co(t.cm,n,t.sel.primIndex)}function go(t,e,n){return t.line==e.line?ae(n.line,t.ch-e.ch+n.ch):ae(n.line+(t.line-e.line),t.ch)}function mo(t,e,n){for(var r=[],i=ae(t.first,0),o=i,s=0;s<e.length;s++){var l=e[s],a=go(l.from,i,o),c=go(uo(l),i,o);if(i=l.to,o=c,"around"==n){var h=t.sel.ranges[s],u=ce(h.head,h.anchor)<0;r[s]=new ao(u?c:a,u?a:c)}else r[s]=new ao(a,a)}return new lo(r,t.sel.primIndex)}function vo(t){t.doc.mode=Kt(t.options,t.doc.modeOption),yo(t)}function yo(t){t.doc.iter((function(t){t.stateAfter&&(t.stateAfter=null),t.styles&&(t.styles=null)})),t.doc.modeFrontier=t.doc.highlightFrontier=t.doc.first,Ri(t,100),t.state.modeGen++,t.curOp&&Ir(t)}function bo(t,e){return 0==e.from.ch&&0==e.to.ch&&""==J(e.text)&&(!t.cm||t.cm.options.wholeLineUpdateBefore)}function wo(t,e,n,r){function i(t){return n?n[t]:null}function o(t,n,i){fn(t,n,i,r),An(t,"change",t,e)}function s(t,e){for(var n=[],o=t;o<e;++o)n.push(new dn(c[o],i(o),r));return n}var l=e.from,a=e.to,c=e.text,h=te(t,l.line),u=te(t,a.line),d=J(c),f=i(c.length-1),p=a.line-l.line;if(e.full)t.insert(0,s(0,c.length)),t.remove(c.length,t.size-c.length);else if(bo(t,e)){var g=s(0,c.length-1);o(u,u.text,f),p&&t.remove(l.line,p),g.length&&t.insert(l.line,g)}else if(h==u)if(1==c.length)o(h,h.text.slice(0,l.ch)+d+h.text.slice(a.ch),f);else{var m=s(1,c.length-1);m.push(new dn(d+h.text.slice(a.ch),f,r)),o(h,h.text.slice(0,l.ch)+c[0],i(0)),t.insert(l.line+1,m)}else if(1==c.length)o(h,h.text.slice(0,l.ch)+c[0]+u.text.slice(a.ch),i(0)),t.remove(l.line+1,p);else{o(h,h.text.slice(0,l.ch)+c[0],i(0)),o(u,d+u.text.slice(a.ch),f);var v=s(1,c.length-1);p>1&&t.remove(l.line+1,p-1),t.insert(l.line+1,v)}An(t,"change",t,e)}function xo(t,e,n){function r(t,i,o){if(t.linked)for(var s=0;s<t.linked.length;++s){var l=t.linked[s];if(l.doc!=i){var a=o&&l.sharedHist;n&&!a||(e(l.doc,a),r(l.doc,t,a))}}}r(t,null,!0)}function _o(t,e){if(e.cm)throw new Error("This document is already in use.");t.doc=e,e.cm=t,zr(t),vo(t),Co(t),t.options.direction=e.direction,t.options.lineWrapping||un(t),t.options.mode=e.modeOption,Ir(t)}function Co(t){("rtl"==t.doc.direction?W:M)(t.display.lineDiv,"CodeMirror-rtl")}function ko(t){Wi(t,(function(){Co(t),Ir(t)}))}function So(t){this.done=[],this.undone=[],this.undoDepth=t?t.undoDepth:1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=t?t.maxGeneration:1}function Lo(t,e){var n={from:ue(e.from),to:uo(e),text:ee(t,e.from,e.to)};return Eo(t,n,e.from.line,e.to.line+1),xo(t,(function(t){return Eo(t,n,e.from.line,e.to.line+1)}),!0),n}function Mo(t){for(;t.length&&J(t).ranges;)t.pop()}function To(t,e){return e?(Mo(t.done),J(t.done)):t.done.length&&!J(t.done).ranges?J(t.done):t.done.length>1&&!t.done[t.done.length-2].ranges?(t.done.pop(),J(t.done)):void 0}function Oo(t,e,n,r){var i=t.history;i.undone.length=0;var o,s,l=+new Date;if((i.lastOp==r||i.lastOrigin==e.origin&&e.origin&&("+"==e.origin.charAt(0)&&i.lastModTime>l-(t.cm?t.cm.options.historyEventDelay:500)||"*"==e.origin.charAt(0)))&&(o=To(i,i.lastOp==r)))s=J(o.changes),0==ce(e.from,e.to)&&0==ce(e.from,s.to)?s.to=uo(e):o.changes.push(Lo(t,e));else{var a=J(i.done);for(a&&a.ranges||Ao(t.sel,i.done),o={changes:[Lo(t,e)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(n),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=l,i.lastOp=i.lastSelOp=r,i.lastOrigin=i.lastSelOrigin=e.origin,s||wt(t,"historyAdded")}function No(t,e,n,r){var i=e.charAt(0);return"*"==i||"+"==i&&n.ranges.length==r.ranges.length&&n.somethingSelected()==r.somethingSelected()&&new Date-t.history.lastSelTime<=(t.cm?t.cm.options.historyEventDelay:500)}function Do(t,e,n,r){var i=t.history,o=r&&r.origin;n==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||No(t,o,J(i.done),e))?i.done[i.done.length-1]=e:Ao(e,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=n,r&&!1!==r.clearRedo&&Mo(i.undone)}function Ao(t,e){var n=J(e);n&&n.ranges&&n.equals(t)||e.push(t)}function Eo(t,e,n,r){var i=e["spans_"+t.id],o=0;t.iter(Math.max(t.first,n),Math.min(t.first+t.size,r),(function(n){n.markedSpans&&((i||(i=e["spans_"+t.id]={}))[o]=n.markedSpans),++o}))}function Wo(t){if(!t)return null;for(var e,n=0;n<t.length;++n)t[n].marker.explicitlyCleared?e||(e=t.slice(0,n)):e&&e.push(t[n]);return e?e.length?e:null:t}function Po(t,e){var n=e["spans_"+t.id];if(!n)return null;for(var r=[],i=0;i<e.text.length;++i)r.push(Wo(n[i]));return r}function Ho(t,e){var n=Po(t,e),r=Ve(t,e);if(!n)return r;if(!r)return n;for(var i=0;i<n.length;++i){var o=n[i],s=r[i];if(o&&s)t:for(var l=0;l<s.length;++l){for(var a=s[l],c=0;c<o.length;++c)if(o[c].marker==a.marker)continue t;o.push(a)}else s&&(n[i]=s)}return n}function zo(t,e,n){for(var r=[],i=0;i<t.length;++i){var o=t[i];if(o.ranges)r.push(n?lo.prototype.deepCopy.call(o):o);else{var s=o.changes,l=[];r.push({changes:l});for(var a=0;a<s.length;++a){var c=s[a],h=void 0;if(l.push({from:c.from,to:c.to,text:c.text}),e)for(var u in c)(h=u.match(/^spans_(\d+)$/))&&$(e,Number(h[1]))>-1&&(J(l)[u]=c[u],delete c[u])}}}return r}function Ro(t,e,n,r){if(r){var i=t.anchor;if(n){var o=ce(e,i)<0;o!=ce(n,i)<0?(i=e,e=n):o!=ce(e,n)<0&&(e=n)}return new ao(i,e)}return new ao(n||e,e)}function Fo(t,e,n,r,i){null==i&&(i=t.cm&&(t.cm.display.shift||t.extend)),Go(t,new lo([Ro(t.sel.primary(),e,n,i)],0),r)}function Io(t,e,n){for(var r=[],i=t.cm&&(t.cm.display.shift||t.extend),o=0;o<t.sel.ranges.length;o++)r[o]=Ro(t.sel.ranges[o],e[o],null,i);Go(t,co(t.cm,r,t.sel.primIndex),n)}function Bo(t,e,n,r){var i=t.sel.ranges.slice(0);i[e]=n,Go(t,co(t.cm,i,t.sel.primIndex),r)}function Vo(t,e,n,r){Go(t,ho(e,n),r)}function $o(t,e,n){var r={ranges:e.ranges,update:function(e){this.ranges=[];for(var n=0;n<e.length;n++)this.ranges[n]=new ao(ge(t,e[n].anchor),ge(t,e[n].head))},origin:n&&n.origin};return wt(t,"beforeSelectionChange",t,r),t.cm&&wt(t.cm,"beforeSelectionChange",t.cm,r),r.ranges!=e.ranges?co(t.cm,r.ranges,r.ranges.length-1):e}function Uo(t,e,n){var r=t.history.done,i=J(r);i&&i.ranges?(r[r.length-1]=e,jo(t,e,n)):Go(t,e,n)}function Go(t,e,n){jo(t,e,n),Do(t,t.sel,t.cm?t.cm.curOp.id:NaN,n)}function jo(t,e,n){(Ct(t,"beforeSelectionChange")||t.cm&&Ct(t.cm,"beforeSelectionChange"))&&(e=$o(t,e,n));var r=n&&n.bias||(ce(e.primary().head,t.sel.primary().head)<0?-1:1);Ko(t,Xo(t,e,r,!0)),n&&!1===n.scroll||!t.cm||"nocursor"==t.cm.getOption("readOnly")||hi(t.cm)}function Ko(t,e){e.equals(t.sel)||(t.sel=e,t.cm&&(t.cm.curOp.updateInput=1,t.cm.curOp.selectionChanged=!0,_t(t.cm)),An(t,"cursorActivity",t))}function qo(t){Ko(t,Xo(t,t.sel,null,!1))}function Xo(t,e,n,r){for(var i,o=0;o<e.ranges.length;o++){var s=e.ranges[o],l=e.ranges.length==t.sel.ranges.length&&t.sel.ranges[o],a=Zo(t,s.anchor,l&&l.anchor,n,r),c=s.head==s.anchor?a:Zo(t,s.head,l&&l.head,n,r);(i||a!=s.anchor||c!=s.head)&&(i||(i=e.ranges.slice(0,o)),i[o]=new ao(a,c))}return i?co(t.cm,i,e.primIndex):e}function Yo(t,e,n,r,i){var o=te(t,e.line);if(o.markedSpans)for(var s=0;s<o.markedSpans.length;++s){var l=o.markedSpans[s],a=l.marker,c="selectLeft"in a?!a.selectLeft:a.inclusiveLeft,h="selectRight"in a?!a.selectRight:a.inclusiveRight;if((null==l.from||(c?l.from<=e.ch:l.from<e.ch))&&(null==l.to||(h?l.to>=e.ch:l.to>e.ch))){if(i&&(wt(a,"beforeCursorEnter"),a.explicitlyCleared)){if(o.markedSpans){--s;continue}break}if(!a.atomic)continue;if(n){var u=a.find(r<0?1:-1),d=void 0;if((r<0?h:c)&&(u=Jo(t,u,-r,u&&u.line==e.line?o:null)),u&&u.line==e.line&&(d=ce(u,n))&&(r<0?d<0:d>0))return Yo(t,u,e,r,i)}var f=a.find(r<0?-1:1);return(r<0?c:h)&&(f=Jo(t,f,r,f.line==e.line?o:null)),f?Yo(t,f,e,r,i):null}}return e}function Zo(t,e,n,r,i){var o=r||1,s=Yo(t,e,n,o,i)||!i&&Yo(t,e,n,o,!0)||Yo(t,e,n,-o,i)||!i&&Yo(t,e,n,-o,!0);return s||(t.cantEdit=!0,ae(t.first,0))}function Jo(t,e,n,r){return n<0&&0==e.ch?e.line>t.first?ge(t,ae(e.line-1)):null:n>0&&e.ch==(r||te(t,e.line)).text.length?e.line<t.first+t.size-1?ae(e.line+1,0):null:new ae(e.line,e.ch+n)}function Qo(t){t.setSelection(ae(t.firstLine(),0),ae(t.lastLine()),j)}function ts(t,e,n){var r={canceled:!1,from:e.from,to:e.to,text:e.text,origin:e.origin,cancel:function(){return r.canceled=!0}};return n&&(r.update=function(e,n,i,o){e&&(r.from=ge(t,e)),n&&(r.to=ge(t,n)),i&&(r.text=i),void 0!==o&&(r.origin=o)}),wt(t,"beforeChange",t,r),t.cm&&wt(t.cm,"beforeChange",t.cm,r),r.canceled?(t.cm&&(t.cm.curOp.updateInput=2),null):{from:r.from,to:r.to,text:r.text,origin:r.origin}}function es(t,e,n){if(t.cm){if(!t.cm.curOp)return Pi(t.cm,es)(t,e,n);if(t.cm.state.suppressEdits)return}if(!(Ct(t,"beforeChange")||t.cm&&Ct(t.cm,"beforeChange"))||(e=ts(t,e,!0))){var r=Ae&&!n&&Ue(t,e.from,e.to);if(r)for(var i=r.length-1;i>=0;--i)ns(t,{from:r[i].from,to:r[i].to,text:i?[""]:e.text,origin:e.origin});else ns(t,e)}}function ns(t,e){if(1!=e.text.length||""!=e.text[0]||0!=ce(e.from,e.to)){var n=po(t,e);Oo(t,e,n,t.cm?t.cm.curOp.id:NaN),os(t,e,n,Ve(t,e));var r=[];xo(t,(function(t,n){n||-1!=$(r,t.history)||(hs(t.history,e),r.push(t.history)),os(t,e,null,Ve(t,e))}))}}function rs(t,e,n){var r=t.cm&&t.cm.state.suppressEdits;if(!r||n){for(var i,o=t.history,s=t.sel,l="undo"==e?o.done:o.undone,a="undo"==e?o.undone:o.done,c=0;c<l.length&&(i=l[c],n?!i.ranges||i.equals(t.sel):i.ranges);c++);if(c!=l.length){for(o.lastOrigin=o.lastSelOrigin=null;;){if(!(i=l.pop()).ranges){if(r)return void l.push(i);break}if(Ao(i,a),n&&!i.equals(t.sel))return void Go(t,i,{clearRedo:!1});s=i}var h=[];Ao(s,a),a.push({changes:h,generation:o.generation}),o.generation=i.generation||++o.maxGeneration;for(var u=Ct(t,"beforeChange")||t.cm&&Ct(t.cm,"beforeChange"),d=function(n){var r=i.changes[n];if(r.origin=e,u&&!ts(t,r,!1))return l.length=0,{};h.push(Lo(t,r));var o=n?po(t,r):J(l);os(t,r,o,Ho(t,r)),!n&&t.cm&&t.cm.scrollIntoView({from:r.from,to:uo(r)});var s=[];xo(t,(function(t,e){e||-1!=$(s,t.history)||(hs(t.history,r),s.push(t.history)),os(t,r,null,Ho(t,r))}))},f=i.changes.length-1;f>=0;--f){var p=d(f);if(p)return p.v}}}}function is(t,e){if(0!=e&&(t.first+=e,t.sel=new lo(Q(t.sel.ranges,(function(t){return new ao(ae(t.anchor.line+e,t.anchor.ch),ae(t.head.line+e,t.head.ch))})),t.sel.primIndex),t.cm)){Ir(t.cm,t.first,t.first-e,e);for(var n=t.cm.display,r=n.viewFrom;r<n.viewTo;r++)Br(t.cm,r,"gutter")}}function os(t,e,n,r){if(t.cm&&!t.cm.curOp)return Pi(t.cm,os)(t,e,n,r);if(e.to.line<t.first)is(t,e.text.length-1-(e.to.line-e.from.line));else if(!(e.from.line>t.lastLine())){if(e.from.line<t.first){var i=e.text.length-1-(t.first-e.from.line);is(t,i),e={from:ae(t.first,0),to:ae(e.to.line+i,e.to.ch),text:[J(e.text)],origin:e.origin}}var o=t.lastLine();e.to.line>o&&(e={from:e.from,to:ae(o,te(t,o).text.length),text:[e.text[0]],origin:e.origin}),e.removed=ee(t,e.from,e.to),n||(n=po(t,e)),t.cm?ss(t.cm,e,r):wo(t,e,r),jo(t,n,j),t.cantEdit&&Zo(t,ae(t.firstLine(),0))&&(t.cantEdit=!1)}}function ss(t,e,n){var r=t.doc,i=t.display,o=e.from,s=e.to,l=!1,a=o.line;t.options.lineWrapping||(a=ie(en(te(r,o.line))),r.iter(a,s.line+1,(function(t){if(t==i.maxLine)return l=!0,!0}))),r.sel.contains(e.from,e.to)>-1&&_t(t),wo(r,e,n,Hr(t)),t.options.lineWrapping||(r.iter(a,o.line+e.text.length,(function(t){var e=hn(t);e>i.maxLineLength&&(i.maxLine=t,i.maxLineLength=e,i.maxLineChanged=!0,l=!1)})),l&&(t.curOp.updateMaxLine=!0)),De(r,o.line),Ri(t,400);var c=e.text.length-(s.line-o.line)-1;e.full?Ir(t):o.line!=s.line||1!=e.text.length||bo(t.doc,e)?Ir(t,o.line,s.line+1,c):Br(t,o.line,"text");var h=Ct(t,"changes"),u=Ct(t,"change");if(u||h){var d={from:o,to:s,text:e.text,removed:e.removed,origin:e.origin};u&&An(t,"change",t,d),h&&(t.curOp.changeObjs||(t.curOp.changeObjs=[])).push(d)}t.display.selForContextMenu=null}function ls(t,e,n,r,i){var o;r||(r=n),ce(r,n)<0&&(n=(o=[r,n])[0],r=o[1]),"string"==typeof e&&(e=t.splitLines(e)),es(t,{from:n,to:r,text:e,origin:i})}function as(t,e,n,r){n<t.line?t.line+=r:e<t.line&&(t.line=e,t.ch=0)}function cs(t,e,n,r){for(var i=0;i<t.length;++i){var o=t[i],s=!0;if(o.ranges){o.copied||((o=t[i]=o.deepCopy()).copied=!0);for(var l=0;l<o.ranges.length;l++)as(o.ranges[l].anchor,e,n,r),as(o.ranges[l].head,e,n,r)}else{for(var a=0;a<o.changes.length;++a){var c=o.changes[a];if(n<c.from.line)c.from=ae(c.from.line+r,c.from.ch),c.to=ae(c.to.line+r,c.to.ch);else if(e<=c.to.line){s=!1;break}}s||(t.splice(0,i+1),i=0)}}}function hs(t,e){var n=e.from.line,r=e.to.line,i=e.text.length-(r-n)-1;cs(t.done,n,r,i),cs(t.undone,n,r,i)}function us(t,e,n,r){var i=e,o=e;return"number"==typeof e?o=te(t,pe(t,e)):i=ie(e),null==i?null:(r(o,i)&&t.cm&&Br(t.cm,i,n),o)}function ds(t){this.lines=t,this.parent=null;for(var e=0,n=0;n<t.length;++n)t[n].parent=this,e+=t[n].height;this.height=e}function fs(t){this.children=t;for(var e=0,n=0,r=0;r<t.length;++r){var i=t[r];e+=i.chunkSize(),n+=i.height,i.parent=this}this.size=e,this.height=n,this.parent=null}ao.prototype.from=function(){return fe(this.anchor,this.head)},ao.prototype.to=function(){return de(this.anchor,this.head)},ao.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},ds.prototype={chunkSize:function(){return this.lines.length},removeInner:function(t,e){for(var n=t,r=t+e;n<r;++n){var i=this.lines[n];this.height-=i.height,pn(i),An(i,"delete")}this.lines.splice(t,e)},collapse:function(t){t.push.apply(t,this.lines)},insertInner:function(t,e,n){this.height+=n,this.lines=this.lines.slice(0,t).concat(e).concat(this.lines.slice(t));for(var r=0;r<e.length;++r)e[r].parent=this},iterN:function(t,e,n){for(var r=t+e;t<r;++t)if(n(this.lines[t]))return!0}},fs.prototype={chunkSize:function(){return this.size},removeInner:function(t,e){this.size-=e;for(var n=0;n<this.children.length;++n){var r=this.children[n],i=r.chunkSize();if(t<i){var o=Math.min(e,i-t),s=r.height;if(r.removeInner(t,o),this.height-=s-r.height,i==o&&(this.children.splice(n--,1),r.parent=null),0==(e-=o))break;t=0}else t-=i}if(this.size-e<25&&(this.children.length>1||!(this.children[0]instanceof ds))){var l=[];this.collapse(l),this.children=[new ds(l)],this.children[0].parent=this}},collapse:function(t){for(var e=0;e<this.children.length;++e)this.children[e].collapse(t)},insertInner:function(t,e,n){this.size+=e.length,this.height+=n;for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(t<=o){if(i.insertInner(t,e,n),i.lines&&i.lines.length>50){for(var s=i.lines.length%25+25,l=s;l<i.lines.length;){var a=new ds(i.lines.slice(l,l+=25));i.height-=a.height,this.children.splice(++r,0,a),a.parent=this}i.lines=i.lines.slice(0,s),this.maybeSpill()}break}t-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var t=this;do{var e=new fs(t.children.splice(t.children.length-5,5));if(t.parent){t.size-=e.size,t.height-=e.height;var n=$(t.parent.children,t);t.parent.children.splice(n+1,0,e)}else{var r=new fs(t.children);r.parent=t,t.children=[r,e],t=r}e.parent=t.parent}while(t.children.length>10);t.parent.maybeSpill()}},iterN:function(t,e,n){for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(t<o){var s=Math.min(e,o-t);if(i.iterN(t,s,n))return!0;if(0==(e-=s))break;t=0}else t-=o}}};var ps=function(t,e,n){if(n)for(var r in n)n.hasOwnProperty(r)&&(this[r]=n[r]);this.doc=t,this.node=e};function gs(t,e,n){cn(e)<(t.curOp&&t.curOp.scrollTop||t.doc.scrollTop)&&ci(t,n)}function ms(t,e,n,r){var i=new ps(t,n,r),o=t.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),us(t,e,"widget",(function(e){var n=e.widgets||(e.widgets=[]);if(null==i.insertAt?n.push(i):n.splice(Math.min(n.length,Math.max(0,i.insertAt)),0,i),i.line=e,o&&!ln(t,e)){var r=cn(e)<t.scrollTop;re(e,e.height+jn(i)),r&&ci(o,i.height),o.curOp.forceUpdate=!0}return!0})),o&&An(o,"lineWidgetAdded",o,i,"number"==typeof e?e:ie(e)),i}ps.prototype.clear=function(){var t=this.doc.cm,e=this.line.widgets,n=this.line,r=ie(n);if(null!=r&&e){for(var i=0;i<e.length;++i)e[i]==this&&e.splice(i--,1);e.length||(n.widgets=null);var o=jn(this);re(n,Math.max(0,n.height-o)),t&&(Wi(t,(function(){gs(t,n,-o),Br(t,r,"widget")})),An(t,"lineWidgetCleared",t,this,r))}},ps.prototype.changed=function(){var t=this,e=this.height,n=this.doc.cm,r=this.line;this.height=null;var i=jn(this)-e;i&&(ln(this.doc,r)||re(r,r.height+i),n&&Wi(n,(function(){n.curOp.forceUpdate=!0,gs(n,r,i),An(n,"lineWidgetChanged",n,t,ie(r))})))},kt(ps);var vs=0,ys=function(t,e){this.lines=[],this.type=e,this.doc=t,this.id=++vs};function bs(t,e,n,r,i){if(r&&r.shared)return xs(t,e,n,r,i);if(t.cm&&!t.cm.curOp)return Pi(t.cm,bs)(t,e,n,r,i);var o=new ys(t,i),s=ce(e,n);if(r&&I(r,o,!1),s>0||0==s&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=D("span",[o.replacedWith],"CodeMirror-widget"),r.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),r.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(tn(t,e.line,e,n,o)||e.line!=n.line&&tn(t,n.line,e,n,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");Pe()}o.addToHistory&&Oo(t,{from:e,to:n,origin:"markText"},t.sel,NaN);var l,a=e.line,c=t.cm;if(t.iter(a,n.line+1,(function(r){c&&o.collapsed&&!c.options.lineWrapping&&en(r)==c.display.maxLine&&(l=!0),o.collapsed&&a!=e.line&&re(r,0),Fe(r,new He(o,a==e.line?e.ch:null,a==n.line?n.ch:null),t.cm&&t.cm.curOp),++a})),o.collapsed&&t.iter(e.line,n.line+1,(function(e){ln(t,e)&&re(e,0)})),o.clearOnEnter&&vt(o,"beforeCursorEnter",(function(){return o.clear()})),o.readOnly&&(We(),(t.history.done.length||t.history.undone.length)&&t.clearHistory()),o.collapsed&&(o.id=++vs,o.atomic=!0),c){if(l&&(c.curOp.updateMaxLine=!0),o.collapsed)Ir(c,e.line,n.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var h=e.line;h<=n.line;h++)Br(c,h,"text");o.atomic&&qo(c.doc),An(c,"markerAdded",c,o)}return o}ys.prototype.clear=function(){if(!this.explicitlyCleared){var t=this.doc.cm,e=t&&!t.curOp;if(e&&Li(t),Ct(this,"clear")){var n=this.find();n&&An(this,"clear",n.from,n.to)}for(var r=null,i=null,o=0;o<this.lines.length;++o){var s=this.lines[o],l=ze(s.markedSpans,this);t&&!this.collapsed?Br(t,ie(s),"text"):t&&(null!=l.to&&(i=ie(s)),null!=l.from&&(r=ie(s))),s.markedSpans=Re(s.markedSpans,l),null==l.from&&this.collapsed&&!ln(this.doc,s)&&t&&re(s,Ar(t.display))}if(t&&this.collapsed&&!t.options.lineWrapping)for(var a=0;a<this.lines.length;++a){var c=en(this.lines[a]),h=hn(c);h>t.display.maxLineLength&&(t.display.maxLine=c,t.display.maxLineLength=h,t.display.maxLineChanged=!0)}null!=r&&t&&this.collapsed&&Ir(t,r,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,t&&qo(t.doc)),t&&An(t,"markerCleared",t,this,r,i),e&&Mi(t),this.parent&&this.parent.clear()}},ys.prototype.find=function(t,e){var n,r;null==t&&"bookmark"==this.type&&(t=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],s=ze(o.markedSpans,this);if(null!=s.from&&(n=ae(e?o:ie(o),s.from),-1==t))return n;if(null!=s.to&&(r=ae(e?o:ie(o),s.to),1==t))return r}return n&&{from:n,to:r}},ys.prototype.changed=function(){var t=this,e=this.find(-1,!0),n=this,r=this.doc.cm;e&&r&&Wi(r,(function(){var i=e.line,o=ie(e.line),s=ir(r,o);if(s&&(fr(s),r.curOp.selectionChanged=r.curOp.forceUpdate=!0),r.curOp.updateMaxLine=!0,!ln(n.doc,i)&&null!=n.height){var l=n.height;n.height=null;var a=jn(n)-l;a&&re(i,i.height+a)}An(r,"markerChanged",r,t)}))},ys.prototype.attachLine=function(t){if(!this.lines.length&&this.doc.cm){var e=this.doc.cm.curOp;e.maybeHiddenMarkers&&-1!=$(e.maybeHiddenMarkers,this)||(e.maybeUnhiddenMarkers||(e.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(t)},ys.prototype.detachLine=function(t){if(this.lines.splice($(this.lines,t),1),!this.lines.length&&this.doc.cm){var e=this.doc.cm.curOp;(e.maybeHiddenMarkers||(e.maybeHiddenMarkers=[])).push(this)}},kt(ys);var ws=function(t,e){this.markers=t,this.primary=e;for(var n=0;n<t.length;++n)t[n].parent=this};function xs(t,e,n,r,i){(r=I(r)).shared=!1;var o=[bs(t,e,n,r,i)],s=o[0],l=r.widgetNode;return xo(t,(function(t){l&&(r.widgetNode=l.cloneNode(!0)),o.push(bs(t,ge(t,e),ge(t,n),r,i));for(var a=0;a<t.linked.length;++a)if(t.linked[a].isParent)return;s=J(o)})),new ws(o,s)}function _s(t){return t.findMarks(ae(t.first,0),t.clipPos(ae(t.lastLine())),(function(t){return t.parent}))}function Cs(t,e){for(var n=0;n<e.length;n++){var r=e[n],i=r.find(),o=t.clipPos(i.from),s=t.clipPos(i.to);if(ce(o,s)){var l=bs(t,o,s,r.primary,r.primary.type);r.markers.push(l),l.parent=r}}}function ks(t){for(var e=function(e){var n=t[e],r=[n.primary.doc];xo(n.primary.doc,(function(t){return r.push(t)}));for(var i=0;i<n.markers.length;i++){var o=n.markers[i];-1==$(r,o.doc)&&(o.parent=null,n.markers.splice(i--,1))}},n=0;n<t.length;n++)e(n)}ws.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var t=0;t<this.markers.length;++t)this.markers[t].clear();An(this,"clear")}},ws.prototype.find=function(t,e){return this.primary.find(t,e)},kt(ws);var Ss=0,Ls=function(t,e,n,r,i){if(!(this instanceof Ls))return new Ls(t,e,n,r,i);null==n&&(n=0),fs.call(this,[new ds([new dn("",null)])]),this.first=n,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=n;var o=ae(n,0);this.sel=ho(o),this.history=new So(null),this.id=++Ss,this.modeOption=e,this.lineSep=r,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof t&&(t=this.splitLines(t)),wo(this,{from:o,to:o,text:t}),Go(this,ho(o),j)};Ls.prototype=nt(fs.prototype,{constructor:Ls,iter:function(t,e,n){n?this.iterN(t-this.first,e-t,n):this.iterN(this.first,this.first+this.size,t)},insert:function(t,e){for(var n=0,r=0;r<e.length;++r)n+=e[r].height;this.insertInner(t-this.first,e,n)},remove:function(t,e){this.removeInner(t-this.first,e)},getValue:function(t){var e=ne(this,this.first,this.first+this.size);return!1===t?e:e.join(t||this.lineSeparator())},setValue:zi((function(t){var e=ae(this.first,0),n=this.first+this.size-1;es(this,{from:e,to:ae(n,te(this,n).text.length),text:this.splitLines(t),origin:"setValue",full:!0},!0),this.cm&&ui(this.cm,0,0),Go(this,ho(e),j)})),replaceRange:function(t,e,n,r){ls(this,t,e=ge(this,e),n=n?ge(this,n):e,r)},getRange:function(t,e,n){var r=ee(this,ge(this,t),ge(this,e));return!1===n?r:""===n?r.join(""):r.join(n||this.lineSeparator())},getLine:function(t){var e=this.getLineHandle(t);return e&&e.text},getLineHandle:function(t){if(se(this,t))return te(this,t)},getLineNumber:function(t){return ie(t)},getLineHandleVisualStart:function(t){return"number"==typeof t&&(t=te(this,t)),en(t)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(t){return ge(this,t)},getCursor:function(t){var e=this.sel.primary();return null==t||"head"==t?e.head:"anchor"==t?e.anchor:"end"==t||"to"==t||!1===t?e.to():e.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:zi((function(t,e,n){Vo(this,ge(this,"number"==typeof t?ae(t,e||0):t),null,n)})),setSelection:zi((function(t,e,n){Vo(this,ge(this,t),ge(this,e||t),n)})),extendSelection:zi((function(t,e,n){Fo(this,ge(this,t),e&&ge(this,e),n)})),extendSelections:zi((function(t,e){Io(this,ve(this,t),e)})),extendSelectionsBy:zi((function(t,e){Io(this,ve(this,Q(this.sel.ranges,t)),e)})),setSelections:zi((function(t,e,n){if(t.length){for(var r=[],i=0;i<t.length;i++)r[i]=new ao(ge(this,t[i].anchor),ge(this,t[i].head||t[i].anchor));null==e&&(e=Math.min(t.length-1,this.sel.primIndex)),Go(this,co(this.cm,r,e),n)}})),addSelection:zi((function(t,e,n){var r=this.sel.ranges.slice(0);r.push(new ao(ge(this,t),ge(this,e||t))),Go(this,co(this.cm,r,r.length-1),n)})),getSelection:function(t){for(var e,n=this.sel.ranges,r=0;r<n.length;r++){var i=ee(this,n[r].from(),n[r].to());e=e?e.concat(i):i}return!1===t?e:e.join(t||this.lineSeparator())},getSelections:function(t){for(var e=[],n=this.sel.ranges,r=0;r<n.length;r++){var i=ee(this,n[r].from(),n[r].to());!1!==t&&(i=i.join(t||this.lineSeparator())),e[r]=i}return e},replaceSelection:function(t,e,n){for(var r=[],i=0;i<this.sel.ranges.length;i++)r[i]=t;this.replaceSelections(r,e,n||"+input")},replaceSelections:zi((function(t,e,n){for(var r=[],i=this.sel,o=0;o<i.ranges.length;o++){var s=i.ranges[o];r[o]={from:s.from(),to:s.to(),text:this.splitLines(t[o]),origin:n}}for(var l=e&&"end"!=e&&mo(this,r,e),a=r.length-1;a>=0;a--)es(this,r[a]);l?Uo(this,l):this.cm&&hi(this.cm)})),undo:zi((function(){rs(this,"undo")})),redo:zi((function(){rs(this,"redo")})),undoSelection:zi((function(){rs(this,"undo",!0)})),redoSelection:zi((function(){rs(this,"redo",!0)})),setExtending:function(t){this.extend=t},getExtending:function(){return this.extend},historySize:function(){for(var t=this.history,e=0,n=0,r=0;r<t.done.length;r++)t.done[r].ranges||++e;for(var i=0;i<t.undone.length;i++)t.undone[i].ranges||++n;return{undo:e,redo:n}},clearHistory:function(){var t=this;this.history=new So(this.history),xo(this,(function(e){return e.history=t.history}),!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(t){return t&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(t){return this.history.generation==(t||this.cleanGeneration)},getHistory:function(){return{done:zo(this.history.done),undone:zo(this.history.undone)}},setHistory:function(t){var e=this.history=new So(this.history);e.done=zo(t.done.slice(0),null,!0),e.undone=zo(t.undone.slice(0),null,!0)},setGutterMarker:zi((function(t,e,n){return us(this,t,"gutter",(function(t){var r=t.gutterMarkers||(t.gutterMarkers={});return r[e]=n,!n&&st(r)&&(t.gutterMarkers=null),!0}))})),clearGutter:zi((function(t){var e=this;this.iter((function(n){n.gutterMarkers&&n.gutterMarkers[t]&&us(e,n,"gutter",(function(){return n.gutterMarkers[t]=null,st(n.gutterMarkers)&&(n.gutterMarkers=null),!0}))}))})),lineInfo:function(t){var e;if("number"==typeof t){if(!se(this,t))return null;if(e=t,!(t=te(this,t)))return null}else if(null==(e=ie(t)))return null;return{line:e,handle:t,text:t.text,gutterMarkers:t.gutterMarkers,textClass:t.textClass,bgClass:t.bgClass,wrapClass:t.wrapClass,widgets:t.widgets}},addLineClass:zi((function(t,e,n){return us(this,t,"gutter"==e?"gutter":"class",(function(t){var r="text"==e?"textClass":"background"==e?"bgClass":"gutter"==e?"gutterClass":"wrapClass";if(t[r]){if(S(n).test(t[r]))return!1;t[r]+=" "+n}else t[r]=n;return!0}))})),removeLineClass:zi((function(t,e,n){return us(this,t,"gutter"==e?"gutter":"class",(function(t){var r="text"==e?"textClass":"background"==e?"bgClass":"gutter"==e?"gutterClass":"wrapClass",i=t[r];if(!i)return!1;if(null==n)t[r]=null;else{var o=i.match(S(n));if(!o)return!1;var s=o.index+o[0].length;t[r]=i.slice(0,o.index)+(o.index&&s!=i.length?" ":"")+i.slice(s)||null}return!0}))})),addLineWidget:zi((function(t,e,n){return ms(this,t,e,n)})),removeLineWidget:function(t){t.clear()},markText:function(t,e,n){return bs(this,ge(this,t),ge(this,e),n,n&&n.type||"range")},setBookmark:function(t,e){var n={replacedWith:e&&(null==e.nodeType?e.widget:e),insertLeft:e&&e.insertLeft,clearWhenEmpty:!1,shared:e&&e.shared,handleMouseEvents:e&&e.handleMouseEvents};return bs(this,t=ge(this,t),t,n,"bookmark")},findMarksAt:function(t){var e=[],n=te(this,(t=ge(this,t)).line).markedSpans;if(n)for(var r=0;r<n.length;++r){var i=n[r];(null==i.from||i.from<=t.ch)&&(null==i.to||i.to>=t.ch)&&e.push(i.marker.parent||i.marker)}return e},findMarks:function(t,e,n){t=ge(this,t),e=ge(this,e);var r=[],i=t.line;return this.iter(t.line,e.line+1,(function(o){var s=o.markedSpans;if(s)for(var l=0;l<s.length;l++){var a=s[l];null!=a.to&&i==t.line&&t.ch>=a.to||null==a.from&&i!=t.line||null!=a.from&&i==e.line&&a.from>=e.ch||n&&!n(a.marker)||r.push(a.marker.parent||a.marker)}++i})),r},getAllMarks:function(){var t=[];return this.iter((function(e){var n=e.markedSpans;if(n)for(var r=0;r<n.length;++r)null!=n[r].from&&t.push(n[r].marker)})),t},posFromIndex:function(t){var e,n=this.first,r=this.lineSeparator().length;return this.iter((function(i){var o=i.text.length+r;if(o>t)return e=t,!0;t-=o,++n})),ge(this,ae(n,e))},indexFromPos:function(t){var e=(t=ge(this,t)).ch;if(t.line<this.first||t.ch<0)return 0;var n=this.lineSeparator().length;return this.iter(this.first,t.line,(function(t){e+=t.text.length+n})),e},copy:function(t){var e=new Ls(ne(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return e.scrollTop=this.scrollTop,e.scrollLeft=this.scrollLeft,e.sel=this.sel,e.extend=!1,t&&(e.history.undoDepth=this.history.undoDepth,e.setHistory(this.getHistory())),e},linkedDoc:function(t){t||(t={});var e=this.first,n=this.first+this.size;null!=t.from&&t.from>e&&(e=t.from),null!=t.to&&t.to<n&&(n=t.to);var r=new Ls(ne(this,e,n),t.mode||this.modeOption,e,this.lineSep,this.direction);return t.sharedHist&&(r.history=this.history),(this.linked||(this.linked=[])).push({doc:r,sharedHist:t.sharedHist}),r.linked=[{doc:this,isParent:!0,sharedHist:t.sharedHist}],Cs(r,_s(this)),r},unlinkDoc:function(t){if(t instanceof Il&&(t=t.doc),this.linked)for(var e=0;e<this.linked.length;++e)if(this.linked[e].doc==t){this.linked.splice(e,1),t.unlinkDoc(this),ks(_s(this));break}if(t.history==this.history){var n=[t.id];xo(t,(function(t){return n.push(t.id)}),!0),t.history=new So(null),t.history.done=zo(this.history.done,n),t.history.undone=zo(this.history.undone,n)}},iterLinkedDocs:function(t){xo(this,t)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(t){return this.lineSep?t.split(this.lineSep):zt(t)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:zi((function(t){"rtl"!=t&&(t="ltr"),t!=this.direction&&(this.direction=t,this.iter((function(t){return t.order=null})),this.cm&&ko(this.cm))}))}),Ls.prototype.eachLine=Ls.prototype.iter;var Ms=0;function Ts(t){var e=this;if(Ds(e),!xt(e,t)&&!Kn(e.display,t)){St(t),s&&(Ms=+new Date);var n=Rr(e,t,!0),r=t.dataTransfer.files;if(n&&!e.isReadOnly())if(r&&r.length&&window.FileReader&&window.File)for(var i=r.length,o=Array(i),l=0,a=function(){++l==i&&Pi(e,(function(){var t={from:n=ge(e.doc,n),to:n,text:e.doc.splitLines(o.filter((function(t){return null!=t})).join(e.doc.lineSeparator())),origin:"paste"};es(e.doc,t),Uo(e.doc,ho(ge(e.doc,n),ge(e.doc,uo(t))))}))()},c=function(t,n){if(e.options.allowDropFileTypes&&-1==$(e.options.allowDropFileTypes,t.type))a();else{var r=new FileReader;r.onerror=function(){return a()},r.onload=function(){var t=r.result;/[\x00-\x08\x0e-\x1f]{2}/.test(t)||(o[n]=t),a()},r.readAsText(t)}},h=0;h<r.length;h++)c(r[h],h);else{if(e.state.draggingText&&e.doc.sel.contains(n)>-1)return e.state.draggingText(t),void setTimeout((function(){return e.display.input.focus()}),20);try{var u=t.dataTransfer.getData("Text");if(u){var d;if(e.state.draggingText&&!e.state.draggingText.copy&&(d=e.listSelections()),jo(e.doc,ho(n,n)),d)for(var f=0;f<d.length;++f)ls(e.doc,"",d[f].anchor,d[f].head,"drag");e.replaceSelection(u,"around","paste"),e.display.input.focus()}}catch(t){}}}}function Os(t,e){if(s&&(!t.state.draggingText||+new Date-Ms<100))Tt(e);else if(!xt(t,e)&&!Kn(t.display,e)&&(e.dataTransfer.setData("Text",t.getSelection()),e.dataTransfer.effectAllowed="copyMove",e.dataTransfer.setDragImage&&!f)){var n=N("img",null,null,"position: fixed; left: 0; top: 0;");n.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",d&&(n.width=n.height=1,t.display.wrapper.appendChild(n),n._top=n.offsetTop),e.dataTransfer.setDragImage(n,0,0),d&&n.parentNode.removeChild(n)}}function Ns(t,e){var n=Rr(t,e);if(n){var r=document.createDocumentFragment();qr(t,n,r),t.display.dragCursor||(t.display.dragCursor=N("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),t.display.lineSpace.insertBefore(t.display.dragCursor,t.display.cursorDiv)),O(t.display.dragCursor,r)}}function Ds(t){t.display.dragCursor&&(t.display.lineSpace.removeChild(t.display.dragCursor),t.display.dragCursor=null)}function As(t){if(document.getElementsByClassName){for(var e=document.getElementsByClassName("CodeMirror"),n=[],r=0;r<e.length;r++){var i=e[r].CodeMirror;i&&n.push(i)}n.length&&n[0].operation((function(){for(var e=0;e<n.length;e++)t(n[e])}))}}var Es=!1;function Ws(){Es||(Ps(),Es=!0)}function Ps(){var t;vt(window,"resize",(function(){null==t&&(t=setTimeout((function(){t=null,As(Hs)}),100))})),vt(window,"blur",(function(){return As(ei)}))}function Hs(t){var e=t.display;e.cachedCharWidth=e.cachedTextHeight=e.cachedPaddingH=null,e.scrollbarsClipped=!1,t.setSize()}for(var zs={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Rs=0;Rs<10;Rs++)zs[Rs+48]=zs[Rs+96]=String(Rs);for(var Fs=65;Fs<=90;Fs++)zs[Fs]=String.fromCharCode(Fs);for(var Is=1;Is<=12;Is++)zs[Is+111]=zs[Is+63235]="F"+Is;var Bs={};function Vs(t){var e,n,r,i,o=t.split(/-(?!$)/);t=o[o.length-1];for(var s=0;s<o.length-1;s++){var l=o[s];if(/^(cmd|meta|m)$/i.test(l))i=!0;else if(/^a(lt)?$/i.test(l))e=!0;else if(/^(c|ctrl|control)$/i.test(l))n=!0;else{if(!/^s(hift)?$/i.test(l))throw new Error("Unrecognized modifier name: "+l);r=!0}}return e&&(t="Alt-"+t),n&&(t="Ctrl-"+t),i&&(t="Cmd-"+t),r&&(t="Shift-"+t),t}function $s(t){var e={};for(var n in t)if(t.hasOwnProperty(n)){var r=t[n];if(/^(name|fallthrough|(de|at)tach)$/.test(n))continue;if("..."==r){delete t[n];continue}for(var i=Q(n.split(" "),Vs),o=0;o<i.length;o++){var s=void 0,l=void 0;o==i.length-1?(l=i.join(" "),s=r):(l=i.slice(0,o+1).join(" "),s="...");var a=e[l];if(a){if(a!=s)throw new Error("Inconsistent bindings for "+l)}else e[l]=s}delete t[n]}for(var c in e)t[c]=e[c];return t}function Us(t,e,n,r){var i=(e=qs(e)).call?e.call(t,r):e[t];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&n(i))return"handled";if(e.fallthrough){if("[object Array]"!=Object.prototype.toString.call(e.fallthrough))return Us(t,e.fallthrough,n,r);for(var o=0;o<e.fallthrough.length;o++){var s=Us(t,e.fallthrough[o],n,r);if(s)return s}}}function Gs(t){var e="string"==typeof t?t:zs[t.keyCode];return"Ctrl"==e||"Alt"==e||"Shift"==e||"Mod"==e}function js(t,e,n){var r=t;return e.altKey&&"Alt"!=r&&(t="Alt-"+t),(C?e.metaKey:e.ctrlKey)&&"Ctrl"!=r&&(t="Ctrl-"+t),(C?e.ctrlKey:e.metaKey)&&"Mod"!=r&&(t="Cmd-"+t),!n&&e.shiftKey&&"Shift"!=r&&(t="Shift-"+t),t}function Ks(t,e){if(d&&34==t.keyCode&&t.char)return!1;var n=zs[t.keyCode];return null!=n&&!t.altGraphKey&&(3==t.keyCode&&t.code&&(n=t.code),js(n,t,e))}function qs(t){return"string"==typeof t?Bs[t]:t}function Xs(t,e){for(var n=t.doc.sel.ranges,r=[],i=0;i<n.length;i++){for(var o=e(n[i]);r.length&&ce(o.from,J(r).to)<=0;){var s=r.pop();if(ce(s.from,o.from)<0){o.from=s.from;break}}r.push(o)}Wi(t,(function(){for(var e=r.length-1;e>=0;e--)ls(t.doc,"",r[e].from,r[e].to,"+delete");hi(t)}))}function Ys(t,e,n){var r=ct(t.text,e+n,n);return r<0||r>t.text.length?null:r}function Zs(t,e,n){var r=Ys(t,e.ch,n);return null==r?null:new ae(e.line,r,n<0?"after":"before")}function Js(t,e,n,r,i){if(t){"rtl"==e.doc.direction&&(i=-i);var o=gt(n,e.doc.direction);if(o){var s,l=i<0?J(o):o[0],a=i<0==(1==l.level)?"after":"before";if(l.level>0||"rtl"==e.doc.direction){var c=or(e,n);s=i<0?n.text.length-1:0;var h=sr(e,c,s).top;s=ht((function(t){return sr(e,c,t).top==h}),i<0==(1==l.level)?l.from:l.to-1,s),"before"==a&&(s=Ys(n,s,1))}else s=i<0?l.to:l.from;return new ae(r,s,a)}}return new ae(r,i<0?n.text.length:0,i<0?"before":"after")}function Qs(t,e,n,r){var i=gt(e,t.doc.direction);if(!i)return Zs(e,n,r);n.ch>=e.text.length?(n.ch=e.text.length,n.sticky="before"):n.ch<=0&&(n.ch=0,n.sticky="after");var o=ft(i,n.ch,n.sticky),s=i[o];if("ltr"==t.doc.direction&&s.level%2==0&&(r>0?s.to>n.ch:s.from<n.ch))return Zs(e,n,r);var l,a=function(t,n){return Ys(e,t instanceof ae?t.ch:t,n)},c=function(n){return t.options.lineWrapping?(l=l||or(t,e),Mr(t,e,l,n)):{begin:0,end:e.text.length}},h=c("before"==n.sticky?a(n,-1):n.ch);if("rtl"==t.doc.direction||1==s.level){var u=1==s.level==r<0,d=a(n,u?1:-1);if(null!=d&&(u?d<=s.to&&d<=h.end:d>=s.from&&d>=h.begin)){var f=u?"before":"after";return new ae(n.line,d,f)}}var p=function(t,e,r){for(var o=function(t,e){return e?new ae(n.line,a(t,1),"before"):new ae(n.line,t,"after")};t>=0&&t<i.length;t+=e){var s=i[t],l=e>0==(1!=s.level),c=l?r.begin:a(r.end,-1);if(s.from<=c&&c<s.to)return o(c,l);if(c=l?s.from:a(s.to,-1),r.begin<=c&&c<r.end)return o(c,l)}},g=p(o+r,r,h);if(g)return g;var m=r>0?h.end:a(h.begin,-1);return null==m||r>0&&m==e.text.length||!(g=p(r>0?0:i.length-1,r,c(m)))?null:g}Bs.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Bs.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Bs.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Bs.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Bs.default=b?Bs.macDefault:Bs.pcDefault;var tl={selectAll:Qo,singleSelection:function(t){return t.setSelection(t.getCursor("anchor"),t.getCursor("head"),j)},killLine:function(t){return Xs(t,(function(e){if(e.empty()){var n=te(t.doc,e.head.line).text.length;return e.head.ch==n&&e.head.line<t.lastLine()?{from:e.head,to:ae(e.head.line+1,0)}:{from:e.head,to:ae(e.head.line,n)}}return{from:e.from(),to:e.to()}}))},deleteLine:function(t){return Xs(t,(function(e){return{from:ae(e.from().line,0),to:ge(t.doc,ae(e.to().line+1,0))}}))},delLineLeft:function(t){return Xs(t,(function(t){return{from:ae(t.from().line,0),to:t.from()}}))},delWrappedLineLeft:function(t){return Xs(t,(function(e){var n=t.charCoords(e.head,"div").top+5;return{from:t.coordsChar({left:0,top:n},"div"),to:e.from()}}))},delWrappedLineRight:function(t){return Xs(t,(function(e){var n=t.charCoords(e.head,"div").top+5,r=t.coordsChar({left:t.display.lineDiv.offsetWidth+100,top:n},"div");return{from:e.from(),to:r}}))},undo:function(t){return t.undo()},redo:function(t){return t.redo()},undoSelection:function(t){return t.undoSelection()},redoSelection:function(t){return t.redoSelection()},goDocStart:function(t){return t.extendSelection(ae(t.firstLine(),0))},goDocEnd:function(t){return t.extendSelection(ae(t.lastLine()))},goLineStart:function(t){return t.extendSelectionsBy((function(e){return el(t,e.head.line)}),{origin:"+move",bias:1})},goLineStartSmart:function(t){return t.extendSelectionsBy((function(e){return rl(t,e.head)}),{origin:"+move",bias:1})},goLineEnd:function(t){return t.extendSelectionsBy((function(e){return nl(t,e.head.line)}),{origin:"+move",bias:-1})},goLineRight:function(t){return t.extendSelectionsBy((function(e){var n=t.cursorCoords(e.head,"div").top+5;return t.coordsChar({left:t.display.lineDiv.offsetWidth+100,top:n},"div")}),q)},goLineLeft:function(t){return t.extendSelectionsBy((function(e){var n=t.cursorCoords(e.head,"div").top+5;return t.coordsChar({left:0,top:n},"div")}),q)},goLineLeftSmart:function(t){return t.extendSelectionsBy((function(e){var n=t.cursorCoords(e.head,"div").top+5,r=t.coordsChar({left:0,top:n},"div");return r.ch<t.getLine(r.line).search(/\S/)?rl(t,e.head):r}),q)},goLineUp:function(t){return t.moveV(-1,"line")},goLineDown:function(t){return t.moveV(1,"line")},goPageUp:function(t){return t.moveV(-1,"page")},goPageDown:function(t){return t.moveV(1,"page")},goCharLeft:function(t){return t.moveH(-1,"char")},goCharRight:function(t){return t.moveH(1,"char")},goColumnLeft:function(t){return t.moveH(-1,"column")},goColumnRight:function(t){return t.moveH(1,"column")},goWordLeft:function(t){return t.moveH(-1,"word")},goGroupRight:function(t){return t.moveH(1,"group")},goGroupLeft:function(t){return t.moveH(-1,"group")},goWordRight:function(t){return t.moveH(1,"word")},delCharBefore:function(t){return t.deleteH(-1,"codepoint")},delCharAfter:function(t){return t.deleteH(1,"char")},delWordBefore:function(t){return t.deleteH(-1,"word")},delWordAfter:function(t){return t.deleteH(1,"word")},delGroupBefore:function(t){return t.deleteH(-1,"group")},delGroupAfter:function(t){return t.deleteH(1,"group")},indentAuto:function(t){return t.indentSelection("smart")},indentMore:function(t){return t.indentSelection("add")},indentLess:function(t){return t.indentSelection("subtract")},insertTab:function(t){return t.replaceSelection("\t")},insertSoftTab:function(t){for(var e=[],n=t.listSelections(),r=t.options.tabSize,i=0;i<n.length;i++){var o=n[i].from(),s=B(t.getLine(o.line),o.ch,r);e.push(Z(r-s%r))}t.replaceSelections(e)},defaultTab:function(t){t.somethingSelected()?t.indentSelection("add"):t.execCommand("insertTab")},transposeChars:function(t){return Wi(t,(function(){for(var e=t.listSelections(),n=[],r=0;r<e.length;r++)if(e[r].empty()){var i=e[r].head,o=te(t.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new ae(i.line,i.ch-1)),i.ch>0)i=new ae(i.line,i.ch+1),t.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),ae(i.line,i.ch-2),i,"+transpose");else if(i.line>t.doc.first){var s=te(t.doc,i.line-1).text;s&&(i=new ae(i.line,1),t.replaceRange(o.charAt(0)+t.doc.lineSeparator()+s.charAt(s.length-1),ae(i.line-1,s.length-1),i,"+transpose"))}n.push(new ao(i,i))}t.setSelections(n)}))},newlineAndIndent:function(t){return Wi(t,(function(){for(var e=t.listSelections(),n=e.length-1;n>=0;n--)t.replaceRange(t.doc.lineSeparator(),e[n].anchor,e[n].head,"+input");e=t.listSelections();for(var r=0;r<e.length;r++)t.indentLine(e[r].from().line,null,!0);hi(t)}))},openLine:function(t){return t.replaceSelection("\n","start")},toggleOverwrite:function(t){return t.toggleOverwrite()}};function el(t,e){var n=te(t.doc,e),r=en(n);return r!=n&&(e=ie(r)),Js(!0,t,r,e,1)}function nl(t,e){var n=te(t.doc,e),r=nn(n);return r!=n&&(e=ie(r)),Js(!0,t,n,e,-1)}function rl(t,e){var n=el(t,e.line),r=te(t.doc,n.line),i=gt(r,t.doc.direction);if(!i||0==i[0].level){var o=Math.max(n.ch,r.text.search(/\S/)),s=e.line==n.line&&e.ch<=o&&e.ch;return ae(n.line,s?0:o,n.sticky)}return n}function il(t,e,n){if("string"==typeof e&&!(e=tl[e]))return!1;t.display.input.ensurePolled();var r=t.display.shift,i=!1;try{t.isReadOnly()&&(t.state.suppressEdits=!0),n&&(t.display.shift=!1),i=e(t)!=G}finally{t.display.shift=r,t.state.suppressEdits=!1}return i}function ol(t,e,n){for(var r=0;r<t.state.keyMaps.length;r++){var i=Us(e,t.state.keyMaps[r],n,t);if(i)return i}return t.options.extraKeys&&Us(e,t.options.extraKeys,n,t)||Us(e,t.options.keyMap,n,t)}var sl=new V;function ll(t,e,n,r){var i=t.state.keySeq;if(i){if(Gs(e))return"handled";if(/\'$/.test(e)?t.state.keySeq=null:sl.set(50,(function(){t.state.keySeq==i&&(t.state.keySeq=null,t.display.input.reset())})),al(t,i+" "+e,n,r))return!0}return al(t,e,n,r)}function al(t,e,n,r){var i=ol(t,e,r);return"multi"==i&&(t.state.keySeq=e),"handled"==i&&An(t,"keyHandled",t,e,n),"handled"!=i&&"multi"!=i||(St(n),Zr(t)),!!i}function cl(t,e){var n=Ks(e,!0);return!!n&&(e.shiftKey&&!t.state.keySeq?ll(t,"Shift-"+n,e,(function(e){return il(t,e,!0)}))||ll(t,n,e,(function(e){if("string"==typeof e?/^go[A-Z]/.test(e):e.motion)return il(t,e)})):ll(t,n,e,(function(e){return il(t,e)})))}function hl(t,e,n){return ll(t,"'"+n+"'",e,(function(e){return il(t,e,!0)}))}var ul=null;function dl(t){var e=this;if(!(t.target&&t.target!=e.display.input.getField()||(e.curOp.focus=E(z(e)),xt(e,t)))){s&&l<11&&27==t.keyCode&&(t.returnValue=!1);var r=t.keyCode;e.display.shift=16==r||t.shiftKey;var i=cl(e,t);d&&(ul=i?r:null,i||88!=r||Ft||!(b?t.metaKey:t.ctrlKey)||e.replaceSelection("",null,"cut")),n&&!b&&!i&&46==r&&t.shiftKey&&!t.ctrlKey&&document.execCommand&&document.execCommand("cut"),18!=r||/\bCodeMirror-crosshair\b/.test(e.display.lineDiv.className)||fl(e)}}function fl(t){var e=t.display.lineDiv;function n(t){18!=t.keyCode&&t.altKey||(M(e,"CodeMirror-crosshair"),bt(document,"keyup",n),bt(document,"mouseover",n))}W(e,"CodeMirror-crosshair"),vt(document,"keyup",n),vt(document,"mouseover",n)}function pl(t){16==t.keyCode&&(this.doc.sel.shift=!1),xt(this,t)}function gl(t){var e=this;if(!(t.target&&t.target!=e.display.input.getField()||Kn(e.display,t)||xt(e,t)||t.ctrlKey&&!t.altKey||b&&t.metaKey)){var n=t.keyCode,r=t.charCode;if(d&&n==ul)return ul=null,void St(t);if(!d||t.which&&!(t.which<10)||!cl(e,t)){var i=String.fromCharCode(null==r?n:r);"\b"!=i&&(hl(e,t,i)||e.display.input.onKeyPress(t))}}}var ml,vl,yl=400,bl=function(t,e,n){this.time=t,this.pos=e,this.button=n};function wl(t,e){var n=+new Date;return vl&&vl.compare(n,t,e)?(ml=vl=null,"triple"):ml&&ml.compare(n,t,e)?(vl=new bl(n,t,e),ml=null,"double"):(ml=new bl(n,t,e),vl=null,"single")}function xl(t){var e=this,n=e.display;if(!(xt(e,t)||n.activeTouch&&n.input.supportsTouch()))if(n.input.ensurePolled(),n.shift=t.shiftKey,Kn(n,t))a||(n.scroller.draggable=!1,setTimeout((function(){return n.scroller.draggable=!0}),100));else if(!Nl(e,t)){var r=Rr(e,t),i=Nt(t),o=r?wl(r,i):"single";R(e).focus(),1==i&&e.state.selectingText&&e.state.selectingText(t),r&&_l(e,i,r,o,t)||(1==i?r?kl(e,r,o,t):Ot(t)==n.scroller&&St(t):2==i?(r&&Fo(e.doc,r),setTimeout((function(){return n.input.focus()}),20)):3==i&&(k?e.display.input.onContextMenu(t):Qr(e)))}}function _l(t,e,n,r,i){var o="Click";return"double"==r?o="Double"+o:"triple"==r&&(o="Triple"+o),ll(t,js(o=(1==e?"Left":2==e?"Middle":"Right")+o,i),i,(function(e){if("string"==typeof e&&(e=tl[e]),!e)return!1;var r=!1;try{t.isReadOnly()&&(t.state.suppressEdits=!0),r=e(t,n)!=G}finally{t.state.suppressEdits=!1}return r}))}function Cl(t,e,n){var r=t.getOption("configureMouse"),i=r?r(t,e,n):{};if(null==i.unit){var o=w?n.shiftKey&&n.metaKey:n.altKey;i.unit=o?"rectangle":"single"==e?"char":"double"==e?"word":"line"}return(null==i.extend||t.doc.extend)&&(i.extend=t.doc.extend||n.shiftKey),null==i.addNew&&(i.addNew=b?n.metaKey:n.ctrlKey),null==i.moveOnDrag&&(i.moveOnDrag=!(b?n.altKey:n.ctrlKey)),i}function kl(t,e,n,r){s?setTimeout(F(Jr,t),0):t.curOp.focus=E(z(t));var i,o=Cl(t,n,r),l=t.doc.sel;t.options.dragDrop&&Et&&!t.isReadOnly()&&"single"==n&&(i=l.contains(e))>-1&&(ce((i=l.ranges[i]).from(),e)<0||e.xRel>0)&&(ce(i.to(),e)>0||e.xRel<0)?Sl(t,r,e,o):Ml(t,r,e,o)}function Sl(t,e,n,r){var i=t.display,o=!1,c=Pi(t,(function(e){a&&(i.scroller.draggable=!1),t.state.draggingText=!1,t.state.delayingBlurEvent&&(t.hasFocus()?t.state.delayingBlurEvent=!1:Qr(t)),bt(i.wrapper.ownerDocument,"mouseup",c),bt(i.wrapper.ownerDocument,"mousemove",h),bt(i.scroller,"dragstart",u),bt(i.scroller,"drop",c),o||(St(e),r.addNew||Fo(t.doc,n,null,null,r.extend),a&&!f||s&&9==l?setTimeout((function(){i.wrapper.ownerDocument.body.focus({preventScroll:!0}),i.input.focus()}),20):i.input.focus())})),h=function(t){o=o||Math.abs(e.clientX-t.clientX)+Math.abs(e.clientY-t.clientY)>=10},u=function(){return o=!0};a&&(i.scroller.draggable=!0),t.state.draggingText=c,c.copy=!r.moveOnDrag,vt(i.wrapper.ownerDocument,"mouseup",c),vt(i.wrapper.ownerDocument,"mousemove",h),vt(i.scroller,"dragstart",u),vt(i.scroller,"drop",c),t.state.delayingBlurEvent=!0,setTimeout((function(){return i.input.focus()}),20),i.scroller.dragDrop&&i.scroller.dragDrop()}function Ll(t,e,n){if("char"==n)return new ao(e,e);if("word"==n)return t.findWordAt(e);if("line"==n)return new ao(ae(e.line,0),ge(t.doc,ae(e.line+1,0)));var r=n(t,e);return new ao(r.from,r.to)}function Ml(t,e,n,r){s&&Qr(t);var i=t.display,o=t.doc;St(e);var l,a,c=o.sel,h=c.ranges;if(r.addNew&&!r.extend?(a=o.sel.contains(n),l=a>-1?h[a]:new ao(n,n)):(l=o.sel.primary(),a=o.sel.primIndex),"rectangle"==r.unit)r.addNew||(l=new ao(n,n)),n=Rr(t,e,!0,!0),a=-1;else{var u=Ll(t,n,r.unit);l=r.extend?Ro(l,u.anchor,u.head,r.extend):u}r.addNew?-1==a?(a=h.length,Go(o,co(t,h.concat([l]),a),{scroll:!1,origin:"*mouse"})):h.length>1&&h[a].empty()&&"char"==r.unit&&!r.extend?(Go(o,co(t,h.slice(0,a).concat(h.slice(a+1)),0),{scroll:!1,origin:"*mouse"}),c=o.sel):Bo(o,a,l,K):(a=0,Go(o,new lo([l],0),K),c=o.sel);var d=n;function f(e){if(0!=ce(d,e))if(d=e,"rectangle"==r.unit){for(var i=[],s=t.options.tabSize,h=B(te(o,n.line).text,n.ch,s),u=B(te(o,e.line).text,e.ch,s),f=Math.min(h,u),p=Math.max(h,u),g=Math.min(n.line,e.line),m=Math.min(t.lastLine(),Math.max(n.line,e.line));g<=m;g++){var v=te(o,g).text,y=X(v,f,s);f==p?i.push(new ao(ae(g,y),ae(g,y))):v.length>y&&i.push(new ao(ae(g,y),ae(g,X(v,p,s))))}i.length||i.push(new ao(n,n)),Go(o,co(t,c.ranges.slice(0,a).concat(i),a),{origin:"*mouse",scroll:!1}),t.scrollIntoView(e)}else{var b,w=l,x=Ll(t,e,r.unit),_=w.anchor;ce(x.anchor,_)>0?(b=x.head,_=fe(w.from(),x.anchor)):(b=x.anchor,_=de(w.to(),x.head));var C=c.ranges.slice(0);C[a]=Tl(t,new ao(ge(o,_),b)),Go(o,co(t,C,a),K)}}var p=i.wrapper.getBoundingClientRect(),g=0;function m(e){var n=++g,s=Rr(t,e,!0,"rectangle"==r.unit);if(s)if(0!=ce(s,d)){t.curOp.focus=E(z(t)),f(s);var l=ii(i,o);(s.line>=l.to||s.line<l.from)&&setTimeout(Pi(t,(function(){g==n&&m(e)})),150)}else{var a=e.clientY<p.top?-20:e.clientY>p.bottom?20:0;a&&setTimeout(Pi(t,(function(){g==n&&(i.scroller.scrollTop+=a,m(e))})),50)}}function v(e){t.state.selectingText=!1,g=1/0,e&&(St(e),i.input.focus()),bt(i.wrapper.ownerDocument,"mousemove",y),bt(i.wrapper.ownerDocument,"mouseup",b),o.history.lastSelOrigin=null}var y=Pi(t,(function(t){0!==t.buttons&&Nt(t)?m(t):v(t)})),b=Pi(t,v);t.state.selectingText=b,vt(i.wrapper.ownerDocument,"mousemove",y),vt(i.wrapper.ownerDocument,"mouseup",b)}function Tl(t,e){var n=e.anchor,r=e.head,i=te(t.doc,n.line);if(0==ce(n,r)&&n.sticky==r.sticky)return e;var o=gt(i);if(!o)return e;var s=ft(o,n.ch,n.sticky),l=o[s];if(l.from!=n.ch&&l.to!=n.ch)return e;var a,c=s+(l.from==n.ch==(1!=l.level)?0:1);if(0==c||c==o.length)return e;if(r.line!=n.line)a=(r.line-n.line)*("ltr"==t.doc.direction?1:-1)>0;else{var h=ft(o,r.ch,r.sticky),u=h-s||(r.ch-n.ch)*(1==l.level?-1:1);a=h==c-1||h==c?u<0:u>0}var d=o[c+(a?-1:0)],f=a==(1==d.level),p=f?d.from:d.to,g=f?"after":"before";return n.ch==p&&n.sticky==g?e:new ao(new ae(n.line,p,g),r)}function Ol(t,e,n,r){var i,o;if(e.touches)i=e.touches[0].clientX,o=e.touches[0].clientY;else try{i=e.clientX,o=e.clientY}catch(t){return!1}if(i>=Math.floor(t.display.gutters.getBoundingClientRect().right))return!1;r&&St(e);var s=t.display,l=s.lineDiv.getBoundingClientRect();if(o>l.bottom||!Ct(t,n))return Mt(e);o-=l.top-s.viewOffset;for(var a=0;a<t.display.gutterSpecs.length;++a){var c=s.gutters.childNodes[a];if(c&&c.getBoundingClientRect().right>=i)return wt(t,n,t,oe(t.doc,o),t.display.gutterSpecs[a].className,e),Mt(e)}}function Nl(t,e){return Ol(t,e,"gutterClick",!0)}function Dl(t,e){Kn(t.display,e)||Al(t,e)||xt(t,e,"contextmenu")||k||t.display.input.onContextMenu(e)}function Al(t,e){return!!Ct(t,"gutterContextMenu")&&Ol(t,e,"gutterContextMenu",!1)}function El(t){t.display.wrapper.className=t.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+t.options.theme.replace(/(^|\s)\s*/g," cm-s-"),gr(t)}bl.prototype.compare=function(t,e,n){return this.time+yl>t&&0==ce(e,this.pos)&&n==this.button};var Wl={toString:function(){return"CodeMirror.Init"}},Pl={},Hl={};function zl(t){var e=t.optionHandlers;function n(n,r,i,o){t.defaults[n]=r,i&&(e[n]=o?function(t,e,n){n!=Wl&&i(t,e,n)}:i)}t.defineOption=n,t.Init=Wl,n("value","",(function(t,e){return t.setValue(e)}),!0),n("mode",null,(function(t,e){t.doc.modeOption=e,vo(t)}),!0),n("indentUnit",2,vo,!0),n("indentWithTabs",!1),n("smartIndent",!0),n("tabSize",4,(function(t){yo(t),gr(t),Ir(t)}),!0),n("lineSeparator",null,(function(t,e){if(t.doc.lineSep=e,e){var n=[],r=t.doc.first;t.doc.iter((function(t){for(var i=0;;){var o=t.text.indexOf(e,i);if(-1==o)break;i=o+e.length,n.push(ae(r,o))}r++}));for(var i=n.length-1;i>=0;i--)ls(t.doc,e,n[i],ae(n[i].line,n[i].ch+e.length))}})),n("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\u202d\u202e\u2066\u2067\u2069\ufeff\ufff9-\ufffc]/g,(function(t,e,n){t.state.specialChars=new RegExp(e.source+(e.test("\t")?"":"|\t"),"g"),n!=Wl&&t.refresh()})),n("specialCharPlaceholder",bn,(function(t){return t.refresh()}),!0),n("electricChars",!0),n("inputStyle",y?"contenteditable":"textarea",(function(){throw new Error("inputStyle can not (yet) be changed in a running editor")}),!0),n("spellcheck",!1,(function(t,e){return t.getInputField().spellcheck=e}),!0),n("autocorrect",!1,(function(t,e){return t.getInputField().autocorrect=e}),!0),n("autocapitalize",!1,(function(t,e){return t.getInputField().autocapitalize=e}),!0),n("rtlMoveVisually",!x),n("wholeLineUpdateBefore",!0),n("theme","default",(function(t){El(t),to(t)}),!0),n("keyMap","default",(function(t,e,n){var r=qs(e),i=n!=Wl&&qs(n);i&&i.detach&&i.detach(t,r),r.attach&&r.attach(t,i||null)})),n("extraKeys",null),n("configureMouse",null),n("lineWrapping",!1,Fl,!0),n("gutters",[],(function(t,e){t.display.gutterSpecs=Ji(e,t.options.lineNumbers),to(t)}),!0),n("fixedGutter",!0,(function(t,e){t.display.gutters.style.left=e?Pr(t.display)+"px":"0",t.refresh()}),!0),n("coverGutterNextToScrollbar",!1,(function(t){return xi(t)}),!0),n("scrollbarStyle","native",(function(t){ki(t),xi(t),t.display.scrollbars.setScrollTop(t.doc.scrollTop),t.display.scrollbars.setScrollLeft(t.doc.scrollLeft)}),!0),n("lineNumbers",!1,(function(t,e){t.display.gutterSpecs=Ji(t.options.gutters,e),to(t)}),!0),n("firstLineNumber",1,to,!0),n("lineNumberFormatter",(function(t){return t}),to,!0),n("showCursorWhenSelecting",!1,jr,!0),n("resetSelectionOnContextMenu",!0),n("lineWiseCopyCut",!0),n("pasteLinesPerSelection",!0),n("selectionsMayTouch",!1),n("readOnly",!1,(function(t,e){"nocursor"==e&&(ei(t),t.display.input.blur()),t.display.input.readOnlyChanged(e)})),n("screenReaderLabel",null,(function(t,e){e=""===e?null:e,t.display.input.screenReaderLabelChanged(e)})),n("disableInput",!1,(function(t,e){e||t.display.input.reset()}),!0),n("dragDrop",!0,Rl),n("allowDropFileTypes",null),n("cursorBlinkRate",530),n("cursorScrollMargin",0),n("cursorHeight",1,jr,!0),n("singleCursorHeightPerLine",!0,jr,!0),n("workTime",100),n("workDelay",100),n("flattenSpans",!0,yo,!0),n("addModeClass",!1,yo,!0),n("pollInterval",100),n("undoDepth",200,(function(t,e){return t.doc.history.undoDepth=e})),n("historyEventDelay",1250),n("viewportMargin",10,(function(t){return t.refresh()}),!0),n("maxHighlightLength",1e4,yo,!0),n("moveInputWithCursor",!0,(function(t,e){e||t.display.input.resetPosition()})),n("tabindex",null,(function(t,e){return t.display.input.getField().tabIndex=e||""})),n("autofocus",null),n("direction","ltr",(function(t,e){return t.doc.setDirection(e)}),!0),n("phrases",null)}function Rl(t,e,n){if(!e!=!(n&&n!=Wl)){var r=t.display.dragFunctions,i=e?vt:bt;i(t.display.scroller,"dragstart",r.start),i(t.display.scroller,"dragenter",r.enter),i(t.display.scroller,"dragover",r.over),i(t.display.scroller,"dragleave",r.leave),i(t.display.scroller,"drop",r.drop)}}function Fl(t){t.options.lineWrapping?(W(t.display.wrapper,"CodeMirror-wrap"),t.display.sizer.style.minWidth="",t.display.sizerWidth=null):(M(t.display.wrapper,"CodeMirror-wrap"),un(t)),zr(t),Ir(t),gr(t),setTimeout((function(){return xi(t)}),100)}function Il(t,e){var n=this;if(!(this instanceof Il))return new Il(t,e);this.options=e=e?I(e):{},I(Pl,e,!1);var r=e.value;"string"==typeof r?r=new Ls(r,e.mode,null,e.lineSeparator,e.direction):e.mode&&(r.modeOption=e.mode),this.doc=r;var i=new Il.inputStyles[e.inputStyle](this),o=this.display=new eo(t,r,i,e);for(var c in o.wrapper.CodeMirror=this,El(this),e.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),ki(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new V,keySeq:null,specialChars:null},e.autofocus&&!y&&o.input.focus(),s&&l<11&&setTimeout((function(){return n.display.input.reset(!0)}),20),Bl(this),Ws(),Li(this),this.curOp.forceUpdate=!0,_o(this,r),e.autofocus&&!y||this.hasFocus()?setTimeout((function(){n.hasFocus()&&!n.state.focused&&ti(n)}),20):ei(this),Hl)Hl.hasOwnProperty(c)&&Hl[c](this,e[c],Wl);Zi(this),e.finishInit&&e.finishInit(this);for(var h=0;h<Vl.length;++h)Vl[h](this);Mi(this),a&&e.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}function Bl(t){var e=t.display;vt(e.scroller,"mousedown",Pi(t,xl)),vt(e.scroller,"dblclick",s&&l<11?Pi(t,(function(e){if(!xt(t,e)){var n=Rr(t,e);if(n&&!Nl(t,e)&&!Kn(t.display,e)){St(e);var r=t.findWordAt(n);Fo(t.doc,r.anchor,r.head)}}})):function(e){return xt(t,e)||St(e)}),vt(e.scroller,"contextmenu",(function(e){return Dl(t,e)})),vt(e.input.getField(),"contextmenu",(function(n){e.scroller.contains(n.target)||Dl(t,n)}));var n,r={end:0};function i(){e.activeTouch&&(n=setTimeout((function(){return e.activeTouch=null}),1e3),(r=e.activeTouch).end=+new Date)}function o(t){if(1!=t.touches.length)return!1;var e=t.touches[0];return e.radiusX<=1&&e.radiusY<=1}function a(t,e){if(null==e.left)return!0;var n=e.left-t.left,r=e.top-t.top;return n*n+r*r>400}vt(e.scroller,"touchstart",(function(i){if(!xt(t,i)&&!o(i)&&!Nl(t,i)){e.input.ensurePolled(),clearTimeout(n);var s=+new Date;e.activeTouch={start:s,moved:!1,prev:s-r.end<=300?r:null},1==i.touches.length&&(e.activeTouch.left=i.touches[0].pageX,e.activeTouch.top=i.touches[0].pageY)}})),vt(e.scroller,"touchmove",(function(){e.activeTouch&&(e.activeTouch.moved=!0)})),vt(e.scroller,"touchend",(function(n){var r=e.activeTouch;if(r&&!Kn(e,n)&&null!=r.left&&!r.moved&&new Date-r.start<300){var o,s=t.coordsChar(e.activeTouch,"page");o=!r.prev||a(r,r.prev)?new ao(s,s):!r.prev.prev||a(r,r.prev.prev)?t.findWordAt(s):new ao(ae(s.line,0),ge(t.doc,ae(s.line+1,0))),t.setSelection(o.anchor,o.head),t.focus(),St(n)}i()})),vt(e.scroller,"touchcancel",i),vt(e.scroller,"scroll",(function(){e.scroller.clientHeight&&(gi(t,e.scroller.scrollTop),vi(t,e.scroller.scrollLeft,!0),wt(t,"scroll",t))})),vt(e.scroller,"mousewheel",(function(e){return so(t,e)})),vt(e.scroller,"DOMMouseScroll",(function(e){return so(t,e)})),vt(e.wrapper,"scroll",(function(){return e.wrapper.scrollTop=e.wrapper.scrollLeft=0})),e.dragFunctions={enter:function(e){xt(t,e)||Tt(e)},over:function(e){xt(t,e)||(Ns(t,e),Tt(e))},start:function(e){return Os(t,e)},drop:Pi(t,Ts),leave:function(e){xt(t,e)||Ds(t)}};var c=e.input.getField();vt(c,"keyup",(function(e){return pl.call(t,e)})),vt(c,"keydown",Pi(t,dl)),vt(c,"keypress",Pi(t,gl)),vt(c,"focus",(function(e){return ti(t,e)})),vt(c,"blur",(function(e){return ei(t,e)}))}Il.defaults=Pl,Il.optionHandlers=Hl;var Vl=[];function $l(t,e,n,r){var i,o=t.doc;null==n&&(n="add"),"smart"==n&&(o.mode.indent?i=_e(t,e).state:n="prev");var s=t.options.tabSize,l=te(o,e),a=B(l.text,null,s);l.stateAfter&&(l.stateAfter=null);var c,h=l.text.match(/^\s*/)[0];if(r||/\S/.test(l.text)){if("smart"==n&&((c=o.mode.indent(i,l.text.slice(h.length),l.text))==G||c>150)){if(!r)return;n="prev"}}else c=0,n="not";"prev"==n?c=e>o.first?B(te(o,e-1).text,null,s):0:"add"==n?c=a+t.options.indentUnit:"subtract"==n?c=a-t.options.indentUnit:"number"==typeof n&&(c=a+n),c=Math.max(0,c);var u="",d=0;if(t.options.indentWithTabs)for(var f=Math.floor(c/s);f;--f)d+=s,u+="\t";if(d<c&&(u+=Z(c-d)),u!=h)return ls(o,u,ae(e,0),ae(e,h.length),"+input"),l.stateAfter=null,!0;for(var p=0;p<o.sel.ranges.length;p++){var g=o.sel.ranges[p];if(g.head.line==e&&g.head.ch<h.length){var m=ae(e,h.length);Bo(o,p,new ao(m,m));break}}}Il.defineInitHook=function(t){return Vl.push(t)};var Ul=null;function Gl(t){Ul=t}function jl(t,e,n,r,i){var o=t.doc;t.display.shift=!1,r||(r=o.sel);var s=+new Date-200,l="paste"==i||t.state.pasteIncoming>s,a=zt(e),c=null;if(l&&r.ranges.length>1)if(Ul&&Ul.text.join("\n")==e){if(r.ranges.length%Ul.text.length==0){c=[];for(var h=0;h<Ul.text.length;h++)c.push(o.splitLines(Ul.text[h]))}}else a.length==r.ranges.length&&t.options.pasteLinesPerSelection&&(c=Q(a,(function(t){return[t]})));for(var u=t.curOp.updateInput,d=r.ranges.length-1;d>=0;d--){var f=r.ranges[d],p=f.from(),g=f.to();f.empty()&&(n&&n>0?p=ae(p.line,p.ch-n):t.state.overwrite&&!l?g=ae(g.line,Math.min(te(o,g.line).text.length,g.ch+J(a).length)):l&&Ul&&Ul.lineWise&&Ul.text.join("\n")==a.join("\n")&&(p=g=ae(p.line,0)));var m={from:p,to:g,text:c?c[d%c.length]:a,origin:i||(l?"paste":t.state.cutIncoming>s?"cut":"+input")};es(t.doc,m),An(t,"inputRead",t,m)}e&&!l&&ql(t,e),hi(t),t.curOp.updateInput<2&&(t.curOp.updateInput=u),t.curOp.typing=!0,t.state.pasteIncoming=t.state.cutIncoming=-1}function Kl(t,e){var n=t.clipboardData&&t.clipboardData.getData("Text");if(n)return t.preventDefault(),e.isReadOnly()||e.options.disableInput||!e.hasFocus()||Wi(e,(function(){return jl(e,n,0,null,"paste")})),!0}function ql(t,e){if(t.options.electricChars&&t.options.smartIndent)for(var n=t.doc.sel,r=n.ranges.length-1;r>=0;r--){var i=n.ranges[r];if(!(i.head.ch>100||r&&n.ranges[r-1].head.line==i.head.line)){var o=t.getModeAt(i.head),s=!1;if(o.electricChars){for(var l=0;l<o.electricChars.length;l++)if(e.indexOf(o.electricChars.charAt(l))>-1){s=$l(t,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(te(t.doc,i.head.line).text.slice(0,i.head.ch))&&(s=$l(t,i.head.line,"smart"));s&&An(t,"electricInput",t,i.head.line)}}}function Xl(t){for(var e=[],n=[],r=0;r<t.doc.sel.ranges.length;r++){var i=t.doc.sel.ranges[r].head.line,o={anchor:ae(i,0),head:ae(i+1,0)};n.push(o),e.push(t.getRange(o.anchor,o.head))}return{text:e,ranges:n}}function Yl(t,e,n,r){t.setAttribute("autocorrect",n?"on":"off"),t.setAttribute("autocapitalize",r?"on":"off"),t.setAttribute("spellcheck",!!e)}function Zl(){var t=N("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),e=N("div",[t],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return a?t.style.width="1000px":t.setAttribute("wrap","off"),m&&(t.style.border="1px solid black"),e}function Jl(t){var e=t.optionHandlers,n=t.helpers={};t.prototype={constructor:t,focus:function(){R(this).focus(),this.display.input.focus()},setOption:function(t,n){var r=this.options,i=r[t];r[t]==n&&"mode"!=t||(r[t]=n,e.hasOwnProperty(t)&&Pi(this,e[t])(this,n,i),wt(this,"optionChange",this,t))},getOption:function(t){return this.options[t]},getDoc:function(){return this.doc},addKeyMap:function(t,e){this.state.keyMaps[e?"push":"unshift"](qs(t))},removeKeyMap:function(t){for(var e=this.state.keyMaps,n=0;n<e.length;++n)if(e[n]==t||e[n].name==t)return e.splice(n,1),!0},addOverlay:Hi((function(e,n){var r=e.token?e:t.getMode(this.options,e);if(r.startState)throw new Error("Overlays may not be stateful.");tt(this.state.overlays,{mode:r,modeSpec:e,opaque:n&&n.opaque,priority:n&&n.priority||0},(function(t){return t.priority})),this.state.modeGen++,Ir(this)})),removeOverlay:Hi((function(t){for(var e=this.state.overlays,n=0;n<e.length;++n){var r=e[n].modeSpec;if(r==t||"string"==typeof t&&r.name==t)return e.splice(n,1),this.state.modeGen++,void Ir(this)}})),indentLine:Hi((function(t,e,n){"string"!=typeof e&&"number"!=typeof e&&(e=null==e?this.options.smartIndent?"smart":"prev":e?"add":"subtract"),se(this.doc,t)&&$l(this,t,e,n)})),indentSelection:Hi((function(t){for(var e=this.doc.sel.ranges,n=-1,r=0;r<e.length;r++){var i=e[r];if(i.empty())i.head.line>n&&($l(this,i.head.line,t,!0),n=i.head.line,r==this.doc.sel.primIndex&&hi(this));else{var o=i.from(),s=i.to(),l=Math.max(n,o.line);n=Math.min(this.lastLine(),s.line-(s.ch?0:1))+1;for(var a=l;a<n;++a)$l(this,a,t);var c=this.doc.sel.ranges;0==o.ch&&e.length==c.length&&c[r].from().ch>0&&Bo(this.doc,r,new ao(o,c[r].to()),j)}}})),getTokenAt:function(t,e){return Me(this,t,e)},getLineTokens:function(t,e){return Me(this,ae(t),e,!0)},getTokenTypeAt:function(t){t=ge(this.doc,t);var e,n=xe(this,te(this.doc,t.line)),r=0,i=(n.length-1)/2,o=t.ch;if(0==o)e=n[2];else for(;;){var s=r+i>>1;if((s?n[2*s-1]:0)>=o)i=s;else{if(!(n[2*s+1]<o)){e=n[2*s+2];break}r=s+1}}var l=e?e.indexOf("overlay "):-1;return l<0?e:0==l?null:e.slice(0,l-1)},getModeAt:function(e){var n=this.doc.mode;return n.innerMode?t.innerMode(n,this.getTokenAt(e).state).mode:n},getHelper:function(t,e){return this.getHelpers(t,e)[0]},getHelpers:function(t,e){var r=[];if(!n.hasOwnProperty(e))return r;var i=n[e],o=this.getModeAt(t);if("string"==typeof o[e])i[o[e]]&&r.push(i[o[e]]);else if(o[e])for(var s=0;s<o[e].length;s++){var l=i[o[e][s]];l&&r.push(l)}else o.helperType&&i[o.helperType]?r.push(i[o.helperType]):i[o.name]&&r.push(i[o.name]);for(var a=0;a<i._global.length;a++){var c=i._global[a];c.pred(o,this)&&-1==$(r,c.val)&&r.push(c.val)}return r},getStateAfter:function(t,e){var n=this.doc;return _e(this,(t=pe(n,null==t?n.first+n.size-1:t))+1,e).state},cursorCoords:function(t,e){var n=this.doc.sel.primary();return _r(this,null==t?n.head:"object"==typeof t?ge(this.doc,t):t?n.from():n.to(),e||"page")},charCoords:function(t,e){return xr(this,ge(this.doc,t),e||"page")},coordsChar:function(t,e){return Sr(this,(t=wr(this,t,e||"page")).left,t.top)},lineAtHeight:function(t,e){return t=wr(this,{top:t,left:0},e||"page").top,oe(this.doc,t+this.display.viewOffset)},heightAtLine:function(t,e,n){var r,i=!1;if("number"==typeof t){var o=this.doc.first+this.doc.size-1;t<this.doc.first?t=this.doc.first:t>o&&(t=o,i=!0),r=te(this.doc,t)}else r=t;return br(this,r,{top:0,left:0},e||"page",n||i).top+(i?this.doc.height-cn(r):0)},defaultTextHeight:function(){return Ar(this.display)},defaultCharWidth:function(){return Er(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(t,e,n,r,i){var o=this.display,s=(t=_r(this,ge(this.doc,t))).bottom,l=t.left;if(e.style.position="absolute",e.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(e),o.sizer.appendChild(e),"over"==r)s=t.top;else if("above"==r||"near"==r){var a=Math.max(o.wrapper.clientHeight,this.doc.height),c=Math.max(o.sizer.clientWidth,o.lineSpace.clientWidth);("above"==r||t.bottom+e.offsetHeight>a)&&t.top>e.offsetHeight?s=t.top-e.offsetHeight:t.bottom+e.offsetHeight<=a&&(s=t.bottom),l+e.offsetWidth>c&&(l=c-e.offsetWidth)}e.style.top=s+"px",e.style.left=e.style.right="","right"==i?(l=o.sizer.clientWidth-e.offsetWidth,e.style.right="0px"):("left"==i?l=0:"middle"==i&&(l=(o.sizer.clientWidth-e.offsetWidth)/2),e.style.left=l+"px"),n&&li(this,{left:l,top:s,right:l+e.offsetWidth,bottom:s+e.offsetHeight})},triggerOnKeyDown:Hi(dl),triggerOnKeyPress:Hi(gl),triggerOnKeyUp:pl,triggerOnMouseDown:Hi(xl),execCommand:function(t){if(tl.hasOwnProperty(t))return tl[t].call(null,this)},triggerElectric:Hi((function(t){ql(this,t)})),findPosH:function(t,e,n,r){var i=1;e<0&&(i=-1,e=-e);for(var o=ge(this.doc,t),s=0;s<e&&!(o=Ql(this.doc,o,i,n,r)).hitSide;++s);return o},moveH:Hi((function(t,e){var n=this;this.extendSelectionsBy((function(r){return n.display.shift||n.doc.extend||r.empty()?Ql(n.doc,r.head,t,e,n.options.rtlMoveVisually):t<0?r.from():r.to()}),q)})),deleteH:Hi((function(t,e){var n=this.doc.sel,r=this.doc;n.somethingSelected()?r.replaceSelection("",null,"+delete"):Xs(this,(function(n){var i=Ql(r,n.head,t,e,!1);return t<0?{from:i,to:n.head}:{from:n.head,to:i}}))})),findPosV:function(t,e,n,r){var i=1,o=r;e<0&&(i=-1,e=-e);for(var s=ge(this.doc,t),l=0;l<e;++l){var a=_r(this,s,"div");if(null==o?o=a.left:a.left=o,(s=ta(this,a,i,n)).hitSide)break}return s},moveV:Hi((function(t,e){var n=this,r=this.doc,i=[],o=!this.display.shift&&!r.extend&&r.sel.somethingSelected();if(r.extendSelectionsBy((function(s){if(o)return t<0?s.from():s.to();var l=_r(n,s.head,"div");null!=s.goalColumn&&(l.left=s.goalColumn),i.push(l.left);var a=ta(n,l,t,e);return"page"==e&&s==r.sel.primary()&&ci(n,xr(n,a,"div").top-l.top),a}),q),i.length)for(var s=0;s<r.sel.ranges.length;s++)r.sel.ranges[s].goalColumn=i[s]})),findWordAt:function(t){var e=te(this.doc,t.line).text,n=t.ch,r=t.ch;if(e){var i=this.getHelper(t,"wordChars");"before"!=t.sticky&&r!=e.length||!n?++r:--n;for(var o=e.charAt(n),s=ot(o,i)?function(t){return ot(t,i)}:/\s/.test(o)?function(t){return/\s/.test(t)}:function(t){return!/\s/.test(t)&&!ot(t)};n>0&&s(e.charAt(n-1));)--n;for(;r<e.length&&s(e.charAt(r));)++r}return new ao(ae(t.line,n),ae(t.line,r))},toggleOverwrite:function(t){null!=t&&t==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?W(this.display.cursorDiv,"CodeMirror-overwrite"):M(this.display.cursorDiv,"CodeMirror-overwrite"),wt(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==E(z(this))},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:Hi((function(t,e){ui(this,t,e)})),getScrollInfo:function(){var t=this.display.scroller;return{left:t.scrollLeft,top:t.scrollTop,height:t.scrollHeight-Zn(this)-this.display.barHeight,width:t.scrollWidth-Zn(this)-this.display.barWidth,clientHeight:Qn(this),clientWidth:Jn(this)}},scrollIntoView:Hi((function(t,e){null==t?(t={from:this.doc.sel.primary().head,to:null},null==e&&(e=this.options.cursorScrollMargin)):"number"==typeof t?t={from:ae(t,0),to:null}:null==t.from&&(t={from:t,to:null}),t.to||(t.to=t.from),t.margin=e||0,null!=t.from.line?di(this,t):pi(this,t.from,t.to,t.margin)})),setSize:Hi((function(t,e){var n=this,r=function(t){return"number"==typeof t||/^\d+$/.test(String(t))?t+"px":t};null!=t&&(this.display.wrapper.style.width=r(t)),null!=e&&(this.display.wrapper.style.height=r(e)),this.options.lineWrapping&&pr(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,(function(t){if(t.widgets)for(var e=0;e<t.widgets.length;e++)if(t.widgets[e].noHScroll){Br(n,i,"widget");break}++i})),this.curOp.forceUpdate=!0,wt(this,"refresh",this)})),operation:function(t){return Wi(this,t)},startOperation:function(){return Li(this)},endOperation:function(){return Mi(this)},refresh:Hi((function(){var t=this.display.cachedTextHeight;Ir(this),this.curOp.forceUpdate=!0,gr(this),ui(this,this.doc.scrollLeft,this.doc.scrollTop),qi(this.display),(null==t||Math.abs(t-Ar(this.display))>.5||this.options.lineWrapping)&&zr(this),wt(this,"refresh",this)})),swapDoc:Hi((function(t){var e=this.doc;return e.cm=null,this.state.selectingText&&this.state.selectingText(),_o(this,t),gr(this),this.display.input.reset(),ui(this,t.scrollLeft,t.scrollTop),this.curOp.forceScroll=!0,An(this,"swapDoc",this,e),e})),phrase:function(t){var e=this.options.phrases;return e&&Object.prototype.hasOwnProperty.call(e,t)?e[t]:t},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},kt(t),t.registerHelper=function(e,r,i){n.hasOwnProperty(e)||(n[e]=t[e]={_global:[]}),n[e][r]=i},t.registerGlobalHelper=function(e,r,i,o){t.registerHelper(e,r,o),n[e]._global.push({pred:i,val:o})}}function Ql(t,e,n,r,i){var o=e,s=n,l=te(t,e.line),a=i&&"rtl"==t.direction?-n:n;function c(){var n=e.line+a;return!(n<t.first||n>=t.first+t.size)&&(e=new ae(n,e.ch,e.sticky),l=te(t,n))}function h(o){var s;if("codepoint"==r){var h=l.text.charCodeAt(e.ch+(n>0?0:-1));if(isNaN(h))s=null;else{var u=n>0?h>=55296&&h<56320:h>=56320&&h<57343;s=new ae(e.line,Math.max(0,Math.min(l.text.length,e.ch+n*(u?2:1))),-n)}}else s=i?Qs(t.cm,l,e,n):Zs(l,e,n);if(null==s){if(o||!c())return!1;e=Js(i,t.cm,l,e.line,a)}else e=s;return!0}if("char"==r||"codepoint"==r)h();else if("column"==r)h(!0);else if("word"==r||"group"==r)for(var u=null,d="group"==r,f=t.cm&&t.cm.getHelper(e,"wordChars"),p=!0;!(n<0)||h(!p);p=!1){var g=l.text.charAt(e.ch)||"\n",m=ot(g,f)?"w":d&&"\n"==g?"n":!d||/\s/.test(g)?null:"p";if(!d||p||m||(m="s"),u&&u!=m){n<0&&(n=1,h(),e.sticky="after");break}if(m&&(u=m),n>0&&!h(!p))break}var v=Zo(t,e,o,s,!0);return he(o,v)&&(v.hitSide=!0),v}function ta(t,e,n,r){var i,o,s=t.doc,l=e.left;if("page"==r){var a=Math.min(t.display.wrapper.clientHeight,R(t).innerHeight||s(t).documentElement.clientHeight),c=Math.max(a-.5*Ar(t.display),3);i=(n>0?e.bottom:e.top)+n*c}else"line"==r&&(i=n>0?e.bottom+3:e.top-3);for(;(o=Sr(t,l,i)).outside;){if(n<0?i<=0:i>=s.height){o.hitSide=!0;break}i+=5*n}return o}var ea=function(t){this.cm=t,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new V,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function na(t,e){var n=ir(t,e.line);if(!n||n.hidden)return null;var r=te(t.doc,e.line),i=er(n,r,e.line),o=gt(r,t.doc.direction),s="left";o&&(s=ft(o,e.ch)%2?"right":"left");var l=cr(i.map,e.ch,s);return l.offset="right"==l.collapse?l.end:l.start,l}function ra(t){for(var e=t;e;e=e.parentNode)if(/CodeMirror-gutter-wrapper/.test(e.className))return!0;return!1}function ia(t,e){return e&&(t.bad=!0),t}function oa(t,e,n,r,i){var o="",s=!1,l=t.doc.lineSeparator(),a=!1;function c(t){return function(e){return e.id==t}}function h(){s&&(o+=l,a&&(o+=l),s=a=!1)}function u(t){t&&(h(),o+=t)}function d(e){if(1==e.nodeType){var n=e.getAttribute("cm-text");if(n)return void u(n);var o,f=e.getAttribute("cm-marker");if(f){var p=t.findMarks(ae(r,0),ae(i+1,0),c(+f));return void(p.length&&(o=p[0].find(0))&&u(ee(t.doc,o.from,o.to).join(l)))}if("false"==e.getAttribute("contenteditable"))return;var g=/^(pre|div|p|li|table|br)$/i.test(e.nodeName);if(!/^br$/i.test(e.nodeName)&&0==e.textContent.length)return;g&&h();for(var m=0;m<e.childNodes.length;m++)d(e.childNodes[m]);/^(pre|p)$/i.test(e.nodeName)&&(a=!0),g&&(s=!0)}else 3==e.nodeType&&u(e.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;d(e),e!=n;)e=e.nextSibling,a=!1;return o}function sa(t,e,n){var r;if(e==t.display.lineDiv){if(!(r=t.display.lineDiv.childNodes[n]))return ia(t.clipPos(ae(t.display.viewTo-1)),!0);e=null,n=0}else for(r=e;;r=r.parentNode){if(!r||r==t.display.lineDiv)return null;if(r.parentNode&&r.parentNode==t.display.lineDiv)break}for(var i=0;i<t.display.view.length;i++){var o=t.display.view[i];if(o.node==r)return la(o,e,n)}}function la(t,e,n){var r=t.text.firstChild,i=!1;if(!e||!A(r,e))return ia(ae(ie(t.line),0),!0);if(e==r&&(i=!0,e=r.childNodes[n],n=0,!e)){var o=t.rest?J(t.rest):t.line;return ia(ae(ie(o),o.text.length),i)}var s=3==e.nodeType?e:null,l=e;for(s||1!=e.childNodes.length||3!=e.firstChild.nodeType||(s=e.firstChild,n&&(n=s.nodeValue.length));l.parentNode!=r;)l=l.parentNode;var a=t.measure,c=a.maps;function h(e,n,r){for(var i=-1;i<(c?c.length:0);i++)for(var o=i<0?a.map:c[i],s=0;s<o.length;s+=3){var l=o[s+2];if(l==e||l==n){var h=ie(i<0?t.line:t.rest[i]),u=o[s]+r;return(r<0||l!=e)&&(u=o[s+(r?1:0)]),ae(h,u)}}}var u=h(s,l,n);if(u)return ia(u,i);for(var d=l.nextSibling,f=s?s.nodeValue.length-n:0;d;d=d.nextSibling){if(u=h(d,d.firstChild,0))return ia(ae(u.line,u.ch-f),i);f+=d.textContent.length}for(var p=l.previousSibling,g=n;p;p=p.previousSibling){if(u=h(p,p.firstChild,-1))return ia(ae(u.line,u.ch+g),i);g+=p.textContent.length}}ea.prototype.init=function(t){var e=this,n=this,r=n.cm,i=n.div=t.lineDiv;function o(t){for(var e=t.target;e;e=e.parentNode){if(e==i)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(e.className))break}return!1}function s(t){if(o(t)&&!xt(r,t)){if(r.somethingSelected())Gl({lineWise:!1,text:r.getSelections()}),"cut"==t.type&&r.replaceSelection("",null,"cut");else{if(!r.options.lineWiseCopyCut)return;var e=Xl(r);Gl({lineWise:!0,text:e.text}),"cut"==t.type&&r.operation((function(){r.setSelections(e.ranges,0,j),r.replaceSelection("",null,"cut")}))}if(t.clipboardData){t.clipboardData.clearData();var s=Ul.text.join("\n");if(t.clipboardData.setData("Text",s),t.clipboardData.getData("Text")==s)return void t.preventDefault()}var l=Zl(),a=l.firstChild;Yl(a),r.display.lineSpace.insertBefore(l,r.display.lineSpace.firstChild),a.value=Ul.text.join("\n");var c=E(i.ownerDocument);H(a),setTimeout((function(){r.display.lineSpace.removeChild(l),c.focus(),c==i&&n.showPrimarySelection()}),50)}}i.contentEditable=!0,Yl(i,r.options.spellcheck,r.options.autocorrect,r.options.autocapitalize),vt(i,"paste",(function(t){!o(t)||xt(r,t)||Kl(t,r)||l<=11&&setTimeout(Pi(r,(function(){return e.updateFromDOM()})),20)})),vt(i,"compositionstart",(function(t){e.composing={data:t.data,done:!1}})),vt(i,"compositionupdate",(function(t){e.composing||(e.composing={data:t.data,done:!1})})),vt(i,"compositionend",(function(t){e.composing&&(t.data!=e.composing.data&&e.readFromDOMSoon(),e.composing.done=!0)})),vt(i,"touchstart",(function(){return n.forceCompositionEnd()})),vt(i,"input",(function(){e.composing||e.readFromDOMSoon()})),vt(i,"copy",s),vt(i,"cut",s)},ea.prototype.screenReaderLabelChanged=function(t){t?this.div.setAttribute("aria-label",t):this.div.removeAttribute("aria-label")},ea.prototype.prepareSelection=function(){var t=Kr(this.cm,!1);return t.focus=E(this.div.ownerDocument)==this.div,t},ea.prototype.showSelection=function(t,e){t&&this.cm.display.view.length&&((t.focus||e)&&this.showPrimarySelection(),this.showMultipleSelections(t))},ea.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},ea.prototype.showPrimarySelection=function(){var t=this.getSelection(),e=this.cm,r=e.doc.sel.primary(),i=r.from(),o=r.to();if(e.display.viewTo==e.display.viewFrom||i.line>=e.display.viewTo||o.line<e.display.viewFrom)t.removeAllRanges();else{var s=sa(e,t.anchorNode,t.anchorOffset),l=sa(e,t.focusNode,t.focusOffset);if(!s||s.bad||!l||l.bad||0!=ce(fe(s,l),i)||0!=ce(de(s,l),o)){var a=e.display.view,c=i.line>=e.display.viewFrom&&na(e,i)||{node:a[0].measure.map[2],offset:0},h=o.line<e.display.viewTo&&na(e,o);if(!h){var u=a[a.length-1].measure,d=u.maps?u.maps[u.maps.length-1]:u.map;h={node:d[d.length-1],offset:d[d.length-2]-d[d.length-3]}}if(c&&h){var f,p=t.rangeCount&&t.getRangeAt(0);try{f=L(c.node,c.offset,h.offset,h.node)}catch(t){}f&&(!n&&e.state.focused?(t.collapse(c.node,c.offset),f.collapsed||(t.removeAllRanges(),t.addRange(f))):(t.removeAllRanges(),t.addRange(f)),p&&null==t.anchorNode?t.addRange(p):n&&this.startGracePeriod()),this.rememberSelection()}else t.removeAllRanges()}}},ea.prototype.startGracePeriod=function(){var t=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout((function(){t.gracePeriod=!1,t.selectionChanged()&&t.cm.operation((function(){return t.cm.curOp.selectionChanged=!0}))}),20)},ea.prototype.showMultipleSelections=function(t){O(this.cm.display.cursorDiv,t.cursors),O(this.cm.display.selectionDiv,t.selection)},ea.prototype.rememberSelection=function(){var t=this.getSelection();this.lastAnchorNode=t.anchorNode,this.lastAnchorOffset=t.anchorOffset,this.lastFocusNode=t.focusNode,this.lastFocusOffset=t.focusOffset},ea.prototype.selectionInEditor=function(){var t=this.getSelection();if(!t.rangeCount)return!1;var e=t.getRangeAt(0).commonAncestorContainer;return A(this.div,e)},ea.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()&&E(this.div.ownerDocument)==this.div||this.showSelection(this.prepareSelection(),!0),this.div.focus())},ea.prototype.blur=function(){this.div.blur()},ea.prototype.getField=function(){return this.div},ea.prototype.supportsTouch=function(){return!0},ea.prototype.receivedFocus=function(){var t=this,e=this;function n(){e.cm.state.focused&&(e.pollSelection(),e.polling.set(e.cm.options.pollInterval,n))}this.selectionInEditor()?setTimeout((function(){return t.pollSelection()}),20):Wi(this.cm,(function(){return e.cm.curOp.selectionChanged=!0})),this.polling.set(this.cm.options.pollInterval,n)},ea.prototype.selectionChanged=function(){var t=this.getSelection();return t.anchorNode!=this.lastAnchorNode||t.anchorOffset!=this.lastAnchorOffset||t.focusNode!=this.lastFocusNode||t.focusOffset!=this.lastFocusOffset},ea.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var t=this.getSelection(),e=this.cm;if(v&&h&&this.cm.display.gutterSpecs.length&&ra(t.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var n=sa(e,t.anchorNode,t.anchorOffset),r=sa(e,t.focusNode,t.focusOffset);n&&r&&Wi(e,(function(){Go(e.doc,ho(n,r),j),(n.bad||r.bad)&&(e.curOp.selectionChanged=!0)}))}}},ea.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var t,e,n,r=this.cm,i=r.display,o=r.doc.sel.primary(),s=o.from(),l=o.to();if(0==s.ch&&s.line>r.firstLine()&&(s=ae(s.line-1,te(r.doc,s.line-1).length)),l.ch==te(r.doc,l.line).text.length&&l.line<r.lastLine()&&(l=ae(l.line+1,0)),s.line<i.viewFrom||l.line>i.viewTo-1)return!1;s.line==i.viewFrom||0==(t=Fr(r,s.line))?(e=ie(i.view[0].line),n=i.view[0].node):(e=ie(i.view[t].line),n=i.view[t-1].node.nextSibling);var a,c,h=Fr(r,l.line);if(h==i.view.length-1?(a=i.viewTo-1,c=i.lineDiv.lastChild):(a=ie(i.view[h+1].line)-1,c=i.view[h+1].node.previousSibling),!n)return!1;for(var u=r.doc.splitLines(oa(r,n,c,e,a)),d=ee(r.doc,ae(e,0),ae(a,te(r.doc,a).text.length));u.length>1&&d.length>1;)if(J(u)==J(d))u.pop(),d.pop(),a--;else{if(u[0]!=d[0])break;u.shift(),d.shift(),e++}for(var f=0,p=0,g=u[0],m=d[0],v=Math.min(g.length,m.length);f<v&&g.charCodeAt(f)==m.charCodeAt(f);)++f;for(var y=J(u),b=J(d),w=Math.min(y.length-(1==u.length?f:0),b.length-(1==d.length?f:0));p<w&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)++p;if(1==u.length&&1==d.length&&e==s.line)for(;f&&f>s.ch&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)f--,p++;u[u.length-1]=y.slice(0,y.length-p).replace(/^\u200b+/,""),u[0]=u[0].slice(f).replace(/\u200b+$/,"");var x=ae(e,f),_=ae(a,d.length?J(d).length-p:0);return u.length>1||u[0]||ce(x,_)?(ls(r.doc,u,x,_,"+input"),!0):void 0},ea.prototype.ensurePolled=function(){this.forceCompositionEnd()},ea.prototype.reset=function(){this.forceCompositionEnd()},ea.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},ea.prototype.readFromDOMSoon=function(){var t=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout((function(){if(t.readDOMTimeout=null,t.composing){if(!t.composing.done)return;t.composing=null}t.updateFromDOM()}),80))},ea.prototype.updateFromDOM=function(){var t=this;!this.cm.isReadOnly()&&this.pollContent()||Wi(this.cm,(function(){return Ir(t.cm)}))},ea.prototype.setUneditable=function(t){t.contentEditable="false"},ea.prototype.onKeyPress=function(t){0==t.charCode||this.composing||(t.preventDefault(),this.cm.isReadOnly()||Pi(this.cm,jl)(this.cm,String.fromCharCode(null==t.charCode?t.keyCode:t.charCode),0))},ea.prototype.readOnlyChanged=function(t){this.div.contentEditable=String("nocursor"!=t)},ea.prototype.onContextMenu=function(){},ea.prototype.resetPosition=function(){},ea.prototype.needsContentAttribute=!0;var aa=function(t){this.cm=t,this.prevInput="",this.pollingFast=!1,this.polling=new V,this.hasSelection=!1,this.composing=null,this.resetting=!1};function ca(t,e){if((e=e?I(e):{}).value=t.value,!e.tabindex&&t.tabIndex&&(e.tabindex=t.tabIndex),!e.placeholder&&t.placeholder&&(e.placeholder=t.placeholder),null==e.autofocus){var n=E(t.ownerDocument);e.autofocus=n==t||null!=t.getAttribute("autofocus")&&n==document.body}function r(){t.value=l.getValue()}var i;if(t.form&&(vt(t.form,"submit",r),!e.leaveSubmitMethodAlone)){var o=t.form;i=o.submit;try{var s=o.submit=function(){r(),o.submit=i,o.submit(),o.submit=s}}catch(t){}}e.finishInit=function(n){n.save=r,n.getTextArea=function(){return t},n.toTextArea=function(){n.toTextArea=isNaN,r(),t.parentNode.removeChild(n.getWrapperElement()),t.style.display="",t.form&&(bt(t.form,"submit",r),e.leaveSubmitMethodAlone||"function"!=typeof t.form.submit||(t.form.submit=i))}},t.style.display="none";var l=Il((function(e){return t.parentNode.insertBefore(e,t.nextSibling)}),e);return l}function ha(t){t.off=bt,t.on=vt,t.wheelEventPixels=oo,t.Doc=Ls,t.splitLines=zt,t.countColumn=B,t.findColumn=X,t.isWordChar=it,t.Pass=G,t.signal=wt,t.Line=dn,t.changeEnd=uo,t.scrollbarModel=Ci,t.Pos=ae,t.cmpPos=ce,t.modes=Vt,t.mimeModes=$t,t.resolveMode=jt,t.getMode=Kt,t.modeExtensions=qt,t.extendMode=Xt,t.copyState=Yt,t.startState=Jt,t.innerMode=Zt,t.commands=tl,t.keyMap=Bs,t.keyName=Ks,t.isModifierKey=Gs,t.lookupKey=Us,t.normalizeKeyMap=$s,t.StringStream=Qt,t.SharedTextMarker=ws,t.TextMarker=ys,t.LineWidget=ps,t.e_preventDefault=St,t.e_stopPropagation=Lt,t.e_stop=Tt,t.addClass=W,t.contains=A,t.rmClass=M,t.keyNames=zs}aa.prototype.init=function(t){var e=this,n=this,r=this.cm;this.createField(t);var i=this.textarea;function o(t){if(!xt(r,t)){if(r.somethingSelected())Gl({lineWise:!1,text:r.getSelections()});else{if(!r.options.lineWiseCopyCut)return;var e=Xl(r);Gl({lineWise:!0,text:e.text}),"cut"==t.type?r.setSelections(e.ranges,null,j):(n.prevInput="",i.value=e.text.join("\n"),H(i))}"cut"==t.type&&(r.state.cutIncoming=+new Date)}}t.wrapper.insertBefore(this.wrapper,t.wrapper.firstChild),m&&(i.style.width="0px"),vt(i,"input",(function(){s&&l>=9&&e.hasSelection&&(e.hasSelection=null),n.poll()})),vt(i,"paste",(function(t){xt(r,t)||Kl(t,r)||(r.state.pasteIncoming=+new Date,n.fastPoll())})),vt(i,"cut",o),vt(i,"copy",o),vt(t.scroller,"paste",(function(e){if(!Kn(t,e)&&!xt(r,e)){if(!i.dispatchEvent)return r.state.pasteIncoming=+new Date,void n.focus();var o=new Event("paste");o.clipboardData=e.clipboardData,i.dispatchEvent(o)}})),vt(t.lineSpace,"selectstart",(function(e){Kn(t,e)||St(e)})),vt(i,"compositionstart",(function(){var t=r.getCursor("from");n.composing&&n.composing.range.clear(),n.composing={start:t,range:r.markText(t,r.getCursor("to"),{className:"CodeMirror-composing"})}})),vt(i,"compositionend",(function(){n.composing&&(n.poll(),n.composing.range.clear(),n.composing=null)}))},aa.prototype.createField=function(t){this.wrapper=Zl(),this.textarea=this.wrapper.firstChild;var e=this.cm.options;Yl(this.textarea,e.spellcheck,e.autocorrect,e.autocapitalize)},aa.prototype.screenReaderLabelChanged=function(t){t?this.textarea.setAttribute("aria-label",t):this.textarea.removeAttribute("aria-label")},aa.prototype.prepareSelection=function(){var t=this.cm,e=t.display,n=t.doc,r=Kr(t);if(t.options.moveInputWithCursor){var i=_r(t,n.sel.primary().head,"div"),o=e.wrapper.getBoundingClientRect(),s=e.lineDiv.getBoundingClientRect();r.teTop=Math.max(0,Math.min(e.wrapper.clientHeight-10,i.top+s.top-o.top)),r.teLeft=Math.max(0,Math.min(e.wrapper.clientWidth-10,i.left+s.left-o.left))}return r},aa.prototype.showSelection=function(t){var e=this.cm.display;O(e.cursorDiv,t.cursors),O(e.selectionDiv,t.selection),null!=t.teTop&&(this.wrapper.style.top=t.teTop+"px",this.wrapper.style.left=t.teLeft+"px")},aa.prototype.reset=function(t){if(!(this.contextMenuPending||this.composing&&t)){var e=this.cm;if(this.resetting=!0,e.somethingSelected()){this.prevInput="";var n=e.getSelection();this.textarea.value=n,e.state.focused&&H(this.textarea),s&&l>=9&&(this.hasSelection=n)}else t||(this.prevInput=this.textarea.value="",s&&l>=9&&(this.hasSelection=null));this.resetting=!1}},aa.prototype.getField=function(){return this.textarea},aa.prototype.supportsTouch=function(){return!1},aa.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!y||E(this.textarea.ownerDocument)!=this.textarea))try{this.textarea.focus()}catch(t){}},aa.prototype.blur=function(){this.textarea.blur()},aa.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},aa.prototype.receivedFocus=function(){this.slowPoll()},aa.prototype.slowPoll=function(){var t=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,(function(){t.poll(),t.cm.state.focused&&t.slowPoll()}))},aa.prototype.fastPoll=function(){var t=!1,e=this;function n(){e.poll()||t?(e.pollingFast=!1,e.slowPoll()):(t=!0,e.polling.set(60,n))}e.pollingFast=!0,e.polling.set(20,n)},aa.prototype.poll=function(){var t=this,e=this.cm,n=this.textarea,r=this.prevInput;if(this.contextMenuPending||this.resetting||!e.state.focused||Rt(n)&&!r&&!this.composing||e.isReadOnly()||e.options.disableInput||e.state.keySeq)return!1;var i=n.value;if(i==r&&!e.somethingSelected())return!1;if(s&&l>=9&&this.hasSelection===i||b&&/[\uf700-\uf7ff]/.test(i))return e.display.input.reset(),!1;if(e.doc.sel==e.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||r||(r="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var a=0,c=Math.min(r.length,i.length);a<c&&r.charCodeAt(a)==i.charCodeAt(a);)++a;return Wi(e,(function(){jl(e,i.slice(a),r.length-a,null,t.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?n.value=t.prevInput="":t.prevInput=i,t.composing&&(t.composing.range.clear(),t.composing.range=e.markText(t.composing.start,e.getCursor("to"),{className:"CodeMirror-composing"}))})),!0},aa.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},aa.prototype.onKeyPress=function(){s&&l>=9&&(this.hasSelection=null),this.fastPoll()},aa.prototype.onContextMenu=function(t){var e=this,n=e.cm,r=n.display,i=e.textarea;e.contextMenuPending&&e.contextMenuPending();var o=Rr(n,t),c=r.scroller.scrollTop;if(o&&!d){n.options.resetSelectionOnContextMenu&&-1==n.doc.sel.contains(o)&&Pi(n,Go)(n.doc,ho(o),j);var h,u=i.style.cssText,f=e.wrapper.style.cssText,p=e.wrapper.offsetParent.getBoundingClientRect();if(e.wrapper.style.cssText="position: static",i.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(t.clientY-p.top-5)+"px; left: "+(t.clientX-p.left-5)+"px;\n      z-index: 1000; background: "+(s?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",a&&(h=i.ownerDocument.defaultView.scrollY),r.input.focus(),a&&i.ownerDocument.defaultView.scrollTo(null,h),r.input.reset(),n.somethingSelected()||(i.value=e.prevInput=" "),e.contextMenuPending=v,r.selForContextMenu=n.doc.sel,clearTimeout(r.detectingSelectAll),s&&l>=9&&m(),k){Tt(t);var g=function(){bt(window,"mouseup",g),setTimeout(v,20)};vt(window,"mouseup",g)}else setTimeout(v,50)}function m(){if(null!=i.selectionStart){var t=n.somethingSelected(),o="​"+(t?i.value:"");i.value="⇚",i.value=o,e.prevInput=t?"":"​",i.selectionStart=1,i.selectionEnd=o.length,r.selForContextMenu=n.doc.sel}}function v(){if(e.contextMenuPending==v&&(e.contextMenuPending=!1,e.wrapper.style.cssText=f,i.style.cssText=u,s&&l<9&&r.scrollbars.setScrollTop(r.scroller.scrollTop=c),null!=i.selectionStart)){(!s||s&&l<9)&&m();var t=0,o=function(){r.selForContextMenu==n.doc.sel&&0==i.selectionStart&&i.selectionEnd>0&&"​"==e.prevInput?Pi(n,Qo)(n):t++<10?r.detectingSelectAll=setTimeout(o,500):(r.selForContextMenu=null,r.input.reset())};r.detectingSelectAll=setTimeout(o,200)}}},aa.prototype.readOnlyChanged=function(t){t||this.reset(),this.textarea.disabled="nocursor"==t,this.textarea.readOnly=!!t},aa.prototype.setUneditable=function(){},aa.prototype.needsContentAttribute=!1,zl(Il),Jl(Il);var ua="iter insert remove copy getEditor constructor".split(" ");for(var da in Ls.prototype)Ls.prototype.hasOwnProperty(da)&&$(ua,da)<0&&(Il.prototype[da]=function(t){return function(){return t.apply(this.doc,arguments)}}(Ls.prototype[da]));return kt(Ls),Il.inputStyles={textarea:aa,contenteditable:ea},Il.defineMode=function(t){Il.defaults.mode||"null"==t||(Il.defaults.mode=t),Ut.apply(this,arguments)},Il.defineMIME=Gt,Il.defineMode("null",(function(){return{token:function(t){return t.skipToEnd()}}})),Il.defineMIME("text/plain","null"),Il.defineExtension=function(t,e){Il.prototype[t]=e},Il.defineDocExtension=function(t,e){Ls.prototype[t]=e},Il.fromTextArea=ca,ha(Il),Il.version="5.65.12",Il}()},240:(t,e,n)=>{"use strict";n.d(e,{Z:()=>l});var r=n(81),i=n.n(r),o=n(645),s=n.n(o)()(i());s.push([t.id,"/* BASICS */\n\n.CodeMirror {\n  /* Set height, width, borders, and global font properties here */\n  font-family: monospace;\n  height: 300px;\n  color: black;\n  direction: ltr;\n}\n\n/* PADDING */\n\n.CodeMirror-lines {\n  padding: 4px 0; /* Vertical padding around content */\n}\n.CodeMirror pre.CodeMirror-line,\n.CodeMirror pre.CodeMirror-line-like {\n  padding: 0 4px; /* Horizontal padding of content */\n}\n\n.CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {\n  background-color: white; /* The little square between H and V scrollbars */\n}\n\n/* GUTTER */\n\n.CodeMirror-gutters {\n  border-right: 1px solid #ddd;\n  background-color: #f7f7f7;\n  white-space: nowrap;\n}\n.CodeMirror-linenumbers {}\n.CodeMirror-linenumber {\n  padding: 0 3px 0 5px;\n  min-width: 20px;\n  text-align: right;\n  color: #999;\n  white-space: nowrap;\n}\n\n.CodeMirror-guttermarker { color: black; }\n.CodeMirror-guttermarker-subtle { color: #999; }\n\n/* CURSOR */\n\n.CodeMirror-cursor {\n  border-left: 1px solid black;\n  border-right: none;\n  width: 0;\n}\n/* Shown when moving in bi-directional text */\n.CodeMirror div.CodeMirror-secondarycursor {\n  border-left: 1px solid silver;\n}\n.cm-fat-cursor .CodeMirror-cursor {\n  width: auto;\n  border: 0 !important;\n  background: #7e7;\n}\n.cm-fat-cursor div.CodeMirror-cursors {\n  z-index: 1;\n}\n.cm-fat-cursor .CodeMirror-line::selection,\n.cm-fat-cursor .CodeMirror-line > span::selection, \n.cm-fat-cursor .CodeMirror-line > span > span::selection { background: transparent; }\n.cm-fat-cursor .CodeMirror-line::-moz-selection,\n.cm-fat-cursor .CodeMirror-line > span::-moz-selection,\n.cm-fat-cursor .CodeMirror-line > span > span::-moz-selection { background: transparent; }\n.cm-fat-cursor { caret-color: transparent; }\n@-moz-keyframes blink {\n  0% {}\n  50% { background-color: transparent; }\n  100% {}\n}\n@-webkit-keyframes blink {\n  0% {}\n  50% { background-color: transparent; }\n  100% {}\n}\n@keyframes blink {\n  0% {}\n  50% { background-color: transparent; }\n  100% {}\n}\n\n/* Can style cursor different in overwrite (non-insert) mode */\n.CodeMirror-overwrite .CodeMirror-cursor {}\n\n.cm-tab { display: inline-block; text-decoration: inherit; }\n\n.CodeMirror-rulers {\n  position: absolute;\n  left: 0; right: 0; top: -50px; bottom: 0;\n  overflow: hidden;\n}\n.CodeMirror-ruler {\n  border-left: 1px solid #ccc;\n  top: 0; bottom: 0;\n  position: absolute;\n}\n\n/* DEFAULT THEME */\n\n.cm-s-default .cm-header {color: blue;}\n.cm-s-default .cm-quote {color: #090;}\n.cm-negative {color: #d44;}\n.cm-positive {color: #292;}\n.cm-header, .cm-strong {font-weight: bold;}\n.cm-em {font-style: italic;}\n.cm-link {text-decoration: underline;}\n.cm-strikethrough {text-decoration: line-through;}\n\n.cm-s-default .cm-keyword {color: #708;}\n.cm-s-default .cm-atom {color: #219;}\n.cm-s-default .cm-number {color: #164;}\n.cm-s-default .cm-def {color: #00f;}\n.cm-s-default .cm-variable,\n.cm-s-default .cm-punctuation,\n.cm-s-default .cm-property,\n.cm-s-default .cm-operator {}\n.cm-s-default .cm-variable-2 {color: #05a;}\n.cm-s-default .cm-variable-3, .cm-s-default .cm-type {color: #085;}\n.cm-s-default .cm-comment {color: #a50;}\n.cm-s-default .cm-string {color: #a11;}\n.cm-s-default .cm-string-2 {color: #f50;}\n.cm-s-default .cm-meta {color: #555;}\n.cm-s-default .cm-qualifier {color: #555;}\n.cm-s-default .cm-builtin {color: #30a;}\n.cm-s-default .cm-bracket {color: #997;}\n.cm-s-default .cm-tag {color: #170;}\n.cm-s-default .cm-attribute {color: #00c;}\n.cm-s-default .cm-hr {color: #999;}\n.cm-s-default .cm-link {color: #00c;}\n\n.cm-s-default .cm-error {color: #f00;}\n.cm-invalidchar {color: #f00;}\n\n.CodeMirror-composing { border-bottom: 2px solid; }\n\n/* Default styles for common addons */\n\ndiv.CodeMirror span.CodeMirror-matchingbracket {color: #0b0;}\ndiv.CodeMirror span.CodeMirror-nonmatchingbracket {color: #a22;}\n.CodeMirror-matchingtag { background: rgba(255, 150, 0, .3); }\n.CodeMirror-activeline-background {background: #e8f2ff;}\n\n/* STOP */\n\n/* The rest of this file contains styles related to the mechanics of\n   the editor. You probably shouldn't touch them. */\n\n.CodeMirror {\n  position: relative;\n  overflow: hidden;\n  background: white;\n}\n\n.CodeMirror-scroll {\n  overflow: scroll !important; /* Things will break if this is overridden */\n  /* 50px is the magic margin used to hide the element's real scrollbars */\n  /* See overflow: hidden in .CodeMirror */\n  margin-bottom: -50px; margin-right: -50px;\n  padding-bottom: 50px;\n  height: 100%;\n  outline: none; /* Prevent dragging from highlighting the element */\n  position: relative;\n  z-index: 0;\n}\n.CodeMirror-sizer {\n  position: relative;\n  border-right: 50px solid transparent;\n}\n\n/* The fake, visible scrollbars. Used to force redraw during scrolling\n   before actual scrolling happens, thus preventing shaking and\n   flickering artifacts. */\n.CodeMirror-vscrollbar, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {\n  position: absolute;\n  z-index: 6;\n  display: none;\n  outline: none;\n}\n.CodeMirror-vscrollbar {\n  right: 0; top: 0;\n  overflow-x: hidden;\n  overflow-y: scroll;\n}\n.CodeMirror-hscrollbar {\n  bottom: 0; left: 0;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n.CodeMirror-scrollbar-filler {\n  right: 0; bottom: 0;\n}\n.CodeMirror-gutter-filler {\n  left: 0; bottom: 0;\n}\n\n.CodeMirror-gutters {\n  position: absolute; left: 0; top: 0;\n  min-height: 100%;\n  z-index: 3;\n}\n.CodeMirror-gutter {\n  white-space: normal;\n  height: 100%;\n  display: inline-block;\n  vertical-align: top;\n  margin-bottom: -50px;\n}\n.CodeMirror-gutter-wrapper {\n  position: absolute;\n  z-index: 4;\n  background: none !important;\n  border: none !important;\n}\n.CodeMirror-gutter-background {\n  position: absolute;\n  top: 0; bottom: 0;\n  z-index: 4;\n}\n.CodeMirror-gutter-elt {\n  position: absolute;\n  cursor: default;\n  z-index: 4;\n}\n.CodeMirror-gutter-wrapper ::selection { background-color: transparent }\n.CodeMirror-gutter-wrapper ::-moz-selection { background-color: transparent }\n\n.CodeMirror-lines {\n  cursor: text;\n  min-height: 1px; /* prevents collapsing before first draw */\n}\n.CodeMirror pre.CodeMirror-line,\n.CodeMirror pre.CodeMirror-line-like {\n  /* Reset some styles that the rest of the page might have set */\n  -moz-border-radius: 0; -webkit-border-radius: 0; border-radius: 0;\n  border-width: 0;\n  background: transparent;\n  font-family: inherit;\n  font-size: inherit;\n  margin: 0;\n  white-space: pre;\n  word-wrap: normal;\n  line-height: inherit;\n  color: inherit;\n  z-index: 2;\n  position: relative;\n  overflow: visible;\n  -webkit-tap-highlight-color: transparent;\n  -webkit-font-variant-ligatures: contextual;\n  font-variant-ligatures: contextual;\n}\n.CodeMirror-wrap pre.CodeMirror-line,\n.CodeMirror-wrap pre.CodeMirror-line-like {\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  word-break: normal;\n}\n\n.CodeMirror-linebackground {\n  position: absolute;\n  left: 0; right: 0; top: 0; bottom: 0;\n  z-index: 0;\n}\n\n.CodeMirror-linewidget {\n  position: relative;\n  z-index: 2;\n  padding: 0.1px; /* Force widget margins to stay inside of the container */\n}\n\n.CodeMirror-widget {}\n\n.CodeMirror-rtl pre { direction: rtl; }\n\n.CodeMirror-code {\n  outline: none;\n}\n\n/* Force content-box sizing for the elements where we expect it */\n.CodeMirror-scroll,\n.CodeMirror-sizer,\n.CodeMirror-gutter,\n.CodeMirror-gutters,\n.CodeMirror-linenumber {\n  -moz-box-sizing: content-box;\n  box-sizing: content-box;\n}\n\n.CodeMirror-measure {\n  position: absolute;\n  width: 100%;\n  height: 0;\n  overflow: hidden;\n  visibility: hidden;\n}\n\n.CodeMirror-cursor {\n  position: absolute;\n  pointer-events: none;\n}\n.CodeMirror-measure pre { position: static; }\n\ndiv.CodeMirror-cursors {\n  visibility: hidden;\n  position: relative;\n  z-index: 3;\n}\ndiv.CodeMirror-dragcursors {\n  visibility: visible;\n}\n\n.CodeMirror-focused div.CodeMirror-cursors {\n  visibility: visible;\n}\n\n.CodeMirror-selected { background: #d9d9d9; }\n.CodeMirror-focused .CodeMirror-selected { background: #d7d4f0; }\n.CodeMirror-crosshair { cursor: crosshair; }\n.CodeMirror-line::selection, .CodeMirror-line > span::selection, .CodeMirror-line > span > span::selection { background: #d7d4f0; }\n.CodeMirror-line::-moz-selection, .CodeMirror-line > span::-moz-selection, .CodeMirror-line > span > span::-moz-selection { background: #d7d4f0; }\n\n.cm-searching {\n  background-color: #ffa;\n  background-color: rgba(255, 255, 0, .4);\n}\n\n/* Used to force a border model for a node */\n.cm-force-border { padding-right: .1px; }\n\n@media print {\n  /* Hide the cursor when printing */\n  .CodeMirror div.CodeMirror-cursors {\n    visibility: hidden;\n  }\n}\n\n/* See issue #2901 */\n.cm-tab-wrap-hack:after { content: ''; }\n\n/* Help users use markselection to safely style text background */\nspan.CodeMirror-selectedtext { background: none; }\n",""]);const l=s},645:t=>{"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var n="",r=void 0!==e[5];return e[4]&&(n+="@supports (".concat(e[4],") {")),e[2]&&(n+="@media ".concat(e[2]," {")),r&&(n+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),n+=t(e),r&&(n+="}"),e[2]&&(n+="}"),e[4]&&(n+="}"),n})).join("")},e.i=function(t,n,r,i,o){"string"==typeof t&&(t=[[null,t,void 0]]);var s={};if(r)for(var l=0;l<this.length;l++){var a=this[l][0];null!=a&&(s[a]=!0)}for(var c=0;c<t.length;c++){var h=[].concat(t[c]);r&&s[h[0]]||(void 0!==o&&(void 0===h[5]||(h[1]="@layer".concat(h[5].length>0?" ".concat(h[5]):""," {").concat(h[1],"}")),h[5]=o),n&&(h[2]?(h[1]="@media ".concat(h[2]," {").concat(h[1],"}"),h[2]=n):h[2]=n),i&&(h[4]?(h[1]="@supports (".concat(h[4],") {").concat(h[1],"}"),h[4]=i):h[4]="".concat(i)),e.push(h))}},e}},81:t=>{"use strict";t.exports=function(t){return t[1]}},380:(t,e,n)=>{"use strict";n.r(e),n.d(e,{default:()=>v});var r=n(379),i=n.n(r),o=n(795),s=n.n(o),l=n(569),a=n.n(l),c=n(565),h=n.n(c),u=n(216),d=n.n(u),f=n(589),p=n.n(f),g=n(240),m={};m.styleTagTransform=p(),m.setAttributes=h(),m.insert=a().bind(null,"head"),m.domAPI=s(),m.insertStyleElement=d();i()(g.Z,m);const v=g.Z&&g.Z.locals?g.Z.locals:void 0},379:t=>{"use strict";var e=[];function n(t){for(var n=-1,r=0;r<e.length;r++)if(e[r].identifier===t){n=r;break}return n}function r(t,r){for(var o={},s=[],l=0;l<t.length;l++){var a=t[l],c=r.base?a[0]+r.base:a[0],h=o[c]||0,u="".concat(c," ").concat(h);o[c]=h+1;var d=n(u),f={css:a[1],media:a[2],sourceMap:a[3],supports:a[4],layer:a[5]};if(-1!==d)e[d].references++,e[d].updater(f);else{var p=i(f,r);r.byIndex=l,e.splice(l,0,{identifier:u,updater:p,references:1})}s.push(u)}return s}function i(t,e){var n=e.domAPI(e);n.update(t);return function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap&&e.supports===t.supports&&e.layer===t.layer)return;n.update(t=e)}else n.remove()}}t.exports=function(t,i){var o=r(t=t||[],i=i||{});return function(t){t=t||[];for(var s=0;s<o.length;s++){var l=n(o[s]);e[l].references--}for(var a=r(t,i),c=0;c<o.length;c++){var h=n(o[c]);0===e[h].references&&(e[h].updater(),e.splice(h,1))}o=a}}},569:t=>{"use strict";var e={};t.exports=function(t,n){var r=function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(t){n=null}e[t]=n}return e[t]}(t);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},216:t=>{"use strict";t.exports=function(t){var e=document.createElement("style");return t.setAttributes(e,t.attributes),t.insert(e,t.options),e}},565:(t,e,n)=>{"use strict";t.exports=function(t){var e=n.nc;e&&t.setAttribute("nonce",e)}},795:t=>{"use strict";t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var e=t.insertStyleElement(t);return{update:function(n){!function(t,e,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var i=void 0!==n.layer;i&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,i&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var o=n.sourceMap;o&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),e.styleTagTransform(r,t,e.options)}(e,t,n)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(e)}}}},589:t=>{"use strict";t.exports=function(t,e){if(e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},93:(t,e,n)=>{"use strict";n.r(e),n.d(e,{default:()=>o});var r=n(477),i=n.n(r);function o(){return i()("/******/ (() => { // webpackBootstrap\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 242:\n/***/ ((module) => {\n\nconst changeExp = new RegExp(/(^(?![><\\-])*\\d+(?:,\\d+)?)([acd])(\\d+(?:,\\d+)?)/);\n\nfunction DiffParser(diff) {\n\tconst changes = [];\n\tlet change_id = 0;\n\t// parse diff\n\tconst diff_lines = diff.split(/\\n/);\n\tfor (var i = 0; i < diff_lines.length; ++i) {\n\t\tif (diff_lines[i].length == 0) continue;\n\t\tconst change = {};\n\t\tconst test = changeExp.exec(diff_lines[i]);\n\t\tif (test == null) continue;\n\t\t// lines are zero-based\n\t\tconst fr = test[1].split(',');\n\t\tchange['lhs-line-from'] = fr[0] - 1;\n\t\tif (fr.length == 1) change['lhs-line-to'] = fr[0] - 1;\n\t\telse change['lhs-line-to'] = fr[1] - 1;\n\t\tconst to = test[3].split(',');\n\t\tchange['rhs-line-from'] = to[0] - 1;\n\t\tif (to.length == 1) change['rhs-line-to'] = to[0] - 1;\n\t\telse change['rhs-line-to'] = to[1] - 1;\n\t\tchange['op'] = test[2];\n\t\tchanges[change_id++] = change;\n\t}\n\treturn changes;\n};\n\nmodule.exports = DiffParser;\n\n\n/***/ }),\n\n/***/ 315:\n/***/ ((module) => {\n\nconst SMS_TIMEOUT_SECONDS = 1.0;\n\nfunction diff(lhs, rhs, options = {}) {\n\tconst {\n\t\tignorews = false,\n\t\tignoreaccents = false,\n\t\tignorecase = false,\n\t\tsplit = 'lines'\n\t} = options;\n\n\tthis.codeify = new CodeifyText(lhs, rhs, {\n\t\tignorews,\n\t\tignoreaccents,\n\t\tignorecase,\n\t\tsplit\n\t});\n\tconst lhs_ctx = {\n\t\tcodes: this.codeify.getCodes('lhs'),\n\t\tmodified: {}\n\t};\n\tconst rhs_ctx = {\n\t\tcodes: this.codeify.getCodes('rhs'),\n\t\tmodified: {}\n\t};\n\tconst vector_d = [];\n\tconst vector_u = [];\n\tthis._lcs(lhs_ctx, 0, lhs_ctx.codes.length, rhs_ctx, 0, rhs_ctx.codes.length, vector_u, vector_d);\n\tthis._optimize(lhs_ctx);\n\tthis._optimize(rhs_ctx);\n\tthis.items = this._create_diffs(lhs_ctx, rhs_ctx);\n};\n\ndiff.prototype.changes = function() {\n\treturn this.items;\n};\n\ndiff.prototype.getLines = function(side) {\n\treturn this.codeify.getLines(side);\n};\n\ndiff.prototype.normal_form = function() {\n\tlet nf = '';\n\tfor (let index = 0; index < this.items.length; ++index) {\n\t\tconst item = this.items[index];\n\t\tlet lhs_str = '';\n\t\tlet rhs_str = '';\n\t\tlet change = 'c';\n\t\tif (item.lhs_deleted_count === 0 && item.rhs_inserted_count > 0) change = 'a';\n\t\telse if (item.lhs_deleted_count > 0 && item.rhs_inserted_count === 0) change = 'd';\n\n\t\tif (item.lhs_deleted_count === 1) lhs_str = item.lhs_start + 1;\n\t\telse if (item.lhs_deleted_count === 0) lhs_str = item.lhs_start;\n\t\telse lhs_str = (item.lhs_start + 1) + ',' + (item.lhs_start + item.lhs_deleted_count);\n\n\t\tif (item.rhs_inserted_count === 1) rhs_str = item.rhs_start + 1;\n\t\telse if (item.rhs_inserted_count === 0) rhs_str = item.rhs_start;\n\t\telse rhs_str = (item.rhs_start + 1) + ',' + (item.rhs_start + item.rhs_inserted_count);\n\t\tnf += lhs_str + change + rhs_str + '\\n';\n\n\t\tconst lhs_lines = this.getLines('lhs');\n\t\tconst rhs_lines = this.getLines('rhs');\n\t\tif (rhs_lines && lhs_lines) {\n\t\t\tlet i;\n\t\t\t// if rhs/lhs lines have been retained, output contextual diff\n\t\t\tfor (i = item.lhs_start; i < item.lhs_start + item.lhs_deleted_count; ++i) {\n\t\t\t\tnf += '< ' + lhs_lines[i] + '\\n';\n\t\t\t}\n\t\t\tif (item.rhs_inserted_count && item.lhs_deleted_count) nf += '---\\n';\n\t\t\tfor (i = item.rhs_start; i < item.rhs_start + item.rhs_inserted_count; ++i) {\n\t\t\t\tnf += '> ' + rhs_lines[i] + '\\n';\n\t\t\t}\n\t\t}\n\t}\n\treturn nf;\n};\n\ndiff.prototype._lcs = function(lhs_ctx, lhs_lower, lhs_upper, rhs_ctx, rhs_lower, rhs_upper, vector_u, vector_d) {\n\twhile ( (lhs_lower < lhs_upper) && (rhs_lower < rhs_upper) && (lhs_ctx.codes[lhs_lower] === rhs_ctx.codes[rhs_lower]) ) {\n\t\t++lhs_lower;\n\t\t++rhs_lower;\n\t}\n\twhile ( (lhs_lower < lhs_upper) && (rhs_lower < rhs_upper) && (lhs_ctx.codes[lhs_upper - 1] === rhs_ctx.codes[rhs_upper - 1]) ) {\n\t\t--lhs_upper;\n\t\t--rhs_upper;\n\t}\n\tif (lhs_lower === lhs_upper) {\n\t\twhile (rhs_lower < rhs_upper) {\n\t\t\trhs_ctx.modified[ rhs_lower++ ] = true;\n\t\t}\n\t}\n\telse if (rhs_lower === rhs_upper) {\n\t\twhile (lhs_lower < lhs_upper) {\n\t\t\tlhs_ctx.modified[ lhs_lower++ ] = true;\n\t\t}\n\t}\n\telse {\n\t\tconst sms = this._sms(lhs_ctx, lhs_lower, lhs_upper, rhs_ctx, rhs_lower, rhs_upper, vector_u, vector_d);\n\t\tthis._lcs(lhs_ctx, lhs_lower, sms.x, rhs_ctx, rhs_lower, sms.y, vector_u, vector_d);\n\t\tthis._lcs(lhs_ctx, sms.x, lhs_upper, rhs_ctx, sms.y, rhs_upper, vector_u, vector_d);\n\t}\n};\n\ndiff.prototype._sms = function(lhs_ctx, lhs_lower, lhs_upper, rhs_ctx, rhs_lower, rhs_upper, vector_u, vector_d) {\n\tconst timeout = Date.now() + SMS_TIMEOUT_SECONDS * 1000;\n\tconst max = lhs_ctx.codes.length + rhs_ctx.codes.length + 1;\n\tconst kdown = lhs_lower - rhs_lower;\n\tconst kup = lhs_upper - rhs_upper;\n\tconst delta = (lhs_upper - lhs_lower) - (rhs_upper - rhs_lower);\n\tconst odd = (delta & 1) != 0;\n\tconst offset_down = max - kdown;\n\tconst offset_up = max - kup;\n\tconst maxd = ((lhs_upper - lhs_lower + rhs_upper - rhs_lower) / 2) + 1;\n\tvector_d[ offset_down + kdown + 1 ] = lhs_lower;\n\tvector_u[ offset_up + kup - 1 ] = lhs_upper;\n\tconst ret = { x:0, y:0 }\n\tlet x;\n\tlet y;\n\tfor (let d = 0; d <= maxd; ++d) {\n\t\tif (SMS_TIMEOUT_SECONDS && Date.now() > timeout) {\n\t\t\t// bail if taking too long\n\t\t\treturn { x: lhs_lower, y: rhs_upper };\n\t\t}\n\t\tfor (let k = kdown - d; k <= kdown + d; k += 2) {\n\t\t\tif (k === kdown - d) {\n\t\t\t\tx = vector_d[ offset_down + k + 1 ];//down\n\t\t\t}\n\t\t\telse {\n\t\t\t\tx = vector_d[ offset_down + k - 1 ] + 1;//right\n\t\t\t\tif ((k < (kdown + d)) && (vector_d[ offset_down + k + 1 ] >= x)) {\n\t\t\t\t\tx = vector_d[ offset_down + k + 1 ];//down\n\t\t\t\t}\n\t\t\t}\n\t\t\ty = x - k;\n\t\t\t// find the end of the furthest reaching forward D-path in diagonal k.\n\t\t\twhile ((x < lhs_upper) && (y < rhs_upper) && (lhs_ctx.codes[x] === rhs_ctx.codes[y])) {\n\t\t\t\tx++; y++;\n\t\t\t}\n\t\t\tvector_d[ offset_down + k ] = x;\n\t\t\t// overlap ?\n\t\t\tif (odd && (kup - d < k) && (k < kup + d)) {\n\t\t\t\tif (vector_u[offset_up + k] <= vector_d[offset_down + k]) {\n\t\t\t\t\tret.x = vector_d[offset_down + k];\n\t\t\t\t\tret.y = vector_d[offset_down + k] - k;\n\t\t\t\t\treturn (ret);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t// Extend the reverse path.\n\t\tfor (k = kup - d; k <= kup + d; k += 2) {\n\t\t\t// find the only or better starting point\n\t\t\tif (k === kup + d) {\n\t\t\t\tx = vector_u[offset_up + k - 1]; // up\n\t\t\t} else {\n\t\t\t\tx = vector_u[offset_up + k + 1] - 1; // left\n\t\t\t\tif ((k > kup - d) && (vector_u[offset_up + k - 1] < x))\n\t\t\t\t\tx = vector_u[offset_up + k - 1]; // up\n\t\t\t}\n\t\t\ty = x - k;\n\t\t\twhile ((x > lhs_lower) && (y > rhs_lower) && (lhs_ctx.codes[x - 1] === rhs_ctx.codes[y - 1])) {\n\t\t\t\t// diagonal\n\t\t\t\tx--;\n\t\t\t\ty--;\n\t\t\t}\n\t\t\tvector_u[offset_up + k] = x;\n\t\t\t// overlap ?\n\t\t\tif (!odd && (kdown - d <= k) && (k <= kdown + d)) {\n\t\t\t\tif (vector_u[offset_up + k] <= vector_d[offset_down + k]) {\n\t\t\t\t\tret.x = vector_d[offset_down + k];\n\t\t\t\t\tret.y = vector_d[offset_down + k] - k;\n\t\t\t\t\treturn (ret);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\tthrow \"the algorithm should never come here.\";\n};\n\ndiff.prototype._optimize = function(ctx) {\n\tlet start = 0;\n\tlet end = 0;\n\twhile (start < ctx.codes.length) {\n\t\twhile ((start < ctx.codes.length) && (ctx.modified[start] === undefined || ctx.modified[start] === false)) {\n\t\t\tstart++;\n\t\t}\n\t\tend = start;\n\t\twhile ((end < ctx.codes.length) && (ctx.modified[end] === true)) {\n\t\t\tend++;\n\t\t}\n\t\tif ((end < ctx.codes.length) && (ctx.codes[start] === ctx.codes[end])) {\n\t\t\tctx.modified[start] = false;\n\t\t\tctx.modified[end] = true;\n\t\t}\n\t\telse {\n\t\t\tstart = end;\n\t\t}\n\t}\n};\n\ndiff.prototype._create_diffs = function(lhs_ctx, rhs_ctx) {\n\tconst items = [];\n\tlet lhs_start = 0;\n\tlet rhs_start = 0;\n\tlet lhs_line = 0;\n\tlet rhs_line = 0;\n\n\twhile (lhs_line < lhs_ctx.codes.length || rhs_line < rhs_ctx.codes.length) {\n\t\tif ((lhs_line < lhs_ctx.codes.length) && (!lhs_ctx.modified[lhs_line])\n\t\t\t&& (rhs_line < rhs_ctx.codes.length) && (!rhs_ctx.modified[rhs_line])) {\n\t\t\t// equal lines\n\t\t\tlhs_line++;\n\t\t\trhs_line++;\n\t\t}\n\t\telse {\n\t\t\t// maybe deleted and/or inserted lines\n\t\t\tlhs_start = lhs_line;\n\t\t\trhs_start = rhs_line;\n\n\t\t\twhile (lhs_line < lhs_ctx.codes.length && (rhs_line >= rhs_ctx.codes.length || lhs_ctx.modified[lhs_line]))\n\t\t\t\tlhs_line++;\n\n\t\t\twhile (rhs_line < rhs_ctx.codes.length && (lhs_line >= lhs_ctx.codes.length || rhs_ctx.modified[rhs_line]))\n\t\t\t\trhs_line++;\n\n\t\t\tif ((lhs_start < lhs_line) || (rhs_start < rhs_line)) {\n\t\t\t\t// store a new difference-item\n\t\t\t\titems.push({\n\t\t\t\t\tlhs_start: lhs_start,\n\t\t\t\t\trhs_start: rhs_start,\n\t\t\t\t\tlhs_deleted_count: lhs_line - lhs_start,\n\t\t\t\t\trhs_inserted_count: rhs_line - rhs_start\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\treturn items;\n};\n\nfunction CodeifyText(lhs, rhs, options) {\n    this._max_code = 0;\n    this._diff_codes = {};\n\tthis.ctxs = {};\n\tthis.options = options;\n\tthis.options.split = this.options.split || 'lines';\n\n\tif (typeof lhs === 'string') {\n\t\tif (this.options.split === 'chars') {\n\t\t\tthis.lhs = lhs.split('');\n\t\t} else if (this.options.split === 'words') {\n\t\t\tthis.lhs = lhs.split(/\\s/);\n\t\t} else if (this.options.split === 'lines') {\n\t\t\tthis.lhs = lhs.split('\\n');\n\t\t}\n\t} else {\n\t\tthis.lhs = lhs;\n\t}\n\tif (typeof rhs === 'string') {\n\t\tif (this.options.split === 'chars') {\n\t\t\tthis.rhs = rhs.split('');\n\t\t} else if (this.options.split === 'words') {\n\t\t\tthis.rhs = rhs.split(/\\s/);\n\t\t} else if (this.options.split === 'lines') {\n\t\t\tthis.rhs = rhs.split('\\n');\n\t\t}\n\t} else {\n\t\tthis.rhs = rhs;\n\t}\n};\n\nCodeifyText.prototype.getCodes = function(side) {\n\tif (!this.ctxs.hasOwnProperty(side)) {\n\t\tvar ctx = this._diff_ctx(this[side]);\n\t\tthis.ctxs[side] = ctx;\n\t\tctx.codes.length = Object.keys(ctx.codes).length;\n\t}\n\treturn this.ctxs[side].codes;\n}\n\nCodeifyText.prototype.getLines = function(side) {\n\treturn this.ctxs[side].lines;\n}\n\nCodeifyText.prototype._diff_ctx = function(lines) {\n\tvar ctx = {i: 0, codes: {}, lines: lines};\n\tthis._codeify(lines, ctx);\n\treturn ctx;\n}\n\nCodeifyText.prototype._codeify = function(lines, ctx) {\n\tfor (let i = 0; i < lines.length; ++i) {\n\t\tlet line = lines[i];\n\t\tif (this.options.ignorews) {\n\t\t\tline = line.replace(/\\s+/g, '');\n\t\t}\n\t\tif (this.options.ignorecase) {\n\t\t\tline = line.toLowerCase();\n\t\t}\n\t\tif (this.options.ignoreaccents) {\n\t\t\tline = line.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');\n\t\t}\n\t\tconst aCode = this._diff_codes[line];\n\t\tif (aCode !== undefined) {\n\t\t\tctx.codes[i] = aCode;\n\t\t} else {\n\t\t\t++this._max_code;\n\t\t\tthis._diff_codes[line] = this._max_code;\n\t\t\tctx.codes[i] = this._max_code;\n\t\t}\n\t}\n}\n\nmodule.exports = diff;\n\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n// This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.\n(() => {\nconst diff = __webpack_require__(315);\nconst DiffParser = __webpack_require__(242);\n\nonmessage = function (ev) {\n\tif (!ev.data) {\n\t\treturn;\n\t}\n\tconst { lhs, rhs, options } = ev.data;\n\tconst compare = new diff(lhs, rhs, options);\n\tconst changes = DiffParser(compare.normal_form());\n\tpostMessage(changes);\n};\n\n})();\n\n/******/ })()\n;","Worker",void 0,void 0)}},477:t=>{"use strict";t.exports=function(t,e,n,r){var i=self||window;try{try{var o;try{o=new i.Blob([t])}catch(e){(o=new(i.BlobBuilder||i.WebKitBlobBuilder||i.MozBlobBuilder||i.MSBlobBuilder)).append(t),o=o.getBlob()}var s=i.URL||i.webkitURL,l=s.createObjectURL(o),a=new i[e](l,n);return s.revokeObjectURL(l),a}catch(r){return new i[e]("data:application/javascript,".concat(encodeURIComponent(t)),n)}}catch(t){if(!r)throw Error("Inline worker is not supported");return new i[e](r,n)}}},316:(t,e,n)=>{const r=n(631);n(95),n(20),n(380);const i=n(171),o=n(563),s=["lgpl-separate-notice","gpl-separate-notice","mpl-separate-notice","commercial"];function l(t,e){r.defineExtension("centerOnCursor",(function(){const t=this.cursorCoords(null,"local");this.scrollTo(null,(t.top+t.bottom)/2-this.getScrollerElement().clientHeight/2)})),this.init(t,e)}const a=console.log,c=console.time,h=console.timeEnd;l.prototype.init=function(t,e={}){this.setOptions(e),this.el=t,this.lhs_cmsettings={...this.settings.cmsettings,...this.settings.lhs_cmsettings,lineWrapping:this.settings.wrap_lines,lineNumbers:this.settings.line_numbers,gutters:this.settings.line_numbers&&["merge","CodeMirror-linenumbers"]||[]},this.rhs_cmsettings={...this.settings.cmsettings,...this.settings.rhs_cmsettings,lineWrapping:this.settings.wrap_lines,lineNumbers:this.settings.line_numbers,gutters:this.settings.line_numbers&&["merge","CodeMirror-linenumbers"]||[]},this._vdoc=new o({_debug:this.settings._debug}),this._linkedScrollTimeout={}},l.prototype.unbind=function(){if(!this._unbound){for(this.settings._debug&&a("api#unbind"),null!=this._changedTimeout&&clearTimeout(this._changedTimeout),this.editor&&(this.editor.lhs&&this.editor.lhs.toTextArea(),this.editor.rhs&&this.editor.rhs.toTextArea());this.el.lastChild;)this.el.removeChild(this.el.lastChild);this._origEl&&(this.el.style=this._origEl.style,this.el.className=this._origEl.className),this._unbound=!0}},l.prototype.readOnly=function(t){return"lhs"===t?this.lhs_cmsettings.readOnly:"rhs"===t?this.rhs_cmsettings.readOnly:this.lhs_cmsettings.readOnly||this.rhs_cmsettings.readOnly},l.prototype.lhs=function(t){this.settings._debug&&a("api#lhs",t&&t.length),this.changes=[],this._current_diff=-1,this.editor.lhs.setValue(t)},l.prototype.rhs=function(t){this.settings._debug&&a("api#rhs",t&&t.length),this.changes=[],this._current_diff=-1,this.editor.rhs.setValue(t)},l.prototype.update=function(){this.settings._debug&&a("api#update"),this.el.dispatchEvent(new Event("changed"))},l.prototype.unmarkup=function(){this.settings._debug&&a("api#unmarkup"),this._clear(),this.el.dispatchEvent(new Event("updated"))},l.prototype.scrollToDiff=function(t){this.settings._debug&&a("api#scrollToDiff",t),this.changes.length&&("next"===t?this._current_diff===this.changes.length-1||void 0===this._current_diff?this._current_diff=0:this._current_diff=Math.min(++this._current_diff,this.changes.length-1):"prev"===t&&(0==this._current_diff||void 0===this._current_diff?this._current_diff=this.changes.length-1:this._current_diff=Math.max(--this._current_diff,0)),this.settings._debug&&a("change","current-diff",this._current_diff),this._scroll_to_change(this.changes[this._current_diff]),this.setChanges(this.changes))},l.prototype.mergeCurrentChange=function(t){this.settings._debug&&a("api#mergeCurrentChange",t),this.changes.length&&("lhs"!=t||this.lhs_cmsettings.readOnly?"rhs"!=t||this.rhs_cmsettings.readOnly||this._merge_change(this.changes[this._current_diff],"lhs","rhs"):this._merge_change(this.changes[this._current_diff],"rhs","lhs"))},l.prototype.scrollTo=function(t,e){this.settings._debug&&a("api#scrollTo",t,e);const n=this.editor[t];n.setCursor(e),n.centerOnCursor(),this._renderChanges(),this.el.dispatchEvent(new Event("updated"))},l.prototype.setOptions=function(t){const e=this.settings?this.settings.rhs_margin:"right";if(this.settings={...this.settings,...t},this.settings._debug&&a("api#setOptions",t),this.editor){if(t.hasOwnProperty("sidebar")){const e=document.querySelectorAll(`#${this.id} .mergely-margin`),n=!!t.sidebar;for(const t of e)t.style.visibility=n?"visible":"hidden"}const n=this.editor.lhs,r=this.editor.rhs;if(t.hasOwnProperty("wrap_lines")&&(n.setOption("lineWrapping",this.settings.wrap_lines),r.setOption("lineWrapping",this.settings.wrap_lines)),t.hasOwnProperty("line_numbers")&&(n.setOption("lineNumbers",this.settings.line_numbers),r.setOption("lineNumbers",this.settings.line_numbers)),t.hasOwnProperty("rhs_margin")&&t.rhs_margin!==e){const t=document.querySelectorAll(`#${this.id} .mergely-editor > div`);t[4].parentNode.insertBefore(t[4],t[3])}this._changing()}},l.prototype.get=function(t){this.settings._debug&&a("api#get",t);const e=this.editor[t].getValue();return void 0===e?"":e},l.prototype.cm=function(t){return this.settings._debug&&a("api#cm","side"),this.editor[t]},l.prototype.search=function(t,e,n){this.settings._debug&&a("api#search",t,e,n);const r=this.editor[t];if(!r.getSearchCursor)throw new Error("install CodeMirror search addon");const i="prev"===n?"findPrevious":"findNext",o={line:0,ch:0};0!==r.getSelection().length&&this.prev_query[t]===e||(this.cursor[this.id]=r.getSearchCursor(e,o,!1),this.prev_query[t]=e);let s=this.cursor[this.id];s[i]()?r.setSelection(s.from(),s.to()):(s=this.cursor[this.id]=r.getSearchCursor(e,o,!1),this.prev_query[t]=e,s[i]()&&r.setSelection(s.from(),s.to()))},l.prototype.resize=function(){this.settings._debug&&a("api#resize");const t=this.el.offsetHeight-2,e=this._queryElement(`#${this.id}-lhs-margin`);e.style.height=`${t}px`,e.height=`${t}`;const n=this._queryElement(`#${this.id}-lhs-rhs-canvas`);n.style.height=`${t}px`,n.height=`${t}`;const r=this._queryElement(`#${this.id}-rhs-margin`);r.style.height=`${t}px`,r.height=`${t}`,this.el.dispatchEvent(new Event("resized")),this.em_height=null,this._changing(),this._set_top_offset("lhs")},l.prototype.bind=function(t){this.settings._debug&&a("api#bind",t),this._origEl={style:t.style,className:t.className};const e=i.getMergelyContainer({clazz:t.className}),n=window.getComputedStyle(t);if(!n.height||"0px"===n.height)throw new Error(`The element "${t.id}" requires an explicit height`);this.id=`${t.id}`,this.lhsId=`${t.id}-lhs`,this.rhsId=`${t.id}-rhs`,this.chfns={lhs:[],rhs:[]},this.prev_query=[],this.cursor=[],this._skipscroll={},this.change_exp=new RegExp(/(\d+(?:,\d+)?)([acd])(\d+(?:,\d+)?)/);this.merge_lhs_button=i.htmlToElement('<div class="merge-button" title="Merge left">&#x25C0;</div>'),this.merge_rhs_button=i.htmlToElement('<div class="merge-button" title="Merge right">&#x25B6;</div>');const o=i.getMarginTemplate({id:this.lhsId}),l=i.getMarginTemplate({id:this.rhsId}),u=i.getEditorTemplate({id:this.lhsId}),d=i.getEditorTemplate({id:this.rhsId}),f=i.getCenterCanvasTemplate({id:this.id});e.append(o),e.append(u),e.append(f),"left"==this.settings.rhs_margin&&e.append(l),e.append(d),"left"!=this.settings.rhs_margin&&e.append(l),this.settings.sidebar||(o.style.visibility="hidden",l.style.visibility="hidden"),t.append(e),s.indexOf(this.settings.license)<0&&t.addEventListener("updated",(()=>{const t={lgpl:"GNU LGPL v3.0",gpl:"GNU GPL v3.0",mpl:"MPL 1.1"};let n=t[this.settings.license];n||(n=t.lgpl);const r=this._queryElement(`#${this.id}`),o=i.getSplash({notice:n,left:(r.offsetWidth-300)/2,top:(r.offsetHeight-58)/3});r.addEventListener("click",(()=>{o.style.visibility="hidden",o.style.opacity="0",o.style.transition="visibility 0s 100ms, opacity 100ms linear",setTimeout((()=>o.remove()),110)}),{once:!0}),e.append(o)}),{once:!0});const p=document.getElementById(`${this.id}-lhs`),g=document.getElementById(`${this.id}-rhs`);if(p||console.error("lhs textarea not defined - Mergely not initialized properly"),g||console.error("rhs textarea not defined - Mergely not initialized properly"),this._current_diff=-1,this.editor={},this.editor.lhs=r.fromTextArea(p,this.lhs_cmsettings),this.editor.rhs=r.fromTextArea(g,this.rhs_cmsettings),this.settings.lhs){const t=typeof this.settings.lhs;"string"===t?this.lhs(this.settings.lhs):"function"===t&&this.settings.lhs((t=>{this.lhs(t)}))}if(this.settings.rhs){const t=typeof this.settings.rhs;"string"===t?this.rhs(this.settings.rhs):"function"===t&&this.settings.rhs((t=>{this.rhs(t)}))}let m;this.editor.lhs.on("beforeChange",((t,e)=>{(e.text.length>1||e.from.line-e.to.line&&"+delete"===e.origin)&&this._clear()})),this.editor.rhs.on("beforeChange",((t,e)=>{this.settings._debug&&a("event#rhs-beforeChange",e),(e.text.length>1||e.from.line-e.to.line&&"+delete"===e.origin)&&this._clear()})),this.editor.lhs.on("change",((t,e)=>{this.settings._debug&&a("event#lhs-change"),this._changing(),this.settings._debug&&a("event#lhs-change [emitted]")})),this.editor.lhs.on("scroll",(()=>{this._skipscroll.lhs?this.settings._debug&&a("event#lhs-scroll (skipped)"):(this.settings._debug&&a("event#lhs-scroll"),setTimeout((()=>{this._scrolling({side:"lhs"})}),1))})),this.editor.rhs.on("change",((t,e)=>{this.settings._debug&&a("event#rhs-change",e),this._changing()})),this.editor.rhs.on("scroll",(()=>{this._skipscroll.rhs?this.settings._debug&&a("event#rhs-scroll (skipped)"):(this.settings._debug&&a("event#rhs-scroll"),setTimeout((()=>{this._scrolling({side:"rhs"})}),1))}));const v=()=>{this.settings._debug&&(c("event#resize"),a("event#resize [start]")),this.resize(),this.settings._debug&&h("event#resize")};function y(t,e,n){if(n.target.className.includes("merge-button"))return void n.preventDefault();this.editor[t];let r=!1;for(let n=0;n<this.changes.length;++n){const i=this.changes[n],o=i[`${t}-line-from`],s=i[`${t}-line-to`];if(e>=o&&e<=s){if(r=!0,this._current_diff>=0){const t=this.changes[this._current_diff];for(let e=t["lhs-line-from"];e<=t["lhs-line-to"];++e)this.editor.lhs.removeLineClass(e,"gutter");for(let e=t["rhs-line-from"];e<=t["rhs-line-to"];++e)this.editor.rhs.removeLineClass(e,"gutter")}this._current_diff=n;break}}this.scrollTo(t,e),r&&this._changing()}this._handleResize=()=>{m&&clearTimeout(m),m=setTimeout(v,this.settings.resize_timeout)},window.addEventListener("resize",(()=>{this._handleResize()})),v(),this.editor.lhs.on("gutterClick",((t,e,n,r)=>{this.settings._debug&&a("event#gutterClick","lhs",e,r),y.call(this,"lhs",e,r)})),this.editor.rhs.on("gutterClick",((t,e,n,r)=>{this.settings._debug&&a("event#gutterClick","rhs",e,r),y.call(this,"rhs",e,r)})),this.editor.lhs.focus()},l.prototype._clear=function(){this.settings._debug&&c("draw#_clear"),this.changes=[],this._clearMarkup(),this._clearCanvases(),this.settings._debug&&h("draw#_clear")},l.prototype._clearMarkup=function(){this.settings._debug&&c("draw#_clearMarkup"),this._vdoc.clear(),this._vdoc=new o({_debug:this.settings._debug}),this.settings._debug&&h("draw#_clearMarkup")},l.prototype._clearCanvases=function(){this.settings._debug&&c("draw#_clearCanvases");const t=this._draw_info(),e=t.lhs_margin.getContext("2d");e.beginPath(),e.fillStyle=this.settings.bgcolor,e.strokeStyle="#888",e.fillRect(0,0,6.5,t.visible_page_height),e.strokeRect(0,0,6.5,t.visible_page_height);const n=t.rhs_margin.getContext("2d");n.beginPath(),n.fillStyle=this.settings.bgcolor,n.strokeStyle="#888",n.fillRect(0,0,6.5,t.visible_page_height),n.strokeRect(0,0,6.5,t.visible_page_height);const r=t.dcanvas.getContext("2d");r.beginPath(),r.fillStyle="rgba(0,0,0,0)",r.clearRect(0,0,this.draw_mid_width,t.visible_page_height),this.settings._debug&&h("draw#_clearCanvases")},l.prototype._scroll_to_change=function(t){if(!t)return;const{lhs:e,rhs:n}=this.editor,r=Math.max(t["lhs-line-from"],0),i=Math.max(t["rhs-line-from"],0);e.setCursor(r,0),n.setCursor(i,0),t["lhs-line-to"]>=0?this.scrollTo("lhs",t["lhs-line-to"]):t["rhs-line-to"]>=0&&this.scrollTo("rhs",t["rhs-line-to"]),e.focus()},l.prototype._scrolling=function({side:t}){if(this.settings._debug&&c(`scroll#_scrolling ${t}`),!this.changes)return void(this.settings._debug&&h(`scroll#_scrolling ${t}`));const e=this.editor[t].getScrollerElement(),{top:n}=e.getBoundingClientRect();let r;if(null===e.offsetParent)return;null==this.midway&&(r=e.clientHeight-(e.offsetHeight-e.offsetParent.offsetHeight),this.midway=(r/2+n).toFixed(2));const i=this.editor[t].coordsChar({left:0,top:this.midway}),o=e.scrollTop,s=e.scrollLeft,l="lhs"===t?"rhs":"lhs";let u=0,d=null,f=!1;for(const e of this.changes)i.line>=e[t+"-line-from"]&&(d=e,i.line>=d[t+"-line-to"]&&(e.hasOwnProperty(t+"-y-start")&&e.hasOwnProperty(t+"-y-end")&&e.hasOwnProperty(l+"-y-start")&&e.hasOwnProperty(l+"-y-end")?u+=e[t+"-y-end"]-e[t+"-y-start"]-(e[l+"-y-end"]-e[l+"-y-start"]):f=!0));const p=this.editor[l].getViewport();let g=!0;if(d&&(this.settings._debug&&a("scroll#_scrolling","last change before midline",d),i.line>=p.from&&i<=p.to&&(g=!1)),g||f){this.settings._debug&&a("scroll#_scrolling","other side",l,"pos:",o-u),this._skipscroll[l]=!0,a("scroll#set oside skip set:",l,this._skipscroll),this._linkedScrollTimeout[l]&&(clearTimeout(this._linkedScrollTimeout[l]),a("scroll#clearing timeout:",this._skipscroll)),this._linkedScrollTimeout[l]=setTimeout((()=>{this._skipscroll[l]=!1,a("scroll#set oside skip unset:",l,this._skipscroll)}),100);const t=o-u;this.editor[l].scrollTo(s,t)}else this.settings._debug&&a("scroll#_scrolling","not scrolling other side");this._renderChanges(),this.settings._debug&&h(`scroll#_scrolling ${t}`)},l.prototype._changing=function(){if(!this.settings.autoupdate)return void(this.settings._debug&&a("change#_changing autoupdate is disabled"));this.settings._debug&&(c("change#_changing"),a("change#_changing [start]"));const t=()=>{this._changedTimeout=null,this.el.dispatchEvent(new Event("changed"))};this.settings.change_timeout>0?(this.settings._debug&&a("change#setting timeout",this.settings.change_timeout),null!=this._changedTimeout&&clearTimeout(this._changedTimeout),this._changedTimeout=setTimeout(t,this.settings.change_timeout)):t(),this.settings._debug&&h("change#_changing")},l.prototype.setChanges=function(t){this.settings._debug&&a("change#setChanges"),this._clear(),this.changes=t,this._renderChanges()},l.prototype._renderChanges=function(){this.settings._debug&&(c("draw#_renderChanges"),a("draw#_renderChanges [start]",this.changes.length,"changes")),this._clearCanvases(),this._calculateOffsets(this.changes),this._markupLineChanges(this.changes),this._renderDiff(this.changes),this.settings._debug&&h("draw#_renderChanges"),this.el.dispatchEvent(new Event("updated"))},l.prototype._getViewportSide=function(t){const e=this.editor[t],n=e.getWrapperElement().getBoundingClientRect();return{from:e.lineAtHeight(n.top,"window"),to:e.lineAtHeight(n.bottom,"window")}},l.prototype._isChangeInView=function(t,e,n){return n[`${t}-line-from`]<0||n[`${t}-line-to`]<0||(n[`${t}-line-from`]>=e.from&&n[`${t}-line-from`]<=e.to||n[`${t}-line-to`]>=e.from&&n[`${t}-line-to`]<=e.to||e.from>=n[`${t}-line-from`]&&e.to<=n[`${t}-line-to`])},l.prototype._set_top_offset=function(t){const e=this.editor[t].getScrollInfo().top;this.editor[t].scrollTo(null,0);const n=this._queryElement(".CodeMirror-measure").offsetParent.offsetTop+4;return this.editor[t].scrollTo(null,e),this.draw_top_offset=.5-n,!0},l.prototype._calculateOffsets=function(t){this.settings._debug&&c("draw#_calculateOffsets");const{lhs:e,rhs:n}=this.editor;this.draw_lhs_min=.5,this.draw_mid_width=this._queryElement(`#${this.id}-lhs-rhs-canvas`).offsetWidth,this.draw_rhs_max=this.draw_mid_width-.5,this.draw_lhs_width=5,this.draw_rhs_width=5,this.em_height=e.defaultTextHeight();const r="local",i=e.getOption("lineWrapping")||n.getOption("lineWrapping"),o=i?null:e.charCoords({line:0},r),s=i?null:n.charCoords({line:0},r);this._getViewportSide("lhs"),this._getViewportSide("rhs");for(const l of t){const t=l["lhs-line-from"]>=0?l["lhs-line-from"]:0,a=l["lhs-line-to"]>=0?l["lhs-line-to"]:0,c=l["rhs-line-from"]>=0?l["rhs-line-from"]:0,h=l["rhs-line-to"]>=0?l["rhs-line-to"]:0;i?"c"===l.op?(l["lhs-y-start"]=e.heightAtLine(t,r),l["lhs-y-end"]=e.heightAtLine(a+1,r),l["rhs-y-start"]=n.heightAtLine(c,r),l["rhs-y-end"]=n.heightAtLine(h+1,r)):"a"===l.op?(-1===l["lhs-line-from"]?l["lhs-y-start"]=e.heightAtLine(t,r):l["lhs-y-start"]=e.heightAtLine(t+1,r),l["lhs-y-end"]=l["lhs-y-start"],l["rhs-y-start"]=n.heightAtLine(c,r),l["rhs-y-end"]=n.heightAtLine(h+1,r)):(l["lhs-y-start"]=e.heightAtLine(t,r),l["lhs-y-end"]=e.heightAtLine(a+1,r),-1===l["rhs-line-from"]?l["rhs-y-start"]=n.heightAtLine(c,r):l["rhs-y-start"]=n.heightAtLine(c+1,r),l["rhs-y-end"]=l["rhs-y-start"]):"c"===l.op?(l["lhs-y-start"]=o.top+t*this.em_height,l["lhs-y-end"]=o.bottom+a*this.em_height,l["rhs-y-start"]=s.top+c*this.em_height,l["rhs-y-end"]=s.bottom+h*this.em_height):"a"===l.op?(-1===l["lhs-line-from"]?l["lhs-y-start"]=o.top+t*this.em_height:l["lhs-y-start"]=o.bottom+t*this.em_height,l["lhs-y-end"]=l["lhs-y-start"],l["rhs-y-start"]=s.top+c*this.em_height,l["rhs-y-end"]=s.bottom+h*this.em_height):(l["lhs-y-start"]=o.top+t*this.em_height,l["lhs-y-end"]=o.bottom+a*this.em_height,-1===l["rhs-line-from"]?l["rhs-y-start"]=s.top+c*this.em_height:l["rhs-y-start"]=s.bottom+c*this.em_height,l["rhs-y-end"]=l["rhs-y-start"]),l["lhs-y-start"]+=.5,l["lhs-y-end"]+=.5,l["rhs-y-start"]+=.5,l["rhs-y-end"]+=.5}this.settings._debug&&h("draw#_calculateOffsets")},l.prototype._markupLineChanges=function(t){this.settings._debug&&c("draw#_markupLineChanges");const{lhs:e,rhs:n}=this.editor,r=this._current_diff,i=this._getViewportSide("lhs"),o=this._getViewportSide("rhs"),{_vdoc:s}=this;for(let l=0;l<t.length;++l){const a=t[l],c=r===l,h=!1!==this.settings.lcs,u=this._isChangeInView("lhs",i,a),d=this._isChangeInView("rhs",o,a);if(u){const t=this.settings.line_numbers&&!n.getOption("readOnly");s.addRender("lhs",a,l,{isCurrent:c,lineDiff:h,getMergeHandler:(t,e,n)=>()=>this._merge_change(t,e,n),mergeButton:t?this.merge_rhs_button.cloneNode(!0):null})}if(d){const t=this.settings.line_numbers&&!e.getOption("readOnly");s.addRender("rhs",a,l,{isCurrent:c,lineDiff:h,getMergeHandler:(t,e,n)=>()=>this._merge_change(t,e,n),mergeButton:t?this.merge_lhs_button.cloneNode(!0):null})}h&&(u||d)&&"c"===a.op&&s.addInlineDiff(a,l,{ignoreaccents:this.settings.ignoreaccents,ignorews:this.settings.ignorews,ignorecase:this.settings.ignorecase,getText:(t,r)=>{if("lhs"===t){return e.getLine(r)||""}return n.getLine(r)||""}})}e.operation((()=>{s.update("lhs",e,i)})),n.operation((()=>{s.update("rhs",n,o)})),this.settings._debug&&h("draw#_markupLineChanges")},l.prototype._merge_change=function(t,e,n){if(!t)return;const{lhs:i,rhs:o}=this.editor,s={lhs:i,rhs:o},l=t[`${e}-line-from`],a=t[`${e}-line-to`];let c=t[`${n}-line-from`];const h=t[`${n}-line-to`],u=s[e].getDoc(),d=s[n].getDoc();let f=l>=0?u.getLine(l).length+1:0;const p=a>=0?u.getLine(a).length+1:0,g=h>=0?d.getLine(h).length+1:0,m=c>=0?d.getLine(c).length+1:0;let v;"c"===t.op?(v=u.getRange(r.Pos(l,0),r.Pos(a,p)),d.replaceRange(v,r.Pos(c,0),r.Pos(h,g))):"lhs"===n&&"d"===t.op||"rhs"===n&&"a"===t.op?(l>0?(v=u.getRange(r.Pos(l,f),r.Pos(a,p)),c+=1):v=u.getRange(r.Pos(0,0),r.Pos(a+1,0)),d.replaceRange(v,r.Pos(c-1,0),r.Pos(h+1,0))):("rhs"===n&&"d"===t.op||"lhs"===n&&"a"===t.op)&&(l>0?(f=u.getLine(l-1).length+1,v=u.getRange(r.Pos(l-1,f),r.Pos(a,p))):v=u.getRange(r.Pos(0,0),r.Pos(a+1,0)),c<0&&(c=0),d.replaceRange(v,r.Pos(c,m)))},l.prototype._draw_info=function(){const t=this.editor.lhs.getScrollerElement(),e=this.editor.rhs.getScrollerElement(),n=t.offsetHeight,r=document.getElementById(`${this.id}-lhs-rhs-canvas`);if(null==r)throw new Error(`Failed to find: ${this.id}-lhs-rhs-canvas`);const i=document.getElementById(`${this.id}-lhs-margin`),o=document.getElementById(`${this.id}-rhs-margin`);return{visible_page_height:n,lhs_scroller:t,rhs_scroller:e,lhs_lines:this.editor.lhs.lineCount(),rhs_lines:this.editor.rhs.lineCount(),dcanvas:r,lhs_margin:i,rhs_margin:o,lhs_xyoffset:{top:i.offsetParent.offsetTop,left:i.offsetParent.offsetLeft},rhs_xyoffset:{top:o.offsetParent.offsetTop,left:o.offsetParent.offsetLeft}}},l.prototype._renderDiff=function(t){this.settings._debug&&c("draw#_renderDiff");const e=this._draw_info(),n=e.lhs_margin,r=e.rhs_margin,i=e.dcanvas.getContext("2d"),o=n.getContext("2d"),s=r.getContext("2d");this.settings._debug&&this.settings._debug&&(a("draw#_renderDiff","visible page height",e.visible_page_height),a("draw#_renderDiff","scroller-top lhs",e.lhs_scroller.scrollTop),a("draw#_renderDiff","scroller-top rhs",e.rhs_scroller.scrollTop)),e.lhs_margin.removeEventListener("click",this._handleLhsMarginClick),e.rhs_margin.removeEventListener("click",this._handleRhsMarginClick);const l=this._getViewportSide("lhs"),u=this._getViewportSide("rhs"),d=e.lhs_scroller.scrollTop,f=e.rhs_scroller.scrollTop,p=e.lhs_margin.offsetHeight/e.lhs_scroller.scrollHeight,g=e.rhs_margin.offsetHeight/e.rhs_scroller.scrollHeight;for(let e=0;e<t.length;++e){const n=t[e],r=n["lhs-y-start"]-d,c=n["lhs-y-end"]-d,h=n["rhs-y-start"]-f,g=n["rhs-y-end"]-f;if(Number.isNaN(r)){a("draw#_renderDiff","unexpected NaN",n["lhs-y-start"],n["lhs-y-end"]);continue}this.settings._debug&&this.settings._debug&&a("draw#_renderDiff","draw1","marker",r,c,h,g);const m=n["lhs-y-start"]*p,v=Math.max(n["lhs-y-end"]*p,5);o.beginPath(),o.fillStyle="#a3a3a3",o.strokeStyle="#000",o.lineWidth=.5,o.fillRect(1.5,m,4.5,Math.max(v-m,5)),o.strokeRect(1.5,m,4.5,Math.max(v-m,5)),o.stroke();const y=n["rhs-y-start"]*p,b=Math.max(n["rhs-y-end"]*p,5);if(s.beginPath(),s.fillStyle="#a3a3a3",s.strokeStyle="#000",s.lineWidth=.5,s.fillRect(1.5,y,4.5,Math.max(b-y,5)),s.strokeRect(1.5,y,4.5,Math.max(b-y,5)),s.stroke(),!this._isChangeInView("lhs",l,n)&&!this._isChangeInView("rhs",u,n))continue;const w=this._current_diff===e?this.settings._colors.current.border:this.settings._colors[n.op].border;i.beginPath(),i.strokeStyle=w,i.lineWidth=1;let x=this.draw_lhs_width,_=c-r-1,C=this.draw_lhs_min,k=r;i.moveTo(C,k),_<=0?i.lineTo(C+x,k):(i.arcTo(C+x,k,C+x,k+3,3),i.arcTo(C+x,k+_,C+x-3,k+_,3)),i.lineTo(C,k+_),i.stroke(),x=this.draw_rhs_width,_=g-h-1,C=this.draw_rhs_max,k=h,i.moveTo(C,k),_<=0?i.lineTo(C-x,k):(i.arcTo(C-x,k,C-x,k+3,3),i.arcTo(C-x,k+_,C-3,k+_,3)),i.lineTo(C,k+_),i.stroke();const S=this.draw_lhs_min+this.draw_lhs_width,L=r+(c+1-r)/2,M=this.draw_rhs_max-this.draw_rhs_width,T=h+(g+1-h)/2;i.moveTo(S,L),L==T?i.lineTo(M,T):i.bezierCurveTo(S+12,L-3,M-12,T-3,M,T),i.stroke()}o.fillStyle=this.settings.vpcolor,s.fillStyle=this.settings.vpcolor;const m=d*p,v=Math.max(e.lhs_scroller.clientHeight*p,5),y=f*g,b=Math.max(e.rhs_scroller.clientHeight*g,5);o.fillRect(1.5,m,4.5,v),s.fillRect(1.5,y,4.5,b),this._handleLhsMarginClick=function(t){const r=t.pageY-e.lhs_xyoffset.top-v/2,i=Math.max(0,r/n.height*e.lhs_scroller.scrollHeight);e.lhs_scroller.scrollTo({top:i})},this._handleRhsMarginClick=function(t){const n=t.pageY-e.rhs_xyoffset.top-b/2,i=Math.max(0,n/r.height*e.rhs_scroller.scrollHeight);e.rhs_scroller.scrollTo({top:i})},e.lhs_margin.addEventListener("click",this._handleLhsMarginClick),e.rhs_margin.addEventListener("click",this._handleRhsMarginClick),this.settings._debug&&h("draw#_renderDiff")},l.prototype._queryElement=function(t){const e=`_element:${t}`,n=this[e]||document.querySelector(t);return this[e]||(this[e]=n),this[e]},t.exports=l},315:t=>{function e(t,e,r={}){const{ignorews:i=!1,ignoreaccents:o=!1,ignorecase:s=!1,split:l="lines"}=r;this.codeify=new n(t,e,{ignorews:i,ignoreaccents:o,ignorecase:s,split:l});const a={codes:this.codeify.getCodes("lhs"),modified:{}},c={codes:this.codeify.getCodes("rhs"),modified:{}};this._lcs(a,0,a.codes.length,c,0,c.codes.length,[],[]),this._optimize(a),this._optimize(c),this.items=this._create_diffs(a,c)}function n(t,e,n){this._max_code=0,this._diff_codes={},this.ctxs={},this.options=n,this.options.split=this.options.split||"lines","string"==typeof t?"chars"===this.options.split?this.lhs=t.split(""):"words"===this.options.split?this.lhs=t.split(/\s/):"lines"===this.options.split&&(this.lhs=t.split("\n")):this.lhs=t,"string"==typeof e?"chars"===this.options.split?this.rhs=e.split(""):"words"===this.options.split?this.rhs=e.split(/\s/):"lines"===this.options.split&&(this.rhs=e.split("\n")):this.rhs=e}e.prototype.changes=function(){return this.items},e.prototype.getLines=function(t){return this.codeify.getLines(t)},e.prototype.normal_form=function(){let t="";for(let e=0;e<this.items.length;++e){const n=this.items[e];let r="",i="",o="c";0===n.lhs_deleted_count&&n.rhs_inserted_count>0?o="a":n.lhs_deleted_count>0&&0===n.rhs_inserted_count&&(o="d"),r=1===n.lhs_deleted_count?n.lhs_start+1:0===n.lhs_deleted_count?n.lhs_start:n.lhs_start+1+","+(n.lhs_start+n.lhs_deleted_count),i=1===n.rhs_inserted_count?n.rhs_start+1:0===n.rhs_inserted_count?n.rhs_start:n.rhs_start+1+","+(n.rhs_start+n.rhs_inserted_count),t+=r+o+i+"\n";const s=this.getLines("lhs"),l=this.getLines("rhs");if(l&&s){let e;for(e=n.lhs_start;e<n.lhs_start+n.lhs_deleted_count;++e)t+="< "+s[e]+"\n";for(n.rhs_inserted_count&&n.lhs_deleted_count&&(t+="---\n"),e=n.rhs_start;e<n.rhs_start+n.rhs_inserted_count;++e)t+="> "+l[e]+"\n"}}return t},e.prototype._lcs=function(t,e,n,r,i,o,s,l){for(;e<n&&i<o&&t.codes[e]===r.codes[i];)++e,++i;for(;e<n&&i<o&&t.codes[n-1]===r.codes[o-1];)--n,--o;if(e===n)for(;i<o;)r.modified[i++]=!0;else if(i===o)for(;e<n;)t.modified[e++]=!0;else{const a=this._sms(t,e,n,r,i,o,s,l);this._lcs(t,e,a.x,r,i,a.y,s,l),this._lcs(t,a.x,n,r,a.y,o,s,l)}},e.prototype._sms=function(t,e,n,r,i,o,s,l){const a=Date.now()+1e3,c=t.codes.length+r.codes.length+1,h=e-i,u=n-o,d=0!=(1&n-e-(o-i)),f=c-h,p=c-u,g=(n-e+o-i)/2+1;l[f+h+1]=e,s[p+u-1]=n;const m={x:0,y:0};let v,y;for(let c=0;c<=g;++c){if(Date.now()>a)return{x:e,y:o};for(let e=h-c;e<=h+c;e+=2){for(e===h-c?v=l[f+e+1]:(v=l[f+e-1]+1,e<h+c&&l[f+e+1]>=v&&(v=l[f+e+1])),y=v-e;v<n&&y<o&&t.codes[v]===r.codes[y];)v++,y++;if(l[f+e]=v,d&&u-c<e&&e<u+c&&s[p+e]<=l[f+e])return m.x=l[f+e],m.y=l[f+e]-e,m}for(k=u-c;k<=u+c;k+=2){for(k===u+c?v=s[p+k-1]:(v=s[p+k+1]-1,k>u-c&&s[p+k-1]<v&&(v=s[p+k-1])),y=v-k;v>e&&y>i&&t.codes[v-1]===r.codes[y-1];)v--,y--;if(s[p+k]=v,!d&&h-c<=k&&k<=h+c&&s[p+k]<=l[f+k])return m.x=l[f+k],m.y=l[f+k]-k,m}}throw"the algorithm should never come here."},e.prototype._optimize=function(t){let e=0,n=0;for(;e<t.codes.length;){for(;e<t.codes.length&&(void 0===t.modified[e]||!1===t.modified[e]);)e++;for(n=e;n<t.codes.length&&!0===t.modified[n];)n++;n<t.codes.length&&t.codes[e]===t.codes[n]?(t.modified[e]=!1,t.modified[n]=!0):e=n}},e.prototype._create_diffs=function(t,e){const n=[];let r=0,i=0,o=0,s=0;for(;o<t.codes.length||s<e.codes.length;)if(o<t.codes.length&&!t.modified[o]&&s<e.codes.length&&!e.modified[s])o++,s++;else{for(r=o,i=s;o<t.codes.length&&(s>=e.codes.length||t.modified[o]);)o++;for(;s<e.codes.length&&(o>=t.codes.length||e.modified[s]);)s++;(r<o||i<s)&&n.push({lhs_start:r,rhs_start:i,lhs_deleted_count:o-r,rhs_inserted_count:s-i})}return n},n.prototype.getCodes=function(t){if(!this.ctxs.hasOwnProperty(t)){var e=this._diff_ctx(this[t]);this.ctxs[t]=e,e.codes.length=Object.keys(e.codes).length}return this.ctxs[t].codes},n.prototype.getLines=function(t){return this.ctxs[t].lines},n.prototype._diff_ctx=function(t){var e={i:0,codes:{},lines:t};return this._codeify(t,e),e},n.prototype._codeify=function(t,e){for(let n=0;n<t.length;++n){let r=t[n];this.options.ignorews&&(r=r.replace(/\s+/g,"")),this.options.ignorecase&&(r=r.toLowerCase()),this.options.ignoreaccents&&(r=r.normalize("NFD").replace(/[\u0300-\u036f]/g,""));const i=this._diff_codes[r];void 0!==i?e.codes[n]=i:(++this._max_code,this._diff_codes[r]=this._max_code,e.codes[n]=this._max_code)}},t.exports=e},171:t=>{function e(t){var e=document.createElement("template");return t=t.trim(),e.innerHTML=t,e.content.firstChild}t.exports={htmlToElement:e,getColors:function(t){const n=e(`\n<div style="display:none" class="${["mergely-editor",...t.classList].join(" ")}">\n\t<div class="mergely current start"></div>\n\t<div class="mergely start end rhs a CodeMirror-linebackground"></div>\n\t<div class="mergely start end lhs d CodeMirror-linebackground"></div>\n\t<div class="mergely start end lhs c CodeMirror-linebackground"></div>\n\t<div class="mergely ch rhs a"></div>\n\t<div class="mergely ch rhs ina"></div>\n\t<div class="mergely ch lhs d"></div>\n\t<div class="mergely ch lhs ind"></div>\n</div>\n`);t.appendChild(n);const r=window.getComputedStyle(n.children[0]),i=window.getComputedStyle(n.children[1]),o=window.getComputedStyle(n.children[2]),s=window.getComputedStyle(n.children[3]),l=window.getComputedStyle(n.children[4]),a=window.getComputedStyle(n.children[5]),c=window.getComputedStyle(n.children[6]),h=window.getComputedStyle(n.children[7]),u={current:{border:r.borderTopColor},a:{border:i.borderTopColor,bg:i.backgroundColor,fg:i.color,ch:l.color,in:a.color},d:{border:o.borderTopColor,bg:o.backgroundColor,fg:o.color,ch:c.color,in:h.color},c:{border:s.borderTopColor,bg:s.backgroundColor,fg:s.color}};return n.remove(),u},getMergelyContainer:function({clazz:t=""}){return e(`<div class="${["mergely-editor",t].join(" ")}" style="display:flex;height:100%;position:relative;overflow:hidden;"></div>`)},getMarginTemplate:function({id:t}){return e(`<div class="mergely-margin">\n\t<canvas id="${t}-margin" width="8px"></canvas>\n</div>`)},getEditorTemplate:function({id:t}){return e(`<textarea id="${t}" class="mergely-column"></textarea>`)},getCenterCanvasTemplate:function({id:t}){return e(`<div class="mergely-canvas">\n\t<canvas id="${t}-lhs-rhs-canvas" width="28px"></canvas>\n</div>`)},getSplash:function({notice:t,left:n,top:r}){return e(`<div class="mergely-splash" style="left: ${n}px; top: ${r}px">\n\t<p>\n\t\t<span class="mergely-icon"></span>\n\t\tThis software is a Combined Work using Mergely and is covered by the\n\t\t${t} license. For the full license, see\n\t\t<a target="_blank" href="http://www.mergely.com">http://www.mergely.com/license</a>.\n\t</p>\n</div>`)}}},314:(t,e,n)=>{const r=n(316),{default:i}=n(93),o=n(315),s=n(171),l=console.log,a={autoupdate:!0,rhs_margin:"right",wrap_lines:!1,line_numbers:!0,lcs:!0,sidebar:!0,viewport:!1,ignorews:!1,ignorecase:!1,ignoreaccents:!1,resize_timeout:500,change_timeout:50,bgcolor:"#eee",vpcolor:"rgba(0, 0, 200, 0.5)",license:"lgpl",cmsettings:{styleSelectedText:!0},lhs_cmsettings:{},rhs_cmsettings:{},lhs:null,rhs:null,_debug:!1};class c{constructor(t,e){let n=t;if("string"==typeof t){if(n=document.querySelector(t),!n)throw new Error(`Failed to find: ${t}`)}else if("object"!=typeof t)throw new Error("The 'selector' element must be a string or DOM element");const o=window.getComputedStyle(n);if(!o.height||"0px"===o.height)throw new Error(`The element "${t}" requires an explicit height`);this.el=n,this._initOptions={...e},this._setOptions(e);const s=new r(n,this._viewOptions);this._diffView=s;const a=["cm","get","lhs","mergeCurrentChange","resize","rhs","scrollTo","scrollToDiff","search","unmarkup","update"];for(const t of a)this[t]=s[t].bind(s);this._listeners=[],this._addEventListener=n.addEventListener.bind(n),this._removeEventListener=n.removeEventListener.bind(n),this.on("changed",(()=>{const t=this._options;t._debug&&l("event#changed got event"),this._stopWorker();const e=new i;this._diffWorker=e,e.onerror=t=>{console.error("Unexpected error with web worker",t)},e.onmessage=e=>{t._debug&&l("event#changed worker finished"),this._changes=e.data,s.setChanges(this._changes)},e.postMessage({lhs:this.get("lhs"),rhs:this.get("rhs"),options:{ignoreaccents:t.ignoreaccents,ignorews:t.ignorews,ignorecase:t.ignorecase}})})),s.bind(this.el)}_setOptions(t){this._options&&this._options._debug&&l("api#options");const e=s.getColors(this.el);this._options={...a,...this._initOptions,...t},this._viewOptions={...a,...this._initOptions,...t,_colors:e}}_stopWorker(){this._diffWorker&&(this._options._debug&&l("event#changed terminating worker"),this._diffWorker.terminate(),this._diffWorker=null)}unbind(){this._stopWorker();for(const[t,e]of this._listeners)this._removeEventListener(t,e);this._diffWorker&&this._diffWorker.terminate(),this._diffView.unbind(),delete this._diffView}mergelyUnregister(){}on(t,e){this._listeners.push([t,e]),this._addEventListener(t,e)}once(t,e){this._listeners.push([t,e]),this._addEventListener(t,e,{once:!0})}clear(t){this._options._debug&&l("api#clear",t),"lhs"===t?this._diffView.readOnly("lhs")||this._diffView.lhs(""):"rhs"===t&&(this._diffView.readOnly("rhs")||this._diffView.rhs(""))}diff(){this._options._debug&&l("api#diff");const t=this.get("lhs"),e=this.get("rhs");return new o(t,e,{ignoreaccents:this._options.ignoreaccents,ignorews:this._options.ignorews,ignorecase:this._options.ignorecase}).normal_form()}merge(t){this._options._debug&&l("api#merge",t);const e=this.get("lhs"),n=this.get("rhs");"lhs"!==t||this._diffView.readOnly("lhs")?this._diffView.readOnly("rhs")||this._diffView.rhs(e):this._diffView.lhs(n)}options(t){if(!t)return this._options;this._setOptions(t),this._diffView.setOptions(t)}summary(){this._options._debug&&l("api#summary");const t=this.get("lhs"),e=this.get("rhs");return{numChanges:this._changes.length,lhsLength:t.length,rhsLength:e.length,c:this._changes.filter((function(t){return"c"===t.op})).length,a:this._changes.filter((function(t){return"a"===t.op})).length,d:this._changes.filter((function(t){return"d"===t.op})).length}}swap(){if(this._options._debug&&l("api#swap"),this._diffView.readOnly())return void l("api#swap readOnly");const t=this.get("lhs"),e=this.get("rhs");this._diffView.lhs(e),this._diffView.rhs(t)}}window.Mergely=c,t.exports=c},563:(t,e,n)=>{const r=n(315),i=console.log;class o{constructor(t){this.id=t,this.background=new Set,this.gutter=new Set,this.marker=null,this.editor=null,this.markup=[],this._clearMarkup=[],this.rendered=!1}addLineClass(t,e){this[t].add(e)}addMergeButton(t,e,n){this.marker=[t,e,n]}markText(t,e,n){this.markup.push([t,e,n])}update(t){this.rendered?console.log("already rendered",this.id):(this.editor=t,t.operation((()=>{if(this.background.size){const e=Array.from(this.background).join(" ");t.addLineClass(this.id,"background",e)}if(this.gutter.size){const e=Array.from(this.gutter).join(" ");t.addLineClass(this.id,"gutter",e)}if(this.marker){const[e,n,r]=this.marker;n.addEventListener("click",r),t.setGutterMarker(this.id,e,n)}if(this.markup.length)for(const e of this.markup){const[n,r,i]=e,o={line:this.id},s={line:this.id};n>=0&&(o.ch=n),r>=0&&(s.ch=r),this._clearMarkup.push(t.markText(o,s,{className:i}))}})),this.rendered=!0)}clear(){const{editor:t}=this;this.rendered&&t.operation((()=>{if(this.background&&t.removeLineClass(this.id,"background"),this.gutter&&t.removeLineClass(this.id,"gutter"),this.marker){const[e,n,r]=this.marker;t.setGutterMarker(this.id,e,null),n.removeEventListener("click",r),n.remove()}if(this._clearMarkup.length){for(const t of this._clearMarkup)t.clear();this._clearMarkup=[],this.markup=[]}}))}}function s(t,e){const n="lhs"===t?"rhs":"lhs";return{lf:e[`${t}-line-from`],lt:e[`${t}-line-to`],olf:e[`${n}-line-from`],olt:e[`${n}-line-to`]}}t.exports=class{constructor(t){this.options=t,this._lines={lhs:{},rhs:{}},this._rendered={lhs:{},rhs:{}}}addRender(t,e,n,r){this.options._debug&&i("vdoc#addRender",t,n,e);const{isCurrent:o,lineDiff:l,mergeButton:a,getMergeHandler:c}=r;if(!!this._rendered[t][n])return void(this.options._debug&&i("vdoc#addRender (already rendered)",t,n,e));const h="lhs"===t?"rhs":"lhs",u=["mergely",t,`cid-${n}`],{lf:d,lt:f,olf:p}=s(t,e);if(o){d!==f&&this._getLine(t,d).addLineClass("background","current"),this._getLine(t,f).addLineClass("background","current");for(let e=d;e<=f;++e)this._getLine(t,e).addLineClass("gutter","mergely current")}const g={lhs:{d:"d",a:"d",c:"c"},rhs:{d:"a",a:"a",c:"c"}}[t][e.op];if(d<0)return u.push("start"),u.push("no-end"),u.push(g),this._getLine(t,0).addLineClass("background",u.join(" ")),void this._setRenderedChange(t,n);if("lhs"===t&&e["lhs-y-start"]===e["lhs-y-end"])return u.push("no-start"),u.push("end"),u.push(g),this._getLine(t,d).addLineClass("background",u.join(" ")),void this._setRenderedChange(t,n);if("rhs"===t&&e["rhs-y-start"]===e["rhs-y-end"])return u.push("no-start"),u.push("end"),u.push(g),this._getLine(t,d).addLineClass("background",u.join(" ")),void this._setRenderedChange(t,n);this._getLine(t,d).addLineClass("background","start"),this._getLine(t,f).addLineClass("background","end");for(let r=d,i=p;-1!==d&&-1!==f&&r<=f;++r,++i)this._getLine(t,r).addLineClass("background",g),this._getLine(t,r).addLineClass("background",u.join(" ")),l&&("lhs"===t&&"d"===e.op?this._getLine(t,r).markText(0,void 0,`mergely ch d lhs cid-${n}`):"rhs"===t&&"a"===e.op&&this._getLine(t,r).markText(0,void 0,`mergely ch a rhs cid-${n}`));if(a){a.className=`merge-button merge-${h}-button`;const n=c(e,t,h);this._getLine(t,d).addMergeButton("merge",a,n)}this._setRenderedChange(t,n)}addInlineDiff(t,e,{getText:n,ignorews:o,ignoreaccents:l,ignorecase:a}){this.options._debug&&i("vdoc#addInlineDiff",e,t);const{lf:c,lt:h,olf:u,olt:d}=s("lhs",t),f=this;for(let t=c,i=u;t>=0&&t<=h||i>=0&&i<=d;++t,++i)if(t<=h&&i<=d){const s=n("lhs",t),c=n("rhs",i);if(!!this._getLine("lhs",t).markup.length||!!this._getLine("rhs",i).markup.length)continue;const h=new r(s,c,{ignoreaccents:l,ignorews:o,ignorecase:a,split:"chars"});for(const n of h.changes()){const{lhs_start:r,lhs_deleted_count:o,rhs_start:s,rhs_inserted_count:l}=n,a=r+o,c=s+l;f._getLine("lhs",t).markText(r,a,`mergely ch ind lhs cid-${e}`);f._getLine("rhs",i).markText(s,c,`mergely ch ina rhs cid-${e}`)}}else if(i>d){f._getLine("lhs",t).markText(0,void 0,`mergely ch ind lhs cid-${e}`)}else if(t>h){f._getLine("rhs",i).markText(0,void 0,`mergely ch ina rhs cid-${e}`)}}_setRenderedChange(t,e){return this.options._debug&&i("vdoc#_setRenderedChange",t,e),this._rendered[t][e]=!0}_getLine(t,e){let n=this._lines[t][e];return n||(n=new o(e),this._lines[t][e]=n,n)}update(t,e,n){this.options._debug&&i("vdoc#update",t,e,n);const r=Object.keys(this._lines[t]);for(let i=0;i<r.length;++i){const o=r[i];if(o<n.from||o>n.to)continue;const s=this._getLine(t,o);s.rendered||s.update(e)}}clear(){this.options._debug&&i("vdoc#clear");for(const t in this._lines.lhs)this._lines.lhs[t].clear();for(const t in this._lines.rhs)this._lines.rhs[t].clear()}}}},e={};function n(r){var i=e[r];if(void 0!==i)return i.exports;var o=e[r]={id:r,exports:{}};return t[r].call(o.exports,o,o.exports,n),o.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.nc=void 0;n(314)})();