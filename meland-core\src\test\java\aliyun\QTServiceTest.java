package aliyun;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wb.tool.Encrypter;
import okhttp3.*;

import java.io.IOException;

public class QTServiceTest {

    /**
     * 主域名
     */
    private static final String API_URL = "https://quickaplus-he-api-cn-shanghai.aliyuncs.com/server";
    /**
     * 对应服务端埋点信息中的：ServiceID
     */
    private static final String SERVICE_ID = "bbZhKAmsnIiksCAq";
    /**
     * 对应服务端埋点信息中的：ServiceSecret
     */
    private static final String SERVICE_SECRET = "tWvFCqCNu2eTqoxeGI1IuPatCZQR1M6N";
    /**
     * client日志
     */
    private static final OkHttpClient client = new OkHttpClient();

    /**
     * 主函数
     *
     * @param args 参数
     */
    public static void main(String[] args) throws Exception {
        String srcJsonString = getMockData();
        JSONObject json = JSON.parseObject(srcJsonString);
        //向报文中添加服务ID
        json.put("app_id", SERVICE_ID);
        //向报文中添加ts
        json.put("ts", String.valueOf(System.currentTimeMillis()));
        //计算签名
        String sign = Encrypter.getMD5(JSONObject.toJSONString(json, SerializerFeature.MapSortField) + SERVICE_SECRET);
        json.put("sign", sign);
        String desJsonString = JSON.toJSONString(json, SerializerFeature.DisableCircularReferenceDetect);
        Request request = new Request.Builder().url(API_URL + "/server").post(RequestBody.create(MediaType.parse("application/json"), desJsonString)).build();
        try {
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                System.out.printf("[DEMO] 发送日志失败 %s%n", response);
            } else {
                System.out.printf("[DEMO] 发送成功 %s%n", response.body().string());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取MockData
     *
     * @return 返回值
     */
    private static String getMockData() {
        return "{\n" +
                " \"appkey\": \"bj2mw38cfcydjamfov4v6u8o\",\n" +
                " \"id\": \"get_coupons\",\n" +
                " \"umid\": \"uuid()\",\n" +
                " \"puid\": \"puid1\",\n" +
                " \"page_name\": \"home_page\",\n" +
                " \"cusp\": {\n" +
                " \"p1\": \"1\",\n" +
                " \"p2\": \"2\",\n" +
                " \"p3\": \"3\"\n" +
                " },\n" +
                " \"gp\": {\n" +
                " \"p1\": \"1\",\n" +
                " \"p2\": \"2\",\n" +
                " \"p3\": \"3\"\n" +
                " },\n" +
                " \"sdk_type\": \"httpapi\"\n" +
                "}";
    }
}
