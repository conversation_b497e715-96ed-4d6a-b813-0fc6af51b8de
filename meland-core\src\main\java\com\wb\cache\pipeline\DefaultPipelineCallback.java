package com.wb.cache.pipeline;

import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 默认管道回调实现
 * 使用列表存储操作函数，在执行时批量处理
 */
public class DefaultPipelineCallback implements PipelineCallback {
    private final List<Runnable> operations = new ArrayList<>();
    private final RedisTemplate<String, Object> redisTemplate;
    
    public DefaultPipelineCallback(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    
    @Override
    public void set(String key, Object value) {
        operations.add(() -> redisTemplate.opsForValue().set(key, value));
    }
    
    @Override
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        operations.add(() -> redisTemplate.opsForValue().set(key, value, timeout, unit));
    }
    
    @Override
    public void delete(String key) {
        operations.add(() -> redisTemplate.delete(key));
    }
    
    @Override
    public void hashPut(String key, Object hashKey, Object value) {
        operations.add(() -> redisTemplate.opsForHash().put(key, hashKey, value));
    }
    
    @Override
    public void hashDelete(String key, Object hashKey) {
        operations.add(() -> redisTemplate.opsForHash().delete(key, hashKey));
    }
    
    @Override
    public void listPush(String key, Object value) {
        operations.add(() -> redisTemplate.opsForList().rightPush(key, value));
    }
    
    @Override
    public void setAdd(String key, Object value) {
        operations.add(() -> redisTemplate.opsForSet().add(key, value));
    }
    
    @Override
    public void expire(String key, long timeout, TimeUnit unit) {
        operations.add(() -> redisTemplate.expire(key, timeout, unit));
    }
    
    /**
     * 获取所有待执行的操作
     * 
     * @return 操作列表
     */
    public List<Runnable> getOperations() {
        return operations;
    }
} 