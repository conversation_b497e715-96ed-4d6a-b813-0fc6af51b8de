package com.wb.common;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.json.JSONObject;
import org.springframework.session.FindByIndexNameSessionRepository;

import com.wb.tool.Encrypter;
import com.wb.util.DbUtil;
import com.wb.util.StringUtil;
import com.wb.util.WebUtil;

public class Session {
	/**
	 * 验证登录是否合法，合法则成功登录，否则抛出异常。
	 * 
	 * @param request  请求对象。
	 * @param response 响应对象。
	 * @throws Exception 登录验证过程发生异常。
	 */
	public static void verify(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String referer = StringUtil.opt(request.getHeader("Referer"));
		String redirect = WebUtil.fetch(request, "redirect");
		HttpSession session = request.getSession(false);

		if (Var.getBool("sys.session.verifyImage.enabled")) {
			if (session == null)
				throw new Exception(Str.format(request, "vcExpired"));
			String verifyCode = (String) session.getAttribute("sys.verifyCode");
			// 每个验证码只允许一次有效，以防止暴力破解
			session.removeAttribute("sys.verifyCode");
			if ((StringUtil.isEmpty(verifyCode))
					|| (!StringUtil.isSame(verifyCode, WebUtil.fetch(request, "verifyCode")))) {
				throw new Exception(Str.format(request, "vcInvalid"));
			}
		}
		if (session != null)
			session.invalidate();
		createSession(request, WebUtil.fetch(request, "username"), WebUtil.fetch(request, "password"), true);
		if ((referer.endsWith("/login")) || (referer.endsWith("m?xwl=sys/session/login")))
			referer = Var.getString("sys.home");
		if ((referer.endsWith("/tlogin")) || (referer.endsWith("m?xwl=sys/session/tlogin")))
			referer = Var.getString("sys.homeMobile");
		if (StringUtil.isEmpty(redirect)) {
			JSONObject jo = new JSONObject();
			jo.put("referer", referer);
			jo.put("dispname", request.getSession(true).getAttribute("sys.dispname"));
			WebUtil.send(response, jo.toString());
		} else {
			response.sendRedirect(redirect);
		}
	}

	/**
	 * 注销当前登录用户的会话。
	 * 
	 * @param request  请求对象
	 * @param response 响应对象
	 * @throws Exception 调用过程发生异常
	 */
	public static void logout(HttpServletRequest request, HttpServletResponse response) throws Exception {
		HttpSession session = request.getSession(false);
		if (session != null)
			session.invalidate();
	}

	/**
	 * 获取当前用户的角色列表。如果当前用户未登录或未关联角色数据将返回null。
	 * 
	 * @param request 当前用户关联的请求对象。
	 * @return 角色列表。
	 */
	public static String[] getRoles(HttpServletRequest request) throws Exception {
		HttpSession session = request.getSession(false);
		if (session == null) {
			return null;
		}
		return (String[]) session.getAttribute("sys.roles");
	}

	/**
	 * 存储用户数据至Session的Attribute.
	 * 
	 * @param session 会话对象。
	 * @throws Exception 存储过程发生异常。
	 */
	public static void storeUserValues(HttpServletRequest request, HttpSession session, String userId)
			throws Exception {
		// 定义需要存储的名称列表，可以根据业务需要扩充列表
		String[] names = Var.sessionVars.split(",");
		String[] valueIds = new String[names.length];

		ArrayList<String> roles = new ArrayList<String>();

		int i = 0;

		// 角色
		ResultSet rs = (ResultSet) DbUtil.run(request,
				"select a.ROLE_ID from WB_USER_ROLE a, WB_ROLE b where a.ROLE_ID=b.ROLE_ID and a.USER_ID={?sys.user?} and a.ROLE_ID<>'default' and b.STATUS<>0");
		roles.add("default");// 默认每个用户都具有的角色
		while (rs.next()) {
			roles.add(rs.getString(1));
		}
		String[] roleArray = (String[]) roles.toArray(new String[roles.size()]);
		session.setAttribute("sys.roles", roleArray);
		session.setAttribute("sys.roleList", StringUtil.join(roleArray, ","));

		// 其他数据
		for (String name : names) {
			valueIds[(i++)] = StringUtil.concat(new String[] { "'", userId, "@", name, "'" });
		}
		rs = (ResultSet) DbUtil.run(request,
				"select VAL_ID,VAL_CONTENT from WB_VALUE where VAL_ID in (" + StringUtil.join(valueIds, ",") + ")");
		while (rs.next()) {
			String fieldName = rs.getString("VAL_ID");
			fieldName = fieldName.substring(fieldName.indexOf('@') + 1);
			String fieldValue = rs.getString("VAL_CONTENT");
			session.setAttribute("sys." + fieldName, fieldValue);
		}
	}

	/**
	 * 创建会话。首先验证用户名称和密码是否合法，如果非法抛出异常。如果合法，创建HTTP会话， 并存储当前用户数据至会话Attribute。
	 * 
	 * @param request        请求对象
	 * @param username
	 * @param password
	 * @param verifyPassword 是否校验密码
	 * @throws Exception 创建会话失败
	 */
	public static void createSession(HttpServletRequest request, String username, String password,
			boolean verifyPassword) throws Exception {
		int timeout = Var.sessionTimeout;

		request.setAttribute("username", username);
		ResultSet rs = (ResultSet) DbUtil.run(request,
				"select USER_ID,USER_NAME,DISPLAY_NAME,PASSWORD,USE_LANG from WB_USER where USER_NAME={?username?} and STATUS=1");
		if (!rs.next())
			throw new IllegalArgumentException(Str.format(request, "userNotExist", new Object[] { username }));
		String userId = rs.getString("USER_ID");
		username = rs.getString("USER_NAME");
		String dispname = rs.getString("DISPLAY_NAME");
		String useLang = rs.getString("USE_LANG");
		if ((verifyPassword) && (!rs.getString("PASSWORD").equals(Encrypter.getMD5(password)))) {
			throw new IllegalArgumentException(Str.format(request, "passwordInvalid", new Object[0]));
		}

		// 用户登录成功后，使旧会话失效并创建新会话
		// 防止了会话固定攻击
		HttpSession oldSession = request.getSession(false);
		if (oldSession != null) {
			oldSession.invalidate();
		}
		HttpSession session = request.getSession(true);
		// 登录的标志为存在会话且属性sys.logined为非null
		session.setAttribute("sys.logined", true);
		if (timeout == -2)
			timeout = Integer.MAX_VALUE;
		if (timeout > -2)
			session.setMaxInactiveInterval(timeout);
		session.setAttribute("sys.user", userId);
		// 映射用户ID到spring session
		session.setAttribute(FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME, userId);
		session.setAttribute("sys.username", username);
		session.setAttribute("sys.dispname", dispname);

		session.setAttribute("sysx.userAgent", StringUtil.substring(request.getHeader("user-agent"), 0, 500));
		session.setAttribute("sys.ip", WebUtil.getRemoteAddr(request));
		session.setAttribute("sys.lang", useLang);
		DbUtil.run(request, "update WB_USER set LOGIN_TIMES=LOGIN_TIMES+1,LAST_LOGIN=now() where USER_ID={?sys.user?}");
		storeUserValues(request, session, userId);
		if (Var.uniqueLogin) {
			String[] userIds = { userId };
			UserList.invalidate(userIds);
		}
	}

	/**
	 * 通过加密的cookie字符串获取sessionid
	 * 
	 * @param smart 加密后的cookie字符串
	 * @return sessionid
	 * @throws Exception
	 */
	public static String getSessionIdBySmart(String smart) throws Exception {
		String sessionId = null;
		smart = smart.replaceAll(" ", "+");
		String cookieStr = Encrypter.decrypt(smart, Var.getString("sys.base.smart"));
		String[] cookies = cookieStr.split(";");
		for (String c : cookies) {
			String[] cookie = c.split("=");
			if (cookie[0].trim().equals("JSESSIONID")) {
				sessionId = cookie[1].trim();
				break;
			}
		}
		return sessionId;
	}

	/**
	 * 根据redis缓存的用户登录信息创建会话
	 * 
	 * @param request 请求对象
	 * @param session redis缓存信息
	 * @throws Exception
	 */
	public static void createSession(HttpServletRequest request, Map<Object, Object> session) throws Exception {
		HttpSession httpSession = request.getSession(true);
		httpSession.setMaxInactiveInterval(24 * 60 * 60);
		httpSession.setAttribute("sys.logined", true);
		httpSession.setAttribute("sys.user", session.get("sys.user"));
		httpSession.setAttribute("sys.username", session.get("sys.username"));
		httpSession.setAttribute("sys.dispname", session.get("sys.dispname"));
		httpSession.setAttribute("sysx.userAgent", StringUtil.substring(request.getHeader("user-agent"), 0, 500));
		httpSession.setAttribute("sys.ip", WebUtil.getRemoteAddr(request));
		storeUserValues(request, httpSession, session.get("sys.user").toString());
	}
}