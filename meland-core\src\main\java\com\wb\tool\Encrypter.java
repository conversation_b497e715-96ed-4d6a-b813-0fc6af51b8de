package com.wb.tool;

import com.wb.util.StringUtil;

import javax.crypto.*;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.*;
import java.util.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 提供常用的加密/解密方法。
 */
public class Encrypter {
    private static final Logger LOGGER = LoggerFactory.getLogger(Encrypter.class);
    
    /**
     * DES字符常量。
     */
    private static final String des = "DES";
    /**
     * MD5 16进制转码时的映射字母表。
     */
    private static final String keyMap = "C2E8D9A3B5F14607";

    /**
     * 使用密钥通过DES算法加密指定的文本。
     *
     * @param text 需要加密的文本。
     * @param key  8位字节的密钥。
     * @return 加密后的文本。
     * @throws Exception 加密过程发生异常。
     */
    public static String encrypt(String text, String key) throws Exception {
        return StringUtil.encodeBase64(encrypt(text.getBytes("utf-8"), key));
    }

    /**
     * 使用密钥解密通过DES算法加密的文本。
     *
     * @param text 需要解密的文本。
     * @param key  8位字节的密钥。
     * @return 解密后的文本。
     * @throws Exception 解密过程发生异常。
     */
    public static String decrypt(String text, String key) throws Exception {
        return new String(decrypt(StringUtil.decodeBase64(text), key), "utf-8");
    }

    /**
     * 使用密钥通过DES算法加密指定的字节。
     *
     * @param bytes 需要加密的字节。
     * @param key   8位字节的密钥。
     * @return 加密后的字节。
     * @throws Exception 加密过程发生异常。
     */
    public static byte[] encrypt(byte[] bytes, String key) throws Exception {
        byte[] keyBytes = key.getBytes("utf-8");
        SecureRandom sr = new SecureRandom();
        DESKeySpec dks = new DESKeySpec(keyBytes);
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(des);
        SecretKey securekey = keyFactory.generateSecret(dks);
        Cipher cipher = Cipher.getInstance(des);
        cipher.init(Cipher.ENCRYPT_MODE, securekey, sr);
        return cipher.doFinal(bytes);
    }

    /**
     * 使用密钥解密通过DES算法加密的字节。
     *
     * @param bytes 需要解密的字节。
     * @param key   8位字节的密钥。
     * @return 解密后的字节。
     * @throws Exception 解密过程发生异常。
     */
    public static byte[] decrypt(byte[] bytes, String key) throws Exception {
        byte[] keyBytes = key.getBytes("utf-8");
        SecureRandom sr = new SecureRandom();
        DESKeySpec dks = new DESKeySpec(keyBytes);
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(des);
        SecretKey securekey = keyFactory.generateSecret(dks);
        Cipher cipher = Cipher.getInstance(des);
        cipher.init(Cipher.DECRYPT_MODE, securekey, sr);
        return cipher.doFinal(bytes);
    }

    /**
     * 对指定文本使用MD5算法进行加密，使用此加密算法获得的值不可解密。
     *
     * @param text 需要加密的字节。
     * @return 32位16进制字符组成的密码。
     * @throws Exception 加密过程中发生异常。
     */
    public static String getMD5(String text) throws Exception {
        return getMD5(text.getBytes("utf-8"));
    }

    /**
     * 对指定字节使用MD5算法进行加密，使用此加密算法获得的值不可解密。
     *
     * @param bytes 需要加密的字节。
     * @return 32位16进制字符组成的密码。
     * @throws Exception 加密过程中发生异常。
     */
    public static String getMD5(byte[] bytes) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(bytes);
        byte bt, codes[] = md.digest();
        char str[] = new char[32];
        int i, j = 0;
        for (i = 0; i < 16; i++) {
            bt = codes[i];
            str[j++] = keyMap.charAt(bt >>> 4 & 0xf);
            str[j++] = keyMap.charAt(bt & 0xf);
        }
        return new String(str);
    }

    /**
     * 将字节数组转换为十六进制字符串。
     *
     * @param hash 字节数组
     * @return 十六进制字符串
     */
    private static String toHexString(byte[] hash) {
        StringBuilder hexString = new StringBuilder(2 * hash.length);
        for (int i = 0; i < hash.length; i++) {
            String hex = Integer.toHexString(0xff & hash[i]);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    /**
     * 对给定的字符串使用SHA-256算法进行散列。
     *
     * @param text 需要散列的字符串
     * @return SHA-256散列后的十六进制字符串
     */
    public static String getSHA256(String text) {
        try {
            // 创建MessageDigest实例
            MessageDigest digest = MessageDigest.getInstance("SHA-256");

            // 对输入文本进行散列
            byte[] encodedhash = digest.digest(text.getBytes());

            // 返回十六进制字符串
            return toHexString(encodedhash);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法实例化失败", e);
        }
    }

    /**
     * 对指定文本使用MD5算法进行加密，使用此加密算法获得的值不可解密。
     *
     * @param val 需要加密的字节。
     * @return 32位16进制字符组成的密码。
     * @throws Exception 加密过程中发生异常。
     */
    public static String getMD5Stan(String val) throws Exception {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        md5.update(val.getBytes("utf-8"));
        byte[] m = md5.digest();//加密
        return getString(m);
    }

    /**
     * 对指定字节使用MD5算法进行加密，使用此加密算法获得的值不可解密。
     *
     * @param b 需要加密的字节。
     * @return 32位16进制字符组成的密码。
     * @throws Exception 加密过程中发生异常。
     */
    private static String getString(byte[] b) {
        StringBuffer buf = new StringBuffer();
        for (int i = 0; i < b.length; i++) {
            int a = b[i];
            if (a < 0)
                a += 256;
            if (a < 16)
                buf.append("0");
            buf.append(Integer.toHexString(a));

        }
        return buf.toString().toUpperCase(); //32位
    }

    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";

    /**
     * AES CBC 加密
     *
     * @param srcData
     * @param key
     * @param iv
     * @return
     * @throws NoSuchAlgorithmException
     * @throws NoSuchPaddingException
     * @throws InvalidKeyException
     * @throws InvalidAlgorithmParameterException
     * @throws IllegalBlockSizeException
     * @throws BadPaddingException
     */
    public static byte[] AES_cbc_encrypt(byte[] srcData, byte[] key, byte[] iv)
            throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException,
            InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException {
        SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, new IvParameterSpec(iv));
        byte[] encData = cipher.doFinal(srcData);
        return encData;
    }

    public static String AES_cbc_encrypt(String srcData, String key, String iv, String charSet)
            throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException,
            InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException,
            UnsupportedEncodingException {
        byte[] encbt = AES_cbc_encrypt(srcData.getBytes(charSet), key.getBytes("utf-8"), iv.getBytes("utf-8"));
        return Base64.getEncoder().encodeToString(encbt);
    }

    public static String AES_cbc_encrypt(String srcData, String key, String iv) throws NoSuchAlgorithmException,
            NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException, IllegalBlockSizeException,
            BadPaddingException, UnsupportedEncodingException {
        byte[] encbt = AES_cbc_encrypt(srcData.getBytes(), key.getBytes("utf-8"), iv.getBytes("utf-8"));
        return Base64.getEncoder().encodeToString(encbt);
    }

    public static String AES_cbc_encrypt_16(String srcData, String key, String iv) throws NoSuchAlgorithmException,
            NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException, IllegalBlockSizeException,
            BadPaddingException, UnsupportedEncodingException {
        byte[] encbt = AES_cbc_encrypt(srcData.getBytes(), key.getBytes("utf-8"), iv.getBytes("utf-8"));
        return encodeBytes(encbt);
    }

    /**
     * AES CBC 解密
     *
     * @param encData
     * @param key
     * @param iv
     * @return
     * @throws NoSuchAlgorithmException
     * @throws NoSuchPaddingException
     * @throws InvalidKeyException
     * @throws InvalidAlgorithmParameterException
     * @throws IllegalBlockSizeException
     * @throws BadPaddingException
     */
    public static byte[] AES_cbc_decrypt(byte[] encData, byte[] key, byte[] iv)
            throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException,
            InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException {
        SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, new IvParameterSpec(iv));
        byte[] decbbdt = cipher.doFinal(encData);
        return decbbdt;
    }

    public static String AES_cbc_decrypt(String encData, String key, String iv, String charSet)
            throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException,
            InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException,
            UnsupportedEncodingException {
        byte[] decbt = AES_cbc_decrypt(Base64.getDecoder().decode(encData), key.getBytes("utf-8"),
                iv.getBytes("utf-8"));
        return new String(decbt, charSet);
    }

    public static String AES_cbc_decrypt(String encData, String key, String iv) throws NoSuchAlgorithmException,
            NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException, IllegalBlockSizeException,
            BadPaddingException, UnsupportedEncodingException {
        byte[] decbt = AES_cbc_decrypt(Base64.getDecoder().decode(encData), key.getBytes("utf-8"),
                iv.getBytes("utf-8"));
        return new String(decbt);
    }

    public static String AES_cbc_decrypt_16(String encData, String key, String iv) throws NoSuchAlgorithmException,
            NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException, IllegalBlockSizeException,
            BadPaddingException, UnsupportedEncodingException {
        byte[] decbt = AES_cbc_decrypt(decodeBytes(encData), key.getBytes("utf-8"),
                iv.getBytes("utf-8"));
        return new String(decbt);
    }

    /**
     * 转16进制
     *
     * @param bytes
     * @return
     */
    public static String encodeBytes(byte[] bytes) {
        StringBuffer strBuf = new StringBuffer();
        for (int i = 0; i < bytes.length; i++) {
            strBuf.append((char) (((bytes[i] >> 4) & 0xF) + ((int) 'a')));
            strBuf.append((char) (((bytes[i]) & 0xF) + ((int) 'a')));
        }
        return strBuf.toString();
    }

    /**
     * 转字节数组
     *
     * @param str
     * @return
     */
    public static byte[] decodeBytes(String str) {
        byte[] bytes = new byte[str.length() / 2];
        for (int i = 0; i < str.length(); i += 2) {
            char c = str.charAt(i);
            bytes[i / 2] = (byte) ((c - 'a') << 4);
            c = str.charAt(i + 1);
            bytes[i / 2] += (c - 'a');
        }
        return bytes;
    }

    /**
     * 计算字符串的MD5哈希值，专用于脚本压缩
     * @param input 输入字符串
     * @return 32位MD5哈希字符串（小写）
     */
    public static String getMD5ForScript(String input) {
        if (input == null) {
            return null;
        }
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] array = md.digest(input.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte b : array) {
                sb.append(Integer.toHexString((b & 0xFF) | 0x100).substring(1, 3));
            }
            return sb.toString();
        } catch (Exception e) {
            LOGGER.error("计算MD5失败", e);
            return null;
        }
    }

    public static void main(String[] args) throws Exception {

        //		String srcStr = "{\"groupID\":\"317230\",\"shopID\":\"76822054\",\"cardType\":\"6999843433120532270\",\"sourceWay\":false,\"customerMobile\":\"13708037516\",\"customerName\":\"苏苏苏\"}",
        //				key = "FSklolV5v8LJjT9q", iv = "FSklolV5v8LJjT9q", charSet = "GB2312";
        //		String encStr = AES_cbc_encrypt(srcStr, key, iv, charSet);
        //		System.out.println(encStr);
        //
        //		String desStr = AES_cbc_decrypt(encStr, key, iv, charSet);
        //		System.out.println(desStr);
        System.out.println(getSHA256("abc"));
        System.out.println(getSHA256("中文测试"));
//        String body = "{\"sequenceId\":\"20220318133803b6eca0a46f0440c1ae076163e182cb96\",\"items\":[{\"priceCurrency\":\"HKD\",\"passengers\":[{\"lastName\":\"\",\"shoeSize\":\"\",\"gender\":\"M\",\"intlCode\":\"15775737395\",\"nationalityName\":\"\",\"mobile\":\"15775737395\",\"weight\":\"\",\"myopiaDegreeL\":\"\",\"birthDate\":\"\",\"ageType\":\"ADU\",\"myopiaDegreeR\":\"\",\"firstName\":\"\",\"birthPlace\":\"\",\"nationalityCode\":\"\",\"name\":\"\",\"height\":\"\"}],\"cost\":598.00,\"quantity\":1,\"adjunctions\":[],\"price\":598.00,\"PLU\":\"TC62017590350\",\"useEndDate\":\"2022-03-18\",\"costCurrency\":\"HKD\",\"useStartDate\":\"2022-03-18\"}],\"contacts\":[{\"intlCode\":\"15775737395\",\"name\":\"\",\"mobile\":\"15775737395\",\"optionalMobile\":\"\",\"optionalIntlCode\":\"\",\"email\":\"\"}]}";
//
//        String encStr = AES_cbc_encrypt_16(body, "a9d2a96d75fb0179", "ad3cf154f68a333d");
//        System.out.println(encStr);
//        System.out.println(AES_cbc_decrypt_16(encStr, "a9d2a96d75fb0179", "ad3cf154f68a333d"));
    }
}