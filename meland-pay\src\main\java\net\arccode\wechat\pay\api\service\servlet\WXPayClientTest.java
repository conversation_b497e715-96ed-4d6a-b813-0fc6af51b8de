package net.arccode.wechat.pay.api.service.servlet;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.security.AlgorithmParameters;
import java.security.Key;
import java.security.Security;
import java.sql.Connection;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.wb.common.Var;
import com.wb.util.DbUtil;
import com.wb.util.LogUtil;
import com.wb.util.SysUtil;
import com.wb.util.WebUtil;

import net.arccode.config.AliPayApi;
import net.arccode.config.AlipayConfig;
import net.arccode.wechat.pay.api.common.constant.WXPayConstants;
import net.arccode.wechat.pay.api.common.exception.WXPayApiException;
import net.arccode.wechat.pay.api.common.util.WXPaySignUtils;
import net.arccode.wechat.pay.api.protocol.pay_notify.PayNotifyResponse;
import net.arccode.wechat.pay.api.service.HttpRequest;
import net.arccode.wechat.pay.api.service.WXPayClient;
import net.arccode.wechat.pay.api.service.Wb;
import net.arccode.wechat.pay.api.service.XmlUtil;

/**
 * 微信客户端测试类
 *
 * <AUTHOR>
 * @since 2015-11-05
 */
@SuppressWarnings("unused")
public class WXPayClientTest {

//    private static final Logger LOG = LoggerFactory.getLogger(WXPayClientTest.class);
//
//    private WXPayClient wxPayClient;
//
//    private WXPayClient wxPayVIPClient;
//
//    private String asyncNotifyUrl = "http://domain:port/path";
//
//    public void before() {
//
//        // 以下配置参数根据公司申请的微信支付帐号填写
//        String appId = "wxfd10ea6b4b2e8471";
//        String mchId = "1483724042";
//        String key = "IQxUa5heNHb0XU1HmTdHonLgKrJe31L4";
//        String certPwd = "";
//        // 绝对路径, 用于退款和商户支付
//        String certPath = "";
//
//        wxPayClient = new WXPayClient(appId, mchId, key);
//        wxPayVIPClient = new WXPayClient(appId, mchId, key, certPwd, certPath);
//    }

    /**
     * 扫码支付下单
     */
    //    @Test
    //    public void scanPay() throws WXPayApiException {
    //
    //        String nonceStr = SDKUtils.genRandomStringByLength(32);
    //        UnifiedOrderRequest request = new UnifiedOrderRequest("commodity-899", SDKUtils
    //                .genOutTradeNo(),
    //                1, "***********", asyncNotifyUrl, "NATIVE", nonceStr);
    //
    //        UnifiedOrderResponse response = wxPayClient.execute(request);
    //
    //        LOG.info(JSON.toJSONString(response));
    //
    //    }

    /**
     * 公众号支付下单
     */
    //    @Test
    //    public void jsApiPay() throws WXPayApiException {
    //
    //        String nonceStr = SDKUtils.genRandomStringByLength(32);
    //        UnifiedOrderRequest request = new UnifiedOrderRequest("commodity-899", SDKUtils
    //                .genOutTradeNo(),
    //                1, "***********", asyncNotifyUrl, "JSAPI", nonceStr);
    //        request.setOpenId("ox3L9wQ_r1nqnX8T6EQuQ97RyMJM");
    //        UnifiedOrderResponse response = wxPayClient.execute(request);
    //
    //        LOG.info(JSON.toJSONString(response));
    //
    //    }

    /**
     * APP支付下单
     */
    //    @Test
    //    public void appPay() throws WXPayApiException {
    //
    //        String nonceStr = SDKUtils.genRandomStringByLength(32);
    //        UnifiedOrderRequest request = new UnifiedOrderRequest("commodity-899", SDKUtils
    //                .genOutTradeNo(),
    //                1, "***********", asyncNotifyUrl, "APP", nonceStr);
    //
    //        UnifiedOrderResponse response = wxPayClient.execute(request);
    //
    //        LOG.info(JSON.toJSONString(response));
    //
    //    }

    /**
     * 退款
     */
    //    @Test
    //    public void refund() throws WXPayApiException {
    //
    //        String nonceStr = SDKUtils.genRandomStringByLength(32);
    //        RefundRequest request = new RefundRequest("T15121416014891124211768",
    //                SDKUtils.genOutRefundNo(), 1, 1, "112102020", nonceStr);
    //
    //        RefundResponse response = wxPayVIPClient.execute(request);
    //        Assert.assertNotNull(response);
    //
    //        LOG.info(JSON.toJSONString(response));
    //
    //    }

    /**
     * 商户支付
     */
    //    @Test
    //    public void mchPay() throws WXPayApiException {
    //
    //        String nonceStr = SDKUtils.genRandomStringByLength(32);
    //
    //        String customerOpenId = "oKVmeuHht8J0Ni58CSNe474AHA3E";
    //        MchPayRequest mchPayRequest = new MchPayRequest(SDKUtils.genOutTradeNo(),
    //                customerOpenId, "NO_CHECK", 100, "xxxx年xx月结算", "***********", nonceStr);
    //
    //        MchPayResponse response = wxPayVIPClient.execute(mchPayRequest);
    //        Assert.assertNotNull(response);
    //
    //        LOG.info(JSON.toJSONString(response));
    //
    //    }

    /**
     * 查询订单详情
     */
    //    @Test
    //    public void queryOrder() throws WXPayApiException {
    //        String nonceStr = SDKUtils.genRandomStringByLength(32);
    //        QueryOrderRequest request = new QueryOrderRequest(null, "T18042215145391412971763",
    //                nonceStr);
    //
    //        QueryOrderResponse response = wxPayClient.execute(request);
    //
    //        LOG.info(JSON.toJSONString(response));
    //
    //    }

    /**
     * 解析支付通知内容
     */
//    public void notifyTxtParse() throws WXPayApiException {
//        String notifyTxt = "<xml>\n" + "  <appid><![CDATA[wxfd10ea6b4b2e8471]]></appid>\n"
//                + "  <attach><![CDATA[支付测试]]></attach>\n" + "  <bank_type><![CDATA[CFT]]></bank_type>\n"
//                + "  <fee_type><![CDATA[CNY]]></fee_type>\n" + "  <is_subscribe><![CDATA[Y]]></is_subscribe>\n"
//                + "  <mch_id><![CDATA[********]]></mch_id>\n"
//                + "  <nonce_str><![CDATA[5d2b6c2a8db53831f7eda20af46e531c]]></nonce_str>\n"
//                + "  <openid><![CDATA[ox3L9wQ_r1nqnX8T6EQuQ97RyMJM]]></openid>\n"
//                + "  <out_trade_no><![CDATA[**********]]></out_trade_no>\n"
//                + "  <result_code><![CDATA[SUCCESS]]></result_code>\n"
//                + "  <return_code><![CDATA[SUCCESS]]></return_code>\n"
//                + "  <sign><![CDATA[B552ED6B279343CB493C5DD0D78AB241]]></sign>\n"
//                + "  <sub_mch_id><![CDATA[********]]></sub_mch_id>\n"
//                + "  <time_end><![CDATA[**************]]></time_end>\n" + "  <total_fee>1</total_fee>\n"
//                + "  <trade_type><![CDATA[JSAPI]]></trade_type>\n"
//                + "  <transaction_id><![CDATA[1004400740201409030005092168]]></transaction_id>\n" + "</xml>";
//
//        PayNotifyResponse response = wxPayClient.parseNotify(notifyTxt, PayNotifyResponse.class);
//
//        System.err.println(response.getResultCode());
//        System.err.println(response.getReturnCode());
//        LOG.info(JSON.toJSONString(response));
//
//    }

    public static void main(String[] args) throws WXPayApiException {
        //        String asyncNotifyUrl = "http://domain:port/path";
        //    	// 以下配置参数根据公司申请的微信支付帐号填写
        //        String appId = "wxfd10ea6b4b2e8471";
        //        String mchId = "1483724042";
        //        String key = "IQxUa5heNHb0XU1HmTdHonLgKrJe31L4";
        //        String certPwd = "";
        //        // 绝对路径, 用于退款和商户支付
        //        String certPath = "";
        //        WXPayClient wxPayClient = new WXPayClient(appId, mchId, key);
        //       // WXPayClient wxPayVIPClient = new WXPayClient(appId, mchId, key, certPwd, certPath);
        //
        //    	String nonceStr = SDKUtils.genRandomStringByLength(32);
        //        UnifiedOrderRequest request = new UnifiedOrderRequest("commodity-899", SDKUtils
        //                .genOutTradeNo(),
        //                1, "***********", asyncNotifyUrl, "JSAPI", nonceStr);
        //        request.setOpenId("ox3L9wQ_r1nqnX8T6EQuQ97RyMJM");
        //        UnifiedOrderResponse response;
        //		try {
        //			response = wxPayClient.execute(request);
        //			 //LOG.info(JSON.toJSONString(response));
        //			 LOG.info(new JSONObject(response).toString());
        //		} catch (WXPayApiException e) {
        //			// TODO Auto-generated catch block
        //			e.printStackTrace();
        //		}
        //    	String dString = "{\"e\":\"3\"}";
        //    	String d ="{\"semester_id\":\"245NHJL0CDUU\",\"stu_status\":\"0\",\"stu_id\":\"2451OJGX4DRA\",\"total_amount\":\"0.01\",\"total_amount1\":\"壹分\",\"detail\":[{\"fee_based\":1,\"item_id\":\"23UMJ8NTHCP1\",\"payable\":600,\"pay_value\":\"0.01\"}]}";
        //
        //    	JSONObject data3 = new JSONObject(d);
        //    	//请求地址
        //    	String  eq = Var.get("lcychina.qr.qrUrl");
        //	    String url = "http://192.168.1.36:8080/paymos/main?xwl=247SDY8FOD1M";
        //	    JSONObject obj = new JSONObject();
        //	    obj.put("trid", 0);
        //	    obj.put("orderid", "2018117144355610");
        //	    obj.put("paytype", 2);
        //	    obj.put("jsondata", d);
        //
        //	    JSONObject dJsonObject = HttpRequest.httpRequest(url, obj.toString());
        //	    System.out.println(dJsonObject);

//        String appId = "wxfd10ea6b4b2e8471";
//        String mchId = "1483724042";
//        String key = "IQxUa5heNHb0XU1HmTdHonLgKrJe31L4";
//        Map<String, String> back = new HashMap<String, String>();
//        String time = Long.toString(System.currentTimeMillis() / 1000);
//        back.put("appId", appId);
//        back.put("timeStamp", time);
//        back.put("nonceStr", "872xL8gymVG0CNuw");
//        back.put("package", "prepay_id=wx081311526202385ee8a313590505290180");
//        back.put("signType", "MD5");
//        String signContent = WXPaySignUtils.getSignContent(back);
//        String sign2 = WXPaySignUtils.md5Sign(signContent, key, WXPayConstants.CHARSET_UTF8).toUpperCase();
//
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("appId", appId);
//        jsonObject.put("timeStamp", time);
//        jsonObject.put("nonceStr", "872xL8gymVG0CNuw");
//        jsonObject.put("package", "prepay_id=wx081311526202385ee8a313590505290180");
//        jsonObject.put("signType", "MD5");
//        jsonObject.put("paySign", sign2);
//
//        System.out.println("二次签名后返回给前端的签名证书字符串是：" + sign2);
//        System.out.println("返回前端：" + jsonObject.toString());

        String encryptedData = "uxvQkXfDreBO3UJN/Po99k7ZinbWX4Y4BpqqYxOIbIDAk9H9qunbm7YiGrSZvwx4Z7F0wC71LQ4Y1T98rckJbFMvwQAdXZZG7959FaVk+MEYHurHEJ9gfi9f/pSDNb6e+4zMo/jFKCvaIkJwbpmdarsjn2WaXxz4/nvi4CQQdfk+ijMvodxZXQ32HypIaHxbqmuzzHSQ0jn7jtVICx9SdQ==";
        String session_key = "ejBvYSs7bXYULWC41egDgQ==";
        String iv = "6YNRyridLGf13Ay7LcGvBw==";
//        String object = wxDecrypt(encryptedData,session_key, iv);
//
//        System.err.println(object);
    }

//    public void ailpayNotify(HttpServletRequest request, HttpServletResponse response) {
//        try {
//            //获取XML文件流
//            String resylt = XmlUtil.getXmlRequest(request, response);
//            if (!Wb.isEmpty(resylt)) {
//                try {
//                    //初始化
//                    // 以下配置参数根据公司申请的微信支付帐号填写
//                    String appId = Var.getString("lcychina.weixin.gzhAppId");
//                    String mchId = Var.getString("lcychina.weixin.partner");
//                    String key = Var.getString("lcychina.weixin.partnerkey");
//                    WXPayClient wxPayClient = new WXPayClient(appId, mchId, key);
//
//                    PayNotifyResponse payResponse = wxPayClient.parseNotify(resylt, PayNotifyResponse.class);
//
//                    if (payResponse.getResultCode() == "SUCCESS" && payResponse.getReturnCode() == "SUCCESS") {
//                        //商户订单号
//                        String out_trade_no = payResponse.getOutTradeNo();
//                        //微信支付订单号
//                        String pay_id = payResponse.getTransactionId();
//                        //处理支付成功以后的逻辑
//                        Connection connection = null;
//                        Statement statement = null;
//                        try {
//                            connection = DbUtil.getConnection();
//                            connection.setAutoCommit(false);
//                            statement = connection.createStatement();
//                            statement.executeUpdate("update tuition_pay_online_rec set pay_status = 1,pay_id = '"
//                                    + pay_id + "' where order_id = '" + out_trade_no + "'");
//                            connection.commit();
//                            LogUtil.info(request, "微信订单：" + out_trade_no + ",回调：验证成功");
//                            WebUtil.send(response,
//                                    "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>");
//                        } catch (Exception e) {
//                            LogUtil.error(request, "微信回调：更新订单错误 - " + SysUtil.getRootError(e));
//                        } finally {
//                            DbUtil.close(statement);
//                            DbUtil.close(connection);
//                        }
//                    } else {
//                        LogUtil.error(request, "微信回调：验证失败,代码：" + payResponse.getReturnCode());
//                    }
//                } catch (Exception e) {
//                    LogUtil.error(request, "微信回调：通信错误 - " + SysUtil.getRootError(e));
//                }
//
//            } else {
//                //签名校验失败
//                LogUtil.error(request, "微信回调：签名校验失败：" + resylt);
//                String checkXml = "<xml><return_code><![CDATA[FAIL]]></return_code>"
//                        + "<return_msg><![CDATA[check sign fail]]></return_msg></xml>";
//                WebUtil.send(response, checkXml);
//            }
//
//        } catch (Exception e) {
//            LogUtil.error(request, "微信回调异常：" + SysUtil.getRootError(e));
//        }
//    }
//
//    public void wechatNotify(HttpServletRequest request, HttpServletResponse response) {
//        try {
//            String rest = AliPayApi.notify_url(request, response);
//            WebUtil.send(response, rest);
//        } catch (Exception e) {
//            LogUtil.error(request, "支付宝回调异常：" + SysUtil.getRootError(e));
//        }
//    }
//
//    public void wechatReturn(HttpServletRequest request, HttpServletResponse response) {
//        try {
//            //商户订单号
//            String out_trade_no = new String(request.getParameter("out_trade_no").getBytes("ISO-8859-1"), "UTF-8");
//            //支付宝交易号
//            String trade_no = new String(request.getParameter("trade_no").getBytes("ISO-8859-1"), "UTF-8");
//            //支付宝交易号
//            String total_amount = new String(request.getParameter("total_amount").getBytes("ISO-8859-1"), "UTF-8");
//            //当前时间
//            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
//            request.setAttribute("trade_no", trade_no);
//            request.setAttribute("time_end", df.format(new Date()));
//            request.setAttribute("total_amount", total_amount);
//
//            String rest = AliPayApi.return_url(request);
//            if (rest != "" && rest != null) {
//                if (rest == "success") {
//                    request.setAttribute("trade_state", "支付成功");
//                } else {
//                    request.setAttribute("trade_state", "支付失败");
//                }
//            } else {
//                request.setAttribute("trade_state", "支付失败");
//            }
//        } catch (Exception e) {
//            LogUtil.error(request, "支付宝回调异常：" + SysUtil.getRootError(e));
//            request.setAttribute("trade_state", "支付失败");
//        }
//    }
//
//    public void ailpay(HttpServletRequest request, HttpServletResponse response) {
//        try {
//            if (request.getParameter("WIDout_trade_no") != null) {
//                // 商户订单号，商户网站订单系统中唯一订单号，必填
//                String out_trade_no = new String(request.getParameter("WIDout_trade_no").getBytes("ISO-8859-1"),
//                        "UTF-8");
//                // 订单名称，必填
//                String subject = new String(request.getParameter("WIDsubject").getBytes("ISO-8859-1"), "UTF-8");
//                System.out.println(subject);
//                // 付款金额，必填
//                String total_amount = new String(request.getParameter("WIDtotal_amount").getBytes("ISO-8859-1"),
//                        "UTF-8");
//                // 商品描述，可空
//                String body = new String(request.getParameter("WIDbody").getBytes("ISO-8859-1"), "UTF-8");
//                // 超时时间 可空
//                String timeout_express = "2m";
//                // 销售产品码 必填
//                String product_code = "QUICK_WAP_WAY";
//                /**********************/
//                //存储支付信息
//                String jsondata = request.getParameter("jsondata");
//                //请求地址
//                String url = Var.get("lcychina.qr.qrUrl") + "/main?xwl=247RU082951M";
//                JSONObject obj = new JSONObject();
//                obj.put("trid", 0);
//                obj.put("orderid", out_trade_no);
//                obj.put("paytype", 2);
//                obj.put("jsondata", jsondata);
//
//                HttpRequest.httpRequest(url, "POST", obj.toString());
//                // SDK 公共请求类，包含公共请求参数，以及封装了签名与验签，开发者无需关注签名与验签
//                //调用RSA签名方式
//                AlipayClient client = new DefaultAlipayClient(AlipayConfig.URL, AlipayConfig.APPID,
//                        AlipayConfig.RSA_PRIVATE_KEY, AlipayConfig.FORMAT, AlipayConfig.CHARSET,
//                        AlipayConfig.ALIPAY_PUBLIC_KEY, AlipayConfig.SIGNTYPE);
//                AlipayTradeWapPayRequest alipay_request = new AlipayTradeWapPayRequest();
//
//                // 封装请求支付信息
//                AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
//                model.setOutTradeNo(out_trade_no);
//                model.setSubject(subject);
//                model.setTotalAmount(total_amount);
//                model.setBody(body);
//                model.setTimeoutExpress(timeout_express);
//                model.setProductCode(product_code);
//                alipay_request.setBizModel(model);
//                // 设置异步通知地址
//                alipay_request.setNotifyUrl(AlipayConfig.notify_url);
//                // 设置同步地址
//                alipay_request.setReturnUrl(AlipayConfig.return_url);
//
//                // form表单生产
//                String form = "";
//                try {
//                    // 调用SDK生成表单
//                    form = client.pageExecute(alipay_request).getBody();
//                    response.setContentType("text/html;charset=" + AlipayConfig.CHARSET);
//                    response.getWriter().write(form);//直接将完整的表单html输出到页面
//                    response.getWriter().flush();
//                    response.getWriter().close();
//                } catch (AlipayApiException e) {
//                    // TODO Auto-generated catch block
//                    e.printStackTrace();
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    public static void name(HttpServletRequest request, HttpServletResponse response) {
//        try {
//            BufferedReader br = new BufferedReader(
//                    new InputStreamReader((ServletInputStream) request.getInputStream(), "utf-8"));
//            StringBuffer sb = new StringBuffer("");
//            String temp;
//            while ((temp = br.readLine()) != null) {
//                sb.append(temp);
//            }
//            br.close();
//            sb.toString();
//        } catch (Exception e) {
//            // TODO: handle exception
//        }
//    }
//
//
//    public static JSONObject get(String encryptedData, String session_key, String iv) {
//        byte[] dataByte = Base64.decode(encryptedData.getBytes());
//        // 加密秘钥
//        byte[] keyByte = Base64.decode(session_key.getBytes());
//        // 偏移量
//        byte[] ivByte = Base64.decode(iv.getBytes());
//        try {
//            // 如果密钥不足16位，那么就补足.  这个if 中的内容很重要
//            int base = 16;
//            if (keyByte.length % base != 0) {
//                int groups = keyByte.length / base + (keyByte.length % base != 0 ? 1 : 0);
//                byte[] temp = new byte[groups * base];
//                Arrays.fill(temp, (byte) 0);
//                System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
//                keyByte = temp;
//            }
//            // 初始化
//            Security.addProvider(new BouncyCastleProvider());
//            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
//            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
//            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
//            parameters.init(new IvParameterSpec(ivByte));
//            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);// 初始化
//            byte[] resultByte = cipher.doFinal(dataByte);
//            if (null != resultByte && resultByte.length > 0) {
//                String result = new String(resultByte, "UTF-8");
//                return new JSONObject(result);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return null;
//    }

//// 算法名
//    public static final String KEY_NAME = "AES";
//    // 加解密算法/模式/填充方式
//    // ECB模式只用密钥即可对数据进行加密解密，CBC模式需要添加一个iv
//    public static final String CIPHER_ALGORITHM = "AES/CBC/PKCS7Padding";
//
//    /**
//     * 微信 数据解密<br/>
//     * 对称解密使用的算法为 AES-128-CBC，数据采用PKCS#7填充<br/>
//     * 对称解密的目标密文:encrypted=Base64_Decode(encryptData)<br/>
//     * 对称解密秘钥:key = Base64_Decode(session_key),aeskey是16字节<br/>
//     * 对称解密算法初始向量:iv = Base64_Decode(iv),同样是16字节<br/>
//     *
//     * @param encrypted   目标密文
//     * @param session_key 会话ID
//     * @param iv          加密算法的初始向量
//     */
//    public static String wxDecrypt(String encrypted, String session_key, String iv) {
//        String json = null;
//        byte[] encrypted64 = Base64.decode(encrypted.getBytes());
//        byte[] key64 = Base64.decode(session_key.getBytes());
//        byte[] iv64 = Base64.decode(iv.getBytes());
//        byte[] data;
//        try {
//            init();
//            json = new String(decrypt(encrypted64, key64, generateIV(iv64)));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return json;
//    }
//
//    /**
//     * 初始化密钥
//     */
//    public static void init() throws Exception {
//        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
//        KeyGenerator.getInstance(KEY_NAME).init(128);
//    }
//
//    /**
//     * 生成iv
//     */
//    public static AlgorithmParameters generateIV(byte[] iv) throws Exception {
//        // iv 为一个 16 字节的数组，这里采用和 iOS 端一样的构造方法，数据全为0
//        // Arrays.fill(iv, (byte) 0x00);
//        AlgorithmParameters params = AlgorithmParameters.getInstance(KEY_NAME);
//        params.init(new IvParameterSpec(iv));
//        return params;
//    }
//
//    /**
//     * 生成解密
//     */
//    public static byte[] decrypt(byte[] encryptedData, byte[] keyBytes, AlgorithmParameters iv)
//            throws Exception {
//        Key key = new SecretKeySpec(keyBytes, KEY_NAME);
//        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
//        // 设置为解密模式
//        cipher.init(Cipher.DECRYPT_MODE, key, iv);
//        return cipher.doFinal(encryptedData);
//    }

}
