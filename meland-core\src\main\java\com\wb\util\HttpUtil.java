package com.wb.util;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.Iterator;
import java.util.Random;
import java.util.UUID;
import java.util.zip.GZIPInputStream;

import javax.servlet.http.HttpServletResponse;

import com.wb.common.Var;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.json.JSONObject;


/*
md5加密算法，有16位、32位加密，分别生成32位、64位密文
*/
public class HttpUtil {

	/**
	 * 根据输入的明文字符串生成32位的MD5加密结果。
	 *
	 * @param plainText 原始明文字符串，需被加密的内容。
	 * @param isUpperCase 是否将生成的MD5加密结果转换为全大写形式。true表示转换为大写，false表示保留小写。
	 * @return 返回指定32位的MD5加密字符串，如果发生异常可能返回null。
	 */
	public static String getMD532(String plainText, boolean isUpperCase) {
		return md5(plainText, 32, isUpperCase);
	}

	/**
	 * 根据输入的明文字符串生成16位的MD5加密结果。
	 *
	 * @param plainText 原始明文字符串，需被加密的内容。
	 * @param isUpperCase 是否将生成的MD5加密结果转换为全大写形式。true表示转换为大写，false表示保留小写。
	 * @return 返回指定16位的MD5加密字符串，如果发生异常可能返回null。
	 */
	public static String getMD516(String plainText, boolean isUpperCase) {
		return md5(plainText, 16, isUpperCase);
	}

	/**
	 * 使用MD5算法对输入的明文字符串进行加密处理，支持16位或32位加密结果，并可选择是否转换为大写形式。
	 *
	 * @param plainText 原始明文字符串，需被加密的内容。
	 * @param digit 指定生成的MD5加密结果位数，有效值为16或32。
	 * @param isUpperCase 是否将生成的MD5加密结果转换为全大写形式。true表示转换为大写，false表示保留小写。
	 * @return 返回指定位数的MD5加密字符串。如果发生异常可能返回null。
	 */
	public static String md5(String plainText, int digit, boolean isUpperCase) {
		String result = null;
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(plainText.getBytes());
			byte b[] = md.digest();
			int i;
			StringBuffer buf = new StringBuffer("");
			for (int offset = 0; offset < b.length; offset++) {
				i = b[offset];
				if (i < 0)
					i += 256;
				if (i < 16)
					buf.append("0");
				buf.append(Integer.toHexString(i));
			}
			if (digit == 16) {
				result = buf.toString().substring(8, 24);
			} else {
				result = buf.toString();

			}
			if (isUpperCase) {
				result = result.toUpperCase();
			}
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		}
		return result;
	}
	// 可以根据自己需求来删减下面的代码，不要要的类型可以删掉

	// type=0：纯数字(0-9)
	// type=1：全小写字母(a-z)
	// type=2：全大写字母(A-Z)
	// type=3: 数字+小写字母
	// type=4: 数字+大写字母
	// type=5：大写字母+小写字母
	// type=6：数字+大写字母+小写字母
	// type=7：固定长度33位：根据UUID拿到的随机字符串，去掉了四个"-"(相当于长度33位的小写字母加数字)

	/**
	 * 生成指定长度和类型的随机代码字符串。
	 *
	 * @param passLength 生成的代码字符串的长度。当 type 为 7 时，此参数将被忽略，返回固定长度的 UUID 字符串。
	 * @param type 随机代码的类型：
	 *             0 - 数字；
	 *             1 - 小写字母；
	 *             2 - 大写字母；
	 *             3 - 数字和小写字母；
	 *             4 - 数字和大写字母；
	 *             5 - 大小写字母；
	 *             6 - 数字和大小写字母，并确保至少包含一个非数字字符；
	 *             7 - UUID 字符串，忽略 passLength 参数。
	 *
	 * @return 根据指定规则生成的随机代码字符串。如果 type 参数无效，可能返回空字符串。
	 */
	public static String getRandomCode(int passLength, int type) {
		StringBuffer buffer = null;
		StringBuffer sb = new StringBuffer();
		Random r = new Random();
		r.setSeed(new Date().getTime());
		switch (type) {
		case 0:
			buffer = new StringBuffer("0123456789");
			break;
		case 1:
			buffer = new StringBuffer("abcdefghijklmnopqrstuvwxyz");
			break;
		case 2:
			buffer = new StringBuffer("ABCDEFGHIJKLMNOPQRSTUVWXYZ");
			break;
		case 3:
			buffer = new StringBuffer("0123456789abcdefghijklmnopqrstuvwxyz");
			break;
		case 4:
			buffer = new StringBuffer("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
			break;
		case 5:
			buffer = new StringBuffer("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ");
			break;
		case 6:
			buffer = new StringBuffer("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789");
			sb.append(buffer.charAt(r.nextInt(buffer.length() - 10)));
			passLength -= 1;
			break;
		case 7:
			String s = UUID.randomUUID().toString();
			sb.append(s.substring(0, 8) + s.substring(9, 13) + s.substring(14, 18) + s.substring(19, 23)
					+ s.substring(24));
		}

		if (type != 7) {
			int range = buffer.length();
			for (int i = 0; i < passLength; ++i) {
				sb.append(buffer.charAt(r.nextInt(range)));
			}
		}
		return sb.toString();
	}

	/**
	 * 发送指定对象数据至客户端，并立即提交。如果缓冲区数据已经提交则该方法没有任何效果。
	 * 发送的对象根据类型分为InputStream类型数据和非InputStream类型数据。
	 * 
	 * @param response
	 *            响应对象，数据将发送到该对象输出流中。
	 * @param object
	 *            发送的对象。
	 * @throws IOException
	 *             输出过程中发生异常。
	 */
	public static void send(HttpServletResponse response, Object object) throws Exception {
		if (response.isCommitted())
			return;
		OutputStream outputStream = response.getOutputStream();
		if (object instanceof InputStream) {
			// 处理刘数据
			InputStream inputStream = (InputStream) object;
			try {

				IOUtils.copy(inputStream, outputStream);
			} finally {
				inputStream.close();
			}
		} else {
			// 处理非流类数据
			int len;
			byte[] bytes;
			if (object instanceof byte[])
				bytes = (byte[]) object;
			else {
				String text;
				if (object == null)
					text = "";
				else
					text = object.toString();
				bytes = text.getBytes("utf-8");
				if (StringUtil.isEmpty(response.getContentType()))
					response.setContentType("text/html;charset=utf-8");
			}
			len = bytes.length;
			response.setContentLength(len);
			outputStream.write(bytes);
		}
		response.flushBuffer();
	}

	/**
	 * 向指定url发起POST方式的web请求，并提交参数。
	 * 
	 * @param url
	 *            请求的url地址。
	 * @param params
	 *            提交的参数。
	 * @return 请求返回的结果，结果以utf-8编码的字符串表示。
	 * @throws IOException
	 *             请求过程中发生异常。
	 */
	public static String request(String url, JSONObject params) throws Exception {
		return new String(requestData(url, params), "utf-8");
	}

	/**
	 * 向指定url发起POST方式的web请求，并提交参数。
	 * 
	 * @param url
	 *            请求的url地址。
	 * @param params
	 *            提交的参数。
	 * @return 请求返回的结果，结果以字节数组表示。
	 * @throws IOException
	 *             请求过程中发生异常。
	 */
	public static byte[] requestData(String url, JSONObject params) throws Exception {
		HttpURLConnection conn = (HttpURLConnection) (new URL(url)).openConnection();
		try {
			byte[] data = getParamsText(params).getBytes("utf-8");
			int timeout = Var.getInt("sys.session.submitTimeout");
			conn.setConnectTimeout(timeout);
			conn.setReadTimeout(timeout);
			conn.setUseCaches(false);
			conn.setDoOutput(true);
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");
			conn.setRequestProperty("Content-Length", Integer.toString(data.length));
			OutputStream os = conn.getOutputStream();
			try {
				os.write(data);
				os.flush();
			} finally {
				os.close();
			}
			ByteArrayOutputStream bos = new ByteArrayOutputStream();
			InputStream is = conn.getInputStream();
			try {
				if ("gzip".equals(conn.getContentEncoding())) {
					isToOsGZip(is, bos);
				} else {
					isToOs(is, bos);
				}
			} finally {
				is.close();
			}
			return bos.toByteArray();
		} finally {
			conn.disconnect();
		}
	}

	/**
	 * 向指定的URL发起POST请求，并提交JSON格式的数据。
	 *
	 * @param urlPath 请求的目标URL地址，必须是有效的网络路径。
	 * @param json 提交的JSON格式字符串数据，可为null或空字符串。
	 * @return 返回服务器响应的结果字符串，如果请求失败则返回空字符串。
	 */
	public static String doJsonPost(String urlPath, String json) {
		String result = "";
		BufferedReader reader = null;
		try {
			URL url = new URL(urlPath);
			HttpURLConnection conn = (HttpURLConnection)url.openConnection();
			conn.setRequestMethod("POST");
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setRequestProperty("Connection", "Keep-Alive");
			conn.setRequestProperty("Charset", "UTF-8");
			conn.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
			conn.setRequestProperty("accept", "application/json");
			conn.setConnectTimeout(30000);  // 设置连接超时为30秒
			conn.setReadTimeout(30000); // 设置读取超时为5秒
			if (json != null && !StringUtil.isEmpty(json)) {
				byte[] writebytes = json.getBytes();
				conn.setRequestProperty("Content-Length", String.valueOf(writebytes.length));
				OutputStream outwritestream = conn.getOutputStream();
				outwritestream.write(json.getBytes());
				outwritestream.flush();
				outwritestream.close();
			}
			if (conn.getResponseCode() == 200) {
				reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
				result = reader.readLine();
			}
		} catch (Exception e) {
			LogUtil.error(StringUtil.concat("执行请求异常：：", SysUtil.getRootError(e),"，参数：",json));
			e.printStackTrace();
		} finally {
			if (reader != null)
				try {
					reader.close();
				} catch (IOException e) {
					LogUtil.error(StringUtil.concat("执行读写异常：：", SysUtil.getRootError(e),"，参数：",json));
					e.printStackTrace();
				}
		}
		return result;
	}

	/**
	 * 把指定JSONObject中的参数转换成以web形式表示的参数字符串。
	 * 
	 * @param jo
	 *            参数对象。
	 * @return 以web形式表示的参数字符串。
	 * @throws IOException
	 *             转换过程发生异常。
	 */
	public static String getParamsText(JSONObject jo) throws Exception {
		if (jo == null)
			return "";
		StringBuilder sb = new StringBuilder();
		Iterator<?> names = jo.keys();
		String name;
		boolean isFirst = true;

		while (names.hasNext()) {
			name = (String) names.next();
			if (isFirst)
				isFirst = false;
			else
				sb.append("&");
			sb.append(name);
			sb.append("=");
			JsonUtil.opt(jo, name) ;
			sb.append(URLEncoder.encode(JsonUtil.opt(jo, name).toString(), "utf-8"));
		}
		return sb.toString();
	}

	/**
	 * 将输入流中的数据写入到输出流中，并返回写入的数据大小。
	 *
	 * @param is 输入流，用于读取数据的源流。
	 * @param os 输出流，用于写入数据的目标流。
	 * @return 返回写入的数据总大小（以字节为单位）。
	 * @throws Exception 如果在读取或写入过程中发生异常，抛出该异常。
	 */
	public static int isToOs(InputStream is, OutputStream os) throws Exception {
		byte buf[] = new byte[8192];
		int len, size = 0;

		while ((len = is.read(buf)) > 0) {
			os.write(buf, 0, len);
			size += len;
		}
		return size;
	}

	/**
	 * 从输入流中读取GZIP压缩格式的数据，解压缩后写入输出流，并返回写入的数据大小。
	 *
	 * @param is 输入流，包含GZIP压缩格式的数据。
	 * @param os 输出流，解压缩后的数据将写入到该流中。
	 * @return 解压缩后写入输出流的数据总大小（以字节为单位）。
	 * @throws Exception 如果在读取、解压缩或写入过程中发生异常。
	 */
	public static int isToOsGZip(InputStream is, OutputStream os) throws Exception {
		GZIPInputStream gzipStream = new GZIPInputStream(is);
		byte buf[] = new byte[8192];
		int len, size = 0;
		while ((len = gzipStream.read(buf)) > 0) {
			os.write(buf, 0, len);
			size += len;
		}
		return size;
	}

	/**
	 * 对输入的字符串进行Base64编码。
	 *
	 * @param key 需要进行Base64编码的原始字符串。
	 * @return 返回编码后的Base64字符串。
	 */
	public static String encode(String key){
		byte[] bt = key.getBytes();
		return (new Base64().encodeToString(bt));
	}

	/**
	 * 对输入的字符串进行Base64解码。
	 *
	 * @param key 需要进行Base64解码的原始字符串。
	 * @return 返回解码后的字符串。
	 * @throws Exception 如果解码过程中发生异常，则抛出。
	 */
	public static String decode(String key) throws Exception {
		// 确保输入不为空
		if (key == null || key.trim().isEmpty()) {
			return "";
		}
		
		// 修正BASE64字符串，确保其长度是4的倍数
		String fixedKey = key;
		int padding = key.length() % 4;
		if (padding > 0) {
			fixedKey = key + "====".substring(0, 4 - padding);
		}
		
		// 解码
		byte[] bt = new Base64().decode(fixedKey);
		return new String(bt);
	}


}