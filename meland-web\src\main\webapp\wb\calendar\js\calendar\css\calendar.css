.x-mydate-selected a {
  background: repeat-x left top;
  border: 1px solid;
  background-color: #eee !important;
  border-color: transparent;
}

.x-mydate-selected span {
  font-weight: bold;
}

.x-calender-main .x-panel-body {
  background: #fff;
}

.x-splitter-focus:after {
  border: none !important;
}

.x-calendar-link {
  cursor: pointer;
}

.x-calendar-container {
  padding: 5px 5px 4px 5px;
  font-family: arial;
  background: #fff;
}

.x-calendar-west {
  font-family: arial;
}

.x-calendar-west .x-datepicker {
  border: none !important;
}

.x-calendar-west .x-panel-header-noborder {
  background: none;
  border: none;
}

.x-calendar-west .x-panel-body,
.x-calendar-west .x-panel-bodyWrap {
  background: #fff;
  border: none;
}

.x-calendar-west>.x-panel-header {
  height: 50px;
  background: #fff;
  display: none;
}

.x-calendar-west>.x-panel-header .x-title-text {
  color: rgb(7, 83, 139);
}

.x-calendar-west>.x-panel-header .x-tool-after-title {
  display: none;
}

.x-calendar-west>.x-panel-header .x-title-text {
  font-size: 20px;
  padding-top: 5px;
}

.x-calendar-west .x-accordion-item .x-accordion-hd {
  border: none !important;
}

.x-calendar-container .x-toolbar {
  background: none;
}

.x-calendar-container .x-panel-body {
  border: none;
}

.x-calendar-container .x-panel-tbar-noborder .x-toolbar {
  border: none;
}

.x-dayview-port,
.x-monthview-port {
  border-left: 1px solid transparent;
  border-top: 1px solid transparent;
  position: relative;
}

.x-dayview-port {
  border-bottom: none;
}

.x-monthview-port {
  overflow: hidden;
}

.x-monthview-row {
  overflow: hidden;
  background-color: white;
  position: relative;
}

.x-monthview-bg,
.x-dayview-bg {
  position: absolute;
  table-layout: fixed;
}

.x-dayview-port {
  overflow-y: scroll;
  overflow-x: hidden;
  cursor: default;
}

.x-dayview-port table {
  table-layout: fixed;
}

.x-monthview-port {
  overflow: hidden;
}

.x-dayview-body {
  position: relative;
  overflow: hidden;
}

.x-dayview-lefter-table,
.x-dayview-viewer-table {
  border-top: 1px solid #EBEBEB;
}

.x-dayview-inner {
  overflow: hidden;
}

.x-dayview-viewer-row-height {
  height: 25px;
}

.x-dayview-lefter {
  background: #fafafa;
}

.x-dayview-lefter-even-row,
.x-dayview-lefter-odd-row {
  opacity: .5;
}

.x-dayview-lefter-active-row {
  background: #fff;
  opacity: 1;
}

.x-dayview-header-lefter,
.x-dayview-lefter {
  width: 70px;
}

.x-dayview-lefter-inner {
  color: #000;
  font-size: 20px;
  line-height: 25px;
}

.x-dayview-lefter-fine-inner {
  padding-right: 10px;
  font-size: 12px;
  color: gray;
}

.x-dayview-viewer {}

.x-monthview-title-height {
  height: 16px;
}

.x-monthview-lefter {
  vertical-align: top;
}

.x-monthview-rest {
  text-align: center;
  vertical-align: middle;
  font-size: 16px;
}

.x-monthview-lefter-inner-b:hover {
  text-decoration: underline;
}

.x-monthview-lefter-inner {
  font-size: 16px;
  line-height: 22px;
  color: #333;
  text-align: center;
  cursor: pointer;
}

.x-monthview-port-td {
  vertical-align: top;
}

.x-event-inner,
.x-dayview-lefter-cell,
.x-dayview-viewer-cell,
.x-dayview-header-days,
.x-monthview-viewer-cell,
.x-monthview-viewer-link,
.x-monthview-viewer-tool {
  line-height: 14px;
  font-size: 14px;
  -moz-user-select: none;
  font-size-adjust: none;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
}

.x-dayview-header-days {
  text-align: center;
  padding: 20px;
  font-size: 16px;
}

.x-dayview-header-day-link,
.x-dayview-header-days,
.x-dayview-west-date-span,
.x-monthview-viewer-link,
.x-dayview-lefter-cell {
  color: #333;
}

.x-dayview-header-weekday {
  display: block;
  font-size: 16px;
  font-family: sans-serif;
  line-height: 20px;
  color: #666;
}

.x-dayview-header-day-link {
  font-size: 45px;
  font-family: sans-serif;
  line-height: 50px;
  color: #333;
  cursor: pointer;
}

.x-dayview-header-day-link:hover {
  text-decoration: underline;
}

.x-monthview-viewer-link {
  cursor: pointer;
  padding-right: 5px;
}

.x-monthview-viewer-link:hover {
  text-decoration: underline;
}

.x-dayview-west-date-span {
  font-size: 15px;
  font-size-adjust: none;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
}

.x-dayview-pool-td {
  vertical-align: top;
}

.x-monthview-body {
  overflow: hidden;
  background-color: #D2DCFF;
  position: relative;
}

.x-event-select-cover {
  position: absolute;
  background-color: #eee;
  opacity: .5;
}

.x-monthview-viewer-cell {
  vertical-align: top;
  border-right: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.x-dayview-ct {
  position: relative;
}

.x-dayview-bg-cell {
  vertical-align: top;
  border-right: 1px solid #EBEBEB;
  height: 1000px;
}

.x-dayview-lefter-cell {
  text-align: center;
  vertical-align: middle;
  border-right: 1px solid #eee;
}

.x-dayview-lefter-even-row {
  border-bottom: 1px solid #eee;
}

.x-dayview-lefter-odd-row {
  border-bottom: 1px dotted transparent;
}

.x-dayview-even-row {
  border-right: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.x-dayview-odd-row {
  border-right: 1px solid #eee;
  border-bottom: 1px dotted #eee;
}

.x-dayview-pool-cell {
  overflow: hidden;
}

.x-dayview-pool-rest {
  height: 25px;
}

.x-dayview-today {
  background: rgb(255, 255, 214);
}

.x-dayview-header table,
.x-monthview-header,
.x-monthview-header table {
  table-layout: fixed;
}

.x-dayview-pool {
  overflow: hidden;
  border-bottom: 1px solid #ebebeb;
  position: relative;
}

.x-dayview-pool-cell-rest {
  height: 24px;
}

.x-dayview-pool table {}

.x-dayview-pool-viewer {
  border: 1px solid #eee;
  border-bottom: none;
  border-right-color: transparent;
  overflow: hidden;
  position: relative;
}

.x-calendar-searchbox {
  border: 1px solid #eee;
  height: 33px;
  padding: 0px 10px;
}

.x-monthview-viewer-title {
  height: 25px;
  line-height: 21px;
  font-size: 13px;
  border-right: 1px solid #eee;
  text-align: right;
}

.x-monthview-ct {
  position: absolute;
  line-height: 12px;
  font-size: 12px;
  table-layout: fixed;
}

.x-dayview-ct {
  line-height: 12px;
  font-size: 12px;
  table-layout: fixed;
}

.x-monthview-dragover {
  background: rgb(241, 244, 250);
}

.x-monthview-viewer-tool {
  text-align: right;
}

.x-monthview-tool-add {
  cursor: pointer;
  width: 16px;
  height: 16px;
  background: url(../image/color_swatch.png ) no-repeat scroll right center;
}

.x-monthview-tool-drop {
  cursor: pointer;
  padding: 0px 10px;
}

.x-monthview-tool-drop:hover {
  opacity: .8;
}

.x-calendar-over {
  background-color: rgb(217, 225, 255);
}

.x-calendar-event {
  -moz-user-select: none;
}

.x-event-cover {
  position: absolute;
  overflow: hidden;
  opacity: 0.95;
  -moz-opacity: 0.95;
  filter: alpha(opacity=95);
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border: 1px solid #fff;
  color: #fff;
}

.x-legend-cover {
  position: relative;
  height: 24px;
  line-height: 24px;
  padding-left: 5px;
  margin-right: 3px;
  overflow: hidden;
}

.x-legend-title-default {
  white-space: nowrap;
  text-overflow: ellipsis;
}

.x-whole-cover {
  position: relative;
  height: 24px;
  border: 1px solid #fff;
  overflow: hidden;
  padding: 0px 10px;
  border-radius: 3px;
}

.x-whole-left-join {
  border-right: 5px solid white;
  border-bottom: 5px solid transparent;
  border-top: 5px solid transparent;
  width: 0;
  height: 0;
  line-height: 0px;
  font-size: 0px;
  position: absolute;
  margin-left: 4px;
  margin-top: -13px;
}

.x-whole-right-join {
  border-left: 5px solid white;
  border-bottom: 5px solid transparent;
  border-top: 5px solid transparent;
  width: 0;
  height: 0;
  line-height: 0px;
  font-size: 0px;
  position: absolute;
  margin-left: -4px;
  margin-top: -13px;
}

.x-dayview-pool-collapse {
  border-left: 8px solid black;
  border-bottom: 8px solid transparent;
  border-top: 8px solid transparent;
  width: 0;
  height: 0;
  line-height: 0px;
  font-size: 0px;
  margin-left: 50px;
  margin-top: 5px;
  cursor: pointer;
}

.x-dayview-pool-expand {
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid black;
  width: 0;
  height: 0;
  line-height: 0px;
  font-size: 0px;
  margin-left: 44px;
  margin-top: 11px;
  cursor: pointer;
}

.x-whole-cover table {
  table-layout: fixed;
}

.x-calendar-cover {
  position: relative;
  border-bottom: 1px solid transparent;
  opacity: 1;
  -moz-opacity: 1;
  filter: alpha(opacity=100);
  cursor: pointer;
  overflow: hidden;
}

.x-calendardrop-cover {
  position: relative;
  padding: 10px 0px 0px 10px;
}

.x-calendardrop-cover:hover {
  opacity: .9;
}

.x-calendar-pin {
  position: absolute;
  padding-top: 3px;
}

.x-calendar-event-pin {
  position: absolute;
  padding-top: 2px;
}

.x-calendar-event-pin-off {
  cursor: pointer;
  width: 10px;
  background: transparent url(../image/bullet_green.png) no-repeat scroll center center;
}

.x-calendar-event-pin-on {
  cursor: pointer;
  width: 10px;
  background: transparent url(../image/bullet_red.png) no-repeat scroll center center;
}

.x-dayview-timeline {
  background-color: transparent;
  background-image: url(../image/timeline.png);
  background-repeat: repeat-x;
  background-position: 0% 0%;
}

.x-dayview-timeindex {
  background-color: transparent;
  background-image: url(../image/timeindex.png);
  background-repeat: no-repeat;
  background-position: 0% 0%;
}

.x-event-editing {
  /*   background-color: #3c8dbc; */
  
  opacity: 1;
  -moz-opacity: 1;
  filter: alpha(opacity=100);
  -moz-box-shadow: 2px 2px 10px rgba(100, 100, 100, 0.6);
  -webkit-box-shadow: rgba(100, 100, 100, 0.6) 2px 2px 10px;
}
/* .skin-purple .x-event-editing {
  background-color: #605ca8;
}

.skin-purple-light .x-event-editing {
  background-color: #605ca8;
}

.skin-green .x-event-editing {
  background-color: #00a65a;
}

.skin-green-light .x-event-editing {
  background-color: #00a65a;
}

.skin-red .x-event-editing {
  background-color: #dd4b39;
}

.skin-red-light .x-event-editing {
  background-color: #dd4b39;
}

.skin-yellow .x-event-editing {
  background-color: #f39c12;
}

.skin-yellow-light .x-event-editing {
  background-color: #f39c12;
} */

.x-event-content-link {
  text-overflow: ellipsis;
  cursor: pointer;
  line-height: 12px;
  font-size: 12px;
  padding-left: 5px;
  padding-right: 5px;
}

.ext-strict .x-whole-cover-inner {
  position: relative;
}

.ext-ie .x-whole-cover-inner {
  position: absolute;
}

.x-whole-title-b {
  color: white;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 13px;
  font-weight: bold;
  line-height: 20px;
  height: 100%;
  cursor: pointer;
  padding-left: 5px;
}

.x-legend-title-b {
  position: absolute;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 13px;
  cursor: pointer;
}

.x-legend-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 100px;
}

.x-calendar-cover td {
  padding: 0px;
  vertical-align: middle;
}

.x-calendar-title-b {
  color: #000;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 16px;
  cursor: pointer;
  overflow: hidden;
  line-height: 20px;
}

.x-calendar-editor-form-title {
  font-size: 20px;
  padding-bottom: 20px;
  color: rgb(7, 83, 139);
}

.x-legend-title-default,
.x-whole-title-default,
.x-calendar-title-default {
  line-height: 11px;
  -moz-user-select: none;
  font-size: 11px;
  font-size-adjust: none;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  color: white;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.x-whole-td {
  width: 5px;
  height: 16px;
  line-height: 11px;
  font-size: 11px;
}

.x-calendar-td {
  width: 5px;
  height: 18px;
  line-height: 11px;
  font-size: 11px;
}

.x-whole-title {
  height: 16px;
  line-height: 12px;
  font-size: 12px;
}

.x-calendar-title {
  height: 18px;
  line-height: 12px;
  font-size: 12px;
}

.x-whole-left,
.x-whole-right {
  height: 16px;
}

.x-legend-title-link {
  line-height: 12px;
  font-size: 12px;
}

.x-legend-tool {
  cursor: pointer;
  position: absolute;
  top: 50%;
  margin-top: -10px;
  right: 0px;
  height: 20px;
  text-align: center;
}

.x-legend-tool .fa {
  line-height: 20px;
}

.x-legend-tool-td,
.x-legend-tool {
  width: 20px;
  padding-right: 10px;
}

.ext-ie .x-event-title-default {
  padding-top: 3px;
}

.x-event-title-default {
  overflow: hidden;
  height: 18px;
  line-height: 18px;
  cursor: pointer;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-weight: bold;
  position: relative;
  padding-left: 5px;
  padding-right: 5px;
}

.x-event-type {
  margin-top: 1px;
  width: 10px;
  height: 10px;
  border: 1px solid white;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
}

.x-legend-title-b .x-event-type {
  border: 1px solid gray;
}

.x-event-content-default {
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 5px;
  padding-right: 5px;
}

.x-calendar-menu .x-color-palette {
  height: auto;
  width: auto;
}

.x-calendar-resultview-lock {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.x-event-more {
  cursor: pointer;
  color: #666;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  position: relative;
  padding-left: 10px;
}

.x-event-more-ct {}

.x-event-more-icon {
  position: absolute;
  cursor: pointer;
  width: 16px;
  height: 16px;
  background: url(../image/ns-expand.gif ) no-repeat scroll center center;
}

.x-event-bottom {
  height: 5px;
}

.x-event-bottom-default {
  cursor: s-resize;
}

.x-monthview-cell-cut {}
/*
 *  for detail container
 */

.x-event-detail-ct {
  z-index: 4100;
  position: absolute;
  background-color: white;
  width: 300px;
  padding: 15px;
  -moz-box-shadow: 0px 0px 20px rgba(100, 100, 100, 0.7);
  -webkit-box-shadow: rgba(100, 100, 100, 0.7) 0px 0px 20px;
  box-shadow: rgba(100, 100, 100, 0.7) 0px 0px 20px;
}

.x-event-detail-title {
  padding: 5px 5px 30px 5px;
  font-size: 30px;
  line-height: 30px;
  font-weight: bold;
}

.x-event-detail-viewer {
  overflow-x: hidden;
  overflow-y: auto;
}

.x-event-detail-tool {
  position: absolute;
  right: 15px;
  top: 15px;
  font-size: 16px;
  cursor: pointer;
  color: #333;
  font-weight: normal;
}

.x-event-detail-foot {
  overflow: hidden;
  padding: 15px 5px 5px 5px;
}

.x-event-detail-foot-text {
  font-size: 14px;
  color: #000;
}

.x-calendar-readonly .x-legend-tool {
  display: none;
}

.x-locked-event {
  width: 10px;
  height: 10px;
  background: url(../image/lock_small.png ) no-repeat scroll left top;
}

.x-locked-white-event {
  width: 10px;
  height: 10px;
  background: url(../image/lock_small.png ) no-repeat scroll left top;
}

.x-alert-event {
  width: 7px;
  height: 7px;
  background: url(../image/clock-black.png ) no-repeat scroll left top;
}

.x-alert-white-event {
  width: 7px;
  height: 7px;
  background: url(../image/clock-white.png ) no-repeat scroll left top;
}

.x-repeat-event-info-ct {
  height: 30px;
  position: relative;
}

.x-repeat-event-info {
  border: 1px solid black;
  background-color: rgb(176, 212, 176);
  height: 25px;
  line-height: 25px;
  font-size: 12px;
  padding-left: 3px;
  padding-right: 2px;
  position: absolute;
}

.x-resultview-event-hide {
  width: 16px;
  height: 16px;
  background: url(../image/lightbulb_off.png ) no-repeat !important;
}

.x-resultview-event-show {
  width: 16px;
  height: 16px;
  background: url(../image/lightbulb.png ) no-repeat !important;
}

.x-dayview-wn {
  text-align: center;
  font-size: 24px;
  cursor: default;
}

.x-dayview-wn-link {
  cursor: pointer;
}

.x-date-picker {
  border-color: #99BBE8;
}

.x-date-middle,
.x-date-left,
.x-date-right {
  background: #AAC7ED;
}

.x-calendar-alert-board {
  padding: 5px;
  border-bottom: 1px solid white;
  word-wrap: break-word;
}

.x-event-pin {
  display: none;
}

.x-dayview-today-link {
  font-weight: bold;
  color: rgb(7, 83, 139);
}

.x-event-editor .x-window-body {
  border: none;
}

.x-calendar-list-item {
  padding: 5px;
}

.x-event-editor {
  border: none !important;
  border-radius: 3px;
  padding: 0px 0px 10px;
}

.x-event-title-box {
  font-size: 20px;
  color: #fff;
}

.x-event-editor-close {
  color: #fff;
  cursor: pointer;
}

.x-event-editor-close:hover {
  opacity: .9;
}

.x-calendar-share-title {
  font-size: 16px;
  color: rgb(7, 83, 139);
}

.x-line-display {
  font-size: 14px;
  padding: 0px 10px;
}

.x-result-fromto:hover {
  text-decoration: underline;
  color: rgb(7, 83, 139);
  cursor: pointer;
}

.x-calendar-titlebar {
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
  z-index: 10;
  background: #fafafa;
  padding: 0px 15px;
}

.x-calendar-titlebar .x-btn-default-toolbar-small,
.x-calendar-titlebar .x-btn-default-toolbar-meidum,
.x-calendar-titlebar .x-btn-default-toolbar-large {
  border: none;
  background: transparent;
}

.x-calendar-titlebox {
  font-size: 20px;
  color: rgb(7, 83, 139);
  text-align: center;
}

.x-year-span {
  display: block;
  font-size: 16px;
  line-height: 16px;
  padding-bottom: 10px;
  color: #000;
  font-weight: normal;
}

.x-today {
  color: rgb(7, 83, 139);
}

.x-monthview-today,
.x-dayview-today {
  background: #fafafa;
}

.x-monthview-today-text {
  color: rgb(7, 83, 139);
  font-weight: bold;
}

.x-calendar-panel .x-panel-body {
  padding: 0px 15px;
}

.x-calendar-west-bbar {
  padding-bottom: 0px;
}