package com.wb.util;

import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.UnknownHostException;

public class IPv6Range {
    private InetAddress startAddress;
    private InetAddress endAddress;

    public IPv6Range(String cidr) throws UnknownHostException {
        String[] parts = cidr.split("/");
        int prefixLength = Integer.parseInt(parts[1]);

        InetAddress baseAddress = Inet6Address.getByName(parts[0]);
        byte[] baseAddressBytes = baseAddress.getAddress();

        // Calculate the start address
        startAddress = InetAddress.getByAddress(baseAddressBytes);

        // Calculate the end address
        long numberOfBitsToFlip = 128 - prefixLength;
        long numberOfAddresses = (long) Math.pow(2, numberOfBitsToFlip) - 1;

        for (int i = 0; i < numberOfBitsToFlip; i++) {
            int byteIndex = 15 - i / 8;
            int bitIndex = i % 8;
            baseAddressBytes[byteIndex] |= (1 << bitIndex);
        }

        endAddress = InetAddress.getByAddress(baseAddressBytes);
    }

    public boolean isInRange(InetAddress address) {
        return isGreaterOrEqual(address, startAddress) && isLessOrEqual(address, endAddress);
    }

    private boolean isGreaterOrEqual(InetAddress a1, InetAddress a2) {
        byte[] a1Bytes = a1.getAddress();
        byte[] a2Bytes = a2.getAddress();
        for (int i = 0; i < a1Bytes.length; i++) {
            if ((a1Bytes[i] & 0xFF) > (a2Bytes[i] & 0xFF)) {
                return true;
            } else if ((a1Bytes[i] & 0xFF) < (a2Bytes[i] & 0xFF)) {
                return false;
            }
        }
        return true;
    }

    private boolean isLessOrEqual(InetAddress a1, InetAddress a2) {
        byte[] a1Bytes = a1.getAddress();
        byte[] a2Bytes = a2.getAddress();
        for (int i = 0; i < a1Bytes.length; i++) {
            if ((a1Bytes[i] & 0xFF) < (a2Bytes[i] & 0xFF)) {
                return true;
            } else if ((a1Bytes[i] & 0xFF) > (a2Bytes[i] & 0xFF)) {
                return false;
            }
        }
        return true;
    }
}

