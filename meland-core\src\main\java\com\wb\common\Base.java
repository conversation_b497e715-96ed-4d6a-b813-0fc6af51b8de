package com.wb.common;

import com.wb.aliyun.sls.SLSLogUtil;
import com.wb.cache.RedisCache;
import com.wb.common.session.OnlineUserManager;
import com.wb.common.xss.JsoupXssCleaner;
import com.wb.common.xss.XssCleaner;
import com.wb.common.xss.XssRequestWrapper;
import com.wb.interact.FilePush;
import com.wb.rocketmq.expense.MessageConsume;
import com.wb.tool.Encrypter;
import com.wb.tool.TaskManager;
import com.wb.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import weixin.popular.support.ExpireKey;
import weixin.popular.support.expirekey.DefaultExpireKey;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.regex.Pattern;

/**
 * WebBuilder基本过滤器，拦截所有的请求路径。任何对应用发起的请求都会触发doFilter方法。
 * <p>
 * 主要作用有： 初始化系统，完成对静态变量的设置和计划任务的加载。 对XWL模块的访问处理，解析对模块文件的访问。
 * 缓存wb目录下的资源，缓存文件内容至内存并根据需要启用gzip压缩，以提高响应性能。 禁止对系统目录的访问，禁止从外部对wb/system进行访问。
 * </p>
 *
 * @see com.wb.common.FileBuffer
 * @see com.wb.common.Parser
 */
public class Base implements Filter {

    private static final Logger LOGGER = LoggerFactory.getLogger(Base.class);
    /**
     * Servlet上下文对象。
     */
    public static volatile ServletContext servletContext;
    /**
     * 应用目录的根路径。
     */
    public static File path;
    /**
     * 应用目录的根路径长度。
     */
    public static int pathLen;
    /**
     * 模块文件的根路径。
     */
    public static File modulePath;
    /**
     * 模块目录路径文本。
     */
    public static String modulePathText;
    /**
     * 模块目录路径长度。
     */
    public static int modulePathLen;
    /**
     * 系统的启动时间。
     */
    public static Date startTime;
    public static int rootPathLen;
    /**
     * 提供静态redis对象，该对象可为任何应用使用。
     */
    public static RedisCache map;
    /**
     * 是否初始化失败。
     */
    private static boolean initFailed;
    /**
     * 初始化失败异常对象。
     */
    private static Throwable initError;
    /**
     * 用于支付订单去重。
     */
    public static ExpireKey expireKey = new DefaultExpireKey();

    /**
     * 允许访问的域名
     */
    public static volatile Pattern allowedDomainPattern;

    private static final XssCleaner xssCleaner = new JsoupXssCleaner();

    /**
     * 在线用户管理器
     */
    public static OnlineUserManager onlineUserManager;

    /**
     * WebBuilder过滤器，实现对xwl文件的执行，文件的缓存和系统文件的保护功能。
     *
     * @param request  ServletRequest 请求对象。
     * @param response ServletResponse 响应对象。
     * @param chain    过滤器链。
     * @throws ServletException 如果执行过程中发生异常将抛出。
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        if (initFailed) {
            throw new RuntimeException(initError);
        }
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse resp = (HttpServletResponse) response;

        if (Var.getBool("sys.config.sys.enableCSP")) {
            // 添加安全响应头
            resp.setHeader("X-Content-Type-Options", "nosniff");
            resp.setHeader("X-Frame-Options", "SAMEORIGIN");
            resp.setHeader("X-XSS-Protection", "1; mode=block");
            resp.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
        }

        // 更新用户在线状态
        try {
            if (onlineUserManager != null) {
                onlineUserManager.handleHttpRequest(req);
            }
        } catch (Exception e) {
            // 不影响正常请求处理
            LOGGER.error("处理在线用户状态时发生错误", e);
        }
        String url;
        if (Var.useServletPath) {
            url = req.getServletPath();
        } else {
            url = req.getRequestURI().substring(rootPathLen);
        }
        String xwl = UrlBuffer.get(url);
        if (xwl != null) {
            request.setCharacterEncoding("utf-8");
            if (xwl.isEmpty()) {
                xwl = request.getParameter("xwl");
                if (xwl == null) {
                    resp.sendError(HttpServletResponse.SC_BAD_REQUEST, "null xwl");
                    return;
                }
                // 使用Java内置正则表达式库对"xwl"参数进行验证
                String xwlPattern = "^[\\w-]+(/[\\w-]+)*$";
                boolean isValid = Pattern.matches(xwlPattern, xwl);

                if (!isValid) {
                    LogUtil.warn(req, "无效访问[" + xwl + "]。");
                    resp.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid xwl parameter");
                    return;
                }
                xwl = StringUtil.concat(xwl, ".xwl");
            }
            if (Var.getBool("sys.config.sys.enableCircuit")) {
                // 添加XWL模块限流检查
                if (!com.wb.circuit.XwlRateLimiterService.getInstance().checkRateLimit(xwl)) {
                    // 超过限流阈值，返回请求过多
                    resp.setStatus(429); // Too Many Requests
                    resp.setContentType("application/json;charset=UTF-8");
                    resp.getWriter()
                            .write("{\"success\":false,\"errMsg\":\"请重试。\",\"errCode\":\"TOO_MANY_REQUESTS\"}");
                    return;
                }

                // 添加XWL模块熔断检查
                if (!com.wb.circuit.XwlCircuitBreakerService.getInstance().allowRequest(xwl)) {
                    // 熔断状态，返回服务不可用
                    resp.setStatus(HttpServletResponse.SC_SERVICE_UNAVAILABLE);
                    resp.setContentType("application/json;charset=UTF-8");
                    resp.getWriter().write(
                            "{\"success\":false,\"errMsg\":\"服务暂时不可用，请稍后再试\",\"errCode\":\"SERVICE_UNAVAILABLE\"}");
                    return;
                }
            }

            req.setAttribute("sys.xwl", xwl);
            setRequest(req);

            // 记录开始时间
            long startTime = System.currentTimeMillis();
            boolean hasException = false;

            try {
                if (Var.enableXssFilter && StringUtil.indexOf(Var.excludeUrl, xwl) == -1) {
                    Parser parser = new Parser(new XssRequestWrapper(req, xssCleaner), resp);
                    parser.parse(xwl);
                } else {
                    Parser parser = new Parser(req, resp);
                    parser.parse(xwl);
                }
            } catch (Exception e) {
                hasException = true;
                if (Var.getBool("sys.config.sys.enableCircuit")) {
                    // 记录异常，用于熔断计数
                    com.wb.circuit.XwlCircuitBreakerService.getInstance().recordFailure(xwl);
                }
                throw e;
            } finally {
                // 记录执行结果
                if (!hasException) {
                    // 执行时间
                    long executionTime = System.currentTimeMillis() - startTime;
                    LOGGER.debug("xwl:" + xwl + " 执行时间: " + executionTime + "ms");
                    if (Var.getBool("sys.config.sys.enableCircuit")) {
                        // 记录成功，同时提供执行时间以便监控性能问题
                        com.wb.circuit.XwlCircuitBreakerService.getInstance().recordSuccess(xwl, executionTime);
                    }
                }
            }
        } else {
            String lowerUrl = url.toLowerCase();
            if (lowerUrl.startsWith("/wb/modules/") || lowerUrl.startsWith("/wb/system/")) {
                // 系统保护的目录
                resp.sendError(HttpServletResponse.SC_FORBIDDEN, url);
            } else if (Var.cacheEnabled && (lowerUrl.startsWith("/wb/"))) {
                // 对referer进行检测
                String referer = req.getHeader("Referer");
                if (referer == null || !allowedDomainPattern.matcher(referer).matches()) {
                    resp.sendError(HttpServletResponse.SC_FORBIDDEN, "无效访问，请从正规渠道访问。");
                    return;
                }
                // 对静态资源进行缓存
                FileBuffer.service(url, req, resp);
            } else {
                // 其他交由用户自定义处理
                chain.doFilter(
                        Var.enableXssFilter ? new XssRequestWrapper((HttpServletRequest) request, xssCleaner) : request,
                        response);
            }
        }
    }

    /**
     * 系统的初始化以及定义系统常用的静态变量。
     *
     * @param config 过滤器配置对象。
     * @throws ServletException 如果执行过程中发生异常将抛出。
     */
    @Override
    public void init(FilterConfig config) throws ServletException {
        try {
            startTime = new Date();
            servletContext = config.getServletContext();
            path = new File(servletContext.getRealPath("/"));
            Var.file = new File(path, "wb/system/var.json");
            pathLen = FileUtil.getPath(path).length() + 1;
            modulePath = new File(path, "wb/modules");
            modulePathText = FileUtil.getPath(Base.modulePath);
            modulePathLen = modulePathText.length() + 1;
            rootPathLen = servletContext.getContextPath().length();
            map = SpringContextUtils.getBean(RedisCache.class);
            // 初始化Redis配置，确保Redis线程为守护线程
            // RedisConfiguration.init(); // 在Web应用中不需要设置守护线程

            // 添加Redis集群模式检测，确保只执行一次
            try {
                LOGGER.info("系统启动，开始执行Redis集群模式检测...");
                if (map != null && map.getRedisTemplate() != null) {
                    // 调用方法进行Redis集群模式检测，方法内部会处理锁逻辑
                    com.wb.cache.RedisConfiguration.detectAndSetClusterMode(map.getRedisTemplate());
                } else {
                    LOGGER.warn("RedisCache未初始化或RedisTemplate为空，跳过Redis集群模式检测");
                }
            } catch (Exception e) {
                LOGGER.error("Redis集群模式检测发生异常: " + e.getMessage(), e);
            }

            SysUtil.reload(2);
            if (!Var.jndi.isEmpty() && !Var.getBool("sys.service.allowInstall")) {
                // 如果默认jndi为空，表示数据库未配置。待安装配置完成后加载数据库相关类。
                SysUtil.reload(3);
                Base.map.put("task.message.running", false);
                TaskManager.start();
                WbUtil.warmModuleRoles();
                // 初始化FilePush线程池（如果需要重新初始化）
                int threadCount = 10;
                FilePush.reinitializeThreadPool(threadCount);
                LOGGER.info("FilePush下载线程池已初始化，线程数: " + threadCount);
            }
            if (Var.getBool("sys.config.aliyun.enableMQ"))
                MessageConsume.load();
            // 初始化在线用户管理器
            onlineUserManager = SpringContextUtils.getBean(OnlineUserManager.class);
            checkLicense();
            runInitScript();
        } catch (Throwable e) {
            initFailed = true;
            initError = e;
            LOGGER.error("启动异常", e);
        }
    }

    /**
     * 加载referer域名设置
     */
    public static void loadDomain() {
        // 对访问域名进行解析
        // 从配置中读取可能包含多个域名的字符串，每个域名可能位于不同的行
        String configDomains = Var.getString("sys.config.referer");
        String[] lines = configDomains.split("\\r?\\n"); // 分割多行
        StringBuilder patternBuilder = new StringBuilder("^(");
        for (String configDomain : lines) {
            // 检查并处理协议
            String protocol = "";
            if (configDomain.startsWith("http://")) {
                protocol = "http://";
                configDomain = configDomain.substring(7);
            } else if (configDomain.startsWith("https://")) {
                protocol = "https://";
                configDomain = configDomain.substring(8);
            } else {
                // 没有明确协议的情况，允许任何协议
                protocol = "https?://";
            }
            // 检查并处理通配符
            String domain = configDomain.startsWith("*.") ? configDomain.replace("*.", "([a-zA-Z0-9]+\\.)?")
                    : Pattern.quote(configDomain);
            // 为当前行构建正则表达式，并添加到总的正则表达式构建器中
            patternBuilder.append(protocol).append(domain).append("(\\:\\d+)?(/.*)?|");
        }
        // 移除最后一个"|"
        if (patternBuilder.length() > 2) {
            patternBuilder.setLength(patternBuilder.length() - 1);
        }
        patternBuilder.append(")$"); // 结束正则表达式
        allowedDomainPattern = Pattern.compile(patternBuilder.toString());
    }

    /**
     * 对request对象进行一些属性设置。
     */
    private void setRequest(HttpServletRequest request) {
        long time = System.currentTimeMillis();
        request.setAttribute("sys.date", new Date(time - (time % 1000)));
        request.setAttribute("sys.id", SysUtil.getId());
    }

    /**
     * 检查注册码是否合法，如果非法将终止系统的运行。 该方法提供了授权控制的原理，将注册码同硬件mac绑定，使软件的运行限制在特定机器。
     * 为增强保密和控制效果，实际验证代码可以放置在多处且能避免破解的系统和业务代码中。
     */
    private void checkLicense() throws Exception {
        String code = FileUtil.readString(new File(Base.path, "wb/system/lic.dat"));
        String validCode = "2782051B84F1254EA33242FCC377861A";// 通用的注册码，可以更改
        String key = "wblic";// 生成注册码时使用的密钥，可以更改
        int index = code.indexOf("lic=");
        if (index != -1) {
            code = code.substring(index + 4);
            if (code.equals(validCode) || code.equals(Encrypter.getMD5(SysUtil.getMacAddress() + key))) {
                return;
            }
        }
        // 如果验证未通过终止运行
        System.exit(0);
    }

    /**
     * 如果存在初始化脚本文件，运行初始化脚本。
     */
    private void runInitScript() throws Exception {
        File file = new File(path, "wb/system/init.js");
        if (file.exists()) {
            ScriptBuffer.run(FileUtil.readString(file), "init.js");
        }
    }

    /**
     * 系统停止时执行，完成系统的清理。
     */
    @Override
    public void destroy() {
        try {
            // 通知WebSocketSessionManager停止Redis操作
            try {
                LOGGER.info("正在通知WebSocketSessionManager停止Redis操作...");
                com.wb.common.ws.WebSocketSessionManager.signalShutdown();
                LOGGER.info("WebSocketSessionManager已收到停止信号");
            } catch (Exception e) {
                LOGGER.error("通知WebSocketSessionManager停止Redis操作时发生错误: " + e.getMessage());
            }

            // 关闭FilePush线程池
            try {
                LOGGER.info("正在关闭FilePush下载线程池...");

                // 先清理所有过期任务
                int cleanedCount = FilePush.cleanupExpiredTasks(0); // 传入0表示清理所有任务
                if (cleanedCount > 0) {
                    LOGGER.info("应用关闭前清理下载任务完成，共清理 " + cleanedCount + " 个任务");
                }

                boolean success = FilePush.waitForThreadsToComplete(30);
                if (success) {
                    LOGGER.info("FilePush下载线程池已成功关闭");
                } else {
                    LOGGER.warn("FilePush下载线程池关闭可能不完全");
                }
            } catch (Exception e) {
                LOGGER.error("关闭FilePush下载线程池时发生错误: " + e.getMessage());
            }

            // 关闭RocketMQ消息消费服务
            try {
                if (Var.getBool("sys.config.aliyun.enableMQ")) {
                    LOGGER.info("正在关闭RocketMQ消息消费服务...");
                    MessageConsume.shutdown();
                    LOGGER.info("RocketMQ消息消费服务已关闭");
                }
            } catch (Exception e) {
                LOGGER.error("关闭RocketMQ消息消费服务时发生错误: " + e.getMessage());
            }

            TaskManager.stop();
            SLSLogUtil.close();
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }
}
