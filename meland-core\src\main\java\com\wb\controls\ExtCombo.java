package com.wb.controls;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;

import com.wb.common.KVBuffer;
import com.wb.util.DbUtil;
import com.wb.util.StringUtil;

/**
 * Combo控件的解析类。
 */
public class ExtCombo extends ExtControl {

	protected void extendConfig() throws Exception {
		String keyName = gs("keyName");
		boolean keyStatusFilter = gb("keyStatusFilter");
		String queryControl = gs("queryControl");
		if (!keyName.isEmpty()) {
			if (this.hasItems)
				this.headerScript.append(',');
			else
				this.hasItems = true;
			
			// 根据是否有状态过滤参数调用不同的方法
			if (!keyStatusFilter) {
				this.headerScript.append(getkeyNameScript(keyName));
			} else {
				// 有状态过滤时，默认筛选启用状态(1)
				this.headerScript.append(getkeyNameScript(keyName, 1));
			}
		}
		if (!queryControl.isEmpty()) {
			if (queryControl.startsWith("app."))
				queryControl = queryControl.substring(4);
			if (this.hasItems)
				this.headerScript.append(',');
			else
				this.hasItems = true;
			this.headerScript.append(getQueryScript(queryControl));
		}
	}

	/**
	 * 获得键值相关的表达式脚本。
	 * @return
	 */
	public static String getkeyNameScript(String keyName) {
		return StringUtil.concat(
				"displayField:\"V\",valueField:\"K\",forceSelection:true,queryMode:\"local\",store:{fields:[\"K\",\"V\"],sorters:\"K\",data:",
				KVBuffer.getList(keyName), "},keyName:\"", keyName, "\"");
	}
	
	/**
	 * 获得键值相关的表达式脚本(带状态过滤)。
	 * @return
	 */
	public static String getkeyNameScript(String keyName, Integer statusFilter) {
		return StringUtil.concat(
				"displayField:\"V\",valueField:\"K\",forceSelection:true,queryMode:\"local\",store:{fields:[\"K\",\"V\"],sorters:\"K\",data:",
				KVBuffer.getList(keyName, statusFilter), "},keyName:\"", keyName, "\"");
	}

	public String getQueryScript(String queryControl) throws Exception {
		ResultSet rs = (ResultSet) this.request.getAttribute(queryControl);
		if (rs == null)
			throw new IllegalArgumentException("Query \"" + queryControl + "\" is not found.");
		ResultSetMetaData meta = rs.getMetaData();
		StringBuilder script = new StringBuilder();

		script.append("queryMode:\"local\",displayField:");
		script.append(StringUtil.quote(DbUtil.getFieldName(meta.getColumnLabel(1))));
		script.append(',');
		if (meta.getColumnCount() > 1) {
			script.append("valueField:");
			script.append(StringUtil.quote(DbUtil.getFieldName(meta.getColumnLabel(2))));
			script.append(',');
		}
		script.append("store:{fields:");
		script.append(DbUtil.getFields(meta, null, null, null));
		script.append(",data:");
		script.append(DbUtil.getData(rs));
		script.append('}');
		return script.toString();
	}
}