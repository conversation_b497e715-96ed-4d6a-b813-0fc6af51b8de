package com.wb.message;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import org.json.JSONObject;

public class Constants {

	/** 提供静态BlockingQueue，用于队列使用。*/
	public static BlockingQueue<JSONObject> MSG_QUEUE = new LinkedBlockingQueue<>();

	/**
	 * 消息规则编码
	 */
	public static final String MSG_CODE = "CODE";

	/**
	 * 消息标题
	 */
	public static final String MSG_TITLE = "TITLE";

	/**
	 * 消息内容
	 */
	public static final String MSG_CONTENT = "CONTENT";

	/**
	 * 接收消息的用户
	 */
	public static final String MSG_USERS = "USERS";

	/**
	 * 接收消息参数
	 */
	public static final String MSG_DATA = "DATA";

	/**
	 * 消息重试次数
	 */
	public static final int MSG_RETRY = 3;
}