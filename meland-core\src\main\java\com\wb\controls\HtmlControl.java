package com.wb.controls;

import java.util.ArrayList;
import java.util.Set;
import java.util.Map.Entry;

import org.json.JSONObject;

import com.wb.util.StringUtil;
import com.wb.util.WebUtil;

/**
 * HTML控件的解析类。
 */
public class HtmlControl extends ScriptControl {
	public void create() throws Exception {
		String tag = StringUtil.select(new String[] { gs("tagType"), (String) this.generalMeta.opt("type") });
		StringBuilder tagEnd = new StringBuilder(tag.length() + 3);
		String xtype = (String) this.generalMeta.opt("xtype");
		tagEnd.append("</").append(tag).append(">");
		ArrayList<String> classList = new ArrayList<String>();
		ArrayList<String> styleList = new ArrayList<String>();

		// tag开始
		this.headerHtml.append('<');
		this.headerHtml.append(tag);
		if (xtype != null) {
			// xtype在html控件中定义为固定的输出项
			this.headerHtml.append(' ');
			this.headerHtml.append(xtype);
		}
		// 初始化baseClass
		String value = this.generalMeta.optString("baseClass");
		if (!value.isEmpty()) {
			classList.add(value);
		}
		// 输出配置项
		processConfigs(classList, styleList);
		// 添加合并的class
		value = gs("class");
		if (!value.isEmpty())
			classList.add(value);
		value = StringUtil.join(classList, " ");
		if (!value.isEmpty()) {
			this.headerHtml.append(" class=\"");
			this.headerHtml.append(value);
			this.headerHtml.append('"');
		}

		value = gs("style");
		if (!value.isEmpty())
			styleList.add(value);
		value = StringUtil.join(styleList, ";");
		if (!value.isEmpty()) {
			this.headerHtml.append(" style=\"");
			this.headerHtml.append(value);
			if (!value.endsWith(";"))
				this.headerHtml.append(';');
			this.headerHtml.append('"');
		}
		// tag结尾
		this.headerHtml.append('>');
		// glyph图标
		String glyph = gs("glyph");
		if (!glyph.isEmpty()) {
			this.headerHtml.append("<span class=\"wb_glyph\">&#" + Integer.valueOf(glyph, 16) + ";</span> ");
		}
		// text为短html
		String html = gs("text");
		if (!html.isEmpty())
			this.headerHtml.append(html);
		html = gs("html");
		if (!html.isEmpty())
			this.headerHtml.append(html);
		if (!Boolean.FALSE.equals(this.generalMeta.opt("tagEnd")))
			this.footerHtml.insert(0, tagEnd);
	}

	/**
	 * 处理配置项。
	 * @param classList class属性列表。
	 * @param styleList style属性列表。
	 */
	protected void processConfigs(ArrayList<String> classList, ArrayList<String> styleList) {
		Set<Entry<String, Object>> es = this.configs.entrySet();
		boolean hasGroup = (classList != null) && (styleList != null);

		for (Entry<String, Object> entry : es) {
			String key = (String) entry.getKey();
			String value = (String) entry.getValue();
			JSONObject itemObject = (JSONObject) this.configsMeta.opt(key);
			if (itemObject != null) {
				if (!Boolean.TRUE.equals(itemObject.opt("hidden"))) {
					String rename = (String) itemObject.opt("rename");
					if (rename != null)
						key = rename;
					if (hasGroup) {
						String group = (String) itemObject.opt("group");
						if (group != null) {
							if ("class".equals(group)) {
								classList.add(value);
								continue;
							}
							styleList.add(StringUtil.concat(new String[] { key, ":", value }));
							continue;
						}
					}

					this.headerHtml.append(' ');
					this.headerHtml.append(key);
					this.headerHtml.append('=');
					char firstChar = value.charAt(0);
					if (firstChar == '@') {
						this.headerHtml.append(WebUtil.replaceParams(this.request, value.substring(1)));
					} else {
						String type = (String) itemObject.opt("type");
						if (type.startsWith("exp"))
							this.headerHtml.append(WebUtil.replaceParams(this.request, value));
						else
							this.headerHtml.append(StringUtil.quote(WebUtil.replaceParams(this.request, value)));
					}
				}
			}
		}
		String tagItems = gs("tagConfigs");
		if (!tagItems.isEmpty()) {
			this.headerHtml.append(' ');
			this.headerHtml.append(tagItems);
		}
	}
}