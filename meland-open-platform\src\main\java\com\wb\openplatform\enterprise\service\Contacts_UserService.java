package com.wb.openplatform.enterprise.service;

import javax.servlet.http.HttpServletRequest;

import org.json.JSONObject;

import com.wb.openplatform.enterprise.util.WeiXinParamesUtil;
import com.wb.openplatform.enterprise.util.WeiXinUtil;
import com.wb.util.LogUtil;

/**
 * 操作部门成员
 * 
 * <AUTHOR>
 *
 */
public class Contacts_UserService {

	private static String createUser_url = "https://qyapi.weixin.qq.com/cgi-bin/user/create?access_token=ACCESS_TOKEN";
	private static String getUser_url = "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=ACCESS_TOKEN&userid=USERID";
	private static String updateUser_url = "https://qyapi.weixin.qq.com/cgi-bin/user/update?access_token=ACCESS_TOKEN";
	private static String deleteUser_url = "https://qyapi.weixin.qq.com/cgi-bin/user/delete?access_token=ACCESS_TOKEN&userid=USERID";
	private static String batchdeleteUser_url = "https://qyapi.weixin.qq.com/cgi-bin/user/batchdelete?access_token=ACCESS_TOKEN";
	private static String getDepartmentUser_url = "https://qyapi.weixin.qq.com/cgi-bin/user/simplelist?access_token=ACCESS_TOKEN&department_id=DEPARTMENT_ID&fetch_child=FETCH_CHILD";
	private static String getDepartmentUserDetails_url = "https://qyapi.weixin.qq.com/cgi-bin/user/list?access_token=ACCESS_TOKEN&department_id=DEPARTMENT_ID&fetch_child=FETCH_CHILD";
	private static String invitationUser_url = "https://qyapi.weixin.qq.com/cgi-bin/batch/invite?access_token=ACCESS_TOKEN";

	// 1.创建成员
	public static JSONObject createUser(String user, HttpServletRequest request) {
		// 重新加载全局变量
		WeiXinParamesUtil.init();
		String accessToken = WeiXinUtil.getAccessToken(WeiXinParamesUtil.corpId, WeiXinParamesUtil.contactsSecret, "TXL").getToken();
		// 2.获取请求的url
		String User_url = createUser_url.replace("ACCESS_TOKEN", accessToken);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(User_url, "POST", user);

		// 4.错误消息处理
		if (null != jsonObject) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}

		return jsonObject;
	}

	// 2.获取成员
	public static JSONObject getUser(String userId, HttpServletRequest request) {
		// 重新加载全局变量
		WeiXinParamesUtil.init();
		String accessToken = WeiXinUtil.getAccessToken(WeiXinParamesUtil.corpId, WeiXinParamesUtil.contactsSecret, "TXL").getToken();
		// 1.获取请求的url
		String User_url = getUser_url.replace("ACCESS_TOKEN", accessToken).replace("USERID", userId);
		// 2.调用接口，发送请求，获取成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(User_url, "GET", null);

		// 3.错误消息处理
		if (null != jsonObject) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	// 3.更新成员
	public static JSONObject updateUser(String user, HttpServletRequest request) {
		// 重新加载全局变量
		WeiXinParamesUtil.init();
		String accessToken = WeiXinUtil.getAccessToken(WeiXinParamesUtil.corpId, WeiXinParamesUtil.contactsSecret, "TXL").getToken();
		// 2.获取请求的url
		String User_url = updateUser_url.replace("ACCESS_TOKEN", accessToken);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(User_url, "POST", user);

		// 4.错误消息处理
		if (null != jsonObject) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	// 4.删除成员
	public static JSONObject deleteUser(String userId, HttpServletRequest request) {
		// 重新加载全局变量
		WeiXinParamesUtil.init();
		String accessToken = WeiXinUtil.getAccessToken(WeiXinParamesUtil.corpId, WeiXinParamesUtil.contactsSecret, "TXL").getToken();
		// 1.获取请求的url
		String User_url = deleteUser_url.replace("ACCESS_TOKEN", accessToken).replace("USERID", userId);
		// 2.调用接口，发送请求，删除成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(User_url, "GET", null);

		// 3.错误消息处理
		if (null != jsonObject) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	// 5.批量删除成员
	public static JSONObject batchdeleteUser(String userIdList, HttpServletRequest request) {
		// 重新加载全局变量
		WeiXinParamesUtil.init();
		String accessToken = WeiXinUtil.getAccessToken(WeiXinParamesUtil.corpId, WeiXinParamesUtil.contactsSecret, "TXL").getToken();
		// 2.获取请求的url
		String User_url = batchdeleteUser_url.replace("ACCESS_TOKEN", accessToken);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(User_url, "POST", userIdList);

		// 4.错误消息处理
		if (null != jsonObject) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	// 6.获取部门成员
	public static JSONObject getDepartmentUser(String departmentId, String fetchChild, HttpServletRequest request) {
		// 重新加载全局变量
		WeiXinParamesUtil.init();
		String accessToken = WeiXinUtil.getAccessToken(WeiXinParamesUtil.corpId, WeiXinParamesUtil.contactsSecret, "TXL").getToken();

		// 1.获取请求的url
		String User_url = getDepartmentUser_url.replace("ACCESS_TOKEN", accessToken)
				.replace("DEPARTMENT_ID", departmentId).replace("FETCH_CHILD", fetchChild);
		// 2.调用接口，发送请求，获取部门成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(User_url, "GET", null);

		// 3.错误消息处理
		if (null != jsonObject) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	// 7.获取部门成员详情
	public static JSONObject getDepartmentUserDetails(String departmentId, String fetchChild,
			HttpServletRequest request) {
		// 重新加载全局变量
		WeiXinParamesUtil.init();
		String accessToken = WeiXinUtil.getAccessToken(WeiXinParamesUtil.corpId, WeiXinParamesUtil.contactsSecret, "TXL").getToken();
		// 1.获取请求的url
		String User_url = getDepartmentUserDetails_url.replace("ACCESS_TOKEN", accessToken)
				.replace("DEPARTMENT_ID", departmentId).replace("FETCH_CHILD", fetchChild);
		// 2.调用接口，发送请求，获取部门成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(User_url, "GET", null);

		// 3.错误消息处理
		if (null != jsonObject) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	// 8.邀请成员
	public static JSONObject invitationUser(String userIdList, HttpServletRequest request) {
		// 重新加载全局变量
		WeiXinParamesUtil.init();
		String accessToken = WeiXinUtil.getAccessToken(WeiXinParamesUtil.corpId, WeiXinParamesUtil.contactsSecret, "TXL").getToken();
		// 2.获取请求的url
		String User_url = invitationUser_url.replace("ACCESS_TOKEN", accessToken);
		// 3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(User_url, "POST", userIdList);

		// 4.错误消息处理
		if (null != jsonObject) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

}
