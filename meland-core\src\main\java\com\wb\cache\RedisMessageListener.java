package com.wb.cache;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import com.wb.common.KVBuffer;
import com.wb.common.ws.WebSocketMessageSender;
import com.wb.common.ws.WebSocketRedisConfig;
import com.wb.common.ws.WebSocketSessionManager;
import com.wb.rocketmq.expense.MessageConsume;
import com.wb.util.*;
import org.json.JSONArray;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import com.alibaba.fastjson.JSONObject;
import com.wb.common.Base;
import com.wb.common.Var;
import com.wb.common.XwlBuffer;

import java.io.IOException;
import java.util.Collection;

/**
 * Redis消息监听器
 */
public class RedisMessageListener implements MessageListener {

    /**
     * Redis模板
     */
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 最近一次错误时间
     */
    private long lastErrorTime = 0;

    /**
     * 连续错误次数
     */
    private int errorCount = 0;

    /**
     * 获取Redis模板
     *
     * @return Redis模板
     */
    public RedisTemplate<String, Object> getRedisTemplate() {
        return redisTemplate;
    }

    /**
     * 设置Redis模板
     *
     * @param redisTemplate Redis模板
     */
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 接收消息
     *
     * @param message message must not be {@literal null}.
     * @param pattern pattern matching the channel (if specified) - can be
     *                {@literal null}.
     */
    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            processMessage(message, pattern);
            // 成功处理消息，重置错误计数
            resetErrorStats();
        } catch (RedisConnectionFailureException e) {
            // 连接异常处理
            handleConnectionError(e, "处理Redis消息时连接异常");
        } catch (Exception e) {
            // 其他异常处理
            LogUtil.error("处理Redis消息时发生异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理消息的实际逻辑
     *
     * @param message 消息对象
     * @param pattern 模式匹配通道
     */
    private void processMessage(Message message, byte[] pattern) {
        byte[] channel = message.getChannel();
        String msgChannel = (String) redisTemplate.getKeySerializer().deserialize(channel);
        byte[] body = message.getBody();
        // 序列化获得的消息内容
        JSONObject msgBody = (JSONObject) redisTemplate.getValueSerializer().deserialize(body);

        if (null == Base.map || null == msgBody) {
            return;
        }
        // 处理其他类型的消息
        try {
            // 处理MQ重启任务
            if ("chat_mq".equals(msgChannel)) {
                handleMQRestartMessage(msgBody);
                return;
            }
            // 处理来自其他服务器的消息
            if (SysUtil.getServerId().equals(msgBody.getString("server"))) {
                return; // 同一台服务器不做处理
            }

            // 处理集群模式相关消息
            if (msgChannel.startsWith("cluster:")) {
                handleClusterMessage(msgChannel, msgBody);
                return;
            }

            // 处理WebSocket相关消息
            if (msgChannel.startsWith("websocket:")) {
                handleWebSocketMessage(msgChannel, msgBody);
                return;
            }

            // 处理熔断事件消息
            if (msgChannel.equals("circuit:breaker:events")) {
                handleCircuitBreakerEvent(msgBody);
                return;
            }

            // 处理熔断配置或限流配置更新消息
            if (msgChannel.equals("circuit:breaker:config:updated") || msgChannel.equals("rate:limit:config:updated")) {
                handleClusterMessage(msgChannel, msgBody);
                return;
            }

            // 处理限流配置确认消息
            if (msgChannel.equals("rate:limit:config:confirmed")) {
                handleRateLimitConfigConfirmation(msgBody);
                return;
            }

            // 处理缓存相关消息
            if (msgChannel.startsWith("cache:")) {
                processCacheMessage(msgChannel, msgBody);
                return;
            }

            // 处理模块相关通知信息
            if ("chat_xwl".equals(msgChannel) && null != XwlBuffer.buffer) {
                handleXwlMessage(msgBody);
                return;
            }
            if ("chat_role".equals(msgChannel) && null != XwlBuffer.roleBuffer) {
                handleRoleMessage(msgBody);
                return;
            }
            if ("chat_kv".equals(msgChannel) && null != KVBuffer.buffer) {
                handleKVMessage(msgBody);
                return;
            }
            if ("chat_var".equals(msgChannel) && null != Var.buffer) {
                handleVarMessage(msgBody);
                return;
            }
            if ("chat_reload".equals(msgChannel)) {
                handleReloadMessage(msgBody);
                return;
            }
        } catch (Exception e) {
            LogUtil.error("处理Redis消息异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理MQ重启消息
     * 
     * @param msgBody 消息内容
     */
    private void handleMQRestartMessage(JSONObject msgBody) {
        try {
            if ("restart".equals(msgBody.getString("type"))
                    && (SysUtil.getServerId().equals(msgBody.getString("server"))
                            || "ALL".equals(msgBody.getString("server")))) {
                LogUtil.info("收到RocketMQ重启指令，准备重启RocketMQ服务...");
                MessageConsume.shutdown();
                Thread.sleep(5000);
                MessageConsume.load();
                LogUtil.info("RocketMQ服务重启完成");
            }
        } catch (InterruptedException e) {
            LogUtil.error("定向重启MQ任务异常：" + e.getMessage());
        }
    }

    /**
     * 处理模块缓存消息
     * 
     * @param msgBody 消息内容
     */
    private void handleXwlMessage(JSONObject msgBody) {
        try {
            if ("add".equals(msgBody.getString("type"))) {
                String key = msgBody.getString("path");

                // 如果本地已有缓存，则跳过处理
                if (XwlBuffer.localCache.containsKey(key) || XwlBuffer.buffer.containsKey(key)) {
                    if (Var.debug) {
                        LogUtil.debug("跳过已存在的初始加载模块: " + key);
                    }
                    return;
                }

                try {
                    // 检查obj字段是否存在
                    if (!msgBody.containsKey("obj")) {
                        LogUtil.warn("模块缓存消息缺少obj字段: " + msgBody.toJSONString());
                        return;
                    }

                    // 获取obj字段值
                    Object objValue = msgBody.get("obj");
                    Object[] obj = null;

                    // 处理不同类型的obj字段
                    if (objValue instanceof com.alibaba.fastjson.JSONArray) {
                        // 正常数组处理
                        com.alibaba.fastjson.JSONArray jsonArray = (com.alibaba.fastjson.JSONArray) objValue;
                        obj = new Object[jsonArray.size()];
                        for (int i = 0; i < jsonArray.size(); i++) {
                            obj[i] = jsonArray.get(i);
                        }
                        XwlBuffer.buffer.put(key, obj);
                    } else if (objValue instanceof com.alibaba.fastjson.JSONObject) {
                        // 处理JSONObject类型，可能是FastJSON特殊序列化格式
                        com.alibaba.fastjson.JSONObject jsonObj = (com.alibaba.fastjson.JSONObject) objValue;

                        // 尝试处理带有@type和@value标记的FastJSON对象
                        if (jsonObj.containsKey("@type") && jsonObj.containsKey("@value")) {
                            Object valueObj = jsonObj.get("@value");
                            if (valueObj instanceof com.alibaba.fastjson.JSONArray) {
                                // 处理@value中的数组
                                com.alibaba.fastjson.JSONArray jsonArray = (com.alibaba.fastjson.JSONArray) valueObj;
                                obj = new Object[jsonArray.size()];
                                for (int i = 0; i < jsonArray.size(); i++) {
                                    obj[i] = jsonArray.get(i);
                                }
                                XwlBuffer.buffer.put(key, obj);
                                // 同时更新本地缓存
                                if (obj != null && obj.length > 0 && obj[0] != null) {
                                    org.json.JSONObject jsonObject = JsonUtil.getObject(obj[0].toString());
                                    XwlBuffer.localCache.put(key, jsonObject);
                                    if (Var.debug) {
                                        LogUtil.debug("已更新本地缓存: " + key);
                                    }
                                }
                                return;
                            }
                        }

                        // 如果无法按上述方式处理，记录详细信息并尝试直接使用
                        if (Var.debug) {
                            LogUtil.info("处理复杂JSONObject类型的obj字段: " + jsonObj.toJSONString());
                        }

                        // 尝试转换为Object数组
                        obj = new Object[2];
                        // 第一个元素存储JSON对象
                        obj[0] = jsonObj;
                        // 第二个元素尝试获取时间戳，如果没有则使用当前时间
                        obj[1] = System.currentTimeMillis();
                        XwlBuffer.buffer.put(key, obj);
                    } else if (objValue instanceof String) {
                        // 尝试将字符串解析为JSONArray
                        try {
                            com.alibaba.fastjson.JSONArray jsonArray = com.alibaba.fastjson.JSON
                                    .parseArray((String) objValue);
                            obj = new Object[jsonArray.size()];
                            for (int i = 0; i < jsonArray.size(); i++) {
                                obj[i] = jsonArray.get(i);
                            }
                            XwlBuffer.buffer.put(key, obj);
                        } catch (Exception e) {
                            // 如果解析为数组失败，尝试解析为对象
                            try {
                                com.alibaba.fastjson.JSONObject jsonObj = com.alibaba.fastjson.JSON
                                        .parseObject((String) objValue);
                                // 创建标准格式的对象数组
                                obj = new Object[2];
                                obj[0] = jsonObj;
                                obj[1] = System.currentTimeMillis();
                                XwlBuffer.buffer.put(key, obj);
                            } catch (Exception e2) {
                                LogUtil.error("obj字段既不是有效的JSON数组也不是有效的JSON对象: " + objValue, e2);
                            }
                        }
                    } else {
                        // 不支持的类型
                        LogUtil.warn("模块缓存消息obj字段类型不支持: " + objValue.getClass().getName() + ", 值: " + objValue);
                    }

                    // 更新本地缓存
                    if (obj != null && obj.length > 0 && obj[0] != null) {
                        org.json.JSONObject jsonObject = JsonUtil.getObject(obj[0].toString());
                        XwlBuffer.localCache.put(key, jsonObject);
                        if (Var.debug) {
                            LogUtil.debug("已更新本地缓存: " + key);
                        }
                    }
                } catch (Exception e) {
                    LogUtil.error("处理模块缓存消息的obj字段异常: " + e.getMessage(), e);
                }
            } else if ("del".equals(msgBody.getString("type"))) {
                String key = msgBody.getString("path");
                XwlBuffer.buffer.remove(key);
                // 同时从本地缓存中移除
                XwlBuffer.localCache.remove(key);
                if (Var.debug) {
                    LogUtil.debug("已从本地缓存移除: " + key);
                }
            }
        } catch (Exception e) {
            LogUtil.error("处理模块缓存消息异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理角色权限消息
     * 
     * @param msgBody 消息内容
     */
    private void handleRoleMessage(JSONObject msgBody) {
        try {
            if ("update".equals(msgBody.getString("type"))) {
                String module = msgBody.getString("module");
                JSONArray roleArr = DbUtil.query("select role_id from wb_role_module where module='" + module + "'");
                List<String> roleList = new ArrayList<>();
                for (int i = 0; i < roleArr.length(); i++) {
                    roleList.add(roleArr.getString(i));
                }
                XwlBuffer.roleBuffer.put(module, roleList);
            } else if ("set".equals(msgBody.getString("type"))) {
                List<String> roleList = XwlBuffer.roleBuffer.computeIfAbsent(msgBody.getString("key"),
                        k -> new ArrayList<>());
                boolean checked = msgBody.getBoolean("checked");
                String role = msgBody.getString("role");
                if (checked && !roleList.contains(role)) {
                    roleList.add(role);
                }
                if (!checked) {
                    roleList.remove(role);
                }
            } else if ("remove".equals(msgBody.getString("type"))) {
                // 遍历ConcurrentHashMap的每个条目
                for (Map.Entry<String, List<String>> entry : XwlBuffer.roleBuffer.entrySet()) {
                    // 获取当前键下的List
                    List<String> roleList = entry.getValue();
                    // 线程安全地从List中移除值
                    roleList.removeIf(item -> item.equals(msgBody.getString("role")));
                }
            } else if ("loadRoleID".equals(msgBody.getString("type"))) {
                // 缓存角色信息
                XwlBuffer.roleIDBuffer = new ConcurrentHashMap<>();
                JSONArray roleArr = DbUtil.queryAll("select role_id,role_name from wb_role");
                for (int i = 0; i < roleArr.length(); i++) {
                    org.json.JSONObject role = roleArr.getJSONObject(i);
                    XwlBuffer.roleIDBuffer.put(role.getString("role_id"), role.getString("role_name"));
                }
                if (Var.debug)
                    LogUtil.info("同步加载服务器[" + SysUtil.getServerId() + "]的角色信息");
            }
        } catch (Exception e) {
            LogUtil.error("处理角色权限消息异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理键值缓存消息
     * 
     * @param msgBody 消息内容
     */
    private void handleKVMessage(JSONObject msgBody) {
        try {
            if ("reload".equals(msgBody.getString("type"))) {
                String keyName = msgBody.getString("KEY_NAME");
                Integer type = msgBody.getInteger("KV_TYPE");
                if (Var.debug)
                    LogUtil.info("同步加载服务器[" + SysUtil.getServerId() + "]的键值[" + keyName + "-" + type + "]");
                Connection conn = DbUtil.getConnection();
                PreparedStatement st = null;
                ResultSet rs = null;
                ConcurrentHashMap<Object, String> map = new ConcurrentHashMap<Object, String>();
                try {
                    st = conn.prepareStatement(
                            "select a.K,a.V from WB_KEY a, WB_KEY_TREE b where a.KEY_ID=b.KEY_ID and b.KEY_NAME=? order by b.KEY_NAME");
                    st.setString(1, keyName);
                    rs = st.executeQuery();
                    while (rs.next()) {
                        String k = rs.getString("K");
                        map.put(type == 1 ? k : Integer.parseInt(k), rs.getString("V"));
                    }
                    if (map.isEmpty())
                        KVBuffer.buffer.remove(keyName);
                    else
                        KVBuffer.buffer.put(keyName, map);
                } finally {
                    DbUtil.close(rs);
                    DbUtil.close(st);
                    DbUtil.close(conn);
                }
            }
        } catch (Exception e) {
            LogUtil.error("处理键值缓存消息异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理变量消息
     * 
     * @param msgBody 消息内容
     */
    private void handleVarMessage(JSONObject msgBody) {
        try {
            if ("set".equals(msgBody.getString("type"))) {
                String path = msgBody.getString("path"), name = msgBody.getString("name"),
                        value = msgBody.getString("value");
                String key = (null == path ? name : (path + '.' + name));
                try {
                    Var.buffer.put(key, value);
                    Var.loadBasicVars();
                } catch (Exception e) {
                    LogUtil.warn(
                            "同步设置[" + SysUtil.getServerId() + "]变量[" + key + "]值[" + value + "]异常：" + e.getMessage());
                }
            } else if ("del".equals(msgBody.getString("type"))) {
                // 同步删除变量
                String path = msgBody.getString("path");
                try {
                    JSONArray names = new JSONArray(msgBody.getString("names"));
                    int j = names.length();
                    for (int i = 0; i < j; i++) {
                        Var.buffer.remove(StringUtil.concat(path, ".", names.optString(i)));
                    }
                } catch (Exception e) {
                    LogUtil.warn("同步删除[" + SysUtil.getServerId() + "]变量[" + path + "]异常：" + e.getMessage());
                }
            } else if ("setFolder".equals(msgBody.getString("type"))) {
                // 同步设置文件夹
                String type = msgBody.getString("varType");
                String path = msgBody.getString("path"), name = msgBody.getString("name");
                try {
                    if ("delete".equals(type)) {
                        path = path + '.';
                        Set<Entry<String, Object>> es = Var.buffer.entrySet();
                        for (Entry<String, Object> e : es) {
                            String key = (String) e.getKey();
                            if (key.startsWith(path)) {
                                Var.buffer.remove(key);
                            }
                        }
                    } else if ("update".equals(type)) {
                        String newName = msgBody.getString("newName");
                        String newPath = StringUtil.concat(path, ".", newName, ".");
                        path = StringUtil.concat(path, ".", name, ".");
                        Set<Entry<String, Object>> es = Var.buffer.entrySet();
                        int oldPathLen = path.length();
                        for (Entry<String, Object> e : es) {
                            String key = (String) e.getKey();
                            if (key.startsWith(path)) {
                                Var.buffer.remove(key);
                                Var.buffer.put(newPath + key.substring(oldPathLen), e.getValue());
                            }
                        }
                    }
                } catch (Exception e) {
                    LogUtil.warn("同步设置[" + SysUtil.getServerId() + "]变量目录[" + path + "]异常：" + e.getMessage());
                }
            }
        } catch (Exception e) {
            LogUtil.error("处理变量消息异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理系统热加载消息
     * 
     * @param msgBody 消息内容
     */
    private void handleReloadMessage(JSONObject msgBody) {
        try {
            if ("reload".equals(msgBody.getString("type"))) {
                SysUtil.reload(msgBody.getInteger("loadType"));
                LogUtil.info("同步重新热加载系统[" + SysUtil.getServerId() + "]");
            }
        } catch (Exception e) {
            LogUtil.error("处理系统热加载消息异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理连接错误
     * 
     * @param e       异常对象
     * @param message 错误消息
     */
    private void handleConnectionError(Exception e, String message) {
        long now = System.currentTimeMillis();
        // 如果距离上次错误超过30秒，重置计数
        if (now - lastErrorTime > 30000) {
            errorCount = 0;
        }

        // 更新错误时间和计数
        lastErrorTime = now;
        errorCount++;

        // 根据错误次数记录不同级别的日志
        if (errorCount <= 3) {
            LogUtil.warn(message + ": " + e.getMessage() + " (第" + errorCount + "次)");
        } else if (errorCount % 10 == 0) {
            // 每10次记录一次详细错误
            LogUtil.error(message + ": " + e.getMessage() + " (连续" + errorCount + "次)", e);
        }
    }

    /**
     * 重置错误统计
     */
    private void resetErrorStats() {
        if (errorCount > 0) {
            errorCount = 0;
            lastErrorTime = 0;
        }
    }

    /**
     * 处理缓存相关消息
     * 
     * @param msgChannel 消息通道
     * @param msgBody    消息内容
     * @return 是否已处理
     */
    private void processCacheMessage(String msgChannel, JSONObject msgBody) {
        if ("cache:invalidate".equals(msgChannel)) {
            String key = msgBody.getString("key");
            if (!StringUtil.isEmpty(key)) {
                // 从本地缓存删除
                if (Base.map != null) {
                    Base.map.handleCacheInvalidation(key);
                    if (Var.debug) {
                        LogUtil.info("处理缓存失效消息: " + key);
                    }
                }
            }
        } else if ("cache:invalidate:all".equals(msgChannel)) {
            if ("clear_all".equals(msgBody.getString("action"))) {
                // 清空本地缓存
                if (Base.map != null) {
                    Base.map.handleClearAllCache();
                    if (Var.debug) {
                        LogUtil.info("处理清空所有缓存消息");
                    }
                }
            }
        } else if ("cache:update".equals(msgChannel)) {
            String key = msgBody.getString("key");
            String valueJson = msgBody.getString("value");
            if (!StringUtil.isEmpty(key) && !StringUtil.isEmpty(valueJson)) {
                // 更新本地缓存
                if (Base.map != null) {
                    Base.map.handleCacheUpdate(key, valueJson);
                    if (Var.debug) {
                        LogUtil.info("处理缓存更新消息: " + key);
                    }
                }
            }
        }
    }

    /**
     * 处理集群相关消息
     *
     * @param channel 频道名称
     * @param body    消息内容
     */
    private void handleClusterMessage(String channel, JSONObject message) {
        try {
            if (channel.equals("cluster:mode")) {
                String action = message.getString("action");
                if ("enable".equals(action)) {
                    RedisConfiguration.enableClusterMode();
                    if (Var.debug)
                        LogUtil.info("收到集群模式启用消息，已启用Redis集群兼容模式");
                } else if ("disable".equals(action)) {
                    RedisConfiguration.disableClusterMode();
                    if (Var.debug)
                        LogUtil.info("收到集群模式禁用消息，已禁用Redis集群兼容模式");
                }
            } else if (channel.equals("rate:limit:config:updated")) {
                // 处理限流配置更新消息
                handleRateLimitConfigUpdated(message);
            } else if (channel.equals("circuit:breaker:config:updated")) {
                // 处理熔断配置更新消息
                handleCircuitBreakerConfigUpdated(message);
            } else if (channel.equals("circuit:breaker:events")) {
                try {
                    handleCircuitBreakerEvent(message);
                } catch (Exception e) {
                    LogUtil.error("处理熔断事件异常: " + e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            LogUtil.error("处理集群消息异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理限流配置更新通知
     * 
     * @param message 消息内容
     */
    private void handleRateLimitConfigUpdated(JSONObject message) {
        try {
            if (Var.debug)
                LogUtil.info("收到限流配置更新通知");

            // 获取消息属性
            String action = message.getString("action");
            String serverFrom = message.getString("server");
            long timestamp = message.getLongValue("timestamp");

            // 检查消息是否包含特定更新信息
            if ("update".equals(action) && message.containsKey("target") && message.containsKey("qpsLimit")) {
                // 增量更新处理
                String target = message.getString("target");
                int qpsLimit = message.getIntValue("qpsLimit");

                // 直接更新本地配置
                com.wb.circuit.XwlRateLimiterService.getInstance().updateLocalConfig(target, qpsLimit);

                if (Var.debug) {
                    LogUtil.info(
                            StringUtil.format("服务器[{0}]更新了限流配置: 模块[{1}]限流值设为{2}QPS", serverFrom, target, qpsLimit));
                }
            } else if ("delete".equals(action) && message.containsKey("target")) {
                // 删除特定配置
                String target = message.getString("target");
                com.wb.circuit.XwlRateLimiterService.getInstance().removeLocalConfig(target);

                if (Var.debug) {
                    LogUtil.info(StringUtil.format("服务器[{0}]删除了模块[{1}]的限流配置", serverFrom, target));
                }
            } else {
                // 全量更新 - 如果消息中没有足够的增量更新信息
                com.wb.circuit.XwlRateLimiterService.getInstance().loadRateLimitConfigurations();
                if (Var.debug)
                    LogUtil.info("已重新加载所有限流配置");
            }
        } catch (Exception e) {
            // 异常处理，使用全量加载兜底
            LogUtil.error("处理限流配置更新异常: " + e.getMessage() + "，将进行全量配置重载", e);
            try {
                com.wb.circuit.XwlRateLimiterService.getInstance().loadRateLimitConfigurations();
            } catch (Exception ex) {
                LogUtil.error("重新加载限流配置失败: " + ex.getMessage(), ex);
            }
        }
    }

    /**
     * 处理熔断配置更新通知
     * 
     * @param message 消息内容
     */
    private void handleCircuitBreakerConfigUpdated(JSONObject message) {
        try {
            if (Var.debug)
                LogUtil.info("收到熔断配置更新通知");

            String action = message.getString("action");
            String serverFrom = message.getString("server");

            // 检查是否包含增量更新信息
            if ("update_single".equals(action) && message.containsKey("target") && message.containsKey("config")) {
                // 增量更新单个配置
                String target = message.getString("target");
                com.alibaba.fastjson.JSONObject configJson = message.getJSONObject("config");

                // 转换为org.json.JSONObject
                String configString = configJson.toString();
                org.json.JSONObject jsonConfig = new org.json.JSONObject(configString);

                // 更新单个配置
                com.wb.circuit.XwlCircuitBreakerService.getInstance().updateSingleConfigurationFromJson(target,
                        jsonConfig);

                if (Var.debug) {
                    LogUtil.info(StringUtil.format("服务器[{0}]更新了熔断配置: 模块[{1}]", serverFrom, target));
                }
            } else if ("remove".equals(action) && message.containsKey("target")) {
                // 删除单个配置
                String target = message.getString("target");
                com.wb.circuit.XwlCircuitBreakerService.getInstance().removeSingleConfiguration(target);

                if (Var.debug) {
                    LogUtil.info(StringUtil.format("服务器[{0}]删除了模块[{1}]的熔断配置", serverFrom, target));
                }
            } else {
                // 全量更新 - 如果消息中没有足够的增量更新信息
                com.wb.circuit.XwlCircuitBreakerService.getInstance().loadConfigurations();
                if (Var.debug)
                    LogUtil.info("已重新加载所有熔断配置");
            }
        } catch (Exception e) {
            // 异常处理，使用全量加载兜底
            LogUtil.error("处理熔断配置更新异常: " + e.getMessage() + "，将进行全量配置重载", e);
            try {
                com.wb.circuit.XwlCircuitBreakerService.getInstance().loadConfigurations();
            } catch (Exception ex) {
                LogUtil.error("重新加载熔断配置失败: " + ex.getMessage(), ex);
            }
        }
    }

    /**
     * 处理熔断事件消息
     *
     * @param eventData 事件数据
     */
    private void handleCircuitBreakerEvent(JSONObject eventData) {
        try {
            String xwlPath = eventData.getString("xwlPath");
            String fromState = eventData.getString("fromState");
            String toState = eventData.getString("toState");
            String serverFrom = eventData.getString("server");

            // 记录熔断事件
            if ("OPEN".equals(toState)) {
                String message = StringUtil.format("收到服务器[{0}]熔断事件通知：模块[{1}]已触发熔断，由[{2}]状态转为[{3}]状态", serverFrom,
                        xwlPath, fromState, toState);
                if (Var.debug)
                    LogUtil.warn(message);

                // 可在此处添加告警通知逻辑，如邮件、短信等
                notifyCircuitBreakerEvent(xwlPath, fromState, toState);
            } else if ("CLOSED".equals(toState)) {
                String message = StringUtil.format("收到服务器[{0}]熔断事件通知：模块[{1}]已恢复正常，由[{2}]状态转为[{3}]状态", serverFrom,
                        xwlPath, fromState, toState);
                if (Var.debug)
                    LogUtil.info(message);
            } else {
                String message = StringUtil.format("收到服务器[{0}]熔断事件通知：模块[{1}]状态变更，由[{2}]状态转为[{3}]状态", serverFrom,
                        xwlPath, fromState, toState);
                if (Var.debug)
                    LogUtil.info(message);
            }
        } catch (Exception e) {
            LogUtil.error("处理熔断事件异常: " + e.getMessage(), e);
        }
    }

    /**
     * 通知熔断事件 此方法可用于发送告警通知
     */
    private void notifyCircuitBreakerEvent(String xwlPath, String fromState, String toState) {
        try {
            // 这里可以添加额外的通知逻辑，如邮件、短信等
            // 例如：AlertUtil.sendAlert("模块[" + xwlPath + "]已触发熔断，请检查服务状态");

            // 如果需要持久化存储熔断事件，可以在这里添加
            // 例如：保存到数据库中以便后续查询
        } catch (Exception e) {
            LogUtil.error("发送熔断告警通知失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理WebSocket相关消息
     * 
     * @param msgChannel 消息通道
     * @param msgBody    消息内容
     */
    private void handleWebSocketMessage(String msgChannel, JSONObject msgBody) {
        try {
            // 忽略自己发出的消息
            String sourceNodeId = msgBody.getString("sourceNodeId");
            if (WebSocketSessionManager.getInstance().getNodeId().equals(sourceNodeId)) {
                return;
            }

            // 处理广播消息
            if (msgChannel.equals(WebSocketRedisConfig.WS_BROADCAST_CHANNEL)) {
                String message = msgBody.getString("message");
                boolean isJson = msgBody.getBooleanValue("isJson");
                String targetName = msgBody.getString("targetName");

                // 找到所有本地会话并发送消息
                Collection<WebSocketSession> localSessions = WebSocketSessionManager.getInstance()
                        .getAllLocalSessions();
                for (WebSocketSession session : localSessions) {
                    if (session.isOpen()) {
                        try {
                            // 如果指定了targetName，则需要过滤
                            if (targetName != null && !targetName.isEmpty()) {
                                String sessionId = session.getId();
                                Map<Object, Object> sessionInfo = WebSocketSessionManager.getInstance()
                                        .getSessionInfo(sessionId);
                                if (sessionInfo != null) {
                                    String sessionName = (String) sessionInfo.get("name");
                                    // 如果会话名称不匹配，则跳过
                                    if (!targetName.equals(sessionName)) {
                                        continue;
                                    }
                                }
                            }
                            session.sendMessage(new TextMessage(message));
                        } catch (IOException e) {
                            LogUtil.error("发送WebSocket广播消息异常: " + e.getMessage());
                        }
                    }
                }
                return;
            }

            // 处理用户消息
            if (msgChannel.startsWith(WebSocketRedisConfig.WS_USER_CHANNEL_PREFIX)) {
                String userId = msgChannel.substring(WebSocketRedisConfig.WS_USER_CHANNEL_PREFIX.length());
                String message = msgBody.getString("message");
                boolean isJson = msgBody.getBooleanValue("isJson");
                String targetName = msgBody.getString("targetName");

                // 获取用户所有会话ID
                Set<String> sessionIds = WebSocketSessionManager.getInstance().getSessionIdsByUserId(userId);
                for (String sessionId : sessionIds) {
                    WebSocketSession session = WebSocketSessionManager.getInstance().getSession(sessionId);
                    if (session != null && session.isOpen()) {
                        // 如果指定了targetName，则需要过滤
                        if (targetName != null && !targetName.isEmpty()) {
                            Map<Object, Object> sessionInfo = WebSocketSessionManager.getInstance()
                                    .getSessionInfo(sessionId);
                            if (sessionInfo != null) {
                                String sessionName = (String) sessionInfo.get("name");
                                // 如果会话名称不匹配，则跳过
                                if (!targetName.equals(sessionName)) {
                                    continue;
                                }
                            }
                        }

                        try {
                            session.sendMessage(new TextMessage(message));
                        } catch (IOException e) {
                            LogUtil.error("发送WebSocket用户消息异常: " + e.getMessage());
                        }
                    }
                }
                return;
            }
        } catch (Exception e) {
            LogUtil.error("处理WebSocket消息异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理限流配置确认消息
     * 
     * @param message 消息内容
     */
    private void handleRateLimitConfigConfirmation(JSONObject message) {
        try {
            // 配置发送方服务器ID
            String serverFrom = message.getString("server");
            // 配置版本
            long configVersion = message.getLongValue("configVersion");
            // 目标模块
            String target = message.getString("target");
            // QPS限制值
            int qpsLimit = message.getIntValue("qpsLimit");

            // 获取本地存储的配置版本
            com.wb.circuit.XwlRateLimiterService service = com.wb.circuit.XwlRateLimiterService.getInstance();
            long localVersion = 0;
            try {
                String versionStr = (String) Base.map.getValue("rate:limit:config:version");
                if (!StringUtil.isEmpty(versionStr)) {
                    localVersion = Long.parseLong(versionStr);
                }
            } catch (Exception e) {
                LogUtil.warn("获取本地限流配置版本失败: " + e.getMessage());
            }

            // 如果收到的配置版本更高，但本地配置没有更新，则进行更新
            if (configVersion > localVersion) {
                LogUtil.info(StringUtil.format("配置版本不一致，收到版本{0}高于本地版本{1}，同步更新配置", configVersion, localVersion));

                // 重新加载所有配置
                service.loadRateLimitConfigurations();
                // 更新版本号
                Base.map.setValue("rate:limit:config:version", String.valueOf(configVersion));

                LogUtil.info(StringUtil.format("配置已同步至最新版本{0}", configVersion));
            } else if (configVersion == localVersion) {
                // 检查单个配置是否一致
                Integer localQpsLimit = service.getAllRateLimitConfigurations().get(target);
                // 如果本地配置与接收到的配置不一致，则更新本地配置
                if ((localQpsLimit == null && qpsLimit > 0) || (localQpsLimit != null && localQpsLimit != qpsLimit)) {

                    LogUtil.info(StringUtil.format("同一版本{0}下配置不一致，模块[{1}]本地QPS={2}，服务器[{3}]QPS={4}，同步更新", configVersion,
                            target, localQpsLimit, serverFrom, qpsLimit));

                    // 更新本地配置
                    service.updateLocalConfig(target, qpsLimit);
                }
            }

            // 记录详细调试信息
            if (Var.debug) {
                LogUtil.debug(StringUtil.format("收到来自服务器[{0}]的限流配置确认消息: 模块[{1}], QPS={2}, 版本={3}, 本地版本={4}", serverFrom,
                        target, qpsLimit, configVersion, localVersion));
            }
        } catch (Exception e) {
            LogUtil.error("处理限流配置确认消息异常: " + e.getMessage(), e);
        }
    }
}

/**
 * 定义一个能够获取RedisExceptionHandler的接口
 */
interface RedisExceptionHandlerAware {
    RedisExceptionHandler getRedisExceptionHandler();
}
