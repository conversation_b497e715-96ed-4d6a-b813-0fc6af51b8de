package com.wb.flow;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.json.JSONObject;

import com.wb.common.Base;
import com.wb.interact.ResourceManager;
import com.wb.util.DbUtil;
import com.wb.util.JsonUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;
import com.wb.util.WbUtil;
import com.wb.util.WebUtil;

public class Flow {
	public static FlowObject start(String filename, JSONObject params, Connection conn) throws Exception {
		FlowObject flowObject = getFlowObject(filename, params, null);
		flowObject.forward();
		flowObject.insert(conn);
		return flowObject;
	}

	public static FlowObject start(String filename) throws Exception {
		return start(filename, null, null);
	}

	public static void start(HttpServletRequest request, HttpServletResponse response) throws Exception {
		doOpen(request, response, 0);
	}

	public static void open(HttpServletRequest request, HttpServletResponse response) throws Exception {
		doOpen(request, response, 1);
	}

	public static void view(HttpServletRequest request, HttpServletResponse response) throws Exception {
		doOpen(request, response, 2);
	}

	private static void doOpen(HttpServletRequest request, HttpServletResponse response, int mode) throws Exception {
		JSONObject params = JsonUtil.getObject(request.getParameter("params"));
		FlowObject flowObject;
		if (mode == 0)
			flowObject = getFlowObject(request.getParameter("filename"), params, request);
		else {
			flowObject = new FlowObject(getFlowData(request.getParameter("flowId"), request), params, request, null,
					false, null);
		}
		if (mode != 0)
			flowObject.checkActiveNode(request.getParameter("nodeName"));
		JSONObject dialog;
		if (mode == 2) {
			dialog = flowObject.getViewDialog();
			if (params.has("_viewDone"))
				flowObject.viewDone();
			if (dialog == null)
				throw new NullPointerException("流程当前节点没有配置查看对话框属性。");
		} else {
			dialog = flowObject.getDialog();
			if (dialog == null)
				throw new NullPointerException("流程当前节点没有配置处理对话框属性。");
		}
		WebUtil.send(response, dialog);
	}

	/**
	 * 执行流程动作
	 * 预定义的动作类型（actionName）包括：
	 * pass：通过动作，使当前节点流转到连线所连接的目标结点。流转时必须满足连线所设置的条件。
	 * reject：退回动作，弹出退回对话框，由用户选择退回到指定的节点。
	 * beforeSign：前加签动作，在当前节点之前动态加入一个新的节点，并指定处理人员。
	 * plusSign：并加签动作，在当前节点动态添加新的处理人员。
	 * afterSign：后加签动作，在当前节点之后动态加入一个新的节点，并指定处理人员。
	 * turn：转办动作，把当前节点的处理人员中的本人替换为另一个用户。
	 * cancel：取消动作，关闭对话框，不执行任何动作。
	 * 
	 * @param actionName 动作类型
	 */
	private static void doAction(HttpServletRequest request, HttpServletResponse response, String actionName)
			throws Exception {
		JSONObject flowParams = new JSONObject(WebUtil.fetch(request, "xFlowParams"));
		JSONObject extraParams = JsonUtil.getObject(WebUtil.fetch(request, "params"));
		Object isSendObj = JsonUtil.opt(flowParams,"isSend");

		Object approvalOpinionObj = JsonUtil.opt(flowParams,"approval_opinion");

		String isSend = null,approvalOpinion = null ;
		if(isSendObj != null){
			isSend = isSendObj.toString() ;

		}
		if(approvalOpinionObj != null){
			approvalOpinion = approvalOpinionObj.toString() ;

		}
		JSONObject result = null;
		JSONObject userData = flowParams.optJSONObject("userData");

		String flowId = flowParams.optString("flowId");
		String newNodeName = flowParams.optString("newNodeName");
		String passName = flowParams.optString("passName");
		String nodeTitle = flowParams.optString("nodeTitle");
		String passUsers = flowParams.optString("passUsers");

		boolean isStart = StringUtil.isEmpty(flowId);
		FlowObject flowObject;
		JSONObject paramsObject;
		if (isStart) {
			String filename = flowParams.optString("filename");
			if (StringUtil.isEmpty(filename))
				throw new RuntimeException("Neither filename nor flowId is specified.");
			flowObject = getFlowObject(filename, extraParams, request);
			paramsObject = flowObject.getParams();
			String module = flowObject.getFlowAttribute("beforeModule");
			if ("draft".equals(actionName)){
				flowObject.draft();
			}else{
				if (!StringUtil.isEmpty(module))
					executeModule(module, request, paramsObject, false);
				flowObject.forward(approvalOpinion);
			}

		} else {
			flowObject = new FlowObject(getFlowData(flowId, request), extraParams, request, null, false, null);
			flowObject.checkActiveNode(flowParams.getString("nodeName"));
			paramsObject = flowObject.getParams();
			String module = flowObject.getFlowAttribute("beforeModule");
			if (!StringUtil.isEmpty(module))
				executeModule(module, request, paramsObject, false);
			if ("pass".equals(actionName))
				flowObject.forward(approvalOpinion);
			else if ("reject".equals(actionName))
				flowObject.reject(flowParams.getString("rejectTo"),approvalOpinion);
			else if ("beforeSign".equals(actionName))
				flowObject.beforeSign(newNodeName, passName, nodeTitle, passUsers, userData);
			else if ("afterSign".equals(actionName))
				flowObject.afterSign(newNodeName, passName, nodeTitle, passUsers, userData);
			else if ("plusSign".equals(actionName))
				flowObject.plusSign(userData);
			else if ("turn".equals(actionName))
				flowObject.turn(userData,approvalOpinion);
			else if ("draft".equals(actionName))
				flowObject.draft();
		}
		//流程前置模块
		String module = flowParams.optString("module");
		if (!StringUtil.isEmpty(module))
			executeModule(module, request, paramsObject, false);
		//后置模块
		module = flowObject.getFlowAttribute("afterModule");
		if (!StringUtil.isEmpty(module))
			executeModule(module, request, paramsObject, false);
		if (isStart) {
			flowObject.insert();
		}else {
			if("draft".equals(actionName)){
				result = flowObject.updateDraft();
			}else{
				result = flowObject.update();
			}

		}
		//最后模块
		module = flowObject.getFlowAttribute("finalModule");
		if (!StringUtil.isEmpty(module)) {
			executeModule(module, request, paramsObject, false);
		}
		if(!"no".equals(isSend)){
			if (WebUtil.jsonResponse(request))
				WebUtil.send(response, result, true);
			else
				WebUtil.send(response, result);
		}
	}

	public static void pass(HttpServletRequest request, HttpServletResponse response) throws Exception {
		doAction(request, response, "pass");
	}

	public static void reject(HttpServletRequest request, HttpServletResponse response) throws Exception {
		doAction(request, response, "reject");
	}

	public static void beforeSign(HttpServletRequest request, HttpServletResponse response) throws Exception {
		doAction(request, response, "beforeSign");
	}

	public static void afterSign(HttpServletRequest request, HttpServletResponse response) throws Exception {
		doAction(request, response, "afterSign");
	}

	public static void plusSign(HttpServletRequest request, HttpServletResponse response) throws Exception {
		doAction(request, response, "plusSign");
	}

	public static void turn(HttpServletRequest request, HttpServletResponse response) throws Exception {
		doAction(request, response, "turn");
	}

	public static void draft(HttpServletRequest request, HttpServletResponse response) throws Exception {
		doAction(request, response, "draft");
	}

	private static JSONObject getFlowData(String flowId, HttpServletRequest request) throws Exception {
		Connection conn = null;
		PreparedStatement st = null;
		ResultSet rs = null;
		String result;
		try {
			conn = DbUtil.getConnection(request);
			st = conn.prepareStatement("select FLOW_DATA from WB_FLOW where FLOW_ID=?");
			st.setString(1, flowId);
			rs = st.executeQuery();
			if (!rs.next())
				throw new IllegalArgumentException("流程“" + flowId + "”不存在。");
			result = (String) DbUtil.getObject(rs, 1, 2011);
		} finally {
			DbUtil.close(rs);
			DbUtil.close(st);
		}
		return new JSONObject(result);
	}

	private static FlowObject getFlowObject(String filename, JSONObject params, HttpServletRequest request)
			throws Exception {
		File file = new File(ResourceManager.basePath, filename);

		if (!file.exists())
			file = new File(Base.path, filename);
		String flowId;
		if (request == null)
			flowId = null;
		else
			flowId = WebUtil.fetch(request, "xFlowId");
		if (StringUtil.isEmpty(flowId))
			flowId = SysUtil.getId();
		return new FlowObject(ResourceManager.getExecuteObject(request, file), params, request, file.getName(), true,
				flowId);
	}

	public static String executeModule(String url, HttpServletRequest request, JSONObject params, boolean isInvoke)
			throws Exception {
		if (request == null) {
			return WbUtil.run(url, params, isInvoke);
		}
		if (WbUtil.canAccess(request, url)) {
			request.setAttribute("sys.params", params);
			return WbUtil.run(url, params, request, isInvoke);
		}
		SysUtil.accessDenied(request, url);
		return null;
	}
}