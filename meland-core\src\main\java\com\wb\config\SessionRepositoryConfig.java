package com.wb.config;

import com.wb.common.Base;
import com.wb.cache.RedisExceptionHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.session.FindByIndexNameSessionRepository;
import org.springframework.session.Session;
import org.springframework.session.data.redis.RedisIndexedSessionRepository;
import org.springframework.web.context.ContextLoader;

import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

/**
 * 会话存储库配置
 * 处理Spring Session的会话管理和索引键过期设置
 */
@Configuration
@EnableAsync
public class SessionRepositoryConfig {

    private static final Logger log = LoggerFactory.getLogger(SessionRepositoryConfig.class);
    private static final int MAX_KEYS = 5000; // 每次最多处理5000个键，避免长时间占用资源
    private static final String SESSION_TASK_EXECUTOR = "sessionTaskExecutor";

    /**
     * 创建会话管理专用线程池
     */
    @Bean(name = SESSION_TASK_EXECUTOR)
    public Executor sessionTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(4);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("session-task-");
        executor.setRejectedExecutionHandler((r, e) -> 
            log.warn("会话任务线程池已满，任务被拒绝执行"));
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(10);
        executor.initialize();
        return executor;
    }

    /**
     * 创建Redis消息监听器容器
     * 用于异步处理Redis键空间事件
     */
    @Bean
    public RedisMessageListenerContainer redisMessageListenerContainer() {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();

        try {
            if (Base.map != null && Base.map.getRedisTemplate() != null) {
                container.setConnectionFactory(Base.map.getRedisTemplate().getConnectionFactory());
                
                // 设置恢复间隔，加快重连速度
                container.setRecoveryInterval(3000);
            } else {
                log.warn("Base.map尚未初始化，无法设置Redis连接工厂");
            }
        } catch (Exception e) {
            log.error("创建Redis消息监听器容器时出错", e);
        }

        container.setTaskExecutor(sessionIndexListenerExecutor());
        return container;
    }

    /**
     * 创建任务执行器
     */
    @Bean
    public Executor sessionIndexListenerExecutor() {
        return Executors.newSingleThreadExecutor(r -> {
            Thread thread = new Thread(r, "session-index-listener");
            thread.setDaemon(true);
            return thread;
        });
    }

    /**
     * 定期设置索引键过期时间的任务
     * 每隔1小时执行一次，降低执行频率减轻Redis负担
     */
    @Scheduled(cron = "0 0 1 * * ?")
    @Async(SESSION_TASK_EXECUTOR)
    public void setExpirationForExistingIndices() {
        try {
            // 检查Base.map是否已初始化
            if (Base.map == null) {
                log.warn("Base.map尚未初始化，跳过会话索引过期时间设置");
                return;
            }

            // 尝试获取分布式锁，避免集群环境中重复执行
            String lockKey = "session_index_expire_lock";
            boolean locked = false;
            
            try {
                locked = Base.map.tryLock(lockKey, 1000, 600000); // 等待1秒，锁定10分钟
            } catch (Exception e) {
                log.error("获取分布式锁时出错", e);
                return;
            }

            if (!locked) {
                log.info("另一个节点正在执行过期时间设置任务，本节点跳过");
                return;
            }

            try {
                log.info("执行定期设置会话索引键过期时间任务...");
                List<String> keys = null;
                
                try {
                    keys = Base.map.scan("spring:session:index:*");
                } catch (Exception e) {
                    log.error("扫描会话索引键时出错", e);
                    return;
                }
                
                if (keys != null && !keys.isEmpty()) {
                    int count = 0;
                    int processedCount = 0;
                    boolean interrupted = false;

                    for (String key : keys) {
                        if (Thread.currentThread().isInterrupted()) {
                            log.warn("任务执行被中断，停止处理");
                            interrupted = true;
                            break;
                        }
                        
                        if (processedCount >= MAX_KEYS) {
                            log.info("已达到单次处理上限({}个键)，剩余键将在下次执行时处理", MAX_KEYS);
                            break;
                        }

                        processedCount++;

                        try {
                            // 检查索引键是否已有过期时间
                            if (Base.map == null) {
                                log.warn("处理过程中Base.map变为null，停止处理");
                                break;
                            }
                            
                            Long ttl = Base.map.getRedisTemplate().getExpire(key, TimeUnit.SECONDS);
                            if (ttl != null && ttl > 0) {
                                // 已有过期时间，跳过处理
                                if (processedCount % 100 == 0) {
                                    log.debug("索引键 {} 已有过期时间 {} 秒，跳过处理", key, ttl);
                                }
                                continue;
                            }

                            // 只处理没有过期时间的索引键
                            if (Base.map.expire(key, 40, TimeUnit.MINUTES)) {
                                count++;
                                if (count % 100 == 0) {
                                    log.debug("已为索引键 {} 设置40分钟过期时间", key);
                                }
                            } else {
                                log.warn("无法为索引键 {} 设置过期时间", key);
                            }

                            // 每处理100个键休眠20毫秒，减轻Redis负担
                            if (processedCount % 100 == 0) {
                                try {
                                    TimeUnit.MILLISECONDS.sleep(20);
                                } catch (InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                    log.warn("任务执行被中断，停止处理");
                                    interrupted = true;
                                    break;
                                }
                            }
                        } catch (Exception e) {
                            log.warn("处理键 {} 时发生错误", key, e);
                        }
                    }
                    
                    String statusMsg = interrupted ? "（因中断提前结束）" : "";
                    log.info("共处理 {} 个会话索引键，为其中 {} 个键设置了过期时间{}", processedCount, count, statusMsg);
                } else {
                    log.info("未发现需要设置过期时间的会话索引键");
                }
            } finally {
                try {
                    if (Base.map != null) {
                        Base.map.unLock(lockKey); // 释放锁
                        log.debug("已释放分布式锁: {}", lockKey);
                    }
                } catch (Exception e) {
                    log.error("释放分布式锁时出错: {}", lockKey, e);
                }
            }
        } catch (Exception e) {
            log.error("定期设置会话索引键过期时间任务异常", e);
        }
    }

    /**
     * 更新指定用户的会话索引过期时间
     * 该方法可以被SessionCreatedEventListener调用，保持索引键与会话同步过期
     * 当索引不存在时会自动创建索引
     *
     * @param userid  用户ID
     * @param session 会话对象
     */
    public static void updatePrincipalIndex(String userid, Session session) {
        if (Base.map == null) {
            log.warn("Base.map未初始化，无法更新索引过期时间");
            return;
        }
        
        if (userid == null || session == null) {
            log.warn("用户ID或会话对象为null，无法更新索引");
            return;
        }

        try {
            long expirationSeconds = session.getMaxInactiveInterval().getSeconds();
            // 创建索引键
            String indexKey = "spring:session:index:" +
                    FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME + ":" + userid;

            // 获取会话ID
            String sessionId = session.getId();

            // 检查索引是否存在并创建
            try {
                // 使用与Spring Session兼容的方式操作Redis
                if (!Base.map.hasKey(indexKey)) {
                    log.info("索引键 {} 不存在，正在创建索引", indexKey);
                    // todo: 这里使用了弯道来实现，可能有更好的方法？ 黄磊 20250316
                    // 尝试获取应用中的RedisIndexedSessionRepository实例
                    RedisIndexedSessionRepository repository = null;
                    try {
                        // 如果有Spring上下文，尝试从中获取RedisIndexedSessionRepository实例
                        if (ContextLoader.getCurrentWebApplicationContext() != null) {
                            repository = ContextLoader.getCurrentWebApplicationContext().getBean(RedisIndexedSessionRepository.class);
                        }
                    } catch (Exception e) {
                        log.warn("无法获取RedisIndexedSessionRepository实例: {}", e.getMessage());
                    }

                    if (repository != null) {
                        // 使用RedisIndexedSessionRepository的原生操作
                        RedisOperations<Object, Object> redisOperations = repository.getSessionRedisOperations();
                        redisOperations.boundSetOps(indexKey).add(sessionId);
                        log.info("使用RedisIndexedSessionRepository创建索引成功");
                    } else {
                        // 获取当前使用的RedisTemplate
                        RedisTemplate<String, Object> redisTemplate = Base.map.getRedisTemplate();
                        if (redisTemplate == null) {
                            log.warn("RedisTemplate为null，无法创建索引");
                            return;
                        }
                        
                        // 使用当前的默认序列化器，避免序列化不一致
                        @SuppressWarnings("unchecked")
                        RedisSerializer<String> keySerializer = (RedisSerializer<String>) redisTemplate.getKeySerializer();
                        @SuppressWarnings("unchecked")
                        RedisSerializer<Object> valueSerializer = (RedisSerializer<Object>) redisTemplate.getValueSerializer();
                        
                        if (keySerializer == null || valueSerializer == null) {
                            log.warn("Redis序列化器未配置，无法创建索引");
                            return;
                        }
                        
                        // 使用相同的序列化器添加会话ID到SET中
                        byte[] rawKey = keySerializer.serialize(indexKey);
                        byte[] rawValue = valueSerializer.serialize(sessionId);
                        
                        if (rawKey == null || rawValue == null) {
                            log.warn("序列化键值失败，无法创建索引");
                            return;
                        }
                        
                        redisTemplate.execute(connection -> {
                            if (connection != null) {
                                connection.setCommands().sAdd(rawKey, rawValue);
                                return true;
                            }
                            return false;
                        }, true);
                        log.info("使用系统RedisTemplate创建索引成功");
                    }

                    log.info("已为用户 {} 创建索引，关联会话 {}", userid, sessionId);
                }
            } catch (Exception e) {
                log.warn("创建会话索引时出错: {}", e.getMessage());
            }

            // 始终更新索引过期时间，确保与会话保持同步
            // 设置索引过期时间比会话多10分钟
            long indexExpiration = expirationSeconds + 600;
            boolean result = Base.map.expire(indexKey, indexExpiration, TimeUnit.SECONDS);

            if (result) {
                log.debug("已更新索引键 {} 的过期时间为 {} 秒", indexKey, indexExpiration);
            } else {
                log.warn("无法为索引键 {} 设置过期时间", indexKey);
            }
        } catch (Exception e) {
            log.warn("更新索引过期时间时出错", e);
        }
    }
} 