# LogUtil日志使用规范

## 概述

本文档规定了`LogUtil`类的使用规范，以确保系统日志的规范性和一致性，同时避免在生产环境中产生过多的无用日志。

## 日志级别

`LogUtil`类支持以下日志级别：

- `DEBUG (0)`: 调试信息，仅在开发环境使用
- `INFO (1)`: 提示信息，记录系统正常操作
- `WARN (2)`: 警告信息，记录可能的问题
- `ERROR (3)`: 错误信息，记录系统错误

## 使用规范

### 1. INFO级别日志使用规范

**重要**: INFO级别的日志必须加上`Var.debug`判定，避免在生产环境中产生过多的日志。

正确示例：
```java
// 正确用法
if (Var.debug) {
    LogUtil.info("用户 {0} 登录成功", userName);
}

// 带request参数的正确用法
if (Var.debug) {
    LogUtil.info(request, "用户 {0} 登录成功", userName);
}
```

错误示例：
```java
// 错误用法：没有Var.debug判断
LogUtil.info("用户 {0} 登录成功", userName);
```

### 2. XWL中日志使用规范

在XWL脚本中直接使用LogUtil时，必须使用带`request`参数的方法，以确保日志中包含用户和IP信息。

正确示例：
```java
// XWL中的正确用法
LogUtil.error(request, "数据保存失败");
LogUtil.warn(request, "参数 {0} 无效", paramName);
if (Var.debug) {
    LogUtil.info(request, "操作完成，结果: {0}", result);
}
```

错误示例：
```java
// XWL中的错误用法：没有使用带request参数的方法
LogUtil.error("数据保存失败");
LogUtil.warn("参数 {0} 无效", paramName);
```

### 3. 错误日志使用规范

错误日志应当包含足够的信息以便问题定位。对于异常情况，应使用带Throwable参数的方法记录完整的异常堆栈。

推荐用法：
```java
try {
    // 业务代码
} catch (Exception e) {
    LogUtil.error(request, "保存数据时发生错误: {0}", e, dataId);
    // 或者
    LogUtil.error("保存数据时发生错误: {0}", e, dataId);
}
```

### 4. 调试日志使用规范

调试日志主要用于开发环境，可以通过`LogUtil.debug`方法记录。

```java
LogUtil.debug("当前处理的数据: {0}", data);
LogUtil.debug(request, "当前处理的数据: {0}", data);
```

### 5. 日志内容规范

- 日志内容应简明扼要，包含必要的上下文信息
- 敏感信息（如密码、身份证号等）不应出现在日志中
- 使用占位符而非字符串拼接，以提高性能
- 日志信息应当有实际意义，避免过于笼统的描述

正确示例：
```java
LogUtil.warn(request, "用户 {0} 尝试访问未授权的资源 {1}", userName, resourceId);
```

错误示例：
```java
LogUtil.warn(request, "出错了"); // 过于笼统
LogUtil.warn(request, "用户" + userName + "尝试访问未授权的资源" + resourceId); // 不应使用字符串拼接
```

## 总结

遵循以上规范可以确保系统日志的一致性和有效性，同时避免在生产环境中产生过多无用的日志信息。记住两个关键点：

1. INFO级别必须加上`Var.debug`判定
2. XWL中直接使用必须使用带request参数的方法 