package com.wb.cache;

import javax.annotation.PreDestroy;
import org.apache.commons.lang.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redisson配置类
 */
@Configuration
public class RedissonConfig {

    /**
     * Redis地址
     */
    @Value("${redis.address}")
    private String address;

    /**
     * Redis端口
     */
    @Value("${redis.port}")
    private int port;

    /**
     * Redis密码
     */
    @Value("${redis.password}")
    private String password;

    /**
     * 业务线程数
     */
    @Value("${redis.threads:8}")
    private int threads;

    /**
     * Netty线程数
     */
    @Value("${redis.nettyThreads:8}")
    private int nettyThreads;

    /**
     * 连接池大小
     */
    @Value("${redis.connectionPoolSize:32}")
    private int connectionPoolSize;

    /**
     * 空闲连接超时
     */
    @Value("${redis.idleConnectionTimeout:30000}")
    private int idleTimeout;

    private RedissonClient redissonClient;

    /**
     * 创建Redisson客户端的Bean
     * 
     * @return Redisson客户端实例
     */
    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        // 设置线程池配置
        config.setThreads(threads) // 控制redisson-3-*线程数
                .setNettyThreads(nettyThreads); // 控制redisson-netty-*线程数

        SingleServerConfig serverConfig = config.useSingleServer().setAddress("redis://" + address + ":" + port)
                // 连接池配置
                .setConnectionPoolSize(connectionPoolSize).setIdleConnectionTimeout(idleTimeout).setConnectTimeout(5000) // 连接超时5秒
                .setRetryAttempts(3) // 命令重试次数
                .setRetryInterval(1000); // 重试间隔1秒

        if (!StringUtils.isEmpty(password)) {
            serverConfig.setPassword(password);
        }
        redissonClient = Redisson.create(config);
        return redissonClient;
    }

    /**
     * 添加销毁钩子确保资源释放
     */
    @PreDestroy
    public void destroy() {
        if (redissonClient != null) {
            redissonClient.shutdown();
        }
    }
}
