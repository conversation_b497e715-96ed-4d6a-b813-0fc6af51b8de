package com.wb.aliyun.dms;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dms_enterprise20181101.AsyncClient;
import com.wb.common.Var;
import darabonba.core.client.ClientOverrideConfiguration;

/**
 * @Author: qny
 * @Date: 2024-03-29-10:49
 * @Description:
 */
public class DmsApiClient {

    private static AsyncClient asyncClient;

    /**
     * 用户访问密钥对中的AccessKey ID LTAI5t7rVTXzufFzZ8H1jWD6
     */
    static String DMS_ACCESS_KEY_ID = "sys.config.smsCloud.accessKeyId";

    /**
     * 用户访问密钥对中的AccessKey Secret ******************************
     */
    static String DMS_ACCESS_KEY_SECRET = "sys.config.smsCloud.accessSecret";

    /**
     * 访问域名 dms-enterprise.cn-shenzhen.aliyuncs.com
     */
    static String DMS_ENDPOINT = "sys.config.dms.endpoint";

    /**
     * 访问区域id cn-shenzhen
     * @return
     */
    static String DMS_REGION_ID = "sys.config.dms.regionId";

    static {
        Runtime.getRuntime().addShutdownHook(new Thread() {
            @Override
            public void run() {
                System.out.println("JVM is shutting down, closing asyncClient...");
                if (asyncClient != null) {
                    asyncClient.close();
                }
            }
        });
    }

    public static AsyncClient getInstance() {
        if(asyncClient == null){
            synchronized (DmsApiClient.class) {
                // Configure Credentials authentication information, including ak, secret, token
                StaticCredentialProvider provider = generateCredentialProvider();
                // Configure the Client
                asyncClient = generateDmsClient(provider);
            }
        }
        return asyncClient;
    }

    private static AsyncClient generateDmsClient(StaticCredentialProvider provider) {

        return AsyncClient.builder()
                // Region ID
                .region(Var.getString(DMS_REGION_ID))
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                // Endpoint 请参考 https://api.aliyun.com/product/dms-enterprise
                                .setEndpointOverride(Var.getString(DMS_ENDPOINT))
                )
                .build();

    }

    private static StaticCredentialProvider generateCredentialProvider() {

        return StaticCredentialProvider.create(Credential.builder()
                // Please ensure that the environment variables ALIBABA_CLOUD_ACCESS_KEY_ID and ALIBABA_CLOUD_ACCESS_KEY_SECRET are set.
                .accessKeyId(Var.getString(DMS_ACCESS_KEY_ID))
                .accessKeySecret(Var.getString(DMS_ACCESS_KEY_SECRET))
                .build());

    }

}
