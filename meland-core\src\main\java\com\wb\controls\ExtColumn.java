package com.wb.controls;

import com.wb.common.KVBuffer;

/**
 * Column控件的解析类。
 */
public class ExtColumn extends ExtControl {
	protected void extendConfig() {
		String keyName = gs("keyName");
		boolean keyStatusFilter = gb("keyStatusFilter");
		if (!keyName.isEmpty()) {
			if (hasItems)
				headerScript.append(',');
			else
				hasItems = true;
			headerScript.append("renderer:Wb.kvRenderer,keyItems:");
			
			// 根据是否有状态过滤参数调用不同的方法
			if (!keyStatusFilter) {
				headerScript.append(KVBuffer.getList(keyName));
			} else {
				// 有状态过滤时，默认筛选启用状态(1)
				headerScript.append(KVBuffer.getList(keyName, 1));
			}
		}
	}
}
