package com.wb.cache;

import java.io.Serializable;

/**
 * 自定义消息
 * 
 * <AUTHOR>
 *
 */
public class RedisMessage implements Serializable {

	/**
	 * 用于标识类的序列化版本号，确保在反序列化对象时，类的版本兼容。
	 * 在版本变更时，应更新此值以避免反序列化出错。
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 表示消息的类型。用于区分不同类别的消息，例如短信（SMS）。
	 */
	private Type type;
	/**
	 * 表示消息的内容。可以包含任意类型的对象，用于存储具体的消息数据。
	 */
	private Object content;

	/**
	 * 获取消息的类型。
	 *
	 * @return 消息的类型，可能为枚举值 Type 中的一个。
	 */
	public Type getType() {
		return type;
	}

	/**
	 * 设置消息的类型。
	 *
	 * @param type 要设置的消息类型，指示消息的类别，例如短消息（SMS）。
	 */
	public void setType(Type type) {
		this.type = type;
	}

	/**
	 * 获取消息内容。
	 *
	 * @return 返回当前消息的内容。
	 */
	public Object getContent() {
		return content;
	}

	/**
	 * 设置内容。
	 *
	 * @param content 要设置的内容对象
	 */
	public void setContent(Object content) {
		this.content = content;
	}

	public static enum Type {
		SMS
	}

	@Override
	public String toString() {
		return "RedisMessage{" + "type=" + type + ", content=" + content + '}';
	}

}
