package aliyun.sls;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.Index;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.LogStore;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.GetLogsResponse;

public class Test {

	//配置AccessKey、服务入口、Project名称、Logstore名称等相关信息。
    //阿里云访问密钥AccessKey。更多信息，请参见访问密钥。阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维。
    static String accessId = "LTAI5tHJfCSjmxmNnkJxudig"; 
    static String accessKey = "******************************";
    //日志服务的服务入口。更多信息，请参见服务入口。
    //此处以杭州为例，其它地域请根据实际情况填写。
    static String host = "cn-shenzhen.log.aliyuncs.com"; 
    //创建日志服务Client。 
    static Client client = new Client(host, accessId, accessKey);
    //Project名称。
    static String projectName = "meland-log";
    //Logstore名称。
    static String logstoreName = "sys-log";
    //查询语句。
    static String query = "*| select * from " + logstoreName;

    //创建Project。
     static void createProject() throws LogException, InterruptedException {
        String projectDescription = "project description";
        System.out.println("ready to create project");
        client.CreateProject(projectName, projectDescription);
        System.out.println(String.format("create project %s success",projectName));
        TimeUnit.SECONDS.sleep(60*2);
    }

    //创建Logstore。
     static void createLogstore() throws LogException, InterruptedException {
        System.out.println("ready to create logstore");
        int ttl_in_day = 3;     //数据保存时间。如果配置为3650，表示永久保存。单位为天。
        int shard_count = 2;   //Shard数量。
        LogStore store = new LogStore(logstoreName, ttl_in_day, shard_count);
        client.CreateLogStore(projectName, store);
        System.out.println(String.format("create logstore %s success",logstoreName));
        TimeUnit.SECONDS.sleep(60);
    }

    //为Logstore创建索引。
     static void createIndex() throws LogException, InterruptedException {
        System.out.println(String.format("ready to create index for %s", logstoreName));
        String logstoreIndex = "{\"line\": {\"token\": [\",\", \" \", \"'\", \"\\\"\", \";\", \"=\", \"(\", \")\", \"[\", \"]\", \"{\", \"}\", \"?\", \"@\", \"&\", \"<\", \">\", \"/\", \":\", \"\\n\", \"\\t\", \"\\r\"], \"caseSensitive\": false, \"chn\": false}, \"keys\": {\"dev\": {\"type\": \"text\", \"token\": [\",\", \" \", \"'\", \"\\\"\", \";\", \"=\", \"(\", \")\", \"[\", \"]\", \"{\", \"}\", \"?\", \"@\", \"&\", \"<\", \">\", \"/\", \":\", \"\\n\", \"\\t\", \"\\r\"], \"caseSensitive\": false, \"alias\": \"\", \"doc_value\": true, \"chn\": false}, \"id\": {\"type\": \"long\", \"alias\": \"\", \"doc_value\": true}}, \"log_reduce\": false, \"max_text_len\": 2048}";
        Index index = new Index();
        index.FromJsonString(logstoreIndex);
        client.CreateIndex(projectName, logstoreName, index);
        System.out.println(String.format("create index for %s success",logstoreName));
        TimeUnit.SECONDS.sleep(60);
    }

    //向Logstore写入数据。
    //为了提高您系统的IO效率，请尽量不要直接使用该方式往日志服务中写数据，此方式仅为功能举例。
    //在大数据、高并发场景下建议使用Aliyun Log Java Producer方式写入日志数据。
     static void pushLogs() throws LogException, InterruptedException {
        System.out.println(String.format("ready to push logs for %s",logstoreName));
        List<LogItem> logGroup = new ArrayList<LogItem>();
        for (int i = 0; i < 100; ++i) {
            LogItem logItem = new LogItem();
            logItem.PushBack("id", String.valueOf(i));
            logItem.PushBack("dev", "test_push");
            logGroup.add(logItem);
        }
        client.PutLogs(projectName, logstoreName, "", logGroup, "");
        System.out.println(String.format("push logs for %s success",logstoreName));
        TimeUnit.SECONDS.sleep(5);
    }

    //通过SQL查询日志。
     static void queryLogs() throws LogException {
        System.out.println(String.format("ready to query logs from %s",logstoreName));
        //fromTime和toTime表示查询日志的时间范围，Unix时间戳格式。
        int fromTime = (int) (System.currentTimeMillis()/1000 - 36000);
        int toTime = fromTime + 36000;
        GetLogsResponse getLogsResponse = client.GetLogs(projectName, logstoreName, fromTime, toTime, "", query);
        System.out.println(getLogsResponse.GetCount());
        for (QueriedLog log : getLogsResponse.GetLogs()) {
            for (LogContent mContent : log.mLogItem.mContents) {
                System.out.println(mContent.mKey + " : " + mContent.mValue);
            }
            System.out.println("********************");
        }
    }

    public static void main(String[] args) throws LogException, InterruptedException {
       //创建Project。
//        createProject();
        //创建Logstore。
//        createLogstore();
        //创建索引。
//        createIndex();
        //写入日志数据。
//        pushLogs();
        //查询日志。
        queryLogs();
    }

}
