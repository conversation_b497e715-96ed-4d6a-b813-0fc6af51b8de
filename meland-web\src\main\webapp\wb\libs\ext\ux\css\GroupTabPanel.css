/* Main GroupTabPanel style */
.x-grouptabpanel {
    background-color: #4E78B1;
    border: solid 15px #4E78B1;
}
/* End of GroupTabPanel style */

/* Card Layout container */
.x-grouptabcontainer {
    background-color: white;
    border-top: 1px solid #d9d9d9 !important;    
    border-bottom: 1px solid #d9d9d9 !important;
    border-right: 1px solid #d9d9d9 !important;
}
/* End of Card Layout container */

.x-grouptabpanel .x-grouptab .x-grid-cell-inner  {
    font-family: tahoma, arial, sans-serif;
    text-decoration: none!important;
}

.x-grouptabpanel .x-grouptab-first .x-grid-cell-inner  {
    font-size: 13px;
    line-height: 18px;
    font-weight: bold;
}

.x-grouptabbar .x-grid-row .x-grid-cell {
    background-color: transparent;
    padding-left: 6px;
}

.x-grouptabbar .x-grid-cell-inner  {
    color: #DFE8F6;
}

.x-grouptabpanel .x-active-group .x-grid-cell-inner  {
    color: #395B8E;
}

.x-grouptabbar .x-grouptab {
    line-height: 24px;
    background-color: transparent;
    font-size: 12px;
    position: relative;
    padding-left: 6px;
    padding-bottom: 2px;
    overflow: visible; /* To allow round corner blobs (.x-grouptabs-corner) to occlude the element border */
}

.x-grouptabbar .x-grouptab-first {
    padding-top: 6px;
}

.x-grouptabbar .x-grouptab-last {
    margin-bottom: 3px;
}

.x-grouptabbar .x-active-tab .x-grid-cell-inner  {
    background-color: #EDEEF0;
}

.x-grouptabbar .x-tree-elbow-line, 
.x-grouptabbar .x-tree-elbow-empty {
    width: 0;
}

.x-grouptabbar .x-active-group .x-grouptab {
    background-color: white!important;
    border: 0 none!important;
}

.x-grouptabbar .x-active-group .x-grouptab-first {
    border-top: 1px solid #d9d9d9!important;
}

.x-grouptabbar .x-active-group .x-grouptab-center {
    border-left: 1px solid #d9d9d9!important;
}

.x-grouptabbar .x-active-group .x-grouptab-last {
    border-bottom: 1px solid #d9d9d9!important;
    padding-bottom: 3px;
}

.x-grouptabbar .x-active-group .x-grouptab-first,
.x-grouptabbar .x-active-group .x-grouptab-last {
    border-left: 1px solid #d9d9d9!important;
}

.x-grouptab-cell {
    border: 0 none!important;
}

/* Tab corners */
.x-grouptabbar .x-grouptabs-corner {
    background-image: url('images/x-grouptabs-corners.gif');
    display: none;
    width: 11px;
    height: 11px;
    position: absolute;
    font-size: 1px;
    line-height: 6px;
    overflow: hidden;
    zoom:1;
}

.x-grouptabbar .x-grouptabs-corner {
    display: none;
}

.x-grouptabbar .x-active-group .x-grouptab-first .x-grouptabs-corner-top-left,
.x-grouptabbar .x-active-group .x-grouptab-last .x-grouptabs-corner-bottom-left {
    display: block;
}

.x-grouptabbar .x-grouptabs-corner-top-left {
    background-position: top left;
    left: 0; top: 0;
}
.x-grouptabbar .x-grouptabs-corner-bottom-left {
    background-position: bottom left;
    left: 0; bottom: 0;
}
.x-grouptabbar .x-grouptabs-corner-top-right {
    background-position: top right;
    right: 0; top: 0;
}
.x-grouptabbar .x-grouptabs-corner-bottom-right {
    background-position: bottom right;
    right: 0; bottom: 0;
}
.x-grouptabbar .x-active-group .x-grouptabs-corner-bottom-left{
    bottom: -4px; left: -4px;
}
.x-grouptabbar .x-active-group .x-grouptabs-corner-bottom-right{
    bottom: -4px; right: -4px;
}
.x-grouptabbar .x-active-group .x-grouptabs-corner-top-left{
    top: -4px; left: -4px;
}
.x-grouptabbar .x-active-group .x-grouptabs-corner-top-right{
    top: -4px; right: -4px;
}

.x-grouptabbar .x-tree-panel .x-grid-cell {
    overflow: visible;
}

.x-grouptabbar .x-panel-body  {
    background-color: #4E78B1;
}

.x-grouptabbar {
    top: 0 !important;
    margin-top: -1px !important;
}

.x-strict .x-ie7 .x-grouptabbar, .x-strict .x-ie6 .x-grouptabbar {
    margin-top: -2px !important;
}

.x-grouptabbar .x-grouptab .x-tree-icon-parent {
    display: none;
}

.x-grouptabbar .x-grouptab .x-tree-elbow,
.x-grouptabbar .x-grouptab .x-tree-elbow-end {
    display: none;
}

.x-grouptabbar .x-grouptab .x-tree-expander {
    background: transparent url('images/elbow-plus-nl.gif') no-repeat;
}

.x-grouptabbar .x-grid-tree-node-expanded .x-grouptab .x-tree-expander {
    background: transparent url('images/elbow-minus-nl.gif') no-repeat;
}