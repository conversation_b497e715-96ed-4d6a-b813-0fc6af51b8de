# Redis发布/订阅功能使用文档

本文档介绍Redis缓存系统的发布/订阅（Pub/Sub）功能，用于实现消息的发布与订阅。

## 功能概览

Redis发布/订阅系统提供以下功能：

- 向指定频道发布消息
- 订阅频道接收消息
- 消息的即时传递
- 支持多种消息格式

## Java 用法

在Java代码中，可以直接使用`Base.map`访问发布/订阅功能：

```java
// 发布消息到指定频道
String channel = "notifications";
String message = "系统将于今晚22:00进行维护";
Long receiverCount = Base.map.publish(channel, message);

// 检查返回值了解有多少客户端接收到了消息
if (receiverCount > 0) {
    LogUtil.info("消息已发送给" + receiverCount + "个订阅者");
} else {
    LogUtil.warn("消息发送失败或没有订阅者");
}
```

## XWL 脚本用法

在XWL脚本中，直接使用`Base.map`访问发布/订阅功能：

```javascript
// 发布消息到指定频道
var channel = "notifications";
var message = "系统将于今晚22:00进行维护";
var receiverCount = Base.map.publish(channel, message);

// 检查返回值了解有多少客户端接收到了消息
if (receiverCount > 0) {
    println("消息已发送给" + receiverCount + "个订阅者");
} else {
    println("消息发送失败或没有订阅者");
}
```

## 使用场景

### 实时通知

```java
// 向所有在线用户发送系统通知
public void sendSystemNotification(String message) {
    Base.map.publish("system:notifications", message);
}

// 向特定用户组发送通知
public void sendGroupNotification(String groupId, String message) {
    Base.map.publish("group:" + groupId + ":notifications", message);
}

// 向特定用户发送消息
public void sendUserMessage(String userId, String message) {
    Base.map.publish("user:" + userId + ":messages", message);
}
```

### 数据同步

```java
// 当数据更新时发布变更通知
public void notifyDataChange(String entityType, String entityId, String operation) {
    JSONObject notification = new JSONObject();
    notification.put("type", entityType);
    notification.put("id", entityId);
    notification.put("operation", operation);
    notification.put("timestamp", System.currentTimeMillis());
    
    Base.map.publish("data:changes", notification.toJSONString());
}
```

### 分布式事件处理

```java
// 触发远程事件
public void triggerEvent(String eventName, Map<String, Object> eventData) {
    JSONObject event = new JSONObject();
    event.put("name", eventName);
    event.put("data", eventData);
    event.put("timestamp", System.currentTimeMillis());
    
    Base.map.publish("system:events", event.toJSONString());
}
```

## 订阅消息

要订阅消息，需要在应用程序启动时配置消息监听器。这通常在Spring配置中完成，或通过编程方式设置：

```java
@Configuration
public class RedisConfig {

    @Bean
    public RedisMessageListenerContainer redisMessageListenerContainer(
            RedisConnectionFactory connectionFactory,
            MessageListenerAdapter listenerAdapter) {
        
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.addMessageListener(listenerAdapter, new PatternTopic("system:notifications"));
        return container;
    }
    
    @Bean
    public MessageListenerAdapter listenerAdapter(NotificationListener listener) {
        return new MessageListenerAdapter(listener, "onMessage");
    }
}

@Component
public class NotificationListener {
    public void onMessage(String message, String channel) {
        LogUtil.info("从频道 [" + channel + "] 收到消息: " + message);
        // 处理消息...
    }
}
```

## 注意事项

1. Redis的发布/订阅功能不保存历史消息，客户端只能收到订阅后发布的消息
2. 如果发布消息时没有订阅者，消息将被丢弃
3. 当客户端断开连接后，将不会收到断开期间的消息
4. 对于需要可靠消息传递的场景，考虑使用Redis Stream或专业的消息队列系统
5. 消息大小应控制在合理范围内，避免影响Redis性能
6. 发布频道名称应遵循一定的命名规范，如使用冒号分隔不同级别 