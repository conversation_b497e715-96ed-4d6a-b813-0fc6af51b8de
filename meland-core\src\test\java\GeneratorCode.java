import com.wb.util.SysUtil;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

public class GeneratorCode {

    public static void main(String[] args) {
        testGenerateShortUUIDs();
    }

    public static void testGenerateShortUUIDs() {
        Set<String> uuidSet = new HashSet<>();
        final int totalUUIDs = 100000;

        for (int i = 0; i < totalUUIDs; i++) {
            String shortUUID = SysUtil.generateShortUUID(13);
            uuidSet.add(shortUUID);
            System.out.println(shortUUID);
        }

        if (uuidSet.size() < totalUUIDs) {
            System.out.println("出现了重复券码: " + uuidSet.size());
        } else {
            System.out.println("没有重复的券码： " + uuidSet.size());
        }
    }
}
