package com.wb.message;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.concurrent.ConcurrentHashMap;

import org.json.JSONObject;

import com.wb.message.handler.HandlerDD;
import com.wb.message.handler.HandlerDefault;
import com.wb.message.handler.HandlerEmail;
import com.wb.message.handler.HandlerQy;
import com.wb.message.handler.HandlerSmall;
import com.wb.message.handler.HandlerSms;
import com.wb.message.handler.HandlerWx;
import com.wb.util.DbUtil;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;

/**
 * 消息处理类
 *
 * <AUTHOR>
 */
public class MessageConsume {
    /**
     * 消息配置缓存。
     */
    public static ConcurrentHashMap<String, ConcurrentHashMap<String, Boolean>> config;
    /**
     * 消息配置其他参数（JSON数据类型）
     */
    public static ConcurrentHashMap<String, ConcurrentHashMap<String, String>> msg_arg;

    /**
     * 加载和初始化。
     */
    public static synchronized void load() {
        try {
            config = new ConcurrentHashMap<>();
            msg_arg = new ConcurrentHashMap<>();
            Connection conn = null;
            Statement st = null;
            ResultSet rs = null;
            ConcurrentHashMap<String, Boolean> map;
            ConcurrentHashMap<String, String> mapj;

            try {
                conn = DbUtil.getConnection();
                st = conn.createStatement();
                rs = st.executeQuery("select * from msg_config");
                while (rs.next()) {
                    map = new ConcurrentHashMap<>();
                    mapj = new ConcurrentHashMap<>();
                    map.put("sys", rs.getBoolean("enable_sys")); // 系统消息
                    map.put("email", rs.getBoolean("enable_email")); // email消息
                    map.put("wx", rs.getBoolean("enable_wx")); // 微信消息
                    map.put("qy", rs.getBoolean("enable_qy")); // 企业微信消息
                    map.put("dd", rs.getBoolean("enable_dd")); // 钉钉消息
                    map.put("small", rs.getBoolean("enable_small")); // 小程序消息
                    map.put("sms", rs.getBoolean("enable_sms")); // 短信消息
                    mapj.put("arg", rs.getString("msg_arg"));
                    mapj.put("id", rs.getString("id"));
                    mapj.put("module", rs.getString("module_path"));
                    config.put(rs.getString("msg_code"), map); // 消息编号
                    msg_arg.put(rs.getString("msg_code"), mapj); // 消息编号
                }
            } finally {
                DbUtil.close(rs);
                DbUtil.close(st);
                DbUtil.close(conn);
            }
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 处理消息
     *
     * @throws Exception
     */
    public static void consume() {
        if (null == config) {
            load();
        }
        JSONObject msgObj = null;
        try {
            try {
                msgObj = Constants.MSG_QUEUE.take();
            } catch (Exception e) {
                return;
            }
            if (null == msgObj) {
                return;
            }
            ConcurrentHashMap<String, Boolean> msgConfig;
            ConcurrentHashMap<String, String> argConfig;
            String title, content, users, data;
            JSONObject arg = null; // 其他参数
            if (StringUtil.isEmpty(msgObj.toString()) || msgObj.toString().equals("{}")
                    || msgObj.toString().equals("[]")) {
                return;
            }
            if (!msgObj.has(Constants.MSG_CODE)) {
                title = msgObj.has(Constants.MSG_TITLE) ? msgObj.optString(Constants.MSG_TITLE) : "系统消息";
                LogUtil.warn(StringUtil.format("[{0}]消息,无消息编号。", title));
                return;
            }
            if (!config.containsKey(msgObj.getString(Constants.MSG_CODE))) {
                LogUtil.warn(StringUtil.format("消息编号 [{0}] 没有配置对应的消息发送规则。", msgObj.getString(Constants.MSG_CODE)));
                return;
            }
            if (!msgObj.has(Constants.MSG_CONTENT) || !msgObj.has(Constants.MSG_USERS)) {
                return; // 如果没有标题和接收人，则直接抛弃消息
            }
            title = msgObj.has(Constants.MSG_TITLE) ? msgObj.optString(Constants.MSG_TITLE) : "系统消息";
            content = msgObj.getString(Constants.MSG_CONTENT);
            users = msgObj.getString(Constants.MSG_USERS);
            data = msgObj.optString(Constants.MSG_DATA);

            msgConfig = config.get(msgObj.getString(Constants.MSG_CODE));
            argConfig = msg_arg.get(msgObj.getString(Constants.MSG_CODE));

            // 其他参数配置
            arg = new JSONObject(argConfig.get("arg"));
            arg.put("module", argConfig.get("module"));

            // 持久化
            persistenceMessage(title, content, users, argConfig.get("id"), data, arg);

            if (msgConfig.get("sys")) {
                new HandlerDefault(title, content, users, data, arg).handlerMessage();
            }
            if (msgConfig.get("email")) {
                new HandlerEmail(title, content, users, data, arg).handlerMessage();
            }
            if (msgConfig.get("wx")) {
                new HandlerWx(title, content, users, data, arg).handlerMessage();
            }
            if (msgConfig.get("qy")) {
                new HandlerQy(title, content, users, data, arg).handlerMessage();
            }
            if (msgConfig.get("dd")) {
                new HandlerDD(title, content, users, data, arg).handlerMessage();
            }
            if (msgConfig.get("small")) {
                data = StringUtil.isEmpty(data) || "null".equals(data) ? StringUtil.toString(arg) : data;
                if (!StringUtil.isEmpty(data)) {
                    new HandlerSmall(title, content, users, data, arg).handlerMessage();
                } else {
                    LogUtil.warn(StringUtil.format("未发送消息[{0}]到小程序，参数[data]为空", title));
                }
            }
            if (msgConfig.get("sms")) {
                new HandlerSms(title, content, users, data, arg).handlerMessage();
            }
        } catch (Exception e) {
            LogUtil.error(StringUtil.format("处理消息[{0}]异常：{1}", msgObj, e));
        }
    }

    /**
     * 消息持久化数据库
     *
     * @param title
     * @param content
     * @param users
     * @param id
     */
    public static void persistenceMessage(String title, String content, String users, String id, String data,
                                          JSONObject arg) {
        Connection conn = null;
        JSONObject param = new JSONObject(data);
        // 判断重写参数是否存在 不存在使用默认参数 存在使用重写参数
        if (StringUtil.isEmpty(param.optString("path"))) {
            param.put("path", StringUtil.isEmpty(param.optString("path")) ? arg.get("path") : "");
        }
        String user_name = "", user_id = "", flow_id = "";
        // 发送用户
        if (param.has("username") && param.has("userid")) {
            user_name = param.getString("username");
            user_id = param.getString("userid");
            if (StringUtil.isEmpty(user_name)) {
                user_name = "系统";
                user_id = "admin";
            }
        } else {
            user_name = "系统";
            user_id = "admin";
        }
        if (param.has("flowId")) {
            flow_id = param.optString("flowId");
        }
        String msgId = SysUtil.getId();
        String[] userArray = users.split(",");

        try {
            conn = DbUtil.getConnection();
            conn.setAutoCommit(false);
            // 使用 try-with-resources 保证 PreparedStatement 资源的关闭
            try (PreparedStatement st = conn.prepareStatement(
                    "insert into msg_message(id, title, content, module, sender, url, status, add_date, add_user) values(?,?,?,?,?,?,'1',NOW(),?)")) {
                st.setString(1, msgId);
                st.setString(2, title);
                st.setString(3, content);
                st.setString(4, id);
                st.setString(5, user_name);
                st.setString(6, param.getString("path"));
                st.setString(7, user_id);
                st.executeUpdate();
            }
            // 存储收送人及消息ID和消息级别
            try (PreparedStatement stUser = conn.prepareStatement(
                    "insert into msg_message_user(id, msg_id, user_id, flow_id, state, add_date, add_user) values(?,?,?,?,'0',NOW(),?)");
                 PreparedStatement stIstop = conn.prepareStatement(
                         "insert into msg_message_istop(id, module, user_id, isTop, add_date, add_user) values(?,?,?,'0',NOW(),?)")) {
                for (String userId : userArray) {
                    // 存储收送人及消息ID
                    stUser.setString(1, SysUtil.getId());
                    stUser.setString(2, msgId);
                    stUser.setString(3, userId);
                    stUser.setString(4, flow_id);
                    stUser.setString(5, user_id);
                    stUser.addBatch();
                    // 储存消息级别
                    stIstop.setString(1, SysUtil.getId());
                    stIstop.setString(2, id);
                    stIstop.setString(3, userId);
                    stIstop.setString(4, user_id);
                    stIstop.addBatch();
                }
                stUser.executeBatch();
                stIstop.executeBatch();
            }
            conn.commit();
        } catch (Exception e) {
            LogUtil.error(StringUtil.format("持久化消息[{0}]异常：{1}", title, e));
        } finally {
            DbUtil.close(conn);
        }
    }
}