<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!-- The contents of this file will be loaded for each web application -->
<Context>
    <!-- 设置缓存的最大大小为800MB（819200KB） -->
    <!--  cacheObjectMaxSize设置为约30MB（30720KB） -->
    <Resources cacheMaxSize="819200" cacheObjectMaxSize="30720" cacheTtl="120000"/>
    
    <!-- 启用并行类加载器以提高性能 - Tomcat 9兼容配置
         - 特别适合大量使用Nashorn脚本的场景
         - 允许多线程并行加载类，减少类加载瓶颈 -->
    <Loader className="org.apache.catalina.loader.WebappLoader" 
            loaderClass="org.apache.catalina.loader.ParallelWebappClassLoader"/>
    
    <CookieProcessor className="org.apache.tomcat.util.http.Rfc6265CookieProcessor"
                     sameSiteCookies="strict"/>
    <WatchedResource>WEB-INF/web.xml</WatchedResource>
    <WatchedResource>WEB-INF/tomcat-web.xml</WatchedResource>
    <WatchedResource>${catalina.base}/conf/web.xml</WatchedResource>

    <!-- Uncomment this to disable session persistence across Tomcat restarts -->
    <!-- <Manager pathname="" /> -->
</Context>
