package com.wb.common.ws;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpSession;
import javax.servlet.http.HttpSessionBindingEvent;
import javax.servlet.http.HttpSessionBindingListener;

import org.springframework.session.Session;
import org.springframework.web.socket.WebSocketSession;

import com.wb.common.UserList;

/**
 * 连接HttpSession和WebSocketSession的桥梁，支持集群环境
 */
public class SessionBridge implements HttpSessionBindingListener, Serializable {

	private static final long serialVersionUID = 4199757719833802282L;
	private HashSet<String> sessionIds;

	public void valueBound(HttpSessionBindingEvent arg0) {
	}

	public void valueUnbound(HttpSessionBindingEvent arg0) {
		if (this.sessionIds != null) {
			// 在会话解绑时，关闭所有相关的WebSocket连接
			closeAllSocket();
		}
	}

	/**
	 * 关闭所有WebSocketSession
	 */
	private void closeAllSocket() {
		try {
			if (sessionIds == null) {
				return;
			}
			
			// 在集群环境中，只关闭本节点的会话
			for (String sessionId : sessionIds) {
				WebSocketSession session = WebSocketSessionManager.getInstance().getSession(sessionId);
				if (session != null) {
					try {
						session.close();
					} catch (Throwable ignored) {
						// 忽略关闭异常
					}
				}
				// 移除会话，包括Redis中的数据
				WebSocketSessionManager.getInstance().removeSession(sessionId);
			}
		} catch (Throwable ignored) {
			// 忽略关闭异常
		}
	}

	/**
	 * 添加WebSocketSession ID到SessionBridge
	 *
	 * @param sessionId WebSocketSession的ID
	 */
	private void addSocketSession(String sessionId) {
		if (this.sessionIds == null) {
			this.sessionIds = new HashSet<>();
		}
		this.sessionIds.add(sessionId);
	}

	/**
	 * 添加WebSocketSession到SessionBridge
	 *
	 * @param httpSession HttpSession对象
	 * @param session     WebSocketSession对象
	 * @param name        WebSocketSession关联的名称
	 */
	public static synchronized void addSession(Session httpSession, WebSocketSession session, String name) {
		// 获取用户ID
		String userId = (String) httpSession.getAttribute("sys.user");
		if (userId == null) {
			return;
		}
		
		// 创建或获取SessionBridge
		SessionBridge sessionBridge = (SessionBridge) httpSession.getAttribute("sysx.socket");
		if (sessionBridge == null) {
			sessionBridge = new SessionBridge();
		}
		
		// 添加会话ID到本地列表
		sessionBridge.addSocketSession(session.getId());
		
		// 将会话添加到WebSocketSessionManager
		WebSocketSessionManager.getInstance().addSession(session, userId, name);
		
		// 保存更改的SessionBridge对象到HttpSession
		httpSession.setAttribute("sysx.socket", sessionBridge);
		
		// 在用户的httpSession中标记WebSocket连接
		httpSession.setAttribute("sysx.hasWebSocket", true);
		
		// 使用Spring Session保存更改
		UserList.getSessionRepository().save(httpSession);
	}

	/**
	 * 从SessionBridge中移除WebSocketSession
	 *
	 * @param httpSession HttpSession对象
	 * @param session     WebSocketSession对象
	 * @param name        WebSocketSession关联的名称
	 */
	public static synchronized void removeSession(Session httpSession, WebSocketSession session, String name) {
		// 获取SessionBridge对象
		SessionBridge sessionBridge = (SessionBridge) httpSession.getAttribute("sysx.socket");
		if (sessionBridge != null && sessionBridge.sessionIds != null) {
			// 从本地列表移除
			sessionBridge.sessionIds.remove(session.getId());
			
			// 保存更改
			httpSession.setAttribute("sysx.socket", sessionBridge);
			
			// 使用Spring Session保存更改
			UserList.getSessionRepository().save(httpSession);
		}
		
		// 从WebSocketSessionManager中移除会话
		WebSocketSessionManager.getInstance().removeSession(session.getId());
	}

	/**
	 * 获取与指定名称关联的WebSocketSession列表
	 *
	 * @param httpSession HttpSession对象
	 * @param name        WebSocketSession关联的名称
	 * @return WebSocketSession列表
	 */
	public static List<WebSocketSession> getSessions(Session httpSession, String name) {
		String userId = (String) httpSession.getAttribute("sys.user");
		if (userId == null) {
			return null;
		}
		
		// 获取用户的所有会话ID
		Set<String> sessionIds = WebSocketSessionManager.getInstance().getSessionIdsByUserId(userId);
		if (sessionIds.isEmpty()) {
			return null;
		}
		
		// 收集可用的WebSocketSession，根据name参数过滤
		List<WebSocketSession> sessions = new ArrayList<>();
		for (String sessionId : sessionIds) {
			WebSocketSession session = WebSocketSessionManager.getInstance().getSession(sessionId);
			if (session != null && session.isOpen()) {
				// 获取会话关联的名称，并进行比较过滤
				String sessionKey = WebSocketRedisConfig.WS_SESSION_KEY_PREFIX + sessionId;
				Map<Object, Object> sessionInfo = WebSocketSessionManager.getInstance().getSessionInfo(sessionId);
				
				if (sessionInfo != null) {
					String sessionName = (String) sessionInfo.get("name");
					// 如果name参数为空或者匹配会话名称，则添加到返回列表
					if (name == null || name.equals(sessionName)) {
						sessions.add(session);
					}
				} else {
					// 如果无法获取会话信息，保持原有行为，添加会话
					sessions.add(session);
				}
			}
		}
		
		return sessions.isEmpty() ? null : sessions;
	}

	/**
	 * 获取与指定名称关联的WebSocketSession列表
	 *
	 * @param httpSession HttpSession对象
	 * @param name        WebSocketSession关联的名称
	 * @return WebSocketSession列表
	 */
	public static List<WebSocketSession> getSessions(HttpSession httpSession, String name) {
		Session session = UserList.getSessionBySessionId(httpSession.getId());
		return getSessions(session, name);
	}
	
	/**
	 * 发送消息给用户
	 * 
	 * @param userId 用户ID
	 * @param message 消息内容
	 * @param isJson 是否为JSON格式
	 * @param targetName 目标名称
	 */
	public static void sendMessageToUser(String userId, String message, boolean isJson, String targetName) {
		WebSocketMessageSender.sendMessageToUser(userId, message, isJson, targetName);
	}
	
	/**
	 * 广播消息给所有用户
	 * 
	 * @param message 消息内容
	 * @param isJson 是否为JSON格式
	 * @param targetName 目标名称
	 */
	public static void broadcastMessage(String message, boolean isJson, String targetName) {
		WebSocketMessageSender.broadcastMessage(message, isJson, targetName);
	}
}
