package com.wb.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.wb.util.LogUtil;
import com.wb.common.Base;

/**
 * Redis处理器自动配置
 * 自动将RedisExceptionHandler应用到所有RedisMessageListenerContainer上
 */
@Component
public class RedisHandlerAutoConfiguration implements BeanPostProcessor{

    private static final Logger logger = LoggerFactory.getLogger(RedisHandlerAutoConfiguration.class);
    
    @Autowired
    private RedisExceptionHandler redisExceptionHandler;
    
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 创建Redis异常处理器
     */
    @Bean
    public RedisExceptionHandler redisExceptionHandler() {
        return new RedisExceptionHandler();
    }
    
    /**
     * 创建Bean后处理器，自动配置所有RedisMessageListenerContainer
     */
    @Bean
    public BeanPostProcessor redisContainerPostProcessor(@Autowired RedisExceptionHandler exceptionHandler) {
        return new BeanPostProcessor() {
            @Override
            public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
                if (bean instanceof RedisMessageListenerContainer) {
                    RedisMessageListenerContainer container = (RedisMessageListenerContainer) bean;
                    
                    // 设置更短的恢复间隔
                    container.setRecoveryInterval(3000);
                    
                    // 设置错误处理器
                    container.setErrorHandler(exceptionHandler);
                    
                    // 调整日志级别，减少启动日志
                    if (beanName.equals("redisMessageListenerContainer")) {
                        logger.info("已为Redis主容器[{}]配置异常处理器", beanName);
                    } else {
                        logger.debug("已为Redis容器[{}]配置异常处理器", beanName);
                    }
                }
                return bean;
            }
        };
    }

    /**
     * 在Bean初始化后处理
     */
    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        // 为RedisCache设置异常处理器
        if (bean instanceof RedisCache && redisExceptionHandler != null) {
            ((RedisCache) bean).setRedisExceptionHandler(redisExceptionHandler);
            LogUtil.info("自动配置RedisCache的异常处理器");
        }
        
        return bean;
    }
    
    /**
     * 手动切换Redis集群模式
     * 在系统运行期间可以调用此方法动态切换集群模式
     * 
     * @param enable 是否启用集群模式
     */
    public void switchClusterMode(boolean enable) {
        try {
            org.springframework.data.redis.core.RedisTemplate<String, Object> template = 
                Base.map != null ? Base.map.getRedisTemplate() : redisTemplate;
                
            if (template == null) {
                logger.warn("无法切换集群模式，RedisTemplate未初始化");
                return;
            }
            
            if (enable) {
                logger.info("手动切换为Redis集群模式");
                // 启用集群模式并发布消息，同时保存状态到Redis
                RedisConfiguration.enableClusterMode(true, template);
            } else {
                logger.info("手动切换为Redis标准模式");
                // 禁用集群模式并发布消息，同时保存状态到Redis
                RedisConfiguration.disableClusterMode(true, template);
            }
        } catch (Exception e) {
            logger.error("手动切换Redis集群模式失败: " + e.getMessage(), e);
        }
    }
} 