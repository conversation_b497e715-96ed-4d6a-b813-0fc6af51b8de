package com.wb.task;

import org.json.JSONObject;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.wb.common.Base;
import com.wb.common.Var;
import com.wb.message.util.SmsUtil;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;

@DisallowConcurrentExecution
public class SmsTask implements Job {

	public void execute(JobExecutionContext context) throws JobExecutionException {
		try {
			JSONObject smsObject = (JSONObject) Base.map.listLeftPop(SmsUtil.LIST_KEY);
			if (null != smsObject)
				SmsUtil.smsCloudSender(smsObject);
		} catch (Throwable e) {
			LogUtil.error(StringUtil.concat("执行短信发送异常：：", SysUtil.getRootError(e)));
			if (Var.printError)
				throw new JobExecutionException(e);
		}
	}
}
