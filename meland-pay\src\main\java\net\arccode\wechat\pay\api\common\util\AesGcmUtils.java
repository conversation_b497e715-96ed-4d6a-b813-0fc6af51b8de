package net.arccode.wechat.pay.api.common.util;

import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * 微信 AEAD_AES_256_GCM的解密
 * 
 * <AUTHOR>
 *
 */
public class AesGcmUtils {

	private static final String ALGORITHM = "AES/GCM/NoPadding";
	private static final int TAG_LENGTH_BIT = 128;

	/**
	 * AEAD_AES_256_GCM解密
	 * 
	 * @param aesKey APIv3密钥
	 * @param associatedData 
	 * @param iv
	 * @param cipherText
	 * @return
	 * @throws Exception
	 */
	public static String aesgcmDecrypt(String aesKey, String associatedData, String iv, String cipherText)
			throws Exception {
		final Cipher cipher = Cipher.getInstance(ALGORITHM, "SunJCE");
		SecretKeySpec key = new SecretKeySpec(aesKey.getBytes(), "AES");
		GCMParameterSpec spec = new GCMParameterSpec(TAG_LENGTH_BIT, iv.getBytes());
		cipher.init(Cipher.DECRYPT_MODE, key, spec);
		cipher.updateAAD(associatedData.getBytes());
		return new String(cipher.doFinal(Base64.getDecoder().decode(cipherText)));
	}
}
