package com.wb.openplatform.accounts.service;

import java.io.File;

import javax.servlet.http.HttpServletRequest;

import org.json.JSONObject;

import com.wb.openplatform.accounts.util.AccountParamesUtil;
import com.wb.openplatform.accounts.util.AccountUtil;
import com.wb.openplatform.enterprise.util.WeiXinUtil;
import com.wb.util.LogUtil;

/**
 * 操作图文消息
 * <AUTHOR>
 *
 */
public class MessageService {
	private static String addMaterial_url = "https://api.weixin.qq.com/cgi-bin/material/add_news?access_token=ACCESS_TOKEN";
	private static String material_url = "https://api.weixin.qq.com/cgi-bin/material/batchget_material?access_token=ACCESS_TOKEN";
	private static String Permanent_url = "https://api.weixin.qq.com/cgi-bin/material/get_material?access_token=ACCESS_TOKEN";
	private static String updateMate_url = "https://api.weixin.qq.com/cgi-bin/material/update_news?access_token=ACCESS_TOKEN";
	private static String deleteMate_url = "https://api.weixin.qq.com/cgi-bin/material/del_material?access_token=ACCESS_TOKEN";
	private static String addTemporary_url = "https://api.weixin.qq.com/cgi-bin/media/upload?access_token=ACCESS_TOKEN&type=TYPE";
	private static String selTemporary_url = "https://api.weixin.qq.com/cgi-bin/media/get?access_token=ACCESS_TOKEN&media_id=MEDIA_ID";
	private static String tags_url = "https://api.weixin.qq.com/cgi-bin/tags/get?access_token=ACCESS_TOKEN";
	private static String user_url = "https://api.weixin.qq.com/cgi-bin/user/get?access_token=ACCESS_TOKEN&next_openid=NEXT_OPENID";
	private static String uploadMaterial_url = "https://api.weixin.qq.com/cgi-bin/material/add_material?access_token=ACCESS_TOKEN&type=TYPE";
	private static String uploadimg_url = "https://api.weixin.qq.com/cgi-bin/media/uploadimg?access_token=ACCESS_TOKEN";
	private static String messTag_url = "https://api.weixin.qq.com/cgi-bin/message/mass/sendall?access_token=ACCESS_TOKEN";
	private static String messOpen_url = "https://api.weixin.qq.com/cgi-bin/message/mass/send?access_token=ACCESS_TOKEN";

	public static JSONObject Sendmessage(String accessToken, HttpServletRequest request) {
		JSONObject jsonObject = null;
		return jsonObject;
	}

	//新增图文素材 
	public static JSONObject addMaterial(String material, HttpServletRequest request) {
		// 重新加载全局变量
		AccountParamesUtil.init();
		String accessToken = AccountUtil.getAccessToken(AccountParamesUtil.grant_Type, AccountParamesUtil.appId,
				AccountParamesUtil.appSecret, "MD").getToken();
		//2.获取请求的url  
		String mat_url = addMaterial_url.replace("ACCESS_TOKEN", accessToken);
		//3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "POST", material);

		//4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	//获取素材列表
	public static JSONObject selectMaterial(String material, HttpServletRequest request) {
		// 重新加载全局变量
		AccountParamesUtil.init();
		String accessToken = AccountUtil.getAccessToken(AccountParamesUtil.grant_Type, AccountParamesUtil.appId,
				AccountParamesUtil.appSecret, "MD").getToken();
		//2.获取请求的url  
		String mat_url = material_url.replace("ACCESS_TOKEN", accessToken);
		//3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "POST", material);

		//4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	//获取永久素材图文
	public static JSONObject selectPermanentMaterial(String media_id, HttpServletRequest request) {
		// 重新加载全局变量
		AccountParamesUtil.init();
		String accessToken = AccountUtil.getAccessToken(AccountParamesUtil.grant_Type, AccountParamesUtil.appId,
				AccountParamesUtil.appSecret, "MD").getToken();
		//2.获取请求的url  
		String mat_url = Permanent_url.replace("ACCESS_TOKEN", accessToken);
		//3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "POST", media_id);

		//4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	//修改永久图文素材
	public static JSONObject updateMaterial(String material, HttpServletRequest request) {
		// 重新加载全局变量
		AccountParamesUtil.init();
		String accessToken = AccountUtil.getAccessToken(AccountParamesUtil.grant_Type, AccountParamesUtil.appId,
				AccountParamesUtil.appSecret, "MD").getToken();
		//2.获取请求的url  
		String mat_url = updateMate_url.replace("ACCESS_TOKEN", accessToken);
		//3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "POST", material);

		//4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	//删除永久图文素材
	public static JSONObject deleteMaterial(String media_id, HttpServletRequest request) {
		// 重新加载全局变量
		AccountParamesUtil.init();
		String accessToken = AccountUtil.getAccessToken(AccountParamesUtil.grant_Type, AccountParamesUtil.appId,
				AccountParamesUtil.appSecret, "MD").getToken();
		//2.获取请求的url  
		String mat_url = deleteMate_url.replace("ACCESS_TOKEN", accessToken);
		//3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "POST", media_id);

		//4.错误消息处理
		if (null != jsonObject) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	//新增临时图文素材
	public static JSONObject addtemporaryMaterial(String file, String type, HttpServletRequest request) {
		// 重新加载全局变量
		AccountParamesUtil.init();
		String accessToken = AccountUtil.getAccessToken(AccountParamesUtil.grant_Type, AccountParamesUtil.appId,
				AccountParamesUtil.appSecret, "MD").getToken();
		//2.获取请求的url  
		String mat_url = addTemporary_url.replace("ACCESS_TOKEN", accessToken).replace("TYPE", type);
		//3.调用接口，发送请求
		JSONObject jsonObject = AccountUtil.uploadTempMaterial(mat_url, file);
		//4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	//获取临时图文素材
	public static File selectTemporaryMaterial(String file, String media_id, String fileName,
			HttpServletRequest request) {
		// 重新加载全局变量
		AccountParamesUtil.init();
		String accessToken = AccountUtil.getAccessToken(AccountParamesUtil.grant_Type, AccountParamesUtil.appId,
				AccountParamesUtil.appSecret, "MD").getToken();
		//2.获取请求的url  
		String mat_url = selTemporary_url.replace("ACCESS_TOKEN", accessToken).replace("MEDIA_ID", media_id);
		File fileUrl = AccountUtil.getPermanentMaterial(mat_url, media_id, file, fileName);
		//4.错误消息处理
		if (fileUrl == null) {
			LogUtil.error(request, "获取临时图文素材失败");
		}
		return fileUrl;
	}

	//获取已创建的标签
	public static JSONObject selectTags(HttpServletRequest request) {
		// 重新加载全局变量
		AccountParamesUtil.init();
		String accessToken = AccountUtil.getAccessToken(AccountParamesUtil.grant_Type, AccountParamesUtil.appId,
				AccountParamesUtil.appSecret, "MD").getToken();
		//2.获取请求的url  
		String mat_url = tags_url.replace("ACCESS_TOKEN", accessToken);
		//3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "GET", null);

		//4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	//获取多媒体永久素材
	public static File getPermanentMaterial(String file, String media_id, String fileName, HttpServletRequest request) {
		// 重新加载全局变量
		AccountParamesUtil.init();
		String accessToken = AccountUtil.getAccessToken(AccountParamesUtil.grant_Type, AccountParamesUtil.appId,
				AccountParamesUtil.appSecret, "MD").getToken();
		String mat_url = Permanent_url.replace("ACCESS_TOKEN", accessToken);
		File fileUrl = AccountUtil.getPermanentMaterial(mat_url, media_id, file, fileName);
		//4.错误消息处理
		if (fileUrl == null) {
			LogUtil.error(request, "获取多媒体永久素材失败");
		}
		return fileUrl;
	}

	//获取用户列表
	public static JSONObject selectUserList(String next_openid, HttpServletRequest request) {
		// 重新加载全局变量
		AccountParamesUtil.init();
		String accessToken = AccountUtil.getAccessToken(AccountParamesUtil.grant_Type, AccountParamesUtil.appId,
				AccountParamesUtil.appSecret, "MD").getToken();
		//2.获取请求的url  
		String mat_url = user_url.replace("ACCESS_TOKEN", accessToken).replace("NEXT_OPENID", next_openid);
		//3.调用接口，发送请求，创建成员
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "GET", null);

		//4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	//上传图文内容素材
	public static JSONObject uploadImg(String file, HttpServletRequest request) {
		// 重新加载全局变量
		AccountParamesUtil.init();
		String accessToken = AccountUtil.getAccessToken(AccountParamesUtil.grant_Type, AccountParamesUtil.appId,
				AccountParamesUtil.appSecret, "MD").getToken();
		String mat_url = uploadimg_url.replace("ACCESS_TOKEN", accessToken);
		JSONObject jsonObject = AccountUtil.uploadImg(mat_url, file);
		//4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	//上传其他类型永久性素材
	public static JSONObject uploadMaterial(String type, String file, HttpServletRequest request) {
		// 重新加载全局变量
		AccountParamesUtil.init();
		String accessToken = AccountUtil.getAccessToken(AccountParamesUtil.grant_Type, AccountParamesUtil.appId,
				AccountParamesUtil.appSecret, "MD").getToken();
		String mat_url = uploadMaterial_url.replace("ACCESS_TOKEN", accessToken).replace("TYPE", type);
		JSONObject jsonObject = AccountUtil.addMaterialEver(mat_url, file);
		//4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	//新建群发-标签
	public static JSONObject massMessageTag(String Tag, HttpServletRequest request) {
		// 重新加载全局变量
		AccountParamesUtil.init();
		String accessToken = AccountUtil.getAccessToken(AccountParamesUtil.grant_Type, AccountParamesUtil.appId,
				AccountParamesUtil.appSecret, "MD").getToken();
		String mat_url = messTag_url.replace("ACCESS_TOKEN", accessToken);
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "POST", Tag);
		//4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

	//新建群发-用戶
	public static JSONObject massMessageOpen(String open, HttpServletRequest request) {
		// 重新加载全局变量
		AccountParamesUtil.init();
		String accessToken = AccountUtil.getAccessToken(AccountParamesUtil.grant_Type, AccountParamesUtil.appId,
				AccountParamesUtil.appSecret, "MD").getToken();
		String mat_url = messOpen_url.replace("ACCESS_TOKEN", accessToken);
		JSONObject jsonObject = WeiXinUtil.httpRequest(mat_url, "POST", open);
		//4.错误消息处理
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.error(request, jsonObject.getString("errmsg"));
			}
		}
		return jsonObject;
	}

}