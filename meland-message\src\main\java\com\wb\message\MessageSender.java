package com.wb.message;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

import org.json.JSONObject;

import com.wb.util.DbUtil;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;

/**
 * 消息发送类
 * 
 * <AUTHOR>
 *
 */
public final class MessageSender {

	/**
	 * 消息生产者对象
	 */
	private static final MessageSender INSTANCE = new MessageSender();

	/**
	 * 单例模式Producer是线程安全的,可以多线程共享一个实例
	 * 
	 * @return
	 */
	public static final MessageSender getInstance() {
		return INSTANCE;
	}

	/**
	 * 发送kafaka消息
	 * 
	 * @param code
	 *            消息规则编码
	 * @param title
	 *            消息标题
	 * @param content
	 *            消息内容
	 * @param users
	 *            接收用户ID，多个用户使用逗号“,”分割
	 * @param data 追加参数
	 */
	public static void send(String code, String title, String content, String users, String data) {
		// 组装消息对象
		JSONObject message = new JSONObject();
		message.put(Constants.MSG_CODE, code);
		message.put(Constants.MSG_TITLE, title);
		message.put(Constants.MSG_CONTENT, content);
		message.put(Constants.MSG_USERS, users);
		message.put(Constants.MSG_DATA, data);

		MessageSender sender = MessageSender.getInstance();
		//sender.init();
		sender.sendMsg(message);
	}

	public static void sendByRole(String code, String title, String content, String roleIds, String data) {
		String users = "";
		try {
			Connection conn = null;
			Statement st = null;
			ResultSet rs = null;

			try {
				conn = DbUtil.getConnection();
				st = conn.createStatement();
				rs = st.executeQuery(
						"SELECT GROUP_CONCAT(DISTINCT USER_ID) as users FROM wb_user_role WHERE ROLE_ID IN("
								+ StringUtil.joinQuote(roleIds.split(",")) + ")");
				if (rs.next())
					users = rs.getString("users");
			} finally {
				DbUtil.close(rs);
				DbUtil.close(st);
				DbUtil.close(conn);
			}
			send(code, title, content, users, data);
		} catch (Throwable e) {
			throw new RuntimeException(e);
		}
	}

	public static void sendByOrg(String code, String title, String content, String orgIds, String data) {
		String users = "";
		try {
			Connection conn = null;
			Statement st = null;
			ResultSet rs = null;

			try {
				conn = DbUtil.getConnection();
				st = conn.createStatement();
				rs = st.executeQuery(
						"SELECT GROUP_CONCAT(DISTINCT user_id) as users FROM hr_info_dept WHERE dept_id IN("
								+ StringUtil.joinQuote(orgIds.split(",")) + ")");
				if (rs.next())
					users = rs.getString("users");
			} finally {
				DbUtil.close(rs);
				DbUtil.close(st);
				DbUtil.close(conn);
			}
			send(code, title, content, users, data);
		} catch (Throwable e) {
			throw new RuntimeException(e);
		}
	}

	public static void sendByClass(String code, String title, String content, String classIds, String data) {
		String users = "";
		try {
			Connection conn = null;
			Statement st = null;
			ResultSet rs = null;

			try {
				conn = DbUtil.getConnection();
				st = conn.createStatement();
				rs = st.executeQuery("SELECT GROUP_CONCAT(id) as users FROM stu_info WHERE class_id IN("
						+ StringUtil.joinQuote(classIds.split(",")) + ")");
				if (rs.next())
					users = rs.getString("users");
			} finally {
				DbUtil.close(rs);
				DbUtil.close(st);
				DbUtil.close(conn);
			}
			send(code, title, content, users, data);
		} catch (Throwable e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 发送消息
	 * 
	 * @param message 消息内容
	 */
	public void sendMsg(final JSONObject message) {
		try {
			Constants.MSG_QUEUE.put(message);
		} catch (InterruptedException e) {
			LogUtil.error("发送消息异常：" + e);
		}
	}
}