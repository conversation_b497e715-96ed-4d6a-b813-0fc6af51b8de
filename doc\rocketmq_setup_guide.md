# RocketMQ 5.x 单机部署与测试指南 (Alibaba Cloud Linux 3)

本文档提供在 Alibaba Cloud Linux 3 系统上安装、配置、启动和测试单机 RocketMQ 5.x 的步骤。

**前提条件:**

*   已安装 Java Development Kit (JDK) 1.8 或更高版本。
    ```bash
    # 检查 Java 版本
    java -version
    # 如果未安装，执行:
    # sudo yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel
    ```
*   已安装基础工具 `wget`, `tar`, `unzip`。
    ```bash
    # sudo yum install -y wget tar unzip
    ```

## 1. 下载与解压 RocketMQ

```bash
# 1. 创建 RocketMQ 安装目录
sudo mkdir -p /opt/rocketmq
cd /opt/rocketmq

# 2. 从 Apache 官网下载 RocketMQ 二进制包 (请检查并替换为最新稳定版本链接)
# 访问 https://rocketmq.apache.org/download 获取最新链接
sudo wget https://dlcdn.apache.org/rocketmq/5.2.0/rocketmq-all-5.2.0-bin-release.zip # 示例版本 5.2.0

# 3. 解压
sudo unzip rocketmq-all-*-bin-release.zip
# 重命名方便管理 (可选)
sudo mv rocketmq-all-*-bin-release rocketmq-5.2.0 # 替换为对应版本号

# 4. 进入 RocketMQ 目录
cd rocketmq-5.2.0 # 替换为对应版本号
```

## 2. 配置 Broker

在启动服务前，需要配置 Broker，特别是指定 NameServer 地址。

```bash
# (重要) 配置 Broker: 指定 NameServer 地址
# 编辑 Broker 配置文件
# sudo vim /opt/rocketmq/rocketmq-5.2.0/conf/broker.conf
# 找到 (或添加) `namesrvAddr` 配置项，确保其指向正确的 NameServer 地址 (本机通常是 localhost:9876)，并取消注释 (去掉行首 '#')
# 例如:
# namesrvAddr = localhost:9876

# 同时，检查并根据需要修改存储路径，如 storePathRootDir, storePathCommitLog 等，
# 生产环境强烈建议使用绝对路径指向高性能磁盘，例如 /data/rocketmq/store，并确保目录存在且 RocketMQ 运行用户可写。
# 示例:
# storePathRootDir = /opt/rocketmq/store/broker # 使用安装目录下的store/broker
# storePathCommitLog = /opt/rocketmq/store/broker/commitlog
# storePathConsumeQueue = /opt/rocketmq/store/broker/consumequeue
# storePathIndex = /opt/rocketmq/store/broker/index
# brokerClusterName = DefaultCluster # 保持默认或修改
# brokerName = broker-a             # 保持默认或修改
# brokerId = 0                     # Master Broker ID 必须是 0
# deleteWhen = 04                  # 文件保留时间 (小时)
# fileReservedTime = 48            # 文件保留时间 (小时)
# brokerRole = ASYNC_MASTER       # 异步 Master
# flushDiskType = ASYNC_FLUSH      # 异步刷盘

# (确保相关存储目录存在，例如 /opt/rocketmq/store/broker)
# sudo mkdir -p /opt/rocketmq/store/broker
# sudo chown -R your_rocketmq_user:your_rocketmq_group /opt/rocketmq/store/broker # 如果使用专用用户
```

## 3. 使用 Systemd 启动和管理服务

建议使用 Systemd 来管理 NameServer 和 Broker 服务。

### 3.1 创建 NameServer 服务文件

```bash
sudo bash -c 'cat > /etc/systemd/system/rocketmq-namesrv.service' << EOF
[Unit]
Description=RocketMQ Name Server
After=network.target

[Service]
# 生产环境建议创建专用用户和组
User=root
Group=root
WorkingDirectory=/opt/rocketmq/rocketmq-5.2.0 # 修改为你的实际路径和版本
# 设置 Java 和 RocketMQ Home 环境变量 (重要!)
# 请将 JAVA_HOME 路径替换为你的实际 JDK 8 安装路径
Environment="JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.442.b06-*******.al8.x86_64"
Environment="ROCKETMQ_HOME=/opt/rocketmq/rocketmq-5.2.0"
# 直接调用 Java 启动 NameServer
ExecStart=/usr/bin/java -server -Xms512m -Xmx512m -Xmn256m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=320m -cp conf:lib/* org.apache.rocketmq.namesrv.NamesrvStartup
Restart=on-failure
# (可选) 增加文件描述符限制
LimitNOFILE=65535

[Install]
WantedBy=multi-user.target
EOF
```

### 3.2 创建 Broker 服务文件

```bash
sudo bash -c 'cat > /etc/systemd/system/rocketmq-broker.service' << EOF
[Unit]
Description=RocketMQ Broker Server
After=network.target rocketmq-namesrv.service

[Service]
# 生产环境建议创建专用用户和组
User=root
Group=root
WorkingDirectory=/opt/rocketmq/rocketmq-5.2.0 # 修改为你的实际路径和版本
# 设置 Java 和 RocketMQ Home 环境变量 (重要!)
# 请将 JAVA_HOME 路径替换为你的实际 JDK 8 安装路径
Environment="JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.442.b06-*******.al8.x86_64"
Environment="ROCKETMQ_HOME=/opt/rocketmq/rocketmq-5.2.0"
# 直接调用 Java 启动 Broker，并指定配置文件
# 根据需要调整内存参数 (-Xms, -Xmx, -Xmn) 和 GC 参数
ExecStart=/usr/bin/java -server -Xms1g -Xmx1g -Xmn512m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=320m -cp conf:lib/* org.apache.rocketmq.broker.BrokerStartup -c conf/broker.conf
Restart=on-failure
# (可选) 增加文件描述符限制
LimitNOFILE=65535

[Install]
WantedBy=multi-user.target
EOF
```

### 3.3 启动与管理

```bash
# 重新加载 systemd 配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start rocketmq-namesrv
sudo systemctl start rocketmq-broker

# 设置开机自启
sudo systemctl enable rocketmq-namesrv
sudo systemctl enable rocketmq-broker

# 检查服务状态
sudo systemctl status rocketmq-namesrv
sudo systemctl status rocketmq-broker

# 停止服务
# sudo systemctl stop rocketmq-broker
# sudo systemctl stop rocketmq-namesrv

# 重启服务
# sudo systemctl restart rocketmq-namesrv
# sudo systemctl restart rocketmq-broker

# 查看启动日志 (如果服务启动失败)
# journalctl -u rocketmq-namesrv.service
# journalctl -u rocketmq-broker.service
# 或者查看 RocketMQ 自身日志 (如果配置了且可写)
# tail -f /root/logs/rocketmqlogs/namesrv.log
# tail -f /root/logs/rocketmqlogs/broker.log
```

## 4. 测试与管理 RocketMQ

安装并启动服务后，可以通过以下命令行方式进行基本测试和管理。

### 4.1 设置环境变量 (当前会话)

为了方便使用 `mqadmin` 和示例工具，建议先设置 NameServer 地址环境变量：

```bash
# 设置 NameServer 地址 (如果 NameServer 在本机)
export NAMESRV_ADDR="localhost:9876"

# (可选) 设置 ROCKETMQ_HOME (如果脚本需要)
export ROCKETMQ_HOME="/opt/rocketmq/rocketmq-5.2.0"

# 进入 RocketMQ bin 目录
cd /opt/rocketmq/rocketmq-5.2.0/bin
```
*注意：如果希望环境变量永久生效，可以将其添加到 `/etc/profile.d/rocketmq.sh` 或用户的 `.bashrc` 文件中。*

### 4.2 使用 mqadmin 管理工具

`mqadmin` 是强大的命令行管理工具。

```bash
# (确保已设置 NAMESRV_ADDR 或在命令后加 -n localhost:9876)

# 查看集群列表 (确认 Broker 已注册)
sh mqadmin clusterList
# 预期应该能看到类似 DefaultCluster 和 Broker 的信息

# 创建一个用于测试的 Topic (例如 TopicTest)
# -t 主题名 -c 集群名 (根据你的 broker.conf 配置)
sh mqadmin updateTopic -t TopicTest -c DefaultCluster -r 4 -w 4

# 查看 TopicTest 的路由信息
sh mqadmin topicRoute -t TopicTest
# 预期应该能看到 TopicTest 分配到的 Broker 地址

# 查看 TopicTest 的状态
sh mqadmin topicStatus -t TopicTest

# 查看 Broker 状态 (替换为实际 Broker IP 和端口, 例如 *************:10911)
# sh mqadmin brokerStatus -b <Broker IP>:<Broker Port>

# 查看更多 mqadmin 命令
# sh mqadmin help
```

### 4.3 使用示例程序测试收发消息

RocketMQ 自带了生产者和消费者示例。

```bash
# (确保已设置 NAMESRV_ADDR 并且 TopicTest 已创建)

# 1. 在一个终端窗口启动消费者 (它会持续运行等待消息)
sh tools.sh org.apache.rocketmq.example.quickstart.Consumer

# 2. 在另一个终端窗口启动生产者 (它会发送一批消息然后退出)
sh tools.sh org.apache.rocketmq.example.quickstart.Producer
```
*观察消费者终端的输出，看是否能打印出类似 `Receive New Messages: [MessageExt...` 的信息，表明消息收发成功。按 `Ctrl+C` 停止消费者。*

## 5. (可选) 卸载 RocketMQ

如果需要卸载 RocketMQ，请参考以下步骤 (谨慎操作！):

```bash
# 1. 停止服务
# sudo systemctl stop rocketmq-broker
# sudo systemctl stop rocketmq-namesrv

# 2. 禁用服务
# sudo systemctl disable rocketmq-broker
# sudo systemctl disable rocketmq-namesrv

# 3. 删除 systemd 服务文件
# sudo rm /etc/systemd/system/rocketmq-namesrv.service
# sudo rm /etc/systemd/system/rocketmq-broker.service
# sudo systemctl daemon-reload

# 4. 删除安装目录
# sudo rm -rf /opt/rocketmq/rocketmq-5.2.0 # 使用实际版本号

# 5. (!!!警告!!!) 删除数据和日志文件 (确认不再需要!)
# 检查 broker.conf 中的 storePathRootDir 等路径配置!
# sudo rm -rf /root/logs/rocketmqlogs # 默认日志路径
# sudo rm -rf /root/store # 默认数据路径
# 或者删除自定义路径，例如:
# sudo rm -rf /data/rocketmq/store
# sudo rm -rf /opt/rocketmq/store
# sudo rm -rf /var/log/rocketmq

# 6. (可选) 删除下载的压缩包
# sudo rm /opt/rocketmq/rocketmq-all-*-bin-release.zip
```

---
*本文档仅供参考，具体配置请根据实际需求和服务器资源进行调整。建议参考 RocketMQ 官方文档获取更详细信息。* 