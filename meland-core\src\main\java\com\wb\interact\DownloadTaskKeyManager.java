package com.wb.interact;

/**
 * 下载任务Redis键管理器
 * 负责管理下载任务相关的Redis键前缀和键生成
 */
public class DownloadTaskKeyManager {
    /**
     * Redis键前缀，用于存储下载任务信息
     */
    private static final String REDIS_DOWNLOAD_TASKS_PREFIX = "download_tasks:";
    private static final String REDIS_DOWNLOAD_ACCESS_PREFIX = "download_tasks_access:";
    private static final String REDIS_DOWNLOAD_TASKS_CANCELLED_PREFIX = "download_tasks_cancelled:";
    private static final String REDIS_DOWNLOAD_TASKS_HEARTBEAT_PREFIX = "download_tasks_heartbeat:";
    private static final String REDIS_DOWNLOAD_SEMAPHORE_KEY = "DOWNLOAD_SEMAPHORE_COUNTER";
    
    // New Keys for Set/SortedSet
    private static final String REDIS_ACTIVE_TASKS_SET_KEY = "download:active_tasks";
    private static final String REDIS_ACCESS_TIMES_ZSET_KEY = "download:access_times";
    private static final String REDIS_ZOMBIE_TASKS_SET_KEY = "download:zombie_tasks";

    /**
     * 获取下载任务文件路径的Redis键
     *
     * @param downloadId 下载ID
     * @return Redis键
     */
    public static String getDownloadTaskKey(String downloadId) {
        return REDIS_DOWNLOAD_TASKS_PREFIX + downloadId;
    }

    /**
     * 获取下载任务错误信息的Redis键
     *
     * @param downloadId 下载ID
     * @return Redis键
     */
    public static String getDownloadErrorKey(String downloadId) {
        return getDownloadTaskKey(downloadId) + "_error";
    }

    /**
     * 获取下载任务开始时间的Redis键
     *
     * @param downloadId 下载ID
     * @return Redis键
     */
    public static String getDownloadStartTimeKey(String downloadId) {
        return getDownloadTaskKey(downloadId) + "_start_time";
    }

    /**
     * 获取下载任务最后访问时间的Redis键
     *
     * @param downloadId 下载ID
     * @return Redis键
     */
    public static String getDownloadAccessKey(String downloadId) {
        return REDIS_DOWNLOAD_ACCESS_PREFIX + downloadId;
    }

    /**
     * 获取下载任务取消状态的Redis键
     *
     * @param downloadId 下载ID
     * @return Redis键
     */
    public static String getDownloadCancelledKey(String downloadId) {
        return REDIS_DOWNLOAD_TASKS_CANCELLED_PREFIX + downloadId;
    }

    /**
     * 获取下载任务心跳的Redis键
     *
     * @param downloadId 下载ID
     * @return Redis键
     */
    public static String getDownloadHeartbeatKey(String downloadId) {
        return REDIS_DOWNLOAD_TASKS_HEARTBEAT_PREFIX + downloadId;
    }
    
    /**
     * 获取下载进度信息的Redis键
     * 
     * @param downloadId 下载ID
     * @return Redis键
     */
    public static String getDownloadProgressKey(String downloadId) {
        return getDownloadTaskKey(downloadId) + "_progress";
    }
    
    /**
     * 获取下载进度百分比的Redis键
     * 
     * @param downloadId 下载ID
     * @return Redis键
     */
    public static String getDownloadProgressPercentageKey(String downloadId) {
        return getDownloadTaskKey(downloadId) + "_progress_percentage";
    }
    
    /**
     * 获取下载进度时间戳的Redis键
     * 
     * @param downloadId 下载ID
     * @return Redis键
     */
    public static String getDownloadProgressTimestampKey(String downloadId) {
        return getDownloadTaskKey(downloadId) + "_progress_timestamp";
    }
    
    /**
     * 获取下载任务完成标记的Redis键
     * 
     * @param downloadId 下载ID
     * @return Redis键
     */
    public static String getDownloadCompletedKey(String downloadId) {
        return getDownloadTaskKey(downloadId) + "_completed";
    }
    
    /**
     * 获取下载信号量计数器的Redis键
     *
     * @return Redis键
     */
    public static String getDownloadSemaphoreKey() {
        return REDIS_DOWNLOAD_SEMAPHORE_KEY;
    }

    /**
     * 获取活跃任务Set的Redis键
     * @return Redis Key
     */
    public static String getActiveTasksSetKey() {
        return REDIS_ACTIVE_TASKS_SET_KEY;
    }

    /**
     * 获取访问时间SortedSet的Redis键
     * @return Redis Key
     */
    public static String getAccessTimesSortedSetKey() {
        return REDIS_ACCESS_TIMES_ZSET_KEY;
    }

    /**
     * 获取僵尸任务Set的Redis键
     * @return Redis Key
     */
    public static String getZombieTasksSetKey() {
        return REDIS_ZOMBIE_TASKS_SET_KEY;
    }
} 