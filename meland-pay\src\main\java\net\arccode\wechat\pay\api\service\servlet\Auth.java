package net.arccode.wechat.pay.api.service.servlet;

import java.io.IOException;
import java.net.URLEncoder;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.arccode.config.AccountsConfig;

/**
 * 授权
 * 
 * <AUTHOR>
 *
 */
public class Auth extends HttpServlet {
	private static final long serialVersionUID = 1L;

	/**
	 * @see HttpServlet#HttpServlet()
	 */
	public Auth() {
		super();
		// TODO Auto-generated constructor stub
	}

	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		//加载配置文件
		AccountsConfig.init();
		// 获取 USER-AGENT
		String appStr = request.getHeader("user-agent");
		// 微信
		String MicroMessenger = "MicroMessenger";
		// 支付宝
		String ApliPayClients = "AlipayClient";
		// 系统域名地址
		String backUri = AccountsConfig.SystemUrl;

		if (appStr.indexOf(MicroMessenger) != -1) {
			// 公众号APPID
			String gzhAppId = AccountsConfig.appId;
			// 授权后要跳转的链接
			backUri = URLEncoder.encode(backUri + "pay/auth.jsp?type=wechat","utf-8");
			//授权地址
			String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + gzhAppId + "&redirect_uri=" + backUri + "&response_type=code&scope=snsapi_base&state=STATE&connect_redirect=1#wechat_redirect";
			response.sendRedirect(url);
		} else if (appStr.indexOf(ApliPayClients) != -1) {
			response.sendRedirect(backUri + "pay/auth.jsp?type=ailpay");
		}else {
			response.sendRedirect(backUri + "pay/error.html");
		}
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		// TODO Auto-generated method stub
		doGet(request, response);
	}

}
