package com.wb.openplatform.accounts.util;

import java.io.BufferedReader;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.URL;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;

import org.apache.http.Header;
import org.apache.http.HeaderElement;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.BasicHttpContext;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wb.openplatform.enterprise.util.MyX509TrustManager;
import com.wb.openplatform.enterprise.voucher.AccessToken;
import com.wb.util.DbUtil;

public class AccountUtil {
	private static Logger log = LoggerFactory.getLogger(AccountUtil.class);

	//获取access_token的接口地址（GET） 限200（次/天）  
	public final static String access_token_url = "https://api.weixin.qq.com/cgi-bin/token?grant_type={grantType}&appid={appId}&secret={secret}";

	/**
	 * 1.发起https请求并获取结果 
	 *  
	 * @param requestUrl 请求地址 
	 * @param requestMethod 请求方式（GET、POST） 
	 * @param outputStr 提交的数据 
	 * @return JSONObject(通过JSONObject.get(key)的方式获取json对象的属性值) 
	 */
	public static JSONObject httpRequest(String requestUrl, String requestMethod, String outputStr) {
		JSONObject jsonObject = null;
		StringBuffer buffer = new StringBuffer();
		try {
			// 创建SSLContext对象，并使用我们指定的信任管理器初始化  
			TrustManager[] tm = { new MyX509TrustManager() };
			SSLContext sslContext = SSLContext.getInstance("SSL", "SunJSSE");
			sslContext.init(null, tm, new java.security.SecureRandom());
			// 从上述SSLContext对象中得到SSLSocketFactory对象  
			SSLSocketFactory ssf = sslContext.getSocketFactory();

			URL url = new URL(requestUrl);
			HttpsURLConnection httpUrlConn = (HttpsURLConnection) url.openConnection();
			httpUrlConn.setSSLSocketFactory(ssf);

			httpUrlConn.setDoOutput(true);
			httpUrlConn.setDoInput(true);
			httpUrlConn.setUseCaches(false);
			//协议
			httpUrlConn.setRequestProperty("accept", "*/*");
			httpUrlConn.setRequestProperty("Accept-Charset", "UTF-8");
			httpUrlConn.setRequestProperty("contentType", "UTF-8");
			httpUrlConn.setRequestProperty("Content-Type", "application/json");
			// 设置请求方式（GET/POST）  
			httpUrlConn.setRequestMethod(requestMethod);

			if ("GET".equalsIgnoreCase(requestMethod))
				httpUrlConn.connect();

			// 当有数据需要提交时  
			if (null != outputStr) {
				OutputStream outputStream = httpUrlConn.getOutputStream();
				// 注意编码格式，防止中文乱码  
				outputStream.write(outputStr.getBytes("UTF-8"));
				outputStream.close();
			}

			// 将返回的输入流转换成字符串  
			InputStream inputStream = httpUrlConn.getInputStream();
			InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
			BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

			String str = null;
			while ((str = bufferedReader.readLine()) != null) {
				buffer.append(str);
			}
			bufferedReader.close();
			inputStreamReader.close();
			// 释放资源  
			inputStream.close();
			inputStream = null;
			httpUrlConn.disconnect();
			jsonObject = new JSONObject(buffer.toString());
		} catch (ConnectException ce) {
			log.error("Weixin server connection timed out.");
		} catch (Exception e) {
			log.error("https request error:{}", e);
		}
		return jsonObject;
	}

	/** 
	 * 2.发起http请求获取返回结果 
	 *  
	 * @param requestUrl 请求地址 
	 * @return 
	 */
	public static String httpRequest(String requestUrl) {
		StringBuffer buffer = new StringBuffer();
		try {
			URL url = new URL(requestUrl);
			HttpURLConnection httpUrlConn = (HttpURLConnection) url.openConnection();

			httpUrlConn.setDoOutput(false);
			httpUrlConn.setDoInput(true);
			httpUrlConn.setUseCaches(false);

			httpUrlConn.setRequestMethod("GET");
			httpUrlConn.connect();

			// 将返回的输入流转换成字符串  
			InputStream inputStream = httpUrlConn.getInputStream();
			//InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "utf-8");  
			InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
			BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

			String str = null;
			while ((str = bufferedReader.readLine()) != null) {
				buffer.append(str);

			}
			bufferedReader.close();
			inputStreamReader.close();
			// 释放资源  
			inputStream.close();
			inputStream = null;
			httpUrlConn.disconnect();

		} catch (Exception e) {
		}
		return buffer.toString();
	}

	/**
	 * 3.发送https请求之获取临时素材 GET
	 * @param requestUrl
	 * @param savePath  文件的保存路径，此时还缺一个扩展名
	 * @return
	 * @throws Exception
	 */
	public static File downloadMedia(String url, String fileDir, String fileName) throws Exception {
		//1.生成一个请求
		HttpGet httpGet = new HttpGet(url);
		//2.配置请求属性
		RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(100000).setConnectTimeout(100000).build();
		httpGet.setConfig(requestConfig);

		//3.发起请求，获取响应信息    
		//3.1 创建httpClient 
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;

		//4.设置本地保存的文件  
		//File file = new File(fileDir);
		File file = null;
		try {
			//5. 发起请求，获取响应信息    
			response = httpClient.execute(httpGet, new BasicHttpContext());
			System.out.println("HttpStatus.SC_OK:" + HttpStatus.SC_OK);
			System.out.println("response.getStatusLine().getStatusCode():" + response.getStatusLine().getStatusCode());
			// System.out.println("http-header:"+JSON.toJSONString( response.getAllHeaders() ));  
			//System.out.println("http-filename:"+getFileName(response) );  

			//请求成功  
			if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {

				//6.取得请求内容  
				HttpEntity entity = response.getEntity();

				if (entity != null) {
					//这里可以得到文件的类型 如image/jpg /zip /tiff 等等 但是发现并不是十分有效，有时明明后缀是.rar但是取到的是null，这点特别说明  
					System.out.println(entity.getContentType());
					//可以判断是否是文件数据流  
					System.out.println(entity.isStreaming());

					//6.1 输出流
					//6.1.1获取文件名，拼接文件路径
					String _fileName = fileName != "" ? fileName : getFileName(response);
					fileDir = fileDir + _fileName;
					file = new File(fileDir);
					//6.1.2根据文件路径获取输出流
					FileOutputStream output = new FileOutputStream(file);

					//6.2 输入流：从钉钉服务器返回的文件流，得到网络资源并写入文件  
					InputStream input = entity.getContent();

					//6.3 将数据写入文件：将输入流中的数据写入到输出流
					byte b[] = new byte[1024];
					int j = 0;
					while ((j = input.read(b)) != -1) {
						output.write(b, 0, j);
					}
					output.flush();
					output.close();
				}
				if (entity != null) {
					EntityUtils.consume(entity);
					//entity.consumeContent();  
				}
			}
		} catch (IOException e) {
			System.out.println("request url=" + url + ", exception, msg=" + e.getMessage());
			e.printStackTrace();
		} finally {
			if (response != null)
				try {
					//释放资源
					response.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			if (httpClient != null)
				try {
					//释放资源
					httpClient.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
		}
		return file;
	}

	/**
	 * 4.发送https请求之获取临时素材 POST
	 * @param url
	 * @param data
	 * @param fileDir
	 * @return
	 * @throws Exception
	 */
	public static File downloadMedia(String url, String data, String fileDir, String fileName) throws Exception {
		//1.生成一个请求
		HttpPost httpPost = new HttpPost(url);

		//2.配置请求属性
		//2.1 设置请求超时时间
		RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(100000).setConnectTimeout(100000).build();
		httpPost.setConfig(requestConfig);
		//2.2 设置数据传输格式-json
		httpPost.addHeader("Content-Type", "application/json");
		httpPost.addHeader("Accept-Charset", "utf-8");
		httpPost.addHeader("Accept-Encoding", "gzip");
		httpPost.addHeader("Accept-Language", "en-US,en");
		httpPost.addHeader("User-Agent",
				"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.22 (KHTML, like Gecko) Chrome/25.0.1364.160 Safari/537.22");

		//2.3 设置请求参数
		StringEntity requestEntity = new StringEntity(data, "utf-8");
		httpPost.setEntity(requestEntity);

		//3.发起请求，获取响应信息    
		//3.1 创建httpClient 
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;

		//4.设置本地保存的文件  
		//File file = new File(fileDir);
		File file = null;
		try {
			//5. 发起请求，获取响应信息    
			response = httpClient.execute(httpPost, new BasicHttpContext());
			System.out.println(response);
			//请求成功  
			if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {

				//6.取得请求内容  
				HttpEntity entity = response.getEntity();

				if (entity != null) {
					//这里可以得到文件的类型 如image/jpg /zip /tiff 等等 但是发现并不是十分有效，有时明明后缀是.rar但是取到的是null，这点特别说明  
					System.out.println(entity.getContentType());
					//可以判断是否是文件数据流  
					System.out.println(entity.isStreaming());

					//6.1 输出流
					//6.1.1获取文件名，拼接文件路径
					String _fileName = fileName != "" ? fileName : getFileName(response);
					fileDir = fileDir + _fileName;
					file = new File(fileDir);
					//6.1.2根据文件路径获取输出流
					FileOutputStream output = new FileOutputStream(file);

					//6.2 输入流：从服务器返回的文件流，得到网络资源并写入文件  
					InputStream input = entity.getContent();

					//6.3 将数据写入文件：将输入流中的数据写入到输出流
					byte b[] = new byte[1024];
					int j = 0;
					while ((j = input.read(b)) != -1) {
						output.write(b, 0, j);
					}
					output.flush();
					output.close();
				}
				if (entity != null) {
					EntityUtils.consume(entity);
					//entity.consumeContent();  
				}
			}
		} catch (IOException e) {
			System.out.println("request url=" + url + ", exception, msg=" + e.getMessage());
			e.printStackTrace();
		} finally {
			if (response != null)
				try {
					//释放资源
					response.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			if (httpClient != null)
				try {
					//释放资源
					httpClient.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
		}

		return file;
	}

	/**
	 * 5.上传素材
	 * @param path
	 * @param KK
	 * @param file
	 * @return
	 */
	public static String connectHttpsByPost(String path, String KK, File file) {
		URL urlObj = null;
		String result = null;
		BufferedReader reader = null;
		try {
			urlObj = new URL(path);
			//连接
			HttpURLConnection con = (HttpURLConnection) urlObj.openConnection();
			con.setDoInput(true);

			con.setDoOutput(true);

			con.setUseCaches(false); // post方式不能使用缓存

			// 设置请求头信息
			con.setRequestProperty("Connection", "Keep-Alive");
			con.setRequestProperty("Charset", "UTF-8");
			// 设置边界
			String BOUNDARY = "----------" + System.currentTimeMillis();
			con.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);

			// 请求正文信息
			// 第一部分：
			StringBuilder sb = new StringBuilder();
			sb.append("--"); // 必须多两道线
			sb.append(BOUNDARY);
			sb.append("\r\n");
			sb.append("Content-Disposition: form-data;name=\"media\";filelength=\"" + file.length() + "\";filename=\""

					+ file.getName() + "\"\r\n");
			sb.append("Content-Type:application/octet-stream\r\n\r\n");
			byte[] head = sb.toString().getBytes("utf-8");
			// 获得输出流
			OutputStream out = new DataOutputStream(con.getOutputStream());
			// 输出表头
			out.write(head);

			// 文件正文部分
			// 把文件已流文件的方式 推入到url中
			DataInputStream in = new DataInputStream(new FileInputStream(file));
			int bytes = 0;
			byte[] bufferOut = new byte[1024];
			while ((bytes = in.read(bufferOut)) != -1) {
				out.write(bufferOut, 0, bytes);
			}
			in.close();
			// 结尾部分
			byte[] foot = ("\r\n--" + BOUNDARY + "--\r\n").getBytes("utf-8");// 定义最后数据分隔线
			out.write(foot);
			out.flush();
			out.close();

			StringBuffer buffer = new StringBuffer();

			// 定义BufferedReader输入流来读取URL的响应
			reader = new BufferedReader(new InputStreamReader(con.getInputStream()));
			String line = null;
			while ((line = reader.readLine()) != null) {
				buffer.append(line);
			}
			if (result == null) {
				result = buffer.toString();
			}
		} catch (IOException e) {
			System.out.println("发送POST请求出现异常！" + e);
			e.printStackTrace();
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return result;
	}

	/**
	 * 6.重写获取access_token
	 */
	public static AccessToken getAccessToken(String grantType, String appid, String appsecret, String type) {
		AccessToken accessToken = null;
		JSONArray array = DbUtil
				.query("SELECT * FROM wb_token WHERE token_type = 'account' AND type = '" + type + "' ");
		if (array != null) {
			accessToken = new AccessToken();
			accessToken.setToken(array.getString(1));
		} else {
			String requestUrl = access_token_url.replace("{grantType}", grantType).replace("{appId}", appid)
					.replace("{secret}", appsecret);
			JSONObject jsonObject = httpRequest(requestUrl, "GET", null);
			// 如果请求成功  
			if (null != jsonObject) {
				try {
					accessToken = new AccessToken();
					accessToken.setToken(jsonObject.getString("access_token"));
					accessToken.setExpiresIn(jsonObject.getInt("expires_in"));
				} catch (JSONException e) {
					accessToken = null;
					// 获取token失败  
					log.error("获取token失败 errcode:{} errmsg:{}", jsonObject.getInt("errcode"),
							jsonObject.getString("errmsg"));
				}
			}
		}

		return accessToken;
	}

	/** 
	 * 7.获取access_token 
	 *  
	 * @param appid 凭证 
	 * @param appsecret 密钥 
	 * @return 
	 */
	public static AccessToken GetUpdateAccessToken(String grantType, String appid, String appsecret) {
		AccessToken accessToken = null;
		String requestUrl = access_token_url.replace("{grantType}", grantType).replace("{appId}", appid)
				.replace("{secret}", appsecret);
		JSONObject jsonObject = httpRequest(requestUrl, "GET", null);
		// 如果请求成功  
		if (null != jsonObject) {
			try {
				accessToken = new AccessToken();
				accessToken.setToken(jsonObject.getString("access_token"));
				accessToken.setExpiresIn(jsonObject.getInt("expires_in"));
			} catch (JSONException e) {
				accessToken = null;
				// 获取token失败  
				log.error("获取token失败 errcode:{} errmsg:{}", jsonObject.getInt("errcode"),
						jsonObject.getString("errmsg"));
			}
		}
		return accessToken;
	}

	/** 8.获取response header中Content-Disposition中的filename值 
	* @desc ：
	*  
	* @param response  响应
	* @return String
	*/
	public static String getFileName(HttpResponse response) {
		Header contentHeader = response.getFirstHeader("Content-Disposition");
		String filename = null;
		if (contentHeader != null) {
			HeaderElement[] values = contentHeader.getElements();
			if (values.length == 1) {
				NameValuePair param = values[0].getParameterByName("filename");
				if (param != null) {
					try {
						filename = param.getValue();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}
		return filename;
	}

	/**
	 * 9.上传其他永久素材(图片素材的上限为5000，其他类型为1000)
	 *
	 * @return
	 * @throws Exception
	 */
	public static JSONObject addMaterialEver(String url, String fileurl) {
		File file = new File(fileurl);
		//上传素材
		String result = connectHttpsByPost(url, null, file);
		result = result.replaceAll("[\\\\]", "");
		JSONObject resultJSON = new JSONObject(result.toString());
		return resultJSON;
	}

	/**
	 * 10.上传图文内容图片
	 * @param url
	 * @param fileurl
	 * @return
	 */
	public static JSONObject uploadImg(String url, String fileurl) {
		File file = new File(fileurl);
		//上传素材
		String result = connectHttpsByPost(url, null, file);
		result = result.replaceAll("[\\\\]", "");
		JSONObject resultJSON = new JSONObject(result.toString());
		return resultJSON;
	}

	/**
	 * 11.新增临时素材
	 * @param url
	 * @param fileurl
	 * @return
	 */
	public static JSONObject uploadTempMaterial(String url, String fileurl) {
		File file = new File(fileurl);
		//上传素材
		String result = connectHttpsByPost(url, null, file);
		result = result.replaceAll("[\\\\]", "");
		JSONObject resultJSON = new JSONObject(result.toString());
		if (resultJSON != null) {
			if (resultJSON.get("created_at") != null) {
				return resultJSON;
			}
		}
		return null;
	}

	/**
	 * 12.获取临时素材
	 * @param url
	 * @param fileurl
	 * @return
	 */
	public static File getTempMaterial(String url, String fileurl, String fileName) {
		//2.调用接口，发送请求，下载文件到本地
		File file = null;
		try {
			file = downloadMedia(url, fileurl, fileName);
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return file;
	}

	/**
	 * 12.获取永久多媒体素材
	 * @param url
	 * @param fileurl
	 * @return
	 */
	public static File getPermanentMaterial(String url, String media_id, String fileurl, String fileName) {
		//2.调用接口，发送请求，下载文件到本地
		File file = null;
		try {
			file = downloadMedia(url, media_id, fileurl, fileName);
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return file;
	}
}
