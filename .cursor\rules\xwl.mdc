---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: *.xwl
alwaysApply: false
---
---
description: XWL文件是一种基于JSON格式的UI描述语言，用于企业级应用开发。它集成了UI布局、事件处理、服务端脚本于一体。
globs: *.xwl
alwaysApply: true
---
# XWL文件规范

## 文件格式
 - 文件名以.xwl结尾，整个文件必须是有效的JSON格式
 - serverScript部分是服务端脚本（直接在Java 8的javax.scriptx.ScriptEngineManager中执行）
 - 其余部分为客户端脚本（在浏览器中执行，基于Extjs 4.2）
 - 客户端支持嵌入HTML脚本，比如案例：dev/examples/misc/element-test
  
## 文件脚本规范(ServerScript、event、javaScript等)【重要】
 - ⚠️ **脚本属性的值必须是单行字符串**
 - ⚠️ **serverScript是基于java 8运行的nashorn脚本**
 - ⚠️ **处理步骤是：生成代码 -> 按照JSON字符串做格式化**
 - ⚠️ **必须使用`\n`表示换行，不能使用实际换行符，不要使用反斜杠+换行(\+实际换行)的组合**
 - ⚠️ **字符串中的引号必须使用`\"`转义**
 - ⚠️ **每行代码结束后必须添加`\n`**
 - ⚠️ **结束标签使用`<\/`来转义，如`<\/div>`**
 
### 正确的serverScript示例:
```json
"serverScript": "var method = app.get(\"method\");\nvar userId = app.get(\"userId\");\n\nswitch(method) {\n  case \"getData\":  \n    try {\n      var result = app.getRecord(\n        \"select * from {SYS#TB#USER_INFO} where USER_ID=?\",\n        [userId]\n      );\n      app.send({\n        success: true,\n        data: result\n      });\n    } catch(e) {\n      app.send({\n        success: false,\n        errMsg: \"查询失败：\" + e.message\n      });\n    }\n    break;\n  default:\n    app.send({\n      success: false,\n      errMsg: \"未知方法: \" + method\n    });\n    break;\n}\n"
```

### 正确的HTML和script标签处理示例:
```json
"events": {
  "initialize": "var html = '<div class=\"custom-class\">\n  <h1>标题</h1>\n  <p>内容</p>\n  <script>\n    function test() {\n      console.log(\"Hello\");\n    }\n  <\/script>\n<\/div>';\n\nExt.get('container').update(html);"
}
```

## 标准API使用
 - 获取参数：`app.get("paramName")`
 - 调用模块：`app.execute('m?xwl=module/path')`
 - 返回结果：`app.send({errCode, errMsg, success, data})`
 
### 完整的API调用示例:
```json
"serverScript": "var userId = app.get(\"userId\");\nvar userName = app.get(\"userName\");\n\ntry {\n  // 调用其他模块\n  var moduleResult = app.execute('m?xwl=system/user/validate', {\n    userId: userId,\n    action: 'check'\n  });\n\n  if (moduleResult.success) {\n    // 数据库操作\n    var affected = app.run(\n      \"update {SYS#TB#USER_INFO} set USER_NAME=? where USER_ID=?\",\n      [userName, userId]\n    );\n\n    app.send({\n      success: true,\n      errCode: 0,\n      data: {\n        affected: affected,\n        timestamp: new Date().getTime()\n      }\n    });\n  } else {\n    app.send({\n      success: false,\n      errCode: 10001,\n      errMsg: \"用户验证失败\"\n    });\n  }\n} catch(e) {\n  app.send({\n    success: false,\n    errCode: 9999,\n    errMsg: \"系统错误: \" + e.message\n  });\n}\n"
```

## 代码格式化
 - case语句缩进2个空格
 - case内部代码缩进4个空格
 - 每个case必须以break结束
 - 必须包含default分支处理未知方法
 
### switch语句格式示例:
```json
"serverScript": "var method = app.get(\"method\");\n\nswitch(method) {\n  case \"query\":  \n    // 查询操作处理逻辑\n    var param = app.get(\"param\");\n    // 执行查询\n    break;\n  case \"update\":  \n    // 更新操作处理逻辑\n    var data = app.get(\"data\");\n    // 执行更新\n    break;\n  default:\n    app.send({\n      success: false,\n      errMsg: \"未知方法: \" + method\n    });\n    break;\n}\n"
```

## XWL文件基本结构示例（完整版）
```json
{
  "hidden": false,
  "children": [{
    "configs": {
      "itemId": "module",
      "serverScript": "var method = app.get(\"method\");\n\nswitch(method) {\n  case \"getData\":  \n    // 获取数据逻辑\n    break;\n  default:\n    app.send({\n      success: false,\n      errMsg: \"未知方法: \" + method\n    });\n    break;\n}\n",
      "initScript": "// 客户端初始化脚本\napp.title = \"用户管理\";"
    },
    "children": [
      {
        "type": "panel",
        "configs": {
          "itemId": "mainPanel",
          "layout": "border",
          "border": false
        },
        "children": [
          {
            "type": "grid",
            "configs": {
              "itemId": "userGrid",
              "region": "center",
              "title": "用户列表",
              "columns": [
                {"text": "用户ID", "dataIndex": "USER_ID", "width": 120},
                {"text": "用户名", "dataIndex": "USER_NAME", "flex": 1}
              ]
            },
            "events": {
              "itemdblclick": "var record = arguments[1];\nWb.open({\n  url: 'm?xwl=system/user/detail',\n  params: {\n    userId: record.get('USER_ID')\n  }\n});"
            }
          }
        ]
      }
    ],
    "type": "module",
    "events": {
      "initialize": "// 模块初始化脚本\nvar me = this;\n\n// 加载数据\napp.query = function() {\n  app.run({\n    url: Wb.getUrl(),\n    params: {\n      method: 'getData'\n    },\n    success: function(result) {\n      me.getComponent('userGrid').getStore().loadData(result.data);\n    }\n  });\n};\n\n// 初始加载\napp.query();"
    }
  }],
  "title": "用户管理模块",
  "iconCls": "f18d",
  "inframe": false,
  "pageLink": ""
}
```

## 常见错误避免
1. **换行符错误**: 
   - 所有脚本属性必须是单行JSON字符串，使用`\n`表示换行，不能使用实际换行符
   - 脚本中每一行末尾必须有`\n`，包括最后一行
   - 不要混用`\n`和实际换行，这会导致解析错误
   
   - ❌ 错误1: 使用实际换行而非`\n`:
     ```json
     "serverScript": "var a = 1;
                      var b = 2;"
     ```
   
   - ❌ 错误2: 使用反斜杠+换行组合:
     ```json
     "serverScript": "var a = 1;\
                      var b = 2;"
     ```
   
   - ❌ 错误3: 在字符串拼接时不正确处理换行:
     ```javascript
     let script = "var method = app.get(\"method\");" + "\n" +
                  "switch(method) {" + "\n";
     ```
   
   - ❌ 错误4: 遗漏行尾的`\n`:
     ```json
     "serverScript": "var a = 1;\nvar b = 2;"
     ```
   
   - ✅ 正确1: 使用`\n`表示所有换行:
     ```json
     "serverScript": "var a = 1;\nvar b = 2;\n"
     ```
   
   - ✅ 正确2: 在字符串拼接时正确处理换行:
     ```javascript
     let script = "var method = app.get(\"method\");\n" +
                  "switch(method) {\n";
     ```
   
   - ✅ 正确3: 多行HTML内容处理:
     ```json
     "html": "<div>\n  <h1>标题</h1>\n  <p>内容</p>\n<\/div>\n"
     ```

2. **引号转义错误**: 字符串中的引号必须双重转义
   - ❌ 错误: `"var name = "张三";"`
   - ✅ 正确: `"var name = \"张三\";\n"`

3. **HTML标签转义错误**: 所有HTML标签都需要转义
   - ❌ 错误: `"var html = '<div>内容</div>';"`
   - ✅ 正确: `"var html = '<div>内容<\/div>';\n"`

4. **无效JSON**: 确保整个文件是有效的JSON格式
   - ❌ 错误: 在JSON中使用单引号或未闭合的引号
   - ✅ 正确: 严格遵守JSON语法规范

5. **遗漏行尾\n**: 每行代码结束必须有`\n`
   - ❌ 错误: `"var a = 1;\nvar b = 2;"`
   - ✅ 正确: `"var a = 1;\nvar b = 2;\n"`

## 原生数据库操作规范
 - 使用DbUtil.getConnection()获取数据库连接
 - 使用try-finally确保资源释放
 - 支持事务操作：connection.setAutoCommit(false)和connection.commit()

### 完整事务示例:
```json
"serverScript": "var DbUtil = Java.type('com.wb.common.util.DbUtil');\nvar conn = null, pstmt = null;\n\ntry {\n  conn = DbUtil.getConnection();\n  conn.setAutoCommit(false);\n\n  // 第一个操作\n  pstmt = conn.prepareStatement(\"INSERT INTO {SYS#TB#USER_INFO} (USER_ID, USER_NAME) VALUES (?, ?)\");\n  pstmt.setString(1, '10001');\n  pstmt.setString(2, '张三');\n  pstmt.executeUpdate();\n  pstmt.close();\n\n  // 第二个操作\n  pstmt = conn.prepareStatement(\"INSERT INTO {SYS#TB#USER_ROLE} (USER_ID, ROLE_ID) VALUES (?, ?)\");\n  pstmt.setString(1, '10001');\n  pstmt.setString(2, 'ADMIN');\n  pstmt.executeUpdate();\n\n  // 提交事务\n  conn.commit();\n  app.send({success: true});\n} catch(e) {\n  if (conn != null) {\n    try {\n      conn.rollback();\n    } catch(re) {\n      // 回滚异常处理\n    }\n  }\n  app.send({success: false, errMsg: e.message});\n} finally {\n  DbUtil.close(pstmt);\n  DbUtil.close(conn);\n}\n"
```

## 代码组织最佳实践
 - 私有成员：仅当前代码块可访问
 - 公有成员：通过Wb.apply(app, {...})定义，模块内任意处可访问
 - 事件处理：可使用handler属性引用公共方法

### 完整代码组织示例:
```json
"events": {
  "initialize": "var me = this;\n\n// 定义私有方法\nvar formatDate = function(date) {\n  return Ext.Date.format(date, 'Y-m-d');\n};\n\n// 定义公共方法\nWb.apply(app, {\n  query: function() {\n    // 查询逻辑实现\n  },\n  save: function() {\n    // 保存逻辑实现\n  },\n  formatValue: function(value) {\n    return value ? value : 'N/A';\n  }\n});\n\n// 初始化加载\napp.query();\n"
}
```
```