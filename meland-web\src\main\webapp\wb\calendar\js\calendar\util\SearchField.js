Ext.define('Ext.ux.form.SearchField', {
  extend: 'Ext.form.field.Trigger',

  alias: 'widget.searchfield',

  trigger1Cls: Ext.baseCSSPrefix + 'form-clear-trigger',

  trigger2Cls: Ext.baseCSSPrefix + 'form-search-trigger',

  hasSearch: false,
  paramName: 'query',

  initComponent: function() {
    this.callParent(arguments);
    this.on('specialkey', function(f, e) {
      if (e.getKey() == e.ENTER) {
        this.onTrigger2Click();
      }
    }, this);
  },

  afterRender: function() {
    this.callParent();
    this.triggerCell.item(0).setDisplayed(false);
  },

  onTrigger1Click: function() {
    var me = this,
      store = me.store,
      proxy = store.getProxy(),
      val;

    if (me.hasSearch) {
      me.setValue('');
      proxy.extraParams[me.paramName] = '';
      store.loadPage(1);
      me.hasSearch = false;
      me.triggerCell.item(0).setDisplayed(false);
      me.doComponentLayout();
    }
  },

  onTrigger2Click: function() {
    var me = this,
      store = me.store,
      proxy = store.getProxy(),
      value = me.getValue();

    if (value.length < 1) {
      me.onTrigger1Click();
      return;
    }
    proxy.extraParams[me.paramName] = value;
    store.loadPage(1);
    me.hasSearch = true;
    me.triggerCell.item(0).setDisplayed(true);
    me.doComponentLayout();
  }
});