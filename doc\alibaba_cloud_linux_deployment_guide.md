# Alibaba Cloud Linux 3 项目部署指南 (Redis + RDS + Tomcat)

本文档旨在指导如何在 Alibaba Cloud Linux 3.2104 LTS 64 位系统上，使用 Redis、阿里云 RDS 数据库，并将 Java Web 项目部署到 Apache Tomcat 9 服务器。

## 1. 系统准备与环境更新

首先，确保系统是最新状态并安装基础工具。

```bash
# 更新系统软件包列表和已安装的包
sudo yum update -y

# 安装基础工具 (git, wget, tar, unzip)
sudo yum install -y git wget tar unzip

# 安装 Java Development Kit (JDK 8) - Tomcat 和应用运行需要
sudo yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 安装 Maven (用于项目构建)
sudo yum install -y maven

# 验证 Java 安装
# java -version
```
*注意：这里分步安装了基础工具、Java 和 Maven。*

## 2. 安装依赖服务

### 2.1 安装 Redis

```bash
# 使用 yum 安装 Redis
sudo yum install -y redis

# 启动 Redis 服务
sudo systemctl start redis

# 设置 Redis 开机自启
sudo systemctl enable redis

# 检查 Redis 状态
sudo systemctl status redis
```

#### Redis 正式环境配置建议 (`/etc/redis.conf`)

对于正式环境，建议修改 Redis 配置文件 (`/etc/redis.conf`) 以提高安全性、稳定性和性能。修改后需要重启 Redis 服务 (`sudo systemctl restart redis`) 使配置生效。

**操作步骤示例 (请根据实际需求调整参数):**

```bash
# 0. (可选) 备份原始配置文件
sudo cp /etc/redis.conf /etc/redis.conf.bak

# 1. 编辑配置文件 (可以使用 vim 或 nano)
sudo vim /etc/redis.conf
# 或者使用 sed 命令直接修改 (注意特殊字符可能需要转义)

# 1.1 安全设置:
#     修改 bind (示例: 绑定到内网 IP ************* 和本地回环)
#     (如果只允许本机访问，用 'bind 127.0.0.1 ::1')
sudo sed -i 's/^bind .*/bind ************* 127.0.0.1 ::1/' /etc/redis.conf
#     设置密码 (将 'your_strong_password_here' 替换为强密码)
sudo sed -i 's/^# requirepass .*/requirepass your_strong_password_here/' /etc/redis.conf
#     确保 protected-mode 开启 (通常是默认值)
# sudo sed -i 's/^protected-mode .*/protected-mode yes/' /etc/redis.conf
#     (可选) 禁用 CONFIG 命令
# sudo echo 'rename-command CONFIG ""' >> /etc/redis.conf

# 1.2 内存管理:
#     设置最大内存 (示例: 4GB)
# sudo sed -i 's/^# maxmemory .*/maxmemory 4gb/' /etc/redis.conf
#     设置内存淘汰策略 (示例: allkeys-lru)
# sudo sed -i 's/^# maxmemory-policy .*/maxmemory-policy allkeys-lru/' /etc/redis.conf

# 1.3 持久化 (推荐 AOF):
#     开启 AOF
# sudo sed -i 's/^appendonly .*/appendonly yes/' /etc/redis.conf
#     设置 AOF 同步策略 (通常是默认值)
# sudo sed -i 's/^# appendfsync everysec/appendfsync everysec/' /etc/redis.conf

# 1.4 日志:
#     设置日志文件路径 (确保 /var/log/redis/ 目录存在且 Redis 用户可写)
# sudo sed -i 's|^logfile .*|logfile /var/log/redis/redis-server.log|' /etc/redis.conf

# 2. 保存配置文件 (如果是用 vim 编辑)

# 3. 重启 Redis 服务使配置生效
sudo systemctl restart redis

# 4. 验证配置是否生效 (例如检查 bind 和 requirepass)
# redis-cli -h 127.0.0.1 # 如果绑定了 127.0.0.1
# > AUTH your_strong_password_here
# > CONFIG GET bind
# > CONFIG GET requirepass
```

**重要配置项说明：**

1.  **安全设置:**
    *   `bind 127.0.0.1 -::1`: (或绑定内网IP) **强烈建议**取消对 `0.0.0.0` 的绑定，限制访问来源。
    *   `requirepass your_strong_password_here`: **极其重要！** 设置一个强密码进行认证。
    *   `protected-mode yes`: 保持默认开启，配合 `bind` 和 `requirepass` 使用。
    *   (可选) `rename-command CONFIG ""`: 禁用或重命名危险命令。

2.  **内存管理:**
    *   `maxmemory <bytes>`: **极其重要！** 设置 Redis 最大内存使用量 (例如 `maxmemory 4gb`)，防止耗尽系统内存。需根据服务器总内存和本机其他服务（如 Tomcat 应用）合理分配。
    *   `maxmemory-policy allkeys-lru`: (或其他策略如 `volatile-lru`) 设置内存达到上限时的淘汰策略。

3.  **持久化 (推荐 AOF):**
    *   `appendonly yes`: 开启 AOF 持久化，数据更安全。
    *   `appendfsync everysec`: AOF 同步策略，性能和安全的良好折衷。

4.  **日志:**
    *   `logfile /var/log/redis/redis-server.log`: 指定明确的日志文件路径，并确保目录可写。

5.  **其他建议:**
    *   `maxclients 10000`: 根据需要调整最大连接数。
    *   **使用专用用户**: 生产环境不建议使用 root 运行，应创建专用用户 (如 `redis`) 并修改 systemd 服务配置及相关目录权限。
    *   **操作系统调优**: 考虑禁用透明大页 (THP) 和设置 `vm.overcommit_memory = 1`。
    *   **定期备份**: RDB/AOF 文件需要定期备份到其他存储位置。

*请根据服务器实际情况和官方文档调整具体参数值。*

### 2.2 安装 Apache Tomcat 9

如果选择直接部署 WAR 包或解压后的 Web 应用（非 Docker 方式），需要安装 Tomcat 服务器。

```bash
# 1. 安装依赖 (wget 用于下载, tar 用于解压)
# (通常在步骤 1 中已安装 wget 和 tar)
sudo yum install -y wget tar

# 2. 创建 Tomcat 安装目录 (例如 /opt/tomcat)
cd /opt

# 3. 从 Apache官网下载 Tomcat 9 (请检查最新稳定版本并替换链接)
# 访问 https://tomcat.apache.org/download-90.cgi 获取最新链接
sudo wget https://dlcdn.apache.org/tomcat/tomcat-9/v9.0.104/bin/apache-tomcat-9.0.104.tar.gz

# 4. 解压下载的压缩包
sudo tar -xzvf apache-tomcat-*.tar.gz

# 5. (可选) 重命名目录方便管理
sudo mv /opt/apache-tomcat-9.0.104 /opt/tomcat9 # 或者直接命名为 tomcat

# 6. (可选) 删除压缩包
sudo rm apache-tomcat-*.tar.gz

# 7. (重要) 设置文件权限
# 确保 bin 目录下的脚本有执行权限
sudo chmod +x /opt/tomcat9/bin/*.sh # 使用你重命名的目录

# 8. (可选, 推荐) 创建 Tomcat 专用用户
# 生产环境不建议使用 root 运行 Tomcat
sudo groupadd tomcat
sudo useradd -s /sbin/nologin -g tomcat -d /opt/tomcat9 tomcat
sudo chown -R tomcat:tomcat /opt/tomcat9/
# 如果创建了专用用户，后续启动/停止需要用该用户，或者配置 systemd 服务

# 9. (重要) 配置 JVM 参数 (通过 /opt/tomcat9/bin/setenv.sh)
# 这是应用来自 entrypoint.sh 的优化参数并设置 Tomcat JVM 环境的最佳实践。
# Tomcat 的 startup.sh 会自动执行此文件 (如果存在且可执行)。
# 建议将以下优化好的脚本内容保存到项目源码中
# (例如 src/main/assembly/tomcat-16g/bin/setenv.sh)，
# 然后在部署步骤 (第 5 节) 中将其复制到 /opt/tomcat9/bin/。

# 创建或编辑 setenv.sh 文件:
sudo vim /opt/tomcat9/bin/setenv.sh

# ---- 优化好的 setenv.sh 内容 开始 ----
#!/bin/sh

# 1. 设置 Java Home (请确保路径正确)
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.442.b06-*******.al8.x86_64

# 2. 设置 CATALINA_OPTS (包含所有 JVM 参数)

# 基础配置和堆大小
# !! 动态计算堆大小，适配 4c8g 环境 (同时运行 Redis) !!

# 获取总物理内存 (MB)
MEM_TOTAL_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
MEM_TOTAL_MB=$((MEM_TOTAL_KB / 1024))

# 计算分配给 Tomcat Heap 的比例 (示例: 55%)
# 需为 OS、Redis (假设已设置 maxmemory 1-2GB) 和其他进程留出足够内存
HEAP_RATIO=55
HEAP_SIZE=$((MEM_TOTAL_MB * HEAP_RATIO / 100))

# 设置最小和最大堆大小 (-Xms 和 -Xmx 必须相同)
# 同时设置线程栈大小
CATALINA_OPTS="$CATALINA_OPTS -server -Xms${HEAP_SIZE}m -Xmx${HEAP_SIZE}m -Xss1m"
echo "Tomcat Heap Size set to: -Xms${HEAP_SIZE}m -Xmx${HEAP_SIZE}m"

# Metaspace 和 Code Cache
# (对于 8GB 内存，如果遇到问题，可适当调低 MaxMetaspaceSize 和 ReservedCodeCacheSize)
CATALINA_OPTS="$CATALINA_OPTS -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=768m"
CATALINA_OPTS="$CATALINA_OPTS -XX:CompressedClassSpaceSize=256m"
CATALINA_OPTS="$CATALINA_OPTS -XX:InitialCodeCacheSize=256m -XX:ReservedCodeCacheSize=512m"

# G1 GC 配置 (来自 entrypoint.sh)
# 计算处理器核心数 (用于设置并行线程数)
PROCESSOR_COUNT=$(grep -c ^processor /proc/cpuinfo)
# 适配 4 核 CPU
PARALLEL_GC_THREADS=$((PROCESSOR_COUNT / 2 > 1 ? PROCESSOR_COUNT / 2 : 1))
CONC_GC_THREADS=$((PROCESSOR_COUNT / 3 > 1 ? PROCESSOR_COUNT / 3 : 1))
echo "GC Threads: Parallel=${PARALLEL_GC_THREADS}, Concurrent=${CONC_GC_THREADS}"

CATALINA_OPTS="$CATALINA_OPTS -XX:+UseG1GC"
CATALINA_OPTS="$CATALINA_OPTS -XX:+UnlockExperimentalVMOptions" # G1MixedGCLiveThresholdPercent 需要
CATALINA_OPTS="$CATALINA_OPTS -XX:+ClassUnloadingWithConcurrentMark"
CATALINA_OPTS="$CATALINA_OPTS -XX:MaxGCPauseMillis=200" # 目标停顿时间
CATALINA_OPTS="$CATALINA_OPTS -XX:G1HeapRegionSize=4m" # 区域大小
CATALINA_OPTS="$CATALINA_OPTS -XX:ParallelGCThreads=${PARALLEL_GC_THREADS}" # 并行GC线程
CATALINA_OPTS="$CATALINA_OPTS -XX:ConcGCThreads=${CONC_GC_THREADS}" # 并发GC线程
CATALINA_OPTS="$CATALINA_OPTS -XX:InitiatingHeapOccupancyPercent=45" # 触发并发GC周期阈值
CATALINA_OPTS="$CATALINA_OPTS -XX:G1MixedGCLiveThresholdPercent=75" # 混合GC live阈值
CATALINA_OPTS="$CATALINA_OPTS -XX:G1MixedGCCountTarget=8" # 混合GC目标次数
CATALINA_OPTS="$CATALINA_OPTS -XX:G1OldCSetRegionThresholdPercent=15" # 加速老年代回收阈值
CATALINA_OPTS="$CATALINA_OPTS -XX:G1RSetUpdatingPauseTimePercent=10"
CATALINA_OPTS="$CATALINA_OPTS -XX:G1HeapWastePercent=5" # 允许的堆浪费比例
CATALINA_OPTS="$CATALINA_OPTS -XX:G1ReservePercent=10" # 保留内存比例
CATALINA_OPTS="$CATALINA_OPTS -XX:+ParallelRefProcEnabled"

# Nashorn 优化 (来自 entrypoint.sh)
CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.compiler.deoptimizeThreshold=-1"
CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.codegen.maxmethod.size=65535"
CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.compiler.recursion.limit=1000"
CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.classloader.loadersource=parent"
CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.args.prepend=--persistent-code-cache"
CATALINA_OPTS="$CATALINA_OPTS -Dnashorn.typeInfo.cacheSize=1024"

# 其他 JVM 优化 (来自 entrypoint.sh)
CATALINA_OPTS="$CATALINA_OPTS -XX:+UseStringDeduplication"
CATALINA_OPTS="$CATALINA_OPTS -XX:-UseBiasedLocking"
CATALINA_OPTS="$CATALINA_OPTS -XX:+DisableExplicitGC"
CATALINA_OPTS="$CATALINA_OPTS -XX:+AlwaysPreTouch" # 内存预分配

# GC 日志配置
CATALINA_OPTS="$CATALINA_OPTS -XX:+PrintGCDetails -XX:+PrintGCDateStamps"
CATALINA_OPTS="$CATALINA_OPTS -Xloggc:/opt/tomcat9/logs/gc.log" # 确保 logs 目录存在且可写
CATALINA_OPTS="$CATALINA_OPTS -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M"

# 堆内存溢出时 Dump
CATALINA_OPTS="$CATALINA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
CATALINA_OPTS="$CATALINA_OPTS -XX:HeapDumpPath=/opt/tomcat9/logs/heapdump.hprof"

# JVM 系统属性
CATALINA_OPTS="$CATALINA_OPTS -Dfile.encoding=UTF-8 -Duser.timezone=Asia/Shanghai"
CATALINA_OPTS="$CATALINA_OPTS -Djdk.tls.ephemeralDHKeySize=2048"
CATALINA_OPTS="$CATALINA_OPTS -Djava.security.egd=file:/dev/./urandom"

# 其他来自 entrypoint.sh 的参数
CATALINA_OPTS="$CATALINA_OPTS -XX:+UnlockDiagnosticVMOptions"
CATALINA_OPTS="$CATALINA_OPTS -XX:GuaranteedSafepointInterval=60000"
CATALINA_OPTS="$CATALINA_OPTS -XX:+UseCountedLoopSafepoints"
CATALINA_OPTS="$CATALINA_OPTS -Darms.logger.level=INFO"

# 导出环境变量
export CATALINA_OPTS

# ---- 优化好的 setenv.sh 内容 结束 ----

# 使 setenv.sh 脚本可执行 (部署步骤中会再次确认)
sudo chmod +x /opt/tomcat9/bin/setenv.sh
```

#### 启动与停止 Tomcat (手动方式)

```bash
# 启动 Tomcat (使用 root 或创建的 tomcat 用户)
# sudo /opt/tomcat9/bin/startup.sh
# 或者切换到 tomcat 用户: sudo su - tomcat -c "/opt/tomcat9/bin/startup.sh"

# 查看启动日志
# tail -f /opt/tomcat9/logs/catalina.out

# 停止 Tomcat
# sudo /opt/tomcat9/bin/shutdown.sh
# 或者切换到 tomcat 用户: sudo su - tomcat -c "/opt/tomcat9/bin/shutdown.sh"
```
*建议为 Tomcat 创建 systemd 服务单元文件，以便更好地管理和设置开机自启。*

#### 创建 Tomcat Systemd 服务文件 (推荐)

使用 systemd 可以更方便地管理 Tomcat 服务的启停、开机自启，并能更好地控制其运行环境和资源。

```bash
# 1. 创建 systemd 服务文件
sudo vim /etc/systemd/system/tomcat.service

# 2. 将以下内容粘贴到文件中。
#    请务必根据实际情况修改 User, Group, JAVA_HOME, CATALINA_HOME (Tomcat安装目录)
# ---- /etc/systemd/system/tomcat.service 内容示例 开始 ----
[Unit]
Description=Apache Tomcat 9 Web Application Server
After=syslog.target network.target

[Service]
# 设置运行用户和组 (如果创建了专用用户)
User=tomcat
Group=tomcat

# 指定必要的环境变量。复杂的 JVM 参数通过 setenv.sh 设置。
Environment=JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.442.b06-*******.al8.x86_64
Environment=CATALINA_PID=/opt/tomcat9/temp/tomcat.pid
Environment=CATALINA_HOME=/opt/tomcat9
Environment=CATALINA_BASE=/opt/tomcat9
# JAVA_OPTS 可用于设置非 CATALINA_OPTS 的 JVM 属性
Environment='JAVA_OPTS=-Djava.awt.headless=true -Djava.security.egd=file:/dev/./urandom'

# startup.sh 会自动执行 bin/setenv.sh (如果存在)
ExecStart=/opt/tomcat9/bin/startup.sh
ExecStop=/opt/tomcat9/bin/shutdown.sh

# 服务类型 - Tomcat 启动脚本会 fork 进程
Type=forking

# 设置 UMask 和文件描述符限制
UMask=0007
RestartSec=10
Restart=always
LimitNOFILE=65535

[Install]
WantedBy=multi-user.target
# ---- /etc/systemd/system/tomcat.service 内容示例 结束 ----

# 3. 保存并退出编辑器 (vim: Esc -> :wq -> Enter)

# 4. 重新加载 systemd 配置
sudo systemctl daemon-reload

# 5. 启动 Tomcat 服务
sudo systemctl start tomcat

# 6. 设置开机自启
sudo systemctl enable tomcat

# 7. 检查服务状态
sudo systemctl status tomcat

# 后续管理命令:
# sudo systemctl stop tomcat
# sudo systemctl restart tomcat
# sudo systemctl disable tomcat
```

### 2.2.1 配置 Tomcat HTTPS (SSL/TLS 证书)

要使 Tomcat 通过 HTTPS 提供服务，你需要配置 SSL/TLS 证书。

#### 前提条件:

*   你已经获得了域名证书文件，可能是以下几种形式之一：
    *   `.pfx`/`.p12` 文件（PKCS#12 格式，已包含证书和私钥）
    *   或者分离的证书文件 (`.crt`) 和私钥文件 (`.key`)，有时还有中间证书链文件
*   将这些文件安全地上传到服务器，例如上传到 `/etc/pki/tls/certs/`（证书）和 `/etc/pki/tls/private/`（私钥）目录，并确保它们具有合适的权限（私钥尤其重要，通常只有 root 或特定用户可读）。

#### 配置步骤:

##### A. 根据证书类型，选择以下其中一种方式:

###### 方式 1: 直接使用 `.pfx`/`.p12` 文件（推荐）

如果您已有 `.pfx` 或 `.p12` 格式的证书（包含证书和私钥）：

```bash
# 1. 复制 .pfx 文件到 Tomcat 配置目录
sudo cp /path/to/your_certificate.pfx /opt/tomcat9/conf/keystore.pfx

# 2. 设置适当的权限，确保 Tomcat 用户可以读取
sudo chown tomcat:tomcat /opt/tomcat9/conf/keystore.pfx
sudo chmod 600 /opt/tomcat9/conf/keystore.pfx
```

###### 方式 2: 将分离的证书和私钥文件合并为 PKCS12 (.p12/.pfx) 格式

如果您有分离的 `.crt` 和 `.key` 文件：

```bash
# 1. (可选) 合并证书链
# 如果您有单独的根证书和中间证书，通常需要合并
# sudo cat your_domain.crt ca_bundle.crt > your_domain_fullchain.crt

# 2. 使用 openssl 从 .crt 和 .key 文件创建 PKCS12 格式的密钥库
sudo openssl pkcs12 -export \
     -in /path/to/your_domain_fullchain.crt \  # 或 your_domain.crt（如果没有中间证书）
     -inkey /path/to/your_domain.key \
     -out /opt/tomcat9/conf/keystore.pfx \
     -name tomcat \
     -passout pass:your_keystore_password  # 替换为您的密码

# 3. 设置适当的权限
sudo chown tomcat:tomcat /opt/tomcat9/conf/keystore.pfx
sudo chmod 600 /opt/tomcat9/conf/keystore.pfx
```

##### B. 配置 Tomcat 以使用证书（针对所有方式）:

```bash
# 编辑 Tomcat 配置文件
sudo vim /opt/tomcat9/conf/server.xml
```

找到 `<Service name="Catalina">` 部分，在现有的 HTTP Connector 旁边，添加或修改 HTTPS Connector。**注意**：由于 Linux 权限限制，非 root 用户不能绑定低于 1024 的端口，推荐使用 8443 端口：

```xml
<!-- 在 <Service name="Catalina"> 部分内添加或修改 -->
<Connector port="8443" protocol="org.apache.coyote.http11.Http11NioProtocol"
           maxThreads="150" SSLEnabled="true">
    <SSLHostConfig>
        <Certificate certificateKeystoreFile="conf/keystore.pfx"
                     certificateKeystorePassword="your_pfx_password"
                     certificateKeystoreType="PKCS12" />
    </SSLHostConfig>
</Connector>
```

其中：
*   `certificateKeystoreFile`: 指向上一步中创建或复制的密钥库文件
*   `certificateKeystorePassword`: 设置为密钥库的密码
*   `certificateKeystoreType`: PKCS12 格式使用 "PKCS12"

##### C. 解决 Linux 低端口绑定限制问题（访问 HTTPS 标准端口 443）:

在 Linux 中，非 root 用户（如 tomcat）无法绑定到 1024 以下的端口（如 HTTPS 的标准端口 443）。为解决这个问题，推荐使用 iptables 端口转发：

```bash
# 1. 启用 IP 转发
echo 1 | sudo tee /proc/sys/net/ipv4/ip_forward
# 确保永久启用
echo "net.ipv4.ip_forward = 1" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 2. 添加 iptables 端口转发规则
# 将外部 443 端口的请求转发到 Tomcat 的 8443 端口
sudo iptables -t nat -A PREROUTING -p tcp --dport 443 -j REDIRECT --to-port 8443

# 3. 保存 iptables 规则以使其在重启后仍然有效
sudo yum install -y iptables-services
sudo service iptables save
sudo systemctl enable iptables

# 4. 检查端口转发规则是否成功添加
sudo iptables -t nat -L -n -v | grep 8443
```

##### D. 配置防火墙:

```bash
# 开放 HTTPS 端口 (如果直接使用 443 端口) 和 Tomcat HTTPS 端口 (如果使用 8443)
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=8443/tcp  # 只有在未使用 iptables 转发时才需要
sudo firewall-cmd --reload
```

##### E. 重启 Tomcat:

```bash
sudo systemctl restart tomcat
```

##### F. 验证配置:

```bash
# 检查 Tomcat 是否成功绑定到配置的端口
sudo netstat -tulpn | grep java

# 尝试访问 HTTPS 网站
curl -k https://localhost:8443  # 直接测试 Tomcat 端口
curl -k https://localhost       # 通过 iptables 转发测试
```

现在，你应该可以通过 `https://<你的域名>` 访问你的应用程序，流量将被透明地转发到 Tomcat 的 8443 端口。

## 3. 获取项目源码

选择以下一种方式获取源码。

### 3.1 从 Gitee 下载 (SSH方式 - 推荐)

```bash
# 1. 生成 SSH Key (如果服务器上还没有)
# ssh-keygen -t rsa -b 4096 -C "<EMAIL>" # 一路回车即可
# 2. 将公钥 (~/.ssh/id_rsa.pub) 内容添加到 Gitee 账户的 SSH 公钥设置中
# cat ~/.ssh/id_rsa.pub
# 3. 测试连接 Gitee
# ssh -T ************* # 首次连接会提示确认，输入 yes

# 4. 克隆你的企业仓库代码
mkdir -p /opt/projects # 创建项目存放目录 (如果不存在)
cd /opt/projects
<NAME_EMAIL>:你的企业路径/你的项目名.git # 替换为你的 Gitee SSH 地址
cd 你的项目名 # 进入项目目录
```

### 3.2 从 Gitee 下载 (HTTPS方式)

```bash
mkdir -p /opt/projects
cd /opt/projects
# 克隆时会提示输入 Gitee 用户名和密码/Token
git clone https://gitee.com/你的企业路径/你的项目名.git
cd 你的项目名
```

### 3.3 上传源码

如果你本地已有源码，可以使用 `scp` (Linux/Mac) 或 WinSCP (Windows) 等工具将源码目录上传到服务器的 `/opt/projects/你的项目名` 目录下。

## 4. 构建项目

```bash
# 进入项目根目录 (假设源码在 /opt/projects/你的项目名)
cd /opt/projects/你的项目名

# (重要) 根据你的项目配置，修改 Redis 的连接信息，
# 以及数据库连接信息，指向你的阿里云 RDS 实例地址、数据库名、用户名和密码。
# 通常在 src/main/resources/application.properties 或 application.yml 等配置文件中
# vim src/main/resources/application.properties # 使用 vim 或其他编辑器修改

# (重要) 确认构建产物
# 确保 Maven 构建 (例如使用 maven-assembly-plugin) 会将应用代码和所需的
# Tomcat 特定配置文件 (例如像 Dockerfile 中的 META-INF/tomcat-16g/ 目录内容)
# 打包到最终的部署单元中 (可能是 target/meland 目录，或一个 .tgz/.tar.gz 归档文件)。

# 执行 Maven 构建命令
# -Dmaven.test.skip=true 跳过测试，加快构建
mvn clean package -Dmaven.test.skip=true
```

## 5. 部署并运行项目到 Tomcat

选择以下一种部署方式，将构建好的应用部署到已安装的 Tomcat 服务器。

### 5.1 方式一：部署 ZIP 压缩包

适用于将应用打包成 ZIP 格式并上传到服务器的场景。

```bash
# 1. 停止正在运行的 Tomcat 服务
sudo systemctl stop tomcat

# 2. 清理 Tomcat 旧应用并创建 ROOT 目录 (假设 Tomcat 在 /opt/tomcat9)
sudo rm -rf /opt/tomcat9/webapps/*
sudo mkdir -p /opt/tomcat9/webapps/ROOT

# 3. 解压 ZIP 文件到 ROOT 目录 (假设 ZIP 文件在 /tmp/app.zip)
sudo unzip /tmp/app.zip -d /opt/tomcat9/webapps/ROOT

# 4. 检查解压后的目录结构，避免多层目录嵌套问题
ls -la /opt/tomcat9/webapps/ROOT/
# 如果解压后有多层目录（如 ROOT/应用名/实际内容），需要移动文件到正确位置
# sudo mv /opt/tomcat9/webapps/ROOT/应用名/* /opt/tomcat9/webapps/ROOT/
# sudo rm -rf /opt/tomcat9/webapps/ROOT/应用名

# 5. (关键) 复制 Tomcat 特定配置 (包含优化的 setenv.sh)
# 从解压后的应用中，将包含优化配置 (如 setenv.sh, server.xml 等) 的目录内容复制到 Tomcat 安装目录
# (假设特定配置在解压后的 META-INF/tomcat-16g/ 目录下)
if [ -d "/opt/tomcat9/webapps/ROOT/META-INF/tomcat-16g" ]; then
    sudo cp -R /opt/tomcat9/webapps/ROOT/META-INF/tomcat-16g/* /opt/tomcat9/
    sudo chmod +x /opt/tomcat9/bin/setenv.sh
fi

# 6. 确保 Tomcat 运行用户对部署的应用有读取权限 (如果使用专用用户 tomcat)
sudo chown -R tomcat:tomcat /opt/tomcat9/webapps/ROOT
# 如果覆盖了配置文件，也要设置权限
sudo chown -R tomcat:tomcat /opt/tomcat9/conf 2>/dev/null || true
sudo chown -R tomcat:tomcat /opt/tomcat9/bin 2>/dev/null || true

# 7. 启动 Tomcat 服务
sudo systemctl start tomcat

# 8. 检查服务状态和查看启动日志
sudo systemctl status tomcat
tail -f /opt/tomcat9/logs/catalina.out

# 9. (可选) 删除临时 ZIP 文件
sudo rm /tmp/app.zip
```

### 5.2 方式二：部署 TAR.GZ 归档文件

这种方式类似于 Dockerfile 中的操作，适用于将应用和特定 Tomcat 配置打包在一起的场景。

```bash
# 1. 上传或复制归档文件到服务器 (例如 /tmp/app.tgz)
# scp /path/to/local/app.tgz user@your_server_ip:/tmp/

# 2. 停止正在运行的 Tomcat 服务
sudo systemctl stop tomcat

# 3. 清理 Tomcat 旧应用并创建 ROOT 目录 (假设 Tomcat 在 /opt/tomcat9)
sudo rm -rf /opt/tomcat9/webapps/*
sudo mkdir -p /opt/tomcat9/webapps/ROOT

# 4. 解压归档文件到 ROOT 目录
sudo tar -xzf /tmp/app.tgz -C /opt/tomcat9/webapps/ROOT

# 5. (关键) 复制 Tomcat 特定配置 (包含优化的 setenv.sh)
# 从解压后的应用中，将包含优化配置 (如 setenv.sh, server.xml 等) 的目录内容复制到 Tomcat 安装目录
# (假设特定配置在解压后的 META-INF/tomcat-16g/ 目录下)
sudo cp -R /opt/tomcat9/webapps/ROOT/META-INF/tomcat-16g/* /opt/tomcat9/
# 确保部署的 setenv.sh 有执行权限
sudo chmod +x /opt/tomcat9/bin/setenv.sh

# 6. 确保 Tomcat 运行用户对部署的应用有读取权限 (如果使用专用用户 tomcat)
sudo chown -R tomcat:tomcat /opt/tomcat9/webapps/ROOT
sudo chown -R tomcat:tomcat /opt/tomcat9/conf # 如果覆盖了 conf 目录下的文件
sudo chown -R tomcat:tomcat /opt/tomcat9/bin # 如果覆盖了 bin 目录下的文件

# 7. 启动 Tomcat 服务
sudo systemctl start tomcat

# 8. 检查服务状态和查看启动日志
sudo systemctl status tomcat
tail -f /opt/tomcat9/logs/catalina.out

# 9. (可选) 删除临时归档文件
sudo rm /tmp/app.tgz
```

### 5.3 方式三：部署构建目录

适用于 Maven 构建直接生成包含应用和配置的目标目录 (例如 `target/meland`) 的场景。

```bash
# 1. 停止正在运行的 Tomcat 服务
sudo systemctl stop tomcat

# 2. 清理 Tomcat 旧应用 (假设 Tomcat 在 /opt/tomcat9)
sudo rm -rf /opt/tomcat9/webapps/*

# 3. 复制构建好的应用目录到 ROOT
# (假设应用目录在 /opt/projects/你的项目名/meland-web/target/meland)
sudo cp -r /opt/projects/你的项目名/meland-web/target/meland /opt/tomcat9/webapps/ROOT

# 4. (关键) 复制 Tomcat 特定配置 (包含优化的 setenv.sh)
# 从构建目录中，将包含优化配置的目录内容复制到 Tomcat 安装目录
# (假设特定配置在构建目录的 META-INF/tomcat-16g/ 目录下)
sudo cp -R /opt/projects/你的项目名/meland-web/target/meland/META-INF/tomcat-16g/* /opt/tomcat9/
# 确保部署的 setenv.sh 有执行权限
sudo chmod +x /opt/tomcat9/bin/setenv.sh

# 5. 确保 Tomcat 运行用户对部署的应用有读取权限 (如果使用专用用户 tomcat)
sudo chown -R tomcat:tomcat /opt/tomcat9/webapps/ROOT
sudo chown -R tomcat:tomcat /opt/tomcat9/conf # 如果覆盖了 conf 目录下的文件
sudo chown -R tomcat:tomcat /opt/tomcat9/bin # 如果覆盖了 bin 目录下的文件

# 6. 启动 Tomcat 服务
sudo systemctl start tomcat

# 7. 检查服务状态和查看启动日志
sudo systemctl status tomcat
tail -f /opt/tomcat9/logs/catalina.out
```

## 6. 防火墙配置

如果服务器启用了防火墙 (Alibaba Cloud Linux 3 默认使用 firewalld)，需要开放应用本身以及 Redis 的端口。
**注意：** 数据库端口（如 3306）的访问控制通常在阿里云 RDS 的安全组中配置。

```bash
# 开放项目端口 (例如 8080，如果使用 Tomcat 默认端口)
sudo firewall-cmd --permanent --add-port=8080/tcp

# 开放 Redis 端口 (默认 6379, 仅当 Redis 需要从外部访问时开放, 且务必配置密码!)
# 如果 Redis 只供本机应用访问 (bind 127.0.0.1)，则无需开放此端口
# sudo firewall-cmd --permanent --add-port=6379/tcp

# 重新加载防火墙规则使配置生效
sudo firewall-cmd --reload

# 查看已开放的端口
# sudo firewall-cmd --list-ports
```

## 7. 访问应用

完成部署、HTTPS 配置和防火墙配置后，可以通过浏览器访问 `https://<你的域名>` 来访问你的应用程序。
如果未配置 HTTPS，则访问 `http://<服务器公网IP>:8080`。

## 8. 常见问题排查

### 8.1 解决 403 Forbidden 错误

如果访问应用时出现 403 Forbidden 错误，特别是静态资源文件（如 CSS、JS、图片、字体文件等），通常是权限或 SELinux 配置问题。

#### 解决步骤：

```bash
# 1. 停止 Tomcat 服务
sudo systemctl stop tomcat

# 2. 修复文件权限
# 设置文件为 644，目录为 755
sudo chmod -R 644 /opt/tomcat9/webapps/ROOT
sudo find /opt/tomcat9/webapps/ROOT -type d -exec chmod 755 {} \;

# 3. 确保 Tomcat 用户拥有所有文件
sudo chown -R tomcat:tomcat /opt/tomcat9/webapps/ROOT

# 4. 检查 SELinux 状态
getenforce
# 如果输出是 "Enforcing"，需要配置 SELinux

# 5. 配置 SELinux（如果启用）
sudo setsebool -P httpd_exec_enable 1
sudo setsebool -P httpd_can_network_connect 1
sudo restorecon -Rv /opt/tomcat9/webapps/ROOT

# 6. 设置 Tomcat 目录的 SELinux 上下文
sudo semanage fcontext -a -t httpd_exec_t "/opt/tomcat9/webapps/ROOT(/.*)?"
sudo restorecon -Rv /opt/tomcat9/webapps/ROOT

# 7. 重启 Tomcat 服务
sudo systemctl start tomcat

# 8. 检查服务状态
sudo systemctl status tomcat
```

#### 其他可能的解决方案：

```bash
# 如果仍有问题，可以尝试临时禁用 SELinux 进行测试（不推荐生产环境）
sudo setenforce 0  # 临时禁用
# 测试访问是否正常，如果正常说明是 SELinux 问题

# 重新启用 SELinux
sudo setenforce 1

# 检查 Tomcat 配置文件中的 security-constraint
# 编辑 /opt/tomcat9/conf/web.xml，确保没有过于严格的安全约束
```

### 8.2 应用无法启动

```bash
# 1. 检查 Java 环境
java -version

# 2. 检查端口占用
sudo netstat -tulpn | grep 8080

# 3. 查看详细的启动日志
tail -n 100 /opt/tomcat9/logs/catalina.out

# 4. 检查 JVM 内存设置
# 编辑 /opt/tomcat9/bin/setenv.sh，调整堆内存大小

# 5. 检查应用依赖
ls -la /opt/tomcat9/webapps/ROOT/WEB-INF/lib/
```

### 8.3 数据库连接问题

```bash
# 1. 检查数据库配置文件
# vim /opt/tomcat9/webapps/ROOT/WEB-INF/classes/application.properties

# 2. 测试数据库连接
# 从应用服务器连接到 RDS 实例
mysql -h your_rds_endpoint -P 3306 -u your_username -p

# 3. 检查 RDS 安全组设置
# 确保 RDS 安全组允许来自应用服务器的连接（端口3306）
```

### 8.4 Redis 连接问题

```bash
# 1. 测试 Redis 连接
redis-cli -h 127.0.0.1 -p 6379 ping
# 如果设置了密码：redis-cli -h 127.0.0.1 -p 6379 -a your_password ping

# 2. 检查 Redis 服务状态
sudo systemctl status redis

# 3. 查看 Redis 日志
sudo tail -f /var/log/redis/redis-server.log

# 4. 检查 Redis 配置
sudo grep -E "^(bind|port|requirepass)" /etc/redis.conf
```

### 8.5 性能问题

```bash
# 1. 查看 JVM 内存使用情况
# 在应用中添加 JVM 监控，或使用 jstat 工具
jstat -gc $(pgrep java)

# 2. 查看系统资源使用
top
free -h
df -h

# 3. 调整 JVM 参数
# 编辑 /opt/tomcat9/bin/setenv.sh，根据实际情况调整内存分配
```

---
*请根据你的具体项目结构、配置文件路径、Gitee仓库地址、服务端口等，仔细替换文档中的占位符和示例值。确保应用程序的数据库配置指向正确的阿里云 RDS 实例，Redis 配置指向正确的 Redis 实例。*