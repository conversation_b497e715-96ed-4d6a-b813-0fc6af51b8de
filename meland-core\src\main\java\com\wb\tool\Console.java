package com.wb.tool;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.session.Session;

import com.wb.common.Var;
import com.wb.util.StringUtil;
import com.wb.util.WbUtil;
import com.wb.util.WebUtil;

public class Console {
	/**
	 * 向当前用户的浏览器控制台中输出指定对象的日志信息。
	 * @param request 请求对象。该请求对象用于关联到对应用户。
	 * @param object 打印的对象。
	 */
	public static void log(HttpServletRequest request, Object object) {
		print(request, object, "log");
	}

	/**
	 * 向当前用户的浏览器控制台中输出指定对象的调试信息。
	 * @param request 请求对象。该请求对象用于关联到对应用户。
	 * @param object 打印的对象。
	 */
	public static void debug(HttpServletRequest request, Object object) {
		print(request, object, "debug");
	}

	/**
	 * 向当前用户的浏览器控制台中输出指定对象的提示信息。
	 * @param request 请求对象。该请求对象用于关联到对应用户。
	 * @param object 打印的对象。
	 */
	public static void info(HttpServletRequest request, Object object) {
		print(request, object, "info");
	}

	/**
	 * 向当前用户的浏览器控制台中输出指定对象的警告信息。
	 * @param request 请求对象。该请求对象用于关联到对应用户。
	 * @param object 打印的对象。
	 */
	public static void warn(HttpServletRequest request, Object object) {
		print(request, object, "warn");
	}

	/**
	 * 向当前用户的浏览器控制台中输出指定对象的错误信息。
	 * @param request 请求对象。该请求对象用于关联到对应用户。
	 * @param object 打印的对象。
	 */
	public static void error(HttpServletRequest request, Object object) {
		print(request, object, "error");
	}

	/**
	 * 向当前用户的浏览器控制台中输出指定对象的字符串信息。
	 * @param request 请求对象。该请求对象用于关联到对应用户。
	 * @param object 打印的对象。
	 * @param type 输出类型。
	 * @param encoded 是否被编码，被编码的内容可在客户端解码。
	 */
	public static void print(HttpServletRequest request, Object object, String type, boolean encoded) {
		if (Var.serverConsolePrint)
			System.out.println(object);
		printToClient(request, object, type, encoded);
	}

	public static void printToClient(HttpServletRequest request, Object object, String type, boolean encoded) {
		if ((Var.consolePrint) || (Var.homeConsolePrint))
			printToClient(request.getSession(false), object, type, encoded);
	}

	public static void printToClient(HttpSession httpSession, Object object, String type, boolean encoded) {
		if (((!Var.consolePrint) && (!Var.homeConsolePrint)) || (httpSession == null))
			return;
		try {
			String text = encodeObject(object, type, encoded);
			if ((Var.consolePrint) && (WbUtil.canAccess(httpSession, "ide")))
				WebUtil.send(httpSession, "ide.console", text);
			if (Var.homeConsolePrint)
				WebUtil.send(httpSession, "sys.home", text);
		} catch (Throwable localThrowable) {
		}
	}
	
	public static void printToClient(Session httpSession, Object object, String type, boolean encoded) {
		if (((!Var.consolePrint) && (!Var.homeConsolePrint)) || (httpSession == null))
			return;
		try {
			String text = encodeObject(object, type, encoded);
			if ((Var.consolePrint) && (WbUtil.canAccess(httpSession, "ide")))
				WebUtil.send(httpSession, "ide.console", text);
			if (Var.homeConsolePrint)
				WebUtil.send(httpSession, "sys.home", text);
		} catch (Throwable localThrowable) {
		}
	}

	private static String encodeObject(Object object, String type, boolean encoded) {
		String string;
		if (object == null)
			string = "null";
		else
			string = object.toString();
		StringBuilder buf = new StringBuilder(string.length() + 30);
		buf.append("{\"cate\":\"console\",\"type\":\"");
		buf.append(type);
		buf.append("\",\"msg\":");
		buf.append(StringUtil.quote(string));
		if (encoded)
			buf.append(",\"encode\":true");
		buf.append('}');
		return buf.toString();
	}

	/**
	 * 向当前用户的浏览器控制台中输出指定对象的字符串信息。
	 * @param request 请求对象。该请求对象用于关联到对应用户。
	 * @param object 打印的对象。
	 * @param type 输出类型。
	 */
	public static void print(HttpServletRequest request, Object object, String type) {
		print(request, object, type, false);
	}

	public static void broadcast(Object object) throws Exception {
		broadcast(object, "info");
	}

	public static void broadcast(Object object, String type) throws Exception {
		if (Var.consolePrint)
			WebUtil.sendWithUrl("ide.console", "ide", encodeObject(object, type, false));
	}

	public static void print(String userId, Object object, String type) throws Exception {
		if (Var.consolePrint)
			WebUtil.send(userId, "ide.console", encodeObject(object, type, false));
	}

	public static void print(String userId, Object object) throws Exception {
		print(userId, object, "info");
	}
}