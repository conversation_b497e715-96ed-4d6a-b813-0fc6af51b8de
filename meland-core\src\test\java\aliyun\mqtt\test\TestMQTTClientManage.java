package aliyun.mqtt.test;

import com.wb.aliyun.mqtt.MQTTClientManage;

/**
 * <AUTHOR>
 * @date 2024/6/14-15:47
 */
public class TestMQTTClientManage {
    public static void main(String[] args) throws Exception {
        MQTTClientManage mqttClientManage = new MQTTClientManage("LTAI4FjhWy6j1yrRiue9dFrT",
                "******************************",
                "post-cn-omn3rs5sk01.mqtt.aliyuncs.com",
                "onsmqtt.cn-shenzhen.aliyuncs.com",
                "post-cn-omn3rs5sk01");

//        //同步查询客户端状态
//        mqttClientManage.createClientIdManager();
//        Boolean aBoolean = mqttClientManage.querySessionByClientId("GID_SELL_COIN_TEST@@@134P6CJ3BWY97");
//        System.out.println(aBoolean);


        //异步监听客户端状态
        mqttClientManage.createClientListener();
        mqttClientManage.subscribeStatus("GID_SELL_COIN_TEST");
//        mqttClientManage.stop();
    }
}
