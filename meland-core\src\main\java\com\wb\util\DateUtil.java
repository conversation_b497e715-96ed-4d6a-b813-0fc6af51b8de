package com.wb.util;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 日期工具方法类。
 */
public class DateUtil {
	/**
	 * 计算两个时间之间的周数
	 * 
	 * @param sdate 开始时间
	 * @param edate 结束时间
	 * @return 周数(学校授课周)
	 */
	public static int computeWeek(Date sdate, Date edate) {
		int wks = 1;
		Calendar sCalendar = Calendar.getInstance();
		sCalendar.setTime(sdate);
		sCalendar.setFirstDayOfWeek(Calendar.MONDAY);
		Calendar eCalendar = Calendar.getInstance();
		eCalendar.setTime(edate);
		eCalendar.setFirstDayOfWeek(Calendar.MONDAY);
		while (sCalendar.before(eCalendar)) {
			sCalendar.add(Calendar.DAY_OF_YEAR, 7);
			if (sCalendar.before(eCalendar) || sCalendar.compareTo(eCalendar) == 0) {
				wks++;
			}
		}
		return wks;
	}

	/**
	 * 计算指定时间教学周
	 * 
	 * @param sdate 学期开始时间
	 * @param edate 要计算的教学周日期
	 * @return 周数(学校授课周)
	 */
	public static int computeTeaWeek(Date sdate, Date edate) {
		int wks = 1;
		Calendar sCalendar = Calendar.getInstance();
		sCalendar.setTime(sdate);
		sCalendar.setFirstDayOfWeek(Calendar.MONDAY);
		Calendar eCalendar = Calendar.getInstance();
		eCalendar.setTime(edate);
		eCalendar.setFirstDayOfWeek(Calendar.MONDAY);
		while (sCalendar.before(eCalendar)) {
			if (sCalendar.get(Calendar.DAY_OF_WEEK) == 2)
				sCalendar.add(Calendar.DAY_OF_YEAR, 7);
			else
				sCalendar.add(Calendar.DAY_OF_YEAR, 7 - (sCalendar.get(Calendar.DAY_OF_WEEK) - 2));
			if (sCalendar.before(eCalendar) || sCalendar.compareTo(eCalendar) == 0) {
				wks++;
			}
		}
		return wks;
	}

	/**
	 * 计算时间段内的天数差，首尾当天都计入结果中
	 */
	public static int getMinusResultByDate(String startDateStr, String endDateStr) {
		int result = 0;
		if (endDateStr == null || startDateStr == null) {
			result = 0;
		} else {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
			try {
				Date startDate = format.parse(startDateStr);
				Date endDate = format.parse(endDateStr);
				result = getMinusResultByDate(startDate, endDate);
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		return result;
	}

	/**
	 * 计算时间段内的天数差，首尾当天都计入结果中
	 */
	public static int getMinusResultByDate(Date startDate, Date endDate) {
		Calendar startCld = Calendar.getInstance();
		startCld.setTime(startDate);

		Calendar endCld = Calendar.getInstance();
		endCld.setTime(endDate);
		return endCld.get(Calendar.DAY_OF_YEAR) - startCld.get(Calendar.DAY_OF_YEAR) + 1;
	}

	/**
	 * 计算时间段内周六、周日的个数 包括首尾日期
	 */
	public static float getWeekendByDates(String startDateStr, String endDateStr) {
		float result = 0;
		int minus = getMinusResultByDate(startDateStr, endDateStr);
		if (minus > 0) {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
			try {
				Date startDate = format.parse(startDateStr);

				Calendar startCld = Calendar.getInstance();
				startCld.setTime(startDate);
				int startDayWeek = startCld.get(Calendar.DAY_OF_WEEK); // 算出开始日期是一周的第几天，calendar日历周日为每周的第一天
				int totalWeekDay = minus + startDayWeek - 2; // 算出从开始日期所在周的第一天开始到结束时间的所有天数
				int weekendNum = totalWeekDay / 7; // 算出周末个数
				result = weekendNum * 2;
				int dayOfWeek = totalWeekDay % 7;
				// 表明是周六
				if (dayOfWeek == 6) {
					result += 1;
				}

			} catch (ParseException e) {
				e.printStackTrace();
			}
		} else {
			result = 0;
		}
		return result;
	}

	/**
	 * 对时间的做加月份的加法，返回结果
	 */
	public static String dateMonthPlus(String startDateStr, int plusNum) {
		String endDateStr = "";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		try {
			Date startDate = sdf.parse(startDateStr);
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(startDate);
			int month = calendar.get(Calendar.MONTH);
			calendar.set(Calendar.MONTH, month + plusNum);
			Date endDate = calendar.getTime();
			endDateStr = sdf.format(endDate);

		} catch (ParseException e) {
			e.printStackTrace();
		}
		return endDateStr;
	}

	/**
	 * 格式化日期。
	 * 
	 * @param date 需要格式化的日期。
	 * @param format 日期格式。
	 * @return 格式化后的日期字符串。如果date为空则返回空字符串。
	 */
	public static String format(Date date, String format) {
		if (date == null)
			return "";
		SimpleDateFormat dateFormat = new SimpleDateFormat(format);
		return dateFormat.format(date);
	}

	/**
	 * 按yyyy-MM-dd HH:mm:ss格式化日期。
	 * 
	 * @param date 需要格式化的日期。
	 * @return 格式化后的日期字符串。如果date为空则返回空字符串。
	 */
	public static String format(Date date) {
		return format(date, "yyyy-MM-dd HH:mm:ss");
	}

	/**
	 * 按yyyy-MM-dd格式化日期。
	 * 
	 * @param date 需要格式化的日期。
	 * @return 格式化后的日期字符串。如果date为空则返回空字符串。
	 */
	public static String formatDate(Date date) {
		return format(date, "yyyy-MM-dd");
	}

	/**
	 * 把以长整数表示的时间戳转换为Timestamp对象。
	 * 
	 * @param time 时间戳值。
	 * @return 转换后的Timestamp对象。
	 */
	public static Timestamp getTimestamp(long time) {
		return new Timestamp(time);
	}

	public static Timestamp now() {
		return new Timestamp(System.currentTimeMillis());
	}

	/**
	 * 获取指定日期所在月的天数。
	 * 
	 * @param date 日期。
	 * @return 位于所在月的天数。
	 */
	public static int daysInMonth(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return cal.getActualMaximum(Calendar.DAY_OF_MONTH);
	}

	/**
	 * 获取指定日期在所在月位于第几天。
	 * 
	 * @param date 日期。
	 * @return 位于所在月的第天几。
	 */
	public static int dayOfMonth(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return cal.get(Calendar.DAY_OF_MONTH);
	}

	/**
	 * 获取指定日期的年部分值。
	 * 
	 * @param date 日期。
	 * @return 年部分值。
	 */
	public static int yearOf(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return cal.get(Calendar.YEAR);
	}

	/**
	 * 获取指定日期在所在年位于第几天。
	 * 
	 * @param date 日期。
	 * @return 位于所在年的第几天。
	 */
	public static int dayOfYear(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return cal.get(Calendar.DAY_OF_YEAR);
	}

	/**
	 * 获取指定日期在所在周位于第几天。
	 * 
	 * @param date 日期。
	 * @return 位于所在周的第几天。
	 */
	public static int dayOfWeek(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		int week = cal.get(Calendar.DAY_OF_WEEK) - 1;
		if (week < 0)
			week = 0;
		return week;
	}

	/**
	 * 把日期转换成yyyy-MM-dd HH:mm:ss.fffffffff格式的字符串。
	 * 
	 * @param value 需要转换的日期。
	 * @return 转换后的字符串。
	 */
	public static String dateToStr(Date value) {
		if (value == null)
			return null;
		Timestamp t = new Timestamp(value.getTime());
		return t.toString();
	}

	/**
	 * 把以 yyyy-MM-dd HH:mm:ss[.f...] 格式的字符串转换成时间戳。如果value为null或空串返回null。
	 * 
	 * @param value 需要转换的字符串。
	 * @return 转换后的时间戳。
	 */
	public static Timestamp strToDate(String value) {
		if (StringUtil.isEmpty(value))
			return null;
		return Timestamp.valueOf(value);
	}

	/**
	 * 判断指定的字符串是否为一个有效的标准日期格式。
	 * 
	 * @return true有效的日期格式，false不是日期格式。
	 */
	public static boolean isDate(String dateStr) {
		int len = dateStr.length();

		if (len < 19)
			return false;
		for (int i = 0; i < len; i++) {
			char ch = dateStr.charAt(i);
			switch (i) {
			case 4:
			case 7:
				if (ch != '-')
					return false;
				break;
			case 10:
				if (ch != ' ')
					return false;
				break;
			case 13:
			case 16:
				if (ch != ':')
					return false;
				break;
			case 19:
				if (ch != '.')
					return false;
				break;
			default:
				if ((ch < '0') || (ch > '9'))
					return false;
				break;
			}
		}
		return true;
	}

	/**
	 * 在指定日期上增加年数。
	 * 
	 * @param date 日期。
	 * @param years 增加的年数。
	 * @return 增加年数后的日期。
	 */
	public static Date incYear(Date date, int years) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.YEAR, years);
		return cal.getTime();
	}

	/**
	 * 在指定日期上增加月数。
	 * 
	 * @param date 日期。
	 * @param months 增加的月数。
	 * @return 增加月数后的日期。
	 */
	public static Date incMonth(Date date, int months) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.MONTH, months);
		return cal.getTime();
	}

	/**
	 * 获取指定日期的小时部分值。
	 * 
	 * @param date 日期。
	 * @return 小时值。
	 */
	public static int hourOfDay(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return cal.get(Calendar.HOUR_OF_DAY);
	}

	/**
	 * 把指定毫秒数格式化成 HH:mm:ss.fff形式显示的字符串。小时值允许大于24小时显示。
	 * 
	 * @param milliSecs 毫秒数。
	 * @return 格式化后的文本。
	 */
	public static String format(long milliSecs) {
		long h = milliSecs / 3600000;
		long hm = milliSecs % 3600000;
		long m = hm / 60000;
		long mm = hm % 60000;
		long s = mm / 1000;
		long sm = mm % 1000;

		return StringUtil.concat(Long.toString(h), ":", Long.toString(m), ":", Long.toString(s),
				Float.toString((float) sm / 1000.0F).substring(1));
	}

	/**
	 * 在指定日期上增加天数。
	 * 
	 * @param date 日期。
	 * @param days 增加的天数。
	 * @return 增加天数后的日期。
	 */
	public static Date incDay(Date date, long days) {
		return new Date(date.getTime() + 86400000 * days);
	}

	/**
	 * 在指定日期上增加秒数。
	 * 
	 * @param date 日期。
	 * @param seconds 增加的秒数。
	 * @return 增加秒数后的日期。
	 */
	public static Date incSecond(Date date, long seconds) {
		return new Date(date.getTime() + 1000 * seconds);
	}

	/**
	 * 计算两个日期相差的天数
	 * 
	 * @param beginDate 开始日期。
	 * @param endDate 结束日期。
	 * @return 去掉小数后的相差天数。
	 */
	public static int getElapsedDays(Date beginDate, Date endDate) {
		return (int) ((endDate.getTime() - beginDate.getTime()) / 86400000);
	}

	/**
	 * 计算两个日期相差的天数
	 * 
	 * @param startDateStr 开始日期。
	 * @param endDateStr 结束日期。
	 * @return 去掉小数后的相差天数。
	 */
	public static int getElapsedDays(String startDateStr, String endDateStr) {
		int result = 0;
		if (endDateStr == null || startDateStr == null) {
			result = 0;
		} else {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
			try {
				Date startDate = format.parse(startDateStr);
				Date endDate = format.parse(endDateStr);
				result = getElapsedDays(startDate, endDate);
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		return result;
	}

	/**
	 * 修正以字符串形式表示的时间值，如果时间不符合格式将修正该值。
	 * 
	 * @param str 以字符串形式表达的时间值。
	 * @return 修正后的值。
	 */
	public static String fixTime(String str) {
		if (str.indexOf(':') == -1)
			return "00:00:00";
		int b = str.indexOf(' ');
		int e = str.indexOf('.');
		if (b == -1)
			b = 0;
		else
			b++;
		if (e == -1)
			e = str.length();
		return str.substring(b, e);
	}

	/**
	 * 修正以字符串形式表示的日期时间值，如果日期时间不符合格式将修正该值。
	 * 
	 * @param str 以字符串形式表达的日期时间值。
	 * @param dateOnly 是否只返回日期部分字符串。
	 * @return 修正后的值。
	 */
	public static String fixTimestamp(String str, boolean dateOnly) {
		int pos = str.indexOf(' ');
		String datePart, timePart = "", sec[];
		if (pos == -1) {
			datePart = str;
			if (!dateOnly)
				timePart = "00:00:00";
		} else {
			datePart = str.substring(0, pos);
			if (!dateOnly)
				timePart = str.substring(pos + 1);
		}
		sec = StringUtil.split(datePart, "-");
		if (sec.length == 3) {
			StringBuilder buf = new StringBuilder(dateOnly ? 10 : 30);
			buf.append(sec[0]);
			buf.append('-');
			if (sec[1].length() == 1)
				buf.append('0');
			buf.append(sec[1]);
			buf.append('-');
			if (sec[2].length() == 1)
				buf.append('0');
			buf.append(sec[2]);
			if (!dateOnly) {
				buf.append(' ');
				buf.append(timePart);
			}
			return buf.toString();
		} else if (sec.length == 2) {
			StringBuilder buf = new StringBuilder(dateOnly ? 10 : 30);
			buf.append(sec[0]);
			buf.append('-');
			if (sec[1].length() == 1)
				buf.append('0');
			buf.append(sec[1]);
			buf.append('-');
			buf.append("01");
			if (!dateOnly) {
				buf.append(' ');
				buf.append(timePart);
			}
			return buf.toString();
		} else if (str.length() == 8) {
			return StringUtil.format("{0}-{1}-{2}", str.substring(0, 4), str.substring(4, 6), str.substring(6, 8));
		} else if (str.length() == 6) {
			return StringUtil.format("{0}-{1}-{2}", str.substring(0, 4), str.substring(4, 6), "01");
		} else if (str.length() == 4) {
			return StringUtil.format("{0}-{1}-{2}", str, "01", "01");
		} else
			return str;
	}

	/**
	 * 根据开始时间、结束时间得到两个时间段内所有的日期
	 * @param start 开始日期
	 * @param end   结束日期
	 * @param calendarType  类型
	 * @return  两个日期之间的日期
	 */
	public static Date[] getDateArrays(Date start, Date end, int calendarType) {
		List<Date> ret = new ArrayList<Date>();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(start);
		Date tmpDate = calendar.getTime();
		long endTime = end.getTime();
		while (tmpDate.before(end) || tmpDate.getTime() == endTime) {
			ret.add(calendar.getTime());
			calendar.add(calendarType, 1);
			tmpDate = calendar.getTime();
		}
		Date[] dates = new Date[ret.size()];
		return (Date[]) ret.toArray(dates);
	}
}