package aliyun.mqtt.test;

import com.wb.aliyun.mqtt.MessageClient;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/15-12:15
 */
public class testMessageP {
    public static void main(String[] args) throws Exception {
        MessageClient client = MessageClient.getMessageClient("post-cn-omn3rs5sk01",
                "post-cn-omn3rs5sk01.mqtt.aliyuncs.com",
                "onsmqtt.cn-shenzhen.aliyuncs.com",
                "LTAI4FjhWy6j1yrRiue9dFrT",
                "******************************",
                false,
                "GID_SELL_COIN_TEST@@@tjf",
                true);
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        String format = simpleDateFormat.format(date);
        System.out.println(format);
        System.out.println(client);
        client.disconnect();
        for (int i = 0; i < 3; i++) {
//            client.sendMessage("T_SELL_COIN/E8F40839F1C5", 0, new String[]{"发送消息1" + format + " - " + i, "发送消息2" + format + " - " + i});
            client.sendMessageP2P("T_SELL_COIN_TEST", "GID_SELL_COIN_TEST@@@214P6DW4W4712", 0, new String[]{"消息的内容p2p-2 " + format + " - " + i});
        }

//        client.disconnect();

        MessageClient client2 = MessageClient.getMessageClient("post-cn-omn3rs5sk01",
                "post-cn-omn3rs5sk01.mqtt.aliyuncs.com",
                "onsmqtt.cn-shenzhen.aliyuncs.com",
                "LTAI4FjhWy6j1yrRiue9dFrT",
                "******************************",
                false,
                "GID_SELL_COIN_TEST@@@tjf",
                true);
        System.out.println(client2);


//        client.disconnect();


    }
}
