/**
 * FeyaSoft MyCalendar
 * Copyright(c) 2006-2012, FeyaSoft Inc. All right reserved.
 * <EMAIL>
 * http://www.cubedrive.com
 *
 * Please read license first before your use myCalendar, For more detail
 * information, please can visit our link: http://www.cubedrive.com/myCalendar
 *
 * You need buy one of the Feyasoft's License if you want to use MyCalendar in
 * your commercial product. You must not remove, obscure or interfere with any
 * FeyaSoft copyright, acknowledgment, attribution, trademark, warning or
 * disclaimer statement affixed to, incorporated in or otherwise applied in
 * connection with the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY
 * KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * WARRANTIES OF MERCHANTABILITY,FITNESS FOR A PARTICULAR PURPOSE
 * AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
 * OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
 * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
Ext.define('Ext.ux.calendar.editor.CalendarEditor', {
  extend: 'Ext.Window',
  initComponent: function() {
    var lan = Ext.ux.calendar.Mask.CalendarEditor;

    this.nameField = this.nameField || Ext.create('Ext.form.TextField', {
      name: 'name',
      fieldLabel: lan['nameField.label'],
      labelAlign: 'right',
      labelWidth: 60,
      allowBlank: false,
      anchor: '99%'
    });

    this.descriptionField = this.descriptionField || Ext.create('Ext.form.TextField', {
      name: 'description',
      fieldLabel: lan['descriptionField.label'],
      labelAlign: 'right',
      labelWidth: 60,
      anchor: '99%'
    });

    this.colorField = this.colorField || Ext.create('Ext.picker.Color');
    this.colorField.on('select', this.onColorSelectFn, this);
    this.colorField.colors = Ext.ux.calendar.Mask.colors;

    this.clearBtn = this.clearBtn || Ext.create('Ext.Button', {
      buttonStyle: 'danger',
      scale: 'medium',
      minWidth: 80,
      text: lan['clearBtn.text'],
      handler: this.onClearFn,
      scope: this
    });

    this.saveBtn = this.saveBtn || Ext.create('Ext.Button', {
      buttonStyle: 'primary',
      scale: 'medium',
      minWidth: 80,
      text: lan['saveBtn.text'],
      handler: this.onSaveFn,
      scope: this
    });

    this.cancelBtn = this.cancelBtn || Ext.create('Ext.Button', {
      buttonStyle: 'default',
      scale: 'medium',
      minWidth: 80,
      text: lan['cancelBtn.text'],
      handler: this.onCancelFn,
      scope: this
    });

    this.formpanel = this.formpanel || Ext.create('Ext.form.FormPanel', {
      cls: 'x-calendar-menu',
      border: false,
      style: 'padding:10px;',
      labelWidth: 70,
      items: [this.nameField, this.descriptionField, {
        border: false,
        style: 'padding-left:65px;',
        items: [this.colorField]
      }],
      buttonAlign: 'right',
      buttons: [this.clearBtn, this.saveBtn, this.cancelBtn]
    });
    Ext.apply(this, {
      width: 500,
      height: 220,
      closable: false,
      closeAction: 'hide',
      layout: 'fit',
      modal: true,
      resizable: false,
      items: [{
        border: false,
        layout: 'fit',
        items: [this.formpanel]
      }]
    });
    this.callParent(arguments);
  },
  onColorSelectFn: function(cp, color) {
    this.color = Ext.ux.calendar.Mask.getIndexByColor(color);
  },

  popup: function(obj) {
    this.action = obj.action;
    this.show();
    var lan = Ext.ux.calendar.Mask.CalendarEditor;
    if ('add' == obj.action) {
      this.setTitle(lan['new.title']);
      this.setIconCls('icon_feyaCalendar_calendar');
    } else {
      this.setTitle(lan['edit.title']);
      this.setIconCls('icon_feyaCalendar_calendar_edit');
    }
    if (obj.cEl) {
      this.calendarEl = obj.cEl;
    } else {
      this.calendarEl = null;
    }
    var mask = Ext.ux.calendar.Mask;
    if (obj.data) {
      this.calendar = obj.data;
      var data = obj.data;
      this.nameField.setValue(data.name);
      this.descriptionField.setValue(data.description);
      var color = data.color;
      var cl = Ext.ux.calendar.Mask.getColorByIndex(color);
      if (cl) {
        this.colorField.select(cl);
      } else {
        this.colorField.select(mask.colors[0]);
      }
    } else {
      this.nameField.reset();
      this.descriptionField.reset();
      this.colorField.select(mask.colors[0]);
    }
  },

  onClearFn: function() {
    this.formpanel.form.reset();
  },

  onSaveFn: function() {
    if (this.formpanel.form.isValid()) {
      var params = {};
      if (this.calendar) {
        params.id = this.calendar.id;
        params.hide = this.calendar.hide;
      } else {
        params.hide = false;
      }
      params.name = this.nameField.getValue();
      params.description = this.descriptionField.getValue();
      params.color = this.color;
      var eh = this.ehandler;
      eh.ds.createUpdateCalendar(params, function(backObj) {
        var cEl = this.calendarEl;
        if (cEl) {
          var oldColor = cEl.calendar.color;
          var oldName = cEl.calendar.name;
          Ext.apply(cEl.calendar, params);
          var color = cEl.calendar.color;
          eh.calendarSet[cEl.calendar.id] = cEl.calendar;
          var titleEl = cEl.down('.x-calendar-title-b');
          if (titleEl) {
            titleEl.dom.innerHTML = '<b>' + params.name + '</b>';
          }
          if (oldColor != color) {
            cEl.calendar.color = oldColor;
            eh.changeColor(cEl.calendar, color);
          } else if (oldName != params.name) {
            eh.checkExpireEvents();
          }
        } else if (backObj.id) {
          var calendar = Ext.apply({}, params);
          calendar.id = backObj.id;
          eh.calendarSet[calendar.id] = calendar;
          var mc = eh.mainPanel.westPanel.myCalendarPanel;
          eh.createCalendar(mc.body, null, null, calendar);
          var css = '.' + eh.id + '-x-calendar-' + calendar.id + '{}';
          eh.ss[eh.ss.length] = Ext.util.CSS.createStyleSheet(css,
            Ext.id());
        }
      }, this);
      this.hide();
    }
  },

  onCancelFn: function() {
    this.hide();
  }
});