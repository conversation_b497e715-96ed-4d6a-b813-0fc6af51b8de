package com.wb.aliyun.sls;

import com.aliyun.openservices.aliyun.log.producer.*;
import com.aliyun.openservices.aliyun.log.producer.errors.LogSizeTooLargeException;
import com.aliyun.openservices.aliyun.log.producer.errors.ProducerException;
import com.aliyun.openservices.aliyun.log.producer.errors.TimeoutException;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.wb.common.Var;
import com.wb.util.StringUtil;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 阿里云SLS日志服务工具类
 * 提供日志的发送、查询等功能
 */
public class SLSLogUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(SLSLogUtil.class);
    private static Client client;
    private static Producer producer;
    // Project名称
    private static String project;
    // 是否已初始化
    private static final AtomicBoolean initialized = new AtomicBoolean(false);
    // 批量发送缓存
    private static final Map<String, List<LogItem>> batchLogsMap = new ConcurrentHashMap<>();
    // 批量发送阈值，达到此数量将触发发送
    private static final int BATCH_SIZE_THRESHOLD = 100;
    // 批量发送时间间隔（毫秒）
    private static final long BATCH_TIME_THRESHOLD = 3000;
    
    // 优化：自定义线程池，避免无限创建线程
    private static final int CORE_POOL_SIZE = Math.max(2, Runtime.getRuntime().availableProcessors() / 2);
    private static final int MAX_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;
    private static final long KEEP_ALIVE_TIME = 60L;
    private static ThreadPoolExecutor logExecutor;
    
    // 优化：用于控制日志发送速率的信号量
    private static final int MAX_CONCURRENT_LOGS = 1000;
    private static Semaphore logSemaphore;
    
    // 优化：追踪日志发送情况
    private static final AtomicInteger pendingLogCount = new AtomicInteger(0);
    private static final AtomicInteger successLogCount = new AtomicInteger(0);
    private static final AtomicInteger failedLogCount = new AtomicInteger(0);
    
    // 记录批处理和监控线程引用，用于优雅关闭
    private static Thread batchSendThread;
    private static Thread monitorThread;
    
    // 设置批量队列最大容量，防止内存溢出
    private static final int MAX_BATCH_QUEUE_SIZE = 10000;
    
    /**
     * 初始化Producer和Client
     * 从配置中读取阿里云SLS相关配置
     */
    public static synchronized void initProducer() {
        if (initialized.get()) {
            return;
        }

        String accessKeySecret = Var.getString("sys.config.sls.accessKeySecret");
        String accessKeyId = Var.getString("sys.config.sls.accessKeyId");
        // 日志服务的服务入口
        String endpoint = Var.getString("sys.config.sls.endpoint");
        project = Var.getString("sys.config.sls.project");

        if (StringUtil.isEmpty(accessKeySecret) || StringUtil.isEmpty(accessKeyId)
                || StringUtil.isEmpty(endpoint) || StringUtil.isEmpty(project)) {
            LOGGER.error("SLS配置不完整，初始化失败");
            return;
        }

        try {
            // 配置Producer
            ProducerConfig producerConfig = new ProducerConfig();
            // 优化：根据可用处理器数量设置IO线程数
            producerConfig.setIoThreadCount(Math.max(1, Runtime.getRuntime().availableProcessors() / 2));
            // 设置最大阻塞时间，单位毫秒
            producerConfig.setMaxBlockMs(6000);  // 减少阻塞时间，避免长时间卡住业务线程
            // 设置发送失败重试次数
            producerConfig.setRetries(2);  // 减少重试次数，避免失败时占用太多资源
            // 优化：增加单个批次的条数限制，提高批量处理效率
            producerConfig.setBatchCountThreshold(512);
            // 设置单个批次的最大大小
            producerConfig.setBatchSizeThresholdInBytes(3 * 1024 * 1024);
            // 设置内存中缓存的日志数量上限
            producerConfig.setTotalSizeInBytes(100 * 1024 * 1024);
            // 优化：减少批次发送的间隔时间，提高吞吐量
            producerConfig.setLingerMs(1000);

            producer = new LogProducer(producerConfig);
            producer.putProjectConfig(new ProjectConfig(project, endpoint, accessKeyId, accessKeySecret));

            // 初始化客户端
            client = new Client(endpoint, accessKeyId, accessKeySecret);
            
            // 优化：初始化自定义线程池
            logExecutor = new ThreadPoolExecutor(
                CORE_POOL_SIZE,
                MAX_POOL_SIZE,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(5000),  // 有界队列，避免无限增长
                new ThreadFactory() {
                    private final AtomicInteger counter = new AtomicInteger(1);
                    @Override
                    public Thread newThread(Runnable r) {
                        Thread thread = new Thread(r);
                        thread.setName("sls-log-worker-" + counter.getAndIncrement());
                        thread.setDaemon(true);  // 使用守护线程，不阻止JVM退出
                        thread.setUncaughtExceptionHandler((t, e) -> 
                            LOGGER.error("线程{}发生未捕获异常", t.getName(), e));
                        return thread;
                    }
                },
                new ThreadPoolExecutor.CallerRunsPolicy()  // 当队列满时，在调用者线程中执行任务
            );
            
            // 优化：初始化信号量
            logSemaphore = new Semaphore(MAX_CONCURRENT_LOGS);

            // 启动定时批量发送任务
            startBatchSendTask();
            
            // 优化：添加性能监控任务
            startMonitorTask();

            initialized.set(true);
            LOGGER.info("SLS Producer初始化成功，project={}, endpoint={}", project, endpoint);
        } catch (Exception e) {
            LOGGER.error("SLS Producer初始化失败", e);
        }
    }

    /**
     * 启动定时批量发送任务
     */
    private static void startBatchSendTask() {
        batchSendThread = new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    TimeUnit.MILLISECONDS.sleep(BATCH_TIME_THRESHOLD);
                    flushAllBatchLogs();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    LOGGER.warn("批量发送线程被中断");
                } catch (Exception e) {
                    LOGGER.error("批量发送日志异常", e);
                }
            }
        });
        batchSendThread.setDaemon(true);
        batchSendThread.setName("SLS-BatchSend-Thread");
        batchSendThread.setUncaughtExceptionHandler((t, e) -> 
            LOGGER.error("批量发送线程发生未捕获异常", t.getName(), e));
        batchSendThread.start();
    }
    
    /**
     * 优化：启动性能监控任务
     */
    private static void startMonitorTask() {
        monitorThread = new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    TimeUnit.SECONDS.sleep(60);  // 每分钟输出一次统计信息
                    int pending = pendingLogCount.get();
                    int success = successLogCount.getAndSet(0);
                    int failed = failedLogCount.getAndSet(0);
                    LOGGER.info("SLS日志统计 - 待处理: {}, 成功: {}, 失败: {}, 线程池活动线程: {}, 队列大小: {}",
                            pending, success, failed, logExecutor.getActiveCount(), logExecutor.getQueue().size());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    LOGGER.error("监控任务异常", e);
                }
            }
        });
        monitorThread.setDaemon(true);
        monitorThread.setName("SLS-Monitor-Thread");
        monitorThread.setUncaughtExceptionHandler((t, e) -> 
            LOGGER.error("监控线程发生未捕获异常", t.getName(), e));
        monitorThread.start();
    }

    /**
     * 刷新所有批量日志
     */
    private static void flushAllBatchLogs() {
        if (batchLogsMap.isEmpty()) {
            return;
        }

        // 创建一个副本，避免并发修改异常
        Map<String, List<LogItem>> mapCopy = new HashMap<>(batchLogsMap);
        
        mapCopy.forEach((logStore, logItems) -> {
            if (!logItems.isEmpty()) {
                List<LogItem> itemsToSend = null;
                synchronized (logItems) {
                    if (!logItems.isEmpty()) {
                        itemsToSend = new ArrayList<>(logItems);
                        logItems.clear();
                    }
                }
                if (itemsToSend != null && !itemsToSend.isEmpty()) {
                    sendBatchLogsInternal(itemsToSend, logStore);
                }
            }
        });
    }

    /**
     * 发送单条日志
     *
     * @param obj      日志内容
     * @param logStore 日志logStore（类型）
     */
    public static void sendLog(JSONObject obj, String logStore) {
        checkInitialized();

        LogItem logItem = convertToLogItem(obj);
        try {
            producer.send(project, logStore, "", "", logItem, new Callback() {
                @Override
                public void onCompletion(Result result) {
                    if (result.isSuccessful()) {
                        LOGGER.debug("发送日志成功, project={}, logStore={}", project, logStore);
                    } else {
                        LOGGER.error("发送日志失败, project={}, logStore={}, errorCode={}, errorMessage={}",
                                project, logStore, result.getErrorCode(), result.getErrorMessage());
                    }
                }
            });
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOGGER.warn("发送日志时线程被中断");
        } catch (Exception e) {
            handleSendException(e, logItem);
        }
    }

    /**
     * 异步发送日志
     *
     * @param obj      日志内容
     * @param logStore 日志logStore（类型）
     * @return CompletableFuture<Boolean> 发送结果
     */
    public static CompletableFuture<Boolean> sendLogAsync(JSONObject obj, String logStore) {
        // 优化：高负载时直接进入批量处理队列
        if (pendingLogCount.get() > MAX_CONCURRENT_LOGS) {
            addToBatch(obj, logStore);
            CompletableFuture<Boolean> future = new CompletableFuture<>();
            future.complete(true);
            return future;
        }
        
        checkInitialized();
        pendingLogCount.incrementAndGet();
        
        CompletableFuture<Boolean> future = new CompletableFuture<>();
        
        // 优化：使用自定义线程池处理日志发送
        logExecutor.execute(() -> {
            boolean acquired = false;
            try {
                // 优化：限制并发量
                try {
                    acquired = logSemaphore.tryAcquire(500, TimeUnit.MILLISECONDS);
                    if (!acquired) {
                        // 无法获取信号量时，添加到批量队列中
                        addToBatch(obj, logStore);
                        future.complete(true);
                        return;
                    }
                    
                    LogItem logItem = convertToLogItem(obj);
                    producer.send(project, logStore, "", "", logItem, new Callback() {
                        @Override
                        public void onCompletion(Result result) {
                            try {
                                boolean success = result.isSuccessful();
                                if (success) {
                                    successLogCount.incrementAndGet();
                                    LOGGER.debug("异步发送日志成功,log={}, project={}, logStore={}", logItem.ToJsonString(), project, logStore);
                                } else {
                                    failedLogCount.incrementAndGet();
                                    LOGGER.error("异步发送日志失败,log={}, project={}, logStore={}, errorCode={}, errorMessage={}",
                                            logItem.ToJsonString(), project, logStore, result.getErrorCode(), result.getErrorMessage());
                                }
                                future.complete(success);
                            } finally {
                                pendingLogCount.decrementAndGet();
                            }
                        }
                    });
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw e; // 重新抛出异常以便外部捕获处理
                }
            } catch (Exception e) {
                pendingLogCount.decrementAndGet();
                failedLogCount.incrementAndGet();
                
                // 异常情况下也添加到批量队列中，保证尽可能不丢日志
                try {
                    addToBatch(obj, logStore);
                } catch (Exception ex) {
                    LOGGER.error("添加到批量队列失败", ex);
                }
                
                future.complete(false);
            } finally {
                if (acquired) {
                    logSemaphore.release();
                }
            }
        });

        return future;
    }

    /**
     * 添加日志到批量发送队列
     *
     * @param obj      日志内容
     * @param logStore 日志logStore（类型）
     */
    public static void addToBatch(JSONObject obj, String logStore) {
        checkInitialized();

        LogItem logItem = convertToLogItem(obj);
        List<LogItem> logItems = batchLogsMap.computeIfAbsent(logStore, k -> new ArrayList<>());

        synchronized (logItems) {
            // 检查批量队列大小限制
            if (logItems.size() >= MAX_BATCH_QUEUE_SIZE) {
                // 队列已满，丢弃最早的日志或进行其他处理
                logItems.remove(0);
                LOGGER.warn("批量队列已满，丢弃最早的日志，logStore={}", logStore);
            }
            
            logItems.add(logItem);
            if (logItems.size() >= BATCH_SIZE_THRESHOLD) {
                List<LogItem> itemsToSend = new ArrayList<>(logItems);
                logItems.clear();
                // 异步发送批量日志
                logExecutor.execute(() -> sendBatchLogsInternal(itemsToSend, logStore));
            }
        }
    }

    /**
     * 批量发送日志
     *
     * @param logItems 日志项列表
     * @param logStore 日志logStore（类型）
     */
    private static void sendBatchLogsInternal(List<LogItem> logItems, String logStore) {
        if (logItems.isEmpty()) {
            return;
        }

        try {
            producer.send(project, logStore, logItems);
            successLogCount.addAndGet(logItems.size());
            LOGGER.debug("批量发送日志成功, project={}, logStore={}, count={}", project, logStore, logItems.size());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOGGER.warn("批量发送日志时线程被中断");
            // 重新添加到批量发送队列
            addToBatchInternal(logItems, logStore);
        } catch (Exception e) {
            failedLogCount.addAndGet(logItems.size());
            LOGGER.error("批量发送日志失败, project={}, logStore={}, count={}, error={}",
                    project, logStore, logItems.size(), e.getMessage());
            // 重新添加到批量发送队列，但避免无限循环重试
            if (logItems.size() <= 10) {  // 只对小批量进行重试，避免大批量失败导致资源浪费
                addToBatchInternal(logItems, logStore);
            }
        }
    }
    
    /**
     * 将日志项列表添加到批量发送队列
     */
    private static void addToBatchInternal(List<LogItem> logItems, String logStore) {
        if (logItems.isEmpty()) {
            return;
        }
        
        List<LogItem> existingLogItems = batchLogsMap.computeIfAbsent(logStore, k -> new ArrayList<>());
        synchronized (existingLogItems) {
            existingLogItems.addAll(logItems);
        }
    }

    /**
     * 将JSONObject转换为LogItem
     *
     * @param obj JSON对象
     * @return LogItem
     */
    private static LogItem convertToLogItem(JSONObject obj) {
        LogItem logItem = new LogItem();
        // 添加时间戳，如果没有则使用当前时间
        if (!obj.has("timestamp")) {
            logItem.SetTime((int) (System.currentTimeMillis() / 1000));
        }

        for (String key : obj.keySet()) {
            Object value = obj.get(key);
            if (value != null) {
                logItem.PushBack(key, value.toString());
            } else {
                logItem.PushBack(key, "");
            }
        }
        return logItem;
    }

    /**
     * 处理发送异常
     *
     * @param e       异常
     * @param logItem 日志项
     */
    private static void handleSendException(Exception e, LogItem logItem) {
        if (e instanceof LogSizeTooLargeException) {
            LOGGER.error("日志大小超过最大允许大小", e);
        } else if (e instanceof TimeoutException) {
            LOGGER.error("分配内存超时", e);
        } else if (e instanceof ProducerException) {
            LOGGER.error("Producer异常", e);
        } else {
            LOGGER.error("发送日志失败, logItem={}", logItem.ToJsonString(), e);
        }
    }

    /**
     * 检查是否已初始化
     */
    private static void checkInitialized() {
        if (!initialized.get()) {
            initProducer();
            if (!initialized.get()) {
                throw new RuntimeException("SLS配置未初始化或初始化失败");
            }
        }
    }

    /**
     * 关闭producer，将缓存的所有日志数据都发送完毕
     */
    public static void close() {
        if (!initialized.get() || producer == null) {
            return;
        }

        try {
            // 停止后台线程
            if (batchSendThread != null) {
                batchSendThread.interrupt();
            }
            if (monitorThread != null) {
                monitorThread.interrupt();
            }
            
            // 先刷新所有批量日志
            flushAllBatchLogs();
            // 关闭producer
            producer.close();
            // 关闭自定义线程池
            logExecutor.shutdown();
            try {
                // 等待线程池中的任务完成
                if (!logExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    logExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logExecutor.shutdownNow();
            }
            LOGGER.info("SLS Producer已关闭");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOGGER.warn("关闭Producer时线程被中断");
        } catch (ProducerException e) {
            LOGGER.error("关闭Producer失败", e);
        } finally {
            initialized.set(false);
        }
    }

    /**
     * 通过查询分析语句查询日志，返回某时间区间内的原始日志
     *
     * @param logStore  日志logStore（类型）
     * @param from      Unix时间戳格式。查询开始时间点。该时间是指写入日志数据时指定的日志时间。
     * @param to        Unix时间戳格式。查询结束时间点。该时间是指写入日志数据时指定的日志时间。
     * @param queryText 查询分析语句
     * @param offset    查询开始行。默认值为0。
     * @param line      请求返回的最大日志条数，最小值为0，最大值为100，默认值为100
     * @return JSONArray 查询结果
     * @throws LogException 日志异常
     */
    public static JSONArray queryLogArray(String logStore, int from, int to, String queryText, int offset, int line)
            throws LogException {
        checkInitialized();

        // 阿里云SLS限制单次查询最多返回100条数据
        if (line > 100) {
            LOGGER.warn("查询行数超过100，已自动调整为100。如需查询更多数据，请使用分页查询方法");
            line = 100;
        }

        try {
            // 执行实际查询
            GetLogsResponse getLogsResponse = client.GetLogs(project, logStore, from, to, "", queryText, line, offset,
                    true);

            JSONArray rows = new JSONArray();
            for (QueriedLog log : getLogsResponse.GetLogs()) {
                JSONObject object = new JSONObject();
                for (LogContent content : log.mLogItem.mContents) {
                    object.put(content.mKey, "null".equals(content.mValue) ? "" : content.mValue);
                }
                rows.put(object);
            }
            return rows;
        } catch (LogException e) {
            // 特殊处理分页限制异常
            if (e.getMessage() != null && e.getMessage().contains("reading data with pagination only allow reading max 1000000")) {
                LOGGER.error("查询数据量超过阿里云SLS限制(1000000)，请使用时间范围分段查询方法", e);
                throw new IllegalArgumentException(
                        "查询数据量超过阿里云SLS限制(1000000)，请使用时间范围分段查询方法queryLogByTimeSegments", e);
            }

            LOGGER.error("查询日志失败, project={}, logStore={}, from={}, to={}, query={}, offset={}, line={}",
                    project, logStore, from, to, queryText, offset, line, e);
            throw e;
        }
    }

    /**
     * 为日志对象生成唯一标识
     *
     * @param logObject 日志对象
     * @return 唯一标识字符串
     */
    private static String generateUniqueKey(JSONObject logObject) {
        StringBuilder key = new StringBuilder();

        // 使用__time__作为主要标识
        if (logObject.has("__time__")) {
            key.append(logObject.get("__time__"));
        }

        // 添加其他可能的唯一标识字段
        String[] possibleKeyFields = {"__source__", "__topic__", "traceId", "requestId", "id"};
        for (String field : possibleKeyFields) {
            if (logObject.has(field)) {
                key.append("_").append(logObject.get(field));
            }
        }

        return key.toString();
    }

    /**
     * 通过SQL查询日志条数
     *
     * @param logStore 日志logStore（类型）
     * @param from     Unix时间戳格式。查询开始时间点。该时间是指写入日志数据时指定的日志时间。
     * @param to       Unix时间戳格式。查询结束时间点。该时间是指写入日志数据时指定的日志时间。
     * @param whereSql where过滤条件
     * @return int 日志条数
     * @throws LogException 日志异常
     */
    public static int queryLogCount(String logStore, int from, int to, String whereSql) throws LogException {
        checkInitialized();

        if (!StringUtil.isEmpty(whereSql) && !whereSql.trim().startsWith("where")) {
            whereSql = " where " + whereSql;
        }

        String query = "*|select COUNT(*) as count " + whereSql;
        try {
            GetLogsResponse getLogsResponse = client.GetLogs(project, logStore, from, to, "", query);
            for (QueriedLog log : getLogsResponse.GetLogs()) {
                for (LogContent mContent : log.mLogItem.mContents) {
                    if ("count".equals(mContent.mKey)) {
                        return Integer.parseInt(mContent.mValue);
                    }
                }
            }
            return 0;
        } catch (LogException e) {
            LOGGER.error("查询日志条数失败, project={}, logStore={}, from={}, to={}, whereSql={}",
                    project, logStore, from, to, whereSql, e);
            throw e;
        }
    }
}
