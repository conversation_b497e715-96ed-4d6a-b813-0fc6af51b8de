package com.wb.cache.stats;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.LongAdder;

/**
 * 缓存统计类
 * 用于记录缓存操作的各种统计指标
 */
public class CacheStats {
    private final LongAdder hits = new LongAdder();
    private final LongAdder misses = new LongAdder();
    private final LongAdder errors = new LongAdder();
    private final LongAdder localHits = new LongAdder();
    private final LongAdder loads = new LongAdder();
    private final Map<String, LongAdder> operationCounters = new ConcurrentHashMap<>();
    
    private final long startTime = System.currentTimeMillis();
    
    /**
     * 记录缓存命中
     */
    public void recordHit() {
        hits.increment();
    }
    
    /**
     * 记录本地缓存命中
     */
    public void recordLocalHit() {
        localHits.increment();
    }
    
    /**
     * 记录缓存未命中
     */
    public void recordMiss() {
        misses.increment();
    }
    
    /**
     * 记录错误
     */
    public void recordError() {
        errors.increment();
    }
    
    /**
     * 记录缓存加载
     */
    public void recordLoad() {
        loads.increment();
    }
    
    /**
     * 记录特定操作
     * 
     * @param operation 操作名称
     */
    public void recordOperation(String operation) {
        operationCounters.computeIfAbsent(operation, k -> new LongAdder()).increment();
    }
    
    /**
     * 获取缓存命中率
     * 
     * @return 命中率（0-1之间）
     */
    public double getHitRate() {
        long totalRequests = hits.sum() + misses.sum();
        if (totalRequests == 0) {
            return 0.0;
        }
        return (double) hits.sum() / totalRequests;
    }
    
    /**
     * 获取本地缓存命中率
     * 
     * @return 本地命中率（0-1之间）
     */
    public double getLocalHitRate() {
        long totalRequests = hits.sum() + misses.sum();
        if (totalRequests == 0) {
            return 0.0;
        }
        return (double) localHits.sum() / totalRequests;
    }
    
    /**
     * 获取缓存运行时间（秒）
     * 
     * @return 运行时间
     */
    public long getRunningTimeSeconds() {
        return (System.currentTimeMillis() - startTime) / 1000;
    }
    
    /**
     * 获取每秒请求数
     * 
     * @return 每秒请求数
     */
    public double getRequestsPerSecond() {
        long runningTime = getRunningTimeSeconds();
        if (runningTime == 0) {
            return 0.0;
        }
        
        long totalRequests = hits.sum() + misses.sum();
        return (double) totalRequests / runningTime;
    }
    
    /**
     * 获取统计信息
     * 
     * @return 统计数据Map
     */
    public Map<String, Object> getStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 基本计数
        stats.put("hits", hits.sum());
        stats.put("misses", misses.sum());
        stats.put("errors", errors.sum());
        stats.put("localHits", localHits.sum());
        stats.put("loads", loads.sum());
        
        // 计算的指标
        stats.put("hitRate", getHitRate());
        stats.put("localHitRate", getLocalHitRate());
        stats.put("requestsPerSecond", getRequestsPerSecond());
        stats.put("runningTimeSeconds", getRunningTimeSeconds());
        
        // 时间信息
        stats.put("startTime", startTime);
        stats.put("currentTime", System.currentTimeMillis());
        
        // 操作计数器
        Map<String, Long> operations = new HashMap<>();
        operationCounters.forEach((key, counter) -> operations.put(key, counter.sum()));
        stats.put("operations", operations);
        
        return stats;
    }
    
    /**
     * 重置所有统计数据
     */
    public void reset() {
        hits.reset();
        misses.reset();
        errors.reset();
        localHits.reset();
        loads.reset();
        operationCounters.clear();
    }
} 