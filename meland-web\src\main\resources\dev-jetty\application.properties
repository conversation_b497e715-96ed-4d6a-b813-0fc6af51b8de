# Redis主机地 址
redis.address=127.0.0.1
# Redis主机端口
redis.port=6379
# Redis验证密码
redis.password=
# 设置数据库索引，如果=2，以使用db2
redis.database=1
# 最大连接数
jredis.maxTotal=300
# 最大空闲连接数
jredis.maxIdle=10
# 每次释放连接的最大数目
jredis.numTestsPerEvictionRun=1024
# 释放连接的扫描间隔（毫秒）
jredis.timeBetweenEvictionRunsMillis=30000
# 连接最小空闲时间
jredis.minEvictableIdleTimeMillis=1800000
# 连接空闲多久后释放, 当空闲时间>该值 且 空闲连接>最大空闲连接数 时直接释放
jredis.softMinEvictableIdleTimeMillis=10000
# 最大等待毫秒数,小于零:阻塞不确定的时间,默认-1
jredis.maxWaitMillis=1500
# 在获取连接的时候检查有效性, 默认false
jredis.testOnBorrow=true
# 在空闲时检查有效性, 默认false
jredis.testWhileIdle=true
# 连接耗尽时是否阻塞, false报异常,true阻塞直到超时, 默认true
jredis.blockWhenExhausted=false

# Redisson线程池和连接池配置
# 业务线程数
redis.threads=8
# Netty线程数
redis.nettyThreads=8
# 连接池大小
redis.connectionPoolSize=32
# 空闲连接超时（毫秒）
redis.idleConnectionTimeout=30000