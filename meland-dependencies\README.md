# meland-dependencies

## 简介
这是乐的文化平台的依赖管理模块，用于统一管理整个项目的依赖版本。通过本模块，可以避免在各个子模块中重复定义依赖版本，从而减少版本冲突的风险。

## 使用方法
在子模块的pom.xml中，添加以下依赖：

```xml
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>lonlysky</groupId>
            <artifactId>meland-dependencies</artifactId>
            <version>1.0.0</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>
```

然后在项目中使用依赖时，就可以不指定版本号，系统会自动使用meland-dependencies中定义的版本：

```xml
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
</dependencies>
```

## 如何添加新依赖
如需添加新的依赖版本，请在meland-dependencies的pom.xml文件中的properties节点下添加版本属性，然后在dependencyManagement节点下添加相应的依赖定义。 