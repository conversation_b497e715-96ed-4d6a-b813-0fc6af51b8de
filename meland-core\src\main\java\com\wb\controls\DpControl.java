package com.wb.controls;

import java.sql.CallableStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map.Entry;
import java.util.Set;

import com.wb.tool.DataProvider;
import com.wb.util.DbUtil;
import com.wb.util.StringUtil;
import com.wb.util.WebUtil;

/**
 * 数据库内容查询控件，见：{@link com.wb.tool.DataProvider}
 */
public class DpControl extends Control {
    @Override
    public void create() throws Exception {
        getContent(true);
    }

    /**
     * 获取查询的内容，该内容为从数据库查询获得的结果转换为指定类型的脚本或二进制流。
     *
     * @param directOutput 是否直接输出到客户端，如果为false仅返回脚本类型的内容。
     * @return 当directOutput为false时，返回脚本类内容，否则返回null。
     */
    public String getContent(boolean directOutput) throws Exception {
        if (gb("disabled", false))
            return null;
        long startTime = System.currentTimeMillis();

        Long totalCount = null;
        ResultSet resultSet = null;
        ResultSet totalResultSet = null;

        String jndi = gs("jndi");
        String limitRecords = gs("limitRecords");
        String limitExportRecords = gs("limitExportRecords");
        String loadParams = gs("loadParams");
        String resultName = gs("resultName");
        String totalCountName = gs("totalCountName");
        String totalLoadParams = gs("totalLoadParams");
        String startParam = this.request.getParameter("start");
        String limitParam = this.request.getParameter("limit");
        String type = gs("type");

        boolean autoPage = gb("autoPage", true);
        try {
            if ((type.isEmpty()) && ("1".equals(this.request.getParameter("_istree"))))
                type = "tree";
            if ((StringUtil.isEmpty(type)) || ("array".equals(type)))
                setOrderSql();
            String sql = gs("sql");
            long beginIndex;
            if (StringUtil.isEmpty(startParam)) {
                beginIndex = 1;
                this.request.setAttribute("start", 0L);
            } else {
                beginIndex = Long.parseLong(startParam) + 1L;
            }
            long endIndex;
            if (StringUtil.isEmpty(limitParam)) {
                endIndex = Long.MAX_VALUE;
                this.request.setAttribute("limit", endIndex);
            } else {
                endIndex = beginIndex + Long.parseLong(limitParam) - 1L;
            }
            // beginIndex和endIndex可用于SQL between语句
            this.request.setAttribute("beginIndex", beginIndex);
            this.request.setAttribute("endIndex", endIndex);
            Object result = getResult(DbUtil.run(this.request, sql, jndi, loadParams, resultName.startsWith("@")),
                    StringUtil.select(resultName, "result"));
            if ((result instanceof ResultSet)) {
                resultSet = (ResultSet) result;// ResultSet在请求结束后自动关闭
            } else {
                String text = StringUtil.concat("{\"total\":1,\"metaData\":{\"fields\":[{\"name\":\"result\",\"type\":\"string\"}]},\"columns\":[{\"xtype\":\"rownumberer\",\"width\":40},{\"dataIndex\":\"result\",flex:1,\"text\":\"result\"}],\"rows\":[{\"result\":",
                        result == null ? "null" : StringUtil.quote(result.toString()), "}],\"elapsed\":",
                        Long.toString(System.currentTimeMillis() - startTime), "}");
                if (directOutput) {
                    WebUtil.send(this.response, text);
                    return null;
                }
                return text;
            }
            if (!autoPage) {
                String totalSql = gs("totalSql");
                if (StringUtil.isEmpty(totalSql)) {
                    if (!totalCountName.isEmpty()) {
                        String totalCountVal = WebUtil.fetch(this.request, totalCountName);
                        if (totalCountVal != null && StringUtil.isInteger(totalCountVal))
                            totalCount = Long.parseLong(totalCountVal);
                    }
                } else {
                    Object totalResult = getResult(
                            DbUtil.run(this.request, totalSql, jndi, totalLoadParams, totalCountName.startsWith("@")),
                            StringUtil.select(totalCountName, "totalCount"));
                    if (totalResult == null)
                        throw new NullPointerException("No value in the totalSql.");
                    if ((totalResult instanceof ResultSet)) {
                        totalResultSet = (ResultSet) totalResult;
                        if (totalResultSet.next()) {
                            // 比使用totalResultSet.getLong更安全和通用
                            totalCount = Long.parseLong(totalResultSet.getString(1));
                        } else
                            throw new NullPointerException("Empty total ResultSet.");
                    } else {
                        totalCount = Long.parseLong(totalResult.toString());
                    }
                }
            }
            DataProvider dp = new DataProvider();
            dp.startTime = startTime;
            dp.request = this.request;
            dp.response = this.response;
            dp.resultSet = resultSet;
            dp.fields = gs("fields");
            dp.fieldsTag = gs("fieldsTag");
            dp.keyDefines = gs("keyDefines");
            dp.totalCount = totalCount;
            dp.createColumns = gb("createColumns", true);
            if (autoPage) {
                dp.beginIndex = beginIndex;
                dp.endIndex = endIndex;
            }
            if (!limitRecords.isEmpty())
                dp.limitRecords = Integer.parseInt(limitRecords);
            if (!limitExportRecords.isEmpty())
                dp.limitExportRecords = Integer.parseInt(limitExportRecords);
            dp.tag = gs("tag");
            dp.type = type;
            String dictTableNames = gs("dictTableNames");
            dp.createKeyValues = gb("createKeyValues", false);
            if (dictTableNames.isEmpty())
                dp.dictTableNames = null;
            else
                dp.dictTableNames = StringUtil.split(dictTableNames, ',', true);
            dp.dictFieldsMap = gs("dictFieldsMap");
            if (directOutput) {
                dp.output();
                return null;
            }
            return dp.getScript();
        } finally {
            DbUtil.close(resultSet);
            DbUtil.close(totalResultSet);
        }
    }

    /**
     * 获取Query运行返回的ResultSet或影响记录数；如果存在输出参数，则尝试获取名称为
     * result的输出参数否则返回运行Query返回的值（结果集或影响记录数）。
     *
     * @param result 运行Query返回的对象。
     * @return 获取的结果集或影响记录数。
     */
    private Object getResult(Object result, String resultName) throws Exception {
        if (result instanceof HashMap<?, ?>) {
            HashMap<?, ?> map = (HashMap<?, ?>) result;
            // 设置输出参数值
            Set<?> es = map.entrySet();
            Entry<?, ?> entry;
            String name, itemId = StringUtil.select(gs("itemId"));
            for (Object e : es) {
                entry = (Entry<?, ?>) e;
                name = (String) entry.getKey();
                // 在hashmap中返回值名称为return
                if ("return".equals(name)) {
                    if (!itemId.isEmpty())
                        name = itemId;
                } else {
                    if (!itemId.isEmpty())
                        name = StringUtil.concat(itemId, ".", name);
                }
                request.setAttribute(name, entry.getValue());
            }
            String rsIndex;
            if ((resultName.startsWith("@")) && (StringUtil.isInteger(rsIndex = resultName.substring(1)))) {
                return getMoreResult(map, Integer.parseInt(rsIndex));
            }
            Object val = map.get(resultName);
            if (val == null) {
                return map.get("return");
            }
            return val;
        }

        return result;
    }

    private Object getMoreResult(HashMap<?, ?> map, int index) throws Exception {
        CallableStatement st = (CallableStatement) map.get("sys.statement");

        for (int i = 1; i < index; i++)
            st.getMoreResults();
        Object result = st.getResultSet();
        if (result == null)
            result = st.getUpdateCount();
        return result;
    }

    /**
     * 根据request参数生成排序SQL
     */
    private void setOrderSql() {
        String sort = this.request.getParameter("sort");
        if ((StringUtil.isEmpty(sort)) || (this.request.getAttribute("sql.orderBy") != null))
            return;
        String orderExp = DbUtil.getOrderSql(sort, gs("orderFields"));
        if (orderExp != null) {
            this.request.setAttribute("sql.orderBy", " order by " + orderExp);

            this.request.setAttribute("sql.orderFields", "," + orderExp);
        }
    }
}