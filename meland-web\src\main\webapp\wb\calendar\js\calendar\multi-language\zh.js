/**
 * FeyaSoft Online Calendar
 * Copyright(c) 2006-2009, FeyaSoft Inc. All right reserved.
 * <EMAIL>
 * http://www.feyasoft.com/myCalendar
 *
 * You need buy one of the Feyasoft's License if you want to use MyCalendar in
 * your commercial product.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY
 * KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * WARRANTIES OF MERCHANTABILITY,FITNESS FOR A PARTICULAR PURPOSE
 * AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
 * OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
 * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
Ext.ns("Ext.ux.calendar");

Ext.ux.calendar.Language = {
  'CalendarWin': {
    'title': '日历',
    'loadMask.msg': '请等待...'
  },

  'MainPanel': {
    'loadMask.msg': '请等待...'
  },

  'SharingPopup': {
    'title': '共享日历'
  },

  'CalendarContainer': {
    'import': 'Import',
    'importICSFile': '选择包含你的日历的文件，我们可以导入iCal格式的事件。',
    'chooseCalendarImport2': '选择要导入的日历',
    'selectICSFile': '选择一个.ics文件',
    'selectFile': '选择文件',
    'holidayList': '假日名单',
    'subscribeHoliday': '订阅假日',
    'unsubscribeHoliday': '取消假期',
    'subscribe': '订阅',
    'unsubscribe': '取消订阅',
    'aboutEventType': '关于事件类型',
    'hideEventType': '隐藏事件类型',
    'showEventType': '显示事件类型',
    'todayBtn.text': '今天',
    'dayBtn.text': '日模式',
    'weekBtn.text': '周模式',
    'monthBtn.text': '月模式',
    'weekMenu.showAll.text': '显示全部',
    'weekMenu.onlyWeek.text': '仅显示工作日',
    'monthMenu.showAll.text': '显示全部',
    'monthMenu.onlyWeek.text': '仅显示工作日',
    'moreMenu.setting.text': '设置',
    'moreMenu.about.text': '关于我的日历',
    'moreBtn.text': '其他',
    'searchCriteria.text': '搜索',
    'moreMenu.showAlert.text': '启动提醒功能',
    'moreMenu.language.text': '语言设置',
    'importCalendar': 'Import Calendar',
    'wantDeleteAllCalendar': '你真的要删除此日历的所有事件?',
    'wantDeleteCalendar': '你真的要删除此日历?'
  },

  'WestPanel': {
    'myShowAllBtn.text': '显示全部',
    'otherCalendarPanel.title': '共享的日历',
    'orgCalendar': '机构的日历',
    'groupCalendar': '群体的日历',
    'myAddBtn.text': '新的日历',
    'myCalendarPanel.title': '我的日历'
  },

  'EventHandler': {
    'showOnlyItem.text': '只显示此日历',
    'viewItem.hide.text': '隐藏日历',
    'viewItem.show.text': '显示日历',
    'editItem.text': '编辑日历',
    'deleteItem.text': '删除日历',
    'clearItem.text': '清空日历',
    'wholeDay': '全天',
    'untitled': '无主题',
    'unlockItem.text': '解锁',
    'lockItem.text': '锁定',
    'editEvent.title': '编辑事件',
    'deleteEvent.title': '删除事件',
    'more': '更多',
    'deleteRepeatPopup.title': '确认',
    'deleteRepeatPopup.msg': '点击“是”删除所有重复这个事件，或单击“否”只删除当前事件.',
    'updateRepeatPopup.title': '确认',
    'updateRepeatPopup.msg': '点击“是”更新所有重复这个事件，或单击“否”只更新当前事件.',
    'shareItem.text': '共享日历',
    'more_x': '还有{0}项'
  },

  'Editor': {
    'confirm': '确认',
    'sendNotifyConfirm': '你是否确定要发送更新通知给客人？',
    'enter4chars': '输入Email地址或至少2个字符进行查询',
    'addGuest': '添加客人',
    'enterGroupInfo': '选择群组（将通知每个成员）',
    'notifyToMember': '通知每个成员',
    'addGroup': '添加群组',
    'selectEventType': '选择事件类型',
    'eventType': '事件类型',
    'new.title': '新建事件',
    'edit.title': '编辑事件',
    'startDayField.label': '时间',
    'endDayField.label': '到',
    'wholeField.label': '全天',
    'subjectField.label': '主题',
    'contentField.label': '内容',
    'calendarField.label': '日历',
    'alertCB.label': '进行提示',
    'lockCB.label': '锁定',
    'deleteBtn.text': '删除',
    'saveBtn.text': '保存',
    'cancelBtn.text': '取消',
    'new.title': '新事件',
    'repeatTypeField.label': '重复类型',
    'repeatIntervalField.label': '重复间隔',
    'intervalUnitLabel.day.text': ' 日 ',
    'intervalUnitLabel.week.text': ' 周 ',
    'intervalUnitLabel.month.text': ' 月 ',
    'intervalUnitLabel.year.text': ' 年 ',
    'detailSetting': '更详细编辑>>',
    'returnBtn.text': '返回',
    'startAndEnd': '开始和结束',
    'repeatStartField.label': '开始',
    'repeatEndTypeField.label': '结束',
    'repeatNoEndRG.label': '无结束日期',
    'repeatEndTimeRG.label': '结束之后',
    'repeatEndDateRG.label': '以结束',
    'repeatEndTimeUnit': '出现',
    'weekCheckGroup.label': '重复天',
    'monthRadioGroup.label': '重复由',
    'repeatByDate': '日期',
    'repeatByDay': '天',
    'repeatDayInfo': '每天',
    'alertLabel': '提示设置',
    'alertEarly.label': '提前',
    'newAlertBtn.text': '新',
    'deleteAlertBtn.label': '清除',
    'emailAlertEarlyInvalid': '电子邮件提示的时间应大于30分钟以上',
    'popupAlertEarlyInvalid': '弹出提示的时间应不超过24小时',
    'repeatIntervalInvalid': '这个值应该是一个正整数',
    'repeatBeginDayInvalid': '重复开始日期不应该晚于重复结束日期',
    'repeatEndDayInvalid': '重复结束日期不得早于重复起始日期',
    'repeatTimeInvalid': '这个值应该是一个正整数',
    'noEnd': '无结束',
    'endByTime': '到达次数后结束',
    'endByDate': '到达日期后结束',
    'repeatEndTimeField.label': '重复次数',
    'repeatEndDateField.label': '结束日期',
    'user': '用户',
    'group': '群组'
  },

  'CalendarEditor': {
    'generalInfo': '基本信息',
    'shareCalendarOrg': '与机构共享日历(只允许机构管理员)',
    'shareColumns.org': '机构',
    'new.title': '新建日历',
    'edit.title': '编辑日历',
    'nameField.label': '名称',
    'descriptionField.label': '描述',
    'clearBtn.text': '清除',
    'saveBtn.text': '保存',
    'cancelBtn.text': '取消',
    'returnBtn.text': '返回',
    'shareCalendar': '与朋友共享日历',
    'shareColumns.user': '用户',
    'shareColumns.permit': '许可',
    'shareColumns.add': '新增用户共享',
    'shareColumns.remove': '删除',
    'enter4chars': '请输入至少4个字符搜索.',
    'shareCalendarGroup': '分享群组日历（只允许群组管理员）',
    'group': '群组',
    'organization': '组织机构',
    'addGroup2Share': '添加群组的共享',
    'selectoneitem': '请选择项目之一',
    'userField.emptyText': '请输入用户名或电子邮件',
    'add_org_share': '添加组织机构的共享',
    'share_with_org': '分享组织机构日历（只允许群组管理员）',
    'select_org': '请选择一个组织机构'
  },

  'ExpirePopup': {
    'title': '提醒事件',
    'hideCB.label': '不再显示',
    'tpl.calendar': '日历',
    'tpl.subject': '主题',
    'tpl.content': '内容',
    'tpl.leftTime': '剩余时间',
    'hour': '小时',
    'minute': '分钟',
    'untitled': '无主题',
    'noContent': '无内容'
  },

  'SettingPopup': {
    'title': '日历设置',
    'hourFormatField.label': '小时格式',
    'dayFormatField.label': '日期格式（日模式）',
    'weekFormatField.label': '日期格式（周模式）',
    'monthFormatField.label': '日期格式（月模式）',
    'applyBtn.text': '应用',
    'resetBtn.text': '重置',
    'closeBtn.text': '关闭',
    'fromtoFormatField.label': '日期格式（当前范围）',
    'scrollStartRowField.label': '开始时间（日周模式滚动条）',
    'languageField.label': '语言',
    'generalForm.title': '普通',
    'dwViewForm.title': '天模型|周模型',
    'monthViewForm.title': '月模型',
    'createByDblClickField.label': '双击创建活动',
    'singleDayField.label': '跨日活动',
    'weekStartDayField.label': '周开始日',
    'activeStartTimeField.label': '生效开始时间',
    'activeEndTimeField.label': '生效结束时间',
    'hideInactiveTimeField.label': '隐藏非活动时间',
    'readOnlyField.label': '只读',
    'intervalField.label': '间隔时隙',
    'startEndInvalid': '生效开始时间应早于活动结束时间!',
    'formatInvalid': '范例: 09:00',
    'initialViewField.label': '初始预览'
  },

  'ResultView': {
    'cm.date': '日期',
    'cm.calendar': '日历',
    'cm.time': '时间',
    'cm.subject': '主题',
    'cm.content': '内容',
    'cm.expire': '剩余时间',
    'groupBtn.group.text': '分组',
    'groupBtn.unGroup.text': '取消分组',
    'returnBtn.text': '返回',
    'hour': '小时',
    'noSubject': '(无主题)',
    'noContent': '(无内容)',
    'loadMask.msg': '请等待...'
  },

  'DayView': {
    'loadMask.msg': '请等待...',
    'addItem.text': '新建事件',
    'events': '事件'
  },

  'MonthView': {
    'loadMask.msg': '请等待...',
    'overview': '情况',
    'showingEvents': '显示事件',
    'totalEvents': '全部事件',
    'dayPre': '周',
    'addItem.text': '新建事件',
    'clearItem.text': '清空事件',
    'cutItem.text': '剪切',
    'copyItem.text': '拷贝',
    'pasteItem.text': '粘贴',
    'events': '事件',
    'notice': '注意',
    'cross_day_not_copy': '超过一天的事件和重复事件未被拷贝'
  },

  'Mask': {
    'noEventType': '无事件类型',
    '12Hours': '12 小时',
    '24Hours': '24 小时',
    'ar': '阿拉伯语',
    'de': '德语',
    'en_US': 'American English',
    'es': '西班牙',
    'fr': 'Français',
    'it': 'Italiano',
    'ja': '日语',
    'lt': 'Lietuvių',
    'nl': 'Nederlandse',
    'pl': 'Polski',
    'pt': '葡萄牙语',
    'ru': '俄文',
    'zh': '简体中文',
    'enable': '启用',
    'disable': '关闭',
    'minute': '分钟',
    'monday': '星期一',
    'sunday': '星期天',
    'permitData': [
      //   [4, 'Read, Write and Share'],
      [2, '读和写'],
      [1, '只读']
    ],
    'alertType': [
      ['email', '电子邮件'],
      ['popup', '弹出消息']
    ],
    'popupAlertUnit': [
      ['minute', '分钟'],
      ['hour', '小时']
    ],
    'alertUnit': [
      ['minute', '分钟'],
      ['hour', '小时'],
      ['day', '天'],
      ['week', '星期']
    ],
    'initialView': [
      ['0', '日视图'],
      ['1', '周视图'],
      ['2', '月视图']
    ]
  },

  repeatType: [
    ['no', '无重复'],
    ['day', '每日'],
    ['week', '每周'],
    ['month', '每月'],
    ['year', '每年']
  ],

  getWeekDayInMonth: function(date) {
    var d = Ext.Date.format(date, 'd');
    var w = Math.floor(d / 7) + 1;
    var wd = Ext.Date.format(date, 'l');
    var str = 'the ' + w;
    if (1 == w) {
      str += 'st';
    } else if (2 == w) {
      str += 'nd';
    } else if (3 == w) {
      str += 'rd';
    } else {
      str += 'th';
    }
    return str + ' ' + wd;
  },

  getIntervalText: function(rtype, intervalSlot) {
    var str = '';
    if ('day' == rtype) {
      if (1 == intervalSlot) {
        str = '每天在';
      } else {
        str = '每' + intervalSlot + '天在';
      }
    } else if ('week' == rtype) {
      if (1 == intervalSlot) {
        str = '每星期在';
      } else {
        str = '每' + intervalSlot + '星期在';
      }
    } else if ('month' == rtype) {
      if (1 == intervalSlot) {
        str = '每月在';
      } else {
        str = '每' + intervalSlot + '月在';
      }
    } else if ('year' == rtype) {
      if (1 == intervalSlot) {
        str = '每年在';
      } else {
        str = '每' + intervalSlot + '年在';
      }
    }
    return str;
  }
};

Ext.apply(Ext.ux.calendar.Mask, Ext.ux.calendar.Language);