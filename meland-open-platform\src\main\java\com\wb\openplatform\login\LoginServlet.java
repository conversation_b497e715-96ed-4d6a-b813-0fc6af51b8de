package com.wb.openplatform.login;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.json.JSONObject;

/**
 * 确认授权
 * <AUTHOR>
 *
 */
public class LoginServlet {

	public static JSONObject loginSQ(HttpServletRequest request, HttpServletResponse response) {
		String uuid = request.getParameter("uuid");
		String code = request.getParameter("code");
		String redirect = request.getParameter("state");
		//查找相对应的uuid
		JSONObject userV = LoginV.getLoginUserMap().get(uuid);
		JSONObject codeJson = new JSONObject();
		if(userV != null && (code != null || code != "")){
			userV = new JSONObject();
			userV.put("code", code);
			userV.put("redirect", redirect);
			codeJson.put("errcode", 200);
			codeJson.put("errmsg", "授权成功");
			LoginV.getLoginUserMap().put(uuid, userV);
		}else if(userV == null && code == null){
			userV = new JSONObject();
			userV.put("sm", "ok");
			userV.put("code", code);
			userV.put("redirect", redirect);
			codeJson.put("errcode", 2001);
			codeJson.put("errmsg", "扫码成功");
			LoginV.getLoginUserMap().put(uuid, userV);
		}else {
			codeJson.put("errcode", 404);
			codeJson.put("errmsg", "授权失败");
		}
		
		return codeJson;
	}
}
