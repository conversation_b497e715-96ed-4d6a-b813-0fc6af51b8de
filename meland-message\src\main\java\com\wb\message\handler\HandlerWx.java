package com.wb.message.handler;

import java.util.List;

import org.json.JSONArray;
import org.json.JSONObject;

import com.wb.common.Var;
import com.wb.message.Handler;
import com.wb.message.util.ParamUtil;
import com.wb.openplatform.accounts.util.AccountUtil;
import com.wb.openplatform.enterprise.util.WeiXinUtil;
import com.wb.util.DbUtil;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;

public class HandlerWx extends Handler {

	public HandlerWx(String title, String content, String users, String data, JSONObject arg) {
		super(title, content, users, data, arg);
	}

	// 接收者的用户列长度
	private static final Integer PAGE_SIZE = 1000;
	private static final String url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=ACCESS_TOKEN";

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public void handlerMessage() {
		/**
		 * 判断是否开启服务消息
		 */
		if (Var.getBool("sys.config.ServiceMessage")) {
			// 存放所有用户
			String user = StringUtil.joinQuote(this.users);
			if (user != "") {
				// 查询用户的企业微信ID
				String sql = "select msg_id from  wb_user_weixin where user_id in (" + user + ") and msg_id <> '' ";

				JSONArray array = DbUtil.query(sql);

				// 总记录数
				Integer totalCount = array.length();
				if (totalCount <= 0) {
					LogUtil.warn(StringUtil.format("消息[{0}] 微信----没有用户", title));
					System.err.println("微信----没有用户");
					return;
				}
				// 分多少次处理
				Integer requestCount = totalCount / PAGE_SIZE;
				for (int i = 0; i <= requestCount; i++) {
					Integer fromIndex = i * PAGE_SIZE;
					// 如果总数少于PAGE_SIZE,为了防止数组越界,toIndex直接使用totalCount即可
					int toIndex = Math.min(totalCount, (i + 1) * PAGE_SIZE);
					List subList = array.toList().subList(fromIndex, toIndex);
					String userList = String.join("|", subList);
					sendMessage(userList);
					// 总数不到一页或者刚好等于一页的时候,只需要处理一次就可以退出for循环了
					if (toIndex == totalCount) {
						break;
					}
				}
			} else {
				LogUtil.warn(StringUtil.format("消息[{0}] 微信----没有用户", title));
				System.err.println("微信----没有用户");
			}
		}else {
			LogUtil.warn(StringUtil.format("消息[{0}] 微信----没有启用服务消息", title));
			System.err.println("微信----没有启用服务消息");
		}
	}

	/**
	 * 发送消息
	 * 
	 * @param users
	 */
	public void sendMessage(String users) {
		try {
			// 重写参数
			JSONObject Arg_ = new JSONObject(this.arg.toString());
			JSONObject param = new JSONObject(this.data);
			// 默认参数
			JSONObject temp = ParamUtil.getWxParams(param, Arg_, title);
			
			if (temp == null) {
				LogUtil.warn(StringUtil.format("消息[{0}] [content_item]为空，无法发送服务消息", title));
				return;
			}
			// 默认parms
			JSONObject parms = Arg_.getJSONObject("parms");
			// 判断重写参数是否存在 不存在使用默认参数 存在使用重写参数
			if (StringUtil.isEmpty(param.optString("path"))) {
				param.put("path",StringUtil.isEmpty(param.optString("path")) ? Arg_.get("path") : "");
			}

			// 获取accessToken
			String accessToken = AccountUtil.getAccessToken(Var.getString("sys.config.message.Grant_Type"), Var.getString("sys.config.message.AppId"), Var.getString("sys.config.message.AppSecret"), "MSG").getToken();

			// 创建JSON参数
			JSONObject obj = new JSONObject();
			obj.put("touser", users);
			obj.put("template_id", parms.getString("template"));
			obj.put("url", Arg_.get("url"));

			JSONObject min = new JSONObject();
			min.put("appid", Var.getString("sys.config.smallProgram.AppID"));
			min.put("pagepath", param.get("path"));
			obj.put("miniprogram", min);
			obj.put("data", temp);

			// 发生地址
			String msgUrl = url.replace("ACCESS_TOKEN", accessToken);
			JSONObject jsonObject = WeiXinUtil.httpRequest(msgUrl, "POST", obj.toString());
			if (jsonObject.has("errcode")) {
				if (0 != jsonObject.getInt("errcode")) {
					LogUtil.warn(
							StringUtil.format("消息[{0}] 微信----发送失败,错误代码【[{1}]】", title, jsonObject.getInt("errcode")));
					System.err.println("微信----发送失败,错误代码【" + jsonObject.getInt("errcode") + "】");
				} else {
					LogUtil.warn(StringUtil.format("消息[{0}] 微信----发送成功", title));
					System.out.println("微信----发送成功");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
