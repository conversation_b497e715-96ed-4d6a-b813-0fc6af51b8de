package com.wb.message.util;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.Date;

import org.json.JSONArray;
import org.json.JSONObject;

import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.wb.common.Base;
import com.wb.common.Var;
import com.wb.rocketmq.MessageSender;
import com.wb.util.DbUtil;
import com.wb.util.JsonUtil;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;

/**
 * 短信发送工具类
 * 
 * <AUTHOR>
 *
 */
public class SmsUtil {

	public static final String LIST_KEY = "SMS:LIST";
	public static final String CODE_PREFIX = "SMS:CODE:";

	/**
	 * 随机生成6位随机验证码 方法说明
	 *
	 * @return String
	 */
	private static String createRandomCode() {
		int smsCodeNo = Var.getInt("sys.config.smsCloud.smsCodeNo");
		smsCodeNo = smsCodeNo < 4 || smsCodeNo > 6 ? 4 : smsCodeNo;
		// 验证码
		// 验证码
		String code = "";
		for (int i = 1; i <= smsCodeNo; i++) {
			int cm = (int) (Math.random() * 9);
			if (code.indexOf("" + cm) != -1) {
				--i;
				continue;
			}
			code += cm;
		}
		return code;
	}

	/**
	 * 阿里云SDK发送短信
	 * 
	 * @param smsObj               发送参数
	 * @param smsObj.phone         电话号码
	 * @param smsObj.signature     签名
	 * @param smsObj.templateCode  模版代码
	 * @param smsObj.templateParam 参数(JSONObject)
	 * @param smsObj.isCode        是否验证码
	 * @param smsObj.creationTime  发送时间
	 * @param smsObj.isBatch       是否批量发送
	 * 
	 * @return 是否发送成功
	 */
	public static boolean smsCloudSender(JSONObject smsObj) {
		if (smsObj == null) {
			return false;
		}
		boolean result = true;
		try {
			String phone;
			DefaultProfile profile = DefaultProfile.getProfile("default",
					Var.getString("sys.config.smsCloud.accessKeyId"),
					Var.getString("sys.config.smsCloud.accessSecret"));
			IAcsClient client = new DefaultAcsClient(profile);

			CommonRequest request = new CommonRequest();
			request.setMethod(MethodType.GET);
			request.setDomain(Var.getString("sys.config.smsCloud.sendUrl"));
			request.setVersion(Var.getString("sys.config.smsCloud.Version"));
			request.putQueryParameter("RegionId", "default");
			request.putQueryParameter("TemplateCode", smsObj.getString("templateCode"));

			boolean isBatch = smsObj.getBoolean("isBatch");
			if (isBatch) {
				phone = smsObj.getJSONArray("phoneNumberJson").toString();
				request.setAction("SendBatchSms");
				request.putQueryParameter("PhoneNumberJson", phone);
				request.putQueryParameter("SignNameJson", smsObj.getJSONArray("signature").toString());
				request.putQueryParameter("TemplateParamJson", smsObj.getJSONArray("templateParam").toString());
			} else {
				phone = smsObj.getString("PhoneNumbers");
				request.setAction("SendSms");
				request.putQueryParameter("PhoneNumbers", phone);
				request.putQueryParameter("SignName", smsObj.getString("signature"));
				request.putQueryParameter("TemplateParam", smsObj.getJSONObject("templateParam").toString());
			}
			CommonResponse response = client.getCommonResponse(request);
			JSONObject data = JsonUtil.getObject(response.getData());

			if (data != null && "OK".equals(data.getString("Code"))) {
				if (smsObj.getBoolean("isCode")) {
					persistenceMessage(phone, smsObj, "发送成功");
					Base.map.put(CODE_PREFIX + phone, smsObj, Var.getInt("sys.config.sms.aging"));
				}
			} else {
				result = false;
				updateSms(phone, smsObj, data.getString("Code"));
				LogUtil.warn(StringUtil.format("发送短信[{0}]失败：{1}", smsObj, data.getString("Code")));
			}

			if (isBatch && JsonUtil.opt(smsObj, "sendId") != null) {
				String sendId = JsonUtil.opt(smsObj, "sendId").toString();
				String status = data == null ? "发送失败"
						: (data.getString("Code").equals("OK") ? "发送成功" : data.getString("Code"));
				batchAddMsm(smsObj.getJSONArray("phoneNumberJson"), sendId, status);
			}

		} catch (Exception e) {
			result = false;
			LogUtil.error(StringUtil.format("发送短信[{0}]异常：{1}", smsObj, e));
		}
		return result;
	}

	/**
	 * 发送短信给指定号码
	 * 
	 * @param phone        手机号
	 * @param signature    签名
	 * @param templateCode 模版代码
	 * @param isCode       是否验证码
	 * @param params       参数对象(JSONObject)
	 */
	public static void sendSms(final String phone, String signature, String templateCode, Boolean isCode,
			JSONObject params) {
		try {
			if ("".equals(signature) || "".equals(templateCode)) {
				LogUtil.warn("短信签名或者短信模版不能为空");
				return;
			}
			JSONObject smsObj = new JSONObject();
			smsObj.put("PhoneNumbers", phone);
			smsObj.put("creationTime", new Date().getTime());
			smsObj.put("templateParam", params);
			smsObj.put("signature", signature);
			smsObj.put("templateCode", templateCode);
			smsObj.put("isCode", isCode);
			smsObj.put("isBatch", false);
			smsObj.put("model", "consumer");
			smsObj.put("methods", "sms");
			// 通过变量控制短信发送的方式，如果开启任务则不采用消息中间件模式发送
			if (Var.getBool("sys.config.sms.useTask"))
				Base.map.listLeftPush(LIST_KEY, smsObj);
			else
				MessageSender.sendAsync("sms", smsObj);
		} catch (Exception e) {
			LogUtil.error("发送短信异常：" + e);
		}
	}

	/**
	 * 发送短信验证码
	 * 
	 * @param phone        手机号码
	 * @param signature    签名
	 * @param templateCode 模版代码
	 */
	public static void sendSmsCode(final String phone, String signature, String templateCode) {
		JSONObject templateParam = new JSONObject();
		templateParam.put("code", createRandomCode());
		sendSms(phone, signature, templateCode, true, templateParam);
	}

	/**
	 * 发送普通短信（非验证码）
	 * 
	 * @param phone        手机号码
	 * @param signature    签名
	 * @param templateCode 模版代码
	 * @param params       模版参数
	 */
	public static void sendSms(final String phone, String signature, String templateCode, JSONObject params) {
		sendSms(phone, signature, templateCode, false, params);
	}

	/**
	 * 批量发送普通短信（非验证码）
	 *
	 * @param phone        手机号码
	 * @param signature    签名
	 * @param templateCode 模版代码
	 * @param params       模版参数
	 */
	public static void sendBatchSms(JSONArray phone, JSONArray signature, JSONArray params, String templateCode) {
		try {
			JSONObject smsObj = new JSONObject();
			smsObj.put("phoneNumberJson", phone);
			smsObj.put("creationTime", new Date().getTime());
			smsObj.put("templateParam", params);
			smsObj.put("signature", signature);
			smsObj.put("templateCode", templateCode);
			smsObj.put("isCode", false);
			smsObj.put("isBatch", true);
			smsObj.put("model", "consumer");
			smsObj.put("methods", "sms");
			// 通过变量控制短信发送的方式，如果开启任务则不采用消息中间件模式发送
			if (Var.getBool("sys.config.sms.useTask"))
				Base.map.listLeftPush(LIST_KEY, smsObj);
			else
				MessageSender.sendAsync("sms", smsObj);
		} catch (Exception e) {
			LogUtil.error("发送短信异常：" + e);
		}
	}

	/**
	 * 批量发送普通短信（非验证码）
	 *
	 * @param phone        手机号码
	 * @param signature    签名
	 * @param templateCode 模版代码
	 * @param params       模版参数
	 * @param sendId       发送短信ID
	 */
	public static void sendBatchSms(JSONArray phone, JSONArray signature, JSONArray params, String templateCode,
			String sendId) {
		try {
			JSONObject smsObj = new JSONObject();
			smsObj.put("phoneNumberJson", phone);
			smsObj.put("creationTime", new Date().getTime());
			smsObj.put("templateParam", params);
			smsObj.put("signature", signature);
			smsObj.put("templateCode", templateCode);
			smsObj.put("sendId", sendId);
			smsObj.put("isCode", false);
			smsObj.put("isBatch", true);
			smsObj.put("model", "consumer");
			smsObj.put("methods", "sms");
			// 通过变量控制短信发送的方式，如果开启任务则不采用消息中间件模式发送
			if (Var.getBool("sys.config.sms.useTask"))
				Base.map.listLeftPush(LIST_KEY, smsObj);
			else
				MessageSender.sendAsync("sms", smsObj);
		} catch (Exception e) {
			LogUtil.error("发送短信异常：" + e);
		}
	}

	/**
	 * 消息持久化数据库
	 *
	 * @param phone
	 * @param smsObj
	 * @param status
	 */
	public static void persistenceMessage(String phone, JSONObject smsObj, String status) {
		Connection conn = null;
		PreparedStatement st = null;

		try {
			// 储存消息
			conn = DbUtil.getConnection();
			conn.setAutoCommit(false);
			st = conn.prepareStatement(
					"insert into wb_sms(id, phone, smsCode, creationTime, aging, ip, status, add_date, add_user) values(?,?,?,?,?,?,?,NOW(),?)");

			JSONObject templateParamObj = smsObj.getJSONObject("templateParam");
			Object codeObj = JsonUtil.opt(templateParamObj, "code");
			StringBuffer buffer = new StringBuffer();
			if (codeObj != null) {
				buffer.append(codeObj.toString());
			}
			st.setString(1, SysUtil.getId());
			st.setString(2, phone);
			st.setString(3, buffer.toString());
			st.setString(4, StringUtil.toString(smsObj.getLong("creationTime")));
			st.setString(5, Var.getString("sys.config.sms.aging"));
			st.setString(6, "0.0.0.0");
			st.setString(7, status);
			st.setString(8, phone);
			DbUtil.addBatch(st);

			DbUtil.executeBatch(st);
			conn.commit();
		} catch (Exception e) {
			LogUtil.error(StringUtil.format("短信存储失败[{0}]异常：{1}", phone, e));
		} finally {
			DbUtil.close(st);
			DbUtil.close(conn);
		}
	}

	/**
	 * 短信发送失败
	 *
	 * @param phone
	 * @param smsObj
	 * @param status
	 */
	public static void updateSms(String phone, JSONObject smsObj, String status) {
		Connection conn = null;
		PreparedStatement st = null;

		try {
			// 储存消息
			conn = DbUtil.getConnection();
			conn.setAutoCommit(false);

			JSONObject templateParamObj = smsObj.getJSONObject("templateParam");
			Object codeObj = JsonUtil.opt(templateParamObj, "code");
			StringBuffer buffer = new StringBuffer();
			if (codeObj != null) {
				buffer.append(codeObj.toString());
			}

			st = conn.prepareStatement("update wb_sms set status = ? where smsCode = ? and phone = ?");
			st.setString(1, status);
			st.setString(2, buffer.toString());
			st.setString(3, phone);
			DbUtil.addBatch(st);

			DbUtil.executeBatch(st);
			conn.commit();
		} catch (Exception e) {
			LogUtil.error(StringUtil.format("短信发送失败[{0}]异常：{1}", phone, e));
		} finally {
			DbUtil.close(st);
			DbUtil.close(conn);
		}
	}

	/**
	 * 消息持久化数据库
	 *
	 * @param phoneArr
	 * @param sendId
	 * @param status
	 */
	private static void batchAddMsm(JSONArray phoneArr, String sendId, String status) {
		Connection conn = null;
		PreparedStatement st = null;
		try {

			// 储存消息
			conn = DbUtil.getConnection();
			conn.setAutoCommit(false);
			for (Object phoneObj : phoneArr) {
				if (phoneObj != null) {
					String phone = phoneObj.toString();

					st = conn.prepareStatement(
							"insert into promote_sms_send_detail (id, ss_id, tel, status, add_date, add_user) values(?,?,?,?,NOW(),?)");
					st.setString(1, SysUtil.getId());
					st.setString(2, sendId);
					st.setString(3, phone);
					st.setString(4, status);
					st.setString(5, phone);
					DbUtil.addBatch(st);
				}
			}

			DbUtil.executeBatch(st);
			conn.commit();
		} catch (Exception e) {
			LogUtil.error(StringUtil.format("短信存储失败[{0}]异常：{1}", phoneArr.toString(), e));
		} finally {
			DbUtil.close(st);
			DbUtil.close(conn);
		}
	}

}
