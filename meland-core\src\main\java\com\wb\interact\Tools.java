package com.wb.interact;

import java.io.File;
import java.sql.*;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.wb.common.*;
import com.wb.util.*;
import org.json.JSONArray;

public class Tools {
    /**
     * 获取数据字典数据库表树数据。
     */
    public static void getDictTree(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String type = request.getParameter("type");
        String jndi = request.getParameter("jndi");
        String schema = request.getParameter("schema");
        String result;
        HashSet<String> tables, copyTables;

        if ("db".equals(type)) {
            tables = getDictTables(request, jndi);
            copyTables = new HashSet<String>();//getSchemaList会删除tables中的值，因此建立一个副本
            for (String t : tables) {
                copyTables.add(t);
            }
            result = DBE.getSchemaList(jndi, tables);
            //如果没有Schema直接返回表列表
            if (result == null) result = DBE.getTableList(jndi, null, copyTables);
        } else {
            if ("schema".equals(type)) result = DBE.getTableList(jndi, schema, getDictTables(request, jndi));
            else result = DBE.getDbList();
        }
        WebUtil.send(response, result);
    }

    /**
     * 获取所有已经定义的字典表表名集。
     *
     * @param request 请求对象。
     * @param jndi    数据库JNDI。
     */
    private static HashSet<String> getDictTables(HttpServletRequest request, String jndi) throws Exception {
        boolean otherDb = !"default".equals(jndi);
        int jndiLen = jndi.length() + 1;
        HashSet<String> tables = new HashSet<String>();
        ResultSet rs = (ResultSet) DbUtil.run(request, "select distinct TABLE_NAME from WB_DICT");

        jndi = jndi.toUpperCase() + '.';
        while (rs.next()) {
            // DICT表名不区分大小写以统一不同数据库差异
            String tableName = rs.getString(1).toUpperCase();
            if (otherDb) {
                if (tableName.startsWith(jndi)) tables.add(tableName.substring(jndiLen));
            } else if (tableName.indexOf('.') == -1) tables.add(tableName);
        }
        return tables;
    }

    /**
     * 获取所有键名组成的数组，并设置到request attribute名为keyNameList为属性中。
     */
    public static void loadKeyNames(HttpServletRequest request, HttpServletResponse response) {
        boolean isFirst = true;
        StringBuilder buf = new StringBuilder();
        ArrayList<Entry<String, ConcurrentHashMap<Object, String>>> keys = SortUtil.sortKey(KVBuffer.buffer);

        buf.append("[");
        for (Entry<String, ConcurrentHashMap<Object, String>> key : keys) {
            if (isFirst) isFirst = false;
            else buf.append(",");
            buf.append(StringUtil.quote(key.getKey()));
        }
        buf.append("]");
        request.setAttribute("keyNameList", StringUtil.quote(buf.toString(), false));
    }

    /**
     * 删除所有模块中的指定角色信息。
     */
    public static void delModulesPerm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        JSONArray destroy = new JSONArray(request.getParameter("destroy"));
        int j = destroy.length();
        if (j == 0) return;
        String[] roles = new String[j];

        for (int i = 0; i < j; i++)
            roles[i] = destroy.getJSONObject(i).getString("ROLE_ID");
        doDelPerm(Base.modulePath, roles);
        //数据库方式管理权限
        if (Var.getBool("sys.app.useDbRole")) {
            for (String role : roles) {
                // 遍历ConcurrentHashMap的每个条目
                for (Map.Entry<String, List<String>> entry : XwlBuffer.roleBuffer.entrySet()) {
                    // 获取当前键下的List
                    List<String> roleList = entry.getValue();
                    // 线程安全地从List中移除值
                    roleList.removeIf(item -> item.equals(role));
                }
                DbUtil.execute("delete from wb_role_module where role_id='" + role + "'");
                //同步通知其他服务器处理
                com.alibaba.fastjson.JSONObject chatParams = new com.alibaba.fastjson.JSONObject();
                chatParams.put("type", "remove");
                chatParams.put("role", role);
                chatParams.put("server", SysUtil.getServerId());
                Base.map.publish("chat_role", chatParams);
            }
            XwlBuffer.loadRoleID();
        }
    }

    /**
     * 对指定目录下的模块进行扫描，并删除模块文件内指定的角色信息。
     *
     * @param path     路径。
     * @param delRoles 删除的模块角色列表。
     */
    private static void doDelPerm(File path, String[] delRoles) throws Exception {
        File[] files = FileUtil.listFiles(path);
        List<String> sqlList = new ArrayList<>();
        for (File file : files) {
            if (file.isDirectory()) {
                doDelPerm(file, delRoles);
            } else {
                String filename = file.getName();
                if (filename.endsWith(".xwl")) {
                    IDE.updateModule(file, null, delRoles, false);
                }
            }
        }
    }

    /**
     * 设置指定模块列表的角色信息。
     */
    public static void setModulesPerm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String[] roles = new String[1];
        String[] userRoles = Session.getRoles(request);
        boolean checked = Boolean.parseBoolean(request.getParameter("checked"));
        JSONArray pathList = new JSONArray(request.getParameter("path"));
        int j = pathList.length();

        roles[0] = request.getParameter("role");
        List<String> sqlList = new ArrayList<>();
        for (int i = 0; i < j; i++) {
            File file = new File(Base.modulePath, pathList.getString(i));
            // 禁止无权限的用户设置该权限
            if (!file.isDirectory()) {
                if (!WbUtil.canAccess(XwlBuffer.get(file), userRoles)) {
                    SysUtil.accessDenied(request);
                }
                IDE.updateModule(file, null, roles, checked);
                //数据库方式管理权限
                if (Var.getBool("sys.app.useDbRole")) {
                    String key = pathList.getString(i);
                    List<String> roleList = XwlBuffer.roleBuffer.computeIfAbsent(key, k -> new ArrayList<>());
                    if (checked && !roleList.contains(roles[0])) {
                        roleList.add(roles[0]);
                        sqlList.add("insert into wb_role_module values('" + roles[0] + "','" + key + "',NOW())");
                    }
                    if (!checked) {
                        roleList.remove(roles[0]);
                        sqlList.add("delete from wb_role_module where module='" + key + "' and role_id='" + roles[0] + "'");
                    }
                    //同步通知其他服务器处理
                    com.alibaba.fastjson.JSONObject chatParams = new com.alibaba.fastjson.JSONObject();
                    chatParams.put("type", "set");
                    chatParams.put("key", key);
                    chatParams.put("checked", checked);
                    chatParams.put("role", roles[0]);
                    chatParams.put("server", SysUtil.getServerId());
                    Base.map.publish("chat_role", chatParams);
                }
            }
        }
        //数据库方式管理权限
        if (Var.getBool("sys.app.useDbRole")) {
            Connection conn = null;
            Statement stmt = null;
            try {
                conn = DbUtil.getConnection(); // 获取数据库连接
                conn.setAutoCommit(false); // 开启事务
                stmt = conn.createStatement();
                for (String sql : sqlList) {
                    stmt.addBatch(sql); // 将SQL语句添加到批处理中
                }
                stmt.executeBatch(); // 执行批处理
                conn.commit(); // 提交事务
            } catch (SQLException e) {
                LogUtil.error("设置权限异常：" + e.getMessage());
                if (conn != null) {
                    try {
                        conn.rollback(); // 如果出现异常，则回滚事务
                    } catch (SQLException ex) {
                        ex.printStackTrace();
                    }
                }
            } finally {
                if (stmt != null) {
                    try {
                        stmt.close(); // 关闭Statement
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }
                }
                if (conn != null) {
                    try {
                        conn.close(); // 关闭数据库连接
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }
}