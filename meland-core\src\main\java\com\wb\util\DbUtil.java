package com.wb.util;

import com.wb.common.Base;
import com.wb.common.Dictionary;
import com.wb.common.KVBuffer;
import com.wb.common.Var;
import com.wb.tool.DataOutput;
import com.wb.tool.DictRecord;
import com.wb.tool.Query;
import com.wb.tool.Updater;
import org.apache.commons.io.IOUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.naming.InitialContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.io.*;
import java.math.BigDecimal;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据库工具方法类。
 */
public class DbUtil {
    /**
     * 预定义的SQL类型和名称对照表
     */
    public static final Object[][] sqlTypes = {{"BIT", Types.BIT}, {"TINYINT", Types.TINYINT},
            {"SMALLINT", Types.SMALLINT}, {"INTEGER", Types.INTEGER}, {"BIGINT", Types.BIGINT},
            {"FLOAT", Types.FLOAT}, {"REAL", Types.REAL}, {"DOUBLE", Types.DOUBLE}, {"NUMERIC", Types.NUMERIC},
            {"DECIMAL", Types.DECIMAL}, {"CHAR", Types.CHAR}, {"VARCHAR", Types.VARCHAR},
            {"LONGVARCHAR", Types.LONGVARCHAR}, {"DATE", Types.DATE}, {"TIME", Types.TIME},
            {"TIMESTAMP", Types.TIMESTAMP}, {"BINARY", Types.BINARY}, {"VARBINARY", Types.VARBINARY},
            {"LONGVARBINARY", Types.LONGVARBINARY}, {"NULL", Types.NULL}, {"OTHER", Types.OTHER},
            {"JAVA_OBJECT", Types.JAVA_OBJECT}, {"DISTINCT", Types.DISTINCT}, {"STRUCT", Types.STRUCT},
            {"ARRAY", Types.ARRAY}, {"BLOB", Types.BLOB}, {"CLOB", Types.CLOB}, {"REF", Types.REF},
            {"DATALINK", Types.DATALINK}, {"BOOLEAN", Types.BOOLEAN}, {"ROWID", Types.ROWID},
            {"NCHAR", Types.NCHAR}, {"NVARCHAR", Types.NVARCHAR}, {"LONGNVARCHAR", Types.LONGNVARCHAR},
            {"NCLOB", Types.NCLOB}, {"SQLXML", Types.SQLXML}};

    private static HashMap<Connection, Object[]> connectionMap = new HashMap<Connection, Object[]>();
    /**
     * 使用动态加载数据
     */
    public static boolean usePagingData = true;

    /**
     * 根据字段类型名称获得字段类型编号。如果名称为空返回VARCHAR，如果没有找到返回null。
     *
     * @param name 字段类型名称。
     * @return 字段类型编号。
     */
    public static Integer getFieldType(String name) {
        if (StringUtil.isEmpty(name))
            return Types.VARCHAR;
        int i, j = sqlTypes.length;

        for (i = 0; i < j; i++)
            if (name.equalsIgnoreCase((String) sqlTypes[i][0]))
                return (Integer) (sqlTypes[i][1]);
        if (StringUtil.isInteger(name))
            return Integer.parseInt(name);
        return null;
    }

    /**
     * 根据字段类型编号获得字段类型名称。如果没有找到名称直接返回type。
     *
     * @param type 字段类型编号。
     * @return 字段类型名称。
     */
    public static String getTypeName(int type) {
        int i, j = sqlTypes.length;

        switch (type) {
            case Types.CHAR:
            case Types.NCHAR:
            case Types.VARCHAR:
            case Types.NVARCHAR:
                return null;
        }
        for (i = 0; i < j; i++)
            if (type == (Integer) sqlTypes[i][1])
                return ((String) (sqlTypes[i][0])).toLowerCase();
        return Integer.toString(type);
    }

    public static void importData(Connection connection, String tableName, JSONArray ja) throws Exception {
        importData(connection, tableName, ja, null, ' ');
    }

    /**
     * 导入JSON数据至指定数据库表。导入完成后自动关闭reader。
     *
     * @param connection 数据库连接对象。
     * @param tableName  表名。
     * @param reader     读数据的reader对象。
     * @throws Exception 导入过程发生异常。
     */
    public static void importData(Connection connection, String tableName, BufferedReader reader) throws Exception {
        importData(connection, tableName, reader, null, ' ');
    }

    /**
     * 导入数据至指定数据库表。导入的数据可以为txt,excel或json格式。导入完成后自动关闭reader。
     *
     * @param connection     数据库连接对象。
     * @param tableName      表名。
     * @param data           读数据的reader对象。
     * @param fieldList      字段名称列表，如果此参数为null，表示数据为json格式。
     * @param fieldSeparator 字段值之间的分隔符，此参数只有当数据为非json格式时才有意义。
     * @throws Exception 导入过程发生异常。
     */
    public static void importData(Connection connection, String tableName, Object data, String[] fieldList,
                                  char fieldSeparator) throws Exception {
        ResultSet rs = null;
        PreparedStatement st = null;

        boolean jsonFormat = fieldList == null;
        BufferedReader reader = null;
        int[] indexList;
        int[] types;
        int j;
        int i;
        String[] quoteNames;
        String[] fieldNames;
        try {
            st = connection
                    .prepareStatement(StringUtil.concat(new String[]{"select * from ", tableName, " where 1=0"}));
            rs = st.executeQuery();
            ResultSetMetaData meta = rs.getMetaData();
            j = meta.getColumnCount();
            if (jsonFormat)
                indexList = null;
            else
                indexList = new int[j];
            types = new int[j];
            fieldNames = new String[j];
            quoteNames = new String[j];
            for (i = 0; i < j; i++) {
                int k = i + 1;
                types[i] = meta.getColumnType(k);
                fieldNames[i] = meta.getColumnLabel(k);
                fieldNames[i] = getFieldName(fieldNames[i]);
                quoteNames[i] = StringUtil.quoteIf(fieldNames[i]);
                if (!jsonFormat)
                    indexList[i] = StringUtil.indexOf(fieldList, fieldNames[i]);
            }
            close(rs);
            close(st);
            st = connection.prepareStatement(StringUtil.concat(new String[]{"insert into ", tableName, "(",
                    StringUtil.join(quoteNames, ","), ") values (?", StringUtil.repeat(",?", j - 1), ")"}));
            if ((data instanceof JSONArray)) {
                JSONArray ja = (JSONArray) data;
                int n = ja.length();
                for (int m = 0; m < n; m++) {
                    JSONObject record = ja.optJSONObject(m);
                    for (i = 0; i < j; i++)
                        setObject(st, i + 1, types[i], JsonUtil.opt(record, fieldNames[i]));
                    addBatch(st);
                }
            } else {
                reader = (BufferedReader) data;
                String line;
                while ((line = reader.readLine()) != null) {
                    if (jsonFormat) {
                        JSONObject record = new JSONObject(line);
                        for (i = 0; i < j; i++)
                            setObject(st, i + 1, types[i], JsonUtil.opt(record, fieldNames[i]));
                    } else {
                        String[] values = StringUtil.split(line, fieldSeparator);
                        for (i = 0; i < j; i++) {
                            String value = indexList[i] == -1 ? null : values[indexList[i]];
                            setObject(st, i + 1, types[i], value);
                        }
                    }
                    addBatch(st);
                }
            }
            executeBatch(st);
        } finally {
            close(rs);
            close(st);
            if (reader != null)
                reader.close();
        }
    }

    /**
     * 导出指定结果集数据。
     *
     * @param rs     导出的结果集。
     * @param writer 写数据的writer对象。
     * @throws Exception 导出过程发生异常。
     */
    @SuppressWarnings("deprecation")
    public static void exportData(ResultSet rs, Writer writer) throws Exception {
        ResultSetMetaData meta = rs.getMetaData();
        InputStream stream;
        boolean newLine = false;
        int i, j = meta.getColumnCount(), k, types[] = new int[j];
        String names[] = new String[j];

        for (i = 0; i < j; i++) {
            types[i] = meta.getColumnType(i + 1);
            names[i] = meta.getColumnLabel(i + 1);
            names[i] = DbUtil.getFieldName(names[i]);
        }
        while (rs.next()) {
            if (newLine)
                writer.write('\n');
            else
                newLine = true;
            writer.write('{');
            for (i = 0; i < j; i++) {
                k = i + 1;
                if (i > 0)
                    writer.write(',');
                writer.write(StringUtil.quote(names[i]));
                writer.write(':');
                if (isBlobField(types[i])) {
                    stream = rs.getBinaryStream(k);
                    try {
                        writer.write(StringUtil.encode(stream));
                    } finally {
                        IOUtils.closeQuietly(stream);
                    }
                } else {
                    writer.write(StringUtil.encode(DbUtil.getObject(rs, k, types[i])));
                }
            }
            writer.write('}');
        }
        writer.flush();
    }

    public static void exportExcel(ResultSet rs, OutputStream outputStream, JSONArray headers, String title,
                                   String keys, String dictTableNames) throws Exception {
        if (headers == null)
            headers = getHeaders(rs, dictTableNames);
        DataOutput.outputExcel(outputStream, headers, getData(rs, keys, dictTableNames), title,
                new JSONObject("{mergeInfo:[]}"), "Y-m-d", "H:i:s", false);
    }

    public static JSONArray getHeaders(ResultSet rs, String dictTableNames) throws Exception {
        ResultSetMetaData meta = rs.getMetaData();
        int j = meta.getColumnCount();
        String[] tables = null;
        JSONArray headers = new JSONArray();

        boolean hasDict = dictTableNames != null;

        if (hasDict)
            tables = dictTableNames.split(",");
        for (int i = 0; i < j; i++) {
            String fieldName = meta.getColumnLabel(i + 1);
            fieldName = getFieldName(fieldName);
            DictRecord dictRecord;
            if (hasDict)
                dictRecord = Dictionary.find(tables, fieldName);
            else
                dictRecord = null;
            JSONObject object = new JSONObject();
            object.put("field", fieldName);
            if (dictRecord == null) {
                object.put("text", fieldName);
                int size = meta.getColumnDisplaySize(i + 1);
                size = Math.max(size, fieldName.length() + 3);
                if (size < 5)
                    size = 5;
                if (size > 18)
                    size = 18;
                object.put("width", size * 10);
            } else {
                object.put("text", dictRecord.dispText);
                object.put("width", dictRecord.dispWidth);
            }
            headers.put(object);
        }
        return headers;
    }

    /**
     * 如果系统允许批操作该方法等同statement.addBatch,否则等同statement.executeUpdate。
     *
     * @param statement 执行更新的PreparedStatement对象。
     * @throws SQLException 执行过程发生异常。
     */
    public static void addBatch(PreparedStatement statement) throws SQLException {
        if (Var.batchUpdate)
            statement.addBatch();
        else
            statement.executeUpdate();
    }

    /**
     * 如果系统允许批操作该方法等同executeBatch,否则无任何效果。
     *
     * @param statement 执行更新的PreparedStatement对象。
     * @return 每次执行影响记录数量的数组。如果不允许批操作则返回null。
     * @throws SQLException 执行过程发生异常。
     */
    public static int[] executeBatch(PreparedStatement statement) throws SQLException {
        if (Var.batchUpdate) {
            return statement.executeBatch();
        }
        return null;
    }

    /**
     * 在数据库连接池中获取新的默认数据库连接，默认连接jndi由变量sys.jndi指定。
     *
     * @return 新的默认数据库连接。
     * @throws Exception 获取连接过程发生异常。
     */
    public static Connection getConnection() throws Exception {
        return getConnection("");
    }

    /**
     * 在当前HttpServletRequest请求对象中获取共享的默认数据库连接，默认连接jndi由变量
     * sys.jndi指定。在请求周期内，该连接将被使用相同JNDI的所有数据库组件共享。
     * 完成请求后，连接将被自动关闭。 如果连接存在未提交的事务且在请求期间未发生异常，
     * 则系统自动提交事务，否则回滚事务。
     *
     * @param request 请求对象。
     * @return 共享的默认数据库连接。
     * @throws Exception 获取连接过程发生异常。
     */
    public static Connection getConnection(HttpServletRequest request) throws Exception {
        return getConnection(request, null);
    }

    /**
     * 在数据库连接池中获取新的指定JNDI的数据库连接。如果参数JNDI为空则使用默认JNDI。
     *
     * @param jndi 在WebBuilder变量sys.jndi注册过的jndi名称。
     * @return 数据库连接。
     * @throws Exception 获取连接过程发生异常。
     */
    public static Connection getConnection(String jndi) throws Exception {
        // 仅允许访问注册过的jndi，防止jndi RMI注入。
        if (StringUtil.isEmpty(jndi))
            jndi = Var.jndi;
        else
            jndi = Var.getString("sys.jndi." + jndi);
        InitialContext context = new InitialContext();
        DataSource ds = (DataSource) context.lookup(jndi);
        Connection conn = ds.getConnection();
        if (Var.checkLeaks) {
            detectConnnection(conn);
        }
        return conn;
    }

    /**
     * 私有的、静态的、同步的方法来检测数据库连接
     *
     * @param connection 数据库连接对象
     */
    private static synchronized void detectConnnection(Connection connection) {
        // 获取connectionMap的所有条目
        Set<Entry<Connection, Object[]>> es = connectionMap.entrySet();
        // 创建一个ArrayList来存储需要被移除的连接
        ArrayList<Connection> removedList = new ArrayList<Connection>();
        // 遍历所有的连接
        for (Entry<Connection, Object[]> e : es) {
            // 获取当前遍历到的连接
            Connection conn = (Connection) e.getKey();
            try {
                // 如果当前连接已经关闭，则将其添加到需要被移除的连接列表中
                if (conn.isClosed())
                    removedList.add(conn);
            } catch (Exception localException) {
                // 如果在检测连接是否关闭时发生异常，则忽略该异常
            }
        }
        // 遍历需要被移除的连接列表，并从connectionMap中移除这些连接
        for (Connection c : removedList)
            connectionMap.remove(c);
        // 创建一个StringWriter对象，用于存储堆栈跟踪信息
        StringWriter writer = new StringWriter();
        // 创建一个PrintWriter对象，将其连接到StringWriter对象上，以便可以将堆栈跟踪信息写入StringWriter
        PrintWriter pwriter = new PrintWriter(writer, true);
        // 创建一个新的Throwable对象，并将其堆栈跟踪信息打印到PrintWriter对象中
        new Throwable().printStackTrace(pwriter);
        // 创建一个Object数组，用于存储当前时间和堆栈跟踪信息
        Object[] object = new Object[2];
        object[0] = new java.util.Date();
        object[1] = writer.toString();
        // 将新的连接和其对应的信息添加到connectionMap中
        connectionMap.put(connection, object);
    }

    public static synchronized ArrayList<Object[]> getUnclosedConnections() {
        ArrayList<Object[]> list = new ArrayList<Object[]>();
        Set<Entry<Connection, Object[]>> es = connectionMap.entrySet();

        for (Entry<Connection, Object[]> e : es) {
            Connection conn = (Connection) e.getKey();
            try {
                if (!conn.isClosed())
                    list.add((Object[]) e.getValue());
            } catch (Exception localException) {
            }
        }
        return list;
    }

    /**
     * 在当前HttpServletRequest请求对象中获取指定JNDI的数据库连接。
     * 在请求周期内，该连接将被使用相同JNDI的所有数据库组件共享。
     * 完成请求后，连接将被自动关闭。 如果连接存在未提交的事务且在请求期间未发生异常，
     * 则系统自动提交事务，否则回滚事务。如果参数JNDI为空则使用默认JNDI。
     *
     * @param request 请求对象。
     * @return 共享的指定JNDI数据库连接。
     * @throws Exception 获取连接过程发生异常。
     */
    public static Connection getConnection(HttpServletRequest request, String jndi) throws Exception {
        String storeName;
        if (StringUtil.isEmpty(jndi))
            storeName = "conn@@";
        else
            storeName = "conn@@" + jndi;
        Object obj = WebUtil.getObject(request, storeName);
        Connection conn;
        if (obj == null) {
            conn = getConnection(jndi);
            WebUtil.setObject(request, storeName, conn);
        } else {
            conn = (Connection) obj;
        }
        return conn;
    }

    /**
     * 重新开始新的数据库事务，设置连接为非自动提交模式，并设置事务的隔离级别。如果连接存在
     * 未提交的事务，首先提交事务，然后开始新的事务。
     *
     * @param connection 连接对象。
     * @param isolation  事务孤立程度。
     * @throws Exception 设置事务发生异常。
     */
    public static void startTransaction(Connection connection, String isolation) throws Exception {
        if (!connection.getAutoCommit())
            connection.commit();
        connection.setAutoCommit(false);
        if (!StringUtil.isEmpty(isolation)) {
            if (isolation.equals("readUncommitted"))
                connection.setTransactionIsolation(Connection.TRANSACTION_READ_UNCOMMITTED);
            else if (isolation.equals("readCommitted"))
                connection.setTransactionIsolation(Connection.TRANSACTION_READ_COMMITTED);
            else if (isolation.equals("repeatableRead"))
                connection.setTransactionIsolation(Connection.TRANSACTION_REPEATABLE_READ);
            else if (isolation.equals("serializable"))
                connection.setTransactionIsolation(Connection.TRANSACTION_SERIALIZABLE);
        }
    }

    /**
     * 关闭指定的ResultSet。如果关闭ResultSet过程中发生错误，系统将不会抛出任何异常。
     *
     * @param resultSet 需要关闭的ResultSet。
     */
    private static void closeResult(ResultSet resultSet) {
        try {
            resultSet.close();
            resultSet = null;
        } catch (Throwable localThrowable) {
        }
    }

    /**
     * 关闭指定的Statement。如果关闭Statement过程中发生错误，系统将不会抛出任何异常。
     *
     * @param statement 需要关闭的Statement。
     */
    private static void closeStatement(Statement statement) {
        try {
            statement.close();
            statement = null;
        } catch (Throwable localThrowable) {
        }
    }

    /**
     * 关闭指定的Connection。如果关闭连接过程中发生错误，系统将不会抛出任何异常。如果指定
     * 关闭连接前提交事务，首先尝试提交操作，如果失败则尝试回滚操作。
     * 
     * 注意：此方法会尝试处理事务，请确保调用前已明确控制事务状态，否则可能导致异常。
     *
     * @param connection 需要关闭的连接。
     * @param rollback   如果连接存在未提交的事务，是否执行回滚。true回滚，false提交。
     */
    private static void closeConnection(Connection connection, boolean rollback) {
        try {
            if (connection.isClosed())
                return;
            try {
                if (!connection.getAutoCommit())
                    if (rollback)
                        connection.rollback();
                    else
                        connection.commit();
            } catch (Throwable e) {
                if (!rollback)
                    connection.rollback();
            } finally {
                connection.close();
                connection = null;
            }
        } catch (Throwable localThrowable1) {
        }
    }

    /**
     * 关闭指定的数据库资源，这些资源包括ResultSet，Statement，Connection。
     * 如果object为空该方法不产生任何效果。如果关闭过程发生异常，这些异常将不会被抛出。
     *
     * @param object 需要关闭的资源
     */
    public static void close(Object object) {
        if ((object instanceof ResultSet))
            closeResult((ResultSet) object);
        else if ((object instanceof Statement))
            closeStatement((Statement) object);
        else if ((object instanceof Connection))
            closeConnection((Connection) object, true);
    }

    /**
     * 关闭指定的数据库连接。如果关闭连接过程中发生错误，系统将不会抛出任何异常。如果连接
     * 存在未提交的事务，首先尝试提交事务如果失败再尝试回滚。
     * 
     * 注意：此方法会自动提交未完成的事务，如果您的业务代码已经明确处理了事务，
     * 请使用closeOnly方法代替，以避免重复处理事务导致的异常。
     *
     * @param connection 需要关闭的连接。
     */
    public static void closeCommit(Connection connection) {
        if (connection != null)
            closeConnection(connection, false);
    }

    /**
     * 判断指定字段类型是否是二进制类型。
     *
     * @param type 字段类型。
     * @return true二进制类型，false不是二进制类型。
     */
    public static boolean isBlobField(int type) {
        switch (type) {
            case Types.BINARY:
            case Types.VARBINARY:
            case Types.LONGVARBINARY:
            case Types.BLOB:
                return true;
        }
        return false;
    }

    /**
     * 判断指定字段类型是否是大文本类型。
     *
     * @param type 字段类型。
     * @return true大文本类型，false不是大文本类型。
     */
    public static boolean isTextField(int type) {
        switch (type) {
            case Types.LONGVARCHAR:
            case Types.LONGNVARCHAR:
            case Types.CLOB:
            case Types.NCLOB:
                return true;
        }
        return false;
    }

    /**
     * 判断指定字段类型是否是字符串类型。
     *
     * @param type 字段类型。
     * @return true字符串类型，false不是字符串类型。
     */
    public static boolean isStringField(int type) {
        switch (type) {
            case Types.CHAR:
            case Types.NCHAR:
            case Types.VARCHAR:
            case Types.NVARCHAR:
                return true;
        }
        return false;
    }

    /**
     * 判断指定字段类型是否可能是浮点数类型。
     *
     * @param type 字段类型。
     * @return true可能是浮点数类型，false不可能是浮点数类型。
     */
    public static boolean maybeFloatField(int type) {
        switch (type) {
            case Types.FLOAT:
            case Types.REAL:
            case Types.DOUBLE:
            case Types.NUMERIC:
            case Types.DECIMAL:
                return true;
        }
        return false;
    }

    /**
     * 读取指定结果集首行所有字段数据，并存储至request的attribute对象。
     * 存储的属性名称为[prefix.字段名称]，属性值为字段值。
     *
     * @param request   请求对象。
     * @param resultSet 结果集对象。
     * @param prefix    存储的名称前缀，如果值为空将不添加前缀。
     * @throws Exception 读取结果集过程发生异常。
     */
    public static void loadFirstRow(HttpServletRequest request, ResultSet resultSet, String prefix) throws Exception {
        if (!resultSet.next())
            return;
        ResultSetMetaData meta = resultSet.getMetaData();
        int j = meta.getColumnCount();
        boolean hasPrefix = !StringUtil.isEmpty(prefix);

        for (int i = 1; i <= j; i++) {
            String name = meta.getColumnLabel(i);
            name = getFieldName(name);
            if (hasPrefix)
                name = StringUtil.concat(new String[]{prefix, ".", name});
            Object object = getObject(resultSet, i, meta.getColumnType(i));
            if (((object instanceof ResultSet)) || ((object instanceof InputStream)))
                WebUtil.setObject(request, SysUtil.getId(), object);
            request.setAttribute(name, object);
        }
    }

    public static HashMap<String, String> getMap(String sql) throws Exception {
        ResultSet rs = null;
        Statement st = null;
        Connection conn = null;
        HashMap<String, String> result = new HashMap<String, String>();
        try {
            conn = getConnection();
            st = conn.createStatement();
            rs = st.executeQuery(sql);
            while (rs.next())
                result.put(rs.getString(1), rs.getString(2));
        } finally {
            close(rs);
            close(st);
            close(conn);
        }
        return result;
    }

    /**
     * 获取指定字段类型的大类别。
     *
     * @param type 字段类型。
     * @return 类别。
     */
    public static String getTypeCategory(int type) {
        switch (type) {
            case Types.BIGINT:
            case Types.INTEGER:
            case Types.SMALLINT:
            case Types.TINYINT:
            case Types.BOOLEAN:
            case Types.BIT:
                // boolean bit为兼容不同数据库返回int型
                return "int";
            case Types.DECIMAL:
            case Types.DOUBLE:
            case Types.FLOAT:
            case Types.NUMERIC:
            case Types.REAL:
                return "float";
            case Types.TIMESTAMP:
            case Types.DATE:
            case Types.TIME:
                return "date";
            default:
                return "string";
        }
    }

    /**
     * 获得结果集当前记录指定索引号大文本字段的字符串值。大文本字段通常指类似CLOB类型的字段。
     *
     * @param rs    结果集对象。
     * @param index 字段索引号。
     * @return 字段值。
     * @throws Exception 读取过程发生异常。
     */
    public static String getText(ResultSet rs, int index) throws Exception {
        return (String) getObject(rs, index, -1);
    }

    /**
     * 获得结果集当前记录指定名称大文本字段的字符串值。大文本字段通常指类似CLOB类型的字段。
     *
     * @param rs        结果集对象。
     * @param fieldName 字段名称。
     * @return 字段值。
     * @throws Exception 读取过程发生异常。
     */
    public static String getText(ResultSet rs, String fieldName) throws Exception {
        return (String) getObject(rs, fieldName, -1);
    }

    /**
     * 设置PreparedStatement指定索引号参数的大文本值。大文本值将使用字符串流的方式进行设置。
     *
     * @param statement PreparedStatement对象。
     * @param index     参数引号。
     * @param value     设置的值。
     * @throws Exception 设置参数值过程发生异常。
     */
    public static void setText(PreparedStatement statement, int index, String value) throws Exception {
        setObject(statement, index, -1, value);
    }

    /**
     * 获取CallableStatement指定索引号的参数值。
     *
     * @param statement CallableStatement对象。
     * @param index     参数索引号。
     * @param type      参数类型。
     * @return 参数值。
     * @throws Exception 获取参数值过程发生异常。
     */
    public static Object getObject(CallableStatement statement, int index, int type) throws Exception {
        Object obj;
        switch (type) {
            case Types.CHAR:
            case Types.NCHAR:
            case Types.VARCHAR:
            case Types.NVARCHAR:
                obj = statement.getString(index);
                break;
            case Types.INTEGER:
                obj = statement.getInt(index);
                break;
            case Types.TINYINT:
                obj = statement.getByte(index);
                break;
            case Types.SMALLINT:
                obj = statement.getShort(index);
                break;
            case Types.BIGINT:
                obj = statement.getLong(index);
                break;
            case Types.REAL:
            case Types.FLOAT:
                obj = statement.getFloat(index);
                break;
            case Types.DOUBLE:
                obj = statement.getDouble(index);
                break;
            case Types.DECIMAL:
            case Types.NUMERIC:
                obj = statement.getBigDecimal(index);
                break;
            case Types.TIMESTAMP:
                obj = statement.getTimestamp(index);
                break;
            case Types.DATE:
                obj = statement.getDate(index);
                break;
            case Types.TIME:
                obj = statement.getTime(index);
                break;
            case Types.BOOLEAN:
            case Types.BIT:
                // boolean bit为兼容不同数据库返回int型
                obj = statement.getBoolean(index) ? 1 : 0;
                break;
            case Types.LONGVARCHAR:
            case Types.LONGNVARCHAR:
            case Types.CLOB:
            case Types.NCLOB:
                Reader rd = statement.getCharacterStream(index);
                if (rd == null)
                    obj = null;
                else
                    obj = SysUtil.readString(rd);
                break;
            default:
                obj = statement.getObject(index);
        }
        if (statement.wasNull())
            return null;
        else
            return obj;
    }

    /**
     * 获取结果集当前记录指定索引号的字段值。
     *
     * @param rs    结果集。
     * @param index 字段索引号。
     * @param type  字段类型。
     * @return 字段值。
     * @throws Exception 获取字段值过程发生异常。
     */
    public static Object getObject(ResultSet rs, int index, int type) throws Exception {
        Object obj;
        switch (type) {
            case Types.CHAR:
            case Types.NCHAR:
            case Types.VARCHAR:
            case Types.NVARCHAR:
                obj = rs.getString(index);
                break;
            case Types.INTEGER:
                obj = rs.getInt(index);
                break;
            case Types.TINYINT:
                obj = rs.getByte(index);
                break;
            case Types.SMALLINT:
                obj = rs.getShort(index);
                break;
            case Types.BIGINT:
                obj = rs.getLong(index);
                break;
            case Types.REAL:
            case Types.FLOAT:
                obj = rs.getFloat(index);
                break;
            case Types.DOUBLE:
                obj = rs.getDouble(index);
                break;
            case Types.DECIMAL:
            case Types.NUMERIC:
                obj = rs.getBigDecimal(index);
                break;
            case Types.TIMESTAMP:
                obj = rs.getTimestamp(index);
                break;
            case Types.DATE:
                obj = rs.getDate(index);
                break;
            case Types.TIME:
                obj = rs.getTime(index);
                break;
            case Types.BOOLEAN:
            case Types.BIT:
                // boolean bit为兼容不同数据库返回int型
                obj = rs.getBoolean(index) ? 1 : 0;
                break;
            case Types.LONGVARCHAR:
            case Types.LONGNVARCHAR:
            case Types.CLOB:
            case Types.NCLOB:
                Reader rd = rs.getCharacterStream(index);
                if (rd == null)
                    obj = null;
                else
                    obj = SysUtil.readString(rd);
                break;
            case Types.BLOB:
            case Types.BINARY:
            case Types.VARBINARY:
            case Types.LONGVARBINARY:
                InputStream is = rs.getBinaryStream(index);
                if (is != null)
                    is.close();// 读取之后再关闭wasNull方法才可判断是否为空
                obj = "(blob)";
                // 如果需要读取数据可直接读取流
                break;
            default:
                obj = rs.getObject(index);
        }
        if (rs.wasNull())
            return null;
        else
            return obj;
    }

    /**
     * 获取结果集当前记录指定名称的字段值。
     *
     * @param rs        结果集。
     * @param fieldName 字段名称。
     * @param type      字段类型。
     * @return 字段值。
     * @throws Exception 获取字段值过程发生异常。
     */
    public static Object getObject(ResultSet rs, String fieldName, int type) throws Exception {
        Object obj;
        switch (type) {
            case Types.CHAR:
            case Types.NCHAR:
            case Types.VARCHAR:
            case Types.NVARCHAR:
                obj = rs.getString(fieldName);
                break;
            case Types.INTEGER:
                obj = rs.getInt(fieldName);
                break;
            case Types.TINYINT:
                obj = rs.getByte(fieldName);
                break;
            case Types.SMALLINT:
                obj = rs.getShort(fieldName);
                break;
            case Types.BIGINT:
                obj = rs.getLong(fieldName);
                break;
            case Types.REAL:
            case Types.FLOAT:
                obj = rs.getFloat(fieldName);
                break;
            case Types.DOUBLE:
                obj = rs.getDouble(fieldName);
                break;
            case Types.DECIMAL:
            case Types.NUMERIC:
                obj = rs.getBigDecimal(fieldName);
                break;
            case Types.TIMESTAMP:
                obj = rs.getTimestamp(fieldName);
                break;
            case Types.DATE:
                obj = rs.getDate(fieldName);
                break;
            case Types.TIME:
                obj = rs.getTime(fieldName);
                break;
            case Types.BOOLEAN:
            case Types.BIT:
                // boolean bit为兼容不同数据库返回int型
                obj = rs.getBoolean(fieldName) ? 1 : 0;
                break;
            case Types.LONGVARCHAR:
            case Types.LONGNVARCHAR:
            case Types.CLOB:
            case Types.NCLOB:
                Reader rd = rs.getCharacterStream(fieldName);
                if (rd == null)
                    obj = null;
                else
                    obj = SysUtil.readString(rd);
                break;
            case Types.BLOB:
            case Types.BINARY:
            case Types.VARBINARY:
            case Types.LONGVARBINARY:
                InputStream is = rs.getBinaryStream(fieldName);
                if (is != null)
                    is.close();// 读取之后再关闭wasNull方法才可判断是否为空
                obj = "(blob)";
                // 如果需要读取数据可直接读取流
                break;
            default:
                obj = rs.getObject(fieldName);
        }
        if (rs.wasNull())
            return null;
        else
            return obj;
    }

    /**
     * 设置PreparedStatement对象指定索引号的参数值。
     *
     * @param statement PreparedStatement对象。
     * @param index     参数索引。
     * @param type      参数类型。
     * @param object    参数值。
     * @throws Exception 设置参数值过程发生异常。
     */
    public static void setObject(PreparedStatement statement, int index, int type, Object object) throws Exception {
        if (object == null || object instanceof String) {
            String value;

            if (object == null)
                value = null;
            else
                value = (String) object;
            if (StringUtil.isEmpty(value))
                statement.setNull(index, type);
            else {
                switch (type) {
                    case Types.CHAR:
                    case Types.NCHAR:
                    case Types.VARCHAR:
                    case Types.NVARCHAR:
                        if (Var.emptyString.equals(value))
                            statement.setString(index, "");
                        else
                            statement.setString(index, value);
                        break;
                    case Types.INTEGER:
                        statement.setInt(index, Integer.parseInt(StringUtil.convertBool(value)));
                        break;
                    case Types.TINYINT:
                        statement.setByte(index, Byte.parseByte(StringUtil.convertBool(value)));
                        break;
                    case Types.SMALLINT:
                        statement.setShort(index, Short.parseShort(StringUtil.convertBool(value)));
                        break;
                    case Types.BIGINT:
                        statement.setLong(index, Long.parseLong(StringUtil.convertBool(value)));
                        break;
                    case Types.REAL:
                    case Types.FLOAT:
                        statement.setFloat(index, Float.parseFloat(StringUtil.convertBool(value)));
                        break;
                    case Types.DOUBLE:
                        statement.setDouble(index, Double.parseDouble(StringUtil.convertBool(value)));
                        break;
                    case Types.DECIMAL:
                    case Types.NUMERIC:
                        statement.setBigDecimal(index, new BigDecimal(StringUtil.convertBool(value)));
                        break;
                    case Types.TIMESTAMP:
                        statement.setTimestamp(index, Timestamp.valueOf(DateUtil.fixTimestamp(value, false)));
                        break;
                    case Types.DATE:
                        // 如果值有空格使用timestamp类型处理
                        try {
                            if (value.indexOf(' ') != -1)
                                statement.setTimestamp(index, Timestamp.valueOf(DateUtil.fixTimestamp(value, false)));
                            else {
                                statement.setDate(index, java.sql.Date.valueOf(DateUtil.fixTimestamp(value.trim(), true)));
                            }
                        } catch (Exception e) {
                            throw new Exception(StringUtil.format("字符串 {0} 日期格式错误：{1}", value, e.getMessage()));
                        }
                        break;
                    case Types.TIME:
                        // 如果值有空格使用timestamp类型处理
                        if (value.indexOf(' ') != -1)
                            statement.setTimestamp(index, Timestamp.valueOf(DateUtil.fixTimestamp(value, false)));
                        else
                            statement.setTime(index, Time.valueOf(DateUtil.fixTime(value)));
                        break;
                    case Types.BOOLEAN:
                    case Types.BIT:
                        statement.setBoolean(index, StringUtil.getBool(value));
                        break;
                    case Types.LONGVARCHAR:
                    case Types.LONGNVARCHAR:
                    case Types.CLOB:
                    case Types.NCLOB:
                        statement.setCharacterStream(index, new StringReader(value), value.length());
                        break;
                    case Types.BLOB:
                    case Types.BINARY:
                    case Types.VARBINARY:
                    case Types.LONGVARBINARY:
                        // 字符串存储到二进制字段视为BASE64编码
                        InputStream is = new ByteArrayInputStream(StringUtil.decodeBase64(value));
                        statement.setBinaryStream(index, is, is.available());
                        break;
                    default:
                        statement.setObject(index, value, type);
                }
            }
        } else {
            if (object instanceof InputStream)
                statement.setBinaryStream(index, (InputStream) object, ((InputStream) object).available());
            else if (object instanceof java.util.Date) {
                statement.setTimestamp(index, new Timestamp(((java.util.Date) object).getTime()));
            } else if (((object instanceof Double)) && (!maybeFloatField(type))) {
                object = Integer.valueOf(((Double) object).intValue());
                statement.setObject(index, object, type);
            } else {
                statement.setObject(index, object, type);
            }
        }
    }

    /**
     * 运行SQL语句，并获得返回值。可能返回值为结果集，影响记录数或输出参数结果Map。
     *
     * @param request    请求对象。用户获取参数和存取值。
     * @param sql        SQL语句。
     * @param jndi       数据库连接jndi。
     * @param loadParams 是否自动加载输出参数，可为"auto","load","none"，默认为自动。
     * @return 运行的结果。
     * @throws Exception 运行SQL异常。
     */
    public static Object run(HttpServletRequest request, String sql, String jndi, String loadParams,
                             boolean returnStatement) throws Exception {
        Query query = new Query();
        query.request = request;
        query.sql = sql;
        query.jndi = jndi;
        query.loadParams = loadParams;
        query.returnStatement = returnStatement;
        return query.run();
    }

    /**
     * 运行SQL语句，并获得返回值。可能返回值为结果集，影响记录数或输出参数结果Map。
     *
     * @param request 请求对象。用户获取参数和存取值。
     * @param sql     SQL语句。
     * @param jndi    数据库连接jndi。
     * @return 运行的结果。
     * @throws Exception 运行SQL异常。
     */
    public static Object run(HttpServletRequest request, String sql, String jndi) throws Exception {
        Query query = new Query();
        query.request = request;
        query.sql = sql;
        query.jndi = jndi;
        return query.run();
    }

    /**
     * 在默认数据库运行SQL语句，并获得返回值。可能返回值为结果集，影响记录数或输出参数结果Map。
     *
     * @param request 请求对象。用户获取参数和存取值。
     * @param sql     SQL语句。
     * @return 运行的结果。
     * @throws Exception 运行SQL异常。
     */
    public static Object run(HttpServletRequest request, String sql) throws Exception {
        return run(request, sql, null);
    }

    /**
     * 在默认数据库运行上下文关联的SQL更新语句。SQL参数取自表字段同名的request参数。
     *
     * @param request   请求对象。
     * @param tableName 表名。
     * @param mode      SQL语句模式：'insert', 'update', 'delete'。
     * @throws Exception 运行SQL异常。
     */
    public static void update(HttpServletRequest request, String tableName, String mode) throws Exception {
        Updater updater = new Updater();

        updater.request = request;
        updater.tableName = tableName;
        updater.mode = mode;
        updater.run();
    }

    public static String getFieldName(String fieldName) {
        if (Var.forceUpperCase) {
            if (fieldName.startsWith("#")) {
                return fieldName.substring(1);
            }
            return fieldName.toUpperCase();
        }
        return fieldName;
    }

    /**
     * 获取结果集元数据的字段定义信息。
     *
     * @param meta           结果集元数据。
     * @param dictTableNames 字典表名列表，如果该值不是null，将创建字段对应的键值字段定义。
     *                       键值字段名称为"字段名__V"。
     * @param dictFieldsMap  字典定义中字段与表的对应关系。用于多表重名字段的表名指定。
     * @param keyDefines     字段值转换为键值定义的名称列表。
     * @return 元数据定义。
     * @throws Exception 读取过程发生异常。
     */
    public static JSONArray getFields(ResultSetMetaData meta, String[] dictTableNames, JSONObject dictFieldsMap,
                                      JSONObject keyDefines) throws Exception {
        int j = meta.getColumnCount();
        String[] mapTable = new String[1];
        JSONArray ja = new JSONArray();

        boolean hasDict = dictTableNames != null;

        for (int i = 0; i < j; i++) {
            int k = i + 1;
            JSONObject jo = new JSONObject();
            String name = meta.getColumnLabel(k);
            name = getFieldName(name);
            if (StringUtil.isEmpty(name))
                name = "FIELD" + Integer.toString(k);
            int type = meta.getColumnType(k);
            String category;
            if ((keyDefines != null) && (keyDefines.has(name)))
                category = "string";
            else
                category = getTypeCategory(type);
            String format;
            switch (type) {
                case Types.TIMESTAMP:
                    format = "Y-m-d H:i:s.u";
                    break;
                case Types.DATE:
                    format = "Y-m-d";
                    break;
                case Types.TIME:
                    format = "H:i:s";
                    break;
                default:
                    format = null;
            }
            jo.put("name", name);
            jo.put("type", category);
            if (format != null)
                jo.put("dateFormat", format);
            if (category.equals("string"))
                jo.put("useNull", false);
            ja.put(jo);
            if (hasDict) {
                DictRecord dictRecord = null;
                if (dictFieldsMap != null) {
                    mapTable[0] = dictFieldsMap.optString(name);
                    if (!StringUtil.isEmpty(mapTable[0]))
                        dictRecord = Dictionary.find(mapTable, name);
                }
                if (dictRecord == null)
                    dictRecord = Dictionary.find(dictTableNames, name);
                if ((dictRecord != null) && (dictRecord.keyName != null)) {
                    jo = new JSONObject();
                    jo.put("name", name + "__V");
                    jo.put("type", "string");
                    jo.put("useNull", false);
                    ja.put(jo);
                }
            }
        }
        return ja;
    }

    /**
     * 获取结果集元数据列模型定义。
     *
     * @param meta           结果集元数据。
     * @param dictTableNames 以逗号分隔的字典定义表名列表。
     * @param dictFieldsMap  字典定义中字段与表的对应关系。用于多表重名字段的表名指定。
     * @param keyDefines     字段值转换为键值定义的名称列表。
     * @return 列模型定义。
     * @throws Exception 读取过程发生异常。
     */
    public static String getColumns(ResultSetMetaData meta, String[] dictTableNames, JSONObject dictFieldsMap,
                                    JSONObject keyDefines) throws Exception {
        int j = meta.getColumnCount();

        String[] mapTable = new String[1];
        String keyItems = null;
        StringBuilder buf = new StringBuilder();
        DictRecord fieldDict = null;

        buf.append('[');
        buf.append("{\"xtype\":\"rownumberer\"}");
        for (int i = 0; i < j; i++) {
            boolean addExtracConfig = false;
            int index = i + 1;
            String fieldName = meta.getColumnLabel(index);
            fieldName = getFieldName(fieldName);
            if (StringUtil.isEmpty(fieldName))
                fieldName = "FIELD" + Integer.toString(index);
            fieldDict = null;
            if (dictFieldsMap != null) {
                mapTable[0] = dictFieldsMap.optString(fieldName);
                if (!StringUtil.isEmpty(mapTable[0]))
                    fieldDict = Dictionary.find(mapTable, fieldName);
            }
            if ((fieldDict == null) && (dictTableNames != null))
                fieldDict = Dictionary.find(dictTableNames, fieldName);
            boolean hasFieldDict = fieldDict != null;
            boolean hasKeyName = (hasFieldDict) && (fieldDict.keyName != null);
            int type;
            int len;
            String precision;
            String scale;
            if ((keyDefines != null) && (keyDefines.has(fieldName))) {
                len = 200;
                precision = "200";
                scale = "0";
                type = Types.VARCHAR;
            } else {
                if (hasKeyName) {
                    len = 10;
                } else {
                    len = meta.getPrecision(index);// 代替getColumnDisplaySize
                    if (len <= 0)
                        len = 100;
                }
                precision = Integer.toString(len);
                int scaleNum = meta.getScale(index);
                if (scaleNum < 0)
                    scaleNum = 100;
                scale = Integer.toString(scaleNum);
                type = meta.getColumnType(index);
                if ((type == Types.NVARCHAR || type == Types.VARCHAR) && len > Var.stringAsText)
                    type = Types.CLOB;
            }
            int fieldNameLen = fieldName.length();
            fieldName = StringUtil.quote(fieldName);
            buf.append(',');
            buf.append("{\"dataIndex\":");
            buf.append(fieldName);
            buf.append(",\"text\":");
            buf.append(
                    (hasFieldDict) && (fieldDict.dispText != null) ? StringUtil.quote(fieldDict.dispText) : fieldName);
            String editor = null;
            boolean isDateTime = false;
            boolean hasRenderer = (hasFieldDict) && ((fieldDict.renderer != null) || (hasKeyName));
            String category;
            switch (type) {
                case Types.TIMESTAMP:
                    category = "timestamp";
                    editor = "\"datetimefield\"";
                    isDateTime = true;
                    len = 18;
                    break;
                case Types.DATE:
                    category = "date";
                    editor = "\"datefield\"";
                    isDateTime = true;
                    len = 12;
                    break;
                case Types.TIME:
                    category = "time";
                    editor = "\"timefield\"";
                    isDateTime = true;
                    len = 10;
                    if (!hasRenderer) {
                        buf.append(",\"renderer\":\"Wb.timeRenderer\"");
                    }
                    break;
                case Types.BIGINT:
                case Types.INTEGER:
                case Types.SMALLINT:
                case Types.TINYINT:
                case Types.DECIMAL:
                case Types.DOUBLE:
                case Types.FLOAT:
                case Types.NUMERIC:
                case Types.REAL:
                    category = "number";
                    String dictSize, dictScale;
                    if (!hasKeyName)
                        buf.append(",\"align\":\"right\"");
                    if (hasFieldDict) {
                        if (fieldDict.fieldSize == -1)
                            dictSize = precision;
                        else
                            dictSize = Integer.toString(fieldDict.fieldSize);
                        if (fieldDict.decimalPrecision == -1)
                            dictScale = scale;
                        else
                            dictScale = Integer.toString(fieldDict.decimalPrecision);
                        editor = StringUtil.concat("\"numberfield\",\"decimalPrecision\":", dictScale);
                        if (fieldDict.validator == null)
                            editor = StringUtil.concat(editor, ",\"validator\":\"Wb.numValidator(", dictSize, ",",
                                    dictScale, ")\"");
                    } else {
                        editor = StringUtil.concat("\"numberfield\",\"decimalPrecision\":", scale,
                                ",\"validator\":\"Wb.numValidator(", precision, ",", scale, ")\"");
                    }
                    break;
                case Types.LONGVARCHAR:
                case Types.LONGNVARCHAR:
                case Types.CLOB:
                case Types.NCLOB:
                    category = "text";
                    editor = "\"textarea\",\"height\":120";
                    len = 18;
                    if (!hasRenderer) {
                        buf.append(",\"renderer\":\"Wb.clobRenderer\"");
                    }
                    break;
                case Types.BINARY:
                case Types.VARBINARY:
                case Types.LONGVARBINARY:
                case Types.BLOB:
                    category = "blob";
                    editor = "\"filefield\"";
                    len = 16;// 默认长度兼容英文需要
                    if (!hasRenderer) {
                        buf.append(",\"renderer\":\"Wb.blobRenderer\"");
                    }
                    break;
                case Types.BOOLEAN:
                case Types.BIT:
                    category = "number";
                    if (!hasKeyName)
                        buf.append(",\"align\":\"right\"");
                    editor = "\"numberfield\",\"maxValue\":1,\"minValue\":0";
                    break;
                default:
                    category = "string";
                    editor = "\"textfield\",\"maxLength\":"
                            + (hasFieldDict && fieldDict.fieldSize != -1 ? Integer.toString(fieldDict.fieldSize)
                            : precision);
            }
            if (hasFieldDict) {
                // 日期时间类型字段允许通过设置字段长度来重置使用何种编辑器
                if ((isDateTime) && (fieldDict.fieldSize != -1)) {
                    switch (fieldDict.fieldSize) {
                        case 1:
                            editor = "\"datefield\"";
                            if (fieldDict.dispFormat != null && (fieldDict.dispFormat.toUpperCase().equals("Y-M")
                                    || fieldDict.dispFormat.toUpperCase().equals("YM")))
                                editor = "\"monthfield\"";
                            len = 12;
                            break;
                        case 2:
                            editor = "\"timefield\"";
                            len = 10;
                            break;
                        case 3:
                            category = "timestamp";
                            editor = "\"datetimefield\"";
                            len = 18;
                            break;
                    }
                }
                //				if (fieldDict.isKey) {
                //					buf.append(
                //							",\"html\":\"<span class='wb_glyph' style='position: absolute; top: 15px; right: 2px;bottom: 0; left: 5px;'>&#xf084</span>\"");
                //					buf.append(",\"style\":\"padding: 7px 0px 7px 13px;\"");
                //				}
                if (fieldDict.isLocked)
                    buf.append(",\"locked\":true");
                if (fieldDict.noList) {
                    buf.append(",\"hidden\":true,\"showInMenu\":false");
                }
                if (fieldDict.dispFormat != null) {
                    buf.append(",\"format\":");
                    buf.append(StringUtil.quote(fieldDict.dispFormat));
                }
                if (hasRenderer) {
                    if (fieldDict.renderer != null) {
                        if (fieldDict.renderer.trim().startsWith("{")) {
                            addExtracConfig = true;
                        } else {
                            buf.append(
                                    ",\"renderer\":\"(function(value,metaData,record,rowIndex,colIndex,store,view){");
                            buf.append(StringUtil.text(fieldDict.renderer));
                            buf.append("})\"");
                        }
                    } else
                        buf.append(",\"renderer\":\"Wb.kvRenderer\"");
                }

                if (fieldDict.autoWrap)
                    buf.append(",\"autoWrap\":true");
                buf.append(",\"parent_id\":\"").append(fieldDict.parentId).append("\"");

            }
            buf.append(",\"category\":\"");
            buf.append(category);
            if ((hasFieldDict) && (fieldDict.dispWidth != -1)) {
                if (fieldDict.dispWidth < 10) {
                    // 如果值小于10视为flex
                    buf.append("\",\"flex\":");
                    buf.append(fieldDict.dispWidth);
                } else {
                    buf.append("\",\"width\":");
                    buf.append(fieldDict.dispWidth);
                }
            } else {
                buf.append("\",\"width\":");
                len = Math.max(len, fieldNameLen + 3);
                if (len < 5)
                    len = 5;
                if (len > 18)
                    len = 18;
                buf.append(len * 10);
            }
            if (hasKeyName) {
                keyItems = KVBuffer.getList(fieldDict.keyName);
                buf.append(",\"keyName\":");
                buf.append(StringUtil.quote(fieldDict.keyName));
                buf.append(",\"keyItems\":");
                buf.append(keyItems);
            }
            if (((!hasFieldDict) || (!fieldDict.noEdit)) && (editor != null)) {
                if ((hasFieldDict) && (fieldDict.validator != null) && (fieldDict.validator.trim().startsWith("{"))) {
                    // 如果validator为对象表达式，作为编辑器配置对象处理
                    if ("blob".equals(category))
                        buf.append(",\"blobEditor\":");// 用于对话框编辑模式
                    else
                        buf.append(",\"editor\":");
                    buf.append(fieldDict.validator);
                    buf.append(",\"editable\":true");
                } else {
                    if ("blob".equals(category))
                        buf.append(",\"blobEditor\":{\"xtype\":");// 用于对话框编辑模式
                    else
                        buf.append(",\"editor\":{\"xtype\":");
                    if (hasKeyName) {
                        buf.append("\"combo\",\"keyName\":");
                        buf.append(StringUtil.quote(fieldDict.keyName));
                        buf.append(
                                ",\"displayField\":\"V\",\"valueField\":\"K\",\"forceSelection\":true,\"queryMode\":\"local\",\"store\":{\"fields\":[\"K\",\"V\"],\"sorters\":\"K\",\"data\":");
                        buf.append(keyItems);
                        buf.append('}');
                    } else {
                        buf.append(editor);
                    }
                    if (hasFieldDict) {
                        if ((meta.isNullable(index) == 0) || (fieldDict.noBlank))
                            buf.append(",\"allowBlank\":false,\"required\":true");
                        if (((Var.checkFieldReadOnly) && (meta.isReadOnly(index))) || (fieldDict.readOnly))
                            buf.append(",\"readOnly\":true");
                        if (fieldDict.defaultValue != null) {
                            if (fieldDict.dataType == "number")
                                buf.append(",\"value\":").append(fieldDict.defaultValue);
                            else
                                buf.append(",\"value\":\"").append(fieldDict.defaultValue).append("\"");
                        }
                        if (fieldDict.validator != null) {
                            buf.append(",\"validator\":\"(function(value){");
                            buf.append(StringUtil.text(fieldDict.validator));
                            buf.append("})\"");
                        }
                    } else {
                        if (meta.isNullable(index) == 0)
                            buf.append(",\"allowBlank\":false,\"required\":true");
                        if ((Var.checkFieldReadOnly) && (meta.isReadOnly(index)))
                            buf.append(",\"readOnly\":true");
                    }
                    buf.append("},\"editable\":true");
                }
            }
            buf.append(",\"metaType\":\"");
            buf.append(meta.getColumnTypeName(index));
            buf.append("\",\"metaRequired\":");
            buf.append(meta.isNullable(index) == ResultSetMetaData.columnNoNulls ? "true" : "false");
            buf.append(",\"metaSize\":");
            buf.append(precision);
            buf.append(",\"metaScale\":");
            buf.append(scale);
            if (addExtracConfig) {
                buf.append(',');
                String colExtraItems = fieldDict.renderer.trim();
                colExtraItems = colExtraItems.substring(1, colExtraItems.length() - 1);
                buf.append(colExtraItems);
            }
            buf.append('}');
        }
        buf.append(']');
        return buf.toString();
    }

    public static String getDbName(Connection conn) {
        try {
            DatabaseMetaData meta = conn.getMetaData();
            return meta.getDatabaseProductName();
        } catch (Throwable e) {
        }
        return null;
    }

    /**
     * 输出结果集首行记录首个BLOB字段至客户端。如果记录存在第2个字段，作为输出的文件名，
     * 如果记录存在第3个字段，作为输出内容的长度。
     *
     * @param resultSet   结果集。
     * @param request     请求对象。
     * @param response    响应对象
     * @param contentType 内容类型，可为download,stream,image或其他用户自定义头信息。
     *                    如果指定为image类型但第2个字段未指定图片格式，默认为image/jpg。
     *                    该类型可以由用户自定义，如image/png。
     * @throws Exception 读过数据库或输出过程发生异常。
     */
    @SuppressWarnings("deprecation")
    public static void outputBlob(ResultSet resultSet, HttpServletRequest request, HttpServletResponse response,
                                  String contentType) throws Exception {
        InputStream inputStream = null;
        try {
            ResultSetMetaData meta = resultSet.getMetaData();
            int rowCount = meta.getColumnCount();
            String name = getFieldName(meta.getColumnLabel(1));
            String size = null;

            if (StringUtil.isEmpty(name))
                name = "blob";
            response.reset();
            if (resultSet.next())
                switch (rowCount) {
                    case 1:
                        inputStream = resultSet.getBinaryStream(1);
                        break;
                    case 2:
                        name = resultSet.getString(2);
                        inputStream = resultSet.getBinaryStream(1);
                        break;
                    case 3:
                        name = resultSet.getString(2);
                        size = resultSet.getString(3);
                        inputStream = resultSet.getBinaryStream(1);
                    default:
                        break;
                }
            else
                throw new Exception("Empty ResultSet.");
            OutputStream outputStream = response.getOutputStream();
            if ("download".equals(contentType))
                contentType = "application/force-download";
            else if ("stream".equals(contentType))
                contentType = "application/octet-stream";
            else if ("image".equals(contentType)) {
                if (inputStream == null) {
                    File nullGif = new File(Base.path, "wb/images/null.png");
                    inputStream = new FileInputStream(nullGif);
                    size = Long.toString(nullGif.length());
                    contentType = "image/gif";
                } else {
                    String extName = FileUtil.getFileExt(name);
                    if (extName.isEmpty())
                        contentType = "image/jpg";
                    else
                        contentType = "image/" + extName;
                }
            }
            response.setHeader("content-type", contentType);
            response.setHeader("content-disposition", "attachment;" + WebUtil.encodeFilename(request, name));
            if (size != null)
                response.setHeader("content-length", size);
            if (inputStream != null)
                IOUtils.copy(inputStream, outputStream);
            response.flushBuffer();
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }

    /**
     * 获取指定表的自动化insert, update, delete和select SQL语句。这些语句为带参数的SQL语句，
     * 通常用于自动化作业，比如使用updater控件更新数据。
     *
     * @param jndi        数据库连接jndi。
     * @param tableName   表名。
     * @param ignoreBlob  是否忽略blob字段。
     * @param scriptType  0原生，1参数，2替换。
     * @param request     请求对象，用于判断是否对指定上传的blob字段进行处理。
     * @param fields      生成where语句前的字段列表。如果为null生成所有字段。
     *                    否则只使用存在于该列表中的字段。
     * @param whereFields 生成where语句的字段列表。如果为null使用所有有效字段，
     *                    否则只使用存在于该列表中的字段。
     * @param fieldsMap   字段映射对象，用于把键指定字段映射为值指定字段。
     * @return SQL语句数组，依次为insert, update, delete和select语句。
     * @throws Exception 生成SQL语句发生异常。
     */
    public static String[] buildSQLs(String jndi, String tableName, boolean ignoreBlob, int scriptType,
                                     HttpServletRequest request, JSONObject fields, JSONObject whereFields, JSONObject fieldsMap)
            throws Exception {
        String[] sqls = new String[4];
        Connection conn = null;
        PreparedStatement st = null;
        ResultSet rs = null;

        StringBuilder selectFields = new StringBuilder();
        StringBuilder insertFields = new StringBuilder();
        StringBuilder insertParams = new StringBuilder();
        StringBuilder condition = new StringBuilder();
        StringBuilder updateParams = new StringBuilder();

        boolean isFirstSelect = true;
        boolean isFirstUpdate = true;
        boolean isFirstCondi = true;
        boolean hasRequest = request != null;
        boolean useDouble = Var.getBool("sys.db.useDouble");
        boolean whereUseDate = Var.getBool("sys.db.whereUseDate");
        boolean whereUseFloat = Var.getBool("sys.db.whereUseFloat");
        int j;
        int i;
        ResultSetMetaData meta;
        try {
            if (request == null)
                conn = getConnection(jndi);
            else
                conn = getConnection(request, jndi);
            st = conn.prepareStatement("select * from " + tableName + " where 1=0");
            rs = st.executeQuery();
            meta = rs.getMetaData();
            j = meta.getColumnCount() + 1;
            for (i = 1; i < j; i++) {
                int type = meta.getColumnType(i);
                String typeName = getTypeName(type);
                int precision = meta.getPrecision(i);
                if (precision <= 0)
                    precision = 100;
                boolean isText = (isTextField(type)) || (precision > 10000);
                boolean isBlob = isBlobField(type);
                boolean isTime = type == Types.TIME || type == Types.TIMESTAMP || type == Types.DATE;
                int scale = meta.getScale(i);
                if (scale < 0)
                    scale = 100;
                boolean isFloat = (maybeFloatField(type)) && (scale > 0);
                if ((isFloat) && (useDouble)) {
                    type = Types.DOUBLE;
                    typeName = "double";
                }
                boolean required = meta.isNullable(i) == ResultSetMetaData.columnNoNulls;
                boolean readOnly = (Var.checkFieldReadOnly) && (meta.isReadOnly(i));
                String fieldName = meta.getColumnLabel(i);
                fieldName = getFieldName(fieldName);
                String fieldValueName = fieldName;
                fieldName = StringUtil.quoteIf(fieldName);
                if (fieldsMap != null) {
                    String mapName = fieldsMap.optString(fieldValueName, null);
                    if (mapName != null) {
                        fieldValueName = mapName;
                    }
                }
                // $fieldName!=1表示忽略对blob空字段的处理，通常在修改记录时未更改blob即忽略处理。
                if ((!isBlob) || (!hasRequest) || (!StringUtil.isEmpty(WebUtil.fetch(request, fieldValueName)))
                        || ("1".equals(WebUtil.fetch(request, "$" + fieldValueName)))) {
                    if (((!ignoreBlob) || (!isBlob)) && ((fields == null) || (fields.has(fieldValueName))
                            || (fields.has("$" + fieldValueName)))) {
                        if (isFirstSelect)
                            isFirstSelect = false;
                        else {
                            selectFields.append(',');
                        }
                        selectFields.append(fieldName);
                        if (!readOnly) {
                            if (isFirstUpdate) {
                                isFirstUpdate = false;
                            } else {
                                insertFields.append(',');
                                insertParams.append(',');
                                updateParams.append(',');
                            }
                            String param;
                            switch (scriptType) {
                                case 1:
                                    if (typeName == null)
                                        param = StringUtil.concat("{?", fieldValueName, "?}");
                                    else
                                        param = StringUtil.concat("{?", typeName, ".", fieldValueName, "?}");
                                    break;
                                case 2:
                                    param = StringUtil.concat("{#", fieldValueName, "#}");
                                    break;
                                default:
                                    param = fieldValueName;
                            }
                            insertFields.append(fieldName);
                            insertParams.append(param);
                            updateParams.append(fieldName);
                            updateParams.append('=');
                            updateParams.append(param);
                        }
                    }
                    if ((!isText) && (!isBlob) && ((!isFloat) || (whereUseFloat)) && ((!isTime) || (whereUseDate))
                            && ((whereFields == null) || (whereFields.has(fieldValueName))
                            || (whereFields.has("#" + fieldValueName)))) {
                        if (isFirstCondi)
                            isFirstCondi = false;
                        else {
                            condition.append(" and ");
                        }
                        condition.append(getCondition(fieldName, fieldValueName, isStringField(type), typeName,
                                required, scriptType));
                    }
                }
            }
            sqls[0] = StringUtil.concat(new String[]{"insert into ", tableName, " (", insertFields.toString(),
                    ") values (", insertParams.toString(), ")"});
            sqls[1] = StringUtil.concat(new String[]{"update ", tableName, " set ", updateParams.toString(),
                    " where ", condition.toString()});
            sqls[2] = StringUtil.concat(new String[]{"delete from ", tableName, " where ", condition.toString()});
            sqls[3] = StringUtil.concat(new String[]{"select ", selectFields.toString(), " from ", tableName,
                    " where ", condition.toString()});
        } finally {
            close(rs);
            close(st);
            if (request == null)
                close(conn); // request!=null共享连接且自动释放
        }
        return sqls;
    }

    /**
     * 获取条件表达式SQL语句片段。
     *
     * @param fieldName  字段名称。
     * @param typeName   字段类型。
     * @param required   字段是否为必须。
     * @param scriptType 生成的脚本类型。
     * @return 条件表达式。
     */
    private static String getCondition(String fieldName, String fieldValueName, boolean isStringField, String typeName,
                                       boolean required, int scriptType) {
        StringBuilder buf = new StringBuilder();
        switch (scriptType) {
            case 1:
                if (isStringField) {
                    // 字符型处理的区别在于某些数据库允许输入空串
                    buf.append("({?#");
                    buf.append(fieldValueName);
                    buf.append("?} is null and (");
                    buf.append(fieldName);
                    buf.append(" is null or ");
                    buf.append(fieldName);
                    buf.append("='') or ");
                    buf.append(fieldName);
                    buf.append("={?");
                    if (typeName == null) {
                        buf.append('#');
                    } else {
                        buf.append(typeName);
                        buf.append(".#");
                    }
                    buf.append(fieldValueName);
                    buf.append("?})");
                } else {
                    if (!required) {
                        buf.append("({?#");
                        buf.append(fieldValueName);
                        buf.append("?} is null and ");
                        buf.append(fieldName);
                        buf.append(" is null or ");
                    }
                    buf.append(fieldName);
                    buf.append("={?");
                    if (typeName == null) {
                        buf.append('#');
                    } else {
                        buf.append(typeName);
                        buf.append(".#");
                    }
                    buf.append(fieldValueName);
                    if (required)
                        buf.append("?}");
                    else
                        buf.append("?})");
                }
                return buf.toString();
            case 2:
                if (!required) {
                    buf.append("({##");
                    buf.append(fieldValueName);
                    buf.append("#} is null and ");
                    buf.append(fieldName);
                    buf.append(" is null or ");
                }
                buf.append(fieldName);
                buf.append("={##");
                buf.append(fieldValueName);
                if (required)
                    buf.append("#}");
                else
                    buf.append("#})");
                return buf.toString();
        }
        if (!required) {
            buf.append("(#");
            buf.append(fieldValueName);
            buf.append(" is null and ");
            buf.append(fieldName);
            buf.append(" is null or ");
        }
        buf.append(fieldName);
        buf.append("=#");
        buf.append(fieldValueName);
        if (!required) buf.append(')');
        return buf.toString();
    }

    /**
     * 设置sql.orderBy和sql.orderFields变量，以方便使用前端参数进行排序。仅适用于type为array。
     *
     * @throws Exception 设置过程发生异常。
     */
    public static String getOrderSql(String sort, String orderFields) {
        JSONArray ja = new JSONArray(sort);
        int j = ja.length();
        if (j > 0) {
            StringBuilder exp = new StringBuilder();
            String defaultPrefix;
            JSONObject orderJo;
            if (StringUtil.isEmpty(orderFields)) {
                orderJo = null;
                defaultPrefix = null;
            } else {
                orderJo = new JSONObject(orderFields);
                defaultPrefix = orderJo.optString("default", null);
            }
            for (int i = 0; i < j; i++) {
                JSONObject jo = ja.getJSONObject(i);
                if (i > 0)
                    exp.append(',');
                String property = jo.getString("property");
                // 检查名称合法性，防止被SQL注入
                if (!StringUtil.checkName(property)) {
                    throw new IllegalArgumentException("Invalid name \"" + property + "\".");
                }
                if (orderJo != null) {
                    if (orderJo.has(property)) {
                        String prefix = orderJo.optString(property);
                        if (!prefix.isEmpty()) {
                            exp.append(prefix);
                            exp.append('.');
                        }
                    } else if (defaultPrefix != null) {
                        exp.append(defaultPrefix);
                        exp.append('.');
                    }
                }
                exp.append(property);
                if (StringUtil.isSame(jo.optString("direction"), "desc"))
                    exp.append(" desc");
            }
            return exp.toString();
        }
        return null;
    }

    public static String getOrderBySql(HttpServletRequest request, String orderFields) {
        String sort = request.getParameter("sort");
        if (StringUtil.isEmpty(sort))
            return "";
        String orderExp = getOrderSql(sort, orderFields);
        return " order by " + orderExp;
    }

    public static String getOrderSql(HttpServletRequest request, String orderFields) {
        String sort = request.getParameter("sort");
        if (StringUtil.isEmpty(sort))
            return "";
        return "," + getOrderSql(sort, orderFields);
    }

    /**
     * 读取结果集数据，并把数据放到JSONArray对象中。如果结果集为空将返回空的JSONArray对象。
     * 读取完成后，结果集不会被关闭。
     *
     * @param rs 需要读取数据的结果集对象。
     * @return 生成的JSONArray对象。
     */
    public static JSONArray getData(ResultSet rs) throws Exception {
        return getData(rs, null, null);
    }

    /**
     * 读取结果集数据，并把数据放到JSONArray对象中。如果结果集为空将返回空的JSONArray对象。
     * 读取完成后，结果集不会被关闭。
     *
     * @param rs             需要读取数据的结果集对象。
     * @param keys           字段名称和键值的对应关系，语法field1=key1,field2=key2...。
     * @param dictTableNames 映射的字典表名，多个表名以逗号分隔。语法table1,table2...。
     * @return 生成的JSONArray对象。
     */
    public static JSONArray getData(ResultSet rs, String keys, String dictTableNames) throws Exception {
        JSONArray result = new JSONArray();
        JSONObject record;
        HashMap<String, String> keyMap;
        int i, j, types[];
        boolean hasKeys[];
        ResultSetMetaData meta;
        Object value, kdMaps[];
        String items[], fields[];

        //生成键值Map
        if (StringUtil.isEmpty(dictTableNames))
            keyMap = new HashMap<String, String>();
        else
            keyMap = (HashMap<String, String>) Dictionary.getKeyFields(StringUtil.split(dictTableNames, ','));
        if (!StringUtil.isEmpty(keys)) {
            items = StringUtil.split(keys, ',');
            for (String item : items) {
                keyMap.put(StringUtil.getNamePart(item), StringUtil.getValuePart(item));
            }
        }
        meta = rs.getMetaData();
        j = meta.getColumnCount();
        fields = new String[j];
        types = new int[j];
        hasKeys = new boolean[j];
        kdMaps = new Object[j];
        for (i = 0; i < j; i++) {
            fields[i] = DbUtil.getFieldName(meta.getColumnLabel(i + 1));
            types[i] = meta.getColumnType(i + 1);
            hasKeys[i] = keyMap.containsKey(fields[i]);
            if (hasKeys[i])
                kdMaps[i] = KVBuffer.buffer.get(keyMap.get(fields[i]));
            else
                kdMaps[i] = null;
        }
        while (rs.next()) {
            record = new JSONObject();
            for (i = 0; i < j; i++) {
                value = DbUtil.getObject(rs, i + 1, types[i]);
                if (hasKeys[i])
                    value = KVBuffer.getValue(((ConcurrentHashMap<?, ?>) kdMaps[i]), value);
                if (value == null)
                    value = JSONObject.NULL;
                record.put(fields[i], value);
            }
            result.put(record);
        }
        return result;
    }

    /**
     * 读取结果集数据，并把数据放到JSONObject对象中。如果结果集为空将返回空的JSONObject对象。
     *
     * @param rs 需要读取数据的结果集对象。
     * @throws Exception
     */
    public static JSONObject getDataObject(ResultSet rs) throws Exception {
        return getDataObject(rs, true);
    }

    /**
     * 读取结果集数据，并把数据放到JSONObject对象中。如果结果集为空将返回空的JSONObject对象。
     *
     * @param rs     需要读取数据的结果集对象。
     * @param toNext 是否读取结果集，主要用在结果集循环中，如果循环中，需要传入false，防止跳过
     * @return 生成的JSONObject对象。
     * @throws Exception 读取数据时发生的异常。
     */
    public static JSONObject getDataObject(ResultSet rs, boolean toNext) throws Exception {
        JSONObject _obj = new JSONObject();
        boolean flag = true;
        if (toNext)
            flag = rs.next();
        if (flag) {
            int _i, _j, _type;
            ResultSetMetaData _meta = rs.getMetaData();
            String _key;
            Object _value;
            _j = _meta.getColumnCount();
            for (_i = 0; _i < _j; _i++) {
                _type = _meta.getColumnType(_i + 1);
                _key = DbUtil.getFieldName(_meta.getColumnLabel(_i + 1));
                _value = DbUtil.getObject(rs, _i + 1, _type);
                if (_value == null)
                    _value = JSONObject.NULL;
                _obj.put(_key, _value);
            }
        }
        return _obj;
    }


    /**
     * 读取结果集数据，并把数据放到JSONObject对象中。如果结果集为空将返回空的JSONObject对象。
     *
     * @param rs 需要读取数据的结果集对象。
     * @return 生成的JSONObject对象。
     * @throws Exception 读取数据时发生的异常。
     */
    public static JSONArray getArray(ResultSet rs) throws Exception {
        ResultSetMetaData meta = rs.getMetaData();
        int type = meta.getColumnType(1);
        JSONArray result = new JSONArray();

        while (rs.next()) {
            result.put(getObject(rs, 1, type));
        }
        return result;
    }

    /**
     * 执行SQL语句，并把执行结果封装为JSONArray输出
     * 注：此方法不会以KV形式存储，只是纯数组形式输出结果集
     *
     * @param sql SQL语句
     * @return JSONArray
     */
    public static JSONArray query(String jndi, String sql) {
        Connection conn = null;
        Statement st = null;
        ResultSet rs = null;
        JSONArray array = new JSONArray();
        try {
            conn = DbUtil.getConnection(jndi);
            st = conn.createStatement();
            rs = st.executeQuery(sql);
            array = getArray(rs);
        } catch (Exception e) {
            LogUtil.error("读取结果集异常：" + e);
            e.printStackTrace();
        } finally {
            close(rs);
            close(st);
            close(conn);
        }
        return array;
    }

    /**
     * 执行SQL语句，并把执行结果封装为JSONArray输出
     * 注：此方法不会以KV形式存储，只是纯数组形式输出结果集
     *
     * @param sql SQL语句
     * @return JSONArray
     */
    public static JSONArray query(String sql) {
        return query("", sql);
    }

    /**
     * 读取结果集数据，并把数据放到JSONArray对象中。如果结果集为空将返回空的JSONArray对象。
     *
     * @param sql 需要读取数据的SQL语句。
     * @return 生成的JSONArray对象。
     */
    public static JSONArray queryAll(String jndi, String sql) {
        Connection conn = null;
        Statement st = null;
        ResultSet rs = null;
        JSONArray array = new JSONArray();
        try {
            conn = DbUtil.getConnection(jndi);
            st = conn.createStatement();
            rs = st.executeQuery(sql);
            array = getData(rs);
        } catch (Exception e) {
            LogUtil.error("读取结果集异常：" + e);
            return new JSONArray();
        } finally {
            close(rs);
            close(st);
            close(conn);
        }
        return array;
    }

    /**
     * 读取结果集数据，并把数据放到JSONArray对象中。如果结果集为空将返回空的JSONArray对象。
     *
     * @param sql 需要读取数据的SQL语句。
     * @return 生成的JSONArray对象。
     */
    public static JSONArray queryAll(String sql) {
        return queryAll("", sql);
    }

    /**
     * 执行SQL更新语句，并返回受影响的行数
     *
     * @param sql 需要执行的SQL语句
     * @return 受影响的行数，执行失败返回-1
     */
    public static int execute(String sql) {
        Connection conn = null;
        PreparedStatement ps = null;
        try {
            conn = DbUtil.getConnection();
            ps = conn.prepareStatement(sql);
            return ps.executeUpdate();
        } catch (Exception e) {
            LogUtil.error("执行SQL语句[" + sql + "]异常：" + e);
            return -1;
        } finally {
            close(ps);
            close(conn);
        }
    }
}