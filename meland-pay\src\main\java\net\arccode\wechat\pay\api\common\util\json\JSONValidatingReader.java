package net.arccode.wechat.pay.api.common.util.json;

public class <PERSON><PERSON><PERSON>ValidatingReader extends <PERSON><PERSON><PERSON>eader {
    public static final Object INVALID = new Object();
    private JSONValidator validator;
    
    public JSONValidatingReader(JSONValidator validator) {
        this.validator = validator;
    }
    
    public JSONValidatingReader(JSONErrorListener listener) {
        this(new <PERSON>SO<PERSON>Validator(listener));
    }
    
    public JSONValidatingReader() {
        this(new StdoutStreamErrorListener());
    }

    public Object read(String string) {
        if (!validator.validate(string)) return INVALID;
        return super.read(string);
    }
}
