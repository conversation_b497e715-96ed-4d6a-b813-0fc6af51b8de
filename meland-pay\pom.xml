<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>lon<PERSON>sky</groupId>
		<artifactId>meland</artifactId>
		<version>1.0.0</version>
	</parent>
	<artifactId>meland-pay</artifactId>
	<version>1.0.0</version>
	<packaging>jar</packaging>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>lonlysky</groupId>
				<artifactId>meland-dependencies</artifactId>
				<version>1.0.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<!-- 依赖核心模块 -->
		<dependency>
			<groupId>lon<PERSON><PERSON></groupId>
			<artifactId>meland-core</artifactId>
			<version>1.0.0</version>
		</dependency>
		
		<!-- 支付宝SDK -->
		<dependency>
			<groupId>com.alipay.sdk</groupId>
			<artifactId>alipay-sdk-java-v3</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.json</groupId>
					<artifactId>json</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<!-- JSON处理 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		
		<!-- JSP标签库 -->
		<dependency>
			<groupId>org.apache.taglibs</groupId>
			<artifactId>taglibs-standard-impl</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.taglibs</groupId>
			<artifactId>taglibs-standard-spec</artifactId>
		</dependency>
		
		<!-- Servlet -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		
		<!-- 加密工具 -->
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
		</dependency>
    </dependencies>

	<!-- 构建相关参数 -->
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.1.0</version>
			</plugin>
		</plugins>
	</build>
</project>
