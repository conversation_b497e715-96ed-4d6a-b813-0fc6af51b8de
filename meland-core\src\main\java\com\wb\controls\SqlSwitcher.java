package com.wb.controls;

import com.wb.common.Var;

public class SqlSwitcher extends Control {
	public void create() throws Exception {
		String varName = gs("varName");

		// 数据库类型由varName属性指定，如果为空使用defaultType变量指定类型
		String sql = gs(Var.getString(varName.isEmpty() ? "sys.db.defaultType" : varName));
		boolean emptySql = sql.isEmpty();
		this.request.setAttribute("sql.autoPage", Boolean.valueOf(emptySql));
		if (emptySql)
			sql = gs("default");
		this.request.setAttribute(gs("itemId"), sql);
	}
}