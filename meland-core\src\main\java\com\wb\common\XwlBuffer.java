package com.wb.common;

import com.alibaba.fastjson.JSON;
import com.wb.exception.NotFoundException;
import com.wb.interact.Controls;
import com.wb.util.*;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 模块缓存器。
 */
public class XwlBuffer {

    /**
     * 模块文件转换成JSONObject后缓存HashMap。
     */
    public static ConcurrentHashMap<String, Object[]> buffer;

    /**
     * 本地LRU内存缓存，用于快速访问常用模块
     */
    private static final int LOCAL_CACHE_CAPACITY = 200;
    public static final Map<String, JSONObject> localCache = Collections
            .synchronizedMap(new LinkedHashMap<String, JSONObject>(LOCAL_CACHE_CAPACITY, 0.75f, true) {
                @Override
                protected boolean removeEldestEntry(Map.Entry<String, JSONObject> eldest) {
                    return size() > LOCAL_CACHE_CAPACITY;
                }
            });

    /**
     * 模块权限缓存
     */
    public static ConcurrentHashMap<String, List<String>> roleBuffer;
    /**
     * 权限缓存
     */
    public static ConcurrentHashMap<String, String> roleIDBuffer;

    public static JSONObject get(String path) throws Exception {
        return get(path, false);
    }

    public static JSONObject get(File file) throws Exception {
        return get(file, false);
    }

    public static JSONObject get(File file, boolean silent) throws Exception {
        return get(FileUtil.getModulePath(file), silent);
    }

    /**
     * 获取指定路径的模块文件内容的JSON对象。
     *
     * @param path   请求的文件相对路径。
     * @param silent 如果值为true且文件不存在返回null，否则将抛出异常。
     * @return 模块内容JSON对象或null。
     * @throws IOException 如果读取文件发生错误将抛出异常。
     */
    public static JSONObject get(String path, boolean silent) throws Exception {
        if (path == null) {
            if (silent)
                return null;
            throw new NullPointerException("Module path is not specified.");
        }

        String pathKey = path.toLowerCase();

        // 1. 首先从本地内存缓存中查找
        JSONObject cachedModule = localCache.get(pathKey);
        if (cachedModule != null) {
            return cachedModule;
        }

        // 2. 其次从全局内存缓存中查找
        long lastModified;
        File file;

        if (Var.uncheckModified) {
            file = null;
            lastModified = -1;
        } else {
            file = new File(Base.modulePath, path);
            lastModified = file.lastModified();
        }

        Object[] obj = (Object[]) buffer.get(pathKey);
        // 检查缓存对象是否有效且格式正确（包含数据和时间戳两个元素）
        // 并且满足以下条件之一：
        // 1. 系统配置为不检查文件修改时间（Var.uncheckModified为true）
        // 2. 文件的最后修改时间与缓存中记录的时间戳一致，表示文件未被修改
        if ((obj != null && obj.length == 2)
                && ((Var.uncheckModified) || (lastModified == ((Long) obj[1]).longValue()))) {
            // 从全局内存中获取到了，转换并加入本地缓存
            JSONObject resultModule = JsonUtil.getObject(obj[0].toString());
            localCache.put(pathKey, resultModule);
            return resultModule;
        }

        if (Var.uncheckModified) {
            file = new File(Base.modulePath, path);
            lastModified = file.lastModified();
        }

        // 3. 最后从文件中加载
        if (lastModified == 0L) {
            if (silent)
                return null;
            throw new NotFoundException("找不到模块 [\"" + path + "\"] .");
        }

        JSONObject root;
        try {
            root = JsonUtil.readObject(file);
        } catch (Exception e) {
            throw new RuntimeException(
                    "Module \"" + FileUtil.getRelativePath(Base.modulePath, file) + "\" is invalid.");
        }
        if (!root.has("children"))
            throw new NotFoundException("Module \"" + FileUtil.getRelativePath(Base.modulePath, file) + "\" is empty.");

        JSONObject moduleNode = root.getJSONArray("children").getJSONObject(0).getJSONObject("configs");
        JSONObject roleObj = new JSONObject();

        if (Var.getBool("sys.app.useDbRole")) {
            // 查询数据库中的权限
            List<String> roleList = roleBuffer.get(path);
            if (null == roleList) {
                roleList = new ArrayList<>();
            }
            roleObj.put("admin", 1);
            for (String s : roleList) {
                roleObj.put(s, 1);
            }
            root.put("roles", roleObj);
        } else {
            if (root.get("roles") == null) {
                roleObj.put("admin", 1);
                root.put("roles", roleObj);
            } else {
                root.getJSONObject("roles").put("admin", 1);
            }
        }

        root.put("loginRequired", !"false".equals(moduleNode.opt("loginRequired")));
        root.put("internalCall", "true".equals(moduleNode.opt("internalCall")));
        boolean[] libTypes = optimize(root, true, SysUtil.getId() + ".");
        autoSetConfig(moduleNode, libTypes);

        if (moduleNode.optString("loadJS").contains("touch"))
            root.put("hasTouch", true);

        // 优化JSON处理：避免不必要的toString再parse
        obj = new Object[2];
        // 直接使用JSON对象
        String jsonCache = root.toString();
        obj[0] = JSON.parseObject(jsonCache);
        obj[1] = Long.valueOf(lastModified);
        // 先缓存到全局内存
        buffer.put(pathKey, obj);
        // 同时添加到本地缓存
        localCache.put(pathKey, root);
        return root;
    }
    
    /**
     * 删除指定文件的缓存
     * 
     * @param file 需要删除缓存的文件
     * @return 如果缓存被成功删除返回true，否则返回false
     */
    public static boolean removeCache(File file) {
        if (file == null) {
            return false;
        }
        String path = FileUtil.getModulePath(file);
        if (path == null) {
            return false;
        }
        String pathKey = path.toLowerCase();
        // 从本地LRU缓存中删除
        localCache.remove(pathKey);
        // 从全局缓存中删除
        return buffer.remove(pathKey) != null;
    }

    /**
     * 根据使用的控件自动设置模块模块项。
     *
     * @param moduleNode 模块配置项节点。
     * @param libTypes   加载的控件库列表。
     */
    private static void autoSetConfig(JSONObject moduleNode, boolean[] libTypes) {
        String loadJS = (String) moduleNode.opt("loadJS");

        if (loadJS == null) {
            ArrayList<String> libs = new ArrayList<String>();
            // 0项为未知控件
            if (libTypes[1])
                libs.add("ext");
            if (libTypes[2])
                libs.add("touch");
            if (libTypes[3])
                libs.add("bootstrap");
            moduleNode.put("loadJS", StringUtil.join(libs, "+"));
        }
    }

    /**
     * 删除指定文件或目录的模块缓存。
     *
     * @param path 文件或目录的相对路径。
     */
    public static void clear(String path) {
        Set<Entry<String, Object[]>> es = buffer.entrySet();
        String key, modulePath, delPath;
        Object[] value;

        delPath = StringUtil.concat(path, "/").toLowerCase();
        for (Entry<String, Object[]> e : es) {
            key = e.getKey();
            modulePath = StringUtil.concat(key, "/");
            if (modulePath.startsWith(delPath)) {
                value = e.getValue();
                // 根据模块的id号清除ServerScript缓存
                ScriptBuffer.remove((JsonUtil.getObject(value[0])).getJSONArray("children").getJSONObject(0)
                        .getJSONObject("configs").getString("id"));

                // 同时从本地缓存中删除
                localCache.remove(key);

                String lockKey = "DEL_XWL:" + key;
                try {
                    if (Base.map.tryLock(lockKey, 1000, 10000)) {
                        // 先移除redis缓存
                        buffer.remove(key);
                        // 同步通知其他服务器处理
                        com.alibaba.fastjson.JSONObject chatParams = new com.alibaba.fastjson.JSONObject();
                        chatParams.put("type", "del");
                        chatParams.put("path", key);
                        chatParams.put("server", SysUtil.getServerId());
                        Base.map.publish("chat_xwl", chatParams);
                    }
                } finally {
                    Base.map.unLock(lockKey);
                }
            }
        }
    }

    /**
     * 加载和初始化。
     */
    public static synchronized void load() {
        buffer = new ConcurrentHashMap<String, Object[]>();
        // 清空本地缓存
        localCache.clear();
    }

    public static synchronized void loadRoles() {
        try {
            roleBuffer = new ConcurrentHashMap<>();
            JSONArray roleArr = DbUtil.queryAll("select role_id,module from wb_role_module order by module");
            // 将数据库中的权限缓存起来
            for (int i = 0; i < roleArr.length(); i++) {
                JSONObject role = roleArr.getJSONObject(i);
                String key = role.getString("module");
                if (roleBuffer.containsKey(key)) {
                    roleBuffer.get(key).add(role.getString("role_id"));
                } else {
                    List<String> roleList = new ArrayList<>();
                    roleList.add(role.getString("role_id"));
                    roleBuffer.put(key, roleList);
                }
            }
            loadRoleID();
        } catch (Exception ex) {
            LogUtil.error("初始化异常：" + ex.getMessage());
        }
    }

    public static synchronized void loadRoleID() {
        // 缓存角色信息
        roleIDBuffer = new ConcurrentHashMap<>();
        JSONArray roleArr = DbUtil.queryAll("select role_id,role_name from wb_role");
        for (int i = 0; i < roleArr.length(); i++) {
            JSONObject role = roleArr.getJSONObject(i);
            roleIDBuffer.put(role.getString("role_id"), role.getString("role_name"));
        }
        // 同步通知其他服务器处理
        com.alibaba.fastjson.JSONObject chatParams = new com.alibaba.fastjson.JSONObject();
        chatParams.put("type", "loadRoleID");
        chatParams.put("server", SysUtil.getServerId());
        Base.map.publish("chat_role", chatParams);
    }

    /**
     * 优化及设置模块。
     *
     * @param data       模块数据对象。
     * @param parentRoot 父节点是否是根控件。
     * @param moduleId   用于标识模块节点的id。
     * @return 加载的库列表。0 未知， 1 Ext, 2 Touch, 3 BS。
     * @throws IOException 访问文件发生异常。
     */
    private static boolean[] optimize(JSONObject data, boolean parentRoot, String moduleId) throws IOException {
        JSONArray ja = data.getJSONArray("children");
        int j = ja.length();
        boolean[] libTypes = new boolean[4];

        int l = libTypes.length;
        if (j == 0) {
            data.remove("children");
        } else {
            String parentType = (String) data.opt("type");
            for (int i = 0; i < j; i++) {
                JSONObject jo = ja.getJSONObject(i);
                String type = (String) jo.opt("type");
                JSONObject configs = (JSONObject) jo.opt("configs");
                JSONObject meta = Controls.get(type);
                JSONObject general = (JSONObject) meta.opt("general");
                JSONObject tag = (JSONObject) general.opt("tag");
                if (tag != null) {
                    Integer lib = (Integer) tag.opt("lib");
                    if (lib == null)
                        lib = 0;
                    else
                        lib = (Integer) lib;
                    libTypes[lib] = true;// 标识使用指定库的控件，便于系统自动选择加载
                }
                JSONObject autoNames = (JSONObject) general.opt("autoNames");
                Object isConfig = configs.opt("isConfig");
                boolean asConfig;
                if ((!parentRoot) && (autoNames != null) && (isConfig == null)
                        && ((autoNames.has(parentType)) || (autoNames.has("any"))))
                    asConfig = true;
                else
                    asConfig = "true".equals(isConfig);
                // 如果指定控件为配置项，移动控件到配置项列表属性__configs中
                if (asConfig) {
                    JSONObject configItems = (JSONObject) data.opt("__configs");
                    JSONArray children;
                    if (configItems == null) {
                        configItems = new JSONObject();
                        children = new JSONArray();
                        configItems.put("children", children);
                        data.put("__configs", configItems);
                    } else {
                        children = (JSONArray) configItems.opt("children");
                    }
                    children.put(jo);
                    ja.remove(i);
                    i--;
                    j--;
                }
                if ("module".equals(type))
                    configs.put("id", moduleId);
                else if ("serverscript".equals(type)) {
                    // id用于ServerScript缓存，id前缀必须保留moduleId，用于清除
                    configs.put("id", StringUtil.concat(moduleId, SysUtil.getId()));
                }
                jo.remove("expanded");
                boolean[] subLibTypes = optimize(jo, Boolean.TRUE.equals(general.opt("root")), moduleId);
                for (int k = 0; k < l; k++) {
                    if (subLibTypes[k])
                        libTypes[k] = true;
                }
            }
            // 如果移动配置项节点后为空，删除children
            if (j == 0)
                data.remove("children");
        }
        return libTypes;
    }

    /**
     * 判断指定模块或目录是否可显示。如果模块未隐藏且可访问则模块可显示， 如果目录下存在1个或以上可显示模块则目录可显示。
     *
     * @param file  需要判断的文件或目录。
     * @param roles 当前用户的角色列表。
     * @param type  1桌面应用，2移动应用，3权限设置。
     * @return true 可显示，false不可显示。
     * @throws IOException 判断过程发生异常。
     */
    public static boolean canDisplay(File file, String[] roles, int type) throws Exception {
        if (file.isDirectory()) {
            if (type == 5)
                return true;
            File configFile = new File(file, "folder.json");
            if (configFile.exists()) {
                JSONObject content = JsonUtil.readObject(configFile);
                if ((type < 3) && (Boolean.TRUE.equals(content.opt("hidden"))))
                    return false;
            }
            File[] files = FileUtil.listFiles(file);
            for (File subFile : files)
                if (canDisplay(subFile, roles, type))
                    return true;
        } else if (file.getName().endsWith(".xwl")) {
            JSONObject content = get(file);
            if ((type == 4) || (type == 5)) {
                if (WbUtil.canAccess(content, roles))
                    return true;
            } else {
                if (type == 3) {
                    if ((Boolean.FALSE.equals(content.opt("loginRequired")))
                            || (Boolean.TRUE.equals(content.opt("internalCall"))))
                        return false;
                } else if (content.has("hasTouch")) {
                    if ((type == 1) && (!Var.homeShowApp))
                        return false;
                } else if (type == 2) {
                    return false;
                }
                if ((Boolean.FALSE.equals(content.opt("hidden"))) && (WbUtil.canAccess(content, roles)))
                    return true;
            }
        }
        return false;
    }
}