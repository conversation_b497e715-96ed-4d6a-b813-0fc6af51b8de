package com.wb.common;

import javax.servlet.http.HttpSession;
import javax.servlet.http.HttpSessionBindingEvent;
import javax.servlet.http.HttpSessionBindingListener;

import com.wb.cache.RedisCache;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.ZSetOperations;

import java.util.Collections;
import java.util.Set;

/**
 * SimpleListener类负责监听会话绑定和解绑事件。
 * @deprecated 此类已弃用，请使用基于Spring Session事件的替代方案：
 * {@link com.wb.common.session.SessionCreatedEventListener} 处理会话创建
 * {@link com.wb.common.session.CustomSessionDestroyedEventListener} 处理会话销毁
 */
@Deprecated
public class SimpleListener implements HttpSessionBindingListener, java.io.Serializable {
    /**
     * 序列化用的编号。
     */
    private static final long serialVersionUID = 4490661288665900556L;

    /**
     * 用户ID。
     */
    private String userId;
    /**
     * 用户名称。
     */
    private String username;
    /**
     * IP地址。
     */
    private String ip;

    /**
     * 当会话被创建时触发该方法。
     */
    public void valueBound(HttpSessionBindingEvent event) {
//        HttpSession session = event.getSession();
//        this.userId = ((String) session.getAttribute("sys.user"));
//        if (Var.log) {
//            this.username = ((String) session.getAttribute("sys.username"));
//            this.ip = ((String) session.getAttribute("sys.ip"));
//            LogUtil.log(username, ip, LogUtil.INFO, "login");
//        }
//        // 包含用户ID的才算在线用户
//        if (!StringUtil.isEmpty(this.userId)) {
//            // 创建一个TypedTuple对象，将用户ID作为值，将当前时间作为分数
//            ZSetOperations.TypedTuple<Object> tuple = new DefaultTypedTuple<>(this.userId, (double) System.currentTimeMillis());
//            // 创建一个只包含这个TypedTuple的Set
//            Set<ZSetOperations.TypedTuple<Object>> tuples = Collections.singleton(tuple);
//            // 将这个Set添加到一个名为RedisCache.ONLINE_USERS的ZSet中
//            Base.map.zsetSet(RedisCache.ONLINE_USERS, tuples);
//            // 默认设置为24小时自动过期
//            Base.map.expire(RedisCache.ONLINE_USERS, 24 * 60 * 60);
//
//        }
    }

    /**
     * 当会话被销毁时触发该方法。
     */
    public void valueUnbound(HttpSessionBindingEvent event) {
//        if (Var.log) LogUtil.log(username, ip, LogUtil.INFO, "logout");
//        // 包含用户ID的才算用户
//        if (!StringUtil.isEmpty(this.userId)) {
//            // 如果用户没有任何会话之后，才做移除操作
//            if (UserList.getSessions(userId).length == 0) Base.map.zsetDel(RedisCache.ONLINE_USERS, this.userId);
//        }
    }
}