package com.wb.rocketmq.config;

import com.wb.common.Var;
import com.wb.util.StringUtil;

/**
 * RocketMQ消息配置类
 * 用于管理RocketMQ的连接和主题配置
 */
public class MqConfig {
    // -------------------- 连接配置 --------------------
    /** 访问密钥ID */
    private String accessKey;
    /** 访问密钥密码 */
    private String secretKey;
    /** 服务器地址 */
    private String serverAddress;
    /** 是否为公网访问 */
    private boolean isPublic;
    
    // -------------------- 主题配置 --------------------
    /** 自定义主题（如果设置，将覆盖默认主题） */
    private String cusTopic;
    
    // -------------------- 同步消息配置 --------------------
    /** 同步消息主题 */
    private String syncTopic;
    /** 同步消息消费组ID */
    private String syncGroupId;
    /** 同步消息标签 */
    private String syncTag;
    
    // -------------------- 异步消息配置 --------------------
    /** 异步消息主题 */
    private String asyncTopic;
    /** 异步消息消费组ID */
    private String asyncGroupId;
    /** 异步消息标签 */
    private String asyncTag;

    // -------------------- 顺序消息配置 --------------------
    /** 顺序消息主题 */
    private String sequentialTopic;
    /** 顺序消息消费组ID */
    private String sequentialGroupId;
    /** 顺序消息标签 */
    private String sequentialTag;

    // -------------------- 定时/延时消息配置 --------------------
    /** 定时/延时消息主题 */
    private String timeDelayTopic;
    /** 定时/延时消息消费组ID */
    private String timeDelayGroupId;
    /** 定时/延时消息标签 */
    private String timeDelayTag;
    /** 消息投递时间戳 */
    private Long deliverTimeStamp;

    /**
     * 构造函数
     * 从系统配置中加载RocketMQ配置参数
     */
    public MqConfig() {
        // 加载连接配置
        this.accessKey = Var.getString("sys.config.aliyun.accessKeyId");
        this.secretKey = Var.getString("sys.config.aliyun.accessKeySecret");
        this.serverAddress = Var.getString("sys.config.aliyun.serverAddress");
        this.isPublic = Var.getBool("sys.config.aliyun.isPublic");
        
        // 加载主题配置
        // 同步消息配置
        this.syncTopic = Var.getString("sys.config.aliyun.syncTopic");
        this.syncGroupId = Var.getString("sys.config.aliyun.syncGroupId");
        
        // 异步消息配置
        this.asyncTopic = Var.getString("sys.config.aliyun.asyncTopic");
        this.asyncGroupId = Var.getString("sys.config.aliyun.asyncGroupId");
        
        // 顺序消息配置
        this.sequentialTopic = Var.getString("sys.config.aliyun.sequentialTopic");
        this.sequentialGroupId = Var.getString("sys.config.aliyun.sequentialGroupId");
        
        // 定时/延时消息配置
        this.timeDelayTopic = Var.getString("sys.config.aliyun.timeDelayTopic");
        this.timeDelayGroupId = Var.getString("sys.config.aliyun.timeDelayGroupId");
    }

    // -------------------- Getter和Setter方法 --------------------
    
    /**
     * 获取访问密钥ID
     * @return 访问密钥ID
     */
    public String getAccessKey() {
        return accessKey;
    }

    /**
     * 设置访问密钥ID
     * @param accessKey 访问密钥ID
     */
    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    /**
     * 获取访问密钥密码
     * @return 访问密钥密码
     */
    public String getSecretKey() {
        return secretKey;
    }

    /**
     * 设置访问密钥密码
     * @param secretKey 访问密钥密码
     */
    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    /**
     * 获取服务器地址
     * @return 服务器地址
     */
    public String getServerAddress() {
        return serverAddress;
    }

    /**
     * 设置服务器地址
     * @param serverAddress 服务器地址
     */
    public void setServerAddress(String serverAddress) {
        this.serverAddress = serverAddress;
    }

    /**
     * 获取同步消息主题
     * 如果设置了自定义主题，则返回自定义主题
     * @return 主题
     */
    public String getSyncTopic() {
        return !StringUtil.isEmpty(cusTopic) ? cusTopic : syncTopic;
    }

    /**
     * 设置同步消息主题
     * @param syncTopic 同步消息主题
     */
    public void setSyncTopic(String syncTopic) {
        this.syncTopic = syncTopic;
    }

    /**
     * 获取同步消息消费组ID
     * @return 同步消息消费组ID
     */
    public String getSyncGroupId() {
        return syncGroupId;
    }

    /**
     * 设置同步消息消费组ID
     * @param syncGroupId 同步消息消费组ID
     */
    public void setSyncGroupId(String syncGroupId) {
        this.syncGroupId = syncGroupId;
    }

    /**
     * 获取同步消息标签
     * @return 同步消息标签
     */
    public String getSyncTag() {
        return syncTag;
    }

    /**
     * 设置同步消息标签
     * @param syncTag 同步消息标签
     */
    public void setSyncTag(String syncTag) {
        this.syncTag = syncTag;
    }

    /**
     * 获取异步消息主题
     * 如果设置了自定义主题，则返回自定义主题
     * @return 主题
     */
    public String getAsyncTopic() {
        return !StringUtil.isEmpty(cusTopic) ? cusTopic : asyncTopic;
    }

    /**
     * 设置异步消息主题
     * @param asyncTopic 异步消息主题
     */
    public void setAsyncTopic(String asyncTopic) {
        this.asyncTopic = asyncTopic;
    }

    /**
     * 获取异步消息消费组ID
     * @return 异步消息消费组ID
     */
    public String getAsyncGroupId() {
        return asyncGroupId;
    }

    /**
     * 设置异步消息消费组ID
     * @param asyncGroupId 异步消息消费组ID
     */
    public void setAsyncGroupId(String asyncGroupId) {
        this.asyncGroupId = asyncGroupId;
    }

    /**
     * 获取异步消息标签
     * @return 异步消息标签
     */
    public String getAsyncTag() {
        return asyncTag;
    }

    /**
     * 设置异步消息标签
     * @param asyncTag 异步消息标签
     */
    public void setAsyncTag(String asyncTag) {
        this.asyncTag = asyncTag;
    }

    /**
     * 获取顺序消息主题
     * 如果设置了自定义主题，则返回自定义主题
     * @return 主题
     */
    public String getSequentialTopic() {
        return !StringUtil.isEmpty(cusTopic) ? cusTopic : sequentialTopic;
    }

    /**
     * 设置顺序消息主题
     * @param sequentialTopic 顺序消息主题
     */
    public void setSequentialTopic(String sequentialTopic) {
        this.sequentialTopic = sequentialTopic;
    }

    /**
     * 获取顺序消息消费组ID
     * @return 顺序消息消费组ID
     */
    public String getSequentialGroupId() {
        return sequentialGroupId;
    }

    /**
     * 设置顺序消息消费组ID
     * @param sequentialGroupId 顺序消息消费组ID
     */
    public void setSequentialGroupId(String sequentialGroupId) {
        this.sequentialGroupId = sequentialGroupId;
    }

    /**
     * 获取顺序消息标签
     * @return 顺序消息标签
     */
    public String getSequentialTag() {
        return sequentialTag;
    }

    /**
     * 设置顺序消息标签
     * @param sequentialTag 顺序消息标签
     */
    public void setSequentialTag(String sequentialTag) {
        this.sequentialTag = sequentialTag;
    }

    /**
     * 获取定时/延时消息主题
     * 如果设置了自定义主题，则返回自定义主题
     * @return 主题
     */
    public String getTimeDelayTopic() {
        return !StringUtil.isEmpty(cusTopic) ? cusTopic : timeDelayTopic;
    }

    /**
     * 设置定时/延时消息主题
     * @param timeDelayTopic 定时/延时消息主题
     */
    public void setTimeDelayTopic(String timeDelayTopic) {
        this.timeDelayTopic = timeDelayTopic;
    }

    /**
     * 获取定时/延时消息消费组ID
     * @return 定时/延时消息消费组ID
     */
    public String getTimeDelayGroupId() {
        return timeDelayGroupId;
    }

    /**
     * 设置定时/延时消息消费组ID
     * @param timeDelayGroupId 定时/延时消息消费组ID
     */
    public void setTimeDelayGroupId(String timeDelayGroupId) {
        this.timeDelayGroupId = timeDelayGroupId;
    }

    /**
     * 获取定时/延时消息标签
     * @return 定时/延时消息标签
     */
    public String getTimeDelayTag() {
        return timeDelayTag;
    }

    /**
     * 设置定时/延时消息标签
     * @param timeDelayTag 定时/延时消息标签
     */
    public void setTimeDelayTag(String timeDelayTag) {
        this.timeDelayTag = timeDelayTag;
    }

    /**
     * 获取消息投递时间戳
     * @return 消息投递时间戳
     */
    public Long getDeliverTimeStamp() {
        return deliverTimeStamp;
    }

    /**
     * 设置消息投递时间戳
     * @param deliverTimeStamp 消息投递时间戳
     */
    public void setDeliverTimeStamp(Long deliverTimeStamp) {
        this.deliverTimeStamp = deliverTimeStamp;
    }

    /**
     * 获取是否为公网访问
     * @return 是否为公网访问
     */
    public boolean getIsPublic() {
        return isPublic;
    }

    /**
     * 设置是否为公网访问
     * @param aPublic 是否为公网访问
     */
    public void setIsPublic(boolean aPublic) {
        isPublic = aPublic;
    }

    /**
     * 获取自定义主题
     * @return 自定义主题
     */
    public String getCusTopic() {
        return cusTopic;
    }

    /**
     * 设置自定义主题
     * 如果设置了自定义主题，将覆盖默认的主题设置
     * @param cusTopic 自定义主题
     */
    public void setCusTopic(String cusTopic) {
        this.cusTopic = cusTopic;
    }
}

