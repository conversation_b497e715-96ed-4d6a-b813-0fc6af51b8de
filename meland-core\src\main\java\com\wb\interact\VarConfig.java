package com.wb.interact;

import java.io.IOException;
import java.util.Map.Entry;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.json.JSONArray;
import org.json.JSONObject;

import com.wb.common.Base;
import com.wb.common.Var;
import com.wb.util.FileUtil;
import com.wb.util.JsonUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;
import com.wb.util.WebUtil;

public class VarConfig {
    /**
     * 获取变量树数据源。
     */
    public static void getTree(HttpServletRequest request, HttpServletResponse response) throws Exception {
        JSONObject jo = JsonUtil.readObject(Var.file);
        JSONObject tree = new JSONObject();
        buildTree(jo, tree);
        WebUtil.send(response, tree);
    }

    /**
     * 设置变量值，更新缓存中的变量值并把变量值写进文件。
     */
    public static synchronized void setVar(HttpServletRequest request, HttpServletResponse response) throws Exception {
        JSONObject object = JsonUtil.readObject(Var.file);

        String name = request.getParameter("name");
        String path = request.getParameter("path");
        String type = request.getParameter("type");
        String valueStr = request.getParameter("value");
        String configStr = request.getParameter("config");
        boolean isNew = Boolean.parseBoolean(request.getParameter("isNew"));
        JSONObject folder;

        if (name.indexOf('.') != -1)
            throw new RuntimeException("名称 \"" + name + "\" 不能包含符号 “.”。");
        Object folderObject = JsonUtil.getValue(object, path, '.');
        if ((folderObject instanceof JSONObject)) {
            folder = (JSONObject) folderObject;
            if (folder.has(name)) {
                if (isNew)
                    throw new RuntimeException("名称 \"" + name + "\" 已经存在。");
            } else if (!isNew)
                throw new RuntimeException("名称 \"" + name + "\" 不存在。");
        } else {
            throw new RuntimeException("目录 \"" + path + "\" 不存在或不是一个目录。");
        }
        Object primativeVal;
        if ("int".equals(type)) {
            primativeVal = Integer.parseInt(valueStr);
        } else {
            if ("bool".equals(type)) {
                primativeVal = Boolean.parseBoolean(valueStr);
            } else {
                if ("double".equals(type))
                    primativeVal = Double.parseDouble(valueStr);
                else
                    primativeVal = valueStr;
            }
        }
        if (isNew) {
            JSONArray value = new JSONArray();
            value.put(primativeVal);
            value.put(request.getParameter("remark"));
            JSONObject config;
            if (configStr.isEmpty())
                config = new JSONObject();
            else
                config = new JSONObject(configStr);
            config.put("type", request.getParameter("type"));
            value.put(config);
            folder.put(name, value);
        } else {
            JSONArray value = folder.getJSONArray(name);
            value.put(0, primativeVal);
        }
        FileUtil.syncSave(Var.file, object.toString(2));
        Var.buffer.put(path + '.' + name, primativeVal);
        Var.loadBasicVars();

        //同步通知其他服务器处理
        com.alibaba.fastjson.JSONObject chatParams = new com.alibaba.fastjson.JSONObject();
        chatParams.put("type", "set");
        chatParams.put("name", name);
        chatParams.put("path", path);
        chatParams.put("value", primativeVal);
        chatParams.put("server", SysUtil.getServerId());
        Base.map.publish("chat_var", chatParams);
    }

    /**
     * 删除变量。
     */
    public static synchronized void delVar(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String path = request.getParameter("path");
        if (StringUtil.isEmpty(path))
            throw new RuntimeException("Empty path value.");
        JSONArray names = new JSONArray(request.getParameter("names"));
        JSONObject object = JsonUtil.readObject(Var.file);
        JSONObject selFolder = (JSONObject) JsonUtil.getValue(object, path, '.');
        int j = names.length();

        for (int i = 0; i < j; i++) {
            if (selFolder != null) {
                selFolder.remove(names.optString(i));
            }
        }
        FileUtil.syncSave(Var.file, object.toString(2));

        String lockKey = "DEL_VAR:" + path, key = "";
        try {
            if (Base.map.tryLock(lockKey, 1000, 10000)) {
                for (int i = 0; i < j; i++) {
                    key = StringUtil.concat(path, ".", names.optString(i));
                    Var.buffer.remove(key);
                }
                //同步通知其他服务器处理
                com.alibaba.fastjson.JSONObject chatParams = new com.alibaba.fastjson.JSONObject();
                chatParams.put("type", "del");
                chatParams.put("path", path);
                chatParams.put("names", request.getParameter("names"));
                chatParams.put("server", SysUtil.getServerId());
                chatParams.put("varFile", object.toString());
                Base.map.publish("chat_var", chatParams);
            }
        } finally {
            Base.map.unLock(lockKey);
        }
    }

    /**
     * 添加、删除或修改目录。
     */
    public static synchronized void setFolder(HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        String type = request.getParameter("type");
        JSONObject object = JsonUtil.readObject(Var.file);
        String path = request.getParameter("path");
        String name = request.getParameter("name");

        if (path == null)
            throw new RuntimeException("null path parameter");
        String lockKey = "SET_VAR_FOLDER:" + path;
        try {
            if (Base.map.tryLock(lockKey, 1000, 10000)) {
                if ("add".equals(type)) {
                    if (name == null)
                        throw new RuntimeException("null name parameter");
                    if (name.indexOf('.') != -1)
                        throw new RuntimeException("名称 \"" + name + "\" 不能包含符号 “.”。");
                    JSONObject folder = (JSONObject) JsonUtil.getValue(object, path, '.');
                    if (folder.has(name))
                        throw new RuntimeException("名称 \"" + name + "\" 已经存在。");
                    folder.put(name, new JSONObject());
                } else if ("delete".equals(type)) {
                    JsonUtil.setValue(object, path, '.', null);
                    path = path + '.';
                    Set<Entry<String, Object>> es = Var.buffer.entrySet();

                    for (Entry<String, Object> e : es) {
                        String key = (String) e.getKey();
                        if (key.startsWith(path)) {
                            Var.buffer.remove(key);
                        }
                    }
                } else if ("update".equals(type)) {
                    String newName = request.getParameter("newName");
                    if (newName.indexOf('.') != -1)
                        throw new RuntimeException("名称 \"" + newName + "\" 不能包含符号 “.”。");
                    JSONObject folder = (JSONObject) JsonUtil.getValue(object, path, '.');
                    if (folder.has(newName))
                        throw new RuntimeException("名称 \"" + newName + "\" 已经存在。");
                    JSONObject jo = folder.getJSONObject(name);
                    folder.remove(name);
                    folder.put(newName, jo);
                    String newPath = StringUtil.concat(path, ".", newName, ".");
                    path = StringUtil.concat(path, ".", name, ".");
                    Set<Entry<String, Object>> es = Var.buffer.entrySet();

                    int oldPathLen = path.length();

                    for (Entry<String, Object> e : es) {
                        String key = (String) e.getKey();
                        if (key.startsWith(path)) {
                            Var.buffer.remove(key);
                            Var.buffer.put(newPath + key.substring(oldPathLen), e.getValue());
                        }
                    }
                }
                FileUtil.syncSave(Var.file, object.toString(2));

                //同步通知其他服务器处理
                com.alibaba.fastjson.JSONObject chatParams = new com.alibaba.fastjson.JSONObject();
                chatParams.put("type", "setFolder");
                chatParams.put("name", name);
                chatParams.put("path", path);
                chatParams.put("newName", request.getParameter("newName"));
                chatParams.put("varType", request.getParameter("type"));
                chatParams.put("server", SysUtil.getServerId());
                chatParams.put("varFile", object.toString());
                Base.map.publish("chat_var", chatParams);
            }
        } finally {
            Base.map.unLock(lockKey);
        }
    }

    /**
     * 递归生成变量目录树。
     *
     * @param jo   当前变量目录。
     * @param tree 当前树。
     */
    private static void buildTree(JSONObject jo, JSONObject tree) throws IOException {
        Set<Entry<String, Object>> entrySet = jo.entrySet();

        JSONArray children = new JSONArray();

        tree.put("children", children);
        for (Entry<String, Object> entry : entrySet) {
            String key = (String) entry.getKey();
            Object object = jo.opt(key);
            if ((object instanceof JSONObject)) {
                JSONObject node = new JSONObject();
                node.put("text", key);
                children.put(node);
                buildTree((JSONObject) object, node);
            }
        }
    }

    /**
     * 获取变量列表。
     */
    public static void getVars(HttpServletRequest request, HttpServletResponse response) throws Exception {
        JSONObject jo = JsonUtil.readObject(Var.file);
        JSONObject folder = (JSONObject) JsonUtil.getValue(jo, request.getParameter("path"), '.');
        if (folder == null)
            throw new IllegalArgumentException("指定路径变量不存在。");
        Set<Entry<String, Object>> entrySet = folder.entrySet();
        JSONArray items = new JSONArray();

        for (Entry<String, Object> entry : entrySet) {
            Object value = entry.getValue();
            if ((value instanceof JSONArray)) {
                JSONArray jsonValue = (JSONArray) value;
                JSONObject item = new JSONObject();
                item.put("name", entry.getKey());
                item.put("value", jsonValue.opt(0));
                item.put("remark", jsonValue.opt(1));
                item.put("meta", jsonValue.opt(2));
                items.put(item);
            }
        }
        WebUtil.send(response, new JSONObject().put("rows", items));
    }
}