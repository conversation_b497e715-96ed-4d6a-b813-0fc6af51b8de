package com.wb.util;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Reader;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.wb.aliyun.sls.SLSConfiguration;
import com.wb.aliyun.sls.SLSLogUtil;
import com.wb.common.*;
import com.wb.common.Dictionary;
import com.wb.exception.AccessDeniedException;
import org.apache.commons.io.IOUtils;
import com.wb.fit.CustomRequest;
import com.wb.interact.Controls;

/**
 * 系统工具方法类。
 */
public class SysUtil {
    /**
     * 服务器端维护的维一ID号。
     */
    private static long currentId = 0;
    /**
     * 服务器ID号的首位字符。
     */
    private static byte serverId0;
    /**
     * 服务器ID号的次位字符。
     */
    private static byte serverId1;
    /**
     * 同步锁。
     */
    private static final Object lock = new Object();

    /**
     * 服务器ID
     */
    private static String serverId;
    /**
     * 36进制数字表。
     */
    public static final byte[] digits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};

    /**
     * 默认缓冲区大小。
     */
    public static final int bufferSize = 4096;

    /**
     * 定义用于Base62编码的字符集
     */
    private static final String ALPHABET = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

    /**
     * 计算字符集的基数，用于Base62编码
     */
    private static final int BASE = ALPHABET.length();

    /**
     * 把反射的方法存储到HashMap中，以提高对方法的访问性能。
     */
    private static final ConcurrentHashMap<String, Method> methodBuffer = new ConcurrentHashMap<String, Method>();

    /**
     * 获取服务器端唯一且正增长的13位长度编号。该编号由2位服务器编号字符串和11位基于时序动态
     * 生成的字符串组成。编号由26个大写字母和10个数字组成。该方法可产生唯一且正增长的id号，并
     * 可保证在公元2386年前的编码长度不超过11位（另2位为服务器编号）。该方法仅保证指定服务器
     * 内（前2位服务器编号）的编号唯一。如果要使用全球唯一的编号，请使用java.util.UUID。
     *
     * @return 指定服务器内唯一的id号。
     * @see java.util.UUID#randomUUID()
     */
    public static String getId() {
        long id;
        synchronized (lock) {
            if (currentId == 0) {
                currentId = (new Date()).getTime() * 10000;
                String serverId = SysUtil.getServerId();
                serverId0 = (byte) serverId.charAt(0);
                serverId1 = (byte) serverId.charAt(1);
            }
            id = currentId++;
        }
        return numToString(id);
    }

    /**
     * 获取服务器编号
     *
     * @return 服务器编号
     */
    public static String getServerId() {
        if (serverId == null || serverId.isEmpty()) {
            try {
                boolean isFind = false; // 标志是否找到有效IP
                Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
                for (NetworkInterface netInterface : Collections.list(networkInterfaces)) {
                    // 跳过回环接口和未启用的接口
                    if (netInterface.isLoopback() || !netInterface.isUp()) {
                        continue;
                    }
                    Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        InetAddress addr = addresses.nextElement();
                        String ip = addr.getHostAddress();
                        // 跳过IPv6地址和特殊地址
                        if (ip.contains(":") || "127.0.0.1".equals(ip) || "0.0.0.0".equals(ip)) {
                            continue;
                        }
                        // 从IP地址中提取最后两位数字
                        String lastTwoDigits = ip.substring(ip.lastIndexOf('.') + 1);
                        if (lastTwoDigits.length() > 2) { // 如果长度大于2，则取最后两位
                            lastTwoDigits = lastTwoDigits.substring(lastTwoDigits.length() - 2);
                        } else if (lastTwoDigits.length() == 1) { // 如果长度为1，则前面补充一个0
                            lastTwoDigits = "0" + lastTwoDigits;
                        }
                        isFind = true; // 已找到有效IP
                        serverId = lastTwoDigits; // 设置服务器ID
                        break;
                    }
                    if (isFind) { // 如果已找到有效IP，则跳出循环
                        break;
                    }
                }
            } catch (SocketException exception) {
                try {
                    String pcName = InetAddress.getLocalHost().getHostName(); // 获取主机名
                    String[] arr = pcName.split("_");
                    if (arr.length != 3) {
                        serverId = pcName.substring(0, 2); // 如果主机名不符合预期格式，则取前两位作为服务器ID
                    } else {
                        serverId = arr[2].substring(0, 2); // 从主机名中提取服务器ID
                    }
                } catch (UnknownHostException ignored) {
                }
            }
        }
        if (serverId == null || serverId.isEmpty()) {
            serverId = generateRandomServerId();
        }
        return serverId; // 返回服务器ID
    }

    private static String generateRandomServerId() {
        Random random = new Random();
        // 定义一个字符串，包含所有可能的字符（0-9，A-Z）
        String characters = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        // 使用StringBuilder构建最终的两位随机字符串
        StringBuilder serverIdBuilder = new StringBuilder();
        for (int i = 0; i < 2; i++) { // 循环两次，每次添加一个随机字符
            int index = random.nextInt(characters.length()); // 生成一个随机索引
            char randomChar = characters.charAt(index); // 获取随机字符
            serverIdBuilder.append(randomChar); // 将随机字符添加到StringBuilder中
        }
        return serverIdBuilder.toString(); // 将StringBuilder转换为字符串
    }

    /**
     * 生成指定长度的短UUID字符串。
     *
     * @param length 期望生成的UUID字符串的长度。
     * @return 生成的短UUID字符串。
     */
    public static String generateShortUUID(int length) {
        // 生成一个新的UUID
        UUID uuid = UUID.randomUUID();
        StringBuilder shortUUID = new StringBuilder();
        // 获取UUID的最高有效位和最低有效位
        long mostSignificantBits = uuid.getMostSignificantBits();
        long leastSignificantBits = uuid.getLeastSignificantBits();
        // 将期望长度分成两部分，分别分配给最高有效位和最低有效位
        int halfLength = length / 2;
        // 对两部分分别进行Base62编码，并确保编码后的长度符合预期
        shortUUID.append(encodeBase62(mostSignificantBits, halfLength));
        shortUUID.append(encodeBase62(leastSignificantBits, length - halfLength)); // 确保总长度等于指定长度
        return shortUUID.toString();
    }

    /**
     * 对一个长整型数字进行Base62编码，并确保编码后的长度。
     *
     * @param value 要编码的长整型数字。
     * @param length 编码后字符串的期望长度。
     * @return 编码后的字符串。
     */
    private static String encodeBase62(long value, int length) {
        StringBuilder encoded = new StringBuilder();
        // 确保输入值为正
        value = Math.abs(value);
        // 循环将数字转换为Base62编码
        while (value > 0) {
            encoded.insert(0, ALPHABET.charAt((int) (value % BASE)));
            value /= BASE;
        }
        // 如果编码后的字符串长度不足，前面用'0'进行填充
        while (encoded.length() < length) {
            encoded.insert(0, ALPHABET.charAt(0));
        }
        // 如果编码后的字符串长度超出预期，从末尾开始截断
        if (encoded.length() > length) {
            return encoded.substring(encoded.length() - length);
        }
        return encoded.toString();
    }


    /**
     * 生成树形结构ID。
     *
     * @param parentId   上级ID，-1代表为顶层
     * @param tableName  树结构表名
     * @param pkField    树结构主键字段
     * @param levelField 树结构层次字段
     * @return 生成的树结构ID值，包含id=唯一ID，level=层级
     */
    public static Map<String, Object> getTreeId(String parentId, String tableName, String pkField, String levelField) {
        Map<String, Object> map = new HashMap<>();
        synchronized (lock) {
            parentId = parentId.equals("-1") ? "" : parentId;
            String treeId = "1001";
            int level = parentId.length() / 4 + 1;

            StringBuilder selectSQL = new StringBuilder();
            StringBuilder whereSQL = new StringBuilder();
            selectSQL.append(" select max(" + pkField + ")");
            whereSQL.append(" from " + tableName);
            if (parentId.length() == 0) {
                whereSQL.append(" where " + pkField + " like '%' ");
                whereSQL.append(" and " + levelField + " = " + level);
            } else {
                whereSQL.append(" where " + pkField + " like '" + parentId + "%' ");
                whereSQL.append(" and " + levelField + " = " + level);
            }
            selectSQL.append(whereSQL);
            Connection conn = null;
            Statement st = null;
            ResultSet rs = null;
            try {
                conn = DbUtil.getConnection();
                st = conn.createStatement();
                rs = st.executeQuery(selectSQL.toString());
                if (rs.next()) {
                    String maxVal = rs.getString(1) == null ? "" : rs.getString(1);
                    if (maxVal.length() == 0) {
                        if (parentId.length() != 0) {
                            treeId = parentId + "0001";
                        }
                    } else {
                        if (maxVal.equals("9999")) {
                            LogUtil.warn("树形ID超出9999范围：" + tableName);
                            treeId = "9999";
                        } else {
                            if (maxVal.length() > 4) {
                                maxVal = maxVal.substring(maxVal.length() - 4, maxVal.length());
                            }
                            maxVal = "0000" + (Long.parseLong(maxVal) + 1);
                            treeId = parentId + maxVal.substring(maxVal.length() - 4, maxVal.length());
                        }
                    }
                }
                map.put("id", treeId);
                map.put("level", level);
            } catch (Exception e) {
                throw new RuntimeException("生成树形结构ID异常：" + tableName, e);
            } finally {
                DbUtil.close(rs);
                DbUtil.close(st);
                DbUtil.close(conn);
            }

        }
        return map;
    }

    /**
     * 把长整数转换为36进制的字符串并在该字符串前加上服务器编号，作为整个字符串返回。
     *
     * @param num 需要转换的数值。
     * @return 转换后的字符串。
     */
    private static String numToString(long num) {
        byte buf[] = new byte[13], charPos = 12;
        long val;

        buf[0] = serverId0;
        buf[1] = serverId1;
        while ((val = num / 36) > 0) {
            buf[charPos--] = digits[(byte) (num % 36)];
            num = val;
        }
        buf[charPos] = digits[(byte) num];
        return new String(buf);
    }

    /**
     * 获取指定异常对象的根异常信息。
     *
     * @param e 异常对象。
     * @return 获取的根异常信息。
     */
    public static String getRootError(Throwable e) {
        Throwable cause, c = e;
        String message;

        do {
            cause = c;
            c = c.getCause();
        } while (c != null);
        message = cause.getMessage();
        if (StringUtil.isEmpty(message)) {
            message = cause.toString();
        }
        return StringUtil.toLine(message.trim());
    }

    /**
     * 判断当前请求对象request是否为自定义
     *
     * @param request 请求对象
     */
    public static boolean isNotCustomRequest(HttpServletRequest request) {
        return !(request instanceof CustomRequest);
    }

    /**
     * 获取指定异常对象的根异常信息。
     *
     * @param e 异常对象。
     * @return 获取的根异常信息。
     */
    public static Throwable getRootExcept(Throwable e) {
        Throwable cause, c = e;

        do {
            cause = c;
            c = c.getCause();
        } while (c != null);
        return cause;
    }

    /**
     * 执行含HttpServletRequest和HttpServletResponse参数的静态方法。
     *
     * @param classMethodName 包名、类名和方法名构成的全名。
     * @param request         请求对象。
     * @param response        响应对象。
     * @throws Exception 执行方法过程发生异常。
     */
    public static void executeMethod(String classMethodName, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Method method = methodBuffer.get(classMethodName);
        if (method == null) {
            // 无需使用同步。
            int pos = classMethodName.lastIndexOf('.');
            String className, methodName;
            if (pos == -1) {
                className = "";
                methodName = classMethodName;
            } else {
                className = classMethodName.substring(0, pos);
                methodName = classMethodName.substring(pos + 1);
            }
            Class<?> cls = Class.forName(className);
            method = cls.getMethod(methodName, HttpServletRequest.class, HttpServletResponse.class);
            methodBuffer.put(classMethodName, method);
        }
        method.invoke(null, request, response);
    }

    /**
     * 把输入流对象转换成基于字节的输入流对象，并关闭原输入流对象。
     *
     * @param is 输入流。
     * @return 字节输入流。
     * @throws IOException 读取流过程发生异常。
     */
    public static ByteArrayInputStream toByteArrayInputStream(InputStream is) throws IOException {
        if (is instanceof ByteArrayInputStream) {
            return (ByteArrayInputStream) is;
        }
        byte[] bytes;
        try {
            bytes = IOUtils.toByteArray(is);
        } finally {
            is.close();
        }
        return new ByteArrayInputStream(bytes);
    }

    /**
     * 从Reader对象读取字符串。读取完成后关闭reader。
     *
     * @param reader Reader对象。
     * @return 读取的字符串。
     * @throws IOException 读取过程发生异常。
     */
    public static String readString(Reader reader) throws IOException {
        try {
            char buf[] = new char[bufferSize];
            StringBuilder sb = new StringBuilder();
            int len;

            while ((len = reader.read(buf)) > 0) {
                sb.append(buf, 0, len);
            }
            return sb.toString();
        } finally {
            reader.close();
        }
    }

    /**
     * 抛出异常信息。
     *
     * @param msg 异常信息。
     * @throws RuntimeException 抛出异常。
     */
    public static void error(String msg) {
        throw new RuntimeException(msg);
    }

    /**
     * 抛出包含指定错误代码的异常信息。错误代码包含在错误信息中，并使用特定关键字引用。
     *
     * @param msg     异常信息。
     * @param errorNo 错误代码。
     * @throws RuntimeException 抛出异常。
     */
    public static void error(String msg, String errorNo) throws RuntimeException {
        throw new RuntimeException(StringUtil.concat("#WBE", errorNo, ":", msg));
    }

    /**
     * 可以用于判断 Map,Collection,String,Array,Long是否为空
     *
     * @param o java.lang.Object.
     * @return boolean.
     */
    @SuppressWarnings("rawtypes")
    public static boolean isEmpty(Object o) {
        if (o == null) {
            return true;
        }
        if (o instanceof String) {
            if (((String) o).trim().length() == 0) {
                return true;
            }
        } else if (o instanceof Collection) {
            if (((Collection) o).isEmpty()) {
                return true;
            }
        } else if (o.getClass().isArray()) {
            if (((Object[]) o).length == 0) {
                return true;
            }
        } else if (o instanceof Map) {
            if (((Map) o).isEmpty()) {
                return true;
            }
        }
        return false;

    }

    /**
     * 判断指定对象是否是数组。
     *
     * @return true对象是数组，false对象不是数组。
     */
    public static boolean isArray(Object object) {
        if (object == null) {
            return false;
        }
        return object.getClass().isArray();
    }

    /**
     * 把JS数组转换为Java数组。
     *
     * @return 转换后的Java数组。
     */
    public static Object[] javaArray(Object[] value) {
        return value;
    }

    /**
     * 把JS数组转换为Java整数数组。
     *
     * @return 转换后的Java整数数组。
     */
    public static Integer[] javaIntArray(Integer[] value) {
        return value;
    }

    /**
     * 把对象类型值转换为字符串类型值。
     *
     * @return 转换后的字符串值。
     */
    public static String javaString(Object value) {
        return value.toString();
    }

    /**
     * 把对象类型值转换为整数类型值。
     *
     * @return 转换后的值。
     */
    public static Integer javaInt(Object value) {
        if (value == null) {
            return null;
        } else if (value instanceof Number) {
            return ((Number) value).intValue();
        } else {
            return Integer.parseInt(value.toString());
        }
    }

    /**
     * 把对象类型值转换为长整数类型值。
     *
     * @return 转换后的值。
     */
    public static Long javaLong(Object value) {
        if (value == null) {
            return null;
        } else if (value instanceof Number) {
            return ((Number) value).longValue();
        } else {
            return Long.parseLong(value.toString());
        }
    }

    /**
     * 把对象类型值转换为浮点数类型值。
     *
     * @return 转换后的值。
     */
    public static Float javaFloat(Object value) {
        if (value == null) {
            return null;
        } else if (value instanceof Number) {
            return ((Number) value).floatValue();
        } else {
            return Float.parseFloat(value.toString());
        }
    }

    /**
     * 把对象类型值转换为双精度类型值。
     *
     * @return 转换后的值。
     */
    public static Double javaDouble(Object value) {
        if (value == null) {
            return null;
        } else if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else {
            return Double.parseDouble(value.toString());
        }
    }

    /**
     * 把JS类型值转换为Java布尔类型值。
     *
     * @return 转换后的Java值。
     */
    public static Boolean javaBool(Boolean value) {
        return value;
    }

    /**
     * 判断指定对象是否是Map。
     *
     * @return true对象是Map，false对象不是Map。
     */
    public static boolean isMap(Object object) {
        return object instanceof Map<?, ?>;
    }

    /**
     * 判断指定对象是否是可遍历的Iterable对象。
     *
     * @return true是，false不是。
     */
    public static boolean isIterable(Object object) {
        return object instanceof Iterable<?>;
    }

    /**
     * 重新热加载系统。
     *
     * @param type 加载类型：1全部，2非数据库相关类，3数据库相关类。
     */
    public static void reload(int type) {
        if (type == 1 || type == 2) {
            Var.load();
            Controls.load();
            FileBuffer.load();
            ScriptBuffer.load();
            Str.load();
            UrlBuffer.load();
            XwlBuffer.load();
        }
        if (type == 1 || type == 3) {
            KVBuffer.load();
            Dictionary.load();
            if (Var.getBool("sys.app.useDbRole")) {
                XwlBuffer.loadRoles();
            }
            if (Var.getBool("sys.config.sls.useSLS")) {
                // 初始化SLS配置，确保日志线程为守护线程
                // SLSConfiguration.init(); // 在Web应用中不需要设置守护线程
                SLSLogUtil.initProducer();
            }
        }
    }

    public static void manualReload(int type) {
        reload(type);
        //同步通知其他服务器处理
        com.alibaba.fastjson.JSONObject chatParams = new com.alibaba.fastjson.JSONObject();
        chatParams.put("type", "reload");
        chatParams.put("loadType", type);
        chatParams.put("server", SysUtil.getServerId());
        Base.map.publish("chat_reload", chatParams);
    }

    /**
     * 获取二维数组内指定的值。索引0存放键，索引1存放值，根据键获取值。
     *
     * @param data 二维数组。
     * @param key  键名
     * @return 键名对应的值。
     */
    public static Object getValue(Object[][] data, String key) {
        for (Object[] item : data) {
            if (key.equals(item[0])) {
                return item[1];
            }
        }
        return null;
    }

    public static void accessDenied(HttpServletRequest request) {
        throw new AccessDeniedException(Str.format(request, "accessDenied", new Object[0]));
    }

    public static void accessDenied(HttpServletRequest request, String msg) {
        throw new AccessDeniedException(Str.format(request, "accessDenied1", new Object[0]) + msg);
    }

    /**
     * 抛出包含Access denied信息的异常。
     */
    public static void accessDenied() {
        throw new RuntimeException("Access denied.");
    }

    public static void accessDenied(String msg) {
        throw new RuntimeException("Access denied: " + msg);
    }

    public static ByteArrayInputStream getEmptyStream() {
        return new ByteArrayInputStream(new byte[0]);
    }

    /**
     * 获取指定对象的类fields和methods成员名称组成的列表。
     *
     * @return 对象的类成员名称列表。
     */
    public static ArrayList<String> getObjectMembers(Object object) {
        return getClassMembers(object.getClass());
    }

    /**
     * 获取指定对象的类fields和methods成员名称组成的列表。
     *
     * @return 对象的类成员名称列表。
     */
    public static ArrayList<String> getClassMembers(Class<?> cls) {
        ArrayList<String> list = new ArrayList<String>();
        Field[] fields = cls.getFields();
        Method[] methods = cls.getMethods();
        String name;

        for (Field field : fields) {
            name = field.getName();
            if (list.indexOf(name) == -1) {
                list.add(name);
            }
        }
        for (Method method : methods) {
            name = method.getName();
            if (list.indexOf(name) == -1) {
                list.add(name);
            }
        }
        return list;
    }

    /**
     * 获取本机的mac地址，如果获取过程发生异常返回"invalid"字符串。
     *
     * @return 本机mac地址或invalid字符串。
     */
    public static String getMacAddress() {
        try {
            NetworkInterface network = NetworkInterface.getByInetAddress(InetAddress.getLocalHost());
            byte[] mac = network.getHardwareAddress();
            StringBuilder sb = new StringBuilder();

            for (int i = 0; i < mac.length; i++) {
                sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
            }
            return sb.toString();
        } catch (Throwable e) {
            return "invalid";
        }
    }

    /**
     * 获取用户设置的输出至用户文件时换行符号。
     *
     * @return 换行符号。
     */
    public static String getLineSeparator() {
        String sp = Var.getString("sys.locale.lineSeparator");
        if ("\\r".equals(sp)) {
            return "\r";
        } else if ("\\n".endsWith(sp)) {
            return "\n";
        } else {
            return "\r\n";
        }
    }

    /**
     * 把数组列表项内的所有值添加到HashSet中。
     *
     * @param <T>   指定类型。
     * @param items 列表项数组。
     * @return 所有列表项添加到HashSet值。
     */
    public static <T> HashSet<T> toHashSet(T[] items) {
        if (items == null) {
            return null;
        }
        HashSet<T> hashSet = new HashSet<T>();
        for (T item : items) {
            hashSet.add(item);
        }
        return hashSet;
    }

    public static <T> HashSet<T> toHashSet(Iterable<T> items) {
        if (items == null) {
            return null;
        }
        HashSet<T> hashSet = new HashSet<T>();
        for (T item : items) {
            hashSet.add(item);
        }
        return hashSet;
    }

    /**
     * 通过键值方式创建Properties配置
     *
     * @param keyValues 键值
     * @return
     */
    public static Properties keyValueToProperties(Object... keyValues) {
        Properties properties = new Properties();
        for (int i = 0; i < keyValues.length; i += 2) {
            properties.setProperty(keyValues[i].toString(), keyValues[i + 1].toString());
        }
        return properties;
    }
}
