package com.wb.common.session;

import com.wb.common.Base;
import com.wb.common.Var;
import com.wb.config.SessionRepositoryConfig;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.session.FindByIndexNameSessionRepository;
import org.springframework.session.Session;
import org.springframework.session.events.SessionCreatedEvent;
import org.springframework.stereotype.Component;

/**
 * 会话创建事件监听器
 * 用于处理会话创建事件，记录日志、更新用户状态和维护索引过期时间
 */
@Component
public class SessionCreatedEventListener {

    private static final Logger log = LoggerFactory.getLogger(SessionCreatedEventListener.class);
    private static final String USER_ID_KEY = "sys.user";
    private static final String USERNAME_KEY = "sys.username";
    private static final String IP_KEY = "sys.ip";

    @Autowired
    private FindByIndexNameSessionRepository<? extends Session> sessionRepository;

    @EventListener
    public void handleSessionCreated(SessionCreatedEvent event) {
        try {
            // 先从事件获取会话ID
            String sessionId = event.getSession().getId();
            log.debug("捕获到会话创建事件: {}", sessionId);

            // 使用repository重新获取完整会话
            Session session = sessionRepository.findById(sessionId);
            if (session == null) {
                log.warn("无法从仓库获取会话: {}", sessionId);
                return;
            }

            // 记录会话类型用于调试
            log.debug("会话类型: {}", session.getClass().getName());

            // 从会话属性中获取用户ID，并更新索引过期时间
            String userId = getUserId(session);
            if (userId != null && !userId.isEmpty()) {
                // 记录用户登录日志
//                logUserLogin(session);

                // 更新在线用户列表
                updateOnlineUsersList(session);

                // 确保会话中包含PRINCIPAL_NAME_INDEX_NAME属性
                if (session.getAttribute(FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME) == null) {
                    session.setAttribute(FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME, userId);
                    // 转换为正确的类型后保存会话
                    @SuppressWarnings("unchecked")
                    FindByIndexNameSessionRepository<Session> typedRepository = (FindByIndexNameSessionRepository<Session>) sessionRepository;
                    typedRepository.save(session);
                    log.debug("已为会话添加PRINCIPAL_NAME_INDEX_NAME映射: {}", userId);
                }

                // 使用SessionRepositoryConfig中的方法更新索引过期时间
                SessionRepositoryConfig.updatePrincipalIndex(userId, session);
                log.debug("已为用户[{}]的会话设置索引过期时间", userId);
            }

        } catch (Exception e) {
            log.warn("处理会话创建事件时出错", e);
        }
    }

    /**
     * 记录用户登录日志
     */
    private void logUserLogin(Session session) {
        if (Var.log) {
            String username = session.getAttribute(USERNAME_KEY);
            String ip = session.getAttribute(IP_KEY);
            if (username != null && ip != null) {
                LogUtil.log(username, ip, LogUtil.INFO, "login");
            }
        }
    }

    /**
     * 更新在线用户列表
     */
    private void updateOnlineUsersList(Session session) {
        String userId = session.getAttribute(USER_ID_KEY);
        if (!StringUtil.isEmpty(userId)) {
            // 委托给OnlineUserManager处理
            if (Base.onlineUserManager != null) {
                Base.onlineUserManager.updateUserActivity(userId);
                log.debug("已更新用户[{}]的在线状态", userId);
            } else {
                log.warn("无法更新用户[{}]的在线状态：onlineUserManager未初始化", userId);
            }
        }
    }

    /**
     * 从会话中获取用户ID (sys.user)
     */
    private String getUserId(Session session) {
        try {
            // 首先尝试从会话中获取USER_ID_KEY属性
            String userId = session.getAttribute(USER_ID_KEY);
            if (userId != null && !userId.isEmpty()) {
                return userId;
            }

            // 如果没有，则尝试获取PRINCIPAL_NAME属性
            return session.getAttribute(FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME);
        } catch (Exception e) {
            log.warn("从会话中获取用户ID时出错", e);
            return null;
        }
    }
}