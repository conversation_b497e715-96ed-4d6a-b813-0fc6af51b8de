package com.wb.openplatform.login;

import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.json.JSONObject;

/**
 * 长连接，验证是否有扫码对接
 * <AUTHOR>
 *
 */
public class LongConnetionServlet {
	
	public static JSONObject ConnectionLong(HttpServletRequest request, HttpServletResponse response) {
		String uuid = request.getParameter("uuid");

		long inTime = new Date().getTime();
		Boolean bool = true;
		JSONObject codeObj = null;
		while (bool) {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			
			JSONObject userVo = LoginV.getLoginUserMap().get(uuid);
			if(userVo != null){
				bool = false;
				codeObj = new JSONObject();
				if(userVo.has("sm")) {
					codeObj.put("sm", "ok");
					if(new Date().getTime() - inTime >= 24000){
						bool = false;
						codeObj = new JSONObject();
						codeObj.put("code", "404");
						return codeObj;
					}
				}else {
					codeObj.put("code", userVo.getString("code"));
					codeObj.put("redirect", userVo.getString("redirect"));
					LoginV.getLoginUserMap().remove(uuid);
					return codeObj;
				}				
			}else{
				if(new Date().getTime() - inTime >= 24000){
					bool = false;
					codeObj = new JSONObject();
					codeObj.put("code", "404");
				}
			}
		}
		return codeObj;
	}
}
