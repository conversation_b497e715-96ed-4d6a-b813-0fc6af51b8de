package com.wb.cache;

import com.wb.util.LogUtil;
import com.wb.common.Base;
import com.wb.common.Var;

/**
 * Redis配置类 设置Redis客户端线程为守护线程
 */
public class RedisConfiguration {

    private static boolean initialized = false;
    private static boolean clusterModeEnabled = false;

    /**
     * 集群模式消息通道
     */
    public static final String CLUSTER_MODE_CHANNEL = "cluster:mode";

    /**
     * 集群模式状态键
     */
    public static final String CLUSTER_MODE_KEY = "redis:cluster:mode:status";

    /**
     * 初始化Redis配置
     */
    public static synchronized void init() {
        if (!initialized) {
            try {
                // 设置Redisson线程为守护线程
                System.setProperty("io.netty.threadFactory.daemon", "true"); // 设置Netty线程为守护线程
                System.setProperty("redisson.thread.daemon", "true"); // 设置Redisson线程为守护线程
                LogUtil.info("Redis客户端守护线程配置已设置");
                initialized = true;
            } catch (Exception e) {
                LogUtil.error("设置Redis客户端守护线程配置失败: " + e.getMessage());
            }
        }
    }

    /**
     * 从Redis存储中获取当前集群模式状态
     * 
     * @param redisTemplate Redis模板对象
     * @return 如果Redis中记录了启用集群模式则返回true，否则返回false
     */
    @SuppressWarnings("unchecked")
    public static boolean getClusterModeStatusFromRedis(
            org.springframework.data.redis.core.RedisTemplate<?, ?> redisTemplate) {
        try {
            if (redisTemplate != null) {
                Object value = redisTemplate.opsForValue().get(CLUSTER_MODE_KEY);
                return "enabled".equals(value);
            }
        } catch (Exception e) {
            LogUtil.warn("从Redis获取集群模式状态失败: " + e.getMessage());
        }
        return false;
    }

    /**
     * 将集群模式状态保存到Redis
     * 
     * @param redisTemplate Redis模板对象
     * @param enabled       是否启用集群模式
     * @return 是否保存成功
     */
    @SuppressWarnings("unchecked")
    public static boolean saveClusterModeStatusToRedis(
            org.springframework.data.redis.core.RedisTemplate<?, ?> redisTemplate, boolean enabled) {
        try {
            if (redisTemplate != null) {
                // 在这里进行强制类型转换，使其能够接受String类型的键值
                ((org.springframework.data.redis.core.RedisTemplate<String, Object>) redisTemplate).opsForValue()
                        .set(CLUSTER_MODE_KEY, enabled ? "enabled" : "disabled");
                return true;
            }
        } catch (Exception e) {
            LogUtil.warn("保存集群模式状态到Redis失败: " + e.getMessage());
        }
        return false;
    }

    /**
     * 检测并自动设置Redis集群模式
     * 
     * @param redisTemplate Redis模板对象
     */
    public static void detectAndSetClusterMode(org.springframework.data.redis.core.RedisTemplate<?, ?> redisTemplate) {
        // 如果传入的redisTemplate为空，直接返回
        if (redisTemplate == null || redisTemplate.getConnectionFactory() == null) {
            LogUtil.warn("检测Redis集群模式失败，RedisTemplate为空或ConnectionFactory未初始化");
            return;
        }

        // 使用分布式锁确保集群环境下只有一个实例执行初始化
        String lockKey = "REDIS_CLUSTER_MODE_INIT_LOCK";
        boolean lockAcquired = false;

        try {
            // 尝试获取RedisCache来使用分布式锁
            if (Base.map != null) {
                // 尝试获取分布式锁，等待5秒，锁有效期30秒
                lockAcquired = Base.map.tryLock(lockKey, 5000, 30000);

                if (!lockAcquired) {
                    if (Var.debug)
                        LogUtil.info("无法获取Redis集群初始化锁，其他实例可能正在执行初始化");
                    return;
                }
                if (Var.debug)
                    LogUtil.info("获取到Redis集群初始化锁，开始执行初始化");
            } else {
                LogUtil.warn("Base.map未初始化，无法使用分布式锁，可能会有多实例同时执行初始化");
            }

            // 实际的Redis集群检测逻辑
            // 首先执行实际检测，获取Redis当前环境状态
            String info = redisTemplate.getRequiredConnectionFactory().getConnection().info().toString();
            boolean isCluster = info.contains("cluster_enabled:1");

            // 获取Redis中存储的历史状态
            boolean redisStoredMode = getClusterModeStatusFromRedis(redisTemplate);

            // 如果检测结果与存储的状态不一致，以实际检测结果为准
            if (isCluster != redisStoredMode) {
                if (Var.debug)
                    LogUtil.info("Redis集群状态变更: 存储状态=" + (redisStoredMode ? "集群" : "标准") + ", 实际状态="
                            + (isCluster ? "集群" : "标准"));
                // 更新Redis中存储的状态
                saveClusterModeStatusToRedis(redisTemplate, isCluster);
            }

            // 根据实际检测结果设置当前状态
            if (isCluster && !clusterModeEnabled) {
                // 检测到集群模式，更新本地设置
                clusterModeEnabled = true;
                if (Var.debug)
                    LogUtil.info("已自动检测并启用Redis集群兼容模式");

                // 由检测的节点发出一次广播通知
                publishClusterModeChange(redisTemplate, "enable");
            } else if (!isCluster && clusterModeEnabled) {
                // 检测到非集群模式，但当前设置为集群模式，更新设置
                clusterModeEnabled = false;
                if (Var.debug)
                    LogUtil.info("已自动检测并禁用Redis集群兼容模式");

                // 由检测的节点发出一次广播通知
                publishClusterModeChange(redisTemplate, "disable");
            } else {
                if (Var.debug)
                    LogUtil.info("Redis使用" + (isCluster ? "集群" : "标准") + "模式");
            }
        } catch (Exception e) {
            LogUtil.warn("检测Redis集群模式失败，默认使用标准模式: " + e.getMessage());
        } finally {
            // 如果成功获取了锁，需要释放
            if (lockAcquired && Base.map != null) {
                try {
                    Base.map.unLock(lockKey);
                    if (Var.debug)
                        LogUtil.info("Redis集群初始化完成，已释放锁");
                } catch (Exception e) {
                    LogUtil.error("释放Redis集群初始化锁失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 启用Redis集群兼容模式，并发布消息通知集群中的其他节点
     */
    public static synchronized void enableClusterMode() {
        if (!clusterModeEnabled) {
            clusterModeEnabled = true;
            LogUtil.info("本地启用Redis集群兼容模式");

            // 不主动发布消息，由调用方决定是否发布
        }
    }

    /**
     * 启用Redis集群兼容模式，并可选是否发布通知消息
     * 
     * @param publish       是否发布通知消息
     * @param redisTemplate Redis模板对象，如果发布消息则必须提供
     */
    public static synchronized void enableClusterMode(boolean publish,
            org.springframework.data.redis.core.RedisTemplate<?, ?> redisTemplate) {
        if (!clusterModeEnabled) {
            clusterModeEnabled = true;
            LogUtil.info("本地启用Redis集群兼容模式");

            // 保存状态到Redis
            if (redisTemplate != null) {
                saveClusterModeStatusToRedis(redisTemplate, true);
            }

            // 是否发布通知消息
            if (publish && redisTemplate != null) {
                publishClusterModeChange(redisTemplate, "enable");
            }
        }
    }

    /**
     * 禁用Redis集群兼容模式，并发布消息通知集群中的其他节点
     */
    public static synchronized void disableClusterMode() {
        if (clusterModeEnabled) {
            clusterModeEnabled = false;
            LogUtil.info("本地禁用Redis集群兼容模式");

            // 不主动发布消息，由调用方决定是否发布
        }
    }

    /**
     * 禁用Redis集群兼容模式，并可选是否发布通知消息
     * 
     * @param publish       是否发布通知消息
     * @param redisTemplate Redis模板对象，如果发布消息则必须提供
     */
    public static synchronized void disableClusterMode(boolean publish,
            org.springframework.data.redis.core.RedisTemplate<?, ?> redisTemplate) {
        if (clusterModeEnabled) {
            clusterModeEnabled = false;
            LogUtil.info("本地禁用Redis集群兼容模式");

            // 保存状态到Redis
            if (redisTemplate != null) {
                saveClusterModeStatusToRedis(redisTemplate, false);
            }

            // 是否发布通知消息
            if (publish && redisTemplate != null) {
                publishClusterModeChange(redisTemplate, "disable");
            }
        }
    }

    /**
     * 发布集群模式变更消息，通知集群中的其他节点
     * 
     * @param redisTemplate Redis模板对象
     * @param action        操作类型："enable"或"disable"
     */
    private static void publishClusterModeChange(org.springframework.data.redis.core.RedisTemplate<?, ?> redisTemplate,
            String action) {
        try {
            if (redisTemplate != null) {
                com.alibaba.fastjson.JSONObject message = new com.alibaba.fastjson.JSONObject();
                message.put("action", action);
                message.put("timestamp", System.currentTimeMillis());
                message.put("server", com.wb.util.SysUtil.getServerId());

                redisTemplate.convertAndSend(CLUSTER_MODE_CHANNEL, message);
                LogUtil.info("已发布集群模式" + (action.equals("enable") ? "启用" : "禁用") + "消息");
            } else {
                LogUtil.warn("无法发布集群模式消息，Redis模板为null");
            }
        } catch (Exception e) {
            LogUtil.error("发布集群模式消息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查是否启用了集群兼容模式
     * 
     * @return 如果启用了集群兼容模式则返回true
     */
    public static boolean isClusterModeEnabled() {
        return clusterModeEnabled;
    }
}