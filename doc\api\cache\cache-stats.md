# 缓存统计功能使用文档

本文档介绍Redis缓存系统的统计功能，用于监控和分析缓存使用情况。

## 功能概览

Redis缓存统计系统提供以下功能：

- 获取缓存命中率
- 获取缓存操作次数
- 查看本地缓存使用情况
- 获取缓存错误数
- 重置统计数据

## Java 用法

在Java代码中，可以直接使用`Base.map`访问缓存统计功能：

```java
// 获取缓存统计信息
Map<String, Object> stats = Base.map.getCacheStats();

// 打印详细的统计信息
LogUtil.info("缓存操作总数: " + stats.get("operationCount"));
LogUtil.info("缓存命中次数: " + stats.get("hitCount"));
LogUtil.info("缓存未命中次数: " + stats.get("missCount"));
LogUtil.info("缓存命中率: " + stats.get("hitRate") + "%");
LogUtil.info("本地缓存命中次数: " + stats.get("localHitCount"));
LogUtil.info("本地缓存大小: " + stats.get("localCacheSize"));
LogUtil.info("加载次数: " + stats.get("loadCount"));
LogUtil.info("错误次数: " + stats.get("errorCount"));

// 重置统计数据
Base.map.resetCacheStats();

// 获取本地缓存大小
int localCacheSize = Base.map.getLocalCacheSize();

// 清理过期的本地缓存项
Base.map.cleanupLocalCache();

// 完全清空本地缓存
Base.map.clearLocalCache();
```

## XWL 脚本用法

在XWL脚本中，直接使用`Base.map`访问缓存统计功能：

```javascript
// 获取缓存统计信息
var stats = Base.map.getCacheStats();

// 打印详细的统计信息
println("缓存操作总数: " + stats.get("operationCount"));
println("缓存命中次数: " + stats.get("hitCount"));
println("缓存未命中次数: " + stats.get("missCount"));
println("缓存命中率: " + stats.get("hitRate") + "%");
println("本地缓存命中次数: " + stats.get("localHitCount"));
println("本地缓存大小: " + stats.get("localCacheSize"));
println("加载次数: " + stats.get("loadCount"));
println("错误次数: " + stats.get("errorCount"));

// 重置统计数据
Base.map.resetCacheStats();

// 获取本地缓存大小
var localCacheSize = Base.map.getLocalCacheSize();

// 清理过期的本地缓存项
Base.map.cleanupLocalCache();

// 完全清空本地缓存
Base.map.clearLocalCache();
```

## 使用场景

### 性能监控

```java
// 定期监控缓存性能
@Scheduled(fixedRate = 3600000) // 每小时执行一次
public void monitorCachePerformance() {
    Map<String, Object> stats = Base.map.getCacheStats();
    double hitRate = (double) stats.get("hitRate");
    long operationCount = (long) stats.get("operationCount");
    
    LogUtil.info("===== 缓存性能报告 =====");
    LogUtil.info("操作总数: " + operationCount);
    LogUtil.info("命中率: " + hitRate + "%");
    
    // 如果命中率过低，记录警告
    if (hitRate < 50.0 && operationCount > 1000) {
        LogUtil.warn("缓存命中率过低，建议优化缓存策略");
    }
    
    // 如果本地缓存大小接近上限，进行清理
    int localCacheSize = Base.map.getLocalCacheSize();
    if (localCacheSize > 800) { // 接近默认1000上限的80%
        LogUtil.info("本地缓存大小接近上限，执行清理");
        Base.map.cleanupLocalCache();
    }
    
    // 重置统计，开始新一轮统计
    Base.map.resetCacheStats();
}
```

### 缓存问题诊断

```java
// 诊断缓存问题
public void diagnoseCacheIssues() {
    Map<String, Object> stats = Base.map.getCacheStats();
    long errorCount = (long) stats.get("errorCount");
    long missCount = (long) stats.get("missCount");
    long hitCount = (long) stats.get("hitCount");
    
    if (errorCount > 0) {
        LogUtil.error("检测到 " + errorCount + " 个缓存操作错误");
    }
    
    // 检查是否存在缓存穿透问题（大量的未命中）
    if (hitCount > 0 && missCount > hitCount * 2) {
        LogUtil.warn("可能存在缓存穿透问题，建议使用布隆过滤器");
    }
}
```

## 注意事项

1. 统计数据存储在内存中，应用重启后将重置
2. 频繁获取统计数据可能影响性能，建议在非关键路径上使用
3. 重置统计数据后，之前的统计信息将不可恢复
4. 本地缓存大小受限于`LOCAL_CACHE_MAX_SIZE`配置（默认1000项）
5. 监控缓存命中率有助于优化缓存策略和提高应用性能 