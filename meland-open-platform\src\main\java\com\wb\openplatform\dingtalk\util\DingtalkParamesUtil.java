package com.wb.openplatform.dingtalk.util;

import com.wb.common.Var;

/**
 * 钉钉参数
 * 
 * <AUTHOR>
 *
 */
public class DingtalkParamesUtil {
	/**
	 * 钉钉扫码应用AppId
	 */
	public static String smAppId = Var.getString("sys.config.dingtalk.smAppId");
	/**
	 * 钉钉扫码应用密匙appSecret
	 */
	public static String smAppSecret = Var.getString("sys.config.dingtalk.smAppSecret");
	/**
	 * app应用AgentId
	 */
	public static int AgentId = Var.getInt("sys.config.dingtalk.AgentId");
	/**
	 * 钉钉app应用AppSecret
	 */
	public static String AppSecret = Var.getString("sys.config.dingtalk.AppSecret");
	/**
	 * 钉钉app应用密匙AppKey
	 */
	public static String AppKey = Var.getString("sys.config.dingtalk.AppKey");
	/**
	 * 钉钉corpId
	 */
	public static String corpId = Var.getString("sys.config.dingtalk.CorpId");
	/**
	 * 钉钉SSOSecret
	 */
	public static String SSOSecret = Var.getString("sys.config.dingtalk.SSOSecret");
	/**
	 * 获取请求token地址
	 */
	public final static String tokenSM = "SM"; // 扫码地址
	public final static String tokenDT = "DT"; // 数据交换地址
	public final static String tokenDD = "DD"; // 应用地址

	public static void init() {
		/**
		 * 钉钉扫码应用AppId
		 */
		smAppId = Var.getString("sys.config.dingtalk.smAppId");
		/**
		 * 钉钉扫码应用密匙appSecret
		 */
		smAppSecret = Var.getString("sys.config.dingtalk.smAppSecret");
		/**
		 * app应用AgentId
		 */
		AgentId = Var.getInt("sys.config.dingtalk.AgentId");
		/**
		 * 钉钉app应用AppSecret
		 */
		AppSecret = Var.getString("sys.config.dingtalk.AppSecret");
		/**
		 * 钉钉app应用密匙AppKey
		 */
		AppKey = Var.getString("sys.config.dingtalk.AppKey");
		/**
		 * 钉钉corpId
		 */
		corpId = Var.getString("sys.config.dingtalk.CorpId");
		/**
		 * 钉钉SSOSecret
		 */
		SSOSecret = Var.getString("sys.config.dingtalk.SSOSecret");
	}
}
