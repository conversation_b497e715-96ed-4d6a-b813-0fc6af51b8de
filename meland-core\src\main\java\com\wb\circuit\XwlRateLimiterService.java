package com.wb.circuit;

import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;
import com.wb.util.WebUtil;
import com.wb.common.Base;
import com.wb.common.Var;
import com.wb.tool.Encrypter;

import org.json.JSONObject;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * XWL模块限流服务 提供基于Redis的分布式限流功能，防止系统过载
 */
public class XwlRateLimiterService {
    private static final Logger LOGGER = LoggerFactory.getLogger(XwlRateLimiterService.class);

    // Redis键前缀
    private static final String PREFIX = "ratelimiter:xwl:";

    // 单例实例
    private static XwlRateLimiterService instance;

    // 限流配置
    private Map<String, Integer> rateLimitConfigs = new ConcurrentHashMap<>();

    // 构造方法私有化
    private XwlRateLimiterService() {
        // 加载限流配置
        loadRateLimitConfigurations();
    }

    // 获取单例实例
    public static synchronized XwlRateLimiterService getInstance() {
        if (instance == null) {
            instance = new XwlRateLimiterService();
        }
        return instance;
    }

    /**
     * 加载限流配置
     */
    public void loadRateLimitConfigurations() {
        try {
            String configJson = (String) Base.map.getValue("rate:limit:config");
            if (!StringUtil.isEmpty(configJson)) {
                JSONObject config = new JSONObject(configJson);
                Iterator<String> keys = config.keys();

                // 清空当前配置并重新加载
                rateLimitConfigs.clear();

                while (keys.hasNext()) {
                    String xwlPath = keys.next();
                    int qpsLimit = config.getInt(xwlPath);
                    rateLimitConfigs.put(xwlPath, qpsLimit);
                }
                LogUtil.info(StringUtil.format("已从Redis加载限流配置，共{0}条规则", rateLimitConfigs.size()));
            }
        } catch (Exception e) {
            LogUtil.error("加载限流配置失败", e);
        }
    }

    /**
     * 检查请求是否超过限流阈值
     * 
     * @param xwlPath XWL模块路径
     * @return 是否允许请求通过
     */
    public boolean checkRateLimit(String xwlPath) {
        // 如果没有配置限流，直接允许通过
        Integer qpsLimit = rateLimitConfigs.get(xwlPath);
        if (qpsLimit == null) {
            return true;
        }

        // 复用WebUtil中的限流功能
        String key = "xwl:" + Encrypter.getMD5ForScript(xwlPath);
        return WebUtil.limitRateKey(key, qpsLimit, 1);
    }

    /**
     * 获取所有限流配置
     */
    public Map<String, Integer> getAllRateLimitConfigurations() {
        return new HashMap<>(rateLimitConfigs);
    }

    /**
     * 保存限流配置
     */
    public void saveRateLimitConfiguration(String xwlPath, int qpsLimit) {
        // 删除Redis中对应的限流器，无论是更新还是删除操作
        try {
            RedissonClient redissonClient = Base.map.getRedissonClient();
            String key = PREFIX + Encrypter.getMD5ForScript(xwlPath);
            RRateLimiter limiter = redissonClient.getRateLimiter(key);
            if (limiter.isExists()) {
                limiter.delete();
            }
        } catch (Exception e) {
            LogUtil.error(StringUtil.format("删除限流器失败:{0}", e.getMessage()), e);
        }

        // 更新本地配置
        updateLocalConfig(xwlPath, qpsLimit);

        // 保存到Redis
        try {
            // 获取当前配置版本
            long configVersion = getConfigVersion();
            // 版本号加1
            configVersion++;

            // 更新配置
            JSONObject config = new JSONObject();
            for (Map.Entry<String, Integer> entry : rateLimitConfigs.entrySet()) {
                config.put(entry.getKey(), entry.getValue());
            }

            // 保存配置和版本号
            Base.map.setValue("rate:limit:config", config.toString());
            Base.map.setValue("rate:limit:config:version", String.valueOf(configVersion));

            // 发布配置更新通知，用于集群同步
            com.alibaba.fastjson.JSONObject notification = new com.alibaba.fastjson.JSONObject();
            notification.put("action", "update");
            notification.put("server", SysUtil.getServerId());
            notification.put("timestamp", System.currentTimeMillis());
            notification.put("target", xwlPath);
            notification.put("qpsLimit", qpsLimit);
            notification.put("type", "ratelimit");
            notification.put("configVersion", configVersion);
            Base.map.publish("rate:limit:config:updated", notification);

            LogUtil.info(StringUtil.format("保存限流配置成功，已发布更新通知 (版本：{0})", configVersion));

            // 发布确认消息，确保配置已经更新成功
            publishConfigUpdateConfirmation(xwlPath, qpsLimit, configVersion);
        } catch (Exception e) {
            LogUtil.error(StringUtil.format("保存限流配置失败:{0}", e.getMessage()), e);
        }
    }

    /**
     * 获取配置版本号
     */
    private long getConfigVersion() {
        try {
            String versionStr = (String) Base.map.getValue("rate:limit:config:version");
            if (!StringUtil.isEmpty(versionStr)) {
                return Long.parseLong(versionStr);
            }
        } catch (Exception e) {
            LogUtil.warn("获取限流配置版本失败: " + e.getMessage());
        }
        return 0;
    }

    /**
     * 发布配置更新确认消息
     */
    private void publishConfigUpdateConfirmation(String xwlPath, int qpsLimit, long configVersion) {
        try {
            // 延迟1秒发送确认消息，确保所有节点都已收到更新
            Thread.sleep(1000);

            com.alibaba.fastjson.JSONObject confirmation = new com.alibaba.fastjson.JSONObject();
            confirmation.put("action", "confirm");
            confirmation.put("server", SysUtil.getServerId());
            confirmation.put("timestamp", System.currentTimeMillis());
            confirmation.put("target", xwlPath);
            confirmation.put("qpsLimit", qpsLimit);
            confirmation.put("configVersion", configVersion);
            confirmation.put("type", "ratelimit");

            // 发布确认消息
            Base.map.publish("rate:limit:config:confirmed", confirmation);

            if (Var.debug) {
                LogUtil.info(
                        StringUtil.format("已发送限流配置更新确认消息: 模块[{0}], QPS={1}, 版本={2}", xwlPath, qpsLimit, configVersion));
            }
        } catch (Exception e) {
            LogUtil.warn("发送限流配置更新确认消息失败: " + e.getMessage());
        }
    }

    /**
     * 删除限流配置
     * 
     * @param xwlPath XWL模块路径
     */
    public void deleteRateLimitConfiguration(String xwlPath) {
        if (rateLimitConfigs.containsKey(xwlPath)) {
            rateLimitConfigs.remove(xwlPath);

            // 删除Redis中对应的限流器
            try {
                RedissonClient redissonClient = Base.map.getRedissonClient();
                String key = PREFIX + Encrypter.getMD5ForScript(xwlPath);
                RRateLimiter limiter = redissonClient.getRateLimiter(key);
                if (limiter.isExists()) {
                    limiter.delete();
                }
            } catch (Exception e) {
                LogUtil.error(StringUtil.format("删除限流器失败:{0}", e.getMessage()), e);
            }

            // 更新Redis中的配置
            try {
                JSONObject config = new JSONObject();
                for (Map.Entry<String, Integer> entry : rateLimitConfigs.entrySet()) {
                    config.put(entry.getKey(), entry.getValue());
                }

                Base.map.setValue("rate:limit:config", config.toString());

                // 发布配置更新通知，用于集群同步
                com.alibaba.fastjson.JSONObject notification = new com.alibaba.fastjson.JSONObject();
                notification.put("action", "delete");
                notification.put("server", SysUtil.getServerId());
                notification.put("timestamp", System.currentTimeMillis());
                notification.put("target", xwlPath);
                notification.put("type", "ratelimit");
                Base.map.publish("rate:limit:config:updated", notification);

                LogUtil.info(StringUtil.format("已删除模块[{0}]的限流配置并同步到集群", xwlPath));
            } catch (Exception e) {
                LogUtil.error(StringUtil.format("更新限流配置失败:{0}", e.getMessage()), e);
            }
        }
    }

    /**
     * 更新本地限流配置（支持增量更新）
     * 
     * @param xwlPath  模块路径
     * @param qpsLimit QPS限制值
     */
    public void updateLocalConfig(String xwlPath, int qpsLimit) {
        if (qpsLimit <= 0) {
            rateLimitConfigs.remove(xwlPath);
            LogUtil.info(StringUtil.format("已移除限流配置: 模块[{0}]", xwlPath));
        } else {
            rateLimitConfigs.put(xwlPath, qpsLimit);
            LogUtil.info(StringUtil.format("已更新限流配置: 模块[{0}], QPS限制={1}", xwlPath, qpsLimit));
        }
    }

    /**
     * 从本地配置中移除限流配置
     * 
     * @param xwlPath 模块路径
     */
    public void removeLocalConfig(String xwlPath) {
        if (rateLimitConfigs.remove(xwlPath) != null) {
            LogUtil.info(StringUtil.format("已删除限流配置: 模块[{0}]", xwlPath));
        }
    }
}