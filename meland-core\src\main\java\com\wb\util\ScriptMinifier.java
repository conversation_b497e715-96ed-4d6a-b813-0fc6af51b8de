package com.wb.util;

import com.google.javascript.jscomp.CompilationLevel;
import com.google.javascript.jscomp.CompilerOptions;
import com.google.javascript.jscomp.Result;
import com.google.javascript.jscomp.SourceFile;
import com.wb.common.Base;
import com.wb.tool.Encrypter;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * JavaScript脚本压缩工具
 * 支持高并发下的脚本压缩，使用编译器池和缓存机制优化性能
 */
public class ScriptMinifier {
    private static final Logger LOGGER = LoggerFactory.getLogger(ScriptMinifier.class);
    
    // 缓存键前缀
    private static final String CACHE_PREFIX = "script_minifier:";
    // 根据CPU核心数确定编译器池大小
    private static final int COMPILER_POOL_SIZE = Runtime.getRuntime().availableProcessors();
    // 编译器池
    private static final BlockingQueue<com.google.javascript.jscomp.Compiler> compilerPool;
    // 缓存过期时间（毫秒）
    private static final long CACHE_EXPIRE_TIME = 3600 * 1000; // 1小时
    
    // 初始化编译器池
    static {
        compilerPool = new ArrayBlockingQueue<>(COMPILER_POOL_SIZE);
        for (int i = 0; i < COMPILER_POOL_SIZE; i++) {
            compilerPool.offer(createCompiler());
        }
        LOGGER.info("JavaScript脚本压缩工具初始化完成，编译器池大小：{}", COMPILER_POOL_SIZE);
    }
    
    /**
     * 创建JavaScript编译器实例
     */
    private static com.google.javascript.jscomp.Compiler createCompiler() {
        com.google.javascript.jscomp.Compiler compiler = new com.google.javascript.jscomp.Compiler();
        CompilerOptions options = new CompilerOptions();
        options.setLanguageOut(CompilerOptions.LanguageMode.ECMASCRIPT5);
        // 设置压缩级别
        CompilationLevel.SIMPLE_OPTIMIZATIONS.setOptionsForCompilationLevel(options);
        compiler.initOptions(options);
        return compiler;
    }
    
    /**
     * 压缩JavaScript脚本
     * @param script 原始脚本
     * @return 压缩后的脚本
     */
    public static String minify(String script) {
        if (script == null || script.trim().isEmpty()) {
            return script;
        }
        
        // 计算脚本的MD5作为缓存key
        String md5 = Encrypter.getMD5ForScript(script);
        if (md5 == null) {
            // 如果MD5计算失败，使用hashCode作为备选
            md5 = String.valueOf(script.hashCode());
        }
        String cacheKey = CACHE_PREFIX + md5;
        
        // 使用Base.map进行缓存管理并保证原子性
        return Base.map.getWithMutex(cacheKey, () -> compressScript(script), CACHE_EXPIRE_TIME / 1000, TimeUnit.SECONDS);
    }
    
    /**
     * 执行实际的脚本压缩
     */
    private static String compressScript(String script) {
        // 检测是否是ExtJS配置片段
        if (isExtJSConfigFragment(script)) {
            // 对于ExtJS配置片段，只进行简单的空白字符移除
            if (LOGGER.isDebugEnabled()) {
                String minified = removeWhitespace(script);
                double ratio = 100.0 * (1 - (double)minified.length() / script.length());
                LOGGER.debug("ExtJS配置片段简单压缩 - 压缩率: {}%", String.format("%.2f", ratio));
                return minified;
            }
            return removeWhitespace(script);
        }
        
        com.google.javascript.jscomp.Compiler compiler = null;
        try {
            // 从池中获取compiler
            compiler = compilerPool.poll(500, TimeUnit.MILLISECONDS);
            
            // 如果无法获取到编译器，创建一个临时的
            if (compiler == null) {
                LOGGER.warn("无法从池中获取编译器，创建临时编译器");
                compiler = createCompiler();
            }
            
            SourceFile extern = SourceFile.fromCode("extern.js", "");
            SourceFile input = SourceFile.fromCode("input.js", script);
            Result result = compiler.compile(extern, input, compiler.getOptions());
            
            String minified = result.success ? compiler.toSource() : script;
            
            // 记录压缩率
            if (result.success && LOGGER.isDebugEnabled()) {
                double ratio = 100.0 * (1 - (double)minified.length() / script.length());
                LOGGER.debug("脚本压缩率: {}%", String.format("%.2f", ratio));
            }
            
            return minified;
        } catch (Exception e) {
            LOGGER.error("脚本压缩失败", e);
            return script;
        } finally {
            // 归还compiler到池中
            if (compiler != null) {
                try {
                    compilerPool.offer(compiler);
                } catch (Exception e) {
                    LOGGER.error("归还编译器到池中失败", e);
                }
            }
        }
    }
    
    /**
     * 检测代码是否为ExtJS配置片段
     * @param script 脚本内容
     * @return 是否为ExtJS配置片段
     */
    private static boolean isExtJSConfigFragment(String script) {
        String trimmed = script.trim();
        // 检查是否以{开头但不是完整的JS语句
        if (trimmed.startsWith("{") && !trimmed.endsWith(";")) {
            // 进一步检查是否包含ExtJS常见属性
            return trimmed.contains("xtype:") || 
                   trimmed.contains("items:") || 
                   trimmed.contains("listeners:") ||
                   trimmed.contains("appScope:");
        }
        // 检查是否以[开头但不是完整的JS语句
        if (trimmed.startsWith("[") && !trimmed.endsWith(";")) {
            return true;
        }
        return false;
    }
    
    /**
     * 简单的空白字符移除
     * @param script 原始脚本
     * @return 移除多余空白后的脚本
     */
    private static String removeWhitespace(String script) {
        // 移除注释
        String result = script.replaceAll("//.*?(?:\r\n|\n|$)", "")  // 单行注释
                              .replaceAll("/\\*[\\s\\S]*?\\*/", "");  // 多行注释 - 使用[\\s\\S]匹配包括换行符在内的所有字符
        
        // 压缩空白字符
        result = result.replaceAll("\\s+", " ")  // 多个空白替换为单个空格
                       .replaceAll("\\s*:\\s*", ":")  // 冒号前后空白移除
                       .replaceAll("\\s*,\\s*", ",")  // 逗号前后空白移除
                       .replaceAll("\\s*\\{\\s*", "{")  // 大括号前后空白移除
                       .replaceAll("\\s*\\}\\s*", "}")  // 大括号前后空白移除
                       .replaceAll("\\s*\\[\\s*", "[")  // 方括号前后空白移除
                       .replaceAll("\\s*\\]\\s*", "]")  // 方括号前后空白移除
                       .replaceAll("\\s*\\(\\s*", "(")  // 小括号前后空白移除
                       .replaceAll("\\s*\\)\\s*", ")")  // 小括号前后空白移除
                       .replaceAll(";\\s*", ";")  // 分号后空白移除
                       .trim();  // 首尾空白移除
        
        return result;
    }
} 