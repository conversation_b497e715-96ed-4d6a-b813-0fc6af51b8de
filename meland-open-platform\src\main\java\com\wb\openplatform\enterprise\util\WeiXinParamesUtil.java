package com.wb.openplatform.enterprise.util;

import com.wb.common.Var;

/**
 * 微信参数
 * 
 * <AUTHOR>
 * @date 20180614
 */
public class WeiXinParamesUtil {
	// 1.微信参数
	// token
	public static String token = Var.getString("sys.config.wechat.Token");
	// EncodingAESKey
	public static String encodingAESKey = Var.getString("sys.config.wechat.EncodingAESKey");
	// 企业ID
	public static String corpId = Var.getString("sys.config.wechat.AppId");
	// 应用的凭证密钥
	public static String agentSecret = Var.getString("sys.config.wechat.CorpSecret");
	// 通讯录秘钥
	public static String contactsSecret = Var.getString("sys.config.wechat.AddressBookSecret");
	// 企业应用的id，整型。可在应用的设置页面查看
	public static int agentId = Var.getInt("sys.config.wechat.AgentId");

	public static void init() {
		// token
		token = Var.getString("sys.config.wechat.Token");
		// EncodingAESKey
		encodingAESKey = Var.getString("sys.config.wechat.EncodingAESKey");
		// 企业ID
		corpId = Var.getString("sys.config.wechat.AppId");
		// 应用的凭证密钥
		agentSecret = Var.getString("sys.config.wechat.CorpSecret");
		// 通讯录秘钥
		contactsSecret = Var.getString("sys.config.wechat.AddressBookSecret");
		// 企业应用的id，整型。可在应用的设置页面查看
		agentId = Var.getInt("sys.config.wechat.AgentId");
	}
}
