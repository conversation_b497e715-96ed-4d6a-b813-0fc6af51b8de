<?xml version="1.0"  encoding="UTF-8"?>
<!DOCTYPE Configure PUBLIC "-//Jetty//Configure//EN" "https://www.eclipse.org/jetty/configure.dtd">
<Configure class="org.eclipse.jetty.webapp.WebAppContext">
    <New class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg>java:comp/env/jdbc/meland</Arg>
        <Arg>
            <New class="com.mysql.jdbc.jdbc2.optional.MysqlDataSource">
                <Set name="Url">*******************************************************************************************************************************************************************************************************
                </Set>
                <Set name="User">dev</Set>
                <Set name="Password">7RaP*vT5!Jxdzv</Set>
            </New>
        </Arg>
    </New>
    <New class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg>java:comp/env/jdbc/meland_r</Arg>
        <Arg>
            <New class="com.mysql.jdbc.jdbc2.optional.MysqlDataSource">
                <Set name="Url">*******************************************************************************************************************************************************************************************************
                </Set>
                <Set name="User">dev</Set>
                <Set name="Password">7RaP*vT5!Jxdzv</Set>
            </New>
        </Arg>
    </New>
    <New class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg>java:comp/env/jdbc/adb</Arg>
        <Arg>
            <New class="com.mysql.jdbc.jdbc2.optional.MysqlDataSource">
                <Set name="Url">*******************************************************************************************************************************************************************************************************
                </Set>
                <Set name="User">dev</Set>
                <Set name="Password">7RaP*vT5!Jxdzv</Set>
            </New>
        </Arg>
    </New>
    <New class="org.eclipse.jetty.plus.jndi.Resource">
        <Arg>java:comp/env/jdbc/report</Arg>
        <Arg>
            <New class="com.mysql.jdbc.jdbc2.optional.MysqlDataSource">
                <Set name="Url">*******************************************************************************************************************************************************************************************************
                </Set>
                <Set name="User">dev</Set>
                <Set name="Password">7RaP*vT5!Jxdzv</Set>
            </New>
        </Arg>
    </New>
</Configure>