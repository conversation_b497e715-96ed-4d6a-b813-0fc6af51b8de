package aliyun.mqtt.test;

import com.wb.aliyun.mqtt.MessageClient;

/**
 * <AUTHOR>
 * @date 2023/12/15-12:39
 */
public class testMessageC {
    public static void main(String[] args) throws Exception {
        MessageClient messageClient = MessageClient.getMessageClient("post-cn-omn3rs5sk01",
                "post-cn-omn3rs5sk01.mqtt.aliyuncs.com",
                "onsmqtt.cn-shenzhen.aliyuncs.com",
                "LTAI4FjhWy6j1yrRiue9dFrT",
                "******************************",
                false,
                "GID_SELL_COIN_TEST@@@214P6DW4W4712",
                true);
        System.out.println(messageClient);
        //P2P模式无需订阅topic
//        messageClient.subscribeTopic("T_SELL_COIN/p2p/GID_SELL_COIN@@@E8F40839F1C5", 1);
//        messageClient.disconnect();
        System.out.println(messageClient.isConnected());
    }
}
