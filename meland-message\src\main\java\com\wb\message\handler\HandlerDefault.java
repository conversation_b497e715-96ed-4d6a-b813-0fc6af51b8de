package com.wb.message.handler;

import java.io.IOException;

import org.json.JSONObject;

import com.wb.message.Handler;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import com.wb.util.WebUtil;

public class HandlerDefault extends Handler {

	public HandlerDefault(String title, String content, String users, String data, JSONObject arg) {
		super(title, content, users, data, arg);
	}

	@Override
	public void handlerMessage() {
		for (String user : users) {
			//消息中心
			JSONObject message = new JSONObject();
			message.put("module", "m?xwl=my/message");
			message.put("title", title);
			message.put("msg", content);
			message.put("count", 1);
			try {
				WebUtil.send(user, "sys.home", message.toString());
			} catch (IOException e) {
				LogUtil.error(StringUtil.format("推送消息[{0}]异常：{1}", message, e));
			}
		}
	}

}
