package com.wb.common;

import com.wb.util.DbUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 键值数据缓存
 */
public class KVBuffer {

	/**
	 * 键值数据缓存。
	 */
	public static ConcurrentHashMap<String, ConcurrentHashMap<Object, String>> buffer;

	/**
	 * 重新加载键值数据
	 *
	 * @param conn    数据库连接对象
	 * @param keyName 键名称
	 * @param type    值类型(1-数值,2-字符)
	 * @throws Exception 异常
	 */
	public static void reloadKey(Connection conn, String keyName, int type) throws Exception {
		PreparedStatement st = null;
		ResultSet rs = null;
		ConcurrentHashMap<Object, String> map = new ConcurrentHashMap<Object, String>();
		try {
			st = conn.prepareStatement(
					"select a.K,a.V from WB_KEY a, WB_KEY_TREE b where a.KEY_ID=b.KEY_ID and b.KEY_NAME=? order by b.KEY_NAME");
			st.setString(1, keyName);
			rs = st.executeQuery();
			while (rs.next()) {
				String k = rs.getString("K");
				map.put(type == 1 ? k : Integer.parseInt(k), rs.getString("V"));
			}
			if (map.isEmpty())
				buffer.remove(keyName);
			else
				buffer.put(keyName, map);

			// 删除相关的状态过滤缓存，确保下次查询能获取最新数据
			deleteStatusFilterCache(keyName);
		} finally {
			DbUtil.close(rs);
			DbUtil.close(st);
		}
		// 同步通知其他服务器处理
		com.alibaba.fastjson.JSONObject chatParams = new com.alibaba.fastjson.JSONObject();
		chatParams.put("type", "reload");
		chatParams.put("KEY_NAME", keyName);
		chatParams.put("KV_TYPE", type);
		chatParams.put("server", SysUtil.getServerId());
		Base.map.publish("chat_kv", chatParams);
	}

	/**
	 * 删除指定键名的状态过滤缓存
	 *
	 * @param keyName 键名称
	 */
	public static void deleteStatusFilterCache(String keyName) {
		if (Base.map != null) {
			// 删除启用状态缓存
			Base.map.deleteWithLocalCache("KV_STATUS_FILTER:" + keyName + ":1");
			// 删除禁用状态缓存
			Base.map.deleteWithLocalCache("KV_STATUS_FILTER:" + keyName + ":0");
		}
	}

	/**
	 * 获取指定键值的键值项列表数组组成的字符串。
	 *
	 * @param keyName 键名称。
	 * @return 键值项列表。
	 */
	public static String getList(String keyName) {
		return getList(keyName, "K", "V");
	}

	/**
	 * 获取指定键值及状态过滤的键值项列表数组组成的字符串。
	 *
	 * @param keyName      键名称。
	 * @param statusFilter 状态过滤（1-启用, 0-禁用）。
	 * @return 键值项列表。
	 */
	public static String getList(String keyName, Integer statusFilter) {
		return getList(keyName, "K", "V", statusFilter);
	}

	/**
	 * 获取指定键值的键值项列表数组组成的字符串。
	 *
	 * @param keyName 键名称。
	 * @param k       键。
	 * @param v       值。
	 * @return 键值项列表。
	 */
	public static String getList(String keyName, String k, String v) {
		ConcurrentHashMap<Object, String> map = buffer.get(keyName);
		if (map == null)
			return "[]";
		StringBuilder buf = new StringBuilder();
		Set<Entry<Object, String>> es = map.entrySet();
		boolean isFirst = true;

		buf.append("[");
		for (Entry<Object, String> e : es) {
			if (isFirst)
				isFirst = false;
			else
				buf.append(",");
			buf.append("{\"");
			buf.append(k);
			buf.append("\":");
			Object K = e.getKey();
			if ((K instanceof Integer))
				buf.append(Integer.toString(((Integer) K).intValue()));
			else
				buf.append(StringUtil.quote((String) K));
			buf.append(",\"");
			buf.append(v);
			buf.append("\":");
			String V = (String) e.getValue();
			if (V.startsWith("@"))
				V = V.substring(1);
			else
				V = StringUtil.quote(V);
			buf.append(V);
			buf.append("}");
		}
		buf.append("]");
		return buf.toString();
	}

	/**
	 * 获取指定键值及状态过滤的键值项列表数组组成的字符串。 此方法使用Redis缓存结果，避免频繁查询数据库。
	 *
	 * @param keyName      键名称。
	 * @param k            键。
	 * @param v            值。
	 * @param statusFilter 状态过滤（1-启用, 0-禁用）。
	 * @return 键值项列表。
	 */
	public static String getList(String keyName, String k, String v, Integer statusFilter) {
		// 如果没有状态过滤，使用常规缓存方法
		if (statusFilter == null) {
			return getList(keyName, k, v);
		}

		// 状态过滤使用Redis缓存
		if (Base.map != null) {
			// 构建缓存键
			final String cacheKey = "KV_STATUS_FILTER:" + keyName + ":" + statusFilter;

			// 使用Redis的getWithMutex方法，带互斥锁避免缓存击穿
			return Base.map.getWithMutex(cacheKey, () -> queryStatusFilteredList(keyName, k, v, statusFilter), 30, // 缓存30分钟
					TimeUnit.MINUTES);
		} else {
			// 如果Redis不可用，直接查询数据库
			return queryStatusFilteredList(keyName, k, v, statusFilter);
		}
	}

	/**
	 * 从数据库查询带状态过滤的键值列表
	 * 
	 * @param keyName      键名称。
	 * @param k            键。
	 * @param v            值。
	 * @param statusFilter 状态过滤（1-启用, 0-禁用）。
	 * @return 键值项列表字符串。
	 */
	private static String queryStatusFilteredList(String keyName, String k, String v, Integer statusFilter) {
		StringBuilder buf = new StringBuilder();
		buf.append("[");

		Connection conn = null;
		PreparedStatement st = null;
		ResultSet rs = null;

		try {
			conn = DbUtil.getConnection();

			st = conn.prepareStatement(
					"select a.K, a.V,b.TYPE from WB_KEY a join WB_KEY_TREE b on a.KEY_ID=b.KEY_ID where b.KEY_NAME=? and a.KV_STATUS=? order by a.K");
			st.setString(1, keyName);
			st.setInt(2, statusFilter);

			rs = st.executeQuery();
			boolean isFirst = true;

			while (rs.next()) {
				if (isFirst)
					isFirst = false;
				else
					buf.append(",");

				buf.append("{\"");
				buf.append(k);
				buf.append("\":");

				String K = rs.getString("K");
				if (rs.getInt("TYPE") == 1) {
					buf.append(StringUtil.quote(K));
				} else {
					try {
						int intK = Integer.parseInt(K);
						buf.append(intK);
					} catch (NumberFormatException ex) {
						buf.append(StringUtil.quote(K));
					}
				}

				buf.append(",\"");
				buf.append(v);
				buf.append("\":");

				String V = rs.getString("V");
				if (V.startsWith("@"))
					V = V.substring(1);
				else
					V = StringUtil.quote(V);

				buf.append(V);
				buf.append("}");
			}
		} catch (Exception e) {
			// 查询失败则返回空数组
			return "[]";
		} finally {
			DbUtil.close(rs);
			DbUtil.close(st);
			DbUtil.close(conn);
		}

		buf.append("]");
		return buf.toString();
	}

	/**
	 * 加载和初始化。
	 */
	public static synchronized void load() {
		try {
			buffer = new ConcurrentHashMap<String, ConcurrentHashMap<Object, String>>();
			Connection conn = null;
			Statement st = null;
			ResultSet rs = null;
			String keyName = null;
			String preKeyName = null;
			ConcurrentHashMap<Object, String> map = new ConcurrentHashMap<Object, String>();
			try {
				conn = DbUtil.getConnection();
				st = conn.createStatement();
				rs = st.executeQuery(
						"select a.K,a.V,b.KEY_NAME,b.TYPE from WB_KEY a, WB_KEY_TREE b where a.KEY_ID=b.KEY_ID order by b.KEY_NAME");
				while (rs.next()) {
					keyName = rs.getString("KEY_NAME");
					if ((preKeyName != null) && (!preKeyName.equals(keyName))) {
						buffer.put(preKeyName, map);
						map = new ConcurrentHashMap<Object, String>();
					}
					String K = rs.getString("K");
					map.put(rs.getInt("TYPE") == 1 ? K : Integer.valueOf(Integer.parseInt(K)), rs.getString("V"));
					preKeyName = keyName;
				}
				if (preKeyName != null)
					buffer.put(preKeyName, map);
			} finally {
				DbUtil.close(rs);
				DbUtil.close(st);
				DbUtil.close(conn);
			}
		} catch (Throwable e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 获取指定键对应的值。
	 *
	 * @param map 键值对照map。
	 * @param key 键。
	 * @return 键对应的字符串值。
	 */
	public static String getValue(ConcurrentHashMap<?, ?> map, Object key) {
		if (key == null)
			return null;
		Object value;
		if ((key instanceof Number))
			value = map.get(Integer.valueOf(((Number) key).intValue()));
		else
			value = map.get(key.toString());
		if (value == null) {
			return key.toString();
		}
		return value.toString();
	}

	/**
	 * 获取指定键名称对应键的值。
	 *
	 * @param keyName 键值对照map。
	 * @param key     键。
	 * @return 键对应的字符串值。
	 */
	public static String getValue(String keyName, Object key) {
		ConcurrentHashMap<Object, String> kv = buffer.get(keyName);
		if (kv == null)
			return null;
		return getValue(kv, key);
	}

	/**
	 * 通过键值表中的值找到对应的键
	 *
	 * @param keyName 键值对类别
	 * @param value   值
	 * @return 键
	 */
	public static Object getByValue(String keyName, String value) {
		value = value == null ? "" : value;
		ConcurrentHashMap<Object, String> map = buffer.get(keyName);
		if (map == null) {
			return null;
		}
		if (!map.containsValue(value)) {
			return null;
		}
		Object v = null;
		for (Entry<Object, String> entry : map.entrySet()) {
			if (value.equals(entry.getValue())) {
				v = entry.getKey();
				break;
			}
		}
		return v;
	}

	/**
	 * 通过键值表中的值找到对应的键
	 *
	 * @param keyName 键值对类别
	 * @param key     键
	 * @return 值
	 */
	public static Object getByKey(String keyName, Object key) {
		key = key == null ? "" : key;
		ConcurrentHashMap<Object, String> map = buffer.get(keyName);
		if (map == null) {
			return null;
		}
		if (!map.containsKey(key)) {
			return null;
		}
		Object v = null;
		for (Entry<Object, String> entry : map.entrySet()) {
			if (key.equals(entry.getKey())) {
				v = entry.getValue();
				break;
			}
		}
		return v;
	}
}
