<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId><PERSON><PERSON><PERSON><PERSON></groupId>
	<artifactId>meland-dependencies</artifactId>
	<version>1.0.0</version>
	<packaging>pom</packaging>
	<name>meland-dependencies</name>
	<description>乐的文化平台依赖管理</description>

	<properties>
		<!-- 基础配置 -->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
		<java.version>1.8</java.version>
		
		<!-- Spring 相关 -->
		<spring.framework.version>5.3.39</spring.framework.version>
		<spring-data-redis.version>2.7.11</spring-data-redis.version>
		<spring-session-data-redis.version>2.7.3</spring-session-data-redis.version>
		<spring-retry.version>1.3.3</spring-retry.version>
		
		<!-- 日志组件 -->
		<slf4j.version>1.7.36</slf4j.version>
		<log4j.version>2.17.2</log4j.version>
		
		<!-- JSON处理 -->
		<fastjson.version>1.2.83</fastjson.version>
		
		<!-- Apache Commons -->
		<commons-collections4.version>4.4</commons-collections4.version>
		<commons-io.version>2.11.0</commons-io.version>
		<commons-compress.version>1.23.0</commons-compress.version>
		<commons-logging.version>1.2</commons-logging.version>
		<commons-codec.version>1.15</commons-codec.version>
		<commons-net.version>3.8.0</commons-net.version>
		<commons-fileupload.version>1.5</commons-fileupload.version>
		<commons-lang.version>2.6</commons-lang.version>
		<commons-lang3.version>3.12.0</commons-lang3.version>
		
		<!-- HTTP相关 -->
		<httpclient.version>4.5.13</httpclient.version>
		
		<!-- 缓存与Redis -->
		<redisson.version>3.17.6</redisson.version>
		<jedis.version>3.7.0</jedis.version>
		
		<!-- 数据库相关 -->
		<mysql-connector.version>5.1.49</mysql-connector.version>
		<druid.version>1.2.8</druid.version>
		
		<!-- XML处理 -->
		<dom4j.version>2.1.3</dom4j.version>
		<xmlbeans.version>3.1.0</xmlbeans.version>
		<xmlsec.version>2.2.3</xmlsec.version>
		
		<!-- Servlet/Web相关 -->
		<servlet-api.version>4.0.1</servlet-api.version>
		<websocket-api.version>1.1</websocket-api.version>
		
		<!-- Office文档处理 -->
		<poi.version>4.1.2</poi.version>
		
		<!-- WebService -->
		<axis2.version>1.7.9</axis2.version>
		
		<!-- 容器 -->
		<jetty.version>9.4.48.v20220622</jetty.version>
		
		<!-- 测试相关 -->
		<junit.version>4.13.1</junit.version>
		<mockito.version>3.12.4</mockito.version>
		
		<!-- 存储相关 -->
		<tika-core.version>1.28.5</tika-core.version>
		<hutool.version>5.8.18</hutool.version>
		
		<!-- 定时任务 -->
		<quartz.version>2.3.2</quartz.version>
		
		<!-- 工具类库 -->
		<jsoup.version>1.15.4</jsoup.version>
		<zxing.version>3.3.0</zxing.version>
		<jaxen.version>1.2.0</jaxen.version>
		<htmlcompressor.version>1.5.2</htmlcompressor.version>
		<closure-compiler.version>v20190415</closure-compiler.version>
		<mqtt.version>1.2.5</mqtt.version>
		<owasp-encoder.version>1.3.1</owasp-encoder.version>
		<protobuf-java.version>3.16.3</protobuf-java.version>
		<sax.version>2.0.1</sax.version>
		<xerces.version>2.12.2</xerces.version>
		<boilerpipe.version>1.2.1</boilerpipe.version>
		<javax.mail.version>1.6.2</javax.mail.version>
		<java-diff-utils.version>4.12</java-diff-utils.version>
		
		<!-- 阿里云SDK -->
		<aliyun-slb.version>2.0.1</aliyun-slb.version>
		<aliyun-rocketmq.version>1.3.0</aliyun-rocketmq.version>
		<aliyun-mq-http-sdk.version>*******</aliyun-mq-http-sdk.version>
		<aliyun-tairjedis-sdk.version>2.1.0</aliyun-tairjedis-sdk.version>
		<aliyun-log-producer.version>0.3.10</aliyun-log-producer.version>
		<aliyun-log.version>0.6.57</aliyun-log.version>
		<aliyun-tea-console.version>0.0.1</aliyun-tea-console.version>
		<aliyun-tea-util.version>0.2.11</aliyun-tea-util.version>
		<aliyun-darabonba-env.version>0.1.1</aliyun-darabonba-env.version>
		<aliyun-onsmqtt.version>1.0.0</aliyun-onsmqtt.version>
		<aliyun-tea-openapi.version>0.0.13</aliyun-tea-openapi.version>
		<aliyun-tea.version>1.3.2</aliyun-tea.version>
		<aliyun-java-sdk-onsmqtt.version>1.0.3</aliyun-java-sdk-onsmqtt.version>
		<aliyun-java-sdk-core.version>4.5.0</aliyun-java-sdk-core.version>
		<aliyun-java-sdk-ecs.version>4.11.0</aliyun-java-sdk-ecs.version>
		<aliyun-dms-enterprise.version>1.53.11</aliyun-dms-enterprise.version>
		<aliyun-java-auth.version>0.1.13-beta</aliyun-java-auth.version>
		<aliyun-darabonba-java-core.version>0.1.13-beta</aliyun-darabonba-java-core.version>
		
		<!-- 其他工具库 -->
		<yuicompressor.version>2.4.8</yuicompressor.version>
		<concurrentlinkedhashmap-lru.version>1.4.2</concurrentlinkedhashmap-lru.version>
		<alibaba-mqtt-server-sdk.version>1.0.0.Final</alibaba-mqtt-server-sdk.version>
		<rocketmq-client-java.version>5.0.6</rocketmq-client-java.version>
		
		<!-- 微信SDK -->
		<weixin-popular.version>2.8.31</weixin-popular.version>
		
		<!-- 支付相关SDK -->
		<alipay-sdk-java.version>4.38.197.ALL</alipay-sdk-java.version>
		<alipay-sdk-java-v3.version>2.7.2.ALL</alipay-sdk-java-v3.version>
		<taglibs-standard.version>1.2.5</taglibs-standard.version>
		<bcprov-jdk15on.version>1.70</bcprov-jdk15on.version>
		
		<!-- Jackson统一版本 -->
		<jackson.version>2.13.3</jackson.version>
		
		<!-- 项目内部模块版本 -->
		<meland-json.version>1.0.0</meland-json.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<!-- ==================== -->
			<!-- 内部模块依赖管理 -->
			<!-- ==================== -->
			<dependency>
				<groupId>lonlysky</groupId>
				<artifactId>meland-json</artifactId>
				<version>${meland-json.version}</version>
			</dependency>
		
			<!-- ==================== -->
			<!-- Spring Framework相关 -->
			<!-- ==================== -->
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-framework-bom</artifactId>
				<version>${spring.framework.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			
			<!-- Spring Data Redis -->
			<dependency>
				<groupId>org.springframework.data</groupId>
				<artifactId>spring-data-redis</artifactId>
				<version>${spring-data-redis.version}</version>
			</dependency>
			
			<!-- Spring Session Redis -->
			<dependency>
				<groupId>org.springframework.session</groupId>
				<artifactId>spring-session-data-redis</artifactId>
				<version>${spring-session-data-redis.version}</version>
			</dependency>
			
			<!-- Spring Session Core -->
			<dependency>
				<groupId>org.springframework.session</groupId>
				<artifactId>spring-session-core</artifactId>
				<version>${spring-session-data-redis.version}</version>
			</dependency>
			
			<!-- Spring Retry -->
			<dependency>
				<groupId>org.springframework.retry</groupId>
				<artifactId>spring-retry</artifactId>
				<version>${spring-retry.version}</version>
			</dependency>
			
			<!-- 明确声明Spring核心依赖，避免"used undeclared dependencies"警告 -->
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-context</artifactId>
				<version>${spring.framework.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-core</artifactId>
				<version>${spring.framework.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-tx</artifactId>
				<version>${spring.framework.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-web</artifactId>
				<version>${spring.framework.version}</version>
			</dependency>
			
			<!-- ============= -->
			<!-- 缓存与Redis相关 -->
			<!-- ============= -->
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>${jedis.version}</version>
			</dependency>
			
			<dependency>
				<groupId>org.redisson</groupId>
				<artifactId>redisson</artifactId>
				<version>${redisson.version}</version>
			</dependency>
			
			<!-- ========== -->
			<!-- JSON处理相关 -->
			<!-- ========== -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			
			<!-- 移除此处的直接依赖，避免循环依赖 -->
			
			<!-- Jackson相关 -->
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-core</artifactId>
				<version>${jackson.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-databind</artifactId>
				<version>${jackson.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-annotations</artifactId>
				<version>${jackson.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.dataformat</groupId>
				<artifactId>jackson-dataformat-yaml</artifactId>
				<version>${jackson.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.dataformat</groupId>
				<artifactId>jackson-dataformat-xml</artifactId>
				<version>${jackson.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.datatype</groupId>
				<artifactId>jackson-datatype-jsr310</artifactId>
				<version>${jackson.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.module</groupId>
				<artifactId>jackson-module-parameter-names</artifactId>
				<version>${jackson.version}</version>
			</dependency>
			
			<!-- ========== -->
			<!-- 数据库相关组件 -->
			<!-- ========== -->
			<!-- MySQL驱动 -->
			<dependency>
				<groupId>mysql</groupId>
				<artifactId>mysql-connector-java</artifactId>
				<version>${mysql-connector.version}</version>
			</dependency>
			
			<!-- 数据库连接池 -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid</artifactId>
				<version>${druid.version}</version>
			</dependency>
			
			<!-- ================== -->
			<!-- Apache Commons工具库 -->
			<!-- ================== -->
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>${commons-collections4.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>${commons-io.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-compress</artifactId>
				<version>${commons-compress.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-logging</groupId>
				<artifactId>commons-logging</artifactId>
				<version>${commons-logging.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-codec</groupId>
				<artifactId>commons-codec</artifactId>
				<version>${commons-codec.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-net</groupId>
				<artifactId>commons-net</artifactId>
				<version>${commons-net.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>${commons-fileupload.version}</version>
			</dependency>
			
			<!-- 添加缺失的commons-lang3依赖 -->
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-lang3</artifactId>
				<version>${commons-lang3.version}</version>
			</dependency>
			
			<!-- 添加commons-lang依赖 -->
			<dependency>
				<groupId>commons-lang</groupId>
				<artifactId>commons-lang</artifactId>
				<version>${commons-lang.version}</version>
			</dependency>
			
			<!-- ========== -->
			<!-- HTTP客户端相关 -->
			<!-- ========== -->
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpclient</artifactId>
				<version>${httpclient.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpcore</artifactId>
				<version>4.4.13</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpmime</artifactId>
				<version>4.5.13</version>
			</dependency>
			<dependency>
				<groupId>com.squareup.okhttp3</groupId>
				<artifactId>okhttp</artifactId>
				<version>4.9.3</version>
			</dependency>
			
			<!-- ========== -->
			<!-- XML处理相关 -->
			<!-- ========== -->
			<dependency>
				<groupId>org.dom4j</groupId>
				<artifactId>dom4j</artifactId>
				<version>${dom4j.version}</version>
				<exclusions>
					<exclusion>
						<groupId>xml-apis</groupId>
						<artifactId>xml-apis</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.apache.xmlbeans</groupId>
				<artifactId>xmlbeans</artifactId>
				<version>${xmlbeans.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.santuario</groupId>
				<artifactId>xmlsec</artifactId>
				<version>${xmlsec.version}</version>
			</dependency>
			<dependency>
				<groupId>xml-apis</groupId>
				<artifactId>xml-apis</artifactId>
				<version>1.4.01</version>
			</dependency>
			
			<!-- ========== -->
			<!-- 日志组件相关 -->
			<!-- ========== -->
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-bom</artifactId>
				<version>${log4j.version}</version>
				<scope>import</scope>
				<type>pom</type>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-api</artifactId>
				<version>${slf4j.version}</version>
			</dependency>
			
			<!-- ============= -->
			<!-- Servlet API相关 -->
			<!-- ============= -->
			<dependency>
				<groupId>javax.servlet</groupId>
				<artifactId>javax.servlet-api</artifactId>
				<version>${servlet-api.version}</version>
			</dependency>
			<dependency>
				<groupId>javax.servlet</groupId>
				<artifactId>servlet-api</artifactId>
				<version>2.3</version>
			</dependency>
			<dependency>
				<groupId>javax.websocket</groupId>
				<artifactId>javax.websocket-api</artifactId>
				<version>${websocket-api.version}</version>
			</dependency>
			
			<!-- ============= -->
			<!-- Office文档处理相关 -->
			<!-- ============= -->
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-excelant</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-scratchpad</artifactId>
				<version>${poi.version}</version>
			</dependency>
			
			<!-- ============= -->
			<!-- 对象存储相关 -->
			<!-- ============= -->
			<dependency>
				<groupId>org.apache.tika</groupId>
				<artifactId>tika-core</artifactId>
				<version>${tika-core.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-core</artifactId>
				<version>${hutool.version}</version>
			</dependency>
			
			<!-- ============= -->
			<!-- 定时任务相关 -->
			<!-- ============= -->
			<dependency>
				<groupId>org.quartz-scheduler</groupId>
				<artifactId>quartz</artifactId>
				<version>${quartz.version}</version>
			</dependency>
			
			<!-- ============= -->
			<!-- 二维码相关 -->
			<!-- ============= -->
			<dependency>
				<groupId>com.google.zxing</groupId>
				<artifactId>core</artifactId>
				<version>${zxing.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.zxing</groupId>
				<artifactId>javase</artifactId>
				<version>${zxing.version}</version>
			</dependency>
			
			<!-- ============= -->
			<!-- HTML解析相关 -->
			<!-- ============= -->
			<dependency>
				<groupId>org.jsoup</groupId>
				<artifactId>jsoup</artifactId>
				<version>${jsoup.version}</version>
			</dependency>
			
			<!-- ============= -->
			<!-- 测试框架相关 -->
			<!-- ============= -->
			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>${junit.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.mockito</groupId>
				<artifactId>mockito-core</artifactId>
				<version>${mockito.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.mockito</groupId>
				<artifactId>mockito-junit-jupiter</artifactId>
				<version>${mockito.version}</version>
				<scope>test</scope>
			</dependency>
			
			<!-- ============= -->
			<!-- WebService相关 -->
			<!-- ============= -->
			<dependency>
				<groupId>org.apache.axis2</groupId>
				<artifactId>axis2</artifactId>
				<version>${axis2.version}</version>
				<type>pom</type>
			</dependency>
			<dependency>
				<groupId>org.apache.axis2</groupId>
				<artifactId>axis2-adb</artifactId>
				<version>${axis2.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.axis2</groupId>
				<artifactId>axis2-kernel</artifactId>
				<version>${axis2.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.axis2</groupId>
				<artifactId>axis2-transport-http</artifactId>
				<version>${axis2.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.axis2</groupId>
				<artifactId>axis2-transport-local</artifactId>
				<version>${axis2.version}</version>
			</dependency>
			
			<!-- ============= -->
			<!-- 阿里云SDK相关 -->
			<!-- ============= -->
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>slb20140515</artifactId>
				<version>${aliyun-slb.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>rocketmq20220801</artifactId>
				<version>${aliyun-rocketmq.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun.mq</groupId>
				<artifactId>mq-http-sdk</artifactId>
				<version>${aliyun-mq-http-sdk.version}</version>
				<classifier>jar-with-dependencies</classifier>
			</dependency>
			<dependency>
				<groupId>com.aliyun.tair</groupId>
				<artifactId>alibabacloud-tairjedis-sdk</artifactId>
				<version>${aliyun-tairjedis-sdk.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun.openservices</groupId>
				<artifactId>aliyun-log-producer</artifactId>
				<version>${aliyun-log-producer.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun.openservices</groupId>
				<artifactId>aliyun-log</artifactId>
				<version>${aliyun-log.version}</version>
				<classifier>jar-with-dependencies</classifier>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>tea-console</artifactId>
				<version>${aliyun-tea-console.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>tea-util</artifactId>
				<version>${aliyun-tea-util.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>darabonba-env</artifactId>
				<version>${aliyun-darabonba-env.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>onsmqtt20200420</artifactId>
				<version>${aliyun-onsmqtt.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>tea-openapi</artifactId>
				<version>${aliyun-tea-openapi.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>tea</artifactId>
				<version>${aliyun-tea.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>aliyun-java-sdk-onsmqtt</artifactId>
				<version>${aliyun-java-sdk-onsmqtt.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>aliyun-java-sdk-core</artifactId>
				<version>${aliyun-java-sdk-core.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>aliyun-java-sdk-ecs</artifactId>
				<version>${aliyun-java-sdk-ecs.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>alibabacloud-dms_enterprise20181101</artifactId>
				<version>${aliyun-dms-enterprise.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.aliyun</groupId>
						<artifactId>aliyun-http-apache</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>aliyun-java-auth</artifactId>
				<version>${aliyun-java-auth.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>darabonba-java-core</artifactId>
				<version>${aliyun-darabonba-java-core.version}</version>
			</dependency>
			
			<!-- ============= -->
			<!-- 其他工具库相关 -->
			<!-- ============= -->
			<dependency>
				<groupId>jaxen</groupId>
				<artifactId>jaxen</artifactId>
				<version>${jaxen.version}</version>
			</dependency>
			<dependency>
				<groupId>com.googlecode.htmlcompressor</groupId>
				<artifactId>htmlcompressor</artifactId>
				<version>${htmlcompressor.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.javascript</groupId>
				<artifactId>closure-compiler</artifactId>
				<version>${closure-compiler.version}</version>
			</dependency>
			<dependency>
				<groupId>org.eclipse.paho</groupId>
				<artifactId>org.eclipse.paho.client.mqttv3</artifactId>
				<version>${mqtt.version}</version>
			</dependency>
			<dependency>
				<groupId>org.owasp.encoder</groupId>
				<artifactId>encoder</artifactId>
				<version>${owasp-encoder.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.protobuf</groupId>
				<artifactId>protobuf-java</artifactId>
				<version>${protobuf-java.version}</version>
			</dependency>
			<dependency>
				<groupId>sax</groupId>
				<artifactId>sax</artifactId>
				<version>${sax.version}</version>
			</dependency>
			<dependency>
				<groupId>xerces</groupId>
				<artifactId>xercesImpl</artifactId>
				<version>${xerces.version}</version>
			</dependency>
			<dependency>
				<groupId>com.syncthemall</groupId>
				<artifactId>boilerpipe</artifactId>
				<version>${boilerpipe.version}</version>
			</dependency>
			<dependency>
				<groupId>com.sun.mail</groupId>
				<artifactId>javax.mail</artifactId>
				<version>${javax.mail.version}</version>
			</dependency>
			<dependency>
				<groupId>com.yahoo.platform.yui</groupId>
				<artifactId>yuicompressor</artifactId>
				<version>${yuicompressor.version}</version>
			</dependency>
			<dependency>
				<groupId>com.googlecode.concurrentlinkedhashmap</groupId>
				<artifactId>concurrentlinkedhashmap-lru</artifactId>
				<version>${concurrentlinkedhashmap-lru.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.mqtt</groupId>
				<artifactId>server-sdk</artifactId>
				<version>${alibaba-mqtt-server-sdk.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.rocketmq</groupId>
				<artifactId>rocketmq-client-java</artifactId>
				<version>${rocketmq-client-java.version}</version>
			</dependency>
			
			<!-- ============= -->
			<!-- 微信SDK -->
			<!-- ============= -->
			<dependency>
				<groupId>com.github.liyiorg</groupId>
				<artifactId>weixin-popular</artifactId>
				<version>${weixin-popular.version}</version>
			</dependency>
			
			<!-- ============= -->
			<!-- 支付相关SDK -->
			<!-- ============= -->
			<dependency>
				<groupId>com.alipay.sdk</groupId>
				<artifactId>alipay-sdk-java</artifactId>
				<version>${alipay-sdk-java.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alipay.sdk</groupId>
				<artifactId>alipay-sdk-java-v3</artifactId>
				<version>${alipay-sdk-java-v3.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.taglibs</groupId>
				<artifactId>taglibs-standard-impl</artifactId>
				<version>${taglibs-standard.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.taglibs</groupId>
				<artifactId>taglibs-standard-spec</artifactId>
				<version>${taglibs-standard.version}</version>
			</dependency>
			<dependency>
				<groupId>org.bouncycastle</groupId>
				<artifactId>bcprov-jdk15on</artifactId>
				<version>${bcprov-jdk15on.version}</version>
			</dependency>
			
			<!-- ============= -->
			<!-- 工具类 -->
			<!-- ============= -->
			<dependency>
				<groupId>io.github.java-diff-utils</groupId>
				<artifactId>java-diff-utils</artifactId>
				<version>${java-diff-utils.version}</version>
			</dependency>
			
			<!-- Google相关工具库 -->
			<dependency>
				<groupId>com.google.code.gson</groupId>
				<artifactId>gson</artifactId>
				<version>2.8.2</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>27.0-jre</version>
			</dependency>
			
			<!-- 添加注解API -->
			<dependency>
				<groupId>org.apache.tomcat</groupId>
				<artifactId>annotations-api</artifactId>
				<version>6.0.53</version>
			</dependency>
			<dependency>
				<groupId>org.jetbrains</groupId>
				<artifactId>annotations</artifactId>
				<version>13.0</version>
			</dependency>
			
			<!-- ============= -->
			<!-- Java兼容性相关 -->
			<!-- ============= -->
			<dependency>
				<groupId>javax.xml.bind</groupId>
				<artifactId>jaxb-api</artifactId>
				<version>2.3.1</version>
			</dependency>
			<dependency>
				<groupId>com.sun.xml.bind</groupId>
				<artifactId>jaxb-core</artifactId>
				<version>2.3.0</version>
			</dependency>
			<dependency>
				<groupId>com.sun.xml.bind</groupId>
				<artifactId>jaxb-impl</artifactId>
				<version>2.3.0</version>
			</dependency>
			<dependency>
				<groupId>javax.activation</groupId>
				<artifactId>javax.activation-api</artifactId>
				<version>1.2.0</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<version>3.2.1</version>
				<executions>
					<execution>
						<id>enforce-java</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<rules>
								<requireJavaVersion>
									<version>[1.8,9)</version>
								</requireJavaVersion>
								<requireMavenVersion>
									<version>[3.5.0,)</version>
								</requireMavenVersion>
							</rules>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.10.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project> 