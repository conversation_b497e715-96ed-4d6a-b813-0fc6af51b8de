package com.wb.message.handler;

import java.util.List;

import org.json.JSONArray;
import org.json.JSONObject;

import com.wb.message.Handler;
import com.wb.message.util.ParamUtil;
import com.wb.openplatform.enterprise.util.WeiXinParamesUtil;
import com.wb.openplatform.enterprise.util.WeiXinUtil;
import com.wb.util.DbUtil;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;

public class HandlerQy extends Handler {

	public HandlerQy(String title, String content, String users,String data,JSONObject arg) {
		super(title, content, users,data,arg);
	}

	//接收者的用户列长度
	private static final Integer PAGE_SIZE = 1000;
	private static final String url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=ACCESS_TOKEN";

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public void handlerMessage() {
		//存放所有用户
		String user = StringUtil.joinQuote(this.users);
		if (user != "") {
			//查询用户的企业微信ID
			String sql = "select ifnull(qy_id,qysmall_id) as qy_id from  wb_user_weixin where user_id in (" + user + ")  and (qy_id <> '' or qysmall_id <> '') ";
			
			JSONArray array = DbUtil.query(sql);

			//总记录数
			Integer totalCount = array.length();
			if(totalCount <= 0) {
				LogUtil.warn(StringUtil.format("消息[{0}] 微信企业----没有用户",  title));
				System.err.println("企业微信----没有用户");
				return;
			}
			//分多少次处理
			Integer requestCount = totalCount / PAGE_SIZE;
			for (int i = 0; i <= requestCount; i++) {
				Integer fromIndex = i * PAGE_SIZE;
				//如果总数少于PAGE_SIZE,为了防止数组越界,toIndex直接使用totalCount即可
				int toIndex = Math.min(totalCount, (i + 1) * PAGE_SIZE);
				List subList = array.toList().subList(fromIndex, toIndex);
				String userList = String.join("|", subList);
				sendMessage(userList);
				//总数不到一页或者刚好等于一页的时候,只需要处理一次就可以退出for循环了
				if (toIndex == totalCount) {
					break;
				}
			}
		} else {
			LogUtil.warn(StringUtil.format("消息[{0}] 微信企业----没有用户",  title));
			System.err.println("企业微信----没有用户");
		}
	}


	/**
	 * 发送消息
	 * @param users
	 */
	public void sendMessage(String users) {
		//获取accessToken
		String accessToken = WeiXinUtil.getAccessToken(WeiXinParamesUtil.corpId, WeiXinParamesUtil.agentSecret, "SQ").getToken();

		// 重写参数
		JSONObject Arg_ = new JSONObject(this.arg.toString());
		JSONObject param = new JSONObject(this.data);
		JSONArray array = ParamUtil.getSmallParams(param, Arg_, title);
		
		String content = StringUtil.format(" >**{0}** \n >", this.title);
		for (Object obj : array) {
			JSONObject prm = (JSONObject) obj;
			content += StringUtil.format("\n > <font color='comment'>{0}</font> : {1}",prm.getString("key"),prm.getString("value"));
		}
					
		//创建JSON参数
		JSONObject obj = new JSONObject();
		obj.put("touser", users);
		obj.put("toparty", "");
		obj.put("totag", "");
		obj.put("agentid", WeiXinParamesUtil.agentId);
		obj.put("msgtype", "markdown");
		//消息
		JSONObject objc = new JSONObject();
		objc.put("content", content);
		obj.put("markdown", objc);
		obj.put("safe", 0);

		//发生地址
		String msgUrl = url.replace("ACCESS_TOKEN", accessToken);
		JSONObject jsonObject = WeiXinUtil.httpRequest(msgUrl, "POST", obj.toString());
		if (jsonObject.has("errcode")) {
			if (0 != jsonObject.getInt("errcode")) {
				LogUtil.warn(StringUtil.format("消息[{0}] 微信企业----发送失败,错误代码【[{1}]】",title ,jsonObject.getInt("errcode")));
				System.err.println("微信企业----发送失败,错误代码【" + jsonObject.getInt("errcode") + "】");
			} else {
				LogUtil.warn(StringUtil.format("消息[{0}] 微信企业----发送成功",  title));
				System.out.println("微信企业----发送成功");
			}
		}
	}

}
