package com.wb.future;

import java.util.concurrent.*;

import com.wb.util.LogUtil;
import com.wb.util.SysUtil;
import org.json.JSONObject;

import com.wb.common.ScriptBuffer;
import com.wb.util.StringUtil;

public class AsyncMultiThreadUtil {

    private static final int CORE_POOL_SIZE  = 5;
    private static final int MAXIMUM_POOL_SIZE = 20;
    private static final long KEEP_ALIVE_TIME = 60L;
    private static BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<Runnable>(100);
    
    // 添加线程工厂，自定义线程名称
    private static ThreadFactory threadFactory = new ThreadFactory() {
        private int count = 0;
        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r);
            thread.setName("AsyncThread-" + count++);
            // 设置为守护线程，不阻止JVM退出
            thread.setDaemon(true);
            return thread;
        }
    };
    
    // 添加拒绝策略，当队列满时使用调用者所在的线程执行任务
    private static RejectedExecutionHandler rejectedExecutionHandler = new ThreadPoolExecutor.CallerRunsPolicy();

    //线程池
    private static ThreadPoolExecutor executorService = new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAXIMUM_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TimeUnit.SECONDS,
            workQueue,
            threadFactory,
            rejectedExecutionHandler);


    @SuppressWarnings("unused")
	public static void send(String scriptText, JSONObject params) {
        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                if(!StringUtil.isEmpty(scriptText)){
                    try {
                        ScriptBuffer.run(scriptText,params) ;
                    } catch (Exception e) {
                        LogUtil.error(StringUtil.concat("执行线程异常：：", SysUtil.getRootError(e),"，参数：",params.toString()));
                        e.printStackTrace();
                    }
                }
                return "执行多线程完成" ;
        },executorService) ;
    }

    public static ThreadPoolExecutor getExecutorService(){
        return executorService;
    }
    
    /**
     * 关闭线程池，等待任务完成
     * @param timeout 等待时间
     * @param unit 时间单位
     */
    public static void shutdown(long timeout, TimeUnit unit) {
        try {
            // 不再接受新任务，等待已有任务完成
            executorService.shutdown();
            // 等待所有任务完成或超时
            if (!executorService.awaitTermination(timeout, unit)) {
                // 超时后强制关闭
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            // 当前线程被中断时，强制关闭线程池
            executorService.shutdownNow();
            // 保持中断状态
            Thread.currentThread().interrupt();
            LogUtil.error("关闭线程池时被中断: " + e.getMessage());
        }
    }
    
    /**
     * 立即关闭线程池，不等待任务完成
     */
    public static void shutdownNow() {
        executorService.shutdownNow();
    }
}
