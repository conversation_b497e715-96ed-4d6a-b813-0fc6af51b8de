var Cropper=function(){"use strict";function t(t){if(!t)throw Error("Node is not passed or invalid selector.");var e=t;if("string"==typeof e&&!(e=document.querySelector(t)))throw Error("Invalid selector.");if(!(e instanceof window.HTMLElement))throw Error("Node should be instance of window.HTMLElement or valid selector string.");return e}function e(t){if(!t)throw Error("Config is not passed or invalid.");if("[object Object]"!==Object.prototype.toString.call(t))throw Error("Invalid config.")}function n(t){if(!t&&0!==t)throw Error("Dimension is not passed or invalid.");if("number"!=typeof t)throw Error("Invalid dimension.");if(!isFinite(t))throw Error("Invalid dimension.");if(t<0)throw Error("Invalid dimension.");return t}function i(t){if(void 0===t)return function(){};if(!t||"function"!=typeof t)throw Error("Invalid callback.");return t}function r(){throw new Error("Dynamic requires are not currently supported by rollup-plugin-commonjs")}var o={width:560,height:340},s={cutout:{fill:"rgba(0, 0, 0, 0.3)"},pattern:{size:16,fill1:"rgb(215, 215, 215)",fill2:"rgb(250, 250, 250)"}},a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},h=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},u=function(){function t(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}return function(e,n,i){return n&&t(e.prototype,n),i&&t(e,i),e}}(),c=function t(e,n,i){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,n);if(void 0===r){var o=Object.getPrototypeOf(e);return null===o?void 0:t(o,n,i)}if("value"in r)return r.value;var s=r.get;if(void 0!==s)return s.call(i)},l=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)},f=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e},_=function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;h(this,t),this.width=e,this.height=n},d=function(){function t(e){if(h(this,t),this._node=e,!e||"string"==typeof e){if("svg"===e||"use"===e)return void(this._node=document.createElementNS("http://www.w3.org/2000/svg",e));this._node=document.createElement(e||"div")}}return u(t,[{key:"render",value:function(t){if(!t)throw Error("Parent node are not passed.");return t.appendChild(this._node),this}},{key:"setWidth",value:function(t){return this._node.width=t,this}},{key:"setHeight",value:function(t){return this._node.height=t,this}},{key:"getSize",value:function(){return new _(this._node.width,this._node.height)}},{key:"getNode",value:function(){return this._node}},{key:"getContext2d",value:function(){return this._node.getContext("2d")}},{key:"setType",value:function(t){return this._node.type=t,this}},{key:"addClass",value:function(t){return this._node.className+=this._node.className.length>1?" "+t:t,this}},{key:"setAttribute",value:function(t,e){return this._node.setAttribute(t,e),this}}]),t}(),g="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},v=function(t,e){return e={exports:{}},t(e,e.exports),e.exports}(function(t,e){!function(e,n){t.exports=function(){function t(t){var e=void 0===t?"undefined":a(t);return null!==t&&("object"===e||"function"===e)}function e(t){return"function"==typeof t}function n(t){F=t}function i(t){W=t}function o(){return void 0!==D?function(){D(h)}:s()}function s(){var t=setTimeout;return function(){return t(h,1)}}function h(){for(var t=0;t<X;t+=2){(0,K[t])(K[t+1]),K[t]=void 0,K[t+1]=void 0}X=0}function u(t,e){var n=this,i=new this.constructor(l);void 0===i[J]&&z(i);var r=n._state;if(r){var o=arguments[r-1];W(function(){return N(r,i,o,n._result)})}else P(n,i,t,e);return i}function c(t){var e=this;if(t&&"object"===(void 0===t?"undefined":a(t))&&t.constructor===e)return t;var n=new e(l);return w(n,t),n}function l(){}function f(){return new TypeError("You cannot resolve a promise with itself")}function _(){return new TypeError("A promises callback cannot return that same promise.")}function d(t){try{return t.then}catch(t){return et.error=t,et}}function v(t,e,n,i){try{t.call(e,n,i)}catch(t){return t}}function y(t,e,n){W(function(t){var i=!1,r=v(n,e,function(n){i||(i=!0,e!==n?w(t,n):k(t,n))},function(e){i||(i=!0,x(t,e))},"Settle: "+(t._label||" unknown promise"));!i&&r&&(i=!0,x(t,r))},t)}function m(t,e){e._state===$?k(t,e._result):e._state===tt?x(t,e._result):P(e,void 0,function(e){return w(t,e)},function(e){return x(t,e)})}function p(t,n,i){n.constructor===t.constructor&&i===u&&n.constructor.resolve===c?m(t,n):i===et?(x(t,et.error),et.error=null):void 0===i?k(t,n):e(i)?y(t,n,i):k(t,n)}function w(e,n){e===n?x(e,f()):t(n)?p(e,n,d(n)):k(e,n)}function b(t){t._onerror&&t._onerror(t._result),C(t)}function k(t,e){t._state===Q&&(t._result=e,t._state=$,0!==t._subscribers.length&&W(C,t))}function x(t,e){t._state===Q&&(t._state=tt,t._result=e,W(b,t))}function P(t,e,n,i){var r=t._subscribers,o=r.length;t._onerror=null,r[o]=e,r[o+$]=n,r[o+tt]=i,0===o&&t._state&&W(C,t)}function C(t){var e=t._subscribers,n=t._state;if(0!==e.length){for(var i=void 0,r=void 0,o=t._result,s=0;s<e.length;s+=3)i=e[s],r=e[s+n],i?N(n,i,r,o):r(o);t._subscribers.length=0}}function S(t,e){try{return t(e)}catch(t){return et.error=t,et}}function N(t,n,i,r){var o=e(i),s=void 0,a=void 0,h=void 0,u=void 0;if(o){if(s=S(i,r),s===et?(u=!0,a=s.error,s.error=null):h=!0,n===s)return void x(n,_())}else s=r,h=!0;n._state!==Q||(o&&h?w(n,s):u?x(n,a):t===$?k(n,s):t===tt&&x(n,s))}function E(t,e){try{e(function(e){w(t,e)},function(e){x(t,e)})}catch(e){x(t,e)}}function M(){return nt++}function z(t){t[J]=nt++,t._state=void 0,t._result=void 0,t._subscribers=[]}function O(){return new Error("Array Methods must be provided an Array")}function R(t){return new it(this,t).promise}function H(t){var e=this;return new e(Y(t)?function(n,i){for(var r=t.length,o=0;o<r;o++)e.resolve(t[o]).then(n,i)}:function(t,e){return e(new TypeError("You must pass an array to race."))})}function j(t){var e=this,n=new e(l);return x(n,t),n}function A(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}function I(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}function T(){var t=void 0;if(void 0!==g)t=g;else if("undefined"!=typeof self)t=self;else try{t=Function("return this")()}catch(t){throw new Error("polyfill failed because global object is unavailable in this environment")}var e=t.Promise;if(e){var n=null;try{n=Object.prototype.toString.call(e.resolve())}catch(t){}if("[object Promise]"===n&&!e.cast)return}t.Promise=rt}var L=void 0;L=Array.isArray?Array.isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)};var Y=L,X=0,D=void 0,F=void 0,W=function(t,e){K[X]=t,K[X+1]=e,2===(X+=2)&&(F?F(h):G())},Z="undefined"!=typeof window?window:void 0,q=Z||{},U=q.MutationObserver||q.WebKitMutationObserver,B="undefined"==typeof self&&"undefined"!=typeof process&&"[object process]"==={}.toString.call(process),V="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel,K=new Array(1e3),G=void 0;G=B?function(){return function(){return process.nextTick(h)}}():U?function(){var t=0,e=new U(h),n=document.createTextNode("");return e.observe(n,{characterData:!0}),function(){n.data=t=++t%2}}():V?function(){var t=new MessageChannel;return t.port1.onmessage=h,function(){return t.port2.postMessage(0)}}():void 0===Z&&"function"==typeof r?function(){try{var t=Function("return this")().require("vertx");return D=t.runOnLoop||t.runOnContext,o()}catch(t){return s()}}():s();var J=Math.random().toString(36).substring(2),Q=void 0,$=1,tt=2,et={error:null},nt=0,it=function(){function t(t,e){this._instanceConstructor=t,this.promise=new t(l),this.promise[J]||z(this.promise),Y(e)?(this.length=e.length,this._remaining=e.length,this._result=new Array(this.length),0===this.length?k(this.promise,this._result):(this.length=this.length||0,this._enumerate(e),0===this._remaining&&k(this.promise,this._result))):x(this.promise,O())}return t.prototype._enumerate=function(t){for(var e=0;this._state===Q&&e<t.length;e++)this._eachEntry(t[e],e)},t.prototype._eachEntry=function(t,e){var n=this._instanceConstructor,i=n.resolve;if(i===c){var r=d(t);if(r===u&&t._state!==Q)this._settledAt(t._state,e,t._result);else if("function"!=typeof r)this._remaining--,this._result[e]=t;else if(n===rt){var o=new n(l);p(o,t,r),this._willSettleAt(o,e)}else this._willSettleAt(new n(function(e){return e(t)}),e)}else this._willSettleAt(i(t),e)},t.prototype._settledAt=function(t,e,n){var i=this.promise;i._state===Q&&(this._remaining--,t===tt?x(i,n):this._result[e]=n),0===this._remaining&&k(i,this._result)},t.prototype._willSettleAt=function(t,e){var n=this;P(t,void 0,function(t){return n._settledAt($,e,t)},function(t){return n._settledAt(tt,e,t)})},t}(),rt=function(){function t(e){this[J]=M(),this._result=this._state=void 0,this._subscribers=[],l!==e&&("function"!=typeof e&&A(),this instanceof t?E(this,e):I())}return t.prototype.catch=function(t){return this.then(null,t)},t.prototype.finally=function(t){var e=this,n=e.constructor;return e.then(function(e){return n.resolve(t()).then(function(){return e})},function(e){return n.resolve(t()).then(function(){throw e})})},t}();return rt.prototype.then=u,rt.all=R,rt.race=H,rt.resolve=c,rt.reject=j,rt._setScheduler=n,rt._setAsap=i,rt._asap=W,rt.polyfill=T,rt.Promise=rt,rt}()}()}),y=function(t){function e(){h(this,e);var t=f(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,"img"));return t._scale=t._originScale=1,t._zoom=0,t}return l(e,t),u(e,[{key:"load",value:function(t){var e=this;return new v(function(n,i){e.getNode().onload=function(){e._checkFormat(),n(e)},e.getNode().onerror=function(){i(Error("Can't load an image."))},e.getNode().src=t,e.getNode().crossOrigin="Anonymous"})}},{key:"isPortrait",value:function(){return"portrait"===this._checkFormat()}},{key:"isLandscape",value:function(){return"landscape"===this._checkFormat()}},{key:"isSquare",value:function(){return"square"===this._checkFormat()}},{key:"scaleToFit",value:function(t){var e=t.getRect().size.width/this.getNode().width,n=t.getRect().size.height/this.getNode().height,i=e>n?e:n;return this._scale=this._originScale=i,this._scale}},{key:"getSize",value:function(){var t=this.getNode().width*this._scale,e=this.getNode().height*this._scale;return new _(t,e)}},{key:"setZoom",value:function(t){return this._zoom=t,this._scale=this._originScale+this._originScale*t,this}},{key:"getZoom",value:function(){return this._zoom}},{key:"getScale",value:function(){return this._scale}},{key:"getOriginScale",value:function(){return this._originScale}},{key:"_checkFormat",value:function(){return this.getNode().width>this.getNode().height?"landscape":this.getNode().width<this.getNode().height?"portrait":"square"}}]),e}(d),m=function(){function t(e){h(this,t),this._context=e}return u(t,[{key:"fillRect",value:function(t,e,n,i){return this._context.fillRect(t,e,n,i)}},{key:"fillStyle",value:function(t){return this._context.fillStyle=t}},{key:"createPattern",value:function(t,e){return this._context.createPattern(t,e)}},{key:"rect",value:function(t,e,n,i){return this._context.rect(t,e,n,i)}},{key:"fill",value:function(){return this._context.fill()}},{key:"beginPath",value:function(){return this._context.beginPath()}},{key:"moveTo",value:function(t,e){return this._context.moveTo(t,e)}},{key:"lineTo",value:function(t,e){return this._context.lineTo(t,e)}},{key:"closePath",value:function(){return this._context.closePath()}},{key:"clearRect",value:function(t,e,n,i){return this._context.clearRect(t,e,n,i)}},{key:"drawImage",value:function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return this._context.drawImage.apply(this._context,e)}}]),t}(),p=function(t){function e(){h(this,e);var t=f(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,"canvas"));return t._context=new m(t._node.getContext("2d")),t.setWidth(s.pattern.size),t.setHeight(s.pattern.size),t._draw(),t}return l(e,t),u(e,[{key:"_draw",value:function(){return this._context.fillStyle(s.pattern.fill1),this._context.fillRect(0,0,8,8),this._context.fillStyle(s.pattern.fill2),this._context.fillRect(8,0,8,8),this._context.fillRect(0,8,8,8),this._context.fillStyle(s.pattern.fill1),this._context.fillRect(8,8,8,8),this}}]),e}(d),w=function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;h(this,t),this.x=e,this.y=n},b=function(){function t(){h(this,t),this._size=0,this._origin={x:0,y:0}}return u(t,[{key:"update",value:function(t){return this._size=t.width>t.height?.85*t.height:.85*t.width,this._origin={x:(t.width-this._size)/2,y:(t.height-this._size)/2},this}},{key:"getRect",value:function(){return{origin:new w(this._origin.x,this._origin.y),size:new _(this._size,this._size)}}},{key:"getMinX",value:function(){return this._origin.x}},{key:"getMaxX",value:function(){return this._origin.x+this._size}},{key:"getMidX",value:function(){return this._origin.x+this._size/2}},{key:"getMinY",value:function(){return this._origin.y}},{key:"getMaxY",value:function(){return this._origin.y+this._size}},{key:"getMidY",value:function(){return this._origin.y+this._size/2}}]),t}(),k=function(){function t(e,n){h(this,t),this._frame=e,this._canvas=n,this._context=new m(this._canvas.getNode().getContext("2d"))}return u(t,[{key:"draw",value:function(){return this._context.fillStyle(s.cutout.fill),this._context.beginPath(),this._context.rect(0,0,this._canvas.getNode().width,this._canvas.getNode().height),this._context.moveTo(this._frame.getMinX(),this._frame.getMinY()),this._context.lineTo(this._frame.getMinX(),this._frame.getMaxY()),this._context.lineTo(this._frame.getMaxX(),this._frame.getMaxY()),this._context.lineTo(this._frame.getMaxX(),this._frame.getMinY()),this._context.closePath(),this._context.fill(),this}}]),t}(),x=function(t){function e(t,n){h(this,e);var i=f(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,"canvas"));return i._frame=t,i._canvas=n,i._context=new m(i._node.getContext("2d")),i}return l(e,t),u(e,[{key:"toDataURL",value:function(){return this.setWidth(this._frame.getRect().size.width),this.setHeight(this._frame.getRect().size.height),this._context.drawImage(this._canvas.getNode(),this._frame.getMinX(),this._frame.getMinY(),this._frame.getRect().size.width,this._frame.getRect().size.height,0,0,this._frame.getRect().size.width,this._frame.getRect().size.height),this.getNode().toDataURL()}}]),e}(d),P=function(){function t(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new d(document.body);h(this,t),this._element=e,this._parent=n,this._onMoveCallback=function(){},this._onPressCallback=function(){},this._onReleaseCallback=function(){},this._onReleaseHandler=this.onReleaseHandler.bind(this),this._onPressHandler=this.onPressHandler.bind(this),this._onMoveHandler=this.onMoveHandler.bind(this)}return u(t,[{key:"onMove",value:function(t){this._onMoveCallback=t}},{key:"onPress",value:function(t){this._onPressCallback=t}},{key:"onRelease",value:function(t){this._onReleaseCallback=t}},{key:"init",value:function(){this._element.getNode().addEventListener("mousedown",this._onPressHandler,!1),this._element.getNode().addEventListener("touchstart",this._onPressHandler,!1),this._parent.getNode().addEventListener("mouseup",this._onReleaseHandler,!1),this._parent.getNode().addEventListener("touchend",this._onReleaseHandler,!1)}},{key:"onMoveHandler",value:function(t){this._onMoveCallback(this._getEventPoint(t))}},{key:"onPressHandler",value:function(t){this._parent.getNode().addEventListener("mousemove",this._onMoveHandler,!1),this._parent.getNode().addEventListener("touchmove",this._onMoveHandler,!1),this._onPressCallback(this._getEventPoint(t))}},{key:"onReleaseHandler",value:function(t){this._parent.getNode().removeEventListener("mousemove",this._onMoveHandler,!1),this._parent.getNode().removeEventListener("touchmove",this._onMoveHandler,!1),this._onReleaseCallback(this._getEventPoint(t))}},{key:"_convertCoordinates",value:function(t){var e=this._element.getNode().getBoundingClientRect(),n=t.x-e.left*(this._element.getNode().width/e.width),i=t.y-e.top*(this._element.getNode().height/e.height);return new w(n,i)}},{key:"_getEventPoint",value:function(t){var e=t.clientX||t.touches[0].clientX,n=t.clientY||t.touches[0].clientY;return this._convertCoordinates(new w(e,n))}}]),t}(),C=function(t){function e(){h(this,e);var t=f(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,"canvas"));return t._context=new m(t._node.getContext("2d")),t._image=new y,t._pattern=new p,t._frame=new b,t._cutout=new k(t._frame,t),t._generator=new x(t._frame,t),t._moveEventListener=new P(t),t._lastPoint=new w(0,0),t._basePoint=new w(0,0),t._onChangeCallback=function(){},t}return l(e,t),u(e,[{key:"render",value:function(t){var n=this;return c(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"render",this).call(this,t),this._drawBackground(),this._moveEventListener.init(),this._moveEventListener.onPress(function(t){n._lastPoint=t}),this._moveEventListener.onMove(function(t){n._drawImage(t)}),this}},{key:"setWidth",value:function(t){return c(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"setWidth",this).call(this,t),this._frame.update(this.getNode()),this}},{key:"setHeight",value:function(t){return c(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"setHeight",this).call(this,t),this._frame.update(this.getNode()),this}},{key:"setImage",value:function(t){return this._resetPoints(),this._image=t,this._image.scaleToFit(this._frame),this}},{key:"draw",value:function(){return this._drawImage(this._centerImagePoint()),this}},{key:"redraw",value:function(){return this._resetPoints(),this._image.scaleToFit(this._frame),this.draw(),this}},{key:"clear",value:function(){return this._context.clearRect(0,0,this.getNode().width,this.getNode().height),this}},{key:"toDataURL",value:function(){return this._generator.toDataURL()}},{key:"setZoom",value:function(t){var e=this._image.getSize();this._image.setZoom(t);var n=this._image.getSize(),i=this._lastPoint.x-(n.width-e.width)/2,r=this._lastPoint.y-(n.height-e.height)/2;return this._drawImage(new w(i,r)),this}},{key:"onChange",value:function(t){this._onChangeCallback=t}},{key:"getData",value:function(){return{origin:{x:(this._frame.getMinX()-this._basePoint.x)/this._image.getScale(),y:(this._frame.getMinY()-this._basePoint.y)/this._image.getScale()},size:{width:this._frame.getRect().size.width/this._image.getScale(),height:this._frame.getRect().size.width/this._image.getScale()}}}},{key:"setData",value:function(t){var e=this._frame.getRect().size.width/t.size.width,n=(e-this._image.getOriginScale())/this._image.getOriginScale();this.setZoom(n);var i=this._frame.getMinX()-t.origin.x*this._image.getScale(),r=this._frame.getMinY()-t.origin.y*this._image.getScale(),o=new w(i,r);return this._resetPoints(),this._drawImage(o),{origin:o,zoom:n}}},{key:"_resetPoints",value:function(){return this._lastPoint=new w(0,0),this._basePoint=new w(0,0),this}},{key:"_centerImagePoint",value:function(){var t=this._frame.getMidX()-this._image.getSize().width/2,e=this._frame.getMidY()-this._image.getSize().height/2;return new w(t,e)}},{key:"_validatePoint",value:function(t){var e=t;return this._image.getSize().width<this._frame.getRect().size.width?e.x=this._centerImagePoint().x:t.x>this._frame.getMinX()?e.x=this._frame.getMinX():t.x+this._image.getSize().width<this._frame.getMaxX()?e.x=this._frame.getMaxX()-this._image.getSize().width:e.x=t.x,this._image.getSize().height<this._frame.getRect().size.height?e.y=this._centerImagePoint().y:t.y>this._frame.getMinY()?e.y=this._frame.getMinY():t.y+this._image.getSize().height<this._frame.getMaxY()?e.y=this._frame.getMaxY()-this._image.getSize().height:e.y=t.y,e}},{key:"_drawImage",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new w(0,0);this.clear(),this._drawBackground();var e=this._basePoint.x+(t.x-this._lastPoint.x),n=this._basePoint.y+(t.y-this._lastPoint.y);return this._basePoint=this._validatePoint(new w(e,n)),this._lastPoint=t,this._context.drawImage(this._image.getNode(),this._basePoint.x,this._basePoint.y,this._image.getSize().width,this._image.getSize().height),this._cutout.draw(),this._onChangeCallback(this),this}},{key:"_drawBackground",value:function(){var t=this._context.createPattern(this._pattern.getNode(),"repeat");return this._context.rect(0,0,this.getNode().width,this.getNode().height),this._context.fillStyle(t),this._context.fill(),this}}]),e}(d),S=function(t){function e(){h(this,e);var t=f(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,"input"));return t.setType("range"),t.addClass("slider"),t.setAttribute("min",0),t.setAttribute("max",100),t.setAttribute("value",0),t._onChangeCallback=function(){},t._onChangeHandler=t._onChange.bind(t),t}return l(e,t),u(e,[{key:"onChange",value:function(t){return this._onChangeCallback=t,this.getNode().addEventListener("change",this._onChangeHandler,!1),this.getNode().addEventListener("input",this._onChangeHandler,!1),this}},{key:"setValue",value:function(t){return this.getNode().value=t,this}},{key:"_onChange",value:function(){this._onChangeCallback(Number(this.getNode().value))}}]),e}(d),N=function(t){function e(t){h(this,e);var n=f(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,"svg"));return n.setAttribute("class","icon icon-"+t),n._use=new d("use"),n._use.getNode().setAttributeNS("http://www.w3.org/1999/xlink","xlink:href","#icon-"+t),n._use.render(n.getNode()),n}return l(e,t),e}(d);return function(){function r(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};h(this,r),e(t),this._canvas=new C,this._image=new y,this._slider=new S,this.setWidth(t.width||o.width),this.setHeight(t.height||o.height),this._onInitCallback=i(t.onInit),this._onChangeCallback=i(t.onChange)}return u(r,[{key:"render",value:function(e){var n=this;this._node=t(e);var i=new d;i.addClass("cropper"),i.render(this._node),this._canvas.render(i.getNode());var r=new d;r.addClass("cropper-tools"),r.render(i.getNode());var o=new d;o.addClass("cropper-zoom"),o.render(r.getNode());var s=new N("frame-landscape"),a=new N("frame-landscape");return s.render(o.getNode()),this._slider.render(o.getNode()),this._slider.onChange(function(t){n._canvas.setZoom(t/100)}),a.render(o.getNode()),this._onInitCallback(this),this._canvas.onChange(function(){n._onChangeCallback(n)}),this}},{key:"setWidth",value:function(t){try{n(t)}catch(t){throw Error("Width property: "+t.message)}return this._canvas.setWidth(t),this._canvas.redraw(),this}},{key:"setHeight",value:function(t){try{n(t)}catch(t){throw Error("Height property: "+t.message)}return this._canvas.setHeight(t),this._canvas.redraw(),this}},{key:"loadImage",value:function(t){var e=this;if(!t)throw Error("Image url or path is not passed.");if("string"!=typeof t)throw Error("Invalid url or path.");return this._image.load(t).then(function(t){return e._canvas.setImage(t),e._canvas.draw(),e._slider.setValue(0),e})}},{key:"getCroppedImage",value:function(){return this._canvas.toDataURL()}},{key:"setZoom",value:function(t){try{n(t)}catch(t){throw Error("Zoom property: "+t.message)}return this._canvas.setZoom(t),this._slider.setValue(100*t),this}},{key:"reset",value:function(){return this.setZoom(0),this._canvas.redraw(),this}},{key:"getData",value:function(){return this._canvas.getData()}},{key:"setData",value:function(t){var e=this._canvas.setData(t),n=e.zoom;return this._slider.setValue(100*n),this}}]),r}()}();
