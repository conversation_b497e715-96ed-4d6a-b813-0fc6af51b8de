package net.arccode.wechat.pay.api.service;


import java.io.IOException;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * XML工具类
 * 
 * <AUTHOR>
 *
 */
public class XmlUtil {

	/**
	 * 处理xml请求信息
	 * 
	 * @param request
	 * @return
	 * @throws IOException
	 */
	public static String getXmlRequest(HttpServletRequest request, HttpServletResponse response) throws IOException {
		ServletInputStream instream = request.getInputStream();
		StringBuffer sb = new StringBuffer();
		int len = -1;
		byte[] buffer = new byte[1024];

		while ((len = instream.read(buffer)) != -1) {
			sb.append(new String(buffer, 0, len));
		}
		instream.close();
		return sb.toString();
	}
}
