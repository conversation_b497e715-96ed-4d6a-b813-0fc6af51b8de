package net.arccode.config;

import com.wb.common.Var;

/**
 * 
 * <AUTHOR> <PERSON>
 * 微信配置参数
 */
public class AccountsConfig {
	// 1.微信公众号参数
	/**
	 * 微信应用appId
	 */
	public static String appId = Var.getString("sys.config.accounts.AppId");
	/**
	 * 微信应用密匙
	 */
	public static String appSecret = Var.getString("sys.config.accounts.AppSecret");
	/**
	 * 微信登录授权类型
	 */
	public static String grantType = Var.getString("sys.config.accounts.Grant_Type");
	/**
	 * 微信商户号
	 */
	public static String partner = Var.getString("sys.config.accounts.Partner");
	/**
	 * 微信商户密匙
	 */
	public static String partnerkey = Var.getString("sys.config.accounts.PartnerKey");
	/**
	 * 微信异步回调地址
	 */
	public static String notifyUrl = Var.getString("sys.config.SystemUrl") + Var.getString("sys.config.accounts.notify_url");
	/**
	 * 微信支付交易类型
	 */
	public static String tradeType = Var.getString("sys.config.accounts.Trade_Type");
	/**
	 * 微信支付内容说明
	 */
	public static String payBody = Var.getString("sys.config.accounts.PayBody");
	
	/**
	 * 支付授权类型
	 */
	public static String authType = "authorization_code";
	
	/**
	 * 域名地址
	 */
	public static String SystemUrl = Var.getString("sys.config.SystemUrl");
	
	/**
	 * 保存支付信息地址
	 */
	public static String payUrl = SystemUrl + Var.getString("sys.config.pay_Url");
	
	/**
	 * 回调支付信息地址
	 */
	public static String backUrl = SystemUrl + Var.getString("sys.config.pay_callback");
	
	public static void init() {
		/**
		 * 微信应用appId
		 */
		appId = Var.getString("sys.config.accounts.AppId");
		/**
		 * 微信应用密匙
		 */
		appSecret = Var.getString("sys.config.accounts.AppSecret");
		/**
		 * 微信登录授权类型
		 */
		grantType = Var.getString("sys.config.accounts.Grant_Type");
		/**
		 * 微信商户号
		 */
		partner = Var.getString("sys.config.accounts.Partner");
		/**
		 * 微信商户密匙
		 */
		partnerkey = Var.getString("sys.config.accounts.PartnerKey");
		/**
		 * 微信异步回调地址
		 */
		notifyUrl = Var.getString("sys.config.SystemUrl") + Var.getString("sys.config.accounts.notify_url");
		/**
		 * 微信支付交易类型
		 */
		tradeType = Var.getString("sys.config.accounts.Trade_Type");
		/**
		 * 微信支付内容说明
		 */
		payBody = Var.getString("sys.config.accounts.PayBody");
		
		/**
		 * 域名地址
		 */
		SystemUrl = Var.getString("sys.config.SystemUrl");
		
		/**
		 * 支付信息地址
		 */
		payUrl = SystemUrl + Var.getString("sys.config.pay_Url");
		
		/**
		 * 回调支付信息地址
		 */
		backUrl = SystemUrl + Var.getString("sys.config.pay_callback");
	}
}
