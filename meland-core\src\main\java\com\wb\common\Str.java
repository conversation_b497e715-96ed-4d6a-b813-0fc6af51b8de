package com.wb.common;

import java.io.File;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.json.JSONObject;

import com.wb.util.FileUtil;
import com.wb.util.JsonUtil;
import com.wb.util.StringUtil;

/**
 * 支持多国语言的文字、数字和日期等。
 */
public class Str {
	/**
	 * Wb的多语言HashMap。HashMap中名称为语言名称，值为文字列表组成的map。
	 */
	private static ConcurrentHashMap<String, ConcurrentHashMap<String, String>> wbLang;
	/**
	 * Ext的多语言HashMap，用于获取所支持的语言种类。
	 */
	private static ConcurrentHashMap<String, String> extLang;
	/**
	 * Touch的多语言HashMap，用于获取所支持的语言种类。
	 */
	private static ConcurrentHashMap<String, String> touchLangList;
	/**
	 * 根据配置文件把同一语言不同名称转换为指定名称的映射HashMap。
	 */
	private static ConcurrentHashMap<String, String> langMap;

	/**
	 * 根据默认语言和输入参数，格式化指定的关键字。
	 * 
	 * @param key
	 *            预定义的字符串关键字。
	 * @param args
	 *            填充到关键字中的参数列表。
	 * @return 格式化后的字符串。
	 * @see com.wb.common.Str#langFormat
	 */
	public static String format(String key, Object... args) {
		return langFormat(Var.defaultLanguage, key, args);
	}

	/**
	 * 根据当前客户端语言和输入参数，格式化指定的关键字。
	 * 
	 * @param request 包含当前语言的request对象。
	 * @param key 预定义的字符串关键字。
	 * @param args 填充到关键字中的参数列表。
	 * @return 格式化后的字符串。
	 * @see com.wb.common.Str#langFormat
	 */
	public static String format(HttpServletRequest request, String key, Object... args) {
		return langFormat(getLanguage(request), key, args);
	}

	/**
	 * 把指定语言转换为wb所支持的语言，如果未找到匹配则返回默认语言。
	 * 
	 * @param lang
	 *            需要转换的语言
	 * @return 转换后的语言
	 * @see com.wb.common.Str#optLang
	 */
	public static String optLanguage(String lang) {
		return optLang(wbLang, lang);
	}

	/**
	 * 把指定语言转换为ext所支持的语言，如果未找到匹配则返回默认语言。
	 * 
	 * @param lang 需要转换的语言
	 * @return 转换后的语言
	 * @see com.wb.common.Str#optLang
	 */
	public static String optExtLanguage(String lang) {
		return optLang(extLang, lang);
	}

	public static String optTouchLanguage(String lang) {
		return optLang(touchLangList, lang);
	}

	public static String getLanguage(HttpServletRequest request) {
		String useLang = (String) request.getAttribute("sys.useLang");
		if (useLang != null)
			return useLang;
		useLang = optLanguage(getClientLanguage(request));
		request.setAttribute("sys.useLang", useLang);
		return useLang;
	}

	public static String getText(HttpServletRequest request, String string) {
		if ((string != null) && (string.startsWith("Str.")))
			return format(request, string.substring(4), new Object[0]);
		return string;
	}

	public static String getClientLanguage(HttpServletRequest request) {
		String setLan = (String) request.getAttribute("sys.clientLang");
		if (setLan != null)
			return setLan;
		HttpSession session = request.getSession(false);

		setLan = Var.language;
		if (session != null) {
			String sessionLan = (String) session.getAttribute("sys.lang");
			if (!StringUtil.isEmpty(sessionLan))
				setLan = sessionLan;
		}
		if (setLan.equals("auto")) {
			setLan = Var.defaultLanguage;
			String acceptLang = request.getHeader("Accept-Language");
			if (acceptLang != null) {
				int pos = acceptLang.indexOf(',');
				if (pos != -1)
					acceptLang = acceptLang.substring(0, pos);
				pos = acceptLang.indexOf(';');
				if (pos != -1)
					acceptLang = acceptLang.substring(0, pos);
				pos = acceptLang.indexOf('-');
				if (pos == -1) {
					setLan = acceptLang.toLowerCase();
				} else {
					String language = StringUtil.concat(new String[] { acceptLang.substring(0, pos).toLowerCase(), "_",
							acceptLang.substring(pos + 1).toUpperCase() });
					String mappedLang = (String) langMap.get(language);
					if (mappedLang == null)
						setLan = language;
					else {
						setLan = mappedLang;
					}
				}
			}
		}
		request.setAttribute("sys.clientLang", setLan);
		return setLan;
	}

	/**
	 * 根据当前的客户端语言，格式化关键字并填充参数，把指定关键字转换为格式化后的文本。
	 * 
	 * @param lang 客户端语言名称。
	 * @param key 需要转换的带有参数的关键字。
	 * @param args 参数列表
	 * @return 格式化后的文本。
	 */
	private static String langFormat(String lang, String key, Object... args) {
		ConcurrentHashMap<String, String> buffer = wbLang.get(optLanguage(lang));
		if (buffer == null)
			return key;
		String str = buffer.get(key);
		if (str == null)
			return key;
		int i = 0;
		for (Object object : args)
			str = StringUtil.replaceAll(str, "{" + (i++) + "}", object == null ? "null" : object.toString());
		return str;
	}

	private static String optLang(ConcurrentHashMap<String, ?> map, String lang) {
		if (!StringUtil.isEmpty(lang)) {
			if (map.containsKey(lang))
				return lang;
			int pos = lang.indexOf('_');
			if (pos != -1) {
				lang = lang.substring(0, pos);
				if (map.containsKey(lang))
					return lang;
			}
		}
		return Var.defaultLanguage;
	}

	/**
	 * 加载和初始化。
	 */
	public static synchronized void load() {
		try {
			langMap = new ConcurrentHashMap<String, String>();
			wbLang = new ConcurrentHashMap<String, ConcurrentHashMap<String, String>>();
			ConcurrentHashMap<String, String> buffer;
			File[] fs = FileUtil.listFiles(new File(Base.path, "wb/script/locale"));
			JSONObject jo;
			String name, langList[];
			Set<Entry<String, Object>> es;

			// 建立同一语言不同名称的映射关系
			jo = JsonUtil.readObject(new File(Base.path, "wb/system/language.json"));
			es = jo.entrySet();
			for (Entry<String, Object> e : es) {
				langList = StringUtil.split((String) e.getValue(), ',', true);
				for (String ln : langList)
					langMap.put(ln, e.getKey());
			}

			// 把wb多语言文件所支持的语言种类和文件内容放到map中
			for (File file : fs) {
				name = file.getName().toLowerCase();
				//互斥调试文件和压缩文件
				if (!name.endsWith(".js") || (Var.debug && !name.endsWith("-debug.js"))
						|| (!Var.debug && name.endsWith("-debug.js")))
					continue;
				if (name.endsWith("-debug.js"))
					name = name.substring(8, name.length() - 9);
				else
					name = name.substring(8, name.length() - 3);
				if (!wbLang.contains(name)) {
					buffer = new ConcurrentHashMap<String, String>();
					jo = JsonUtil.readObject(file);
					es = jo.entrySet();
					for (Entry<String, Object> e : es)
						buffer.put(e.getKey(), (String) e.getValue());
					wbLang.put(name, buffer);
				}
			}

			// 把ext多语言文件所支持的语言种类放到map中
			extLang = new ConcurrentHashMap<String, String>();
			fs = FileUtil.listFiles(new File(Base.path, "wb/libs/ext/locale"));
			for (File file : fs) {
				name = file.getName();
				if (!name.endsWith("-debug.js")) {
					name = name.substring(9, name.length() - 3);
					extLang.put(name, name);
				}
			}

			// 把touch多语言文件所支持的语言种类放到map中
			touchLangList = new ConcurrentHashMap<String, String>();
			fs = FileUtil.listFiles(new File(Base.path, "wb/libs/touch/locale"));
			for (File f : fs) {
				name = f.getName();
				if (!name.endsWith("-debug.js")) {
					name = name.substring(7, name.length() - 3);
					touchLangList.put(name, name);
				}
			}

		} catch (Throwable e) {
			throw new RuntimeException(e);
		}
	}
}