package com.wb.common.ws;

import com.wb.util.SysUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

import javax.annotation.PostConstruct;

/**
 * WebSocket Redis配置类，用于定义常量和配置Redis监听器
 */
@Configuration
public class WebSocketRedisConfig {

    private static final Logger LOGGER = LoggerFactory.getLogger(WebSocketRedisConfig.class);

    @Autowired
    private WebSocketMessageListener webSocketMessageListener;

    // 使用@Qualifier指定要使用的RedisMessageListenerContainer Bean
    @Autowired
    @Qualifier("redisMessageListenerContainer")
    private RedisMessageListenerContainer webSocketRedisContainer;

    // 注入我们定义的MessageListenerAdapter Bean，而不是每次创建新的
    @Autowired
    private MessageListenerAdapter webSocketMessageAdapter;

    // WebSocket消息通道
    public static final String WS_BROADCAST_CHANNEL = "websocket:broadcast";
    public static final String WS_USER_CHANNEL_PREFIX = "websocket:user:";
    public static final String WS_NODE_CHANNEL_PREFIX = "websocket:node:";

    // Redis键前缀
    public static final String WS_SESSION_KEY_PREFIX = "websocket:sessions:";
    public static final String WS_USER_KEY_PREFIX = "websocket:users:";
    public static final String WS_NODE_KEY_PREFIX = "websocket:nodes:";

    // 当前节点ID
    private final String nodeId = SysUtil.getServerId();

    @Bean
    @DependsOn("webSocketSessionManager")
    public WebSocketMessageListener webSocketMessageListener() {
        try {
            WebSocketMessageListener listener = new WebSocketMessageListener();
            // 在Bean创建时就设置nodeId，避免运行时修改
            listener.setNodeId(nodeId);
            LOGGER.info("WebSocketMessageListener Bean创建成功，节点ID: {}", nodeId);
            return listener;
        } catch (Exception e) {
            LOGGER.error("创建WebSocketMessageListener Bean异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Bean
    @DependsOn("webSocketMessageListener")
    public MessageListenerAdapter webSocketMessageAdapter() {
        try {
            MessageListenerAdapter adapter = new MessageListenerAdapter(webSocketMessageListener());
            LOGGER.info("MessageListenerAdapter Bean创建成功");
            return adapter;
        } catch (Exception e) {
            LOGGER.error("创建MessageListenerAdapter Bean异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    // 确保WebSocketSessionManager在其他Bean之前初始化
    @Bean("webSocketSessionManager")
    public WebSocketSessionManager webSocketSessionManager() {
        try {
            WebSocketSessionManager manager = WebSocketSessionManager.getInstance();
            LOGGER.info("WebSocketSessionManager Bean创建成功");
            return manager;
        } catch (Exception e) {
            LOGGER.error("创建WebSocketSessionManager Bean异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    @PostConstruct
    public void initializeMessageListening() {
        try {
            // 确保初始化顺序：先设置属性，再添加监听器

            // 设置监听器属性
            webSocketMessageListener.setWebSocketRedisContainer(webSocketRedisContainer);

            // 使用注入的适配器，而不是每次创建新的
            // 添加广播通道监听
            webSocketRedisContainer.addMessageListener(webSocketMessageAdapter,
                new ChannelTopic(WS_BROADCAST_CHANNEL));

            // 添加节点特定通道监听
            webSocketRedisContainer.addMessageListener(webSocketMessageAdapter,
                new ChannelTopic(WS_NODE_CHANNEL_PREFIX + nodeId));

            LOGGER.info("WebSocket Redis消息监听初始化完成，节点ID: {}", nodeId);
        } catch (Exception e) {
            // 记录异常但不中断启动
            LOGGER.error("WebSocket消息监听初始化异常: {}", e.getMessage(), e);
        }
    }
}