package com.wb.message;

import org.json.JSONObject;

public abstract class Handler {

	/**
	 * 消息标题
	 */
	protected String title;

	/**
	 * 消息内容
	 */
	protected String content;

	/**
	 * 收到用户
	 */
	protected String[] users;

	/**
	 * 收到参数
	 */
	protected String data;

	/**
	 * 其他参数
	 */
	protected JSONObject arg;

	public Handler(String title, String content, String users, String data, JSONObject arg) {
		this.title = title;
		this.content = content;
		this.users = users.split(",");
		this.data = data;
		this.arg = arg;
		System.out.println("发送给用户：" + users);
		System.out.println("发送参数：" + arg);
	}

	public abstract void handlerMessage();

}
