package net.arccode.config;

import com.wb.common.Var;

public class AlipayConfig {
	/**
	 * 支付宝应用appId
	 */
	public static String APPID = Var.getString("sys.config.alipay.AppId");
	
	/**
	 * 支付宝私有密钥 pkcs8格式的
	 */
	public static String RSA_PRIVATE_KEY = Var.getString("sys.config.alipay.PrivateKey");
	
	/**
	 * 服务器异步回调路径
	 */
	public static String notify_url = Var.getString("sys.config.SystemUrl")
			+ Var.getString("sys.config.alipay.notify_url");
	
	/**
	 * 服务器同步回调路径
	 */
	public static String return_url = Var.getString("sys.config.SystemUrl")
			+ Var.getString("sys.config.alipay.return_url");
	
	/**
	 * 请求网关地址
	 */
	public static String URL = "https://openapi.alipay.com/gateway.do";
	
	/**
	 * 编码
	 */
	public static String CHARSET = "UTF-8";
	
	/**
	 * 返回格式
	 */
	public static String FORMAT = "json";
	
	/**
	 * 支付宝公有密匙
	 */
	public static String ALIPAY_PUBLIC_KEY = Var.getString("sys.config.alipay.PublicKey");
	
	/**
	 * 日志记录目录
	 */
	public static String log_path = "/log";
	
	/**
	 * RSA2
	 */
	public static String SIGNTYPE = "RSA2";
	
	/**
	 * 域名地址
	 */
	public static String SystemUrl = Var.getString("sys.config.SystemUrl");
	
	/**
	 * 保存支付信息地址
	 */
	public static String payUrl = SystemUrl + Var.getString("sys.config.pay_Url");
	
	/**
	 * 回调支付信息地址
	 */
	public static String backUrl = SystemUrl + Var.getString("sys.config.pay_callback");

	public static void init() {
		/**
		 * 商户appid
		 */
		APPID = Var.getString("sys.config.alipay.AppId");
		/**
		 * 私钥 pkcs8格式的
		 */
		RSA_PRIVATE_KEY = Var.getString("sys.config.alipay.PrivateKey");
		/**
		 * 服务器异步通知页面路径
		 */
		notify_url = Var.getString("sys.config.SystemUrl") + Var.getString("sys.config.alipay.notify_url");
		/**
		 * 页面跳转同步通知页面路径
		 */
		return_url = Var.getString("sys.config.SystemUrl") + Var.getString("sys.config.alipay.return_url");
		/**
		 * 请求网关地址
		 */
		URL = "https://openapi.alipay.com/gateway.do";
		/**
		 * 编码
		 */
		CHARSET = "UTF-8";
		/**
		 * 返回格式
		 */
		FORMAT = "json";
		/**
		 * 支付宝公钥
		 */
		ALIPAY_PUBLIC_KEY = Var.getString("sys.config.alipay.PublicKey");
		/**
		 * 日志记录目录
		 */
		log_path = "/log";
		/**
		 * RSA2
		 */
		SIGNTYPE = "RSA2";
		
		/**
		 * 域名地址
		 */
		SystemUrl = Var.getString("sys.config.SystemUrl");
		
		/**
		 * 支付信息地址
		 */
		payUrl = SystemUrl + Var.getString("sys.config.pay_Url");
		
		/**
		 * 回调支付信息地址
		 */
		backUrl = SystemUrl + Var.getString("sys.config.pay_callback");
	}
}
