package com.wb.common.ws;

import com.alibaba.fastjson.JSONObject;
import com.wb.common.Base;
import com.wb.util.LogUtil;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * WebSocket消息发送工具类
 */
public class WebSocketMessageSender {
    
    /**
     * 发送广播消息给所有连接的客户端
     */
    public static void broadcastMessage(String message, boolean isJson, String targetName) {
        // 先尝试本地广播
        Collection<WebSocketSession> localSessions = WebSocketSessionManager.getInstance().getAllLocalSessions();
        
        for (WebSocketSession session : localSessions) {
            if (session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(message));
                } catch (IOException e) {
                    LogUtil.error("发送WebSocket广播消息异常: " + e.getMessage());
                }
            }
        }
        
        // 然后广播到Redis通道，通知其他节点
        com.alibaba.fastjson.JSONObject msgData = new com.alibaba.fastjson.JSONObject();
        msgData.put("message", message);
        msgData.put("isJson", isJson);
        msgData.put("targetName", targetName);
        msgData.put("sourceNodeId", WebSocketSessionManager.getInstance().getNodeId());
        
        // 使用Base.map发送Redis消息
        Base.map.publish(WebSocketRedisConfig.WS_BROADCAST_CHANNEL, msgData);
    }
    
    /**
     * 发送消息给指定用户
     */
    public static void sendMessageToUser(String userId, String message, boolean isJson, String targetName) {
        boolean sentLocally = false;
        
        // 获取用户所有会话ID
        Set<String> sessionIds = WebSocketSessionManager.getInstance().getSessionIdsByUserId(userId);
        
        for (String sessionId : sessionIds) {
            WebSocketSession session = WebSocketSessionManager.getInstance().getSession(sessionId);
            if (session != null && session.isOpen()) {
                // 如果指定了targetName，则需要获取会话信息并进行过滤
                if (targetName != null && !targetName.isEmpty()) {
                    Map<Object, Object> sessionInfo = WebSocketSessionManager.getInstance().getSessionInfo(sessionId);
                    if (sessionInfo != null) {
                        String sessionName = (String) sessionInfo.get("name");
                        // 如果会话名称不匹配指定的targetName，则跳过该会话
                        if (!targetName.equals(sessionName)) {
                            continue;
                        }
                    }
                }
                
                try {
                    session.sendMessage(new TextMessage(message));
                    sentLocally = true;
                } catch (IOException e) {
                    LogUtil.error("发送WebSocket用户消息异常: " + e.getMessage());
                }
            }
        }
        
        // 如果本地没有发送成功，则通过Redis通知其他节点
        if (!sentLocally) {
            com.alibaba.fastjson.JSONObject msgData = new com.alibaba.fastjson.JSONObject();
            msgData.put("message", message);
            msgData.put("isJson", isJson);
            msgData.put("targetName", targetName);
            msgData.put("sourceNodeId", WebSocketSessionManager.getInstance().getNodeId());
            
            String channel = WebSocketRedisConfig.WS_USER_CHANNEL_PREFIX + userId;
            Base.map.publish(channel, msgData);
        }
    }
    
    /**
     * 检查用户是否有活跃的WebSocket连接
     */
    public static boolean hasActiveConnection(String userId) {
        Set<String> sessionIds = WebSocketSessionManager.getInstance().getSessionIdsByUserId(userId);
        return !sessionIds.isEmpty();
    }
} 