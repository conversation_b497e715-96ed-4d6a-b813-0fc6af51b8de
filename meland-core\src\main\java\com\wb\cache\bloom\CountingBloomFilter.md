# 计数布隆过滤器使用指南

计数布隆过滤器(Counting Bloom Filter)是标准布隆过滤器的一种变体，它不仅支持添加元素，还**支持删除元素**的操作，这是普通布隆过滤器无法实现的特性。

## 原理

普通布隆过滤器使用位数组（每个位只能是0或1），而计数布隆过滤器使用计数器数组，每个位置不再是单一的位，而是一个可以计数的计数器。当元素被添加时，对应位置的计数器加1；当元素被删除时，对应位置的计数器减1。

## 特点

1. **支持删除操作**：最大的特点是可以删除元素，而不影响其他元素的查询
2. **同样具有误判特性**：可能会误报元素存在，但不会漏报元素不存在
3. **空间消耗较大**：比标准布隆过滤器需要更多的空间，因为需要存储计数值

## 使用场景

1. **缓存键管理**：记录哪些键在缓存中，并支持在键过期时删除对应的布隆过滤器条目
2. **动态数据集**：需要频繁添加和删除元素的场景
3. **需要精确统计的场景**：例如访问计数、元素计数等

## 代码示例

### 1. 创建计数布隆过滤器

```java
// 通过RedisCache创建
CountingBloomFilter filter = redisCache.createCountingBloomFilter(
    "userIdFilter",  // 过滤器名称
    100000,          // 预期元素数量
    0.01,            // 误判率(1%)
    15               // 最大计数值(可选，默认15)
);

// 或者直接获取已有的过滤器
CountingBloomFilter existingFilter = redisCache.getCountingBloomFilter("userIdFilter");
```

### 2. 添加元素

```java
// 添加单个元素
filter.add("user:1001");

// 批量添加元素
List<String> userIds = Arrays.asList("user:1002", "user:1003", "user:1004");
filter.addAll(userIds);
```

### 3. 查询元素是否存在

```java
// 检查单个元素
boolean exists = filter.mightContain("user:1001");
if (exists) {
    System.out.println("用户ID可能存在");
} else {
    System.out.println("用户ID一定不存在");
}

// 批量检查元素
Map<String, Boolean> results = filter.multiMightContain(
    Arrays.asList("user:1001", "user:1002", "user:9999")
);
for (Map.Entry<String, Boolean> entry : results.entrySet()) {
    System.out.println(entry.getKey() + ": " + (entry.getValue() ? "可能存在" : "一定不存在"));
}
```

### 4. 删除元素

```java
// 删除单个元素
filter.remove("user:1001");

// 批量删除元素
filter.removeAll(Arrays.asList("user:1002", "user:1003"));
```

### 5. 与RedisCache结合使用

```java
// 使用计数布隆过滤器获取缓存值
Object cachedValue = redisCache.getWithCountingBloomFilter("user:1001", "userIdFilter");

// 使用计数布隆过滤器和加载函数获取缓存值
User user = redisCache.getWithCountingBloomFilter(
    "user:1001",                    // 缓存键
    "userIdFilter",                 // 过滤器名称
    key -> loadUserFromDb(key),     // 加载函数
    30,                             // 过期时间
    TimeUnit.MINUTES                // 时间单位
);

// 删除缓存值，同时从计数布隆过滤器中移除
redisCache.deleteWithCountingBloomFilter("user:1001", "userIdFilter");

// 批量删除缓存值，同时从计数布隆过滤器中移除
redisCache.multiDeleteWithCountingBloomFilter(
    Arrays.asList("user:1001", "user:1002"), 
    "userIdFilter"
);
```

### 6. 获取过滤器信息

```java
// 获取过滤器信息
Map<String, Object> info = filter.getInfo();
System.out.println("元素估计数量: " + info.get("approximateElementCount"));
System.out.println("当前误判率: " + info.get("currentFpp"));
System.out.println("非零计数器数量: " + info.get("nonZeroCounters"));

// 获取所有计数器值
Map<String, Integer> counters = filter.getAllCounters();
```

### 7. 清空过滤器

```java
// 清空过滤器中的所有数据
filter.clear();
```

## 性能考虑

1. 每个操作都需要多次Redis访问，建议使用批量方法以提高性能
2. 计数器最大值默认为15，可以在创建过滤器时调整
3. 在高并发场景下，删除操作可能会影响性能，建议在低峰期进行批量删除

## 注意事项

1. 删除不存在的元素可能会导致误判率上升
2. 计数布隆过滤器比普通布隆过滤器占用更多的空间
3. 不适合用于超大规模数据，因为每个元素操作都需要多次Redis访问 