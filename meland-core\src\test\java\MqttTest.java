import com.aliyun.onsmqtt20200420.Client;
import com.aliyun.onsmqtt20200420.models.ListGroupIdRequest;
import com.aliyun.onsmqtt20200420.models.ListGroupIdResponse;
import com.aliyun.onsmqtt20200420.models.ListGroupIdResponseBody;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;

import java.util.List;

public class MqttTest {

    /**
     * 使用AK&SK初始化账号Client
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */
    public static Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        Config config = new Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/OnsMqtt
        config.endpoint = "onsmqtt.cn-shenzhen.aliyuncs.com";
        return new Client(config);
    }

    public static void listGid(String[] args) throws Exception {
        // 请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例使用环境变量获取 AccessKey 的方式进行调用，仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html
        Client client = MqttTest.createClient(args[0], args[1]);
        ListGroupIdRequest listGroupIdRequest = new ListGroupIdRequest().setInstanceId("mqtt-cn-wwo3ioc0s02");
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            ListGroupIdResponse response = client.listGroupIdWithOptions(listGroupIdRequest, runtime);
            List<ListGroupIdResponseBody.ListGroupIdResponseBodyData> data = response.getBody().getData();
            for(int i=0;i<data.size();i++){
                System.out.println(data.get(i).getGroupId());
            }
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
    }

    public static void main(String[] args) throws Exception {
        listGid(new String[]{"LTAI4FjhWy6j1yrRiue9dFrT", "******************************"});
    }
}
