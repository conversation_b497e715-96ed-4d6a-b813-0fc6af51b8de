package com.wb.interact;

import com.wb.common.*;
import com.wb.util.*;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.util.ArrayList;
import java.util.Map.Entry;

/**
 * 门户及其相关的应用。
 */
public class Portal {
    /**
     * 递归获取模块列表树节点。
     *
     * @param request 请求对象
     * @param path    请求路径
     * @param roles   角色列表
     * @param type    数据来源类型(1:主页, 2:权限模块, 3:用户模块)
     * @return 模块列表的JSON数组
     */
    public static JSONArray getModuleList(HttpServletRequest request, String path, String[] roles, int type)
            throws Exception {

        // 确定显示类型
        int displayType = (type == 2) ? 3 : type;

        // 初始化基础路径
        File baseDir = initializeBasePath(request, path);
        String currentPath = path == null ? "" : path + "/";

        // 获取排序后的文件列表
        ArrayList<Entry<String, Integer>> sortedFiles = IDE.getSortedFile(baseDir);
        JSONArray moduleList = new JSONArray();

        // 处理每个文件
        for (Entry<String, Integer> entry : sortedFiles) {
            String fileName = entry.getKey();
            File currentFile = new File(baseDir, fileName);

            if (!currentFile.exists() || !XwlBuffer.canDisplay(currentFile, roles, displayType)) {
                continue;
            }

            JSONObject moduleInfo = createModuleInfo(request, currentFile, currentPath + fileName, type, roles);
            if (moduleInfo != null) {
                moduleList.put(moduleInfo);
            }
        }

        return moduleList;
    }

    /**
     * 创建模块信息的 JSON 对象。
     *
     * @param request  请求对象，用于获取国际化资源及用户上下文。
     * @param file     文件对象，表示要生成信息的模块文件或文件夹。
     * @param fullPath 文件的完整路径，用于读取模块内容或设置路径属性。
     * @param type     数据来源类型，用于区分模块用途，例如 1 表示主页，2 表示权限模块，3 表示用户模块。
     * @param roles    用户角色数组，用于权限控制，仅返回符合用户角色的模块信息。
     * @return 表示模块信息的 JSON 对象，包括模块标题、路径、子模块列表等。
     * @throws Exception 如果文件读取、路径解析或 JSON 处理过程中发生任何异常。
     */
    private static JSONObject createModuleInfo(HttpServletRequest request, File file, String fullPath, int type, String[] roles)
            throws Exception {
        JSONObject content = file.isDirectory() ?
                readFolderContent(file) :
                XwlBuffer.get(fullPath);

        JSONObject moduleInfo = new JSONObject();
        String title = Str.getText(request, content.optString("title"));
        String fileName = file.getName();

        // 设置基本信息
        moduleInfo.put("text", StringUtil.select(new String[]{title, fileName}));
        moduleInfo.put("path", FileUtil.getModulePath(file));
        moduleInfo.put("fileName", fileName);
        moduleInfo.put("inframe", Boolean.TRUE.equals(content.opt("inframe")));

        // 处理目录和文件的特殊属性
        if (file.isDirectory()) {
            moduleInfo.put("children", getModuleList(request, FileUtil.getModulePath(file), roles, type));
        } else {
            setFileSpecificProperties(moduleInfo, content);
        }

        // 设置额外属性
        setAdditionalProperties(moduleInfo, content, type);

        return moduleInfo;
    }

    /**
     * 初始化模块的基础路径。
     *
     * @param request 请求对象，用于权限校验及上下文访问。
     * @param path    模块的子路径，若为 null 则使用默认路径。
     * @return 返回初始化后的基础路径文件对象。
     * @throws Exception 如果路径不合法或权限校验未通过时抛出异常。
     */
    private static File initializeBasePath(HttpServletRequest request, String path) throws Exception {
        if (path == null) {
            return Base.modulePath;
        }

        File baseDir = new File(Base.modulePath, path);
        if (!FileUtil.isAncestor(Base.modulePath, baseDir)) {
            SysUtil.accessDenied(request);
        }
        return baseDir;
    }

    /**
     * 读取指定文件夹的内容信息。
     *
     * @param folder 表示目标文件夹的文件对象。
     * @return 如果文件夹中存在名为 "folder.json" 的文件，则返回该文件内容解析生成的 JSON 对象；
     *         否则返回一个空的 JSON 对象。
     * @throws Exception 如果读取文件或解析 JSON 时发生异常。
     */
    private static JSONObject readFolderContent(File folder) throws Exception {
        File configFile = new File(folder, "folder.json");
        return configFile.exists() ? JsonUtil.readObject(configFile) : new JSONObject();
    }

    /**
     * 设置文件的特定属性。
     *
     * @param moduleInfo 包含模块信息的 JSON 对象，将被更新以包含特定的文件信息。
     * @param content    包含文件源数据的 JSON 对象，用于提取并设置特定属性。
     */
    private static void setFileSpecificProperties(JSONObject moduleInfo, JSONObject content) {
        String pageLink = (String) content.opt("pageLink");
        if (!StringUtil.isEmpty(pageLink)) {
            moduleInfo.put("pageLink", pageLink);
        }
        moduleInfo.put("leaf", true);
    }

    /**
     * 设置附加属性到模块信息对象中。
     *
     * @param moduleInfo 包含模块信息的 JSON 对象，将被更新以包含附加的属性。
     * @param content    包含源数据的 JSON 对象，用于提取并设置附加属性。
     * @param type       数据来源类型，用于区分模块用途，例如 1 表示主页，2 表示权限模块。
     */
    private static void setAdditionalProperties(JSONObject moduleInfo, JSONObject content, int type) {
        if (type == 1) {
            moduleInfo.put("cls", "wb_pointer");
        }

        String iconCls = content.optString("iconCls");
        if (!StringUtil.isEmpty(iconCls)) {
            moduleInfo.put("iconCls", iconCls);
        }

        if (type == 2) {
            moduleInfo.put("checked", false);
        }
    }

    /**
     * 获取应用程序信息并将结果发送到响应中。
     *
     * @param request  请求对象，用于获取请求参数和进行权限验证。
     * @param response 响应对象，用于返回应用程序信息的结果。
     * @throws Exception 如果权限验证失败或处理过程中发生任何异常。
     */
    public static void getAppInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String url = FileUtil.getModulePath(request.getParameter("url"));
        if (!WbUtil.canAccess(request, url))
            SysUtil.accessDenied(request);
        WebUtil.send(response, WbUtil.getAppInfo(url, request));
    }

    /**
     * 初始化主页设置，根据用户的桌面配置和权限动态生成页面元素并设置到请求属性中。
     *
     * @param request  HTTP 请求对象，用于获取用户会话和相关参数。
     * @param response HTTP 响应对象，可能用于生成错误响应或用户界面输出。
     * @throws Exception 如果在处理过程中发生任何错误，则抛出异常。
     */
    public static void initHome(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String desktopString = Resource.getString(request, "desktop", null);

        String[] roles = Session.getRoles(request);

        if (desktopString == null)
            desktopString = Resource.getString("sys.home.desktop", null);
        String userOptions;
        int treeWidth;
        int viewIndex;
        boolean treeCollapsed;
        boolean treeHidden;
        boolean navIconHidden;
        if (desktopString != null) {
            JSONObject desktop = new JSONObject(desktopString);
            userOptions = desktop.optString("userOptions", "{}");
            treeWidth = desktop.optInt("treeWidth", 200);
            viewIndex = desktop.optInt("viewIndex", 2);
            treeCollapsed = desktop.optBoolean("treeCollapsed", false);
            treeHidden = desktop.optBoolean("treeHidden", false);
            navIconHidden = desktop.optBoolean("navIconHidden", true);
            JSONArray pages = desktop.optJSONArray("pages");
            if (pages != null) {
                int j = pages.length();
                int activeIndex = desktop.optInt("active", 0);

                JSONArray tabItems = new JSONArray();
                for (int i = 0; i < j; i++) {
                    JSONObject page = pages.optJSONObject(i);
                    String rawUrl = page.optString("url");
                    String url = FileUtil.getModulePath(rawUrl, true);
                    JSONObject module = XwlBuffer.get(url, true);
                    if ((module != null) && (!WbUtil.canAccess(module, roles)))
                        module = null;
                    if (module == null) {
                        if (i <= activeIndex)
                            activeIndex--;
                    } else {
                        JSONObject item = new JSONObject();
                        item.put("url", rawUrl);
                        String title = Str.getText(request, (String) module.opt("title"));
                        item.put("title", StringUtil.select(new String[]{title, FileUtil.getFilename(url)}));
                        item.put("iconCls", (String) module.opt("iconCls"));
                        item.put("inframe", Boolean.TRUE.equals(module.opt("inframe")));
                        String params = page.optString("params");
                        if (!StringUtil.isEmpty(params))
                            item.put("params", new JSONObject(params));
                        String pageLink = (String) module.opt("pageLink");
                        if (!StringUtil.isEmpty(pageLink))
                            JsonUtil.apply(item, new JSONObject(pageLink));
                        tabItems.put(item);
                    }
                }
                request.setAttribute("activeIndex", Integer.valueOf(Math.max(activeIndex, 0)));
                request.setAttribute("tabItems", StringUtil.text(tabItems.toString()));
            }
            JSONArray portlets = desktop.optJSONArray("portlets");
            if (portlets != null) {
                int j = portlets.length();

                for (int i = 0; i < j; i++) {
                    JSONArray cols = portlets.optJSONArray(i);
                    int l = cols.length();
                    for (int k = 0; k < l; k++) {
                        JSONObject portlet = cols.optJSONObject(k);
                        String url = FileUtil.getModulePath(portlet.optString("url"), true);
                        JSONObject module = XwlBuffer.get(url, true);
                        if ((module != null) && (!WbUtil.canAccess(module, roles)))
                            module = null;
                        if (module == null) {
                            cols.remove(k);
                            k--;
                            l--;
                        } else {
                            String title = Str.getText(request, (String) module.opt("title"));
                            portlet.put("title", StringUtil.select(new String[]{title, FileUtil.getFilename(url)}));
                            portlet.put("iconCls", (String) module.opt("iconCls"));
                            portlet.put("inframe", Boolean.TRUE.equals(module.opt("inframe")));
                            String pageLink = (String) module.opt("pageLink");
                            if (!StringUtil.isEmpty(pageLink))
                                JsonUtil.apply(portlet, new JSONObject(pageLink));
                        }
                    }
                }
                request.setAttribute("portlets", StringUtil.text(portlets.toString()));
            }
        } else {
            userOptions = "{}";
            treeWidth = 200;
            viewIndex = 2;
            treeCollapsed = false;
            treeHidden = false;
            navIconHidden = true;
        }
        request.setAttribute("treeWidth", Integer.valueOf(treeWidth));
        request.setAttribute("viewIndex", Integer.valueOf(viewIndex));
        request.setAttribute("treeCollapsed", Boolean.valueOf(treeCollapsed));
        request.setAttribute("treeHidden", Boolean.valueOf(treeHidden));
        request.setAttribute("navIconHidden", Boolean.valueOf(navIconHidden));
        request.setAttribute("userOptions", StringUtil.text(userOptions));
    }

    /**
     * 保存当前用户桌面。
     */
    public static void saveDesktop(HttpServletRequest request, HttpServletResponse response) throws Exception {
        doSaveDesktop(request, 1);
    }

    /**
     * 保存当前桌面为默认桌面。
     */
    public static void saveAsDefaultDesktop(HttpServletRequest request, HttpServletResponse response) throws Exception {
        doSaveDesktop(request, 2);
    }

    /**
     * 保存当前桌面为所有用户桌面。
     */
    public static void saveAsAllDesktop(HttpServletRequest request, HttpServletResponse response) throws Exception {
        doSaveDesktop(request, 3);
    }

    /**
     * 保存桌面信息。
     *
     * @param request 请求对象。
     * @param type    保存类型，1当前用户桌面，2默认桌面，3所有桌面
     */
    private static void doSaveDesktop(HttpServletRequest request, int type) throws Exception {
        JSONObject desktop = new JSONObject();
        desktop.put("treeWidth", Integer.parseInt(request.getParameter("treeWidth")));
        desktop.put("viewIndex", Integer.parseInt(request.getParameter("viewIndex")));
        desktop.put("treeCollapsed", Boolean.parseBoolean(request.getParameter("treeCollapsed")));
        desktop.put("treeHidden", Boolean.parseBoolean(request.getParameter("treeHidden")));
        desktop.put("navIconHidden", Boolean.parseBoolean(request.getParameter("navIconHidden")));
        desktop.put("pages", new JSONArray(request.getParameter("pages")));
        desktop.put("portlets", new JSONArray(request.getParameter("portlets")));
        desktop.put("active", Integer.parseInt(request.getParameter("active")));
        desktop.put("userOptions", request.getParameter("userOptions"));
        if (type == 1) {
            Resource.set(request, "desktop", desktop.toString());
        } else {
            if (type == 3)
                DbUtil.run(request, "delete from WB_RESOURCE where RES_ID like '%@desktop'");
            Resource.set("sys.home.desktop", desktop.toString());
        }
    }

    public static void saveUserOptions(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String desktopString = Resource.getString(request, "desktop", null);

        if (desktopString == null)
            desktopString = Resource.getString("sys.home.desktop", null);
        JSONObject desktop = new JSONObject(StringUtil.select(new String[]{desktopString, "{}"}));
        desktop.put("userOptions", request.getParameter("userOptions"));
        Resource.set(request, "desktop", desktop.toString());
    }

    public static String getAppListText(HttpServletRequest request) throws Exception {
        JSONObject result = new JSONObject();
        result.put("fileName", "Root");
        result.put("children", getModuleList(request, request.getParameter("path"), Session.getRoles(request), 1));
        return result.toString();
    }

    /**
     * 获取主页应用列表树的节点列表。
     */
    public static void getAppList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        WebUtil.send(response, getAppListText(request));
    }

    /**
     * 获取权限模块列表树的节点列表。
     */
    public static void getPermList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        WebUtil.send(response, new JSONObject().put("children",
                getModuleList(request, request.getParameter("path"), Session.getRoles(request), 2)));
    }

    /**
     * 获取用户模块权限模块列表树的节点列表。
     */
    public static void getUserPermList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        WebUtil.send(response, new JSONObject().put("children",
                getModuleList(request, request.getParameter("path"), Session.getRoles(request), 3)));
    }

    public static void getAllList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        WebUtil.send(response, new JSONObject().put("children",
                getModuleList(request, request.getParameter("path"), Session.getRoles(request), 4)));
    }

    /**
     * 设置桌面应用界面方案。
     */
    public static void setTheme(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String theme = request.getParameter("theme");

        Value.set(request, "theme", theme);
        WebUtil.setSessionValue(request, "sys.theme", theme);
    }

    /**
     * 设置移动应用界面方案。
     */
    public static void setTouchTheme(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String theme = request.getParameter("theme");

        Value.set(request, "touchTheme", theme);
        WebUtil.setSessionValue(request, "sys.touchTheme", theme);
    }

    /**
     * 初始化移动应用首页模块。
     */
    public static void initTouchHome(HttpServletRequest request, HttpServletResponse response) throws Exception {
        HttpSession session = request.getSession(false);
        boolean isNotLogin = (session == null) || (session.getAttribute("sys.logined") == null);
        request.setAttribute("isNotLogin", Integer.valueOf(isNotLogin ? 1 : 0));
    }

    /**
     * 搜索模块目录下指定名称的模块。
     */
    private static void searchModule(HttpServletRequest request, HttpServletResponse response, boolean isPerm)
            throws Exception {
        JSONArray array = new JSONArray();
        String query = request.getParameter("query").toLowerCase();
        String[] roles = Session.getRoles(request);
        if (query.isEmpty())
            query = ".xwl";
        doSearchFile(request, Base.modulePath, query.toLowerCase(), "", "", array, isPerm, roles);
        WebUtil.send(response, new JSONObject().put("rows", array));
    }

    /**
     * 搜索主页模块目录下指定名称的模块。
     */
    public static void searchAppModule(HttpServletRequest request, HttpServletResponse response) throws Exception {
        searchModule(request, response, false);
    }

    /**
     * 搜索权限模块目录下指定名称的模块。
     */
    public static void searchPermModule(HttpServletRequest request, HttpServletResponse response) throws Exception {
        searchModule(request, response, true);
    }

    /**
     * 在指定目录下搜索文件。内部函数，用于递归搜索。
     *
     * @param request    请求对象。
     * @param folder     目录。
     * @param searchName 查找的文件名称关键字，任何包括该关键字的文件均将被列举。
     * @param parentText 上级目录显示名称。
     * @param array      用于存放搜索结果。
     * @param isPerm     是否来自权限设置模块。
     * @param roles      当前用户角色列表。
     * @return 是否完成搜索，如果搜索结果大于等于100项，将返回true，否则返回false。
     * @throws Exception 搜索过程中发生异常。
     */
    private static boolean doSearchFile(HttpServletRequest request, File folder, String searchName, String parentText,
                                        String parentFile, JSONArray array, boolean isPerm, String[] roles) throws Exception {
        File[] files = FileUtil.listFiles(folder);

        for (File file : files)
            if (XwlBuffer.canDisplay(file, roles, isPerm ? 3 : 1)) {
                if (file.isDirectory()) {
                    File indexFile = new File(file, "folder.json");
                    String folderTitle, folderFile = file.getName();
                    if (indexFile.exists()) {
                        JSONObject jo = JsonUtil.readObject(indexFile);
                        folderTitle = jo.optString("title");
                        if (folderTitle.isEmpty())
                            folderTitle = folderFile;
                    } else {
                        folderTitle = folderFile;
                    }
                    folderTitle = Str.getText(request, folderTitle);
                    // 查找权限时只查找模块不查找目录
                    if ((!isPerm) && (folderTitle.toLowerCase().indexOf(searchName) != -1)) {
                        JSONObject jo = new JSONObject();
                        jo.put("path", parentText);
                        jo.put("title", folderTitle);
                        jo.put("file", folderFile);
                        jo.put("parentFile", parentFile);
                        jo.put("isFolder", true);
                        array.put(jo);
                        if (array.length() > 99) {
                            return true;
                        }
                    }

                    if (doSearchFile(request, file, searchName,
                            StringUtil.concat(new String[]{parentText, "/", folderTitle}),
                            StringUtil.concat(new String[]{parentFile, "/", folderFile}), array, isPerm, roles))
                        return true;
                } else {
                    String path = FileUtil.getModulePath(file);
                    if (path.endsWith(".xwl")) {
                        JSONObject moduleData = XwlBuffer.get(path);
                        String title = moduleData.optString("title");
                        if (title.isEmpty())
                            title = path.substring(path.lastIndexOf('/') + 1);
                        title = Str.getText(request, title);
                        if (title.toLowerCase().indexOf(searchName) != -1) {
                            JSONObject jo = new JSONObject();
                            jo.put("path", parentText);
                            jo.put("title", title);
                            jo.put("file", file.getName());
                            jo.put("parentFile", parentFile);
                            jo.put("isFolder", false);
                            array.put(jo);
                            if (array.length() > 99)
                                return true;
                        }
                    }
                }
            }
        return false;
    }

    /**
     * 获取移动模块列表。
     */
    public static void getMobileAppList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        ArrayList<JSONObject> appList = new ArrayList<JSONObject>();
        JSONArray outputList = new JSONArray();

        String[] roles = Session.getRoles(request);
        scanMobileApp(request, appList, new File(Base.modulePath, "apps"), roles);
        for (JSONObject jo : appList)
            outputList.put(jo);
        WebUtil.send(response, new JSONObject().put("rows", outputList));
    }

    /**
     * 扫描指定路径下的移动应用模块并生成应用列表。
     *
     * @param request 请求对象，用于国际化资源解析和权限过滤。
     * @param appList 存储扫描结果的应用列表，每个元素是一个JSON对象，表示一个应用模块的信息。
     * @param path    文件路径，表示需要扫描的模块目录。
     * @param roles   用户角色数组，用于权限控制。
     * @throws Exception 如果在扫描过程中发生任何异常。
     */
    private static void scanMobileApp(HttpServletRequest request, ArrayList<JSONObject> appList, File path,
                                      String[] roles) throws Exception {
        JSONObject content, item, viewport;
        ArrayList<Entry<String, Integer>> fileNames = IDE.getSortedFile(path);
        String title, url, image, glyph, fileName;
        File file;

        for (Entry<String, Integer> entry : fileNames) {
            fileName = entry.getKey();
            file = new File(path, fileName);
            if (!XwlBuffer.canDisplay(file, roles, 2))
                continue;
            if (file.isDirectory()) {
                scanMobileApp(request, appList, file, roles);
            } else {
                url = FileUtil.getModulePath(file);
                content = XwlBuffer.get(url, true);
                if (content == null)
                    continue;
                viewport = getViewport(content);
                // 没有Viewport的模块不能作为应用
                if (viewport == null)
                    continue;
                viewport = viewport.getJSONObject("configs");
                item = new JSONObject();
                title = Str.getText(request, content.optString("title"));
                item.put("title", StringUtil.select(title, file.getName()));
                image = viewport.optString("appImage");
                if (image.isEmpty()) {
                    glyph = viewport.optString("appGlyph");
                    if (glyph.isEmpty())
                        item.put("glyph", "&#xf10a;");
                    else
                        item.put("glyph", StringUtil.concat("&#x", glyph, ";"));
                } else
                    item.put("image", image);
                item.put("url", url);
                appList.add(item);
            }
        }
    }

    /**
     * 获取指定节点下的 viewport 节点。
     *
     * @param rootNode 根节点的 JSON 对象，从中查找目标节点。
     * @return 如果找到 type 为 "tviewport" 的 JSON 节点，则返回该节点；如果未找到，返回 null。
     * @throws Exception 如果在处理 JSON 数据时发生错误。
     */
    private static JSONObject getViewport(JSONObject rootNode) throws Exception {
        JSONObject module = (JSONObject) ((JSONArray) rootNode.opt("children")).opt(0);

        JSONArray items = (JSONArray) module.opt("children");
        if (items == null)
            return null;
        int j = items.length();
        for (int i = 0; i < j; i++) {
            JSONObject jo = (JSONObject) items.opt(i);
            if ("tviewport".equals(jo.opt("type")))
                return jo;
        }
        return null;
    }
}