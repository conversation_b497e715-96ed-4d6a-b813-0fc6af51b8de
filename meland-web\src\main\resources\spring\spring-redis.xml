<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                        https://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- 配置JedisPoolConfig -->
    <bean id="jedisPoolConfig"
          class="redis.clients.jedis.JedisPoolConfig">
        <property name="maxTotal" value="${jredis.maxTotal}"/>
        <property name="maxIdle" value="${jredis.maxIdle}"/>
        <property name="numTestsPerEvictionRun" value="${jredis.numTestsPerEvictionRun}"/>
        <property name="timeBetweenEvictionRunsMillis" value="${jredis.timeBetweenEvictionRunsMillis}"/>
        <property name="minEvictableIdleTimeMillis" value="${jredis.minEvictableIdleTimeMillis}"/>
        <property name="softMinEvictableIdleTimeMillis" value="${jredis.softMinEvictableIdleTimeMillis}"/>
        <property name="maxWaitMillis" value="${jredis.maxWaitMillis}"/>
        <property name="testOnBorrow" value="${jredis.testOnBorrow}"/>
        <property name="testWhileIdle" value="${jredis.testWhileIdle}"/>
        <property name="blockWhenExhausted" value="${jredis.blockWhenExhausted}"/>
    </bean>

    <!-- 配置Redis哨兵 -->
    <bean id="redisSentinelConfiguration" 
          class="org.springframework.data.redis.connection.RedisSentinelConfiguration">
        <property name="master">
            <bean class="org.springframework.data.redis.connection.RedisNode">
                <property name="name" value="mymaster"/>
            </bean>
        </property>
        <property name="sentinels">
            <set>
                <bean class="org.springframework.data.redis.connection.RedisNode">
                    <constructor-arg name="host" value="${redis.address}"/>
                    <constructor-arg name="port" value="${redis.port}"/>
                </bean>
                <!-- 可以添加更多哨兵节点 -->
            </set>
        </property>
    </bean>

    <!-- 配置JedisConnectionFactory - 使用哨兵配置 -->
    <bean id="jedisConnectionFactory"
          class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory"
          destroy-method="destroy">
        <!-- 使用哨兵配置 - 如果已经配置哨兵模式，请启用此配置替换下方单机配置 -->
        <!-- <constructor-arg ref="redisSentinelConfiguration"/> -->
        <!-- <property name="password" value="${redis.password}"/> -->
        <!-- <property name="timeout" value="5000"/> -->
        <!-- <property name="usePool" value="true"/> -->
        <!-- <property name="poolConfig" ref="jedisPoolConfig"/> -->
        <!-- <property name="database" value="${redis.database}"/> -->
        
        <!-- 单机配置 - 当前使用此配置 -->
        <property name="hostName" value="${redis.address}"/>
        <property name="port" value="${redis.port}"/>
        <property name="password" value="${redis.password}"/>
        <property name="timeout" value="10000"/>
        <property name="usePool" value="true"/>
        <property name="poolConfig" ref="jedisPoolConfig"/>
        <property name="database" value="${redis.database}"/>
    </bean>

    <bean id="redisTemplate"
          class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory"
                  ref="jedisConnectionFactory"/>
        <property name="keySerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
        </property>
        <property name="valueSerializer">
            <bean class="com.wb.cache.GenericFastJson2JsonRedisSerializer"/>
        </property>
        <property name="hashKeySerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
        </property>
        <property name="hashValueSerializer">
            <bean class="com.wb.cache.GenericFastJson2JsonRedisSerializer"/>
        </property>
    </bean>

    <!-- 定义默认的字符串序列化 -->
    <bean id="stringRedisTemplate" class="org.springframework.data.redis.core.StringRedisTemplate">
        <property name="connectionFactory" ref="jedisConnectionFactory"/>
    </bean>


    <!-- 定义监听类 -->
    <bean id="redisMessageListener" class="com.wb.cache.RedisMessageListener">
        <property name="redisTemplate" ref="redisTemplate"/>
    </bean>


    <!-- 定义监听容器 -->
    <bean id="redisMessageListenerContainer"
          class="org.springframework.data.redis.listener.RedisMessageListenerContainer"
          destroy-method="destroy">
        <property name="connectionFactory" ref="jedisConnectionFactory"/>
        <!-- 设置恢复间隔，加快重连速度 -->
        <property name="recoveryInterval" value="3000"/>
        <!-- 任务执行器 -->
        <property name="taskExecutor">
            <bean class="org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler">
                <property name="poolSize" value="10"/>
                <property name="threadNamePrefix" value="redis-listener-"/>
                <property name="waitForTasksToCompleteOnShutdown" value="true"/>
                <property name="awaitTerminationSeconds" value="60"/>
            </bean>
        </property>
        <!-- 消息监听器 -->
        <property name="messageListeners">
            <map>
                <entry key-ref="redisMessageListener">
                    <list>
                        <!-- 添加MQ相关的监听频道 -->
                        <bean class="org.springframework.data.redis.listener.ChannelTopic">
                            <constructor-arg value="chat_mq"/>
                        </bean>
                        <!-- 添加XWL相关的监听频道 -->  
                        <bean class="org.springframework.data.redis.listener.ChannelTopic">
                            <constructor-arg value="chat_xwl"/>
                        </bean>
                        <!-- 添加Role相关的监听频道 -->
                        <bean class="org.springframework.data.redis.listener.ChannelTopic">
                            <constructor-arg value="chat_role"/>
                        </bean>
                        <!-- 添加KV相关的监听频道 -->
                        <bean class="org.springframework.data.redis.listener.ChannelTopic">
                            <constructor-arg value="chat_kv"/>
                        </bean>
                        <!-- 添加Var相关的监听频道 -->
                        <bean class="org.springframework.data.redis.listener.ChannelTopic">
                            <constructor-arg value="chat_var"/>
                        </bean>
                        <!-- 添加Reload相关的监听频道 -->
                        <bean class="org.springframework.data.redis.listener.ChannelTopic">
                            <constructor-arg value="chat_reload"/>
                        </bean>
                        <!-- 添加缓存相关的监听频道 -->
                        <bean class="org.springframework.data.redis.listener.ChannelTopic">
                            <constructor-arg value="cache:invalidate"/>
                        </bean>
                        <bean class="org.springframework.data.redis.listener.ChannelTopic">
                            <constructor-arg value="cache:invalidate:all"/>
                        </bean>
                        <bean class="org.springframework.data.redis.listener.ChannelTopic">
                            <constructor-arg value="cache:update"/>
                        </bean>
                        <!-- 添加熔断器和限流器相关的监听频道 -->
                        <bean class="org.springframework.data.redis.listener.ChannelTopic">
                            <constructor-arg value="circuit:breaker:config:updated"/>
                        </bean>
                        <bean class="org.springframework.data.redis.listener.ChannelTopic">
                            <constructor-arg value="circuit:breaker:events"/>
                        </bean>
                        <bean class="org.springframework.data.redis.listener.ChannelTopic">
                            <constructor-arg value="rate:limit:config:updated"/>
                        </bean>
                        <bean class="org.springframework.data.redis.listener.ChannelTopic">
                            <constructor-arg value="cluster:mode"/>
                        </bean>
                        <!-- 添加WebSocket相关的监听频道 -->
                        <bean class="org.springframework.data.redis.listener.ChannelTopic">
                            <constructor-arg value="websocket:broadcast"/>
                        </bean>
                        <bean class="org.springframework.data.redis.listener.PatternTopic">
                            <constructor-arg value="websocket:user:*"/>
                        </bean>
                    </list>
                </entry>
            </map>
        </property>
    </bean>
    <!-- 启用基于 Redis 的 session 共享 -->
    <bean class="org.springframework.session.data.redis.config.annotation.web.http.RedisHttpSessionConfiguration">
        <!-- 会话超时时间，单位：秒 -->
        <property name="maxInactiveIntervalInSeconds" value="1800" /> <!-- 30分钟 -->
        <!-- 会话redis前缀 -->
        <property name="redisNamespace" value="spring:session" />
        <!-- 清理过期会话的调度频率 -->
        <property name="cleanupCron" value="0 */30 * * * *" /> <!-- 每30分钟 -->
    </bean>
    
    <!-- Redis异常处理器 - 确保Redis异常能被正确处理 -->
    <bean id="redisExceptionHandler" class="com.wb.cache.RedisExceptionHandler" />
    
    <!-- 自动配置RedisMessageListenerContainer的后处理器 -->
    <bean class="com.wb.cache.RedisHandlerAutoConfiguration" />
</beans>