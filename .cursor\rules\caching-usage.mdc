---
description: 
globs: 
alwaysApply: false
---
# 缓存使用规则

本项目使用 Redis 进行缓存，核心实现位于 [`meland-core/src/main/java/com/wb/cache/RedisCache.java`](mdc:meland-core/src/main/java/com/wb/cache/RedisCache.java)。

**调用方式:**

后端Java和XWL的ServerScript中都可以通过静态对象 `Base.map` 来调用 `RedisCache` 类中定义的缓存操作方法。

**主要功能:**

*   基本的键值对存取 (`setValue`, `getValue`, `delKey`)
*   哈希表操作 (`hashPut`, `hashGet`, `hashGetAll`)
*   列表操作 (`listPush`, `listGet`)
*   集合操作 (`setSet`, `getSetAll`)
*   有序集合操作 (`zsetSet`, `zsetRange`)
*   分布式锁 (`tryLock`, `unLock`)
*   缓存高级特性：
    *   本地缓存 (`localCache`)
    *   缓存穿透防护 (`getWithProtection`, `getWithLoader`, `NULL_VALUE`)
    *   缓存雪崩防护 (随机化过期时间 `getRandomizedExpireTime`)
    *   缓存击穿防护 (互斥锁 `getWithMutex`)
    *   布隆过滤器 (`RedisBloomFilter`, `CountingBloomFilter`)
*   缓存统计 (`CacheStats`)
*   管道操作 (`executePipeline`)
*   集群兼容模式处理 (`RedisConfiguration`)

**注意事项:**

*   调用缓存方法前请确保了解各个方法的用途和参数。
*   对于需要加载数据的缓存场景，优先使用带有加载函数的 `getWithLoader` 或 `getWithMutex` 方法。
*   对于可能存在大量不存在的key查询，考虑使用布隆过滤器进行优化 (`getWithBloomFilter`, `getWithCountingBloomFilter`)。

