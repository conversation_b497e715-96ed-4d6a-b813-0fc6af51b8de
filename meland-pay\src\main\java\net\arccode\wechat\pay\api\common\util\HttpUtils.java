package net.arccode.wechat.pay.api.common.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.SocketTimeoutException;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.net.ssl.SSLContext;

import okhttp3.*;
import org.apache.commons.codec.binary.Hex;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.ConnectionPoolTimeoutException;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.HTTP;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.wb.common.Var;
import weixin.popular.util.SignatureUtil;

/**
 * <pre>
 * Http工具, 普通使用OkHTTP; 对于需要加载证书访问的情况, 暂时使用HttpClient实现
 *
 * OkHttp官方文档并不建议我们创建多个OkHttpClient,因此全局使用一个.
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2015-09-09
 */
public class HttpUtils {

    private static final Logger LOG = LoggerFactory.getLogger(HttpUtils.class);

    private static final OkHttpClient mOkHttpClient = new OkHttpClient();

    //表示请求器是否已经做了初始化工作
    private static boolean hasInit = false;

    //连接超时时间，默认10秒
    private static int socketTimeout = 10000;

    //请求器的配置
    private static RequestConfig requestConfig;

    //传输超时时间，默认30秒
    private static int connectTimeout = 30000;

    //HTTP请求器
    private static CloseableHttpClient httpClient;

    /**
     * 初始化 SSL
     *
     * @throws IOException
     * @throws KeyStoreException
     * @throws UnrecoverableKeyException
     * @throws NoSuchAlgorithmException
     * @throws KeyManagementException
     */
    @SuppressWarnings("deprecation")
    private static void init(String certPwd, String certPath) throws IOException, KeyStoreException,
            UnrecoverableKeyException, NoSuchAlgorithmException, KeyManagementException {

        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        FileInputStream instream = new FileInputStream(new File(certPath));//加载本地的证书进行https加密传输
        try {
            keyStore.load(instream, certPwd.toCharArray());//设置证书密码
        } catch (CertificateException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } finally {
            instream.close();
        }
        // Trust own CA and all self-signed certs
        SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(keyStore, certPwd.toCharArray()).build();
        // Allow TLSv1 protocol only
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslcontext, new String[]{"TLSv1.2"}, null,
                SSLConnectionSocketFactory.getDefaultHostnameVerifier());
        httpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
        //根据默认超时限制初始化requestConfig
        requestConfig = RequestConfig.custom().setSocketTimeout(socketTimeout).setConnectTimeout(connectTimeout)
                .build();
        hasInit = true;
    }

    /**
     * 该不会开启异步线程。
     *
     * @param request
     * @return
     * @throws java.io.IOException
     */
    public static Response execute(Request request) throws IOException {
        return mOkHttpClient.newCall(request).execute();
    }

    /**
     * 对上次文件进行MD5获取其Hash值
     *
     * @param fis
     * @return
     */
    public static String md5HashCode(InputStream fis) {
        try {
            MessageDigest MD5 = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[8192];
            int length;
            while ((length = fis.read(buffer)) != -1) {
                MD5.update(buffer, 0, length);
            }
            return new String(Hex.encodeHex(MD5.digest()));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 微信商户图片上传接口
     *
     * @param url      请求地址
     * @param file     文件
     * @param certPwd  证书密码
     * @param certPath 证书地址
     * @return 上传结果
     * @throws Exception
     */
    public static String mchUploadMedia(String url, File file, String certPwd, String certPath) throws Exception {
        if (!hasInit) {
            init(certPwd, certPath);
        }
        String media_hash = md5HashCode(new FileInputStream(file));
        String sign_type = "HMAC-SHA256";
        String mch_id = Var.getString("sys.config.accounts.Partner");
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("mch_id", mch_id);
        paramMap.put("media_hash", media_hash);
        paramMap.put("sign_type", sign_type);
        String sign = SignatureUtil.generateSign(paramMap, sign_type, Var.getString("sys.config.accounts.PartnerKey"));

        MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
        multipartEntityBuilder.addTextBody("mch_id", mch_id, ContentType.MULTIPART_FORM_DATA);
        multipartEntityBuilder.addBinaryBody("media", file, ContentType.create("image/jpg"), file.getName());
        multipartEntityBuilder.addTextBody("media_hash", media_hash, ContentType.MULTIPART_FORM_DATA);
        multipartEntityBuilder.addTextBody("sign_type", sign_type, ContentType.MULTIPART_FORM_DATA);
        multipartEntityBuilder.addTextBody("sign", sign, ContentType.MULTIPART_FORM_DATA);

        String result = null;
        HttpPost httpPost = new HttpPost(url);
        ////这里的Content-type要设置为"multipart/form-data"，否则返回“参数填写有误，请检查后重试”
        httpPost.addHeader(HTTP.CONTENT_TYPE, "multipart/form-data; charset=UTF-8");
        httpPost.addHeader(HTTP.USER_AGENT, "wxpay sdk java v1.0 " + mch_id);
        httpPost.setEntity(multipartEntityBuilder.build());
        //设置请求器的配置
        httpPost.setConfig(requestConfig);
        try {
            HttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            result = EntityUtils.toString(entity, "UTF-8");
        } catch (ConnectionPoolTimeoutException e) {
            LOG.warn("http get throw ConnectionPoolTimeoutException(wait time out)");
        } catch (ConnectTimeoutException e) {
            LOG.warn("http get throw ConnectTimeoutException");
        } catch (SocketTimeoutException e) {
            LOG.warn("http get throw SocketTimeoutException");
        } catch (Exception e) {
            LOG.error("mchUploadMedia请求出现了异常", e);
            throw e;
        } finally {
            httpPost.abort();
        }
        return result;
    }

    /**
     * 带证书发送请求, 该不会开启异步线程。
     *
     * @param url
     * @param body
     * @param certPwd
     * @param certPath
     * @return
     * @throws Exception
     */
    public static String executeAttachCA(String url, String body, String certPwd, String certPath) throws Exception {
        if (!hasInit) {
            init(certPwd, certPath);
        }
        String result = null;
        HttpPost httpPost = new HttpPost(url);
        //解决XStream对出现双下划线的bug
        LOG.info("API，POST过去的数据是：");
        LOG.info(body);
        //得指明使用UTF-8编码，否则到API服务器XML的中文不能被成功识别
        StringEntity postEntity = new StringEntity(body, "UTF-8");
        httpPost.addHeader("Content-Type", "text/xml");
        httpPost.setEntity(postEntity);
        //设置请求器的配置
        httpPost.setConfig(requestConfig);
        LOG.info("executing request" + httpPost.getRequestLine());
        try {
            HttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            result = EntityUtils.toString(entity, "UTF-8");
        } catch (ConnectionPoolTimeoutException e) {
            LOG.warn("http get throw ConnectionPoolTimeoutException(wait time out)");
        } catch (ConnectTimeoutException e) {
            LOG.warn("http get throw ConnectTimeoutException");
        } catch (SocketTimeoutException e) {
            LOG.warn("http get throw SocketTimeoutException");
        } catch (Exception e) {
            LOG.error("executeAttachCA请求出现了异常", e);
            throw e;
        } finally {
            httpPost.abort();
        }
        return result;
    }

    /**
     * 开启异步线程访问网络
     *
     * @param request
     * @param responseCallback
     */
    public static void enqueue(Request request, Callback responseCallback) {
        mOkHttpClient.newCall(request).enqueue(responseCallback);
    }

    /**
     * 开启异步线程访问网络, 且不在意返回结果（实现空callback）
     *
     * @param request
     */
    public static void enqueue(Request request) {
        mOkHttpClient.newCall(request).enqueue(new Callback() {

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {

            }

            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {

            }
        });
    }

    public static String getStringFromServer(String url) throws IOException {
        Request request = new Request.Builder().url(url).build();
        Response response = execute(request);
        if (response.isSuccessful()) {
            String responseUrl = response.body().string();
            return responseUrl;
        } else {
            throw new IOException("Unexpected code " + response);
        }
    }
}
