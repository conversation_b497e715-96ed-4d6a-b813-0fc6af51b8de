package com.wb.aliyun.sls;

import com.wb.util.LogUtil;

/**
 * 阿里云日志服务配置类
 * 设置SLS客户端线程为守护线程
 */
public class SLSConfiguration {
    
    private static boolean initialized = false;

    /**
     * 初始化阿里云日志服务配置
     */
    public static synchronized void init() {
        if (!initialized) {
            try {
                // 设置阿里云日志服务线程为守护线程
                System.setProperty("aliyun.log.producer.daemon", "true");
                System.setProperty("aliyun.log.threadFactory.daemon", "true");
                LogUtil.info("阿里云日志服务客户端守护线程配置已设置");
                initialized = true;
            } catch (Exception e) {
                LogUtil.error("设置阿里云日志服务客户端守护线程配置失败: " + e.getMessage());
            }
        }
    }
} 