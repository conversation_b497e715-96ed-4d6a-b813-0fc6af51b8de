package com.wb.message.util;

import java.util.concurrent.ConcurrentHashMap;

import org.json.JSONArray;
import org.json.JSONObject;

import com.wb.common.Var;
import com.wb.message.MessageConsume;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;

/**
 * 消息参数转换
 * 
 * <AUTHOR>
 *
 */
public class ParamUtil {

	/**
	 * 根据code获取微信小程序消息参数
	 * 
	 * @param param
	 * @param code
	 * @return
	 */
	public static JSONArray getSmallParams(JSONObject param, String code) {
		JSONArray temp = getSmallParams(param, null, code, null);
		return temp;
	}

	/**
	 * 根据code获取微信小程序消息参数
	 * 
	 * @param param
	 * @param code
	 * @param title
	 * @return
	 */
	public static JSONArray getSmallParams(JSONObject param, String code, String title) {
		JSONArray temp = getSmallParams(param, null, code, title);
		return temp;
	}

	/**
	 * 根据arg获取微信小程序消息参数
	 * 
	 * @param param
	 * @param arg
	 * @param title
	 * @return
	 */
	public static JSONArray getSmallParams(JSONObject param, JSONObject arg, String title) {
		JSONArray temp = getSmallParams(param, arg, null, title);
		return temp;
	}

	/**
	 * 根据arg获取微信小程序消息参数
	 * 
	 * @param param
	 * @param arg
	 * @return
	 */
	public static JSONArray getSmallParams(JSONObject param, JSONObject arg) {
		JSONArray temp = getSmallParams(param, arg, null, null);
		return temp;
	}

	/**
	 * 获取小程序消息参数
	 * 
	 * @param param
	 * @param arg
	 * @param code
	 * @param title
	 * @return
	 */
	public static JSONArray getSmallParams(JSONObject param, JSONObject arg, String code, String title) {
		JSONArray temp;
		try {
			// 默认parms
			JSONObject temparms = new JSONObject();
			if (code != null) {
				// 获取消息固定参数
				ConcurrentHashMap<String, String> argConfig = MessageConsume.msg_arg.get(code);
				JSONObject args = new JSONObject(argConfig.get("arg"));
				temparms = args.getJSONObject("parms");
			}
			if (arg != null) {
				temparms = arg.getJSONObject("parms");
			}

			// 获取配置文件parms参数
			if (!temparms.has("small")) {
				LogUtil.warn(StringUtil.format("消息[{0}]没有参数[small]，无法发送小程序消息", title));
				return null;
			}
			// 重新参数值判断
			if (StringUtil.isEmpty(param.optString("temparms")) || param.optString("temparms").equals("{}")) {
				LogUtil.warn(StringUtil.format("消息[{0}]没有参数[temparms]，无法发送小程序消息", title));
				return null;
			}
			// 固定参数值判断
			if (StringUtil.isEmpty(temparms.optString("editKey")) || temparms.optString("editKey").equals("[]")) {
				LogUtil.warn(StringUtil.format("消息[{0}]没有默认参数[editKey]，无法发送小程序消息", title));
				return null;
			}
			if (StringUtil.isEmpty(temparms.optString("editValue")) || temparms.optString("editValue").equals("")) {
				LogUtil.warn(StringUtil.format("消息[{0}]没有默认参数[editValue]，无法发送小程序消息", title));
				return null;
			}

			// UTF-8 转码 重新传入参数
			String keyStr = new String(param.getJSONObject("temparms").toString().getBytes("UTF-8"), "UTF-8");
			JSONObject keys = new JSONObject(keyStr);
			// 拼接参数
			temp = temparms.getJSONArray("small");
			// 自定义模板键
			JSONArray editKey = temparms.getJSONArray("editKey");
			// 固定值键
			String editValue = temparms.getString("editValue");
			for (Object obj : editKey) {
				JSONObject prm = (JSONObject) obj;
				String v = keys.get(prm.getString("key")).toString();
				if ("".equals(v)) {
					continue;
				}
				JSONObject sm = new JSONObject();
				sm.put("key", prm.getString("name"));
				sm.put(editValue, v);
				temp.put(sm);
			}
		} catch (Exception e) {
			return new JSONArray();
		}
		return temp;
	}

	/**
	 * 根据code获取微信公众号消息参数
	 * 
	 * @param param
	 * @param code
	 * @return
	 */
	public static JSONObject getWxParams(JSONObject param, String code) {
		JSONObject temp = getWxParams(param, null, code, null);
		return temp;
	}

	/**
	 * 根据code获取微信公众号消息参数
	 * 
	 * @param param
	 * @param code
	 * @param title
	 * @return
	 */
	public static JSONObject getWxParams(JSONObject param, String code, String title) {
		JSONObject temp = getWxParams(param, null, code, title);
		return temp;
	}

	/**
	 * 根据data获取微信公众号消息参数
	 * 
	 * @param param
	 * @param arg
	 * @param title
	 * @return
	 */
	public static JSONObject getWxParams(JSONObject param, JSONObject arg, String title) {
		JSONObject temp = getWxParams(param, arg, null, title);
		return temp;
	}

	/**
	 * 根据data获取微信公众号消息参数
	 * 
	 * @param param
	 * @param arg
	 * @return
	 */
	public static JSONObject getWxParams(JSONObject param, JSONObject arg) {
		JSONObject temp = getWxParams(param, arg, null, null);
		return temp;
	}

	/**
	 * 获取微信公众号消息参数
	 * 
	 * @param param
	 * @param arg
	 * @param code
	 * @param title
	 * @return
	 */
	private static JSONObject getWxParams(JSONObject param, JSONObject arg, String code, String title) {
		System.err.println(arg);
		JSONObject temp;
		try {
			// 默认parms
			JSONObject temparms = null;
			if (code != null) {
				// 获取消息固定参数
				ConcurrentHashMap<String, String> argConfig = MessageConsume.msg_arg.get(code);
				JSONObject args = new JSONObject(argConfig.get("arg"));
				temparms = args.getJSONObject("parms");
			}else if (arg != null) {
				temparms = arg.getJSONObject("parms");
			}

			// 获取配置文件parms参数
			if (!temparms.has("template")) {
				LogUtil.warn(StringUtil.format("消息[{0}]没有参数[template]，无法发送模板消息", title));
				return null;
			}
			if (!param.has("temparms")) {
				LogUtil.warn(StringUtil.format("消息[{0}]没有参数[temparms]，无法发送模板消息", title));
				return null;
			}
			// 重新参数值判断
			if (StringUtil.isEmpty(param.optString("temparms")) || param.optString("temparms").equals("{}")) {
				LogUtil.warn(StringUtil.format("消息[{0}]没有参数[temparms]，无法发送模板消息", title));
				return null;
			}
			// 固定参数值判断
			if (StringUtil.isEmpty(temparms.optString("template")) || temparms.optString("template").equals("")) {
				LogUtil.warn(StringUtil.format("消息[{0}]没有默认参数[template]，无法发送模板消息", title));
				return null;
			}
			if (StringUtil.isEmpty(temparms.optString("temparms")) || temparms.optString("temparms").equals("{}")) {
				LogUtil.warn(StringUtil.format("消息[{0}]没有默认参数[temparms]，无法发送模板消息", title));
				return null;
			}
			if (StringUtil.isEmpty(temparms.optString("editKey")) || temparms.optString("editKey").equals("[]")) {
				LogUtil.warn(StringUtil.format("消息[{0}]没有默认参数[editKey]，无法发送模板消息", title));
				return null;
			}
			if (StringUtil.isEmpty(temparms.optString("editValue")) || temparms.optString("editValue").equals("")) {
				LogUtil.warn(StringUtil.format("消息[{0}]没有默认参数[editValue]，无法发送模板消息", title));
				return null;
			}

			// UTF-8 转码 重新传入参数
			String keyStr = new String(param.getJSONObject("temparms").toString().getBytes("UTF-8"), "UTF-8");
			JSONObject keys = new JSONObject(keyStr);
			// 拼接参数
			temp = temparms.getJSONObject("temparms");
			// 自定义模板键
			JSONArray editKey = temparms.getJSONArray("editKey");
			// 固定值键
			String editValue = temparms.getString("editValue");
			for (Object obj : editKey) {
				JSONObject prm = (JSONObject) obj;
				String v = keys.get(prm.getString("key")).toString();
				if (v.equals(""))
					continue;
				temp.getJSONObject(prm.getString("keyType")).put(editValue, v);
			}
		} catch (Exception e) {
			System.err.println(e.getStackTrace());
			return null;
		}
		return temp;
	}

	/**
	 * 获取邮箱参数
	 * 
	 * @param param
	 * @param arg
	 * @param title
	 * @param content
	 * @return
	 */
	public static String getEmailParams(JSONObject param, JSONObject arg, String title, String content) {
		String temp_ = content;
		try {
			// 默认parms
			JSONObject temparms = null;
			if (arg != null) {
				temparms = arg.getJSONObject("parms");
			}

			// 重新参数值判断
			if (StringUtil.isEmpty(param.optString("temparms")) || param.optString("temparms").equals("{}")) {
				LogUtil.warn(StringUtil.format("消息[{0}]没有参数[temparms]，无法发送邮箱消息", title));
				return null;
			}
			// 固定参数值判断
			if (StringUtil.isEmpty(temparms.optString("editKey")) || temparms.optString("editKey").equals("[]")) {
				LogUtil.warn(StringUtil.format("消息[{0}]没有默认参数[editKey]，无法发送邮箱消息", title));
				return null;
			}
			if (StringUtil.isEmpty(temparms.optString("editValue")) || temparms.optString("editValue").equals("")) {
				LogUtil.warn(StringUtil.format("消息[{0}]没有默认参数[editValue]，无法发送邮箱消息", title));
				return null;
			}

			// UTF-8 转码 重新传入参数
			String keyStr = new String(param.getJSONObject("temparms").toString().getBytes("UTF-8"), "UTF-8");
			JSONObject keys = new JSONObject(keyStr);
			// 自定义模板键
			JSONArray editKey = temparms.getJSONArray("editKey");
			for (Object obj : editKey) {
				JSONObject prm = (JSONObject) obj;
				String key = prm.getString("key");
				String val = keys.get(prm.getString("key")).toString();
				if (key.equals("")) {
					continue;
				}
				if(val.indexOf("Var") != -1) {
					val = Var.getString(key);
				}
				String key_ = StringUtil.format("${0}", key);
				temp_ = temp_.replace(key_, val);
			}
		} catch (Exception e) {
			System.err.println("构建内容失败" + e);
			LogUtil.warn(StringUtil.format("邮箱[{0}]构建内容失败，无法发送邮箱消息", title));
			return "";
		}
		return temp_;
	}
}
