package com.wb.util;

import com.aliyun.sdk.service.dms_enterprise20181101.AsyncClient;
import com.aliyun.sdk.service.dms_enterprise20181101.models.*;
import com.google.gson.Gson;
import com.wb.aliyun.dms.DmsApiClient;
import com.wb.common.Var;
import darabonba.core.TeaPair;
import darabonba.core.utils.CommonUtil;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: qny
 * @Date: 2024-03-28-15:08
 * @Description:
 */
public class SqlExportUtil {

    /**
     * 实例ID 2477954
     */
    static String DMS_INSTANCE_ID = "sys.config.dms.instanceId";

    /**
     * 数据库dbID 54422048
     */
    static String DMS_DB_ID = "sys.config.dms.dbId";

    /**
     * 业务表
     */
    public static String TABLE_NAME = "log_export_order";

    /**
     * 默认导出类型
     */
    public static String EXPORT_CLASSFIY = "export";

    /**
     *
     */
    public static String TO_AUDIT = "toaudit";

    /**
     *
     */
    public static String APPROVE_AGREE = "AGREE";

    private static final Logger LOGGER = LoggerFactory.getLogger(SqlExportUtil.class);


    static CreateDataExportOrderRequest.PluginParam.Builder generatePluginBuilder(){

        return CreateDataExportOrderRequest.PluginParam.builder()
                .instanceId((long) Var.getInt(DMS_INSTANCE_ID))
                // qny-mysql 54422048
                .dbId((long) Var.getInt(DMS_DB_ID));

    }

    /**
     *
     *  classify 导出工单的原因类别
     *  affectRows 预估影响行数
     *  ignoreAffectRows 是否跳过 SQL 行数校验
     *  ignoreReason 如跳过，跳过原因必填
     * @param exportSql 导出sql语句
     * @param comment 导出模块
     * @param exportType 导出类型 XLSX、CSV、JSON、TXT
     * @return
     * @throws Exception
     */
    public static long createOrder(String exportSql,String comment, String exportType,String userId) throws Exception {

        long order = 0L;
        try{
            /*if(ignoreAffectRows && StringUtil.isEmpty(ignoreReason)){
                // 校验ignoreReason是否为空
                throw new Exception("参数错误");
            }*/
            // 获取客户端
            AsyncClient client = DmsApiClient.getInstance();
            // 工单参数填写
            CreateDataExportOrderRequest.PluginParam pluginParam = generatePluginBuilder()
                    .classify(EXPORT_CLASSFIY)
                    // 1L
                    .affectRows(1L)
                    // 逻辑库还是物理库 false-物理库
                    .logic(false)
                    // 不跳过校验
                    .ignoreAffectRows(false)
                    // .ignoreAffectRowsReason(ignoreReason)
                    // select * from test_table; 需带;
                    .exeSQL(exportSql)
                    .build();
            // 请求参数构建
            CreateDataExportOrderRequest request = CreateDataExportOrderRequest.builder()
                    .pluginParam(pluginParam)
                    .comment(comment)
                    .build();

            // Asynchronously get the return value of the API request
            CompletableFuture<CreateDataExportOrderResponse> response = client.createDataExportOrder(request);
            // 等待结果执行完毕
            CreateDataExportOrderResponse resp = response.get();
            // 解析返回参数
            String result = new Gson().toJson(resp);
            JSONObject jsonObject = new JSONObject(result);
            JSONObject body = jsonObject.getJSONObject("body");
            boolean success = body.getBoolean("success");
            if(success){
                order = body.getJSONObject("createOrderResult").getJSONArray("createOrderResult").getLong(0);
                // insert一条数据到营运系统
                doOrderTableInsert(order,exportType,exportSql,comment,userId);
            }else{
                throw new Exception("创建工单失败,result:["+result+"]");
            }
        }catch (Exception e){
            LogUtil.error("创建工单:["+order+"]失败,异常原因:"+e.toString());
            throw new Exception(e.toString());
        }
        return order;

    }


    /**
     * 查询提交的工单当前状态 每20s执行一次，当状态为ENABLE_EXPORT时跳出循环
     * @param order
     * @throws Exception
     *
     * 导出工单状态，取值如下：
     * PRE_CHECKING：执行预校验。
     * PRE_CHECK_SUCCESS：预校验成功。
     * PRE_CHECK_FAIL：预校验失败。
     * WAITING_APPLY_AUDIT：等待提交审批。
     * APPLY_AUDIT_SUCCESS：提交审批成功。
     * ENABLE_EXPORT：审批通过，允许导出。
     * WAITING_EXPORT：等待调度进行导出。
     * DOING_EXPORT：执行导出。
     * EXPORT_FAIL：导出失败。
     * EXPORT_SUCCESS：导出成功
     *
     */
    public static String getOrderStatus(long order) throws Exception{

        String jobStatus = null;
        AsyncClient client = DmsApiClient.getInstance();

        // 调用查看工单详情状态的接口，这里假设是 getOrderStatus
        GetDataExportOrderDetailRequest request = GetDataExportOrderDetailRequest.builder()
                .orderId(order)
                .build();

        CompletableFuture<GetDataExportOrderDetailResponse> response = client.getDataExportOrderDetail(request);
        GetDataExportOrderDetailResponse resp = response.get();

        String result = new Gson().toJson(resp);
        JSONObject jsonObject = new JSONObject(result);
        JSONObject body = jsonObject.getJSONObject("body");
        // 获取 Success 信息
        boolean success = body.getBoolean("success");
        // 获取 JobStatus 信息
        if(success){
            jobStatus = body.getJSONObject("dataExportOrderDetail").getJSONObject("keyInfo")
                    .getString("jobStatus");
        }
        return jobStatus;

    }

    /**
     * 工单自动审批通过
     * @param order
     * @return
     */
    public static String doOrderApprove(long order) {

        LogUtil.info("提交工单审批:["+order+"]成功");
        String errorMsg = null;
        AsyncClient client = DmsApiClient.getInstance();
        try{
            // 获取流程审批号
            GetOrderBaseInfoRequest request = GetOrderBaseInfoRequest.builder()
                    .orderId(order)
                    .build();

            CompletableFuture<GetOrderBaseInfoResponse> response = client.getOrderBaseInfo(request);
            GetOrderBaseInfoResponse resp = response.get();
            JSONObject jsonObject = new JSONObject(new Gson().toJson(resp));
            JSONObject orderBaseInfo = jsonObject.getJSONObject("body").getJSONObject("orderBaseInfo");

            int worFlowId = orderBaseInfo.getInt("workflowInstanceId");
            String statusCode = orderBaseInfo.getString("statusCode");
            if(TO_AUDIT.equals(statusCode)){
                // 自动通过审批
                ApproveOrderRequest approveOrderRequest = ApproveOrderRequest.builder()
                        .approvalType(APPROVE_AGREE)
                        .workflowInstanceId((long) worFlowId)
                        .build();

                CompletableFuture<ApproveOrderResponse> approveResponse = client.approveOrder(approveOrderRequest);
                ApproveOrderResponse approveResp = approveResponse.get();
                String result = new Gson().toJson(approveResp);
                JSONObject approveJo = new JSONObject(result);
                // 解析返回参数
                JSONObject body = approveJo.getJSONObject("body");
                boolean success = body.getBoolean("success");
                if(! success){
                    errorMsg = "工单:["+order+"],自动审核通过失败,"+",失败原因:"+result;
                }
            }else{
                errorMsg = "工单:["+order+"],流程审批状态为:"+statusCode+",不可自动审批通过";
            }
        }catch (Exception e){
            errorMsg = "工单:["+order+"],自动审核通过失败,异常原因:"+e.toString();
        }
        return errorMsg;

    }

    /**
     * 工单提交审批
     * @param order
     *
     */
    public static String doOrderSubmit(long order) {

        String errorMsg = null;
        AsyncClient client = DmsApiClient.getInstance();
        try{
            SubmitOrderApprovalRequest request = SubmitOrderApprovalRequest.builder()
                    .orderId(order)
                    .build();

            CompletableFuture<SubmitOrderApprovalResponse> response = client.submitOrderApproval(request);
            SubmitOrderApprovalResponse resp = response.get();
            // 解析返回参数
            String result = new Gson().toJson(resp);
            JSONObject jsonObject = new JSONObject(result);
            JSONObject body = jsonObject.getJSONObject("body");
            boolean success = body.getBoolean("success");
            if(! success){
                errorMsg = "提交工单审批失败:["+order+"],失败原因:"+result;
            }
        }catch (Exception e){
            errorMsg = "提交工单审批失败:["+order+"],异常原因:"+e.toString();
        }
        return errorMsg;

    }

    /**
     * 执行工单导出
     * @param order 工单号
     * @param exportType 导出类型 XLSX、CSV、JSON、TXT
     * @throws Exception
     */
    public static void executeOrder(long order,String exportType) throws Exception{

        AsyncClient client = DmsApiClient.getInstance();
        // Parameter settings for API request
        ExecuteDataExportRequest executeDataExportRequest = ExecuteDataExportRequest.builder().orderId(order)
                .actionDetail(CommonUtil.buildMap(
                        // 数据导出模式，默认 FAST，NORMAL 允许进行中断
                        new TeaPair("mode", "FAST"),
                        // UTF8 UTF8MB4、GB2312、ISO_8859_1、GBK、LATAIN1、CP1252
                        new TeaPair("encoding", "UTF8"),
                        new TeaPair("fileType", exportType)
                )).build();

        CompletableFuture<ExecuteDataExportResponse> response = client.executeDataExport(executeDataExportRequest);
        response.thenAccept(resp -> {
            String result = new Gson().toJson(resp);
            JSONObject jsonObject = new JSONObject(result);
            boolean success = jsonObject.getJSONObject("body").getBoolean("success");
            if(success){
                // 刚执行完工单时，可能无法立刻获取到下载链接
                // getOrderExportUrl(order);
                // doUpdate
                DbUtil.execute("update "+TABLE_NAME+" set order_status='EXPORT_SUCCESS' where order_num='"+order+"'");
                LogUtil.info("执行工单:["+order+"]完成");
            }
        }).exceptionally(throwable -> {
            // Handling exceptions
            // 异常原因回写数据表
            DbUtil.execute("update "+TABLE_NAME+" set remark='"+throwable.toString()+"' where order_num='"+order+"'");
            LogUtil.error("执行导出工单:["+order+"]失败,异常原因:"+throwable.toString());
            return null;
        });

    }

    /**
     * 获取数据导出结果附件下载地址
     * @param order 工单号
     * @return
     */
    public static String getOrderExportUrl(long order){

        String url = null;
        String errorMsg = null;
        try{
            AsyncClient client = DmsApiClient.getInstance();
            GetDataExportDownloadURLRequest request = GetDataExportDownloadURLRequest.builder().orderId(order).build();

            CompletableFuture<GetDataExportDownloadURLResponse> response = client.getDataExportDownloadURL(request);
            GetDataExportDownloadURLResponse resp = response.get();

            String result = new Gson().toJson(resp);
            JSONObject jsonObject = new JSONObject(result);
            JSONObject body = jsonObject.getJSONObject("body");
            if(body.getBoolean("success")){
                JSONObject downloadURLResult = body.getJSONObject("downloadURLResult");
                if(downloadURLResult.has("URL")){
                    url = downloadURLResult.getString("URL");
                    DbUtil.execute("update "+TABLE_NAME+" set download_url='"+url+"' where order_num='"+order+"'");
                    LogUtil.info("获取下载链接,工单:["+order+"]完成");
                }else{
                    errorMsg = "downloadURLResult:["+downloadURLResult+"]";
                }
            }else{
                errorMsg = "result:["+result+"]";
            }
        }
        catch (Exception e){
            errorMsg = "获取数据导出结果附件下载地址:["+order+"]失败,异常原因:"+e.toString();
            LogUtil.error("获取数据导出结果附件下载地址:["+order+"]失败,异常原因:"+e.toString());
        }
        if(! StringUtil.isEmpty(errorMsg)){
            DbUtil.execute("update "+TABLE_NAME+" set remark='"+errorMsg+"' where order_num='"+order+"'");
        }
        return url;

    }

    /**
     *
     * @param order
     * @param exportType
     * @param exportSql
     * @param comment
     */
    private static void doOrderTableInsert(long order, String exportType, String exportSql, String comment,String userId) throws Exception {

        try(Connection connection = DbUtil.getConnection();
            PreparedStatement statement = connection.prepareStatement("insert into "+TABLE_NAME +
                    "(id, order_num, export_sql, export_reason, export_type,add_date, add_user, modify_date, modify_user) " +
                    "values (?, ?, ?, ?, ?, ?, ?, ?, ?)")){

            String day = DateUtil.format(new Date());

            statement.setString(1, SysUtil.getId());
            statement.setString(2, String.valueOf(order));
            statement.setString(3, exportSql);
            statement.setString(4, comment);
            statement.setString(5, exportType);
            statement.setString(6, day);
            statement.setString(7, userId);
            statement.setString(8, day);
            statement.setString(9, userId);

            int index = statement.executeUpdate();
            if(index != 1){
                LOGGER.warn("保存导出工单记录:"+order+"失败");
            }
        }catch (Exception e){
            throw new Exception("保存导出工单记录:"+order+"失败,e:"+e.toString());
        }

    }


}
