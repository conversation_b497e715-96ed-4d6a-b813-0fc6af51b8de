package com.wb.message.handler;

import java.security.GeneralSecurityException;
import java.util.Date;
import java.util.Properties;

import javax.mail.Authenticator;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import org.json.JSONArray;
import org.json.JSONObject;

import com.sun.mail.util.MailSSLSocketFactory;
import com.wb.common.Var;
import com.wb.message.Handler;
import com.wb.message.util.ParamUtil;
import com.wb.util.DbUtil;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;

public class HandlerEmail extends Handler {

	public HandlerEmail(String title, String content, String users, String data, JSONObject arg) {
		super(title, content, users, data, arg);
	}

	// 发件人的 邮箱 和 密码（替换为自己的邮箱和密码）
	private final static String myEmailAccount = Var.getString("sys.config.E-mail.SenderEmail");
	private final static String myEmailPassword = Var.getString("sys.config.E-mail.EmailPassWord");

	// 发件人邮箱的 SMTP 服务器地址, 必须准确, 不同邮件服务器地址不同,格式为: smtp.xxx.com
	private final static String myEmailSMTPHost = Var.getString("sys.config.E-mail.EmailSMTPHost");

	// SMTP 服务器的端口
	private final static String smtpPort = Var.getString("sys.config.E-mail.smtpPort");

	// 使用SMTP身份验证
	private final static String smtpAuth = Var.getString("sys.config.E-mail.smtpAuth");

	// 服务协议
	private final static String Protocol = Var.getString("sys.config.E-mail.Protocol");

	@Override
	public void handlerMessage() {
		try {
			Properties prop = new Properties();
			// 协议
			prop.setProperty("mail.transport.protocol", Protocol);
			// 服务器
			prop.setProperty("mail.smtp.host", myEmailSMTPHost);
			// 端口
			prop.setProperty("mail.smtp.port", smtpPort);
			// 使用smtp身份验证
			prop.setProperty("mail.smtp.auth", smtpAuth);
			// 使用SSL，企业邮箱必需！
			// 开启安全协议
			MailSSLSocketFactory sf = null;
			try {
				sf = new MailSSLSocketFactory();
				sf.setTrustAllHosts(true);
			} catch (GeneralSecurityException e1) {
				e1.printStackTrace();
			}
			prop.put("mail.smtp.ssl.enable", "true");
			prop.put("mail.smtp.ssl.socketFactory", sf);
			//
			// 获取Session对象
			Session session = Session.getDefaultInstance(prop, new Authenticator() {
				// 此访求返回用户和密码的对象
				@Override
				protected PasswordAuthentication getPasswordAuthentication() {
					PasswordAuthentication passwordAuthentication = new PasswordAuthentication(myEmailAccount,
							myEmailPassword);
					return passwordAuthentication;
				}
			});
			// 设置为debug模式, 可以查看详细的发送 log
			// session.setDebug(true);

			// 3. 创建一封邮件
			// 默认parms
			JSONObject Arg_ = new JSONObject(this.arg.toString());
			JSONObject temparms = null;
			if (Arg_ != null) {
				temparms = Arg_.getJSONObject("parms");
			}
			String emailContent = "";
			if (temparms.has("email"))
				emailContent = temparms.getString("email");
			if (!"".equals(emailContent)) {
				// 重写参数
				JSONObject param_data = new JSONObject(this.data);
				String Ec = ParamUtil.getEmailParams(param_data, Arg_, title, emailContent);

				MimeMessage message = createMimeMessage(session, this.title, Ec, myEmailAccount, this.users);

				// 4. 根据 Session 获取邮件传输对象
				Transport.send(message);
				System.out.println("邮件消息----发送成功");
			} else {
				LogUtil.warn(StringUtil.format("消息[{0}] 邮箱----邮件内容为空发送失败", title));
			}
		} catch (Exception e) {
			LogUtil.warn(StringUtil.format("消息[{0}] 邮箱----发送失败" + e, title));
			System.err.println("邮件消息----发送失败" + e);
		}
	}

	/**
	 * 创建一封只包含文本的简单邮件
	 *
	 * @param session     和服务器交互的会话
	 * @param sendMail    发件人邮箱
	 * @param receiveMail 收件人邮箱
	 * @return
	 * @throws Exception
	 */
	public static MimeMessage createMimeMessage(Session session, String title, String content, String sendMail,
			String[] receiveMail) throws Exception {
		// 1. 创建一封邮件
		MimeMessage message = new MimeMessage(session);

		// 2. From: 发件人称）
		message.setFrom(new InternetAddress(sendMail, Var.getString("sys.base.school_name"), "UTF-8"));

		// 3. To: 收件人（可以增加多个收件人、抄送、密送）
		// 存放所有用户
		String user = StringUtil.joinQuote(receiveMail);
		JSONArray array = DbUtil.queryAll("select email,user_name from hr_info where email <> '' and id in (" + user
				+ ") union select mail_box as email,user_name from stu_info where mail_box <> '' and  id in (" + user
				+ ")");

		for (int i = 0; i < array.length(); i++) {
			JSONObject obj = array.getJSONObject(i);

			message.setRecipient(MimeMessage.RecipientType.TO,
					new InternetAddress(obj.getString("email"), obj.getString("user_name"), "UTF-8"));
		}
		if (array.length() == 0) {
			System.err.println("邮件消息----没有用户");
		}

		// 4. Subject: 邮件主题
		message.setSubject(title, "UTF-8");

		// 5. Content: 邮件正文（可以使用html标签）
		message.setContent("尊敬的你好,\r\n<br/>" + content, "text/html;charset=UTF-8");

		// 6. 设置发件时间
		message.setSentDate(new Date());

		// 7. 保存设置
		message.saveChanges();

		return message;
	}
}
