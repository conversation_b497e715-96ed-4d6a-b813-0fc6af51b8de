package com.wb.controls;

import java.io.File;

import com.wb.common.Base;
import com.wb.tool.ExcelForm;
import com.wb.util.StringUtil;
import com.wb.util.WebUtil;

public class ExtContainer extends ExtControl {
	protected void extendConfig() throws Exception {
		String excelForm = gs("excelForm");
		String excelFormAlign = gs("excelFormAlign");
		int sheetIndex;

		if (!excelForm.isEmpty()) {
			if (hasItems)
				headerScript.append(',');
			else
				hasItems = true;
			if (gs("autoScroll").isEmpty())
				headerScript.append("autoScroll:true,createObject:true,");
			else
				headerScript.append("createObject:true,");
			headerScript.append("html:");
			if (excelForm.indexOf('|') == -1) {
				sheetIndex = 0;
			} else {
				sheetIndex = Integer.parseInt(StringUtil.getValuePart(excelForm, '|'));
				excelForm = StringUtil.getNamePart(excelForm, '|');
			}
			File file = new File(Base.path, excelForm);
			headerScript.append(
					StringUtil.quote(ExcelForm.getHtml(WebUtil.fetch(request), file, sheetIndex, excelFormAlign)));
		}
	}
}
