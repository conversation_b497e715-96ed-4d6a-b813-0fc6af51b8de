# Redis集合操作使用文档

本文档介绍Redis缓存系统提供的集合数据结构操作功能，包括Set（集合）、ZSet（有序集合）和List（列表）操作。

## 功能概览

Redis集合操作功能提供以下能力：

### Set操作
- 添加元素到集合
- 从集合中删除元素
- 获取集合中所有元素

### ZSet操作
- 添加元素到有序集合
- 从有序集合中删除元素
- 获取有序集合中的元素范围
- 获取有序集合的大小

### List操作
- 从左侧/右侧添加元素到列表
- 从左侧/右侧弹出列表元素
- 获取列表中指定范围的元素
- 获取列表长度
- 获取列表指定位置的元素

## Set操作

Set是无序且唯一的元素集合，适合用于存储不重复的元素集。

### Java 用法

```java
@Autowired
private RedisCache redisCache;

// 添加元素到集合
Base.map.setSet("user:tags:1001", "Java", "Python", "Redis");

// 添加多个元素到集合
Set<String> tags = new HashSet<>();
tags.add("MySQL");
tags.add("MongoDB");
Base.map.setSet("user:tags:1001", tags.toArray());

// 从集合中删除元素
Base.map.setDel("user:tags:1001", "MongoDB");

// 获取集合中的所有元素
Set<Object> allTags = Base.map.getSetAll("user:tags:1001");
```

### XWL 脚本用法

```javascript
// 添加元素到集合
Base.map.setSet("user:tags:1001", "Java", "Python", "Redis");

// 从集合中删除元素
Base.map.setDel("user:tags:1001", "MongoDB");

// 获取集合中的所有元素
var allTags = Base.map.getSetAll("user:tags:1001");
```

## ZSet操作

ZSet是有序集合，每个元素关联一个分数，用来排序，适合用于排行榜等场景。

### Java 用法

```java
@Autowired
private RedisCache redisCache;

// 添加元素到有序集合
Set<ZSetOperations.TypedTuple<Object>> scoreSet = new HashSet<>();
scoreSet.add(new DefaultTypedTuple<>("user1", 95.0));
scoreSet.add(new DefaultTypedTuple<>("user2", 88.5));
scoreSet.add(new DefaultTypedTuple<>("user3", 92.0));
Base.map.zsetSet("exam:scores", scoreSet);

// 从有序集合中删除元素
Base.map.zsetDel("exam:scores", "user2");

// 获取有序集合中的元素范围（按排名从小到大）
Set<Object> topScores = Base.map.zsetRange("exam:scores", 0, 2);

// 获取有序集合的大小
long size = Base.map.zsetSize("exam:scores");
```

### XWL 脚本用法

```javascript
// 获取ZSet相关类
var ZSetOps = Java.type("org.springframework.data.redis.core.ZSetOperations");
var DefaultTypedTuple = Java.type("org.springframework.data.redis.core.DefaultTypedTuple");

// 创建有序集合
var scoreSet = new java.util.HashSet();
scoreSet.add(new DefaultTypedTuple("user1", 95.0));
scoreSet.add(new DefaultTypedTuple("user2", 88.5));
scoreSet.add(new DefaultTypedTuple("user3", 92.0));

// 添加元素到有序集合
Base.map.zsetSet("exam:scores", scoreSet);

// 从有序集合中删除元素
Base.map.zsetDel("exam:scores", "user2");

// 获取有序集合中的元素范围
var topScores = Base.map.zsetRange("exam:scores", 0, 2);

// 获取有序集合的大小
var size = Base.map.zsetSize("exam:scores");
```

## List操作

List是一个双向链表结构，可以从两端操作，适合用于消息队列、最新动态等。

### Java 用法

```java
@Autowired
private RedisCache redisCache;

// 从右侧添加元素到列表
Base.map.listRightPush("notifications:1001", "新消息1");
Base.map.listRightPush("notifications:1001", "新消息2");

// 批量添加元素到列表
List<String> messages = Arrays.asList("新消息3", "新消息4", "新消息5");
Base.map.listPushAll("notifications:1001", messages);

// 从左侧添加元素到列表（优先级高）
Base.map.listLeftPush("notifications:1001", "紧急消息");

// 从左侧弹出列表元素（先进先出）
Object message = Base.map.listLeftPop("notifications:1001");

// 从右侧弹出列表元素（后进先出）
Object lastMessage = Base.map.listRightPop("notifications:1001");

// 获取列表中指定范围的元素（从索引0到4）
List<Object> recentMessages = Base.map.listGet("notifications:1001", 0, 4);

// 获取列表指定位置的元素
Object secondMessage = Base.map.getListKeyIndex("notifications:1001", 1);

// 获取列表长度
Long length = Base.map.getListLength("notifications:1001");
```

### XWL 脚本用法

```javascript
// 从右侧添加元素到列表
Base.map.listRightPush("notifications:1001", "新消息1");
Base.map.listRightPush("notifications:1001", "新消息2");

// 批量添加元素到列表
var messages = ["新消息3", "新消息4", "新消息5"];
Base.map.listPushAll("notifications:1001", messages);

// 从左侧添加元素到列表（优先级高）
Base.map.listLeftPush("notifications:1001", "紧急消息");

// 从左侧弹出列表元素（先进先出）
var message = Base.map.listLeftPop("notifications:1001");

// 从右侧弹出列表元素（后进先出）
var lastMessage = Base.map.listRightPop("notifications:1001");

// 获取列表中指定范围的元素（从索引0到4）
var recentMessages = Base.map.listGet("notifications:1001", 0, 4);

// 获取列表指定位置的元素
var secondMessage = Base.map.getListKeyIndex("notifications:1001", 1);

// 获取列表长度
var length = Base.map.getListLength("notifications:1001");
```

## 国际化处理

按照系统国际化规则，错误信息和提示可以使用：

```javascript
// 在XWL脚本中
var errorMsg = Str.format(request, "cache_listEmpty");
Wb.warn(Wb.format("Str.cache_invalidIndex"));

// 带参数的消息
var msg = Str.format(request, "cache_elementAdded", key, value);
```

## 示例：实时消息通知系统

### Java 示例

```java
/**
 * 消息通知服务，使用Redis List存储用户的通知消息
 */
@Service
public class NotificationService {
    @Autowired
    private RedisCache redisCache;
    
    private static final String NOTIFICATION_PREFIX = "user:notifications:";
    private static final int MAX_NOTIFICATIONS = 100;
    
    /**
     * 给用户发送一条通知
     */
    public void sendNotification(String userId, String message) {
        String key = NOTIFICATION_PREFIX + userId;
        
        // 添加消息到列表右侧
        Base.map.listRightPush(key, message);
        
        // 如果列表长度超过最大限制，从左侧删除旧消息
        Long size = Base.map.getListLength(key);
        if (size != null && size > MAX_NOTIFICATIONS) {
            Base.map.listLeftPop(key);
        }
    }
    
    /**
     * 获取用户最近的通知
     */
    public List<String> getRecentNotifications(String userId, int count) {
        String key = NOTIFICATION_PREFIX + userId;
        List<Object> notifications = Base.map.listGet(key, 0, count - 1);
        
        // 转换成字符串列表
        List<String> result = new ArrayList<>();
        for (Object notification : notifications) {
            result.add(notification.toString());
        }
        
        return result;
    }
    
    /**
     * 标记一条通知为已读（从列表中删除）
     */
    public void markAsRead(String userId, String message) {
        String key = NOTIFICATION_PREFIX + userId;
        
        // 从列表中删除该消息
        Base.map.listPush(key, message);  // 使用负数表示删除
    }
    
    /**
     * 清空用户的所有通知
     */
    public void clearAllNotifications(String userId) {
        String key = NOTIFICATION_PREFIX + userId;
        Base.map.delKey(key);
    }
    
    /**
     * 获取用户未读通知数量
     */
    public long getUnreadCount(String userId) {
        String key = NOTIFICATION_PREFIX + userId;
        Long size = Base.map.getListLength(key);
        return size != null ? size : 0;
    }
}
```

### XWL 脚本示例

```javascript
/**
 * 消息通知服务，使用Redis List存储用户的通知消息
 */

var NOTIFICATION_PREFIX = "user:notifications:";
var MAX_NOTIFICATIONS = 100;

/**
 * 给用户发送一条通知
 */
function sendNotification(userId, message) {
    var key = NOTIFICATION_PREFIX + userId;
    
    // 添加消息到列表右侧
    Base.map.listRightPush(key, message);
    
    // 如果列表长度超过最大限制，从左侧删除旧消息
    var size = Base.map.getListLength(key);
    if (size != null && size > MAX_NOTIFICATIONS) {
        Base.map.listLeftPop(key);
    }
}

/**
 * 获取用户最近的通知
 */
function getRecentNotifications(userId, count) {
    var key = NOTIFICATION_PREFIX + userId;
    var notifications = Base.map.listGet(key, 0, count - 1);
    
    // 已经是字符串列表，直接返回
    return notifications;
}

/**
 * 标记所有通知为已读（清空通知列表）
 */
function markAllAsRead(userId) {
    var key = NOTIFICATION_PREFIX + userId;
    Base.map.delKey(key);
    return true;
}
```

## 应用场景

### Set适用场景
- 标签系统
- 用户关注/粉丝
- 多用户共同好友
- 社交网络中的用户兴趣交集

### ZSet适用场景
- 排行榜系统
- 优先级队列
- 延迟队列
- 带权重的数据集

### List适用场景
- 消息队列
- 最近浏览记录
- 系统日志
- 社交媒体动态流

## 注意事项

1. Set集合中的元素是唯一的，如果添加重复元素，只会保留一个
2. ZSet在有大量数据的情况下，Range操作可能会比较耗时，建议限制返回数量
3. List如果达到最大长度，应考虑从另一端删除元素，避免无限增长
4. 对于大型集合，尽量使用批量操作，提高性能
5. 集合操作在大多数情况下是原子性的，不需要额外加锁
6. 不建议在单个集合中存储过多数据，可能影响Redis性能 