package com.wb.controls;

import com.wb.common.KVBuffer;

public class ExtRadioGroup extends ExtControl {
	protected void extendConfig() throws Exception {
		String keyName = gs("keyName");
		boolean keyStatusFilter = gb("keyStatusFilter");
		if (!keyName.isEmpty()) {
			if (this.hasItems)
				this.headerScript.append(',');
			else
				this.hasItems = true;
			this.headerScript.append("items:");
			
			// 根据是否有状态过滤参数调用不同的方法
			if (!keyStatusFilter) {
				this.headerScript.append(KVBuffer.getList(keyName, "indexValue", "boxLabel"));
			} else {
				// 有状态过滤时，默认筛选启用状态(1)
				this.headerScript.append(KVBuffer.getList(keyName, "indexValue", "boxLabel", 1));
			}
		}
	}
}