# Redis布隆过滤器使用文档

本文档介绍Redis缓存系统提供的布隆过滤器功能，包括标准布隆过滤器和计数布隆过滤器，用于高效判断元素是否存在。

## 功能概览

系统提供两种布隆过滤器实现：

1. **标准布隆过滤器 (RedisBloomFilter)**
   - 判断元素是否可能存在
   - 添加元素到过滤器
   - 无法删除元素
   - 占用空间小

2. **计数布隆过滤器 (CountingBloomFilter)**
   - 支持所有标准布隆过滤器功能
   - 支持删除元素
   - 可以获取计数器信息
   - 占用空间相对更大

## 标准布隆过滤器

标准布隆过滤器适用于判断大量数据中某个元素是否可能存在，具有较小的空间占用和极高的判断效率。

### Java 用法

```java
@Autowired
private RedisCache redisCache;

// 创建布隆过滤器
RedisBloomFilter filter = Base.map.createBloomFilter(
    "userIdFilter",    // 过滤器名称
    1000000,           // 预期元素数量
    0.01               // 误判率(1%)
);

// 添加元素到布隆过滤器
filter.add("user:1001");

// 批量添加元素
List<String> userIds = Arrays.asList("user:1002", "user:1003", "user:1004");
filter.addAll(userIds);

// 判断元素是否存在
boolean exists = filter.mightContain("user:1001");  // 返回true
boolean notExists = filter.mightContain("user:9999");  // 可能返回false

// 批量判断元素是否存在
Map<String, Boolean> results = filter.multiMightContain(
    Arrays.asList("user:1001", "user:9999")
);

// 获取过滤器信息
Map<String, Object> filterInfo = filter.getInfo();
```

### XWL 脚本用法

```javascript
// 创建布隆过滤器
var filter = Base.map.createBloomFilter(
    "userIdFilter",    // 过滤器名称
    1000000,           // 预期元素数量
    0.01               // 误判率(1%)
);

// 如果过滤器已存在，直接获取
var existingFilter = Base.map.getBloomFilter("userIdFilter");

// 添加元素到布隆过滤器
filter.add("user:1001");

// 批量添加元素
var userIds = ["user:1002", "user:1003", "user:1004"];
filter.addAll(userIds);

// 判断元素是否存在
var exists = filter.mightContain("user:1001");  // 返回true
var notExists = filter.mightContain("user:9999");  // 可能返回false

// 批量判断元素是否存在
var results = filter.multiMightContain(["user:1001", "user:9999"]);

// 获取过滤器信息
var filterInfo = filter.getInfo();
```

## 标准布隆过滤器与缓存结合使用

系统提供了将布隆过滤器与缓存获取结合的便捷方法。

### Java 用法

```java
// 使用布隆过滤器获取缓存，如果布隆过滤器显示元素不存在，则直接返回null
Object cachedValue = Base.map.getWithBloomFilter(
    "user:1001",        // 缓存键
    "userIdFilter"      // 布隆过滤器名称
);

// 使用布隆过滤器和加载器获取缓存
User user = Base.map.getWithBloomFilter(
    "user:1001",                    // 缓存键
    "userIdFilter",                 // 布隆过滤器名称
    key -> loadUserFromDb(key),     // 加载函数
    30,                             // 过期时间
    TimeUnit.MINUTES                // 时间单位
);
```

### XWL 脚本用法

```javascript
// 使用布隆过滤器获取缓存
var cachedValue = Base.map.getWithBloomFilter(
    "user:1001",        // 缓存键
    "userIdFilter"      // 布隆过滤器名称
);

// 使用布隆过滤器和加载器获取缓存
var user = Base.map.getWithBloomFilter(
    "user:1001",                     // 缓存键
    "userIdFilter",                  // 布隆过滤器名称
    function(key) {                  // 加载函数
        var userId = key.substring(5);
        return userService.findById(userId);
    },
    30,                              // 过期时间
    java.util.concurrent.TimeUnit.MINUTES // 时间单位
);
```

## 计数布隆过滤器

计数布隆过滤器扩展了标准布隆过滤器，支持元素删除操作，适用于需要动态添加和删除元素的场景。

### Java 用法

```java
@Autowired
private RedisCache redisCache;

// 创建计数布隆过滤器
CountingBloomFilter filter = Base.map.createCountingBloomFilter(
    "userIdCountingFilter",  // 过滤器名称
    1000000,                 // 预期元素数量
    0.01,                    // 误判率(1%)
    15                       // 最大计数值(可选，默认15)
);

// 添加元素到计数布隆过滤器
filter.add("user:1001");

// 批量添加元素
List<String> userIds = Arrays.asList("user:1002", "user:1003", "user:1004");
filter.addAll(userIds);

// 判断元素是否存在
boolean exists = filter.mightContain("user:1001");  // 返回true

// 批量判断元素是否存在
Map<String, Boolean> results = filter.multiMightContain(
    Arrays.asList("user:1001", "user:9999")
);

// 删除元素（标准布隆过滤器不支持此操作）
filter.remove("user:1001");

// 批量删除元素
filter.removeAll(Arrays.asList("user:1002", "user:1003"));

// 获取过滤器信息
Map<String, Object> filterInfo = filter.getInfo();

// 获取所有计数器值
Map<String, Integer> counters = filter.getAllCounters();

// 获取过滤器中的元素数量（估计值）
long count = filter.approximateElementCount();

// 清空过滤器
filter.clear();
```

### XWL 脚本用法

```javascript
// 创建计数布隆过滤器
var filter = Base.map.createCountingBloomFilter(
    "userIdCountingFilter",  // 过滤器名称
    1000000,                 // 预期元素数量
    0.01,                    // 误判率(1%)
    15                       // 最大计数值(可选，默认15)
);

// 如果过滤器已存在，直接获取
var existingFilter = Base.map.getCountingBloomFilter("userIdCountingFilter");

// 添加元素到计数布隆过滤器
filter.add("user:1001");

// 批量添加元素
var userIds = ["user:1002", "user:1003", "user:1004"];
filter.addAll(userIds);

// 判断元素是否存在
var exists = filter.mightContain("user:1001");  // 返回true

// 批量判断元素是否存在
var results = filter.multiMightContain(["user:1001", "user:9999"]);

// 删除元素（标准布隆过滤器不支持此操作）
filter.remove("user:1001");

// 批量删除元素
filter.removeAll(["user:1002", "user:1003"]);

// 获取过滤器信息
var filterInfo = filter.getInfo();

// 获取所有计数器值
var counters = filter.getAllCounters();

// 获取过滤器中的元素数量（估计值）
var count = filter.approximateElementCount();

// 清空过滤器
filter.clear();
```

## 计数布隆过滤器与缓存结合使用

系统提供了将计数布隆过滤器与缓存操作结合的便捷方法。

### Java 用法

```java
// 使用计数布隆过滤器获取缓存
Object cachedValue = Base.map.getWithCountingBloomFilter(
    "user:1001",                // 缓存键
    "userIdCountingFilter"      // 计数布隆过滤器名称
);

// 使用计数布隆过滤器和加载器获取缓存
User user = Base.map.getWithCountingBloomFilter(
    "user:1001",                    // 缓存键
    "userIdCountingFilter",         // 计数布隆过滤器名称
    key -> loadUserFromDb(key),     // 加载函数
    30,                             // 过期时间
    TimeUnit.MINUTES                // 时间单位
);

// 删除缓存并从计数布隆过滤器中移除
Base.map.deleteWithCountingBloomFilter(
    "user:1001",                // 缓存键
    "userIdCountingFilter"      // 计数布隆过滤器名称
);

// 批量删除缓存并从计数布隆过滤器中移除
Base.map.multiDeleteWithCountingBloomFilter(
    Arrays.asList("user:1001", "user:1002"),
    "userIdCountingFilter"
);
```

### XWL 脚本用法

```javascript
// 使用计数布隆过滤器获取缓存
var cachedValue = Base.map.getWithCountingBloomFilter(
    "user:1001",                // 缓存键
    "userIdCountingFilter"      // 计数布隆过滤器名称
);

// 使用计数布隆过滤器和加载器获取缓存
var user = Base.map.getWithCountingBloomFilter(
    "user:1001",                     // 缓存键
    "userIdCountingFilter",          // 计数布隆过滤器名称
    function(key) {                  // 加载函数
        var userId = key.substring(5);
        return userService.findById(userId);
    },
    30,                              // 过期时间
    java.util.concurrent.TimeUnit.MINUTES // 时间单位
);
```

## 国际化处理

按照系统国际化规则，错误信息和提示可以使用：

```javascript
// 在XWL脚本中
var errorMsg = Str.format(request, "cache_filterCreateFailed");
Wb.warn(Wb.format("Str.cache_elementNotFound"));

// 带参数的消息
var msg = Str.format(request, "cache_elementAdded", key);
```

## 示例：使用布隆过滤器防止缓存穿透

### Java 示例

```java
/**
 * 用户服务，使用布隆过滤器防止缓存穿透
 */
@Service
public class UserService {
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private UserRepository userRepository;
    
    private static final String USER_PREFIX = "user:";
    private static final String BLOOM_FILTER_NAME = "userIdFilter";
    private static final long CACHE_EXPIRE_TIME = 30;
    
    /**
     * 初始化用户ID布隆过滤器
     */
    @PostConstruct
    public void initBloomFilter() {
        // 创建布隆过滤器
        RedisBloomFilter filter = Base.map.createBloomFilter(
            BLOOM_FILTER_NAME,    // 过滤器名称
            1000000,              // 预期用户数量
            0.01                  // 误判率1%
        );
        
        // 预热布隆过滤器，加载所有用户ID
        List<String> allUserIds = userRepository.findAllUserIds();
        for (String userId : allUserIds) {
            filter.add(USER_PREFIX + userId);
        }
    }
    
    /**
     * 根据ID获取用户信息（使用布隆过滤器防止缓存穿透）
     */
    public User getUserById(String userId) {
        String cacheKey = USER_PREFIX + userId;
        
        // 使用布隆过滤器和加载器获取缓存
        return Base.map.getWithBloomFilter(
            cacheKey,
            BLOOM_FILTER_NAME,
            key -> {
                // 从数据库加载用户
                User user = userRepository.findById(userId);
                if (user != null) {
                    // 确保添加到布隆过滤器
                    RedisBloomFilter filter = Base.map.getBloomFilter(BLOOM_FILTER_NAME);
                    filter.add(key);
                }
                return user;
            },
            CACHE_EXPIRE_TIME,
            TimeUnit.MINUTES
        );
    }
    
    /**
     * 创建新用户
     */
    public void createUser(User user) {
        // 保存到数据库
        userRepository.save(user);
        
        // 更新缓存
        String cacheKey = USER_PREFIX + user.getId();
        Base.map.setValue(cacheKey, user, CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
        
        // 添加到布隆过滤器
        RedisBloomFilter filter = Base.map.getBloomFilter(BLOOM_FILTER_NAME);
        filter.add(cacheKey);
    }
}
```

### XWL 脚本示例

```javascript
/**
 * 用户管理功能，使用计数布隆过滤器管理用户ID缓存
 */

// 配置常量
var USER_PREFIX = "user:";
var BLOOM_FILTER_NAME = "userIdCountingFilter";
var CACHE_EXPIRE_TIME = 30; // 分钟

/**
 * 初始化计数布隆过滤器
 */
function initCountingBloomFilter() {
    var redisCache = Base.map("redisCache");
    
    // 创建计数布隆过滤器
    var filter = redisCache.createCountingBloomFilter(
        BLOOM_FILTER_NAME,   // 过滤器名称
        1000000,             // 预期用户数量
        0.01                 // 误判率1%
    );
    
    // 预热布隆过滤器
    var allUserIds = userService.findAllUserIds();
    for (var i = 0; i < allUserIds.size(); i++) {
        var userId = allUserIds.get(i);
        filter.add(USER_PREFIX + userId);
    }
    
    return filter;
}

/**
 * 根据ID获取用户信息
 */
function getUserById(userId) {
    var redisCache = Base.map("redisCache");
    var cacheKey = USER_PREFIX + userId;
    
    // 使用计数布隆过滤器和加载器获取缓存
    return redisCache.getWithCountingBloomFilter(
        cacheKey,
        BLOOM_FILTER_NAME,
        function(key) {
            // 从数据库加载用户
            var userIdParam = key.substring(USER_PREFIX.length);
            return userService.findById(userIdParam);
        },
        CACHE_EXPIRE_TIME,
        java.util.concurrent.TimeUnit.MINUTES
    );
}

/**
 * 删除用户
 */
function deleteUser(userId) {
    var redisCache = Base.map("redisCache");
    var cacheKey = USER_PREFIX + userId;
    
    // 从数据库删除
    userService.deleteById(userId);
    
    // 删除缓存并从计数布隆过滤器中移除
    redisCache.deleteWithCountingBloomFilter(cacheKey, BLOOM_FILTER_NAME);
}
```

## 布隆过滤器与计数布隆过滤器比较

| 特性 | 标准布隆过滤器 | 计数布隆过滤器 |
|------|--------------|--------------|
| 支持添加元素 | ✓ | ✓ |
| 支持删除元素 | ✗ | ✓ |
| 空间占用 | 较小 | 较大 |
| 查询性能 | 极快 | 快 |
| 误判率 | 可配置 | 可配置 |
| 计数功能 | ✗ | ✓ |
| 适用场景 | 静态数据集 | 动态数据集 |

## 注意事项

1. 布隆过滤器可能出现误判（false positive），即可能会误报元素存在，但不会漏报元素不存在
2. 布隆过滤器的误判率与预期元素数量和位数组大小有关，要合理设置这些参数
3. 计数布隆过滤器支持删除操作，但会占用更多内存
4. 布隆过滤器创建后，预期元素数量和误判率无法更改，需要提前做好容量规划
5. 布隆过滤器适合判断"一定不存在"的场景，用来防止缓存穿透效果很好
6. 对于频繁变化的数据集，推荐使用计数布隆过滤器
7. 被删除又重新添加的元素在标准布隆过滤器中始终存在，不会被"删除" 