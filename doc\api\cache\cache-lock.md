# 分布式锁使用文档

本文档介绍Redis缓存系统提供的分布式锁功能，用于在分布式环境中实现资源互斥访问控制。

## 功能概览

分布式锁功能提供以下能力：

- 获取分布式锁（支持等待时间和锁超时）
- 释放分布式锁
- 基于Redisson实现的可重入锁

## Java 用法

在Java代码中，可以直接使用`Base.map`访问分布式锁功能：

```java
// 尝试获取分布式锁，指定等待时间和锁持有时间
boolean locked = Base.map.tryLock(
    "order:lock:10001",  // 锁的键名
    3000,               // 最长等待时间(毫秒)
    5000                // 锁持有时间(毫秒)
);

if (locked) {
    try {
        // 获取锁成功，执行业务逻辑
        processOrder();
    } finally {
        // 操作完成后释放锁
        Base.map.unLock("order:lock:10001");
    }
} else {
    // 获取锁失败处理
    throw new RuntimeException("操作正在处理中，请稍后再试");
}
```

## XWL 脚本用法

在XWL脚本中，直接使用`Base.map`访问分布式锁功能：

```javascript
// 尝试获取分布式锁
var locked = Base.map.tryLock(
    "order:lock:10001",  // 锁的键名
    3000,               // 最长等待时间(毫秒)
    5000                // 锁持有时间(毫秒)
);

if (locked) {
    try {
        // 获取锁成功，执行业务逻辑
        processOrder();
    } finally {
        // 操作完成后释放锁
        Base.map.unLock("order:lock:10001");
    }
} else {
    // 获取锁失败处理
    Wb.warn(Wb.format("Str.lock_failedToAcquire"));
}
```

## 国际化处理

按照系统国际化规则，错误信息和提示可以使用：

```javascript
// 在XWL脚本中
var lockingMsg = Str.format(request, "lock_trying");
Wb.info(Wb.format("Str.lock_acquired"));
Wb.warn(Wb.format("Str.lock_failedToAcquire"));

// 带参数的消息
var msg = Str.format(request, "lock_released", lockKey);
```

## 示例：并发处理控制

### Java 示例

```java
/**
 * 处理订单支付（使用分布式锁防止并发问题）
 */
public boolean processOrderPayment(String orderId, BigDecimal amount) {
    // 构建锁键名
    String lockKey = "payment:lock:" + orderId;
    
    // 尝试获取锁，最多等待3秒，锁持有时间10秒
    boolean locked = Base.map.tryLock(lockKey, 3000, 10000);
    
    if (!locked) {
        // 获取锁失败，可能是支付正在处理中
        throw new ServiceException("订单支付处理中，请勿重复提交");
    }
    
    try {
        // 查询订单状态
        Order order = orderRepository.findById(orderId);
        
        // 检查订单状态是否可支付
        if (order.getStatus() != OrderStatus.WAITING_PAYMENT) {
            throw new ServiceException("订单状态不允许支付");
        }
        
        // 执行支付处理
        boolean paymentSuccess = paymentService.processPayment(order, amount);
        
        if (paymentSuccess) {
            // 更新订单状态
            order.setStatus(OrderStatus.PAID);
            orderRepository.save(order);
            
            // 可能的其他操作
            inventoryService.reduceStock(order.getItems());
            
            return true;
        } else {
            return false;
        }
    } finally {
        // 释放锁
        Base.map.unLock(lockKey);
    }
}
```

### XWL 脚本示例

```javascript
/**
 * 处理订单支付（使用分布式锁防止并发问题）
 */
function processOrderPayment(orderId, amount) {
    // 构建锁键名
    var lockKey = "payment:lock:" + orderId;
    
    // 尝试获取锁，最多等待3秒，锁持有时间10秒
    var locked = Base.map.tryLock(lockKey, 3000, 10000);
    
    if (!locked) {
        // 获取锁失败，可能是支付正在处理中
        Wb.warn(Wb.format("Str.payment_processing"));
        return false;
    }
    
    try {
        // 查询订单状态
        var order = orderService.findById(orderId);
        
        // 检查订单状态是否可支付
        if (order.getStatus() != "WAITING_PAYMENT") {
            Wb.warn(Wb.format("Str.order_statusNotAllowPayment"));
            return false;
        }
        
        // 执行支付处理
        var paymentSuccess = paymentService.processPayment(order, amount);
        
        if (paymentSuccess) {
            // 更新订单状态
            order.setStatus("PAID");
            orderService.save(order);
            
            // 可能的其他操作
            inventoryService.reduceStock(order.getItems());
            
            return true;
        } else {
            return false;
        }
    } finally {
        // 释放锁
        Base.map.unLock(lockKey);
    }
}
```

## 获取Redisson客户端

系统还提供了直接获取Redisson客户端的方法，对于需要更高级分布式功能的场景（如读写锁、信号量等）可以使用：

### Java 用法

```java
// 获取Redisson客户端
RedissonClient redisson = Base.map.getRedissonClient();

// 使用Redisson的读写锁
RReadWriteLock rwLock = redisson.getReadWriteLock("rwlock:document:10001");

// 获取读锁
RLock readLock = rwLock.readLock();
readLock.lock(30, TimeUnit.SECONDS);  // 锁30秒自动释放
try {
    // 读取文档
    Document doc = documentService.getDocument("10001");
} finally {
    readLock.unlock();
}

// 获取写锁
RLock writeLock = rwLock.writeLock();
writeLock.lock(30, TimeUnit.SECONDS);
try {
    // 修改文档
    documentService.updateDocument("10001", newContent);
} finally {
    writeLock.unlock();
}
```

### XWL 脚本用法

```javascript
// 获取Redisson客户端
var redisson = Base.map.getRedissonClient();

// 使用Redisson的读写锁
var rwLock = redisson.getReadWriteLock("rwlock:document:10001");

// 获取读锁
var readLock = rwLock.readLock();
readLock.lock(30, java.util.concurrent.TimeUnit.SECONDS);
try {
    // 读取文档
    var doc = documentService.getDocument("10001");
} finally {
    readLock.unlock();
}
```

## 注意事项

1. 锁定时必须指定合理的等待时间和锁持有时间，避免死锁
2. 锁释放要放在finally中，确保即使发生异常也能释放锁
3. 锁名称遵循全局统一命名规范，建议使用`业务名:lock:资源ID`格式
4. 默认锁过期时间为1000毫秒，如需长时间锁定请明确指定超时时间
5. 尽量减少锁的持有时间，仅在必要的操作过程中持有锁
6. 系统使用的是Redisson实现的分布式锁，支持可重入特性
7. 对于高并发场景，考虑使用粒度更细的锁策略，避免锁争用 