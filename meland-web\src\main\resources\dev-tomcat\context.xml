<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE Context>
<Context reloadable="true" crossContext="true">
    <WatchedResource>WEB-INF/web.xml</WatchedResource>
    <Resource name="jdbc/meland" auth="Container"
              type="javax.sql.DataSource" driverClassName="com.mysql.jdbc.Driver"
              url="********************************************************************************************************************************************************************************************************"
              validationQuery="select 1" testWhileIdle="true" testOnBorrow="false"
              username="dev" password="7RaP*vT5!Jxdzv" maxTotal="1000" maxIdle="30"
              removeAbandonedOnBorrow="true" removeAbandonedTimeout="10000"/>
    <Resource name="jdbc/meland_r" auth="Container"
              type="javax.sql.DataSource" driverClassName="com.mysql.jdbc.Driver"
              url="********************************************************************************************************************************************************************************************************"
              validationQuery="select 1" testWhileIdle="true" testOnBorrow="false"
              username="dev" password="7RaP*vT5!Jxdzv" maxTotal="1000" maxIdle="30"
              removeAbandonedOnBorrow="true" removeAbandonedTimeout="10000"/>
    <Resource name="jdbc/adb" auth="Container"
              type="javax.sql.DataSource" driverClassName="com.mysql.jdbc.Driver"
              url="********************************************************************************************************************************************************************************************************"
              validationQuery="select 1" testWhileIdle="true" testOnBorrow="false"
              username="dev" password="7RaP*vT5!Jxdzv" maxTotal="1000" maxIdle="30"
              removeAbandonedOnBorrow="true" removeAbandonedTimeout="10000"/>
</Context>