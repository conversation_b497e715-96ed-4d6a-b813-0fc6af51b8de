(function(a){a.fn.barrager=function(c){c=a.extend({close:true,bottom:0,max:10,speed:8,color:"#fff",old_ie_color:"#000000"},c||{});var f=new Date().getTime();var l="barrage_"+f;var d="#"+l;var n=a("<div class='barrage' id='"+l+"'></div>").appendTo(a(this));var o=a(window).height()-100;var m=(o>this.height())?this.height():o;var e=a(window).width()+500;var g=(e>this.width())?this.width():e;var b=(c.bottom==0)?Math.floor(Math.random()*m+40):c.bottom;n.css("bottom",b+"px");div_barrager_box=a("<div class='barrage_box cl'></div>").appendTo(n);if(c.img){div_barrager_box.append("<span class='portrait z'></span>");var j=a("<img src='' >").appendTo(d+" .barrage_box .portrait");j.attr("src",c.img)}div_barrager_box.append(" <div class='z p'></div>");if(c.close){div_barrager_box.append(" <div class='close z'></div>")}var k=a("<span title=''></span>").appendTo(d+" .barrage_box .p");k.attr({id:c.id}).empty().append(c.info);if(navigator.userAgent.indexOf("MSIE 6.0")>0||navigator.userAgent.indexOf("MSIE 7.0")>0||navigator.userAgent.indexOf("MSIE 8.0")>0){k.css("color",c.old_ie_color)}else{k.css("color",c.color)}var h=0;n.css("margin-right",0);a(d).animate({right:g},c.speed*1000,function(){a(d).remove()});div_barrager_box.mouseover(function(){a(d).stop(true)});div_barrager_box.mouseout(function(){a(d).animate({right:g},c.speed*1000,function(){a(d).remove()})});a(d+".barrage .barrage_box .close").click(function(){a(d).remove()})};a.fn.barrager.removeAll=function(){a(".barrage").remove()}})(jQuery);