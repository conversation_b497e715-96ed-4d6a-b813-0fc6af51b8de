package com.wb.tool;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Types;
import java.util.Date;
import java.util.Properties;

import org.quartz.CronScheduleBuilder;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SimpleScheduleBuilder;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.quartz.impl.StdSchedulerFactory;

import com.wb.common.ScriptBuffer;
import com.wb.common.Var;
import com.wb.task.ScriptProxy;
import com.wb.util.DbUtil;
import com.wb.util.StringUtil;

public class TaskManager {
    public static Scheduler scheduler;

    /**
     * 把指定参数的任务加载到任务引擎中。如果相同id的任务已经存在，则重新加载该任务。
     *
     * @param taskId          任务id
     * @param taskName        任务名称
     * @param intervalType    周期类型
     * @param intervalExpress 周期表达式
     * @param className       执行的Java类
     * @param serverScript    执行的服务器端脚本
     * @param beginDate       开始时间
     * @param endDate         结束时间s
     * @throws Exception 加载任务发生异常
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static void loadTask(String taskId, String taskName, int intervalType, String intervalExpress,
                                String className, String serverScript, Date beginDate, Date endDate) throws Exception {
        JobDetail job;
        String[] express;
        Trigger trigger = null;

        // 如果指定id的任务已经存在，删除后重新加载
        deleteTask(taskId);
        if (StringUtil.isEmpty(className)) {
            job = JobBuilder.newJob(ScriptProxy.class).withIdentity(taskId, Scheduler.DEFAULT_GROUP)
                    .withDescription(taskName).build();
            JobDataMap dataMap = job.getJobDataMap();
            dataMap.put("job.id", "job." + taskId);
            dataMap.put("job.serverScript", serverScript);
        } else {
            Class clazz = Class.forName(className);
            job = JobBuilder.newJob(clazz).withIdentity(taskId, Scheduler.DEFAULT_GROUP).withDescription(taskName)
                    .build();
        }
        express = StringUtil.split(intervalExpress, ":");
        switch (intervalType) {
            case 0: //每隔XX秒执行
                trigger = TriggerBuilder.newTrigger().withIdentity(taskId, Scheduler.DEFAULT_GROUP)
                        .withSchedule(
                                SimpleScheduleBuilder.repeatSecondlyForever(Integer.parseInt(express[0])))
                        .build();
                break;
            case 1: //每隔XX分执行
                trigger = TriggerBuilder.newTrigger().withIdentity(taskId)
                        .withSchedule(
                                SimpleScheduleBuilder.repeatMinutelyForever(Integer.parseInt(express[0])))
                        .build();
                break;
            case 2: //每隔XX小时执行
                trigger = TriggerBuilder.newTrigger().withIdentity(taskId)
                        .withSchedule(
                                SimpleScheduleBuilder.repeatHourlyForever(Integer.parseInt(express[0])))
                        .build();
                break;
            case 3: //每日XX时间执行
                trigger = TriggerBuilder.newTrigger().withIdentity(taskId).withSchedule(CronScheduleBuilder
                        .dailyAtHourAndMinute(Integer.parseInt(express[0]), Integer.parseInt(express[1]))).build();
                break;
            case 4: //每周XX时间执行
                trigger = TriggerBuilder.newTrigger().withIdentity(taskId)
                        .withSchedule(CronScheduleBuilder.weeklyOnDayAndHourAndMinute(Integer.parseInt(express[0]),
                                Integer.parseInt(express[1]), Integer.parseInt(express[2])))
                        .build();
                break;
            case 5: //每月XX时间执行
                trigger = TriggerBuilder.newTrigger().withIdentity(taskId)
                        .withSchedule(CronScheduleBuilder.monthlyOnDayAndHourAndMinute(Integer.parseInt(express[0]),
                                Integer.parseInt(express[1]), Integer.parseInt(express[2])))
                        .build();
                break;
        }
        if (beginDate != null) {
            if (trigger != null) {
                trigger = trigger.getTriggerBuilder().startAt(beginDate).build();
            }
        }
        if (endDate != null) {
            if (trigger != null) {
                trigger = trigger.getTriggerBuilder().endAt(endDate).build();
            }
        }
        if (scheduler != null) {
            scheduler.scheduleJob(job, trigger);
        }
    }

    /**
     * 删除指定id的任务，如果任务不存在，则该方法无任何效果。
     *
     * @param taskId 任务id
     */
    public static void deleteTask(String taskId) throws Exception {
        JobKey key = new JobKey(taskId, Scheduler.DEFAULT_GROUP);
        if (scheduler != null) {
            if (scheduler.getJobDetail(key) != null) {
                scheduler.deleteJob(key);
            }
        }
        ScriptBuffer.remove("job." + taskId);
    }

    /**
     * 开始计划任务引擎，加载所有已经配置的计划任务。如果任务已经加载，则重新加载该任务。
     *
     * @throws Exception 启动异常
     */
    public static synchronized void start() throws Exception {
        if (!Var.getBool("sys.task.enabled")) {
            return;
        }
        if (scheduler == null) {
            StdSchedulerFactory factory = new StdSchedulerFactory();
            Properties props = new Properties();
            props.put(StdSchedulerFactory.PROP_THREAD_POOL_CLASS, "org.quartz.simpl.SimpleThreadPool");
            props.put("org.quartz.threadPool.threadCount", Var.getString("sys.task.threadCount"));
            // 判断是否启动集群
            if (Var.getBool("sys.task.cluster")) {
                // 加入Quartz 集群配置
                props.put("org.quartz.jobStore.isClustered", "true");
                props.put("org.quartz.jobStore.clusterCheckinInterval", "20000");
                props.put("org.quartz.jobStore.class", "org.quartz.impl.jdbcjobstore.JobStoreTX");
                props.put("org.quartz.jobStore.driverDelegateClass", "org.quartz.impl.jdbcjobstore.StdJDBCDelegate");
                props.put("org.quartz.jobStore.dataSource", "myDS");
                props.put("org.quartz.jobStore.tablePrefix", "QRTZ_");
                props.put("org.quartz.dataSource.myDS.jndiURL", Var.jndi);
            }
            factory.initialize(props);
            scheduler = factory.getScheduler();
            scheduler.start();
        } else if (scheduler.isStarted()) {
            return;
        }
        Connection conn = null;
        Statement st = null;
        ResultSet rs = null;
        try {
            conn = DbUtil.getConnection();
            st = conn.createStatement();
            rs = st.executeQuery("select * from WB_TASK");
            while (rs.next()) {
                // 停止的任务
                if (rs.getInt("STATUS") == 0) {
                    continue;
                }
                String taskId = rs.getString("TASK_ID");

                JobKey jobKey = JobKey.jobKey(taskId);
                if (!scheduler.checkExists(jobKey)) {
                    loadTask(taskId, rs.getString("TASK_NAME"), rs.getInt("INTERVAL_TYPE"),
                            rs.getString("INTERVAL_EXPRESS"), rs.getString("CLASS_NAME"),
                            (String) DbUtil.getObject(rs, "SERVER_SCRIPT", Types.NCLOB), rs.getTimestamp("BEGIN_DATE"),
                            rs.getTimestamp("END_DATE"));
                }
            }
        } finally {
            DbUtil.close(rs);
            DbUtil.close(st);
            DbUtil.close(conn);
        }
    }

    /**
     * 停止计划任务引擎。
     *
     * @throws Exception 停止异常
     */
    public static synchronized void stop() throws Exception {
        if (!Var.getBool("sys.task.enabled")) {
            return;
        }
        if (scheduler == null || scheduler.isShutdown()) {
            return;
        }
        scheduler.shutdown();
        scheduler = null;
        Thread.sleep(Var.getInt("sys.task.stopDelay"));
    }
}