package net.arccode.config;

import java.util.HashMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.json.JSONObject;

import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;
import com.wb.util.WebUtil;

import net.arccode.wechat.pay.api.common.constant.WXPayConstants;
import net.arccode.wechat.pay.api.common.util.SDKUtils;
import net.arccode.wechat.pay.api.common.util.WXPaySignUtils;
import net.arccode.wechat.pay.api.protocol.unified_order.UnifiedOrderRequest;
import net.arccode.wechat.pay.api.protocol.unified_order.UnifiedOrderResponse;
import net.arccode.wechat.pay.api.service.HttpRequest;
import net.arccode.wechat.pay.api.service.WXPayClient;
import net.arccode.wechat.pay.api.service.Wb;

/**
 * <AUTHOR> <PERSON> 微信统一下单
 */
public class UnifiedOrder {
	/**
	 * 获取用户信息地址
	 */
	public static String req_URL = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=APPID&secret=SECRET&code=CODE&grant_type=GRANT_TYPE";

	/**
	 * 微信统一下单
	 * 
	 * @param request
	 * @param response
	 * @param parm
	 * @return
	 */
	public static JSONObject unifiedOrder(HttpServletRequest request, HttpServletResponse response, JSONObject parm,
			String openId) {
		// 加载配置文件
		AccountsConfig.init();

		HttpSession session = request.getSession();
		String asyncNotifyUrl = AccountsConfig.notifyUrl;

		// 以下配置参数根据公司申请的微信支付帐号填写
		String appId = AccountsConfig.appId;
		String mchId = AccountsConfig.partner;
		String key = AccountsConfig.partnerkey;
		// 订单生成的机器 IP
		String spbill_create_ip = WebUtil.getRemoteAddr(request);
		// 商品描述
		String body = AccountsConfig.payBody;
		// 支付金额
		String totalFeeStr = parm.getString("totalfee");
		float totalFee = 0;
		if (!StringUtil.isEmpty(totalFeeStr)) {
			totalFee = new Float(totalFeeStr);
		}
		Integer total_fee = Math.round(totalFee * 100);
		// //支付参数
		String jsondata = parm.getString("jsondata");
		// 学生类型（0/新生1/在册学生）
		String charge_type = parm.getString("charge_type");
		String code = parm.getString("code");
		// 商户订单号
		String out_trade_no = SDKUtils.genOutTradeNo();
		// 初始化
		WXPayClient wxPayClient = new WXPayClient(appId, mchId, key);

		String nonceStr = SDKUtils.genOrderNo();
		UnifiedOrderRequest UNrequest = new UnifiedOrderRequest(body, out_trade_no, total_fee, spbill_create_ip,
				asyncNotifyUrl, "JSAPI", nonceStr);
		// 用户OpenId
		Object openId_ = session.getAttribute("openId");
		if (Wb.isEmpty(openId)) {
			if (Wb.isEmpty(openId_) || openId_ == "undefined") {
				String URL = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + AccountsConfig.appId
						+ "&secret=" + AccountsConfig.appSecret + "&code=" + code + "&grant_type="
						+ AccountsConfig.authType;
				JSONObject data = HttpRequest.httpRequest(URL, "GET", null);
				if (!Wb.isEmpty(data)) {
					openId_ = data.getString("openid");
					session.setAttribute("openId", openId_);
				}
			}
		}
		if (Wb.isEmpty(openId)) {
			openId = openId_.toString();
		}
		UNrequest.setOpenId(openId);
		UnifiedOrderResponse UNresponse = null;
		try {
			UNresponse = wxPayClient.execute(UNrequest);
			// 存储支付信息
			String url = AccountsConfig.payUrl;
			JSONObject data = new JSONObject();
			data.put("trid", 0);
			data.put("orderid", out_trade_no);
			data.put("paytype", 1);
			data.put("charge_type", charge_type);
			data.put("jsondata", jsondata);
			HttpRequest.httpRequest(url, data.toString());

			HashMap<String, String> back = new HashMap<String, String>();
			String time = Long.toString(System.currentTimeMillis() / 1000);
			back.put("appId", UNresponse.getAppId());
			back.put("timeStamp", time);
			back.put("nonceStr", UNresponse.getNonceStr());
			back.put("package", "prepay_id=" + UNresponse.getPrepayId());
			back.put("signType", "MD5");
			String signContent = WXPaySignUtils.getSignContent(back);
			String sign2 = WXPaySignUtils.md5Sign(signContent, key, WXPayConstants.CHARSET_UTF8).toUpperCase();

			JSONObject jsonObject = new JSONObject();
			jsonObject.put("appId", UNresponse.getAppId());
			jsonObject.put("timeStamp", time);
			jsonObject.put("nonceStr", UNresponse.getNonceStr());
			jsonObject.put("package", "prepay_id=" + UNresponse.getPrepayId());
			jsonObject.put("signType", "MD5");
			jsonObject.put("paySign", sign2);
			jsonObject.put("out_trade_no", out_trade_no);

			return jsonObject;
		} catch (Exception e) {
			LogUtil.error(request, "微信统一下单异常" + SysUtil.getRootError(e));
			return null;
		}
	}
}
