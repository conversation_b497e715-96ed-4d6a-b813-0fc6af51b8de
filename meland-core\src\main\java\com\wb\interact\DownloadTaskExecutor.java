package com.wb.interact;

import com.wb.util.LogUtil;

import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 下载任务执行器类，用于管理和执行文件下载相关的异步任务
 * 负责线程池管理、任务调度和监控
 */
public class DownloadTaskExecutor {
    private static final AtomicInteger counter = new AtomicInteger(1);
    
    // 线程池定义，替换ForkJoinPool.commonPool()
    private static final ThreadPoolExecutor taskExecutor = new ThreadPoolExecutor(
        5, 10, 60, TimeUnit.SECONDS, 
        new LinkedBlockingQueue<>(100),
        r -> {
            Thread thread = new Thread(r, "file-push-worker-" + counter.getAndIncrement());
            thread.setDaemon(true);
            return thread;
        },
        new ThreadPoolExecutor.CallerRunsPolicy()
    );
    
    private static final ScheduledExecutorService monitorExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread thread = new Thread(r, "file-push-monitor");
        thread.setDaemon(true);
        return thread;
    });
    
    static {
        // 添加JVM关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                LogUtil.info("正在关闭DownloadTaskExecutor线程池...");
                taskExecutor.shutdown();
                if (!taskExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    LogUtil.warn("DownloadTaskExecutor线程池未能在30秒内关闭，执行强制关闭");
                    taskExecutor.shutdownNow();
                }
                monitorExecutor.shutdown();
                if (!monitorExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    monitorExecutor.shutdownNow();
                }
                LogUtil.info("DownloadTaskExecutor线程池已关闭");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                LogUtil.error("关闭DownloadTaskExecutor线程池时被中断");
                taskExecutor.shutdownNow();
                monitorExecutor.shutdownNow();
            }
        }));
    }
    
    /**
     * 提交异步任务到线程池执行
     * 
     * @param task 要执行的任务
     * @return 表示任务执行的Future对象
     */
    public static Future<?> submitTask(Runnable task) {
        return taskExecutor.submit(task);
    }
    
    /**
     * 提交带返回值的异步任务到线程池执行
     * 
     * @param <T> 任务返回值类型
     * @param task 要执行的任务
     * @return 表示任务执行的Future对象
     */
    public static <T> Future<T> submitTask(Callable<T> task) {
        return taskExecutor.submit(task);
    }
    
    /**
     * 通过CompletableFuture异步执行任务
     * 
     * @param task 要执行的任务
     * @return CompletableFuture对象
     */
    public static CompletableFuture<Void> runAsync(Runnable task) {
        return CompletableFuture.runAsync(task, taskExecutor);
    }
    
    /**
     * 在监控线程中定时执行任务
     * 
     * @param task 要执行的任务
     * @param initialDelay 初始延迟时间
     * @param period 执行周期
     * @param unit 时间单位
     * @return 表示任务执行的ScheduledFuture对象
     */
    public static ScheduledFuture<?> scheduleAtFixedRate(Runnable task, long initialDelay, long period, TimeUnit unit) {
        return monitorExecutor.scheduleAtFixedRate(task, initialDelay, period, unit);
    }
    
    /**
     * 延迟执行一次性任务
     * 
     * @param task 要执行的任务
     * @param delay 延迟时间
     * @param unit 时间单位
     * @return 表示任务执行的ScheduledFuture对象
     */
    public static ScheduledFuture<?> schedule(Runnable task, long delay, TimeUnit unit) {
        return monitorExecutor.schedule(task, delay, unit);
    }
    
    /**
     * 获取线程池当前状态信息
     * 
     * @return 包含线程池状态的字符串
     */
    public static String getPoolStatus() {
        return String.format("线程池状态 - 活跃:%d, 池大小:%d, 核心:%d, 最大:%d, 队列:%d",
            taskExecutor.getActiveCount(), taskExecutor.getPoolSize(), taskExecutor.getCorePoolSize(),
            taskExecutor.getMaximumPoolSize(), taskExecutor.getQueue().size());
    }
    
    /**
     * 关闭线程池，等待任务完成
     * 
     * @param waitForTasks 是否等待任务完成
     */
    public static void shutdown(boolean waitForTasks) {
        try {
            LogUtil.info("正在关闭下载任务处理器...");
            
            // 停止接受新任务
            taskExecutor.shutdown();
            monitorExecutor.shutdown();
            
            if (waitForTasks) {
                // 等待现有任务完成
                if (!taskExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    LogUtil.warn("下载任务处理器未能在30秒内完成，正在强制关闭");
                    taskExecutor.shutdownNow();
                }
            } else {
                // 立即强制关闭
                taskExecutor.shutdownNow();
            }
            
            monitorExecutor.shutdownNow();
            LogUtil.info("下载任务处理器已关闭");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LogUtil.error("关闭下载任务处理器时被中断");
            
            // 强制关闭
            taskExecutor.shutdownNow();
            monitorExecutor.shutdownNow();
        }
    }
    
    /**
     * 强制取消所有任务
     * 
     * @param taskFutures 当前正在执行的任务Future映射
     */
    public static void cancelAllTasks(Map<String, Future<?>> taskFutures) {
        for (Map.Entry<String, Future<?>> entry : taskFutures.entrySet()) {
            entry.getValue().cancel(true);
        }
    }
} 