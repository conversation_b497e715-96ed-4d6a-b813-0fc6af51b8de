package net.arccode.config;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.json.JSONObject;

import com.wb.util.LogUtil;
import com.wb.util.SysUtil;

import net.arccode.wechat.pay.api.protocol.pay_notify.PayNotifyResponse;
import net.arccode.wechat.pay.api.service.HttpRequest;
import net.arccode.wechat.pay.api.service.WXPayClient;
import net.arccode.wechat.pay.api.service.Wb;
import net.arccode.wechat.pay.api.service.XmlUtil;

public class WechatApi {
	/**
	 * 异步接口，后台操作
	 * 
	 * @param request
	 * @return
	 */
	public static String notify_url(HttpServletRequest request,HttpServletResponse response) {
		try {
			//获取XML文件流
			String resylt = XmlUtil.getXmlRequest(request, response);
			if (!Wb.isEmpty(resylt)) {
				try {
					//初始化
					// 以下配置参数根据公司申请的微信支付帐号填写
					String appId = AccountsConfig.appId;
					String mchId = AccountsConfig.partner;
					String key = AccountsConfig.partnerkey;
					WXPayClient wxPayClient = new WXPayClient(appId, mchId, key);

					PayNotifyResponse payResponse = wxPayClient.parseNotify(resylt, PayNotifyResponse.class);
					
					if (payResponse.getReturnCode().equals("SUCCESS") || payResponse.getResultCode().equals("SUCCESS")) {
						//商户订单号
						String out_trade_no = payResponse.getOutTradeNo();
						//微信支付订单号	
						String pay_id = payResponse.getTransactionId();
						//处理支付成功以后的逻辑
						try {
							//请求回调支付信息地址
							String url = AccountsConfig.backUrl;
							JSONObject obj = new JSONObject();
							obj.put("pay_status", 1);
							obj.put("pay_number", pay_id);
							obj.put("order_number", out_trade_no);

							HttpRequest.httpRequest(url, obj.toString());

							LogUtil.error(request, "微信订单：" + out_trade_no + "交易号："+pay_id+",回调：验证成功");
							
							return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
						} catch (Exception e) {
							LogUtil.error(request, "微信回调：更新订单错误 - " + SysUtil.getRootError(e));
						} finally {
						}
					}else{
						LogUtil.error(request, "微信回调：验证失败,代码：" + payResponse.getReturnCode()+"=="+payResponse.getResultCode());
					}
				} catch (Exception e) {
					LogUtil.error(request, "微信回调：通信错误 - " + SysUtil.getRootError(e));
				}

			} else {
				//签名校验失败
				LogUtil.error(request, "微信回调：签名校验失败：" + resylt);
				String checkXml = "<xml><return_code><![CDATA[FAIL]]></return_code>"
						+ "<return_msg><![CDATA[check sign fail]]></return_msg></xml>";
				return checkXml;
			}

		} catch (Exception e) {
			LogUtil.error(request, "微信回调异常：" + SysUtil.getRootError(e));
		}
		return null;
	}
}
