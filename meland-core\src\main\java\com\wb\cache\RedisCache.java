package com.wb.cache;

import com.alibaba.fastjson.JSONObject;
import com.wb.cache.bloom.CountingBloomFilter;
import com.wb.cache.bloom.RedisBloomFilter;
import com.wb.cache.pipeline.DefaultPipelineCallback;
import com.wb.cache.pipeline.PipelineCallback;
import com.wb.cache.stats.CacheStats;
import com.wb.common.Var;
import com.wb.util.LogUtil;
import com.wb.util.StringUtil;
import com.wb.util.SysUtil;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Service;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.data.redis.connection.RedisZSetCommands.Tuple;
import org.springframework.data.redis.connection.RedisZSetCommands.Limit; // 添加导入
import java.nio.charset.StandardCharsets; // 添加导入

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
public class RedisCache {

    /**
     * 表示分布式锁的前缀，用于标识存储在 Redis 中的锁键。
     * <p>
     * LOCK_PREFIX 在基于 Redis 的分布式锁实现中，用于区分其他数据， 以避免与其他 Redis 键冲突。通过设置统一的前缀，能够方便地
     * 标识和管理与锁相关的键值。
     */
    public static final String LOCK_PREFIX = "redis_lock";
    /**
     * 表示当前在线用户的唯一标识符。
     * <p>
     * 该常量用于存储在线用户的用户标识列表的键值。 适用于缓存、会话或其他存储机制中，用于跟踪当前活动的用户。
     */
    public static final String ONLINE_USERS = "ONLINE_USER_IDS";
    /**
     * 表示在线用户的前缀，用于标识和管理与在线用户相关的数据。 该前缀通常用于 Redis 键或其他存储系统中，作为在线用户信息的命名空间前缀。
     */
    public static final String ONLINE_USER_PREFIX = "ONLINE:USER:";
    /**
     * 表示锁的过期时间，以毫秒为单位。
     * <p>
     * 该变量定义了锁的有效时间长度，超过此时间锁将自动失效。 通常用于分布式锁等需要时间限制的场景。
     */
    public static final int LOCK_EXPIRE = 1000; // ms

    /**
     * 表示操作或请求的最大重试次数。
     * <p>
     * 该变量定义了在出现错误或失败情况下允许的重试次数限制。 通常用于网络请求、任务执行或其他可能临时失败的操作中。
     * <p>
     * 常量值为 3，表示最多可重试三次。
     */
    private static final int RETRY_TIMES = 3; // 重试次数

    /**
     * 表示操作的重试间隔时间，单位为毫秒。 该变量为常量，用于控制在某些情况下重复尝试操作之间的等待时间。
     */
    private static final long RETRY_INTERVAL = 100; // 重试间隔时间100ms

    /**
     * 表示序列号的最大值。 此常量用于限制系统中序列号的上限，通常与业务逻辑相关。 值为 9999，不可更改，确保程序运行时的一致性。
     */
    private static final int MAX_SEQUENCE = 9999;

    /**
     * RedisTemplate 是 Spring 提供的用于操作 Redis 数据库的工具类。 redisTemplate 是一个泛型实例，其中键为
     * String 类型，值为 Object 类型。 该变量被声明为 final，表示其引用不可更改，一旦初始化完成，将始终指向同一个
     * RedisTemplate 实例。
     * <p>
     * 该变量可以用于执行 Redis 的各种操作，如存储和检索数据、发布订阅消息、 处理 Redis 的事务和管道等功能。
     */
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 用于访问 Redis 数据库中字符串类型数据的模板工具类。 提供对 Redis 操作的基础功能支持，例如增删改查等操作。 使用该实例可以直接与 Redis
     * 进行交互，简化编程操作。 是基于 Spring Data Redis 提供的封装类。
     */
    private final StringRedisTemplate stringRedisTemplate;

    /**
     * RedissonClient 对象，用于与 Redis 服务器进行交互。
     * <ul>
     * - 提供分布式缓存的功能。 - 支持分布式锁的实现。 - 支持各种 Redis 数据结构的操作，如字符串、哈希、列表、集合等。 - 支持发布与订阅功能。
     * - 可用于实现分布式任务调度与执行。
     * </ul>
     * 此变量为不可变引用，只能通过构造函数初始化，确保线程安全。
     */
    private final RedissonClient redissonClient;

    /**
     * 表示缓存空值的标记对象，用于缓存穿透防护
     */
    private static final Object NULL_VALUE = new Object();

    /**
     * 本地缓存，用于减少对Redis的访问
     */
    private final Map<String, CacheItem> localCache = new ConcurrentHashMap<>();

    /**
     * 本地缓存项过期时间（秒）
     */
    private static final long LOCAL_CACHE_EXPIRE = 60;

    /**
     * 本地缓存最大容量
     */
    private static final int LOCAL_CACHE_MAX_SIZE = 1000;

    /**
     * 过期时间随机波动范围（百分比）
     */
    private static final double EXPIRE_TIME_DELTA_FACTOR = 0.1;

    /**
     * 缓存统计对象
     */
    private final CacheStats cacheStats = new CacheStats();

    /**
     * 获取缓存统计信息
     *
     * @return 统计信息Map
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = cacheStats.getStats();
        stats.put("localCacheSize", getLocalCacheSize());
        return stats;
    }

    /**
     * 重置缓存统计数据
     */
    public void resetCacheStats() {
        cacheStats.reset();
    }

    /**
     * 获取随机化的过期时间，用于防止缓存雪崩
     *
     * @param baseTime 基准时间
     * @param unit     时间单位
     * @return 随机化后的过期时间（秒）
     */
    private long getRandomizedExpireTime(long baseTime, TimeUnit unit) {
        // 转换为秒
        long expireTimeSeconds = unit.toSeconds(baseTime);
        // 计算波动范围（默认为10%）
        long delta = (long) (expireTimeSeconds * EXPIRE_TIME_DELTA_FACTOR);
        // 在正负delta范围内生成随机值
        long randomDelta = ThreadLocalRandom.current().nextLong(-delta, delta);
        // 返回基准时间加上随机波动值，确保至少为1秒
        return Math.max(1, expireTimeSeconds + randomDelta);
    }

    /**
     * 内部缓存项类，存储值和过期时间
     */
    private static class CacheItem {
        private final Object value;
        private final long expireTime;

        public CacheItem(Object value, long expireTimeMillis) {
            this.value = value;
            this.expireTime = expireTimeMillis;
        }

        public Object getValue() {
            return value;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }
    }

    /**
     * 将值添加到本地缓存
     *
     * @param key           键
     * @param value         值
     * @param expireSeconds 过期时间（秒）
     */
    private void putLocalCache(String key, Object value, long expireSeconds) {
        // 如果本地缓存容量达到上限，清理过期项
        if (localCache.size() >= LOCAL_CACHE_MAX_SIZE) {
            cleanupLocalCache();

            // 如果清理后仍然达到上限，移除最早的项
            if (localCache.size() >= LOCAL_CACHE_MAX_SIZE) {
                // 简单策略：随机移除一个元素
                Optional<String> keyToRemove = localCache.keySet().stream().findFirst();
                keyToRemove.ifPresent(localCache::remove);
            }
        }

        // 添加到本地缓存
        localCache.put(key, new CacheItem(value, expireSeconds * 1000));
    }

    /**
     * 从本地缓存获取值
     *
     * @param key 键
     * @return 值，如果不存在或已过期则返回null
     */
    private Object getLocalCache(String key) {
        CacheItem item = localCache.get(key);

        if (item == null) {
            return null;
        }

        // 检查是否过期
        if (item.isExpired()) {
            localCache.remove(key);
            return null;
        }

        // 记录本地缓存命中
        cacheStats.recordLocalHit();

        return item.getValue();
    }

    /**
     * 清理过期的本地缓存项
     */
    public void cleanupLocalCache() {
        // 删除过期的缓存项
        localCache.entrySet().removeIf(entry -> entry.getValue().isExpired());
    }

    /**
     * 清空本地缓存 在集群环境中，会通知其他节点同步清空本地缓存
     */
    public void clearLocalCache() {
        localCache.clear();

        // 发布清空本地缓存的消息
        try {
            com.alibaba.fastjson.JSONObject message = new com.alibaba.fastjson.JSONObject();
            message.put("action", "clear_all");
            message.put("server", SysUtil.getServerId());
            publish("cache:invalidate:all", message);
        } catch (Exception e) {
            LogUtil.warn("发布清空缓存消息失败: " + e.getMessage());
        }
    }

    /**
     * 获取本地缓存大小
     *
     * @return 本地缓存项数量
     */
    public int getLocalCacheSize() {
        return localCache.size();
    }

    /**
     * 获取带有缓存穿透防护的值
     *
     * @param key 键
     * @return 值，如果不存在则返回null
     */
    public Object getWithProtection(String key) {
        validateKey(key);

        // 尝试从本地缓存获取
        Object localValue = getLocalCache(key);
        if (localValue != null) {
            // 如果是NULL_VALUE标记，表示Redis中确实没有这个值
            if (localValue == NULL_VALUE) {
                return null;
            }
            return localValue;
        }

        // 从Redis获取
        Object value = getValue(key);

        // 更新本地缓存
        if (value != null) {
            // 存储真实值
            putLocalCache(key, value, LOCAL_CACHE_EXPIRE);
        } else {
            // 存储空值标记，防止缓存穿透，空值过期时间较短
            putLocalCache(key, NULL_VALUE, 5);
        }

        return value;
    }

    /**
     * 带有缓存穿透防护和加载机制的获取方法 如果缓存中不存在，则使用提供的函数加载并缓存
     *
     * @param key          键
     * @param loadFunction 加载函数，用于在缓存未命中时加载数据
     * @param expireTime   过期时间
     * @param unit         时间单位
     * @return 缓存中的值或通过加载函数获取的值
     */
    public <T> T getWithLoader(String key, Function<String, T> loadFunction, long expireTime, TimeUnit unit) {
        validateKey(key);
        if (loadFunction == null) {
            throw new IllegalArgumentException("加载函数不能为null");
        }
        validateTime(expireTime, unit);

        // 尝试从本地缓存获取
        Object localValue = getLocalCache(key);
        if (localValue != null) {
            if (localValue == NULL_VALUE) {
                return null;
            }
            return (T) localValue;
        }

        // 从Redis获取
        Object value = getValue(key);

        if (value != null) {
            // 更新本地缓存
            putLocalCache(key, value, LOCAL_CACHE_EXPIRE);
            return (T) value;
        }

        // 缓存未命中，调用加载函数
        try {
            // 记录加载次数
            cacheStats.recordLoad();

            T loadedValue = loadFunction.apply(key);

            // 将加载的值存入Redis和本地缓存
            if (loadedValue != null) {
                // 使用随机化的过期时间防止缓存雪崩
                long randomizedExpireTime = getRandomizedExpireTime(expireTime, unit);
                setValue(key, loadedValue, randomizedExpireTime, TimeUnit.SECONDS);
                putLocalCache(key, loadedValue, LOCAL_CACHE_EXPIRE);
            } else {
                // 存储空值标记，防止缓存穿透，空值过期时间较短
                setValue(key, NULL_VALUE, 60, TimeUnit.SECONDS);
                putLocalCache(key, NULL_VALUE, 5);
            }

            return loadedValue;
        } catch (Exception e) {
            LogUtil.error("加载缓存值失败 [key=" + key + "]: " + e.getMessage());
            // 记录错误
            cacheStats.recordError();
            return null;
        }
    }

    /**
     * 带有缓存穿透防护和加载机制的获取方法（无本地缓存版本） 如果缓存中不存在，则使用提供的函数加载并缓存到Redis，但不使用本地缓存
     *
     * @param key          键
     * @param loadFunction 加载函数，用于在缓存未命中时加载数据
     * @param expireTime   过期时间
     * @param unit         时间单位
     * @return 缓存中的值或通过加载函数获取的值
     */
    public <T> T getWithLoaderNoLocalCache(String key, Function<String, T> loadFunction, long expireTime,
            TimeUnit unit) {
        validateKey(key);
        if (loadFunction == null) {
            throw new IllegalArgumentException("加载函数不能为null");
        }
        validateTime(expireTime, unit);

        // 直接从Redis获取，不查询本地缓存
        Object value = getValue(key);

        if (value != null) {
            return (T) value;
        }

        // 缓存未命中，调用加载函数
        try {
            // 记录加载次数
            cacheStats.recordLoad();

            T loadedValue = loadFunction.apply(key);

            // 将加载的值只存入Redis，不存入本地缓存
            if (loadedValue != null) {
                // 使用随机化的过期时间防止缓存雪崩
                long randomizedExpireTime = getRandomizedExpireTime(expireTime, unit);
                setValue(key, loadedValue, randomizedExpireTime, TimeUnit.SECONDS);
            } else {
                // 存储空值标记，防止缓存穿透，空值过期时间较短
                setValue(key, NULL_VALUE, 60, TimeUnit.SECONDS);
            }

            return loadedValue;
        } catch (Exception e) {
            LogUtil.error("加载缓存值失败 [key=" + key + "]: " + e.getMessage());
            // 记录错误
            cacheStats.recordError();
            return null;
        }
    }

    /**
     * 带有缓存穿透防护和加载机制的获取方法 根据useLocalCache参数决定是否使用本地缓存
     *
     * @param key           键
     * @param loadFunction  加载函数，用于在缓存未命中时加载数据
     * @param expireTime    过期时间
     * @param unit          时间单位
     * @param useLocalCache 是否使用本地缓存
     * @return 缓存中的值或通过加载函数获取的值
     */
    public <T> T getWithLoader(String key, Function<String, T> loadFunction, long expireTime, TimeUnit unit,
            boolean useLocalCache) {
        if (!useLocalCache) {
            return getWithLoaderNoLocalCache(key, loadFunction, expireTime, unit);
        }

        return getWithLoader(key, loadFunction, expireTime, unit);
    }

    /**
     * 带有防止缓存雪崩机制的设置方法 通过添加随机过期时间，避免大量缓存同时过期 在多节点集群环境中，通知其他节点更新本地缓存
     *
     * @param key        键
     * @param value      值
     * @param expireTime 基础过期时间
     * @param unit       时间单位
     */
    public void setWithAntiAvalanche(String key, Object value, long expireTime, TimeUnit unit) {
        validateKey(key);
        validateValue(value);
        validateTime(expireTime, unit);

        // 使用随机化的过期时间防止缓存雪崩
        long randomizedExpireTime = getRandomizedExpireTime(expireTime, unit);

        // 存入Redis
        setValue(key, value, randomizedExpireTime, TimeUnit.SECONDS);

        // 同时更新本地缓存
        putLocalCache(key, value, LOCAL_CACHE_EXPIRE);

        // 发布缓存更新消息，通知其他节点更新本地缓存
        try {
            // 将值转换为字符串形式以便传输
            String valueJson = value instanceof String ? (String) value : JSONObject.toJSONString(value);

            com.alibaba.fastjson.JSONObject message = new com.alibaba.fastjson.JSONObject();
            message.put("key", key);
            message.put("value", valueJson);
            message.put("server", SysUtil.getServerId());
            publish("cache:update", message);
        } catch (Exception e) {
            // 序列化失败不影响主要功能，仅记录日志
            LogUtil.warn("发布缓存更新消息时序列化值失败: " + e.getMessage());
        }
    }

    /**
     * 删除键，同时清除本地缓存和Redis缓存 在多节点集群环境中，会发布缓存删除事件，通知其他节点清除本地缓存
     *
     * @param key 键
     */
    public void deleteWithLocalCache(String key) {
        validateKey(key);

        // 从本地缓存中删除
        localCache.remove(key);

        // 从Redis中删除
        delKey(key);

        // 发布缓存删除消息，通知其他节点删除本地缓存
        try {
            com.alibaba.fastjson.JSONObject message = new com.alibaba.fastjson.JSONObject();
            message.put("key", key);
            message.put("server", SysUtil.getServerId());
            publish("cache:invalidate", message);
        } catch (Exception e) {
            LogUtil.warn("发布缓存失效消息失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除键，同时清除本地缓存和Redis缓存 在多节点集群环境中，会发布缓存删除事件，通知其他节点清除本地缓存
     *
     * @param keys 键集合
     * @return 删除的键数量
     */
    public long multiDeleteWithLocalCache(Collection<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return 0;
        }

        // 从本地缓存中删除
        for (String key : keys) {
            if (!StringUtil.isEmpty(key)) {
                localCache.remove(key);

                // 发布缓存删除消息，通知其他节点删除本地缓存
                try {
                    com.alibaba.fastjson.JSONObject message = new com.alibaba.fastjson.JSONObject();
                    message.put("key", key);
                    message.put("server", SysUtil.getServerId());
                    publish("cache:invalidate", message);
                } catch (Exception e) {
                    LogUtil.warn("发布缓存失效消息失败: " + e.getMessage());
                }
            }
        }

        // 从Redis中批量删除
        return multiDelete(keys);
    }

    /**
     * 使用互斥锁机制防止缓存击穿
     *
     * @param key        缓存键
     * @param supplier   数据加载函数
     * @param expireTime 过期时间
     * @param unit       时间单位
     * @param <T>        返回值类型
     * @return 缓存值或加载的值
     */
    public <T> T getWithMutex(String key, Supplier<T> supplier, long expireTime, TimeUnit unit) {
        validateKey(key);
        if (supplier == null) {
            throw new IllegalArgumentException("数据加载函数不能为null");
        }
        validateTime(expireTime, unit);

        // 尝试从本地缓存获取
        Object localValue = getLocalCache(key);
        if (localValue != null) {
            if (localValue == NULL_VALUE) {
                return null;
            }
            return (T) localValue;
        }

        // 从Redis中获取
        Object value = getValue(key);

        if (value != null) {
            // 更新本地缓存
            putLocalCache(key, value, LOCAL_CACHE_EXPIRE);
            return (T) value;
        }

        // 缓存未命中，使用分布式锁防止缓存击穿
        String lockKey = "lock:" + key;
        boolean locked = tryLock(lockKey, 100, 5000);

        try {
            if (locked) {
                // 获取锁成功，再次检查缓存（双重检查，避免其他线程已经加载了值）
                value = getValue(key);
                if (value != null) {
                    putLocalCache(key, value, LOCAL_CACHE_EXPIRE);
                    return (T) value;
                }

                // 记录加载次数
                cacheStats.recordLoad();

                // 真正加载数据
                T loadedValue = supplier.get();

                // 将加载的值存入Redis和本地缓存
                if (loadedValue != null) {
                    // 使用随机化的过期时间防止缓存雪崩
                    long randomizedExpireTime = getRandomizedExpireTime(expireTime, unit);
                    setValue(key, loadedValue, randomizedExpireTime, TimeUnit.SECONDS);
                    putLocalCache(key, loadedValue, LOCAL_CACHE_EXPIRE);
                } else {
                    // 存储空值标记，防止缓存穿透，空值过期时间较短
                    setValue(key, NULL_VALUE, 60, TimeUnit.SECONDS);
                    putLocalCache(key, NULL_VALUE, 5);
                }

                return loadedValue;
            } else {
                // 未获取到锁，说明其他线程正在加载，稍等后再次从缓存获取
                try {
                    Thread.sleep(200);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }

                // 再次从Redis获取
                value = getValue(key);
                if (value != null) {
                    putLocalCache(key, value, LOCAL_CACHE_EXPIRE);
                    if (value == NULL_VALUE) {
                        return null;
                    }
                    return (T) value;
                }

                // 仍然没有，返回null
                return null;
            }
        } catch (Exception e) {
            // 记录错误
            cacheStats.recordError();
            LogUtil.error("使用互斥锁获取缓存值失败 [key=" + key + "]: " + e.getMessage());
            return null;
        } finally {
            if (locked) {
                unLock(lockKey);
            }
        }
    }

    /**
     * RedisCache 构造方法，用于初始化 Redis 缓存工具类实例。
     *
     * @param redisTemplate       用于操作 Redis 的 RedisTemplate 实例。
     * @param stringRedisTemplate 专门用于操作字符串类型数据的 RedisTemplate 实例。
     * @param redissonClient      Redisson 客户端实例，用于分布式锁等高级功能。
     */
    public RedisCache(RedisTemplate<String, Object> redisTemplate, StringRedisTemplate stringRedisTemplate,
            RedissonClient redissonClient) {
        this.redisTemplate = redisTemplate;
        this.stringRedisTemplate = stringRedisTemplate;
        this.redissonClient = redissonClient;

        // 检测并自动设置Redis集群模式
        RedisConfiguration.detectAndSetClusterMode(redisTemplate);

        // 缓存消息由统一的消息监听器处理，不需要在这里初始化
        LogUtil.info("Redis缓存服务初始化完成" + (RedisConfiguration.isClusterModeEnabled() ? "，已启用集群兼容模式" : ""));
    }

    /**
     * 执行Redis操作并处理异常
     *
     * @param operation 操作描述
     * @param key       操作的键
     * @param supplier  实际执行的操作
     * @return 操作结果，如果发生异常则返回null
     */
    private <T> T executeWithExceptionHandling(String operation, String key, Supplier<T> supplier) {
        // 记录操作次数
        cacheStats.recordOperation(operation);

        try {
            T result = supplier.get();
            if (result != null) {
                // 记录命中
                if (operation.startsWith("GET_") || operation.equals("MULTI_GET")) {
                    cacheStats.recordHit();
                }
            } else if (operation.startsWith("GET_") || operation.equals("MULTI_GET")) {
                // 记录未命中
                cacheStats.recordMiss();
            }
            return result;
        } catch (IllegalArgumentException e) {
            // 参数错误类异常需要抛出，让调用者知道
            LogUtil.error("参数错误: " + operation + " [key=" + key + "]: " + e.getMessage());
            // 记录错误
            cacheStats.recordError();
            throw e; // 重新抛出参数异常
        } catch (Exception e) {
            // 检查是否是 GET_VALUE 的反序列化异常
            if ("GET_VALUE".equals(operation) && e instanceof SerializationException) {
                LogUtil.debug(
                        "Redis操作失败 (反序列化异常): " + operation + " [key=" + key + "], 尝试回退到原始字节读取: " + e.getMessage());
                try {
                    RedisSerializer<String> stringSerializer = StringRedisSerializer.UTF_8;
                    byte[] rawBytes = redisTemplate.execute(
                            (RedisCallback<byte[]>) connection -> connection.get(stringSerializer.serialize(key)));
                    if (rawBytes != null) {
                        try {
                            String fallbackValue = new String(rawBytes, java.nio.charset.StandardCharsets.UTF_8);
                            LogUtil.debug("反序列化回退成功 [key=" + key + "], 读取到字符串值: " + fallbackValue);
                            try {
                                return (T) fallbackValue;
                            } catch (ClassCastException cce) {
                                LogUtil.error("反序列化回退成功但类型转换失败 [key=" + key + "]: " + cce.getMessage());
                                cacheStats.recordError();
                                return null;
                            }
                        } catch (Exception decodeEx) {
                            LogUtil.error("反序列化回退成功但解码字节失败 [key=" + key + "]: " + decodeEx.getMessage());
                            cacheStats.recordError();
                            return null;
                        }
                    } else {
                        LogUtil.warn("反序列化回退失败 [key=" + key + "]: 原始字节值为 null (键可能不存在)");
                        cacheStats.recordMiss();
                        return null;
                    }
                } catch (Exception fallbackEx) {
                    LogUtil.error("反序列化回退尝试过程中发生异常 [key=" + key + "]: " + fallbackEx.getMessage());
                    cacheStats.recordError();
                }
                // !!! 新增：检查是否是 ZSET_REVERSE_RANGE_WITH_SCORES 的反序列化异常 !!!
            } else if ("ZSET_REVERSE_RANGE_WITH_SCORES".equals(operation) && e instanceof SerializationException) {
                LogUtil.debug(
                        "Redis操作失败 (ZSet反序列化异常): " + operation + " [key=" + key + "], 尝试回退到原始字节读取: " + e.getMessage());
                try {
                    // 尝试直接执行 ZREVRANGE WITHSCORES 并将成员作为字符串处理
                    Set<Tuple> rawTuples = redisTemplate.execute((RedisCallback<Set<Tuple>>) connection -> {
                        // 注意：这里的 start 和 end 需要从原始调用中获取，但 executeWithExceptionHandling 没有这些参数
                        // 这是一个设计缺陷，暂时假设我们读取整个 ZSet 或一个固定范围 (例如 0 到 -1)
                        // 更优的方案是修改 zsetReverseRangeWithScores 方法直接处理异常或传递参数
                        // 这里我们先用 0, -1 读取全部
                        return connection.zRevRangeWithScores(key.getBytes(java.nio.charset.StandardCharsets.UTF_8), 0,
                                -1);
                    });

                    if (rawTuples != null) {
                        Set<ZSetOperations.TypedTuple<Object>> fallbackResult = new LinkedHashSet<>();
                        RedisSerializer<String> stringSerializer = StringRedisSerializer.UTF_8;
                        for (Tuple rawTuple : rawTuples) {
                            String memberValue;
                            try {
                                // 尝试将成员字节解码为字符串
                                memberValue = new String(rawTuple.getValue(), java.nio.charset.StandardCharsets.UTF_8);
                            } catch (Exception decodeEx) {
                                memberValue = "<解码失败: "
                                        + org.apache.commons.codec.binary.Hex.encodeHexString(rawTuple.getValue())
                                        + ">";
                                LogUtil.warn("ZSet成员解码失败 [key=" + key + ", memberBytes(hex)="
                                        + org.apache.commons.codec.binary.Hex.encodeHexString(rawTuple.getValue())
                                        + "]: " + decodeEx.getMessage());
                            }
                            fallbackResult.add(new DefaultTypedTuple<>(memberValue, rawTuple.getScore()));
                        }
                        LogUtil.debug("ZSet反序列化回退成功 [key=" + key + "], 读取到 " + fallbackResult.size() + " 个成员 (作为字符串)");
                        // 同样，需要注意类型转换
                        try {
                            return (T) fallbackResult;
                        } catch (ClassCastException cce) {
                            LogUtil.error("ZSet反序列化回退成功但类型转换失败 [key=" + key + "]: " + cce.getMessage());
                            cacheStats.recordError();
                            return null;
                        }
                    } else {
                        LogUtil.warn("ZSet反序列化回退失败 [key=" + key + "]: 原始元组集为 null (键可能不存在)");
                        cacheStats.recordMiss();
                        return null;
                    }
                } catch (Exception fallbackEx) {
                    LogUtil.error("ZSet反序列化回退尝试过程中发生异常 [key=" + key + "]: " + fallbackEx.getMessage());
                    cacheStats.recordError();
                }
                // !!! 新增：检查是否是 HASH_GET_ALL 的反序列化异常 !!!
            } else if ("HASH_GET_ALL".equals(operation) && e instanceof SerializationException) {
                LogUtil.debug(
                        "Redis操作失败 (Hash反序列化异常): " + operation + " [key=" + key + "], 尝试回退到原始字节读取: " + e.getMessage());
                try {
                    // 尝试直接执行 HGETALL 并将字段和值作为字符串处理
                    Map<byte[], byte[]> rawMap = redisTemplate
                            .execute((RedisCallback<Map<byte[], byte[]>>) connection -> connection
                                    .hGetAll(key.getBytes(StandardCharsets.UTF_8)));

                    if (rawMap != null) {
                        Map<Object, Object> fallbackResult = new LinkedHashMap<>();
                        RedisSerializer<String> stringSerializer = StringRedisSerializer.UTF_8;
                        for (Map.Entry<byte[], byte[]> entry : rawMap.entrySet()) {
                            String fieldName;
                            String fieldValue;
                            try {
                                fieldName = stringSerializer.deserialize(entry.getKey());
                            } catch (Exception keyEx) {
                                fieldName = "<Key解码失败: "
                                        + org.apache.commons.codec.binary.Hex.encodeHexString(entry.getKey()) + ">";
                                LogUtil.warn("Hash Key解码失败 [key=" + key + ", keyBytes(hex)="
                                        + org.apache.commons.codec.binary.Hex.encodeHexString(entry.getKey()) + "]: "
                                        + keyEx.getMessage());
                            }
                            try {
                                // 尝试将值字节解码为字符串
                                fieldValue = stringSerializer.deserialize(entry.getValue());
                                // 如果反序列化成功，但结果是 null（理论上 StringRedisSerializer 不会这样，除非原始数据是特定的 null 表示），则转为空字符串
                                if (fieldValue == null)
                                    fieldValue = "";
                            } catch (Exception valueEx) {
                                // 如果 StringRedisSerializer 解码失败，尝试直接 new String
                                try {
                                    fieldValue = new String(entry.getValue(), StandardCharsets.UTF_8);
                                    LogUtil.debug("Hash Value 使用 StringRedisSerializer 解码失败，回退到 new String 成功 [key="
                                            + key + ", field=" + fieldName + "]");
                                } catch (Exception directDecodeEx) {
                                    fieldValue = "<Value解码失败: "
                                            + org.apache.commons.codec.binary.Hex.encodeHexString(entry.getValue())
                                            + ">";
                                    LogUtil.warn("Hash Value解码失败 [key=" + key + ", field=" + fieldName
                                            + ", valueBytes(hex)="
                                            + org.apache.commons.codec.binary.Hex.encodeHexString(entry.getValue())
                                            + "]: " + valueEx.getMessage());
                                }
                            }
                            fallbackResult.put(fieldName, fieldValue);
                        }
                        LogUtil.debug("Hash反序列化回退成功 [key=" + key + "], 读取到 " + fallbackResult.size() + " 个字段 (值作为字符串)");
                        try {
                            return (T) fallbackResult; // 返回 Map<String, String>
                        } catch (ClassCastException cce) {
                            LogUtil.error("Hash反序列化回退成功但类型转换失败 [key=" + key + "]: " + cce.getMessage());
                            cacheStats.recordError();
                            return null;
                        }
                    } else {
                        LogUtil.warn("Hash反序列化回退失败 [key=" + key + "]: HGETALL 返回 null (键可能不存在)");
                        cacheStats.recordMiss();
                        return null;
                    }
                } catch (Exception fallbackEx) {
                    LogUtil.error("Hash反序列化回退尝试过程中发生异常 [key=" + key + "]: " + fallbackEx.getMessage());
                    cacheStats.recordError();
                }
            }

            // --- 原有的通用异常处理逻辑 ---
            // 检查是否是Redis连接异常
            if (e.getClass().getName().contains("RedisConnectionFailure")) {
                // Redis连接异常，尝试自动重连
                LogUtil.warn("Redis连接异常: " + operation + " [key=" + key + "]: " + e.getMessage());

                // 如果注入了异常处理器，尝试重置连接
                if (redisExceptionHandler != null) {
                    redisExceptionHandler.tryResetConnection();
                }

                // 记录错误
                cacheStats.recordError();
                return null;
            }

            // 检查是否是JedisConnectionFactory销毁异常，这通常发生在应用关闭过程中
            String errorMsg = e.getMessage();
            if (errorMsg != null && errorMsg.contains("JedisConnectionFactory was destroyed")) {
                // 系统正在关闭中，使用较低级别的日志，避免产生大量错误日志
                LogUtil.debug("系统关闭中，Redis连接已销毁: " + operation + " [key=" + key + "]");
            } else if (!(e instanceof SerializationException && ("GET_VALUE".equals(operation)
                    || "ZSET_REVERSE_RANGE_WITH_SCORES".equals(operation) || "HASH_GET_ALL".equals(operation)))) {
                // 如果不是上面已经处理过的反序列化异常，则记录普通错误
                LogUtil.error("Redis操作失败: " + operation + " [key=" + key + "]: " + e.getMessage());
            }
            // 记录错误 (如果是未处理的反序列化异常，这里也会记录一次)
            cacheStats.recordError();
            return null;
        }
    }

    /**
     * 执行无返回值的Redis操作并处理异常
     *
     * @param operation 操作描述
     * @param key       操作的键
     * @param runnable  实际执行的操作
     */
    private void executeWithExceptionHandling(String operation, String key, Runnable runnable) {
        // 记录操作次数
        cacheStats.recordOperation(operation);

        try {
            runnable.run();
        } catch (IllegalArgumentException e) {
            // 参数错误类异常需要抛出，让调用者知道
            LogUtil.error("参数错误: " + operation + " [key=" + key + "]: " + e.getMessage());
            // 记录错误
            cacheStats.recordError();
            throw e; // 重新抛出参数异常
        } catch (Exception e) {
            // 检查是否是Redis连接异常
            if (e.getClass().getName().contains("RedisConnectionFailure")) {
                // Redis连接异常，尝试自动重连
                LogUtil.warn("Redis连接异常: " + operation + " [key=" + key + "]: " + e.getMessage());

                // 如果注入了异常处理器，尝试重置连接
                if (redisExceptionHandler != null) {
                    redisExceptionHandler.tryResetConnection();
                }

                // 记录错误
                cacheStats.recordError();
                return;
            }

            // 检查是否是JedisConnectionFactory销毁异常，这通常发生在应用关闭过程中
            String errorMsg = e.getMessage();
            if (errorMsg != null && errorMsg.contains("JedisConnectionFactory was destroyed")) {
                // 系统正在关闭中，使用较低级别的日志，避免产生大量错误日志
                if (com.wb.common.Var.debug) {
                    LogUtil.info("系统关闭中，Redis连接已销毁: " + operation + " [key=" + key + "]");
                }
            } else {
                // 其他操作执行异常只记录日志
                LogUtil.error("Redis操作失败: " + operation + " [key=" + key + "]: " + e.getMessage());
            }
            // 记录错误
            cacheStats.recordError();
        }
    }

    /**
     * 验证键是否有效
     *
     * @param key 要验证的键
     * @throws IllegalArgumentException 如果键为空
     */
    private void validateKey(String key) {
        if (StringUtil.isEmpty(key)) {
            throw new IllegalArgumentException("Redis键不能为空");
        }
    }

    /**
     * 验证哈希键是否有效
     *
     * @param key  主键
     * @param hKey 哈希键
     * @throws IllegalArgumentException 如果键或哈希键为空
     */
    private void validateHashKey(String key, String hKey) {
        validateKey(key);
        if (StringUtil.isEmpty(hKey)) {
            throw new IllegalArgumentException("哈希键不能为空");
        }
    }

    /**
     * 验证值是否有效
     *
     * @param value 要验证的值
     * @throws IllegalArgumentException 如果值为null
     */
    private void validateValue(Object value) {
        if (value == null) {
            throw new IllegalArgumentException("值不能为null");
        }
    }

    /**
     * 验证集合是否有效
     *
     * @param collection 要验证的集合
     * @param name       集合名称，用于错误消息
     * @throws IllegalArgumentException 如果集合为null或为空
     */
    private void validateCollection(Collection<?> collection, String name) {
        if (collection == null || collection.isEmpty()) {
            throw new IllegalArgumentException(name + "不能为空");
        }
    }

    /**
     * 验证时间参数是否有效
     *
     * @param time 时间值
     * @param unit 时间单位
     * @throws IllegalArgumentException 如果时间参数无效
     */
    private void validateTime(long time, TimeUnit unit) {
        if (time <= 0) {
            throw new IllegalArgumentException("时间值必须大于0，当前值：" + time);
        }
        if (unit == null) {
            throw new IllegalArgumentException("时间单位不能为null");
        }
    }

    /**
     * 尝试获取分布式锁。
     *
     * @param key      锁的唯一标识
     * @param waitTime 等待锁的最大时间（单位：毫秒），该时间内未获取到锁则放弃
     * @param keepTime 锁持有的时间（单位：毫秒），在此时间后锁会自动释放，避免死锁
     * @return 如果成功获取锁则返回 true，否则返回 false
     */
    private boolean internalTryLock(final String key, final long waitTime, final long keepTime) {
        // 参数检查：确保等待时间和保持时间都不为负数
        if (waitTime < 0 || keepTime < 0) {
            LogUtil.error("尝试获取锁失败，因为时间参数不合法 [key=" + key + ", waitTime=" + waitTime + ", keepTime=" + keepTime + "]");
            return false;
        }
        try {
            // 通过Redisson客户端获取对应的锁
            RLock lock = redissonClient.getLock(key);
            // 尝试获取锁，根据指定的等待时间和保持时间
            boolean acquired = lock.tryLock(waitTime, keepTime, TimeUnit.MILLISECONDS);
            // 如果未能在指定时间内获取锁，记录一条信息日志
            if (!acquired && Var.debug) {
                LogUtil.warn("未能在指定时间内获取锁 [key=" + key + "]");
            }
            return acquired;
        } catch (InterruptedException e) {
            // 处理线程被中断的情况：重新设置中断状态，并记录错误
            Thread.currentThread().interrupt(); // 重设中断状态
            LogUtil.error("尝试获取锁时线程被中断 [key=" + key + "]: " + e.getMessage());
            return false;
        } catch (Exception e) {
            // 处理其他类型的异常，并记录错误
            LogUtil.error("尝试获取锁 [" + key + "] 失败：" + e.getMessage());
            return false;
        }
    }

    /**
     * 尝试加分布式锁
     *
     * @param key      锁ID
     * @param waitTime 等待时间，XX毫秒内未拿到锁则直接返回
     * @param keepTime XX毫秒后强制释放锁
     * @return 是否成功获取锁
     */
    public boolean tryLock(final String key, final long waitTime, final long keepTime) {
        if (StringUtil.isEmpty(key)) {
            throw new IllegalArgumentException("锁键不能为空");
        }
        if (waitTime < 0) {
            throw new IllegalArgumentException("等待时间不能为负数：" + waitTime);
        }
        if (keepTime <= 0) {
            throw new IllegalArgumentException("锁持有时间必须大于0：" + keepTime);
        }

        for (int i = 0; i < RETRY_TIMES; i++) {
            if (internalTryLock(key, waitTime, keepTime)) {
                return true;
            }
            try {
                Thread.sleep(RETRY_INTERVAL);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                LogUtil.error("重试获取锁时线程中断: " + e.getMessage());
                return false;
            }
        }

        LogUtil.warn("在 " + RETRY_TIMES + " 次尝试后未能获取锁 [key=" + key + "]");
        return false;
    }

    /**
     * 解除分布式锁
     *
     * @param key 锁ID
     */
    public void unLock(String key) {
        if (StringUtil.isEmpty(key)) {
            LogUtil.warn("尝试释放空键的锁");
            return;
        }

        try {
            RLock lock = redissonClient.getLock(key);
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            } else {
                if (Var.debug)
                    LogUtil.warn("尝试释放不由当前线程持有的锁 [" + key + "]");
            }
        } catch (IllegalMonitorStateException e) {
            LogUtil.error("尝试释放不由当前线程持有的锁 [" + key + "]. 异常信息：" + e.getMessage());
        } catch (Exception e) {
            LogUtil.error("解锁 [" + key + "] 失败：" + e.getMessage());
        }
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout) {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return true=设置成功；false=设置失败
     * @throws IllegalArgumentException 如果参数无效
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit) {
        if (StringUtil.isEmpty(key)) {
            throw new IllegalArgumentException("Redis键不能为空");
        }
        if (timeout <= 0) {
            throw new IllegalArgumentException("过期时间必须大于0，当前值：" + timeout);
        }
        if (unit == null) {
            throw new IllegalArgumentException("时间单位不能为null");
        }

        // executeWithExceptionHandling 在捕获到某些异常时可能返回 null
        // expire 方法返回原始类型 boolean，需要安全处理 null 值以避免 NullPointerException
        Boolean result = executeWithExceptionHandling("EXPIRE", key, () -> {
            // 直接返回 redisTemplate.expire 的结果 (Boolean)
            return redisTemplate.expire(key, timeout, unit);
        });

        // 安全地检查 null 并返回 boolean。如果发生异常(result为null)或操作未成功(result为false)，都返回 false。
        return result != null && result;
    }

    /**
     * 根据指定的模式获取匹配的键集合。
     *
     * @param pattern 键的匹配模式，支持通配符，例如 *、？ 等。
     * @return 匹配模式的键集合，如果没有匹配的键则返回空集合。
     */
    public Set<String> getKeys(final String pattern) {
        return redisTemplate.keys(pattern);
    }

    /**
     * 删除单个key
     *
     * @param key 键
     * @throws IllegalArgumentException 如果键为空
     */
    public void delKey(final String key) {
        if (StringUtil.isEmpty(key)) {
            throw new IllegalArgumentException("Redis键不能为空");
        }

        executeWithExceptionHandling("DELETE_KEY", key, () -> redisTemplate.delete(key));
    }

    public void remove(final String key) {
        delKey(key);
    }

    /**
     * 删除多个key
     *
     * @param keys 键集合
     * @throws IllegalArgumentException 如果键集合为空或包含空键
     */
    public void delKeys(final Collection<String> keys) {
        if (keys == null || keys.isEmpty()) {
            throw new IllegalArgumentException("键集合不能为空");
        }

        // 过滤空键
        Collection<String> filteredKeys = keys.stream().filter(k -> {
            if (StringUtil.isEmpty(k)) {
                LogUtil.warn("键集合中存在空键，已过滤");
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        if (filteredKeys.isEmpty()) {
            throw new IllegalArgumentException("过滤后的键集合为空，所有键均无效");
        }

        executeWithExceptionHandling("DELETE_KEYS", String.join(",", filteredKeys),
                () -> redisTemplate.delete(filteredKeys));
    }

    /**
     * 存入普通对象
     *
     * @param key   Redis键
     * @param value 值
     * @param time  过期时间
     * @param unit  时间单位
     * @throws IllegalArgumentException 如果参数无效
     */
    public void setValue(final String key, final Object value, long time, TimeUnit unit) {
        if (StringUtil.isEmpty(key)) {
            throw new IllegalArgumentException("Redis键不能为空");
        }
        if (value == null) {
            throw new IllegalArgumentException("值不能为null");
        }
        if (time <= 0) {
            throw new IllegalArgumentException("过期时间必须大于0，当前值：" + time);
        }
        if (unit == null) {
            throw new IllegalArgumentException("时间单位不能为null");
        }

        executeWithExceptionHandling("SET_VALUE", key, () -> redisTemplate.opsForValue().set(key, value, time, unit));
    }

    /**
     * 存入普通对象，不过期
     * 
     * @param key   Redis键
     * @param value 值对象
     * @throws IllegalArgumentException 如果key或value为空
     */
    public void setValue(final String key, final Object value) {
        executeWithExceptionHandling("SET_VALUE", key, () -> redisTemplate.opsForValue().set(key, value));
    }

    /**
     * 存入普通对象
     *
     * @param key   Redis键
     * @param value 值
     */
    public void put(final String key, final Object value) {
        validateKey(key);
        validateValue(value);

        executeWithExceptionHandling("PUT", key, () -> redisTemplate.opsForValue().set(key, value, 24, TimeUnit.HOURS));
    }

    /**
     * 存储过期XX毫秒的数据
     *
     * @param key   Redis键
     * @param value 值
     * @param times 过期时间（毫秒）
     * @throws IllegalArgumentException 如果参数无效
     */
    public void put(final String key, final Object value, long times) {
        validateKey(key);
        validateValue(value);
        if (times <= 0) {
            throw new IllegalArgumentException("过期时间必须大于0，当前值：" + times);
        }

        setValue(key, value, times, TimeUnit.MILLISECONDS);
    }

    // 存储普通对象操作

    /**
     * 存入普通对象
     *
     * @param key     键
     * @param value   值
     * @param timeout 有效期，单位秒
     */
    public void setValueTimeout(final String key, final Object value, final long timeout) {
        validateKey(key);
        validateValue(value);
        if (timeout <= 0) {
            throw new IllegalArgumentException("过期时间必须大于0，当前值：" + timeout);
        }

        executeWithExceptionHandling("SET_VALUE_TIMEOUT", key,
                () -> redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS));
    }

    /**
     * 获取普通对象
     *
     * @param key 键
     * @return 对象
     * @throws IllegalArgumentException 如果键为空
     */
    public Object getValue(final String key) {
        if (StringUtil.isEmpty(key)) {
            throw new IllegalArgumentException("Redis键不能为空");
        }

        // 调用 executeWithExceptionHandling，它现在包含回退逻辑
        return executeWithExceptionHandling("GET_VALUE", key, () -> redisTemplate.opsForValue().get(key));
    }

    public Object get(final String key) {
        return getValue(key);
    }

    // 存储Hash操作

    /**
     * 确定哈希hashKey是否存在
     *
     * @param key  键
     * @param hkey hash键
     * @return true=存在；false=不存在
     * @throws IllegalArgumentException 如果键或哈希键为空
     */
    public boolean hasHashKey(final String key, String hkey) {
        if (StringUtil.isEmpty(key)) {
            throw new IllegalArgumentException("Redis键不能为空");
        }
        if (StringUtil.isEmpty(hkey)) {
            throw new IllegalArgumentException("哈希键不能为空");
        }

        return executeWithExceptionHandling("HASH_HAS_KEY", key + ":" + hkey, () -> {
            Boolean ret = redisTemplate.opsForHash().hasKey(key, hkey);
            return ret != null && ret;
        });
    }

    /**
     * 往Hash中存入数据
     *
     * @param key   Redis键
     * @param hKey  Hash键
     * @param value 值
     * @throws IllegalArgumentException 如果键或哈希键为空，或值为null
     */
    public void hashPut(final String key, final String hKey, final Object value) {
        if (StringUtil.isEmpty(key)) {
            throw new IllegalArgumentException("Redis键不能为空");
        }
        if (StringUtil.isEmpty(hKey)) {
            throw new IllegalArgumentException("哈希键不能为空");
        }
        if (value == null) {
            throw new IllegalArgumentException("哈希值不能为null");
        }

        executeWithExceptionHandling("HASH_PUT", key + ":" + hKey,
                () -> redisTemplate.opsForHash().put(key, hKey, value));
    }

    /**
     * 往Hash中存入多个数据
     *
     * @param key    Redis键
     * @param values Hash键值对
     */
    public void hashPutAll(final String key, final Map<Object, Object> values) {
        validateKey(key);
        if (values == null || values.isEmpty()) {
            throw new IllegalArgumentException("哈希值映射不能为空");
        }

        executeWithExceptionHandling("HASH_PUT_ALL", key, () -> redisTemplate.opsForHash().putAll(key, values));
    }

    /**
     * 获取Hash中的数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public Object hashGet(final String key, final String hKey) {
        if (StringUtil.isEmpty(key) || StringUtil.isEmpty(hKey)) {
            LogUtil.warn("键或哈希键为空 [key=" + key + ", hKey=" + hKey + "]");
            return null;
        }

        return executeWithExceptionHandling("HASH_GET", key + ":" + hKey,
                () -> redisTemplate.opsForHash().get(key, hKey));
    }

    /**
     * 删除Hash中的数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return 删除的数量
     */
    public Long hashDelete(final String key, final String hKey) {
        validateHashKey(key, hKey);

        return executeWithExceptionHandling("HASH_DELETE", key + ":" + hKey,
                () -> redisTemplate.opsForHash().delete(key, hKey));
    }

    /**
     * 获取Hash中的数据
     *
     * @param key Redis键
     * @return Hash对象 (Map<Object, Object>)
     */
    public Map<Object, Object> hashGetAll(final String key) {
        validateKey(key);

        // 调用包含回退逻辑的 executeWithExceptionHandling
        return executeWithExceptionHandling("HASH_GET_ALL", key, () -> redisTemplate.opsForHash().entries(key));
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key   Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public List<Object> hashMultiGet(final String key, final Collection<Object> hKeys) {
        validateKey(key);
        validateCollection(hKeys, "哈希键集合");

        return executeWithExceptionHandling("HASH_MULTI_GET", key,
                () -> redisTemplate.opsForHash().multiGet(key, hKeys));
    }

    /**
     * 删除Hash中的数据
     *
     * @param key   Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public long hashDeleteKeys(final String key, final Collection<Object> hKeys) {
        validateKey(key);
        validateCollection(hKeys, "哈希键集合");

        return executeWithExceptionHandling("HASH_DELETE_KEYS", key,
                () -> redisTemplate.opsForHash().delete(key, hKeys.toArray()));
    }

    /**
     * 从 Redis 哈希表中获取指定字段的值并解析为指定类型的列表。
     *
     * @param key   Redis 哈希表的键，不能为空。
     * @param field 哈希表中的字段名，不能为空。
     * @param obj   指定要解析的目标类型，不能为空。
     * @param <T>   要解析的目标类型的类型参数。
     * @return 如果字段存在，则返回解析后的列表；如果字段不存在，则返回空列表。
     * @throws IllegalArgumentException 如果目标类型 obj 为空时抛出此异常。
     */
    public <T> List<T> hashGetList(String key, String field, Class<T> obj) {
        validateHashKey(key, field);
        if (obj == null) {
            throw new IllegalArgumentException("目标类型不能为null");
        }

        return executeWithExceptionHandling("HASH_GET_LIST", key + ":" + field, () -> {
            Object value = redisTemplate.opsForHash().get(key, field);
            if (value != null) {
                return JSONObject.parseArray(value.toString(), obj);
            } else {
                return new ArrayList<>();
            }
        });
    }

    /**
     * 将一个列表序列化为JSON字符串后存储到Redis的哈希表中指定字段。
     *
     * @param <T>    列表中元素的类型
     * @param key    Redis哈希表的键
     * @param field  Redis哈希表的字段
     * @param values 要存储的列表值，不能为null
     * @throws IllegalArgumentException 如果列表为null时抛出此异常
     */
    public <T> void hashSetList(String key, String field, List<T> values) {
        validateHashKey(key, field);
        if (values == null) {
            throw new IllegalArgumentException("列表不能为null");
        }

        executeWithExceptionHandling("HASH_SET_LIST", key + ":" + field, () -> {
            String v = JSONObject.toJSONString(values);
            redisTemplate.opsForHash().put(key, field, v);
        });
    }

    // 存储Set相关操作

    /**
     * 往Set中存入数据
     *
     * @param key    Redis键
     * @param values 值
     * @return 存入的个数
     */
    public long setSet(final String key, final Object... values) {
        validateKey(key);
        if (values == null || values.length == 0) {
            throw new IllegalArgumentException("集合值不能为空");
        }

        return executeWithExceptionHandling("SET_SET", key, () -> {
            Long count = redisTemplate.opsForSet().add(key, values);
            return count == null ? 0 : count;
        });
    }

    /**
     * 删除Set中的数据
     *
     * @param key    Redis键
     * @param values 值
     * @return 移除的个数
     */
    public long setDel(final String key, final Object... values) {
        validateKey(key);
        if (values == null || values.length == 0) {
            return 0; // 没有要删除的值，直接返回0
        }

        return executeWithExceptionHandling("SET_DEL", key, () -> {
            Long count = redisTemplate.opsForSet().remove(key, values);
            return count == null ? 0 : count;
        });
    }

    /**
     * 获取set中的所有对象
     *
     * @param key Redis键
     * @return set集合
     */
    public Set<Object> getSetAll(final String key) {
        validateKey(key);

        return executeWithExceptionHandling("GET_SET_ALL", key, () -> {
            Set<Object> members = redisTemplate.opsForSet().members(key);
            return members != null ? members : new HashSet<>();
        });
    }

    // 存储ZSet相关操作

    /**
     * 往ZSet中存入数据
     *
     * @param key    Redis键
     * @param values 值
     * @return 存入的个数
     */
    public long zsetSet(final String key, final Set<ZSetOperations.TypedTuple<Object>> values) {
        validateKey(key);
        validateCollection(values, "有序集合值");

        return executeWithExceptionHandling("ZSET_SET", key, () -> {
            Long count = redisTemplate.opsForZSet().add(key, values);
            return count == null ? 0 : count;
        });
    }

    /**
     * 删除ZSet中的数据
     *
     * @param key    Redis键
     * @param values 值
     * @return 移除的个数
     */
    public long zsetDel(final String key, final Set<ZSetOperations.TypedTuple<Object>> values) {
        validateKey(key);
        validateCollection(values, "有序集合值");

        return executeWithExceptionHandling("ZSET_DEL", key, () -> {
            Long count = redisTemplate.opsForZSet().remove(key,
                    values.stream().map(ZSetOperations.TypedTuple::getValue).toArray());
            return count == null ? 0 : count;
        });
    }

    /**
     * 删除ZSet中的数据
     *
     * @param key   Redis键
     * @param value 值
     * @return 删除的个数
     */
    public long zsetDel(final String key, final Object value) {
        validateKey(key);
        validateValue(value);

        return executeWithExceptionHandling("ZSET_DEL", key, () -> {
            Long count = redisTemplate.opsForZSet().remove(key, value);
            return count == null ? 0 : count;
        });
    }

    /**
     * 从ZSet中获取一部分数据
     *
     * @param key   Redis键
     * @param start 开始位置
     * @param end   结束位置
     * @return 获取的数据
     */
    public Set<Object> zsetRange(final String key, long start, long end) {
        validateKey(key);

        return executeWithExceptionHandling("ZSET_RANGE", key, () -> {
            Set<Object> range = redisTemplate.opsForZSet().range(key, start, end);
            return range != null ? range : new HashSet<>();
        });
    }

    /**
     * 获取ZSet的元素数量
     *
     * @param key Redis键
     * @return 元素数量
     */
    public long zsetSize(final String key) {
        validateKey(key);

        return executeWithExceptionHandling("ZSET_SIZE", key, () -> {
            Long size = redisTemplate.opsForZSet().zCard(key);
            return size == null ? 0 : size;
        });
    }

    /**
     * 获取ZSet中指定元素的分数
     *
     * @param key   Redis键
     * @param value 元素值
     * @return 元素的分数，如果元素不存在则返回null
     */
    public Double zsetScore(final String key, final Object value) {
        validateKey(key);
        validateValue(value);

        return executeWithExceptionHandling("ZSET_SCORE", key, () -> redisTemplate.opsForZSet().score(key, value));
    }

    /**
     * 按分数范围查询ZSet中的元素
     *
     * @param key Redis键
     * @param min 最小分数
     * @param max 最大分数
     * @return 符合分数范围的元素集合
     */
    public Set<Object> zsetRangeByScore(final String key, final double min, final double max) {
        validateKey(key);

        return executeWithExceptionHandling("ZSET_RANGE_BY_SCORE", key, () -> {
            Set<Object> result = redisTemplate.opsForZSet().rangeByScore(key, min, max);
            return result != null ? result : new HashSet<>();
        });
    }

    /**
     * 带分数获取ZSet元素（逆序）
     *
     * @param key   Redis键
     * @param start 开始位置
     * @param end   结束位置
     * @return 获取的数据及分数
     */
    public Set<ZSetOperations.TypedTuple<Object>> zsetReverseRangeWithScores(final String key, long start, long end) {
        validateKey(key);

        // 注意：这里的 start 和 end 传给了 supplier，但 executeWithExceptionHandling 中的回退逻辑目前未使用它们
        // 这是一个潜在问题，如果需要精确范围的回退，需要重构
        return executeWithExceptionHandling("ZSET_REVERSE_RANGE_WITH_SCORES", key, () -> {
            Set<ZSetOperations.TypedTuple<Object>> range = redisTemplate.opsForZSet().reverseRangeWithScores(key, start,
                    end);
            return range != null ? range : new HashSet<>();
        });
    }

    // 存储List相关操作

    /**
     * 往List中存入数据
     *
     * @param key   Redis键
     * @param value 数据
     * @return 存入的个数
     * @throws IllegalArgumentException 如果键为空或值为null
     */
    public long listPush(final String key, final Object value) {
        if (StringUtil.isEmpty(key)) {
            throw new IllegalArgumentException("Redis键不能为空");
        }
        if (value == null) {
            throw new IllegalArgumentException("列表值不能为null");
        }

        return executeWithExceptionHandling("LIST_PUSH", key, () -> {
            Long count = redisTemplate.opsForList().rightPush(key, value);
            return count == null ? 0 : count;
        });
    }

    /**
     * 往List中存入多个数据
     *
     * @param key    Redis键
     * @param values 多个数据
     * @return 存入的个数
     */
    public long listPushAll(final String key, final Collection<Object> values) {
        validateKey(key);
        validateCollection(values, "列表值集合");

        return executeWithExceptionHandling("LIST_PUSH_ALL", key, () -> {
            Long count = redisTemplate.opsForList().rightPushAll(key, values);
            return count == null ? 0 : count;
        });
    }

    /**
     * 往List中存入多个数据
     *
     * @param key    Redis键
     * @param values 多个数据
     * @return 存入的个数
     */
    public long listPushAll(final String key, final Object... values) {
        validateKey(key);
        if (values == null || values.length == 0) {
            throw new IllegalArgumentException("列表值不能为空");
        }

        return executeWithExceptionHandling("LIST_PUSH_ALL", key, () -> {
            Long count = redisTemplate.opsForList().rightPushAll(key, values);
            return count == null ? 0 : count;
        });
    }

    /**
     * 从List中获取begin到end之间的元素
     *
     * @param key   Redis键
     * @param start 开始位置
     * @param end   结束位置（start=0，end=-1表示获取全部元素）
     * @return List对象
     */
    public List<Object> listGet(final String key, final int start, final int end) {
        if (StringUtil.isEmpty(key)) {
            LogUtil.warn("尝试从空键列表中获取数据");
            return new ArrayList<>();
        }

        return executeWithExceptionHandling("LIST_GET", key, () -> {
            List<Object> result = redisTemplate.opsForList().range(key, start, end);
            return result != null ? result : new ArrayList<>();
        });
    }

    /**
     * 将指定的值从左侧推入到指定键对应的列表中。
     *
     * @param key   Redis中列表的键，不能为空或无效。
     * @param value 要推入列表的值，不能为空或无效。
     */
    public void listLeftPush(String key, Object value) {
        validateKey(key);
        validateValue(value);

        executeWithExceptionHandling("LIST_LEFT_PUSH", key, () -> redisTemplate.opsForList().leftPush(key, value));
    }

    /**
     * 在列表的右侧推入一个元素。
     *
     * @param key   用于标识列表的键，不能为空且必须是一个有效的字符串。
     * @param value 要推入到列表中的元素，不能为空。
     */
    public void listRightPush(String key, Object value) {
        validateKey(key);
        validateValue(value);

        executeWithExceptionHandling("LIST_RIGHT_PUSH", key, () -> redisTemplate.opsForList().rightPush(key, value));
    }

    /**
     * 从Redis中的列表左侧弹出一个元素。
     *
     * @param key Redis列表对应的键，不能为空或null。
     * @return 弹出的列表元素，如果列表为空则返回null。
     */
    public Object listLeftPop(String key) {
        validateKey(key);

        return executeWithExceptionHandling("LIST_LEFT_POP", key, () -> redisTemplate.opsForList().leftPop(key));
    }

    /**
     * 从指定的列表的右端弹出一个元素。
     *
     * @param key 待操作的列表的键，不能为 null。
     * @return 弹出的元素，如果列表为空则返回 null。
     */
    public Object listRightPop(String key) {
        validateKey(key);

        return executeWithExceptionHandling("LIST_RIGHT_POP", key, () -> redisTemplate.opsForList().rightPop(key));
    }

    /**
     * 根据提供的键和索引获取列表中的元素。
     *
     * @param key   要查询的键，表示列表的标识符。
     * @param index 列表中元素的位置索引，正数表示从头开始计数，负数表示从尾部开始计数。
     * @return 返回指定键对应列表中指定索引位置的元素，如果不存在则返回null。
     */
    public Object getListKeyIndex(String key, int index) {
        validateKey(key);

        return executeWithExceptionHandling("GET_LIST_KEY_INDEX", key,
                () -> redisTemplate.opsForList().index(key, index));
    }

    /**
     * 获取指定键的列表长度。
     *
     * @param key 列表对应的键，不能为 null。
     * @return 返回列表的长度，如果列表不存在则返回 0。
     */
    public Long getListLength(String key) {
        validateKey(key);

        return executeWithExceptionHandling("GET_LIST_LENGTH", key, () -> {
            Long size = redisTemplate.opsForList().size(key);
            return size != null ? size : 0L;
        });
    }

    /**
     * 获取Spring RedisTemplate操作对象
     *
     * @return RedisTemplate操作对象
     */
    public RedisTemplate<String, Object> getRedisTemplate() {
        return redisTemplate;
    }

    public StringRedisTemplate getStringRedisTemplate() {
        return stringRedisTemplate;
    }

    /**
     * 模糊查询缓存键
     *
     * @param pattern 如：Key*
     * @return 匹配的Key列表
     */
    public List<String> scan(String pattern) {
        if (StringUtil.isEmpty(pattern)) {
            throw new IllegalArgumentException("查询模式不能为空");
        }

        return executeWithExceptionHandling("SCAN", pattern, () -> {
            ScanOptions options = ScanOptions.scanOptions().match(pattern).build();
            Cursor<byte[]> cursor = stringRedisTemplate.getConnectionFactory().getConnection().scan(options);
            List<String> keys = new ArrayList<>();
            while (cursor.hasNext()) {
                keys.add(new String(cursor.next()));
            }
            try {
                cursor.close();
            } catch (Exception e) {
                LogUtil.warn("关闭游标失败: " + e.getMessage());
            }
            return keys;
        });
    }

    /**
     * 模糊查询缓存键
     *
     * @param pattern 如：Key*
     * @return 匹配的Key列表
     */
    public List<String> scanForKeys(String pattern) {
        if (StringUtil.isEmpty(pattern)) {
            throw new IllegalArgumentException("查询模式不能为空");
        }

        return executeWithExceptionHandling("SCAN_FOR_KEYS", pattern, () -> {
            // 使用 connection.scan() 而不是 opsForSet().scan()
            ScanOptions options = ScanOptions.scanOptions().match(pattern).build();
            Cursor<byte[]> cursor = stringRedisTemplate.getConnectionFactory().getConnection().scan(options);
            List<String> keys = new ArrayList<>();
            while (cursor.hasNext()) {
                keys.add(new String(cursor.next(), StandardCharsets.UTF_8)); // 确保使用UTF-8解码
            }
            try {
                cursor.close();
            } catch (Exception e) {
                LogUtil.warn("关闭游标失败: " + e.getMessage());
            }
            return keys;
        });
    }

    /**
     * 获取RedissonClient操作对象
     *
     * @return RedissonClient操作对象
     */
    public RedissonClient getRedissonClient() {
        return redissonClient;
    }

    /**
     * 根据指定的序列键生成下一个序列号。支持每日重置功能，确保序列号在特定条件下从头开始。
     *
     * @param sequenceKey 序列键，用于标识序列号的类别或类型。
     * @param resetDaily  如果为 true，则序列号将在新的一天开始时重置。
     * @return 返回格式化后的下一个序列号（4位数字字符串）。
     * @throws RuntimeException 当生成序列号失败时抛出异常。
     */
    public String getNextSequence(final String sequenceKey, final boolean resetDaily) {
        // 为每个序列使用独立的日期键和锁键
        final String dateKey = sequenceKey + ":date";
        final RLock lock = redissonClient.getLock(sequenceKey + ":lock");

        try {
            // 获取分布式锁，确保操作的原子性
            lock.lock();

            final RAtomicLong sequence = redissonClient.getAtomicLong(sequenceKey);
            final String currentDate = LocalDate.now().format(DateTimeFormatter.ISO_DATE);
            final RBucket<String> dateBucket = redissonClient.getBucket(dateKey);
            final String lastDate = dateBucket.get();

            // 如果需要每日重置，并且当前日期与上次记录不一致，则重置序列
            if (resetDaily && (lastDate == null || !lastDate.equals(currentDate))) {
                sequence.set(0);
                dateBucket.set(currentDate);
                // 设置日期 Bucket 的过期时间为2天，避免长期存在
                dateBucket.expire(2, TimeUnit.DAYS);
            }

            long nextSeq = sequence.incrementAndGet();
            // 防止序列超出范围或出现负数，重置序列
            if (nextSeq > MAX_SEQUENCE || nextSeq < 0) {
                sequence.set(0);
                nextSeq = 0;
            }

            return String.format("%04d", nextSeq);
        } catch (Exception e) {
            throw new RuntimeException("生成序列号失败", e);
        } finally {
            // 仅当当前线程持有锁时才释放，防止非法监控状态异常
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 执行管道操作，批量执行Redis命令以提高性能
     *
     * @param callback 管道回调函数
     * @return 执行结果列表
     */
    public List<Object> executePipeline(Consumer<PipelineCallback> callback) {
        // 创建回调对象
        DefaultPipelineCallback pipelineCallback = new DefaultPipelineCallback(redisTemplate);

        // 调用回调函数，收集所有操作
        callback.accept(pipelineCallback);

        // 获取所有操作
        List<Runnable> operations = pipelineCallback.getOperations();
        if (operations.isEmpty()) {
            return Collections.emptyList();
        }

        // 使用管道批量执行操作
        return redisTemplate.executePipelined(new SessionCallback<Object>() {
            @Override
            @SuppressWarnings("unchecked")
            public Object execute(RedisOperations operations) throws DataAccessException {
                // 执行所有收集的操作
                for (Runnable operation : pipelineCallback.getOperations()) {
                    operation.run();
                }
                return null;
            }
        });
    }

    /**
     * 批量删除键
     *
     * @param keys 键集合
     * @return 删除的键数量
     */
    public long multiDelete(final Collection<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return 0;
        }

        // 过滤空键
        List<String> validKeys = keys.stream().filter(key -> !StringUtil.isEmpty(key)).collect(Collectors.toList());

        if (validKeys.isEmpty()) {
            return 0;
        }

        int batchSize = 100; // 每批处理的键数量
        if (validKeys.size() <= batchSize) {
            // 数量不多时，直接删除
            return executeWithExceptionHandling("MULTI_DELETE", String.join(",", validKeys), () -> {
                Long count = redisTemplate.delete(validKeys);
                return count != null ? count : 0;
            });
        } else {
            // 数量较多时，分批处理
            List<List<String>> batches = new ArrayList<>();
            List<String> currentBatch = new ArrayList<>();
            int count = 0;
            long totalDeleted = 0;

            for (String key : validKeys) {
                currentBatch.add(key);
                count++;

                if (count % batchSize == 0) {
                    batches.add(currentBatch);
                    currentBatch = new ArrayList<>();
                }
            }

            // 添加最后一批（如果有）
            if (!currentBatch.isEmpty()) {
                batches.add(currentBatch);
            }

            // 分批执行删除
            for (List<String> batch : batches) {
                Long deleted = executeWithExceptionHandling("MULTI_DELETE_BATCH", String.join(",", batch), () -> {
                    Long count1 = redisTemplate.delete(batch);
                    return count1 != null ? count1 : 0;
                });

                if (deleted != null) {
                    totalDeleted += deleted;
                }
            }

            return totalDeleted;
        }
    }

    /**
     * 批量设置键值对
     *
     * @param keyValueMap 键值对映射
     * @param timeout     过期时间
     * @param unit        时间单位
     */
    public void multiSet(final Map<String, Object> keyValueMap, long timeout, TimeUnit unit) {
        if (keyValueMap == null || keyValueMap.isEmpty()) {
            return;
        }
        validateTime(timeout, unit);

        // 检查所有键和值
        keyValueMap.forEach((key, value) -> {
            validateKey(key);
            validateValue(value);
        });

        // 对于集群模式，检查是否需要使用Hash Tag确保键分布在同一槽位
        if (keyValueMap.size() > 1) {
            // 记录可能在集群中不兼容的操作
            RedisCacheClusterCompatibility.logIncompatibleCommand("MSET", "多键");

            // 考虑转换为使用Pipeline的单键SET操作
        }

        int batchSize = 100; // 每批处理的键值对数量
        if (keyValueMap.size() <= batchSize) {
            // 数量不多时，直接使用Pipeline
            executePipeline(pipeline -> {
                keyValueMap.forEach((key, value) -> pipeline.set(key, value, timeout, unit));
            });
        } else {
            // 数量较多时，分批处理
            List<Map<String, Object>> batches = new ArrayList<>();
            Map<String, Object> currentBatch = new HashMap<>();
            int count = 0;

            for (Map.Entry<String, Object> entry : keyValueMap.entrySet()) {
                currentBatch.put(entry.getKey(), entry.getValue());
                count++;

                if (count % batchSize == 0) {
                    batches.add(currentBatch);
                    currentBatch = new HashMap<>();
                }
            }

            // 添加最后一批（如果有）
            if (!currentBatch.isEmpty()) {
                batches.add(currentBatch);
            }

            // 分批执行
            for (Map<String, Object> batch : batches) {
                executePipeline(pipeline -> {
                    batch.forEach((key, value) -> pipeline.set(key, value, timeout, unit));
                });
            }
        }
    }

    // 缓存的布隆过滤器实例，按名称存储
    private final Map<String, RedisBloomFilter> bloomFilters = new ConcurrentHashMap<>();

    /**
     * 创建一个布隆过滤器
     *
     * @param name             过滤器名称
     * @param expectedElements 预期元素数量
     * @param fpp              可接受的误判率（0-1之间，越小越精确但空间占用越大）
     * @return 布隆过滤器实例
     */
    public RedisBloomFilter createBloomFilter(String name, long expectedElements, double fpp) {
        RedisBloomFilter filter = new RedisBloomFilter(name, expectedElements, fpp, redisTemplate, stringRedisTemplate);
        bloomFilters.put(name, filter);
        return filter;
    }

    /**
     * 获取布隆过滤器
     *
     * @param name 过滤器名称
     * @return 布隆过滤器实例，如果不存在则返回null
     */
    public RedisBloomFilter getBloomFilter(String name) {
        return bloomFilters.get(name);
    }

    /**
     * 使用布隆过滤器防护的获取方法 首先检查布隆过滤器，如果布隆过滤器显示元素一定不存在，则直接返回null 否则再查询Redis缓存
     *
     * @param key             缓存键
     * @param bloomFilterName 要使用的布隆过滤器名称
     * @return 值，如果不存在则返回null
     */
    public Object getWithBloomFilter(String key, String bloomFilterName) {
        validateKey(key);
        if (StringUtil.isEmpty(bloomFilterName)) {
            throw new IllegalArgumentException("布隆过滤器名称不能为空");
        }

        RedisBloomFilter filter = getBloomFilter(bloomFilterName);
        if (filter == null) {
            LogUtil.warn("指定的布隆过滤器不存在 [name=" + bloomFilterName + "]");
            return getValue(key);
        }

        // 首先判断布隆过滤器，如果布隆过滤器显示元素一定不存在，则无需查询Redis
        if (!filter.mightContain(key)) {
            // 记录布隆过滤器拦截
            cacheStats.recordOperation("BF_INTERCEPT");
            return null;
        }

        // 布隆过滤器显示元素可能存在，查询Redis
        return getValue(key);
    }

    /**
     * 使用布隆过滤器和加载器的获取方法 结合了布隆过滤器防护和缓存加载机制
     *
     * @param key             缓存键
     * @param bloomFilterName 要使用的布隆过滤器名称
     * @param loadFunction    加载函数
     * @param expireTime      过期时间
     * @param unit            时间单位
     * @param <T>             返回值类型
     * @return 缓存值或加载函数返回的值
     */
    public <T> T getWithBloomFilter(String key, String bloomFilterName, Function<String, T> loadFunction,
            long expireTime, TimeUnit unit) {
        validateKey(key);
        if (StringUtil.isEmpty(bloomFilterName)) {
            throw new IllegalArgumentException("布隆过滤器名称不能为空");
        }
        if (loadFunction == null) {
            throw new IllegalArgumentException("加载函数不能为null");
        }
        validateTime(expireTime, unit);

        RedisBloomFilter filter = getBloomFilter(bloomFilterName);
        if (filter == null) {
            LogUtil.warn("指定的布隆过滤器不存在 [name=" + bloomFilterName + "]");
            // 没有布隆过滤器，使用普通的getWithLoader
            return getWithLoader(key, loadFunction, expireTime, unit);
        }

        // 尝试从本地缓存获取
        Object localValue = getLocalCache(key);
        if (localValue != null) {
            if (localValue == NULL_VALUE) {
                return null;
            }
            return (T) localValue;
        }

        // 首先判断布隆过滤器，如果布隆过滤器显示元素一定不存在，则无需查询Redis
        if (!filter.mightContain(key)) {
            // 记录布隆过滤器拦截
            cacheStats.recordOperation("BF_INTERCEPT");
            return null;
        }

        // 从Redis获取
        Object value = getValue(key);

        if (value != null) {
            // 更新本地缓存
            putLocalCache(key, value, LOCAL_CACHE_EXPIRE);
            return (T) value;
        }

        // 缓存未命中，调用加载函数
        try {
            // 记录加载次数
            cacheStats.recordLoad();

            T loadedValue = loadFunction.apply(key);

            // 将加载的值存入Redis和本地缓存
            if (loadedValue != null) {
                // 使用随机化的过期时间防止缓存雪崩
                long randomizedExpireTime = getRandomizedExpireTime(expireTime, unit);
                setValue(key, loadedValue, randomizedExpireTime, TimeUnit.SECONDS);
                putLocalCache(key, loadedValue, LOCAL_CACHE_EXPIRE);

                // 添加到布隆过滤器
                filter.add(key);
            } else {
                // 存储空值标记，防止缓存穿透，空值过期时间较短
                setValue(key, NULL_VALUE, 60, TimeUnit.SECONDS);
                putLocalCache(key, NULL_VALUE, 5);
            }

            return loadedValue;
        } catch (Exception e) {
            LogUtil.error("加载缓存值失败 [key=" + key + "]: " + e.getMessage());
            // 记录错误
            cacheStats.recordError();
            return null;
        }
    }

    // 缓存的计数布隆过滤器实例，按名称存储
    private final Map<String, CountingBloomFilter> countingBloomFilters = new ConcurrentHashMap<>();

    /**
     * 创建一个计数布隆过滤器
     *
     * @param name             过滤器名称
     * @param expectedElements 预期元素数量
     * @param fpp              可接受的误判率（0-1之间，越小越精确但空间占用越大）
     * @param maxCount         每个计数器的最大值（默认为15）
     * @return 计数布隆过滤器实例
     */
    public CountingBloomFilter createCountingBloomFilter(String name, long expectedElements, double fpp, int maxCount) {
        if (StringUtil.isEmpty(name)) {
            throw new IllegalArgumentException("过滤器名称不能为空");
        }
        if (countingBloomFilters.containsKey(name)) {
            LogUtil.info("计数布隆过滤器已存在，返回现有实例 [name=" + name + "]");
            return countingBloomFilters.get(name);
        }

        CountingBloomFilter filter = new CountingBloomFilter(name, expectedElements, fpp, maxCount, redisTemplate,
                stringRedisTemplate);
        countingBloomFilters.put(name, filter);
        return filter;
    }

    /**
     * 创建一个计数布隆过滤器（使用默认的最大计数值15）
     *
     * @param name             过滤器名称
     * @param expectedElements 预期元素数量
     * @param fpp              可接受的误判率
     * @return 计数布隆过滤器实例
     */
    public CountingBloomFilter createCountingBloomFilter(String name, long expectedElements, double fpp) {
        return createCountingBloomFilter(name, expectedElements, fpp, 15);
    }

    /**
     * 获取计数布隆过滤器
     *
     * @param name 过滤器名称
     * @return 计数布隆过滤器实例，如果不存在则返回null
     */
    public CountingBloomFilter getCountingBloomFilter(String name) {
        return countingBloomFilters.get(name);
    }

    /**
     * 使用计数布隆过滤器防护的获取方法 首先检查计数布隆过滤器，如果过滤器显示元素一定不存在，则直接返回null 否则再查询Redis缓存
     *
     * @param key                     缓存键
     * @param countingBloomFilterName 要使用的计数布隆过滤器名称
     * @return 值，如果不存在则返回null
     */
    public Object getWithCountingBloomFilter(String key, String countingBloomFilterName) {
        validateKey(key);
        if (StringUtil.isEmpty(countingBloomFilterName)) {
            throw new IllegalArgumentException("计数布隆过滤器名称不能为空");
        }

        CountingBloomFilter filter = getCountingBloomFilter(countingBloomFilterName);
        if (filter == null) {
            LogUtil.warn("指定的计数布隆过滤器不存在 [name=" + countingBloomFilterName + "]");
            return getValue(key);
        }

        // 首先判断布隆过滤器，如果布隆过滤器显示元素一定不存在，则无需查询Redis
        if (!filter.mightContain(key)) {
            // 记录布隆过滤器拦截
            cacheStats.recordOperation("CBF_INTERCEPT");
            return null;
        }

        // 布隆过滤器显示元素可能存在，查询Redis
        return getValue(key);
    }

    /**
     * 使用计数布隆过滤器和加载器的获取方法 结合了计数布隆过滤器防护和缓存加载机制
     *
     * @param key                     缓存键
     * @param countingBloomFilterName 要使用的计数布隆过滤器名称
     * @param loadFunction            加载函数
     * @param expireTime              过期时间
     * @param unit                    时间单位
     * @param <T>                     返回值类型
     * @return 缓存值或加载函数返回的值
     */
    public <T> T getWithCountingBloomFilter(String key, String countingBloomFilterName,
            Function<String, T> loadFunction, long expireTime, TimeUnit unit) {
        validateKey(key);
        if (StringUtil.isEmpty(countingBloomFilterName)) {
            throw new IllegalArgumentException("计数布隆过滤器名称不能为空");
        }
        if (loadFunction == null) {
            throw new IllegalArgumentException("加载函数不能为null");
        }
        validateTime(expireTime, unit);

        CountingBloomFilter filter = getCountingBloomFilter(countingBloomFilterName);
        if (filter == null) {
            LogUtil.warn("指定的计数布隆过滤器不存在 [name=" + countingBloomFilterName + "]");
            // 没有计数布隆过滤器，使用普通的getWithLoader
            return getWithLoader(key, loadFunction, expireTime, unit);
        }

        // 尝试从本地缓存获取
        Object localValue = getLocalCache(key);
        if (localValue != null) {
            if (localValue == NULL_VALUE) {
                return null;
            }
            return (T) localValue;
        }

        // 首先判断计数布隆过滤器，如果计数布隆过滤器显示元素一定不存在，则无需查询Redis
        if (!filter.mightContain(key)) {
            // 记录布隆过滤器拦截
            cacheStats.recordOperation("CBF_INTERCEPT");
            return null;
        }

        // 从Redis获取
        Object value = getValue(key);

        if (value != null) {
            // 更新本地缓存
            putLocalCache(key, value, LOCAL_CACHE_EXPIRE);
            return (T) value;
        }

        // 缓存未命中，调用加载函数
        try {
            // 记录加载次数
            cacheStats.recordLoad();

            T loadedValue = loadFunction.apply(key);

            // 将加载的值存入Redis和本地缓存
            if (loadedValue != null) {
                // 使用随机化的过期时间防止缓存雪崩
                long randomizedExpireTime = getRandomizedExpireTime(expireTime, unit);
                setValue(key, loadedValue, randomizedExpireTime, TimeUnit.SECONDS);
                putLocalCache(key, loadedValue, LOCAL_CACHE_EXPIRE);

                // 添加到计数布隆过滤器
                filter.add(key);
            } else {
                // 存储空值标记，防止缓存穿透，空值过期时间较短
                setValue(key, NULL_VALUE, 60, TimeUnit.SECONDS);
                putLocalCache(key, NULL_VALUE, 5);
            }

            return loadedValue;
        } catch (Exception e) {
            LogUtil.error("加载缓存值失败 [key=" + key + "]: " + e.getMessage());
            // 记录错误
            cacheStats.recordError();
            return null;
        }
    }

    /**
     * 删除键并从计数布隆过滤器中移除
     *
     * @param key                     要删除的键
     * @param countingBloomFilterName 计数布隆过滤器名称
     * @return 是否成功删除
     */
    public boolean deleteWithCountingBloomFilter(String key, String countingBloomFilterName) {
        validateKey(key);
        if (StringUtil.isEmpty(countingBloomFilterName)) {
            throw new IllegalArgumentException("计数布隆过滤器名称不能为空");
        }

        CountingBloomFilter filter = getCountingBloomFilter(countingBloomFilterName);
        if (filter == null) {
            LogUtil.warn("指定的计数布隆过滤器不存在 [name=" + countingBloomFilterName + "]");
            delKey(key);
            return true;
        }

        // 从本地缓存中删除
        localCache.remove(key);

        // 从Redis中删除
        delKey(key);

        // 从计数布隆过滤器中删除
        return filter.remove(key);
    }

    /**
     * 批量删除键并从计数布隆过滤器中移除
     *
     * @param keys                    要删除的键集合
     * @param countingBloomFilterName 计数布隆过滤器名称
     * @return 成功删除的键数量
     */
    public int multiDeleteWithCountingBloomFilter(Collection<String> keys, String countingBloomFilterName) {
        if (keys == null || keys.isEmpty()) {
            return 0;
        }

        if (StringUtil.isEmpty(countingBloomFilterName)) {
            throw new IllegalArgumentException("计数布隆过滤器名称不能为空");
        }

        CountingBloomFilter filter = getCountingBloomFilter(countingBloomFilterName);
        if (filter == null) {
            LogUtil.warn("指定的计数布隆过滤器不存在 [name=" + countingBloomFilterName + "]");
            int count = (int) multiDelete(keys);
            return count;
        }

        int successCount = 0;

        // 过滤掉空键
        List<String> validKeys = keys.stream().filter(key -> !StringUtil.isEmpty(key)).collect(Collectors.toList());

        if (validKeys.isEmpty()) {
            return 0;
        }

        // 从本地缓存中删除
        for (String key : validKeys) {
            localCache.remove(key);
        }

        // 从Redis中批量删除
        multiDelete(validKeys);

        // 从计数布隆过滤器中批量删除
        successCount = filter.removeAll(validKeys);

        return successCount;
    }

    /**
     * 发布消息到指定通道
     *
     * @param channel 通道名称
     * @param message 消息内容
     * @return 接收消息的客户端数量
     */
    public Long publish(String channel, String message) {
        try {
            if (StringUtil.isEmpty(channel) || message == null) {
                return 0L;
            }
            // RedisTemplate.convertAndSend返回的是接收消息的客户端数量
            redisTemplate.convertAndSend(channel, message);
            return 1L; // 返回成功发送的消息数量
        } catch (Exception e) {
            LogUtil.error("Redis发布消息异常: " + e.getMessage() + ", 通道: " + channel, e);
            return 0L;
        }
    }

    /**
     * 发布消息到指定通道
     *
     * @param channel 通道名称
     * @param message 消息内容
     * @return 接收消息的客户端数量
     */
    public Long publish(String channel, Object message) {
        try {
            if (StringUtil.isEmpty(channel) || message == null) {
                return 0L;
            }
            // RedisTemplate.convertAndSend返回的是接收消息的客户端数量
            redisTemplate.convertAndSend(channel, message);
            return 1L; // 返回成功发送的消息数量
        } catch (Exception e) {
            LogUtil.error("Redis发布消息异常: " + e.getMessage() + ", 通道: " + channel, e);
            return 0L;
        }
    }

    /**
     * 检查键是否存在
     *
     * @param key Redis键
     * @return 是否存在
     */
    public boolean hasKey(final String key) {
        validateKey(key);

        return executeWithExceptionHandling("HAS_KEY", key, () -> {
            Boolean exists = redisTemplate.hasKey(key);
            return exists != null && exists;
        });
    }

    /**
     * 订阅Redis消息通道
     *
     * @param channel         通道名称
     * @param messageListener 消息监听器回调
     */
    public void subscribe(String channel, Consumer<String> messageListener) {
        try {
            if (StringUtil.isEmpty(channel) || messageListener == null) {
                return;
            }

            stringRedisTemplate.getConnectionFactory().getConnection().subscribe((message, pattern) -> {
                String msg = new String(message.getBody(), java.nio.charset.StandardCharsets.UTF_8);
                messageListener.accept(msg);
            }, channel.getBytes(java.nio.charset.StandardCharsets.UTF_8));

            LogUtil.info("成功订阅Redis通道: " + channel);
        } catch (Exception e) {
            LogUtil.error("订阅Redis通道异常: " + e.getMessage() + ", 通道: " + channel, e);
        }
    }

    @Autowired(required = false)
    private RedisExceptionHandler redisExceptionHandler;

    /**
     * 设置Redis异常处理器
     * 
     * @param handler 异常处理器
     */
    public void setRedisExceptionHandler(RedisExceptionHandler handler) {
        this.redisExceptionHandler = handler;
        LogUtil.info("设置RedisCache的异常处理器");
    }

    /**
     * 处理缓存失效的键
     *
     * @param key 失效的键
     */
    public void handleCacheInvalidation(String key) {
        if (!StringUtil.isEmpty(key)) {
            // 从本地缓存中删除
            localCache.remove(key);
            if (Var.debug) {
                LogUtil.info("通过订阅消息删除本地缓存: " + key);
            }
        }
    }

    /**
     * 处理清空所有缓存消息 供RedisMessageListener调用
     */
    public void handleClearAllCache() {
        // 清空本地缓存
        localCache.clear();
        if (Var.debug) {
            LogUtil.info("通过订阅消息清空所有本地缓存");
        }
    }

    /**
     * 处理缓存更新消息 供RedisMessageListener调用
     * 
     * @param key       缓存键
     * @param valueJson 缓存值的JSON字符串
     */
    public void handleCacheUpdate(String key, String valueJson) {
        if (!StringUtil.isEmpty(key) && !StringUtil.isEmpty(valueJson)) {
            try {
                // 尝试将JSON字符串解析为对象
                Object value;
                try {
                    value = JSONObject.parse(valueJson);
                } catch (Exception e) {
                    // 如果解析失败，直接使用字符串值
                    value = valueJson;
                }

                // 更新本地缓存
                putLocalCache(key, value, LOCAL_CACHE_EXPIRE);

                if (Var.debug) {
                    LogUtil.info("通过订阅消息更新本地缓存: " + key);
                }
            } catch (Exception e) {
                LogUtil.warn("处理缓存更新消息失败: " + e.getMessage());
            }
        }
    }

    /**
     * 判断当前是否运行在Redis集群模式下
     * 
     * @return 如果当前运行在Redis集群模式下则返回true
     */
    public boolean isClusterMode() {
        return RedisConfiguration.isClusterModeEnabled();
    }

    /**
     * 切换集群兼容模式
     * 
     * @param enable 是否启用集群模式
     */
    public void switchClusterMode(boolean enable) {
        try {
            if (redisTemplate == null) {
                LogUtil.warn("无法切换集群模式，RedisTemplate未初始化");
                return;
            }

            if (enable) {
                LogUtil.info("通过RedisCache切换为Redis集群模式");
                RedisConfiguration.enableClusterMode(true, redisTemplate);
            } else {
                LogUtil.info("通过RedisCache切换为Redis标准模式");
                RedisConfiguration.disableClusterMode(true, redisTemplate);
            }
        } catch (Exception e) {
            LogUtil.error("切换Redis集群模式失败: " + e.getMessage(), e);
        }
    }

    /**
     * 手动发布集群模式状态变更消息 用于在集群节点间同步集群模式设置
     * 
     * @param action    操作：enable或disable
     * @param serverIds 要通知的服务器ID列表，为空表示通知所有服务器
     */
    public void publishClusterModeChangeMessage(String action, String... serverIds) {
        try {
            com.alibaba.fastjson.JSONObject message = new com.alibaba.fastjson.JSONObject();
            message.put("action", action);
            message.put("timestamp", System.currentTimeMillis());
            message.put("server", SysUtil.getServerId());

            if (serverIds != null && serverIds.length > 0) {
                com.alibaba.fastjson.JSONArray targets = new com.alibaba.fastjson.JSONArray();
                for (String serverId : serverIds) {
                    targets.add(serverId);
                }
                message.put("targets", targets);
            }

            publish(RedisConfiguration.CLUSTER_MODE_CHANNEL, message);
            LogUtil.info("已手动发布集群模式" + ("enable".equals(action) ? "启用" : "禁用") + "消息");
        } catch (Exception e) {
            LogUtil.error("发布集群模式消息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检测当前Redis服务是否支持集群模式
     * 
     * @return 当前Redis服务是否为集群模式
     */
    public boolean detectClusterMode() {
        try {
            if (redisTemplate != null && redisTemplate.getConnectionFactory() != null) {
                String info = redisTemplate.getRequiredConnectionFactory().getConnection().info().toString();
                return info.contains("cluster_enabled:1");
            }
        } catch (Exception e) {
            LogUtil.warn("检测Redis集群模式失败: " + e.getMessage());
        }
        return false;
    }

    /**
     * 获取当前Redis集群模式状态 先从Redis存储中获取，如失败则返回本地状态
     * 
     * @return 集群模式状态
     */
    public boolean getClusterModeStatus() {
        try {
            if (redisTemplate != null) {
                boolean storedStatus = RedisConfiguration.getClusterModeStatusFromRedis(redisTemplate);
                return storedStatus;
            }
        } catch (Exception e) {
            LogUtil.warn("获取Redis集群模式状态失败: " + e.getMessage());
        }
        return RedisConfiguration.isClusterModeEnabled();
    }
}