.accept_icon {
  background-image: url(../images/accept.png) !important;
}

.action_icon {
  background-image: url(../images/action.png) !important;
}

.add_icon {
  background-image: url(../images/add.png) !important;
}

.aim_icon {
  background-image: url(../images/aim.png) !important;
}

.ajax_icon {
  background-image: url(../images/ajax.png) !important;
}

.align_icon {
  background-image: url(../images/align.png) !important;
}

.anonymous_icon {
  background-image: url(../images/anonymous.png) !important;
}

.application_icon {
  background-image: url(../images/application.png) !important;
}

.application_add_icon {
  background-image: url(../images/application_add.png) !important;
}

.application_delete_icon {
  background-image: url(../images/application_delete.png) !important;
}

.arrow_branch_icon {
  background-image: url(../images/arrow_branch.png) !important;
}

.arrow_divide_icon {
  background-image: url(../images/arrow_divide.png) !important;
}

.arrow_join_icon {
  background-image: url(../images/arrow_join.png) !important;
}

.attach_icon {
  background-image: url(../images/attach.png) !important;
}

.axis_icon {
  background-image: url(../images/axis.png) !important;
}

.backup_icon {
  background-image: url(../images/backup.png) !important;
}

.backup_add_icon {
  background-image: url(../images/backup_add.png) !important;
}

.backup_delete_icon {
  background-image: url(../images/backup_delete.png) !important;
}

.backup_edit_icon {
  background-image: url(../images/backup_edit.png) !important;
}

.backup_start_icon {
  background-image: url(../images/backup_start.png) !important;
}

.backup_stop_icon {
  background-image: url(../images/backup_stop.png) !important;
}

.bold_icon {
  background-image: url(../images/bold.png) !important;
}

.book_icon {
  background-image: url(../images/book.png) !important;
}

.bottom_icon {
  background-image: url(../images/bottom.png) !important;
}

.bug_icon {
  background-image: url(../images/bug.png) !important;
}

.business_icon {
  background-image: url(../images/business.png) !important;
}

.button_icon {
  background-image: url(../images/button.png) !important;
}

.calc_icon {
  background-image: url(../images/calc.png) !important;
}

.calendar_icon {
  background-image: url(../images/calendar.png) !important;
}

.cancel_icon {
  background-image: url(../images/cancel.png) !important;
}

.chart_icon {
  background-image: url(../images/chart.png) !important;
}

.check_icon {
  background-image: url(../images/check.png) !important;
}

.check_group_icon {
  background-image: url(../images/check_group.png) !important;
}

.check_in_icon {
  background-image: url(../images/check_in.png) !important;
}

.check_off_icon {
  background-image: url(../images/check_off.png) !important;
}

.check_on_icon {
  background-image: url(../images/check_on.png) !important;
}

.check_out_icon {
  background-image: url(../images/check_out.png) !important;
}

.chrome_icon {
  background-image: url(../images/chrome.png) !important;
}

.clock_icon {
  background-image: url(../images/clock.png) !important;
}

.close_icon {
  background-image: url(../images/close.png) !important;
}

.color_icon {
  background-image: url(../images/color.png) !important;
}

.column_icon {
  background-image: url(../images/column.png) !important;
}

.columns_icon {
  background-image: url(../images/columns.png) !important;
}

.combobox_icon {
  background-image: url(../images/combobox.png) !important;
}

.comment_icon {
  background-image: url(../images/comment.png) !important;
}

.computer_icon {
  background-image: url(../images/computer.png) !important;
}

.connect_icon {
  background-image: url(../images/connect.png) !important;
}

.console_icon {
  background-image: url(../images/console.png) !important;
}

.container_icon {
  background-image: url(../images/container.png) !important;
}

.copy_icon {
  background-image: url(../images/copy.png) !important;
}

.cube_icon {
  background-image: url(../images/cube.png) !important;
}

.cut_icon {
  background-image: url(../images/cut.png) !important;
}

.data_icon {
  background-image: url(../images/data.png) !important;
}

.dataview_icon {
  background-image: url(../images/dataview.png) !important;
}

.date_icon {
  background-image: url(../images/date.png) !important;
}

.datetime_icon {
  background-image: url(../images/datetime.png) !important;
}

.db_icon {
  background-image: url(../images/db.png) !important;
}

.db_add_icon {
  background-image: url(../images/db_add.png) !important;
}

.db_delete_icon {
  background-image: url(../images/db_delete.png) !important;
}

.db_edit_icon {
  background-image: url(../images/db_edit.png) !important;
}

.db_export_icon {
  background-image: url(../images/db_export.png) !important;
}

.db_field_icon {
  background-image: url(../images/db_field.png) !important;
}

.db_field_add_icon {
  background-image: url(../images/db_field_add.png) !important;
}

.db_field_delete_icon {
  background-image: url(../images/db_field_delete.png) !important;
}

.db_field_edit_icon {
  background-image: url(../images/db_field_edit.png) !important;
}

.db_form_icon {
  background-image: url(../images/db_form.png) !important;
}

.db_form_add_icon {
  background-image: url(../images/db_form_add.png) !important;
}

.db_form_delete_icon {
  background-image: url(../images/db_form_delete.png) !important;
}

.db_form_edit_icon {
  background-image: url(../images/db_form_edit.png) !important;
}

.db_import_icon {
  background-image: url(../images/db_import.png) !important;
}

.db_start_icon {
  background-image: url(../images/db_start.png) !important;
}

.db_stop_icon {
  background-image: url(../images/db_stop.png) !important;
}

.dbe_icon {
  background-image: url(../images/dbe.png) !important;
}

.delete_icon {
  background-image: url(../images/delete.png) !important;
}

.desktop_icon {
  background-image: url(../images/desktop.png) !important;
}

.develop_icon {
  background-image: url(../images/develop.png) !important;
}

.disconnect_icon {
  background-image: url(../images/disconnect.png) !important;
}

.down_icon {
  background-image: url(../images/down.png) !important;
}

.dp_icon {
  background-image: url(../images/dp.png) !important;
}

.edge_icon {
  background-image: url(../images/edge.png) !important;
}

.edit_icon {
  background-image: url(../images/edit.png) !important;
}

.eject_icon {
  background-image: url(../images/eject.png) !important;
}

.error_icon {
  background-image: url(../images/error.png) !important;
}

.execute_icon {
  background-image: url(../images/execute.png) !important;
}

.explorer_icon {
  background-image: url(../images/explorer.png) !important;
}

.export_icon {
  background-image: url(../images/export.png) !important;
}

.fieldset_icon {
  background-image: url(../images/fieldset.png) !important;
}

.field_label_icon {
  background-image: url(../images/field_label.png) !important;
}

.file_add_icon {
  background-image: url(../images/file_add.png) !important;
}

.file_css_icon {
  background-image: url(../images/file_css.png) !important;
}

.file_default_icon {
  background-image: url(../images/file_default.png) !important;
}

.file_delete_icon {
  background-image: url(../images/file_delete.png) !important;
}

.file_doc_icon {
  background-image: url(../images/file_doc.png) !important;
}

.file_edit_icon {
  background-image: url(../images/file_edit.png) !important;
}

.file_jar_icon {
  background-image: url(../images/file_jar.png) !important;
}

.file_java_icon {
  background-image: url(../images/file_java.png) !important;
}

.file_js_icon {
  background-image: url(../images/file_js.png) !important;
}

.file_jsp_icon {
  background-image: url(../images/file_jsp.png) !important;
}

.file_ppt_icon {
  background-image: url(../images/file_ppt.png) !important;
}

.file_ss_icon {
  background-image: url(../images/file_ss.png) !important;
}

.file_txt_icon {
  background-image: url(../images/file_txt.png) !important;
}

.file_xls_icon {
  background-image: url(../images/file_xls.png) !important;
}

.file_xml_icon {
  background-image: url(../images/file_xml.png) !important;
}

.file_xwl_icon {
  background-image: url(../images/file_xwl.png) !important;
}

.file_zip_icon {
  background-image: url(../images/file_zip.png) !important;
}

.fill_icon {
  background-image: url(../images/fill.png) !important;
}

.filter_icon {
  background-image: url(../images/filter.png) !important;
}

.filter_add_icon {
  background-image: url(../images/filter_add.png) !important;
}

.filter_delete_icon {
  background-image: url(../images/filter_delete.png) !important;
}

.filter_edit_icon {
  background-image: url(../images/filter_edit.png) !important;
}

.firefox_icon {
  background-image: url(../images/firefox.png) !important;
}

.first_icon {
  background-image: url(../images/first.png) !important;
}

.flag1_icon {
  background-image: url(../images/flag1.png) !important;
}

.flag2_icon {
  background-image: url(../images/flag2.png) !important;
}

.flag3_icon {
  background-image: url(../images/flag3.png) !important;
}

.flash_icon {
  background-image: url(../images/flash.png) !important;
}

.folder_icon {
  background-image: url(../images/folder.png) !important;
}

.folder_add_icon {
  background-image: url(../images/folder_add.png) !important;
}

.folder_closed_icon {
  background-image: url(../images/folder_closed.png) !important;
}

.folder_delete_icon {
  background-image: url(../images/folder_delete.png) !important;
}

.folder_edit_icon {
  background-image: url(../images/folder_edit.png) !important;
}

.folder_open_icon {
  background-image: url(../images/folder_open.png) !important;
}

.form_icon {
  background-image: url(../images/form.png) !important;
}

.format_icon {
  background-image: url(../images/format.png) !important;
}

.full_screen_icon {
  background-image: url(../images/full_screen.png) !important;
}

.get_icon {
  background-image: url(../images/get.png) !important;
}

.go_icon {
  background-image: url(../images/go.png) !important;
}

.grid_icon {
  background-image: url(../images/grid.png) !important;
}

.group_icon {
  background-image: url(../images/group.png) !important;
}

.help_icon {
  background-image: url(../images/help.png) !important;
}

.hiddenfield_icon {
  background-image: url(../images/hiddenfield.png) !important;
}

.history_icon {
  background-image: url(../images/history.png) !important;
}

.home_icon {
  background-image: url(../images/home.png) !important;
}

.hr_icon {
  background-image: url(../images/hr.png) !important;
}

.htmleditor_icon {
  background-image: url(../images/htmleditor.png) !important;
}

.ie_icon {
  background-image: url(../images/ie.png) !important;
}

.image_icon {
  background-image: url(../images/image.png) !important;
}

.import_icon {
  background-image: url(../images/import.png) !important;
}

.info_icon {
  background-image: url(../images/info.png) !important;
}

.insert_icon {
  background-image: url(../images/insert.png) !important;
}

.italic_icon {
  background-image: url(../images/italic.png) !important;
}

.item_icon {
  background-image: url(../images/item.png) !important;
}

.ja_icon {
  background-image: url(../images/ja.png) !important;
}

.js_icon {
  background-image: url(../images/js.png) !important;
}

.key_icon {
  background-image: url(../images/key.png) !important;
}

.keyboard_icon {
  background-image: url(../images/keyboard.png) !important;
}

.label_icon {
  background-image: url(../images/label.png) !important;
}

.lamp_green_icon {
  background-image: url(../images/lamp_green.png) !important;
}

.lamp_red_icon {
  background-image: url(../images/lamp_red.png) !important;
}

.lamp_yellow_icon {
  background-image: url(../images/lamp_yellow.png) !important;
}

.last_icon {
  background-image: url(../images/last.png) !important;
}

.law_icon {
  background-image: url(../images/law.png) !important;
}

.left_icon {
  background-image: url(../images/left.png) !important;
}

.level_down_icon {
  background-image: url(../images/level_down.png) !important;
}

.level_up_icon {
  background-image: url(../images/level_up.png) !important;
}

.li_icon {
  background-image: url(../images/li.png) !important;
}

.link_icon {
  background-image: url(../images/link.png) !important;
}

.link_add_icon {
  background-image: url(../images/link_add.png) !important;
}

.link_delete_icon {
  background-image: url(../images/link_delete.png) !important;
}

.link_edit_icon {
  background-image: url(../images/link_edit.png) !important;
}

.list_icon {
  background-image: url(../images/list.png) !important;
}

.listcon_icon {
  background-image: url(../images/listcon.png) !important;
}

.lock_icon {
  background-image: url(../images/lock.png) !important;
}

.logout_icon {
  background-image: url(../images/logout.png) !important;
}

.love_icon {
  background-image: url(../images/love.png) !important;
}

.mail_icon {
  background-image: url(../images/mail.png) !important;
}

.measure_icon {
  background-image: url(../images/measure.png) !important;
}

.media_flash_icon {
  background-image: url(../images/media_flash.png) !important;
}

.menu_icon {
  background-image: url(../images/menu.png) !important;
}

.menuitem_icon {
  background-image: url(../images/menuitem.png) !important;
}

.minus_icon {
  background-image: url(../images/minus.png) !important;
}

.model_icon {
  background-image: url(../images/model.png) !important;
}

.module_icon {
  background-image: url(../images/module.png) !important;
}

.money_icon {
  background-image: url(../images/money.png) !important;
}

.mouse_icon {
  background-image: url(../images/mouse.png) !important;
}

.move_icon {
  background-image: url(../images/move.png) !important;
}

.music_icon {
  background-image: url(../images/music.png) !important;
}

.navview_icon {
  background-image: url(../images/navview.png) !important;
}

.ne_icon {
  background-image: url(../images/ne.png) !important;
}

.new_icon {
  background-image: url(../images/new.png) !important;
}

.notepad_icon {
  background-image: url(../images/notepad.png) !important;
}

.null_icon {
  background-image: url(../images/null.png) !important;
}

.object_icon {
  background-image: url(../images/object.png) !important;
}

.ok_icon {
  background-image: url(../images/ok.png) !important;
}

.open_icon {
  background-image: url(../images/open.png) !important;
}

.opera_icon {
  background-image: url(../images/opera.png) !important;
}

.option_icon {
  background-image: url(../images/option.png) !important;
}

.order_icon {
  background-image: url(../images/order.png) !important;
}

.ordered_list_icon {
  background-image: url(../images/ordered_list.png) !important;
}

.org_icon {
  background-image: url(../images/org.png) !important;
}

.page_icon {
  background-image: url(../images/page.png) !important;
}

.panel_icon {
  background-image: url(../images/panel.png) !important;
}

.paste_icon {
  background-image: url(../images/paste.png) !important;
}

.pause_icon {
  background-image: url(../images/pause.png) !important;
}

.pay_icon {
  background-image: url(../images/pay.png) !important;
}

.phone_icon {
  background-image: url(../images/phone.png) !important;
}

.photo_icon {
  background-image: url(../images/photo.png) !important;
}

.picker_icon {
  background-image: url(../images/picker.png) !important;
}

.plugin_icon {
  background-image: url(../images/plugin.png) !important;
}

.plus_icon {
  background-image: url(../images/plus.png) !important;
}

.preview_icon {
  background-image: url(../images/preview.png) !important;
}

.printer_icon {
  background-image: url(../images/printer.png) !important;
}

.product_icon {
  background-image: url(../images/product.png) !important;
}

.property_icon {
  background-image: url(../images/property.png) !important;
}

.question_icon {
  background-image: url(../images/question.png) !important;
}

.radio_group_icon {
  background-image: url(../images/radio_group.png) !important;
}

.radio_off_icon {
  background-image: url(../images/radio_off.png) !important;
}

.radio_on_icon {
  background-image: url(../images/radio_on.png) !important;
}

.record_icon {
  background-image: url(../images/record.png) !important;
}

.record_add_icon {
  background-image: url(../images/record_add.png) !important;
}

.record_copy_icon {
  background-image: url(../images/record_copy.png) !important;
}

.record_delete_icon {
  background-image: url(../images/record_delete.png) !important;
}

.record_edit_icon {
  background-image: url(../images/record_edit.png) !important;
}

.redo_icon {
  background-image: url(../images/redo.png) !important;
}

.refresh_icon {
  background-image: url(../images/refresh.png) !important;
}

.registration_icon {
  background-image: url(../images/registration.png) !important;
}

.relation_icon {
  background-image: url(../images/relation.png) !important;
}

.remove_icon {
  background-image: url(../images/remove.png) !important;
}

.rename_icon {
  background-image: url(../images/rename.png) !important;
}

.replace_icon {
  background-image: url(../images/replace.png) !important;
}

.report_icon {
  background-image: url(../images/report.png) !important;
}

.research_icon {
  background-image: url(../images/research.png) !important;
}

.resource_icon {
  background-image: url(../images/resource.png) !important;
}

.restore_icon {
  background-image: url(../images/restore.png) !important;
}

.resume_icon {
  background-image: url(../images/resume.png) !important;
}

.right_icon {
  background-image: url(../images/right.png) !important;
}

.role_icon {
  background-image: url(../images/role.png) !important;
}

.rss_icon {
  background-image: url(../images/rss.png) !important;
}

.ruby_icon {
  background-image: url(../images/ruby.png) !important;
}

.run_icon {
  background-image: url(../images/run.png) !important;
}

.safari_icon {
  background-image: url(../images/safari.png) !important;
}

.save_icon {
  background-image: url(../images/save.png) !important;
}

.save_all_icon {
  background-image: url(../images/save_all.png) !important;
}

.save_as_icon {
  background-image: url(../images/save_as.png) !important;
}

.script_icon {
  background-image: url(../images/script.png) !important;
}

.search_icon {
  background-image: url(../images/search.png) !important;
}

.search_again_icon {
  background-image: url(../images/search_again.png) !important;
}

.security_icon {
  background-image: url(../images/security.png) !important;
}

.seek_icon {
  background-image: url(../images/seek.png) !important;
}

.select_all_icon {
  background-image: url(../images/select_all.png) !important;
}

.series_icon {
  background-image: url(../images/series.png) !important;
}

.server_icon {
  background-image: url(../images/server.png) !important;
}

.servers_icon {
  background-image: url(../images/servers.png) !important;
}

.server_add_icon {
  background-image: url(../images/server_add.png) !important;
}

.server_delete_icon {
  background-image: url(../images/server_delete.png) !important;
}

.server_start_icon {
  background-image: url(../images/server_start.png) !important;
}

.server_stop_icon {
  background-image: url(../images/server_stop.png) !important;
}

.set_icon {
  background-image: url(../images/set.png) !important;
}

.settings1_icon {
  background-image: url(../images/settings1.png) !important;
}

.settings2_icon {
  background-image: url(../images/settings2.png) !important;
}

.share_icon {
  background-image: url(../images/share.png) !important;
}

.shopping_cart_icon {
  background-image: url(../images/shopping_cart.png) !important;
}

.shopping_cart_add_icon {
  background-image: url(../images/shopping_cart_add.png) !important;
}

.shopping_cart_delete_icon {
  background-image: url(../images/shopping_cart_delete.png) !important;
}

.shopping_cart_edit_icon {
  background-image: url(../images/shopping_cart_edit.png) !important;
}

.sitemap_icon {
  background-image: url(../images/sitemap.png) !important;
}

.slider_icon {
  background-image: url(../images/slider.png) !important;
}

.sort_by_date_icon {
  background-image: url(../images/sort_by_date.png) !important;
}

.sort_by_name_icon {
  background-image: url(../images/sort_by_name.png) !important;
}

.sort_by_quantity_icon {
  background-image: url(../images/sort_by_quantity.png) !important;
}

.sound_icon {
  background-image: url(../images/sound.png) !important;
}

.sound_delete_icon {
  background-image: url(../images/sound_delete.png) !important;
}

.sp_icon {
  background-image: url(../images/sp.png) !important;
}

.spacer_icon {
  background-image: url(../images/spacer.png) !important;
}

.spinner_icon {
  background-image: url(../images/spinner.png) !important;
}

.sql_icon {
  background-image: url(../images/sql.png) !important;
}

.sql_switcher_icon {
  background-image: url(../images/sql_switcher.png) !important;
}

.ss_icon {
  background-image: url(../images/ss.png) !important;
}

.star_icon {
  background-image: url(../images/star.png) !important;
}

.start_icon {
  background-image: url(../images/start.png) !important;
}

.stop_icon {
  background-image: url(../images/stop.png) !important;
}

.store_icon {
  background-image: url(../images/store.png) !important;
}

.strike_icon {
  background-image: url(../images/strike.png) !important;
}

.support_icon {
  background-image: url(../images/support.png) !important;
}

.system_icon {
  background-image: url(../images/system.png) !important;
}

.tab_icon {
  background-image: url(../images/tab.png) !important;
}

.table_icon {
  background-image: url(../images/table.png) !important;
}

.table_add_icon {
  background-image: url(../images/table_add.png) !important;
}

.table_align_columns_icon {
  background-image: url(../images/table_align_columns.png) !important;
}

.table_align_rows_icon {
  background-image: url(../images/table_align_rows.png) !important;
}

.table_auto_format_icon {
  background-image: url(../images/table_auto_format.png) !important;
}

.table_borders_icon {
  background-image: url(../images/table_borders.png) !important;
}

.table_cell_alignment_icon {
  background-image: url(../images/table_cell_alignment.png) !important;
}

.table_delete_icon {
  background-image: url(../images/table_delete.png) !important;
}

.table_edit_icon {
  background-image: url(../images/table_edit.png) !important;
}

.table_merge_cells_icon {
  background-image: url(../images/table_merge_cells.png) !important;
}

.table_split_cells_icon {
  background-image: url(../images/table_split_cells.png) !important;
}

.tbar_icon {
  background-image: url(../images/tbar.png) !important;
}

.te_icon {
  background-image: url(../images/te.png) !important;
}

.textarea_icon {
  background-image: url(../images/textarea.png) !important;
}

.text_align_center_icon {
  background-image: url(../images/text_align_center.png) !important;
}

.text_align_left_icon {
  background-image: url(../images/text_align_left.png) !important;
}

.text_align_right_icon {
  background-image: url(../images/text_align_right.png) !important;
}

.text_align_right_pin_icon {
  background-image: url(../images/text_align_right_pin.png) !important;
}

.text_indent_icon {
  background-image: url(../images/text_indent.png) !important;
}

.text_justify_icon {
  background-image: url(../images/text_justify.png) !important;
}

.text_outdent_icon {
  background-image: url(../images/text_outdent.png) !important;
}

.tile_windows_horizontally_icon {
  background-image: url(../images/tile_windows_horizontally.png) !important;
}

.tile_windows_vertically_icon {
  background-image: url(../images/tile_windows_vertically.png) !important;
}

.time_icon {
  background-image: url(../images/time.png) !important;
}

.toggle_icon {
  background-image: url(../images/toggle.png) !important;
}

.tool_icon {
  background-image: url(../images/tool.png) !important;
}

.toolbar_icon {
  background-image: url(../images/toolbar.png) !important;
}

.top_icon {
  background-image: url(../images/top.png) !important;
}

.topic_icon {
  background-image: url(../images/topic.png) !important;
}

.total_icon {
  background-image: url(../images/total.png) !important;
}

.trash_icon {
  background-image: url(../images/trash.png) !important;
}

.tree_icon {
  background-image: url(../images/tree.png) !important;
}

.treestore_icon {
  background-image: url(../images/treestore.png) !important;
}

.underline_icon {
  background-image: url(../images/underline.png) !important;
}

.undo_icon {
  background-image: url(../images/undo.png) !important;
}

.unlock_icon {
  background-image: url(../images/unlock.png) !important;
}

.unordered_list_icon {
  background-image: url(../images/unordered_list.png) !important;
}

.up_icon {
  background-image: url(../images/up.png) !important;
}

.upward_icon {
  background-image: url(../images/upward.png) !important;
}

.usb_icon {
  background-image: url(../images/usb.png) !important;
}

.user_icon {
  background-image: url(../images/user.png) !important;
}

.user1_icon {
  background-image: url(../images/user1.png) !important;
}

.user_add_icon {
  background-image: url(../images/user_add.png) !important;
}

.user_delete_icon {
  background-image: url(../images/user_delete.png) !important;
}

.user_edit_icon {
  background-image: url(../images/user_edit.png) !important;
}

.user_group_icon {
  background-image: url(../images/user_group.png) !important;
}

.video_icon {
  background-image: url(../images/video.png) !important;
}

.view_icon {
  background-image: url(../images/view.png) !important;
}

.viewport_icon {
  background-image: url(../images/viewport.png) !important;
}

.wait_icon {
  background-image: url(../images/wait.png) !important;
}

.wand_icon {
  background-image: url(../images/wand.png) !important;
}

.warning_icon {
  background-image: url(../images/warning.png) !important;
}

.weather_icon {
  background-image: url(../images/weather.png) !important;
}

.web_icon {
  background-image: url(../images/web.png) !important;
}

.window_icon {
  background-image: url(../images/window.png) !important;
}

.workflow_icon {
  background-image: url(../images/workflow.png) !important;
}

.wrench_icon {
  background-image: url(../images/wrench.png) !important;
}

.write_icon {
  background-image: url(../images/write.png) !important;
}

.zoom_icon {
  background-image: url(../images/zoom.png) !important;
}

.zoom_in_icon {
  background-image: url(../images/zoom_in.png) !important;
}

.zoom_out_icon {
  background-image: url(../images/zoom_out.png) !important;
}

.x-grid-tree-loading .x-tree-icon {
  background-image: url(../images/app/loading1.gif) !important;
}

@font-face {
  font-family: 'FontAwesome';
  src: url('../libs/fa/fonts/fontawesome-webfont.eot?v=4.7.0');
  src: url('../libs/fa/fonts/fontawesome-webfont.eot?#iefix&v=4.7.0') format('embedded-opentype'), url('../libs/fa/fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'), url('../libs/fa/fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'), url('../libs/fa/fonts/fontawesome-webfont.ttf?v=4.7.0') format('truetype'), url('../libs/fa/fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular') format('svg');
  font-weight: normal;
  font-style: normal;
}

.wb_icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  margin-top: -3px;
  float: left;
  position: relative;
  top: 2px;
}

.wb_icon1 {
  width: 16px;
  height: 16px;
}

.wb_icon2 {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  float: left;
  position: relative;
  top: 2px;
}

@-webkit-keyframes wb_spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

@keyframes wb_spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

.x-portal .x-panel-dd-spacer {
  border: 2px dashed #99bbe8;
  background: #f6f6f6;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -khtml-border-radius: 4px;
  border-radius: 4px;
  margin: 3px;
}

.x-portlet {
  margin: 4px;
}

.x-view-selector {
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  background-color: #c3daf9;
  border: 1px dotted #39b;
  filter: alpha(opacity=50);
  opacity: 0.5;
  zoom: 1;
}

.wb_cell_height70 .x-grid-cell {
  height: 70px;
}

.wb_cell_bold .x-grid-cell {
  font-weight: bold;
}

.wb_cell_pointer .x-grid-cell {
  cursor: pointer;
}

.wb_cell_compact {
  padding: 1px 2px 1px 2px;
}
.wb_over_hidden {
  display: none;
}

.x-grid-row-over .wb_over_hidden {
  display: block;
}

.wb_spiner {
  font-family: FontAwesome;
  display: inline-block;
  -moz-animation: wb_spin 2s infinite linear;
  -o-animation: wb_spin 2s infinite linear;
  -webkit-animation: wb_spin 2s infinite linear;
  animation: wb_spin 2s infinite linear
}

.wb_gicon {
  float: left;
  width: 22px;
  margin: 1px 5px 0 0;
  text-align: center;
  font-family: FontAwesome;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.wb_glyph {
  font-family: FontAwesome;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.wb_height12 {
  display: inline-block;
  height: 12px;
}

.wb_btn14 .x-btn-glyph {
  font-size: 14px;
}

.wb_nodeglyph {
  font-family: FontAwesome;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
  width: 18px;
  color: #777;
  display: inline-block;
  font-size: 16px;
  top: 2px;
  left: -1px;
}

.wb_gbutton {
  font-family: FontAwesome;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor: pointer;
}

.wb_glyph2 {
  position: absolute;
  top: 4px;
  right: 0;
  bottom: 0;
  left: 0;
  color: white;
  text-align: center;
  font-family: FontAwesome;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.wb_glyph3 {
  font-family: FontAwesome;
  font-size: 15px;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.wb_glyph4 {
  font-family: FontAwesome;
  font-size: 48px;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.wb_glyphfont {
  font-size: 16px;
  color: #333333;
}

.wb_glyphsize {
  font-size: 16px;
}

.wb_glyph_icon {
  display: inline-block;
  font-size: 14px;
  width: 20px;
  color: #777;
  height: 12px;
}

.wb_opacity {
  opacity: 0;
}

.wb_doc {
  padding: 2px 8px 2px 8px;
  line-height: 1.8;
  font-size: 12px;
}

.wb_pb {
  page-break-after: always;
}

.wb_abs {
  position: absolute !important;
}

.wb_rel {
  position: relative !important;
}

.wb_hr {
  border: none;
  border-top: 1px solid #bbbbbb;
}

.wb_line {
  border-bottom: 1px solid #AAA;
  padding-bottom: 5px;
}

.wb_vline {
  border-right: 1px solid #AAA;
  padding-right: 5px;
}

.wb_code {
  background-color: #f7f7f7;
  border: solid 1px #e8e8e8;
  color: #314e64;
  font-size: 12px;
  padding: 5px;
  margin: 5px 0 5px 0;
  line-height: 1.6;
}

.wb_table {
  border: 1px solid #666666;
  table-layout: fixed;
  border-collapse: collapse;
}

.wb_table td {
  border: 1px solid #666666;
  padding: 3px;
}

.wb_tb {
  border: 1px solid #aaa;
  table-layout: fixed;
  border-collapse: collapse
}

.wb_tb > tbody > tr > td {
  border: 1px solid #aaa;
  padding: 5px;
}

.xfield .wb_tb td {
  border: none;
  padding: 0;
}

.wb_pad td {
  padding: 5px;
}

.wb_gray {
  background-color: #EEE;
}

.wb_dark {
  background-color: #666;
}

.wb_white {
  background-color: white;
}

.wb_xlink:hover {
  color: blue;
  cursor: pointer;
}

.wb_link a:link {
  color: #FFFF00;
}

.wb_link a:visited {
  color: #FFFF00;
}

.wb_link a:hover {
  color: #FFFF00;
}

.wb_link a:active {
  color: #FFFF00;
}

.wb_pointer {
  cursor: pointer;
}

.wb_thumb {
  float: left;
  margin: 4px;
  margin-right: 0;
  padding: 5px;
}

.wb_item {
  border: 1px solid transparent;
  float: left;
  margin: 4px;
  margin-right: 0;
  padding: 5px;
  height: 105px;
  text-align: center;
}

.wb_item_img {
  border: 1px solid transparent;
  float: left;
  margin: 4px;
  margin-right: 0;
  padding: 5px;
  height: 60px;
  text-align: center;
  overflow: hidden;
}

.wb_tag {
  border: 1px solid #ababab;
  background-color: #ebebeb;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -khtml-border-radius: 8px;
  border-radius: 8px;
  float: left;
  margin: 5px;
  padding: 2px;
}

.wb_tag_text {
  margin: 8px;
}

.wb_tag_icon {
  margin: 8px 5px 8px 0;
  cursor: pointer;
  font-family: FontAwesome;
  width: 10px;
  text-align: center;
  opacity: 0.3;
}

.wb_tag_over {
  background-color: #d0e1f9;
}

.wb_tag_selected {
  background-color: #38f;
  color: white;
}

.wb_tag_selected .wb_tag_icon {
  opacity: 1;
}

.wb_badge {
  position: absolute;
  right: 10px;
  background: #E74C3C;
  color: #fff;
  padding: 3px 7px;
  white-space: nowrap;
  border-radius: 10px;
  margin-top: -3px;
}

.wb_badge1 {
  position: relative;
  font-size: 11px;
  top: -6px;
  right: 3px;
  background: #E74C3C;
  color: #fff;
  padding: 1px 5px;
  white-space: nowrap;
  border-radius: 10px;
}

.wb_nowrap {
  white-space: nowrap;
}

.wb_table1 {
  width: 100%;
  border-collapse: collapse;
}

.wb_table1 td {
  border: 1px solid transparent;
  border-bottom: 1px solid #eee;
  padding: 5px;
}

.wb_table2 {
  border-collapse: collapse;
  table-layout: fixed;
}

.wb_table2 td {
  border: 1px solid transparent;
  border-bottom: 1px solid #eee;
  padding: 5px;
}

.wb_ib {
  display: inline-block;
}

.wb_hover {
  background-color: #eff5fb;
  border: 1px solid #d6e9fb;
}

.wb_selected {
  background-color: #d0e1f9;
  border: 1px solid #a7beda;
}

.wb_red_row .x-grid-cell {
  background-color: red
}

.wb_green_row .x-grid-cell {
  background-color: springgreen
}

.wb_blue_row .x-grid-cell {
  background-color: blue
}

.wb_yellow_row .x-grid-cell {
  background-color: yellow
}

.wb_hotpink_row .x-grid-cell {
  background-color: hotpink
}

.wb_gold_row .x-grid-cell {
  background-color: gold
}

.wb_bisque_row .x-grid-cell {
  background-color: bisque
}

.wb_aqua_row .x-grid-cell {
  background-color: aqua
}

.wb_orange_row .x-grid-cell {
  background-color: orange
}

.wb_bold_row .x-grid-cell {
  font-weight: bold;
}

.wb_highlight_row .x-grid-cell {
  color: blue;
}

.grid-line .x-grid-table {
  table-layout: fixed;
  border-collapse: collapse;
}

.grid-line .x-grid-cell {
  border: 1px solid #c8c8c8;
}

.wb_header {
  background-color: #ddd;
}

.wb_table2 .wb_header_td {
  border: 1px solid #c8c8c8;
}

.wb_stripe_even {
  background: #f9f9f9;
}

.wb_stripe_odd {
  background: #fff;
}

.wb_stripe_even:hover {
  background-color: #eff5fb;
  border: 1px solid #d6e9fb;
}

.wb_stripe_odd:hover {
  background-color: #eff5fb;
  border: 1px solid #d6e9fb;
}

.wb_selected .wb_td {
  background-color: #d0e1f9;
}

.wb_line_td {
  border: 1px solid #eee;
  min-width: 35px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -khtml-border-radius: 3px;
  border-radius: 3px;
  color: #128bed;
  text-align: center;
  padding: 3px 5px 3px 5px;
  cursor: pointer;
  white-space: nowrap;
}

.wb_line_select {
  color: #fff;
  background-color: #128bed;
  border-color: #128bed;
}
.wb_line_over {
  border: 1px solid #128bed;
}
.wb_no_border {
  border: 0 !important;
}

.wb_lbl_bigBold {
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #c8c8c8;
  padding-bottom: 5px;
  margin: 8px 0 8px 0;
}

.wb_lbl_bold {
  font-weight: bold;
  border-bottom: 1px solid #c8c8c8;
  padding-bottom: 5px;
  margin: 8px 0 8px 0;
}

.wb_lbl_normal {
  border-bottom: 1px solid #c8c8c8;
  padding-bottom: 5px;
  margin: 8px 0 8px 0;
}

.wb_simple {
  padding: 0 8px 0 8px;
  cursor: pointer;
}

.wb_simple_select {
  background: #0270ad;
}
.wb_removed {
  text-decoration: line-through;
}

.wb_btn_default {
  background: #fff;
  border-color: #ccc;
  color: #333;
}

.x-wb_btn_default_focus {
  background: #e6e6e6;
  border-color: #8c8c8c;
  color: #333;
}

.x-wb_btn_default_over {
  background: #e6e6e6;
  border-color: #adadad;
  color: #333;
}

.x-wb_btn_default_pressed {
  background: #d4d4d4;
  border-color: #8c8c8c;
  color: #333;
}

.wb_btn_default .x-btn-inner {
  color: #333;
}

.wb_btn_default .x-btn-glyph {
  color: #333;
  opacity: 1;
}

.wb_btn_primary {
  background: #337ab7;
  border-color: #2e6da4;
}

.x-wb_btn_primary_focus {
  background: #286090;
  border-color: #122b40;
}

.x-wb_btn_primary_over {
  background: #286090;
  border-color: #204d74;
}

.x-wb_btn_primary_pressed {
  background: #204d74;
  border-color: #122b40;
}

.wb_btn_primary .x-btn-inner {
  color: white;
}

.wb_btn_primary .x-btn-glyph {
  color: white;
  opacity: 1;
}

.wb_btn_success {
  background: #4cb84c;
  border-color: #3cae3c;
}

.x-wb_btn_success_focus {
  background: #449d44;
  border-color: #255625;
}

.x-wb_btn_success_over {
  background: #449d44;
  border-color: #398439;
}

.x-wb_btn_success_pressed {
  background: #398439;
  border-color: #255625;
}

.wb_btn_success .x-btn-inner {
  color: white;
}

.wb_btn_success .x-btn-glyph {
  color: white;
  opacity: 1;
}

.wb_btn_info {
  background: #5bc0de;
  border-color: #46b8da;
}

.x-wb_btn_info_focus {
  background: #31b0d5;
  border-color: #1b6d85;
}

.x-wb_btn_info_over {
  background: #31b0d5;
  border-color: #269abc;
}

.x-wb_btn_info_pressed {
  background: #269abc;
  border-color: #1b6d85;
}

.wb_btn_info .x-btn-inner {
  color: white;
}

.wb_btn_info .x-btn-glyph {
  color: white;
  opacity: 1;
}

.wb_btn_warning {
  background: #f0ad4e;
  border-color: #eea236;
}

.x-wb_btn_warning_focus {
  background: #ec971f;
  border-color: #985f0d;
}

.x-wb_btn_warning_over {
  background: #ec971f;
  border-color: #d58512;
}

.x-wb_btn_warning_pressed {
  background: #d58512;
  border-color: #985f0d;
}

.wb_btn_warning .x-btn-inner {
  color: white;
}

.wb_btn_warning .x-btn-glyph {
  color: white;
  opacity: 1;
}

.wb_btn_danger {
  background: #d9433f;
  border-color: #d42f2a;
}

.x-wb_btn_danger_focus {
  background: #c9302c;
  border-color: #761c19;
}

.x-wb_btn_danger_over {
  background: #c9302c;
  border-color: #ac2925;
}

.x-wb_btn_danger_pressed {
  background: #ac2925;
  border-color: #761c19;
}

.wb_btn_danger .x-btn-inner {
  color: white;
}

.wb_btn_danger .x-btn-glyph {
  color: white;
  opacity: 1;
}

.wb_btn_round {
  border-radius: 30px;
}

.x-btn-default-large .x-btn-inner {
  font-size: 16px;
}

.x-btn-default-medium .x-btn-inner {
  font-size: 14px;
}

.wb_required {
  color: #c00;
}

.wb_shadow {
  border: #909090 1px solid;
  background: #fff;
  -ms-filter: "progid:DXImageTransform.Microsoft.Shadow(Strength=4, Direction=135, Color='#333')";
  filter: progid: DXImageTransform.Microsoft.Shadow(Strength=4, Direction=135, Color='#333');
  -moz-box-shadow: 2px 2px 10px #333;
  -webkit-box-shadow: 2px 2px 10px #333;
  box-shadow: 2px 2px 10px #333;
}

.wb_underline {
  border-bottom: 1px solid #c8c8c8;
  padding-bottom: 5px;
  margin: 8px 0 8px 0;
}

.wb_frame {
  border: 1px solid #c8c8c8;
}

.x-grid-row-over .wb_right_icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  margin-top: -3px;
  float: right;
  position: relative;
  top: 2px;
  cursor: pointer;
}
.wb_seg {
  text-align: center;
  display: table;
  width: 100%;
  height: 100%;
}
.wb_seg_item {
  vertical-align: middle;
  display: table-cell;
  font-size: 18px;
}
.wb_title1 .x-panel-header-text-container-default-framed {
  font-size: 14px;
  color: #555;
}

.wizard_horizontal ul.wizard_steps {
  display: table;
  list-style: none;
  position: relative;
  width: 100%;
  margin: 0;
  padding: 0;
}

.wizard_horizontal ul.wizard_steps li {
  display: table-cell;
  text-align: center;
}

.wizard_horizontal ul.wizard_steps li a,
.wizard_horizontal ul.wizard_steps li:hover {
  display: block;
  position: relative;
  -moz-opacity: 1;
  filter: alpha(opacity=100);
  opacity: 1;
  color: #666;
}

.wizard_horizontal ul.wizard_steps li:first-child a:before {
  left: 50%;
}

.wizard_horizontal ul.wizard_steps li a.done .step_no,
.wizard_horizontal ul.wizard_steps li a.done:before {
  background: #1ABB9C;
  color: #fff;
}

.wizard_horizontal ul.wizard_steps li a:before {
  content: "";
  position: absolute;
  height: 4px;
  background: #ccc;
  top: 20px;
  width: 100%;
  z-index: 4;
  left: 0;
}

.x-grid-tree-loading .wb_nodeglyph {
  -moz-animation: wb_spin 2s infinite linear;
  -o-animation: wb_spin 2s infinite linear;
  -webkit-animation: wb_spin 2s infinite linear;
  animation: wb_spin 2s infinite linear
}

.wizard_horizontal ul.wizard_steps li .step_no {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 100px;
  display: block;
  margin: 0 auto 10px;
  font-size: 16px;
  text-align: center;
  position: relative;
  z-index: 5;
}

.step_no {
  background: #34495E;
  color: #fff;
}

.wizard_horizontal ul.wizard_steps li .disabled .step_no {
  background: #ccc;
}

.wizard_horizontal ul.wizard_steps li:last-child a:before {
  right: 50%;
  width: 50%;
  left: auto;
}

.ext-ux-clearbutton {
  width: 12px;
  height: 12px;
  background-image: url(../images/clear-text-icon.gif);
  background-position: 0 0;
  background-repeat: no-repeat;
  -moz-user-focus: ignore;
  cursor: pointer;
  position: absolute;
  overflow: hidden;
  background-color: white;
}

.ext-ux-clearbutton-mouse-over-input {
  background-position: 0 -12px;
}

.ext-ux-clearbutton-mouse-over-button {
  background-position: 0 -24px;
}

.ext-ux-clearbutton-mouse-down {
  background-position: 0 -36px;
}

.ext-ux-clearbutton-on {
  opacity: 1;
  visibility: visible;
  transition: opacity .35s linear;
  -webkit-transition: opacity .35s linear;
  -moz-transition: opacity .35s linear;
  -o-transition: opacity .35s linear;
  -ms-transition: opacity .35s linear;
  -khtml-transition: opacity .35s linear;
}

.ext-ux-clearbutton-off {
  opacity: 0;
  visibility: hidden;
  transition: opacity .35s linear, visibility .0s linear .35s;
  -webkit-transition: opacity .35s linear, visibility .0s linear .35s;
  -moz-transition: opacity .35s linear, visibility .0s linear .35s;
  -o-transition: opacity .35s linear, visibility .0s linear .35s;
  -ms-transition: opacity .35s linear, visibility .0s linear .35s;
  -khtml-transition: opacity .35s linear, visibility .0s linear .35s;
}
/*bootstrap按钮样式*/

.btn {
  display: inline-block;
  padding: 6px 12px;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
}

.btn:focus,
.btn:active:focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn.active.focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

.btn:hover,
.btn:focus,
.btn.focus {
  color: #333;
  text-decoration: none;
}

.btn:active,
.btn.active {
  background-image: none;
  outline: 0;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
}

.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
  cursor: not-allowed;
  filter: alpha(opacity=65);
  -webkit-box-shadow: none;
  box-shadow: none;
  opacity: .65;
}

a.btn.disabled,
fieldset[disabled] a.btn {
  pointer-events: none;
}

.btn-default {
  color: #333;
  background-color: #fff;
  border-color: #ccc;
}

.btn-default:focus,
.btn-default.focus {
  color: #333;
  background-color: #e6e6e6;
  border-color: #8c8c8c;
}

.btn-default:hover {
  color: #333;
  background-color: #e6e6e6;
  border-color: #adadad;
}

.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default {
  color: #333;
  background-color: #e6e6e6;
  border-color: #adadad;
}

.btn-default:active:hover,
.btn-default.active:hover,
.open > .dropdown-toggle.btn-default:hover,
.btn-default:active:focus,
.btn-default.active:focus,
.open > .dropdown-toggle.btn-default:focus,
.btn-default:active.focus,
.btn-default.active.focus,
.open > .dropdown-toggle.btn-default.focus {
  color: #333;
  background-color: #d4d4d4;
  border-color: #8c8c8c;
}

.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default {
  background-image: none;
}

.btn-default.disabled,
.btn-default[disabled],
fieldset[disabled] .btn-default,
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled.focus,
.btn-default[disabled].focus,
fieldset[disabled] .btn-default.focus,
.btn-default.disabled:active,
.btn-default[disabled]:active,
fieldset[disabled] .btn-default:active,
.btn-default.disabled.active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default.active {
  background-color: #fff;
  border-color: #ccc;
}

.btn-default .badge {
  color: #fff;
  background-color: #333;
}

.btn-sm,
.btn-group-sm > .btn {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}

.x-tab-default-active .x-tab-glyph {
  color: #3c8dbc;
}
/* 登录界面样式 */

.wb-login-mode-bar {
  position: relative;
  border-bottom: 1px solid #30a0e5;
}

.wb-login-mode {
  float: left;
  width: 33.33%;
  cursor: pointer;
  padding: 15px 0px 10px 0px;
  text-align: center;
  font-size: 14px;
  opacity: .9;
  color: gray;
  border-bottom: 3px solid transparent;
  transition: all .5s;
}

.wb-login-mode-active {
  color: #30a0e5;
  opacity: 1;
  border-bottom: 3px solid #30a0e5;
  transition: all .5s;
}

.qrcode-hint {
  padding-top: 15px;
  font-size: 14px;
}

.wb-login {
  opacity: .95;
}

.wb-login-logo {
  padding-top: 20px;
  text-align: center;
}

.wb-topbar {
  background: transparent;
  border-bottom: 1px dotted rgba(0, 0, 0, 0.45);
  opacity: .9;
}

.wb-bottombar {
  background: transparent;
  opacity: .9;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.wb-signup {
  padding: 15px;
}

.wb-slogan {
  font-size: 20px;
  color: #000;
}

.shadow {
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  -ms-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2)
}

.wb-login .sbtn,
.wb-login .x-form-text,
.wb-login .x-form-item-label-top,
.wb-login .x-form-cb-label,
.wb-login .forget-pass {
  font-size: 16px;
}

.wb-login .x-form-item-label-top {
  padding-bottom: 10px;
  color: gray;
}

.wb-login .x-form-text {
  height: 39px;
  padding: 5px 15px;
  border-radius: 5px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -webkit-appearance: none;
}

.forget-pass:hover {
  text-decoration: underline;
}

.forget-pass {
  text-align: right;
  cursor: pointer;
}
/*默认样式*/

.x-panel-default .x-panel-header {
  border: 1px solid #e6e9ed !important;
  border-top: 2px solid #3c8dbc !important;
}

.x-window>.x-window-header {
  border-top-color: #3c8dbc;
  border-top-width: 4px;
  border-top-style: solid;
}

.x-accordion-item .x-accordion-hd {
  background: white;
  border-top-color: #ececec;
  padding: 4px 5px 5px 5px;
}
/* 紫色主题 */

.skin-purple .pt_app_title,
.skin-purple .pt_topbar .x-btn-pressed.x-btn-over,
.skin-purple .pt_topbar .x-btn-over,
.skin-purple .pt_topbar .x-btn-default-toolbar-small-menu-active,
.skin-purple .pt_topbar .x-btn-default-toolbar-small-pressed {
  background-color: #4d4b88;
}

.skin-purple .pt_topbar {
  background-color: #605ca8;
}

.skin-purple .x-panel-default .x-panel-header {
  border-top: 2px solid #605ca8 !important;
}

.skin-purple .x-window>.x-window-header {
  border-top-color: #605ca8;
}

.skin-purple .wb_btn_primary {
  background: #605ca8;
  border-color: #2e6da4;
}

.skin-purple .wb_btn_primary_focus {
  background: #4d4b88;
  border-color: #343256;
}

.skin-purple .x-wb_btn_primary_over {
  background: #4d4b88;
  border-color: #343256;
}

.skin-purple .x-wb_btn_primary_pressed {
  background: #393760;
  border-color: #343256;
}

.skin-purple .x-tab-default-top-active {
  border-bottom: 1.5px solid #605ca8;
}

.skin-purple .x-tab-default-active .x-tab-inner,
.skin-purple .x-tab-default-active .x-tab-glyph {
  color: #605ca8;
}
/* 绿色主题 */

.skin-green .pt_app_title,
.skin-green .pt_topbar .x-btn-pressed.x-btn-over,
.skin-green .pt_topbar .x-btn-over,
.skin-green .pt_topbar .x-btn-default-toolbar-small-menu-active,
.skin-green .pt_topbar .x-btn-default-toolbar-small-pressed {
  background-color: #009551;
}

.skin-green .pt_topbar {
  background-color: #00a65a;
}

.skin-green .x-panel-default .x-panel-header {
  border-top: 2px solid #00a65a !important;
}

.skin-green .x-window>.x-window-header {
  border-top-color: #00a65a;
}

.skin-green .wb_btn_primary {
  background: #00a65a;
  border-color: #005730;
}

.skin-green .wb_btn_primary_focus {
  background: #009551;
  border-color: #005730;
}

.skin-green .x-wb_btn_primary_over {
  background: #009551;
  border-color: #005730;
}

.skin-green .x-wb_btn_primary_pressed {
  background: #393760;
  border-color: #005730;
}

.skin-green .x-tab-default-top-active {
  border-bottom: 1.5px solid #00a65a;
}

.skin-green .x-tab-default-active .x-tab-inner,
.skin-green .x-tab-default-active .x-tab-glyph {
  color: #00a65a;
}
/* 红色主题 */

.skin-red .pt_app_title,
.skin-red .pt_topbar .x-btn-pressed.x-btn-over,
.skin-red .pt_topbar .x-btn-over,
.skin-red .pt_topbar .x-btn-default-toolbar-small-menu-active,
.skin-red .pt_topbar .x-btn-default-toolbar-small-pressed {
  background-color: #c64333;
}

.skin-red .pt_topbar {
  background-color: #dd4b39;
}

.skin-red .x-panel-default .x-panel-header {
  border-top: 2px solid #dd4b39 !important;
}

.skin-red .x-window>.x-window-header {
  border-top-color: #dd4b39;
}

.skin-red .wb_btn_primary {
  background: #dd4b39;
  border-color: #a03629;
}

.skin-red .wb_btn_primary_focus {
  background: #c64333;
  border-color: #a03629;
}

.skin-red .x-wb_btn_primary_over {
  background: #c64333;
  border-color: #a03629;
}

.skin-red .x-wb_btn_primary_pressed {
  background: #393760;
  border-color: #a03629;
}

.skin-red .x-tab-default-top-active {
  border-bottom: 1.5px solid #dd4b39;
}

.skin-red .x-tab-default-active .x-tab-inner,
.skin-red .x-tab-default-active .x-tab-glyph {
  color: #dd4b39;
}

.skin-red .wb_badge1 {
  background: #00a65a;
}
/* 黄色主题 */

.skin-yellow .pt_app_title,
.skin-yellow .pt_topbar .x-btn-pressed.x-btn-over,
.skin-yellow .pt_topbar .x-btn-over,
.skin-yellow .pt_topbar .x-btn-default-toolbar-small-menu-active,
.skin-yellow .pt_topbar .x-btn-default-toolbar-small-pressed {
  background-color: #da8c10;
}

.skin-yellow .pt_topbar {
  background-color: #f39c12;
}

.skin-yellow .x-panel-default .x-panel-header {
  border-top: 2px solid #f39c12 !important;
}

.skin-yellow .x-window>.x-window-header {
  border-top-color: #f39c12;
}

.skin-yellow .wb_btn_primary {
  background: #f39c12;
  border-color: #b0710d;
}

.skin-yellow .wb_btn_primary_focus {
  background: #da8c10;
  border-color: #b0710d;
}

.skin-yellow .x-wb_btn_primary_over {
  background: #da8c10;
  border-color: #b0710d;
}

.skin-yellow .x-wb_btn_primary_pressed {
  background: #393760;
  border-color: #b0710d;
}

.skin-yellow .x-tab-default-top-active {
  border-bottom: 1.5px solid #f39c12;
}

.skin-yellow .x-tab-default-active .x-tab-inner,
.skin-yellow .x-tab-default-active .x-tab-glyph {
  color: #f39c12;
}

.skin-yellow .wb_badge1 {
  background: #00a65a;
}
/* GRID行样式*/

.x-grid-view .x-grid-td {
  height: 38px;
  vertical-align: middle;
}

.x-grid-row-selected .x-grid-td {
  border-style: solid;
  border-color: #f2e3b2;
  color: #404040;
  background-color: #ffefbb;
}
/*详情页样式*/

.desc-title {
  margin-top: 15px;
  margin-bottom: 15px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
  font-size: 16px;
  line-height: 1.5;
}

.desc-view {
  border: 1px solid #e8e8e8;
}

.desc-view {
  width: 100%;
  overflow: hidden;
  border-radius: 4px;
}

.desc-view table {
  table-layout: auto;
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
}

.desc-row {
  border-bottom: 1px solid #e8e8e8;
}

.desc-item-label {
  background-color: #fafafa;
}

.desc-item-label,
.desc-item-content {
  padding: 8px 16px;
  border-right: 1px solid #e8e8e8;
}

.desc-item-label {
  color: rgba(0, 0, 0, 0.85);
  font-weight: normal;
  font-size: 14px;
  line-height: 1.5;
  white-space: nowrap;
}

.desc-view th {
  text-align: inherit;
}

.des-status {
  font-size: 14px;
  line-height: 40px;
  position: absolute;
  right: 60px;
  top: 40px;
  z-Index: 2;
  padding: 0 4em;
  -webkit-transform-origin: left bottom;
  -moz-transform-origin: left bottom;
  transform-origin: left bottom;
  -webkit-transform: translate(29.29%, -100%) rotate(45deg);
  -moz-transform: translate(29.29%, -100%) rotate(45deg);
  transform: translate(29.29%, -100%) rotate(45deg);
  text-indent: 0;
  border-radius: 20% / 50%;
  opacity: 0.5;
}

.flow_form .wb-disabled,
.flow_form .x-form-type-text input.x-form-invalid-field.wb-disabled {
  background: #ffffff;
  border-width: 0 0 1px 0;
}

.x_toast_cls,
.x_toast_body {
  background-color: #dd4b39 !important;
  border-color: #d73925;
  color: #fff !important;
}

/* 为Toolbar添加自动换行功能 */
.wb_wrap_toolbar .x-toolbar-default {
  display: flex;
  flex-wrap: wrap;
}

.wb_wrap_toolbar .x-toolbar-item {
  margin-bottom: 2px;
}

.wb_wrap_toolbar .x-toolbar-separator {
  display: none; /* 在换行模式下隐藏分隔符 */
}

/* ======================== 现代化UI样式增强 ======================== */

/* 更现代的圆角设置 */
.x-panel {
  border-radius: 8px;
  overflow: hidden;
}

.x-window {
  border-radius: 8px;
  overflow: hidden;
}

.x-btn {
  border-radius: 4px;
}

.x-form-text:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
  transition: all 0.3s ease;
}

/* 现代化阴影效果 */
.shadow,
.x-panel,
.x-window {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

/* 悬停时的提升效果 */
.x-btn:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

/* 现代字体 */
body, .x-body {
  font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, "Roboto", "Helvetica Neue", Arial, sans-serif;
  font-size: 14px;
  color: #3c4043;
}

/* 标题样式现代化 */
.x-panel-header-text,
.x-window-header-text {
  font-weight: 500;
  font-size: 16px;
}

/* 优化面板头部与圆角设计的协调性 */
.x-panel-header {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom: none;
  /* background: linear-gradient(to bottom, #f8f9fa, #eef0f5); */
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
  padding: 8px 10px;
}

.x-panel-header-text {
  color: #344054;
  font-weight: 600;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.7);
}

/* 为面板头部添加微妙的左侧边框装饰 */
.x-panel-default .x-panel-header:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #1890ff;
  border-top-left-radius: 8px;
}

/* 改进树形面板头部样式 */
.x-accordion-item .x-accordion-hd {
  /* background: linear-gradient(to bottom, #f8f9fa, #eef0f5); */
  border-radius: 4px;
}

.x-accordion-item.x-accordion-item-expanded .x-accordion-hd {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

/* 窗口头部也应用同样的样式 */
.x-window-header {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  /* background: linear-gradient(to bottom, #f8f9fa, #eef0f5); */
}

.x-window-header-text {
  color: #344054;
  font-weight: 600;
}

/* 为不同类型的面板设置不同颜色的左侧边框 */
.x-panel-primary .x-panel-header:before {
  background-color: #1890ff;
}

.x-panel-success .x-panel-header:before {
  background-color: #52c41a;
}

.x-panel-warning .x-panel-header:before {
  background-color: #faad14;
}

.x-panel-danger .x-panel-header:before {
  background-color: #f5222d;
}

.x-panel-info .x-panel-header:before {
  background-color: #722ed1;
}

/* 适配动态主题色变更 - 紫色主题 */
.skin-purple .x-panel-header {
  background: linear-gradient(to bottom, #f8f9fa, #efeff7);
}

.skin-purple .x-panel-default .x-panel-header:before {
  background-color: #605ca8;
}

.skin-purple .x-window-header {
  background: linear-gradient(to bottom, #f8f9fa, #efeff7);
}

.skin-purple .x-form-text:focus {
  border-color: #605ca8;
  box-shadow: 0 0 0 2px rgba(96, 92, 168, 0.2);
}

.skin-purple .x-panel-primary .x-panel-header:before,
.skin-purple .x-panel-info .x-panel-header:before {
  background-color: #605ca8;
}

.skin-purple .x-panel-success .x-panel-header:before {
  background-color: #52c41a;
}

.skin-purple .x-panel-warning .x-panel-header:before {
  background-color: #faad14;
}

.skin-purple .x-panel-danger .x-panel-header:before {
  background-color: #f5222d;
}

/* 适配动态主题色变更 - 绿色主题 */
.skin-green .x-panel-header {
  background: linear-gradient(to bottom, #f8f9fa, #eef5f0);
}

.skin-green .x-panel-default .x-panel-header:before {
  background-color: #00a65a;
}

.skin-green .x-window-header {
  background: linear-gradient(to bottom, #f8f9fa, #eef5f0);
}

.skin-green .x-form-text:focus {
  border-color: #00a65a;
  box-shadow: 0 0 0 2px rgba(0, 166, 90, 0.2);
}

.skin-green .x-panel-primary .x-panel-header:before,
.skin-green .x-panel-info .x-panel-header:before {
  background-color: #00a65a;
}

.skin-green .x-panel-success .x-panel-header:before {
  background-color: #52c41a;
}

.skin-green .x-panel-warning .x-panel-header:before {
  background-color: #faad14;
}

.skin-green .x-panel-danger .x-panel-header:before {
  background-color: #f5222d;
}

/* 适配动态主题色变更 - 红色主题 */
.skin-red .x-panel-header {
  background: linear-gradient(to bottom, #f8f9fa, #f7efef);
}

.skin-red .x-panel-default .x-panel-header:before {
  background-color: #dd4b39;
}

.skin-red .x-window-header {
  background: linear-gradient(to bottom, #f8f9fa, #f7efef);
}

.skin-red .x-form-text:focus {
  border-color: #dd4b39;
  box-shadow: 0 0 0 2px rgba(221, 75, 57, 0.2);
}

.skin-red .x-panel-primary .x-panel-header:before,
.skin-red .x-panel-info .x-panel-header:before {
  background-color: #dd4b39;
}

.skin-red .x-panel-success .x-panel-header:before {
  background-color: #52c41a;
}

.skin-red .x-panel-warning .x-panel-header:before {
  background-color: #faad14;
}

.skin-red .x-panel-danger .x-panel-header:before {
  background-color: #f5222d;
}

/* 适配动态主题色变更 - 黄色主题 */
.skin-yellow .x-panel-header {
  background: linear-gradient(to bottom, #f8f9fa, #f7f4ed);
}

.skin-yellow .x-panel-default .x-panel-header:before {
  background-color: #f39c12;
}

.skin-yellow .x-window-header {
  background: linear-gradient(to bottom, #f8f9fa, #f7f4ed);
}

.skin-yellow .x-form-text:focus {
  border-color: #f39c12;
  box-shadow: 0 0 0 2px rgba(243, 156, 18, 0.2);
}

.skin-yellow .x-panel-primary .x-panel-header:before,
.skin-yellow .x-panel-info .x-panel-header:before {
  background-color: #f39c12;
}

.skin-yellow .x-panel-success .x-panel-header:before {
  background-color: #52c41a;
}

.skin-yellow .x-panel-warning .x-panel-header:before {
  background-color: #faad14;
}

.skin-yellow .x-panel-danger .x-panel-header:before {
  background-color: #f5222d;
}


/* 优化面板头部为现代扁平化设计 */
.x-panel-header {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: none;
  padding: 8px 10px;
  position: relative;
  height: auto !important;
}

.x-panel-header-text {
  color: #333333;
  font-weight: 500;
  text-shadow: none;
  font-size: 13px;
  letter-spacing: 0.2px;
}

/* 精简的左侧边框作为颜色指示 */
.x-panel-default .x-panel-header:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 2px;
  background-color: #3c8dbc; /* 与主题蓝色统一 */
  border-top-left-radius: 4px;
}

/* 面板内容区域样式调整 */
.x-panel-body {
  border-color: #f0f0f0;
}

/* 面板工具按钮调整 */
.x-tool img {
  width: 12px;
  height: 12px;
}

/* 为不同类型的面板设置不同颜色的左侧边框 */
.x-panel-primary .x-panel-header:before {
  background-color: #1890ff;
}

.x-panel-success .x-panel-header:before {
  background-color: #52c41a;
}

.x-panel-warning .x-panel-header:before {
  background-color: #faad14;
}

.x-panel-danger .x-panel-header:before {
  background-color: #f5222d;
}

.x-panel-info .x-panel-header:before {
  background-color: #722ed1;
}

/* 改进树形面板头部样式 */
.x-accordion-item .x-accordion-hd {
  border-radius: 3px;
  border: none;
  border-bottom: 1px solid #f0f0f0;
  padding: 6px 8px;
}

.x-accordion-item.x-accordion-item-expanded .x-accordion-hd {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

/* 窗口头部也应用同样的样式 */
.x-window-header {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  padding: 8px 10px;
  border: none;
  border-bottom: 1px solid #f0f0f0;
  height: auto !important;
}

.x-window-header-text {
  color: #333333;
  font-weight: 500;
  font-size: 13px;
  letter-spacing: 0.2px;
}

/* 标签页更纤细的底部边框 */
.x-tab-default-top-active {
  border-bottom-width: 2px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  transition: all 0.2s ease-in-out;
}

/* Tab页样式优化 */
.x-tab-default {
  padding: 8px 14px;
  border-radius: 4px 4px 0 0;
  /* 优化：只对背景色应用过渡，不用all */
  transition: background-color 0.2s ease;
  margin-right: 2px;
  border: 1px solid transparent;
  position: relative;
}

.x-tab-default:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.x-tab-default .x-tab-inner {
  font-size: 14px;
  font-weight: normal;
  /* 优化：移除不必要的过渡效果 */
}

/* 标签页图标美化 */
.x-tab-default .x-tab-glyph {
  /* 优化：移除不必要的过渡 */
  margin-right: 5px;
  opacity: 0.8;
}

.x-tab-default-active .x-tab-glyph {
  opacity: 1;
}

/* 标签页关闭按钮样式 */
.x-tab-default .x-tab-close-btn {
  top: 9px;
  right: 5px;
  width: 14px;
  height: 14px;
  background-size: 14px;
  opacity: 0.6;
  /* 优化：只对透明度应用过渡 */
  transition: opacity 0.2s ease;
}

.x-tab-default .x-tab-close-btn:hover {
  opacity: 1;
  /* 优化：移除transform缩放效果 */
}

/* 标签页关闭按钮的定位调整 */
.x-tab-default.x-tab-with-icon .x-tab-close-btn {
  right: 5px;
}

/* 让Tab栏更加突出 */
.x-tab-bar {
  background-color: #f9f9f9;
  border-bottom: 1px solid #e0e0e0;
  padding-top: 4px;
  /* 优化：减少阴影复杂度 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

/* 修复标签栏底部条纹问题 */
.x-tab-bar-strip,
.x-tab-bar-strip-default,
.x-tab-bar-strip-default-top,
.x-tab-bar-strip-default-bottom,
.x-tab-bar-strip-default-left,
.x-tab-bar-strip-default-right {
  height: 0 !important;
  width: 0 !important;
  border: none !important;
  background: transparent !important;
  visibility: hidden !important;
}

/* 修复标签页底部高亮线，确保我们的自定义指示器正常工作 */
.x-tab-default-top-active {
  border-bottom-width: 0 !important;
  /* 优化：移除复杂的阴影效果 */
  box-shadow: none;
  position: relative;
}

/* 标签栏容器美化 */
.x-tab-bar-body {
  border: none;
  padding: 2px 0 0 5px;
}

/* 标签页滚动条美化 */
.x-tab-bar .x-box-scroller {
  background-color: transparent;
  width: 24px;
  cursor: pointer;
  /* 优化：只对背景色应用过渡 */
  transition: background-color 0.2s ease;
}

.x-tab-bar .x-box-scroller:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.x-tab-bar .x-box-scroller .x-box-scroller-body {
  opacity: 0.7;
  /* 优化：只对透明度应用过渡 */
  transition: opacity 0.2s ease;
}

.x-tab-bar .x-box-scroller:hover .x-box-scroller-body {
  opacity: 1;
}

/* 优化标签页指示器动画 */
.x-tab-default:before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: #157fcc;
  /* 优化：只对必要属性应用过渡，减少为0.2s */
  transition: width 0.2s ease, left 0.2s ease;
}

.x-tab-default:hover:before {
  width: 30%;
  left: 35%;
}

.x-tab-default-top-active:before {
  width: 100%;
  left: 0;
}

/* 为不同主题定制标签样式 */
.skin-purple .x-tab-default:before {
  background-color: #605ca8;
}

.skin-green .x-tab-default:before {
  background-color: #00a65a;
}

.skin-red .x-tab-default:before {
  background-color: #dd4b39;
}

.skin-yellow .x-tab-default:before {
  background-color: #f39c12;
}

/* 工具栏样式优化 */
.x-toolbar-default {
  padding: 6px 8px;
}

/* =================== 主题色适配 =================== */

/* 紫色主题 */
.skin-purple .x-panel-default .x-panel-header:before {
  background-color: #605ca8;
}

.skin-purple .x-form-text:focus {
  border-color: #605ca8;
  box-shadow: 0 0 0 2px rgba(96, 92, 168, 0.1);
}

.skin-purple .x-panel-primary .x-panel-header:before,
.skin-purple .x-panel-info .x-panel-header:before {
  background-color: #605ca8;
}

.skin-purple .x-tab-default-top-active {
  border-bottom-color: #605ca8;
}

.skin-purple .x-tab-default-active .x-tab-inner,
.skin-purple .x-tab-default-active .x-tab-glyph {
  color: #605ca8;
}

/* 绿色主题 */
.skin-green .x-panel-default .x-panel-header:before {
  background-color: #00a65a;
}

.skin-green .x-form-text:focus {
  border-color: #00a65a;
  box-shadow: 0 0 0 2px rgba(0, 166, 90, 0.1);
}

.skin-green .x-panel-primary .x-panel-header:before,
.skin-green .x-panel-info .x-panel-header:before {
  background-color: #00a65a;
}

.skin-green .x-tab-default-top-active {
  border-bottom-color: #00a65a;
}

.skin-green .x-tab-default-active .x-tab-inner,
.skin-green .x-tab-default-active .x-tab-glyph {
  color: #00a65a;
}

/* 红色主题 */
.skin-red .x-panel-default .x-panel-header:before {
  background-color: #dd4b39;
}

.skin-red .x-form-text:focus {
  border-color: #dd4b39;
  box-shadow: 0 0 0 2px rgba(221, 75, 57, 0.1);
}

.skin-red .x-panel-primary .x-panel-header:before,
.skin-red .x-panel-info .x-panel-header:before {
  background-color: #dd4b39;
}

.skin-red .x-tab-default-top-active {
  border-bottom-color: #dd4b39;
}

.skin-red .x-tab-default-active .x-tab-inner,
.skin-red .x-tab-default-active .x-tab-glyph {
  color: #dd4b39;
}

.skin-red .wb_badge1 {
  background: #00a65a;
}

/* 黄色主题 */
.skin-yellow .x-panel-default .x-panel-header:before {
  background-color: #f39c12;
}

.skin-yellow .x-form-text:focus {
  border-color: #f39c12;
  box-shadow: 0 0 0 2px rgba(243, 156, 18, 0.1);
}

.skin-yellow .x-panel-primary .x-panel-header:before,
.skin-yellow .x-panel-info .x-panel-header:before {
  background-color: #f39c12;
}

.skin-yellow .x-tab-default-top-active {
  border-bottom-color: #f39c12;
}

.skin-yellow .x-tab-default-active .x-tab-inner,
.skin-yellow .x-tab-default-active .x-tab-glyph {
  color: #f39c12;
}

.skin-yellow .wb_badge1 {
  background: #00a65a;
}
