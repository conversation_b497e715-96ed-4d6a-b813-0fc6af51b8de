package com.wb.common.ws;

import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

/**
 * 自定义处理类，用于后续获取HttpSession
 * 
 * <AUTHOR>
 *
 */
@Component
public class HttpSessionHandshakeInterceptor implements HandshakeInterceptor {

	@Override
	public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler,
			Map<String, Object> attributes) {
		if (request instanceof ServletServerHttpRequest) {
			HttpServletRequest servletRequest = ((ServletServerHttpRequest) request).getServletRequest();

			// 获取请求参数，放入属性对象中
			Map<String, String[]> parameterMap = servletRequest.getParameterMap();
			Map<String, String> httpParams = parameterMap.entrySet().stream()
					.filter(entry -> entry.getValue().length > 0)
					.collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()[0]));
			attributes.putAll(httpParams);

			// 把会话放入属性中
			HttpSession session = servletRequest.getSession();
			attributes.put("HTTP_SESSION_ID", session.getId());
		}
		return true;
	}

	@Override
	public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler,
			Exception ex) {
	}
}
