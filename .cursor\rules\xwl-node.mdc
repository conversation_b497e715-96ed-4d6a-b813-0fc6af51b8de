---
description: 
globs: *.xwl
alwaysApply: false
---
---
description: 基于Node.js的XWL文件编辑工具使用规则和最佳实践
globs: *.xwl
alwaysApply: true
---

# 基于Node.js的XWL文件编辑最佳实践

## 工具概述

Node.js方式的XWL格式化工具用于解决XWL大文件编辑时可能出现的格式错误问题。这些问题通常包括：
- 换行符未正确处理（使用实际换行而非`\n`）
- 引号未正确转义（未使用`\"`）
- HTML结束标签未正确转义（未使用`<\/`）
- 行尾缺少`\n`

使用Node.js直接调用方式避免了批处理脚本可能出现的编码问题，更加可靠。

## 使用方法

### 命令行工具（推荐）

1. **提取脚本**：
```bash
# 切换到项目根目录
cd 项目根目录

# 提取脚本到临时文件
node xwl-tools/xwl-cli.js extract 相对路径/文件名.xwl 脚本键名 xwl-temp/脚本键名.js
```

2. **在Cursor中编辑**：
   - 打开并编辑提取的脚本文件
   - 正常使用所有Cursor功能，无需担心格式问题

3. **更新回XWL文件**：
```bash
node xwl-tools/xwl-cli.js update 相对路径/文件名.xwl 脚本键名 xwl-temp/脚本键名.js
```

4. **清理临时文件**（可选）：
```bash
# 使用批处理脚本清理
xwl-tools/xwl-edit.bat clean

# 或直接删除目录
rmdir /s /q xwl-temp
```

## 编辑XWL文件的规则(注：优先使用命令行工具提取脚本，包括serverScript、js脚本、html等)

1. **脚本属性必须是单行字符串**：
   - XWL中的脚本（serverScript、footerScript、html等）必须是单行字符串
   - 必须使用`\n`表示换行，不能使用实际换行符
   - 每行代码结束后必须添加`\n`，包括最后一行

2. **转义规则**：
   - 字符串中的引号必须使用`\"`转义
   - HTML结束标签使用`<\/`来转义，如`<\/div>`

3. **避免错误的模式**：
   - 不要使用实际换行而非`\n`
   - 不要使用反斜杠+换行组合
   - 不要混用`\n`和实际换行
   - 不要遗漏行尾的`\n`

## 完整示例

以编辑熔断配置界面为例：

```bash
# 1. 提取footerScript脚本
node xwl-tools/xwl-cli.js extract meland-web/src/main/webapp/wb/modules/common/system/circuit-breaker-config.xwl footerScript xwl-temp/footerScript.js

# 2. 在Cursor中编辑xwl-temp/footerScript.js文件

# 3. 将修改后的脚本更新回XWL文件
node xwl-tools/xwl-cli.js update meland-web/src/main/webapp/wb/modules/common/system/circuit-breaker-config.xwl footerScript xwl-temp/footerScript.js

# 4. 完成后清理临时文件（可选）
xwl-tools/xwl-edit.bat clean
```

## XWL-CLI.js工具命令选项

- **extract**: 从XWL文件中提取脚本
  ```
  node xwl-tools/xwl-cli.js extract <文件路径> <脚本键名> [输出文件路径]
  ```

- **update**: 更新XWL文件中的脚本
  ```
  node xwl-tools/xwl-cli.js update <XWL文件路径> <脚本键名> <脚本文件路径>
  ```

- **format**: 格式化脚本（单独使用）
  ```
  node xwl-tools/xwl-cli.js format <脚本文件路径> [输出文件路径]
  ```

- **parse**: 解析XWL格式的脚本为可编辑格式（单独使用）
  ```
  node xwl-tools/xwl-cli.js parse <格式化脚本文件路径> [输出文件路径]
  ```

## 可能的问题及解决方案

1. **Node.js未安装**：
   - 确保已安装Node.js，可通过`node --version`检查
   - 如未安装，请先安装Node.js

2. **路径问题**：
   - 确保在项目根目录执行命令
   - 使用相对于项目根目录的文件路径
   - Windows环境下可以使用正斜杠`/`或反斜杠`\`

3. **临时目录不存在**：
   - 如果`xwl-temp`目录不存在，请先创建：`mkdir xwl-temp`

4. **权限问题**：
   - 确保对目标文件有读写权限

5. **编码问题**：
   - 如果出现乱码，检查文件编码是否为UTF-8
   - 可以在编辑器中指定保存为UTF-8编码

## 最佳实践

1. **大文件处理**：
   - 将大型XWL文件拆分为多个小模块
   - 使用`app.execute`调用其他模块

2. **编辑前准备**：
   - 总是先备份原文件
   - 创建临时目录：`mkdir xwl-temp`
   - 使用Node.js工具提取需要编辑的部分

3. **更新文件**：
   - 编辑完成后使用Node.js工具更新回XWL文件
   - 测试更新后的文件是否正常工作

4. **项目管理**：
   - 添加`xwl-temp`到`.gitignore`文件中
   - 开发完成后清理临时文件 