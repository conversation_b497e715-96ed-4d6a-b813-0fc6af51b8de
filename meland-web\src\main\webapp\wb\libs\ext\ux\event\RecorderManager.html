<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
    <title>Event Recorder Manager</title>
    <link rel="stylesheet" type="text/css" href="../../../resources/css/ext-all.css" />

    <!-- GC -->


    <script type="text/javascript" src="../../../ext.js"></script>
    <script type="text/javascript">
        Ext.Loader.setPath({
            'Ext.ux': '..'
        });
        Ext.require([
            '*',
            'Ext.ux.event.RecorderManager'
        ]);

        Ext.onReady(function () {
            var vp = Ext.create('Ext.Viewport', {
                    layout: 'fit',
                    items: [{
                        xtype: 'eventrecordermanager',
                        attachTo: window.opener
                    }]
                });
        });
    </script>
</head>
<body>
</body>
</html>
