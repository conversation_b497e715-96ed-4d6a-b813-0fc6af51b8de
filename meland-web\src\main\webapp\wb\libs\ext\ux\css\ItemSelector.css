/* Styles used by Ext.ux.form.ItemSelector */

.x-form-multiselect-body .x-boundlist .x-mask {
    background: none;
}

.x-form-itemselector-body .x-form-item {
    margin: 0;
}

.x-form-itemselector-top {
    background-image: url(images/top2.gif);
}
.x-form-itemselector-up {
    background-image: url(images/up2.gif);
}
.x-form-itemselector-add {
    background-image: url(images/right2.gif);
}
.x-form-itemselector-remove {
    background-image: url(images/left2.gif);
}
.x-form-itemselector-down {
    background-image: url(images/down2.gif);
}
.x-form-itemselector-bottom {
    background-image: url(images/bottom2.gif);
}
