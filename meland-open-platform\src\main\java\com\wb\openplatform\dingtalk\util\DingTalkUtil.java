package com.wb.openplatform.dingtalk.util;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.ConnectException;
import java.net.URL;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.wb.openplatform.accounts.util.AccountUtil;
import com.wb.openplatform.enterprise.util.MyX509TrustManager;
import com.wb.openplatform.enterprise.voucher.AccessToken;
import com.wb.util.DbUtil;

/**
 * 由Mr. Huang编辑于2019-04-04
 * <AUTHOR>
 */
public class DingTalkUtil {
	private static Logger log = LoggerFactory.getLogger(AccountUtil.class);

	//获取access_token的接口地址（GET） 限200（次/天）  
	public final static String access_token_url = "https://oapi.dingtalk.com/gettoken?corpid={corpid}&corpsecret={corpsecret}";
	//获取第三方
	public final static String SANaccess_token_url = "https://oapi.dingtalk.com/sns/gettoken?appid={corpid}&appsecret={corpsecret}";
	/**
	 * 获取access_token
	 */
	public final static String token_url = "https://oapi.dingtalk.com/gettoken?appkey={appkey}&appsecret={secret}";

	/**
	 * 1.发起https请求并获取结果 
	 *  
	 * @param requestUrl 请求地址 
	 * @param requestMethod 请求方式（GET、POST） 
	 * @param outputStr 提交的数据 
	 * @return JSONObject(通过JSONObject.get(key)的方式获取json对象的属性值) 
	 */
	public static JSONObject httpRequest(String requestUrl, String requestMethod, String outputStr) {
		JSONObject jsonObject = null;
		StringBuffer buffer = new StringBuffer();
		try {
			// 创建SSLContext对象，并使用我们指定的信任管理器初始化  
			TrustManager[] tm = { new MyX509TrustManager() };
			SSLContext sslContext = SSLContext.getInstance("SSL", "SunJSSE");
			sslContext.init(null, tm, new java.security.SecureRandom());
			// 从上述SSLContext对象中得到SSLSocketFactory对象  
			SSLSocketFactory ssf = sslContext.getSocketFactory();

			URL url = new URL(requestUrl);
			HttpsURLConnection httpUrlConn = (HttpsURLConnection) url.openConnection();
			httpUrlConn.setSSLSocketFactory(ssf);

			httpUrlConn.setDoOutput(true);
			httpUrlConn.setDoInput(true);
			httpUrlConn.setUseCaches(false);
			//协议
			httpUrlConn.setRequestProperty("accept", "*/*");
			httpUrlConn.setRequestProperty("Accept-Charset", "UTF-8");
			httpUrlConn.setRequestProperty("contentType", "UTF-8");
			httpUrlConn.setRequestProperty("Content-Type", "application/json");
			// 设置请求方式（GET/POST）  
			httpUrlConn.setRequestMethod(requestMethod);

			if ("GET".equalsIgnoreCase(requestMethod))
				httpUrlConn.connect();

			// 当有数据需要提交时  
			if (null != outputStr) {
				OutputStream outputStream = httpUrlConn.getOutputStream();
				// 注意编码格式，防止中文乱码  
				outputStream.write(outputStr.getBytes("UTF-8"));
				outputStream.close();
			}

			// 将返回的输入流转换成字符串  
			InputStream inputStream = httpUrlConn.getInputStream();
			InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
			BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

			String str = null;
			while ((str = bufferedReader.readLine()) != null) {
				buffer.append(str);
			}
			bufferedReader.close();
			inputStreamReader.close();
			// 释放资源  
			inputStream.close();
			inputStream = null;
			httpUrlConn.disconnect();
			jsonObject = new JSONObject(buffer.toString());
		} catch (ConnectException ce) {
			log.error("Weixin server connection timed out.");
		} catch (Exception e) {
			log.error("https request error:{}", e);
		}
		return jsonObject;
	}
	
	/**
	 * 2.重写获取access_token
	 */
	public static AccessToken getAccessToken(String appid, String appsecret,String type) {
		AccessToken accessToken = null;
		JSONArray array = DbUtil.query("SELECT * FROM wb_token WHERE token_type = 'dingtalk' AND type = '" + type + "' ");
		if (array != null) {
			accessToken = new AccessToken();
			accessToken.setToken(array.getString(1));
		} else {
			String requestUrl = "";
			JSONObject jsonObject = null;
			if(type.equals(DingtalkParamesUtil.tokenSM)) {
				requestUrl = SANaccess_token_url.replace("{corpid}", appid).replace("{corpsecret}", appsecret);
			}else {
				requestUrl = access_token_url.replace("{corpid}", appid).replace("{corpsecret}", appsecret);
			}

			if(type != null) {
				jsonObject = httpRequest(requestUrl, "GET", null);
			}
			System.out.println(jsonObject);
			// 如果请求成功  
			if (null != jsonObject) {
				try {
					accessToken = new AccessToken();
					accessToken.setToken(jsonObject.getString("access_token"));
					if(jsonObject.has("expires_in")) {
						accessToken.setExpiresIn(jsonObject.getInt("expires_in"));
					}				
				} catch (JSONException e) {
					accessToken = null;
					// 获取token失败  
					log.error("获取token失败 errcode:{} errmsg:{}", jsonObject.getInt("errcode"),
							jsonObject.getString("errmsg"));
				}
			}
		}

		return accessToken;
	}

	/** 
	 * 3.获取access_token 
	 *  
	 * @param appid 凭证 
	 * @param appsecret 密钥 
	 * @return 
	 */
	public static AccessToken GetUpdateAccessToken(String url, String appid, String appsecret) {
		AccessToken accessToken = null;
		String requestUrl = "";
		JSONObject jsonObject = null;
		if(url.equals(DingtalkParamesUtil.tokenSM)) {
			requestUrl = SANaccess_token_url.replace("{corpid}", appid).replace("{corpsecret}", appsecret);
		}else if(url.equals(DingtalkParamesUtil.tokenDT)) {
			requestUrl = access_token_url.replace("{corpid}", appid).replace("{corpsecret}", appsecret);
		}else if(url.equals(DingtalkParamesUtil.tokenDD)) {
			requestUrl = token_url.replace("{appkey}", appid).replace("{secret}", appsecret);
		}

		if(url != null) {
			jsonObject = httpRequest(requestUrl, "GET", null);
		}
		System.out.println(jsonObject);
		// 如果请求成功  
		if (null != jsonObject) {
			try {
				accessToken = new AccessToken();
				accessToken.setToken(jsonObject.getString("access_token"));
				if(jsonObject.has("expires_in")) {
					accessToken.setExpiresIn(jsonObject.getInt("expires_in"));
				}				
			} catch (JSONException e) {
				accessToken = null;
				// 获取token失败  
				log.error("获取token失败 errcode:{} errmsg:{}", jsonObject.getInt("errcode"),
						jsonObject.getString("errmsg"));
			}
		}
		return accessToken;
	}
}
