package com.wb.cache.bloom;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.function.Supplier;

/**
 * Redis布隆过滤器实现
 * 用于缓存穿透防护，通过概率判断元素是否存在
 */
public class RedisBloomFilter {
    private static final Logger logger = LoggerFactory.getLogger(RedisBloomFilter.class);
    
    private final String filterName; // 过滤器名称，用作Redis键前缀
    private final int numHashFunctions; // 哈希函数数量
    private final int bitSize; // 位数组大小
    private final double expectedFpp; // 期望的误判率
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final StringRedisTemplate stringRedisTemplate;
    
    /**
     * 创建布隆过滤器
     * 
     * @param filterName 过滤器名称
     * @param expectedElements 预期元素数量
     * @param expectedFpp 期望的误判率（0-1之间，越小越精确但空间占用越大）
     * @param redisTemplate Redis模板
     * @param stringRedisTemplate 字符串Redis模板
     */
    public RedisBloomFilter(String filterName, long expectedElements, double expectedFpp, 
                           RedisTemplate<String, Object> redisTemplate, 
                           StringRedisTemplate stringRedisTemplate) {
        if (isEmpty(filterName)) {
            throw new IllegalArgumentException("过滤器名称不能为空");
        }
        if (expectedElements <= 0) {
            throw new IllegalArgumentException("预期元素数量必须大于0");
        }
        if (expectedFpp <= 0 || expectedFpp >= 1) {
            throw new IllegalArgumentException("期望误判率必须在0-1之间");
        }
        
        this.filterName = "bf:" + filterName;
        this.expectedFpp = expectedFpp;
        this.redisTemplate = redisTemplate;
        this.stringRedisTemplate = stringRedisTemplate;
        
        // 根据预期元素数量和误判率计算最佳的位数组大小和哈希函数数量
        this.bitSize = optimalBitSize(expectedElements, expectedFpp);
        this.numHashFunctions = optimalNumHashFunctions(expectedElements, bitSize);
        
        logger.info("初始化布隆过滤器 [name=" + filterName + ", expectedElements=" + expectedElements 
                + ", fpp=" + expectedFpp + ", bitSize=" + bitSize 
                + ", hashFunctions=" + numHashFunctions + "]");
    }
    
    /**
     * 判断字符串是否为空
     */
    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 计算最优的位数组大小
     */
    private int optimalBitSize(long expectedElements, double fpp) {
        return (int) Math.ceil(-1 * expectedElements * Math.log(fpp) / (Math.log(2) * Math.log(2)));
    }
    
    /**
     * 计算最优的哈希函数数量
     */
    private int optimalNumHashFunctions(long expectedElements, int bitSize) {
        return Math.max(1, (int) Math.round((double) bitSize / expectedElements * Math.log(2)));
    }
    
    /**
     * 计算元素的位置
     * 
     * @param element 元素
     * @return 位置数组
     */
    private int[] getBitIndices(String element) {
        int[] indices = new int[numHashFunctions];
        
        // 使用Murmur3哈希函数的两个种子作为基础
        long hash1 = MurmurHash3.hash64(element.getBytes(), 0);
        long hash2 = MurmurHash3.hash64(element.getBytes(), hash1);
        
        // 使用双重哈希法生成多个哈希函数
        for (int i = 0; i < numHashFunctions; i++) {
            // 使用双重哈希法计算位置: (hash1 + i * hash2) % bitSize
            // 这种方法可以产生numHashFunctions个不同的哈希值
            indices[i] = (int) ((hash1 + i * hash2) % bitSize);
        }
        
        return indices;
    }
    
    /**
     * 添加元素到布隆过滤器
     * 
     * @param element 要添加的元素
     * @return 是否成功
     */
    public boolean add(String element) {
        if (isEmpty(element)) {
            return false;
        }
        
        try {
            int[] bitIndices = getBitIndices(element);
            return executeWithExceptionHandling("BF_ADD", filterName, () -> {
                // 使用Pipeline批量设置位
                List<Object> results = redisTemplate.executePipelined(new SessionCallback<Object>() {
                    @Override
                    @SuppressWarnings("unchecked")
                    public Object execute(RedisOperations operations) throws DataAccessException {
                        for (int bitIndex : bitIndices) {
                            stringRedisTemplate.opsForValue().setBit(filterName, bitIndex, true);
                        }
                        return null;
                    }
                });
                return results != null && !results.isEmpty();
            });
        } catch (Exception e) {
            logger.error("添加元素到布隆过滤器失败 [name=" + filterName + ", element=" + element + "]: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 批量添加元素到布隆过滤器
     * 
     * @param elements 要添加的元素集合
     * @return 添加成功的元素数量
     */
    public int addAll(Collection<String> elements) {
        if (elements == null || elements.isEmpty()) {
            return 0;
        }
        
        try {
            return executeWithExceptionHandling("BF_ADD_ALL", filterName, () -> {
                int successCount = 0;
                
                // 分批处理
                int batchSize = 100;
                List<List<String>> batches = new ArrayList<>();
                List<String> currentBatch = new ArrayList<>();
                
                for (String element : elements) {
                    if (!isEmpty(element)) {
                        currentBatch.add(element);
                        
                        if (currentBatch.size() >= batchSize) {
                            batches.add(currentBatch);
                            currentBatch = new ArrayList<>();
                        }
                    }
                }
                
                if (!currentBatch.isEmpty()) {
                    batches.add(currentBatch);
                }
                
                // 处理每一批
                for (List<String> batch : batches) {
                    // 使用Pipeline批量设置位
                    List<Object> pipelineResults = redisTemplate.executePipelined(new SessionCallback<Object>() {
                        @Override
                        @SuppressWarnings("unchecked")
                        public Object execute(RedisOperations operations) throws DataAccessException {
                            for (String element : batch) {
                                for (int bitIndex : getBitIndices(element)) {
                                    stringRedisTemplate.opsForValue().setBit(filterName, bitIndex, true);
                                }
                            }
                            return null;
                        }
                    });
                    
                    if (pipelineResults != null && !pipelineResults.isEmpty()) {
                        successCount += batch.size();
                    }
                }
                
                return successCount;
            });
        } catch (Exception e) {
            logger.error("批量添加元素到布隆过滤器失败 [name=" + filterName + "]: " + e.getMessage());
            return 0;
        }
    }
    
    /**
     * 判断元素是否存在于布隆过滤器中
     * 
     * @param element 要检查的元素
     * @return 如果返回false则一定不存在，如果返回true则可能存在（有误判的可能）
     */
    public boolean mightContain(String element) {
        if (isEmpty(element)) {
            return false;
        }
        
        try {
            int[] bitIndices = getBitIndices(element);
            return executeWithExceptionHandling("BF_CONTAINS", filterName, () -> {
                // 使用Pipeline批量检查位
                List<Object> results = redisTemplate.executePipelined(new SessionCallback<Object>() {
                    @Override
                    @SuppressWarnings("unchecked")
                    public Object execute(RedisOperations operations) throws DataAccessException {
                        for (int bitIndex : bitIndices) {
                            stringRedisTemplate.opsForValue().getBit(filterName, bitIndex);
                        }
                        return null;
                    }
                });
                
                // 所有位都为1，则可能存在
                if (results != null) {
                    for (Object result : results) {
                        if (!Boolean.TRUE.equals(result)) {
                            return false;
                        }
                    }
                    return true;
                }
                return false;
            });
        } catch (Exception e) {
            logger.error("检查元素是否存在于布隆过滤器失败 [name=" + filterName + ", element=" + element + "]: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 批量检查元素是否存在于布隆过滤器中
     * 
     * @param elements 要检查的元素集合
     * @return 存在的元素映射（元素 -> 是否可能存在）
     */
    public Map<String, Boolean> multiMightContain(Collection<String> elements) {
        if (elements == null || elements.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<String, Boolean> result = new HashMap<>();
        for (String element : elements) {
            if (!isEmpty(element)) {
                result.put(element, mightContain(element));
            }
        }
        
        return result;
    }
    
    /**
     * 获取布隆过滤器的当前大小（占用位数）
     * 
     * @return 大小
     */
    public long getSize() {
        try {
            return executeWithExceptionHandling("BF_SIZE", filterName, () -> {
                // 使用redisTemplate直接调用bitCount方法
                Long size = redisTemplate.execute(
                    (org.springframework.data.redis.core.RedisCallback<Long>) connection ->
                        connection.stringCommands().bitCount(filterName.getBytes())
                );
                return size != null ? size : 0L;
            });
        } catch (Exception e) {
            logger.error("获取布隆过滤器大小失败 [name=" + filterName + "]: " + e.getMessage());
            return 0;
        }
    }
    
    /**
     * 估算布隆过滤器中的元素数量
     * 
     * @return 估计的元素数量
     */
    public long approximateElementCount() {
        long setBits = getSize();
        // 使用布隆过滤器的数学公式估算元素数量
        double estimatedElements = -bitSize * Math.log(1 - (double) setBits / bitSize) / numHashFunctions;
        return Math.round(estimatedElements);
    }
    
    /**
     * 清空布隆过滤器
     */
    public void clear() {
        executeWithExceptionHandling("BF_CLEAR", filterName, () -> 
            redisTemplate.delete(filterName));
    }
    
    /**
     * 获取当前的误判率
     * 
     * @return 当前误判率
     */
    public double getCurrentFpp() {
        long setBits = getSize();
        double probability = Math.pow(1 - Math.exp(-numHashFunctions * approximateElementCount() / (double) bitSize), numHashFunctions);
        return probability;
    }
    
    /**
     * 获取布隆过滤器的基本信息
     * 
     * @return 信息Map
     */
    public Map<String, Object> getInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("name", filterName);
        info.put("bitSize", bitSize);
        info.put("hashFunctions", numHashFunctions);
        info.put("expectedFpp", expectedFpp);
        info.put("currentSize", getSize());
        info.put("approximateElementCount", approximateElementCount());
        info.put("currentFpp", getCurrentFpp());
        return info;
    }
    
    /**
     * 通用的异常处理执行方法
     */
    private <T> T executeWithExceptionHandling(String operation, String key, Supplier<T> supplier) {
        try {
            T result = supplier.get();
            return result;
        } catch (Exception e) {
            logger.error("Redis操作失败 [op=" + operation + ", key=" + key + "]: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 通用的异常处理执行方法（无返回值）
     */
    private void executeWithExceptionHandling(String operation, String key, Runnable runnable) {
        try {
            runnable.run();
        } catch (Exception e) {
            logger.error("Redis操作失败 [op=" + operation + ", key=" + key + "]: " + e.getMessage());
            throw e;
        }
    }
} 